<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.sinoair.billing</groupId>
    <artifactId>app-billing-timer</artifactId>
    <version>${app_version}</version>

    <name>billing-timer Maven Webapp</name>
    <!-- FIXME change it to the project's website -->
    <url>http://www.example.com</url>

    <repositories>
        <repository>
            <id>alimaven</id>
            <name>aliyun maven</name>
            <url>https://maven.aliyun.com/repository/public</url>
<!--            <url>http://maven.aliyun.com/nexus/content/groups/public/</url>-->
        </repository>
        <repository>
            <id>central</id>
            <name>central-mirror</name>
            <url>http://************:8081/nexus/content/groups/public</url>
        </repository>
    </repositories>

    <!-- param init start -->
    <properties>
        <maven.test.skip>true</maven.test.skip>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <plugin.mybatis.generator>1.3.1</plugin.mybatis.generator>
        <mybatis.generator.generatorConfig.xml>${basedir}/src/test/resources/generatorConfig.xml</mybatis.generator.generatorConfig.xml>
        <mybatis.generator.generatorConfig.properties>file:///${basedir}/src/test/resources/generatorConfig.properties</mybatis.generator.generatorConfig.properties>
        <!-- jdk -->
        <jdk.version>1.8</jdk.version>
        <!-- encoding -->
        <encoding.val>utf8</encoding.val>
        <!-- spring版本号 4.3.14.RELEASE -->
        <spring.version>4.3.14.RELEASE</spring.version>
        <!-- mybatis版本号 -->
        <mybatis.version>3.4.6</mybatis.version>
        <!-- log4j日志文件管理包版本 -->
        <slf4j.version>1.7.25</slf4j.version>
        <log4j.version>1.2.17</log4j.version>
        <!-- junit -->
        <junit.version>4.12</junit.version>
        <!-- jetty -->
        <jetty.version>9.4.7.RC0</jetty.version>
        <!-- spring-mybatis -->
        <mybatis.spring.version>1.3.2</mybatis.spring.version>
        <!-- 阿里巴巴数据源 -->
<!--        <alibaba.version>1.0.11</alibaba.version>-->
        <alibaba.version>1.2.5</alibaba.version>
        <!-- jackson -->
        <jackson.version>2.13.3</jackson.version>
        <!-- servlet-api -->
        <servlet-api.version>3.1.0</servlet-api.version>
        <!-- commons-lang3 -->
        <commons-lang3.version>3.3.2</commons-lang3.version>
        <!-- commons-fileupload -->
        <commons-fileupload.version>1.3.1</commons-fileupload.version>
        <!-- jsp-api -->
        <jsp-api.version>2.2.1-b03</jsp-api.version>
        <!-- jstl -->
        <jstl.version>1.2</jstl.version>
        <!-- oracle 驱动 -->
        <ojdbc.version>6.0</ojdbc.version>
        <!-- 工具网站 -->
        <!-- <pagehelper.version>3.4.1</pagehelper.version>-->
        <pagehelper.version>5.1.1</pagehelper.version>
        <hibernate.validator.version>5.1.1.Final</hibernate.validator.version>
        <!-- fastjson -->
        <fastjson.version>1.2.83</fastjson.version>
        <!-- commons-collections -->
        <commons-collections.version>3.2.1</commons-collections.version>
        <!-- aspectjweaver -->
        <aspectjweaver.version>1.8.6</aspectjweaver.version>
        <!-- javax.servlet-api -->
        <javax.servlet-api.version>3.0.1</javax.servlet-api.version>
        <!-- cglib -->
        <cglib.version>3.2.0</cglib.version>
        <!-- ehcache-core -->
        <ehcache-core.version>2.6.11</ehcache-core.version>
        <!-- commons-codec -->
        <commons-codec.version>1.11</commons-codec.version>
        <!-- apache.poi -->
        <apache.poi.version>3.10-FINAL</apache.poi.version>
        <!-- org.quartz -->
        <org.quartz.version>2.2.1</org.quartz.version>
        <commons-beanutils.version>1.9.2</commons-beanutils.version>
        <javax.mail.version>1.4</javax.mail.version>
        <httpclient.version>4.5.3</httpclient.version>
        <xstream.version>1.4.20</xstream.version>
        <cos_api.version>5.2.4</cos_api.version>
        <sinoair-core-support.version>1.0.1</sinoair-core-support.version>
        <bcprov-jdk16.version>1.46</bcprov-jdk16.version>
    </properties>

    <!-- param init end -->
    <dependencies>
        <!--quartz-->
        <dependency>
            <groupId>org.quartz-scheduler</groupId>
            <artifactId>quartz</artifactId>
            <version>${org.quartz.version}</version>
        </dependency>
        <!-- junit -->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>${junit.version}</version>
            <scope>test</scope>
        </dependency>

        <!-- spring start -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
            <version>${spring.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/commons-beanutils/commons-beanutils -->
        <dependency>
            <groupId>commons-beanutils</groupId>
            <artifactId>commons-beanutils</artifactId>
            <version>${commons-beanutils.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-oxm</artifactId>
            <version>${spring.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-tx</artifactId>
            <version>${spring.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-jdbc</artifactId>
            <version>${spring.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
            <version>${spring.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-aop</artifactId>
            <version>${spring.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context-support</artifactId>
            <version>${spring.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <version>${spring.version}</version>
        </dependency>

        <!-- mybatis/spring包 -->
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis-spring</artifactId>
            <version>${mybatis.spring.version}</version>
        </dependency>
        <!-- spring end -->

        <!-- mybatis核心包 -->
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
            <version>${mybatis.version}</version>
        </dependency>



        <!-- oracle驱动包 -->
<!--        <dependency>-->
<!--            <groupId>oracle</groupId>-->
<!--            <artifactId>ojdbc</artifactId>-->
<!--            <version>${ojdbc.version}</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.oracle</groupId>
            <artifactId>ojdbc6</artifactId>
            <version>11.2.0.3</version>
        </dependency>


        <!-- 阿里巴巴数据源包 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
            <version>${alibaba.version}</version>
        </dependency>

        <!-- json数据 -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
            <version>${jackson.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>${jackson.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
            <version>${jackson.version}</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>${fastjson.version}</version>
        </dependency>

        <!-- log start -->
        <dependency>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
            <version>${log4j.version}</version>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>${slf4j.version}</version>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-log4j12</artifactId>
            <version>${slf4j.version}</version>
        </dependency>
        <!-- log end -->

        <!-- apache.commons -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>${commons-lang3.version}</version>
        </dependency>

        <!-- commons-collections -->
        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
            <version>${commons-collections.version}</version>
        </dependency>


        <!--commons-fileupload -->
        <dependency>
            <groupId>commons-fileupload</groupId>
            <artifactId>commons-fileupload</artifactId>
            <version>${commons-fileupload.version}</version>
        </dependency>

        <!-- jsp-api -->
        <dependency>
            <groupId>javax.servlet.jsp</groupId>
            <artifactId>jsp-api</artifactId>
            <version>${jsp-api.version}</version>
        </dependency>

        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <version>${javax.servlet-api.version}</version>
        </dependency>

        <!-- jstl -->
        <dependency>
            <groupId>jstl</groupId>
            <artifactId>jstl</artifactId>
            <version>${jstl.version}</version>
        </dependency>

        <!-- 工具网站 -->
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper</artifactId>
            <version>${pagehelper.version}</version>
        </dependency>

        <!-- hibernate-validator -->
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-validator</artifactId>
            <version>${hibernate.validator.version}</version>
        </dependency>

        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
            <version>${aspectjweaver.version}</version>
        </dependency>
        <dependency>
            <groupId>cglib</groupId>
            <artifactId>cglib</artifactId>
            <version>${cglib.version}</version>
        </dependency>
        <dependency>
            <groupId>net.sf.ehcache</groupId>
            <artifactId>ehcache-core</artifactId>
            <version>${ehcache-core.version}</version>
        </dependency>

        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>${commons-codec.version}</version>
        </dependency>

        <!-- poi -->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>${apache.poi.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>${apache.poi.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml-schemas</artifactId>
            <version>${apache.poi.version}</version>
        </dependency>

        <dependency>
            <groupId>javax.mail</groupId>
            <artifactId>mail</artifactId>
            <version>${javax.mail.version}</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/org.apache.httpcomponents/httpclient -->
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>${httpclient.version}</version>
        </dependency>


        <!-- https://mvnrepository.com/artifact/com.thoughtworks.xstream/xstream -->
        <dependency>
            <groupId>com.thoughtworks.xstream</groupId>
            <artifactId>xstream</artifactId>
            <version>${xstream.version}</version>
        </dependency>

        <!-- 定时任务执行控制  -->
        <dependency>
            <groupId>code.sinoair.core</groupId>
            <artifactId>sinoair-core-support</artifactId>
            <version>${sinoair-core-support.version}</version>
        </dependency>

        <!-- tencent cos -->
        <dependency>
            <groupId>com.qcloud</groupId>
            <artifactId>cos_api</artifactId>
            <version>${cos_api.version}</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/org.bouncycastle/bcprov-jdk16 -->
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk16</artifactId>
            <version>${bcprov-jdk16.version}</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/commons-codec/commons-codec -->
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>1.11</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/commons-net/commons-net -->
        <dependency>
            <groupId>commons-net</groupId>
            <artifactId>commons-net</artifactId>
            <version>3.6</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.jcraft/jsch -->
        <dependency>
            <groupId>com.jcraft</groupId>
            <artifactId>jsch</artifactId>
            <version>0.1.54</version>
        </dependency>

        <!-- drools jar start  -->
        <!--<dependency>-->
            <!--<groupId>org.kie</groupId>-->
            <!--<artifactId>kie-api</artifactId>-->
            <!--<version>6.5.0.Final</version>-->
        <!--</dependency>-->
        <!--<dependency>-->
            <!--<groupId>org.drools</groupId>-->
            <!--<artifactId>drools-compiler</artifactId>-->
            <!--<version>6.5.0.Final</version>-->
            <!--<scope>runtime</scope>-->
        <!--</dependency>-->
        <dependency>
            <groupId>javax.mail</groupId>
            <artifactId>mail</artifactId>
            <version>1.4</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/net.sf.opencsv/opencsv -->
        <dependency>
            <groupId>net.sf.opencsv</groupId>
            <artifactId>opencsv</artifactId>
            <version>2.3</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/net.sourceforge.javacsv/javacsv -->
        <dependency>
            <groupId>net.sourceforge.javacsv</groupId>
            <artifactId>javacsv</artifactId>
            <version>2.0</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-redis</artifactId>
            <version>1.6.6.RELEASE</version>
        </dependency>

        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
            <version>2.9.1</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
            <version>2.11.1</version>
        </dependency>

    </dependencies>

    <profiles>
        <profile>
            <id>local</id>
            <properties>
                <!--war包名称-->
                <finalName>app-billing-timer-local</finalName>
                <app_version>0.0.0</app_version>
                <environment.name>local</environment.name>
                <environment.desc>local environment on windows server</environment.desc>
                <!--database-->
                <jdbc.url>jdbc:oracle:thin:@************:1521:training</jdbc.url>
                <jdbc.username>ecbilling</jdbc.username>
                <jdbc.password>ecbilling_244</jdbc.password>
                <!--database2-->
                <jdbc2.url2>jdbc:oracle:thin:@************:1521:training</jdbc2.url2>
                <jdbc2.username2>ceop</jdbc2.username2>
                <jdbc2.password2>CEOP</jdbc2.password2>
                <!--database3-->
                <jdbc3.url3>jdbc:oracle:thin:@************:1521:training</jdbc3.url3>
                <jdbc3.username3>ecbilling</jdbc3.username3>
                <jdbc3.password3>ecbilling_244</jdbc3.password3>

                <dataSource>DruidDataSource</dataSource>
                <log4j.rootLogger>debug,console</log4j.rootLogger>
                <sinoair.sinoairProxyHost>************</sinoair.sinoairProxyHost>
                <sinoair.sinoairProxyPort>8080</sinoair.sinoairProxyPort>
                <sinoair.openroxy>ON</sinoair.openroxy>
                <ceos_push_record_url>http://*************:8002/api/pushOrders2CaiNiaoFromBilling</ceos_push_record_url>

                <db.redis.ip>*************</db.redis.ip>
                <db.redis.port>6379</db.redis.port>
                <db.redis.password>RedisDB.Cloud20211110</db.redis.password>
                <db.redis.database>3</db.redis.database>

            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <!--war包名称-->
                <finalName>app-billing-timer-test</finalName>
                <app_version>0.0.0</app_version>
                <environment.name>test</environment.name>
                <environment.desc>test environment on windows server</environment.desc>
                <!--database-->
                <jdbc.url>jdbc:oracle:thin:@************:1521:training</jdbc.url>
                <jdbc.username>ecbilling</jdbc.username>
                <jdbc.password>ecbilling_244</jdbc.password>
                <!--database2-->
                <jdbc2.url2>jdbc:oracle:thin:@************:1521:training</jdbc2.url2>
                <jdbc2.username2>ceop</jdbc2.username2>
                <jdbc2.password2>CEOP</jdbc2.password2>
                <!--database3-->
                <jdbc3.url3>jdbc:oracle:thin:@************:1521:training</jdbc3.url3>
                <jdbc3.username3>ecbilling</jdbc3.username3>
                <jdbc3.password3>ecbilling_244</jdbc3.password3>

                <dataSource>DruidDataSource</dataSource>
                <log4j.rootLogger>debug,console</log4j.rootLogger>
                <sinoair.sinoairProxyHost>************</sinoair.sinoairProxyHost>
                <sinoair.sinoairProxyPort>8080</sinoair.sinoairProxyPort>
                <sinoair.openroxy>OFF</sinoair.openroxy>
                <ceos_push_record_url>http://*************:8002/api/pushOrders2CaiNiaoFromBilling</ceos_push_record_url>

                <db.redis.ip>*************</db.redis.ip>
                <db.redis.port>6379</db.redis.port>
                <db.redis.password>RedisDB.Cloud20211110</db.redis.password>
                <db.redis.database>3</db.redis.database>
            </properties>
        </profile>
        <profile>
            <id>product</id>
            <properties>
                <!--war包名称-->
                <finalName>app-billing-timer-product</finalName>
                <app_version>0.0.0</app_version>
                <environment.name>product</environment.name>
                <environment.desc>product environment on windows server</environment.desc>
                <!--database-->
                <jdbc.url>*******************************************</jdbc.url>
                <jdbc.username>billing</jdbc.username>
                <jdbc.password>billing.2021.SinoAir</jdbc.password>
                <!--database2-->
                <jdbc2.url2>*******************************************</jdbc2.url2>
                <jdbc2.username2>ceos</jdbc2.username2>
                <jdbc2.password2>Ceos_210</jdbc2.password2>
                <!--database3-->
                <jdbc3.url3>*******************************************</jdbc3.url3>
                <jdbc3.username3>billing</jdbc3.username3>
                <jdbc3.password3>billing.2021.SinoAir</jdbc3.password3>

                <dataSource>DruidDataSource</dataSource>
                <log4j.rootLogger>info,console</log4j.rootLogger>
                <sinoair.sinoairProxyHost>************</sinoair.sinoairProxyHost>
                <sinoair.sinoairProxyPort>8080</sinoair.sinoairProxyPort>
                <sinoair.openroxy>OFF</sinoair.openroxy>
                <ceos_push_record_url>http://**************:8083/api/pushOrders2CaiNiaoFromBilling</ceos_push_record_url>

                <db.redis.ip>*************</db.redis.ip>
                <db.redis.port>36379</db.redis.port>
                <db.redis.password>kj_WY_fzplm@2021</db.redis.password>
                <db.redis.database>4</db.redis.database>
            </properties>
        </profile>
        <profile>
            <id>product-local</id>
            <properties>
                <!--war包名称-->
                <finalName>app-billing-timer-product</finalName>
                <app_version>0.0.0</app_version>
                <environment.name>product</environment.name>
                <environment.desc>product environment on windows server</environment.desc>
                <!--database-->
                <jdbc.url>*******************************************</jdbc.url>
                <jdbc.username>billing</jdbc.username>
                <jdbc.password>billing.2021.SinoAir</jdbc.password>
                <!--database2-->
                <jdbc2.url2>******************************************</jdbc2.url2>
                <jdbc2.username2>ceos</jdbc2.username2>
                <jdbc2.password2>Ceos_210</jdbc2.password2>
                <!--database3-->
                <jdbc3.url3>*******************************************</jdbc3.url3>
                <jdbc3.username3>billing</jdbc3.username3>
                <jdbc3.password3>billing.2021.SinoAir</jdbc3.password3>

                <dataSource>DruidDataSource</dataSource>
                <log4j.rootLogger>debug,console</log4j.rootLogger>
                <sinoair.sinoairProxyHost>************</sinoair.sinoairProxyHost>
                <sinoair.sinoairProxyPort>8080</sinoair.sinoairProxyPort>
                <sinoair.openroxy>OFF</sinoair.openroxy>
                <ceos_push_record_url>http://**************:8083/api/pushOrders2CaiNiaoFromBilling</ceos_push_record_url>

                <db.redis.ip>*************</db.redis.ip>
                <db.redis.port>6379</db.redis.port>
                <db.redis.password>RedisDB.Cloud20211110</db.redis.password>
                <db.redis.database>3</db.redis.database>
            </properties>
        </profile>
    </profiles>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <finalName>${finalName}</finalName>
        <plugins>
            <!-- Mybatis generator代码生成插件 配置 -->
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <version>${plugin.mybatis.generator}</version>
                <configuration>
                    <configurationFile>${mybatis.generator.generatorConfig.xml}</configurationFile>
                    <overwrite>true</overwrite>
                    <verbose>true</verbose>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${jdk.version}</source>
                    <target>${jdk.version}</target>
                    <encoding>${encoding.val}</encoding>
                </configuration>
            </plugin>
            <!-- 打jar文件时，配置mainfest文件，假如lib包的jar依赖 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <classesDirectory>target/classes</classesDirectory>
                    <archive>
                        <manifest>
                            <mainClass>com.sinoair.billing.run.BillingTimerStart</mainClass>
                            <!-- 打包MANIFEST.MF文件不记录时间戳版本 -->
                            <useUniqueVersions>false</useUniqueVersions>
                            <addClasspath>true</addClasspath>
                            <classpathPrefix>lib/</classpathPrefix>
                        </manifest>
                        <manifestEntries>
                            <Class-Path>.</Class-Path>
                        </manifestEntries>
                    </archive>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-dependencies</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <type>jar</type>
                            <includeTypes>jar</includeTypes>
                            <!--    <useBaseVersion>false</useBaseVersion>-->
                            <outputDirectory>
                                ${project.build.directory}/lib
                            </outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
