@echo off
echo ========================================
echo 手动计费任务执行脚本
echo ========================================
echo.
echo 请确保：
echo 1. 数据库连接正常
echo 2. 运单号文件已准备好：D:\work\workspace\app-billing-timer\missing_eawb_codes.txt
echo 3. 文件格式：每行一个运单号
echo 4. 建议在业务低峰期执行
echo.
echo 按任意键开始执行，或按Ctrl+C取消...
pause

echo.
echo 开始执行手动计费任务...
echo.

REM 这里需要根据您的实际Java环境和classpath进行调整
REM java -cp "target/classes;lib/*" com.sinoair.billing.run.BillingTimerStart ManualBillingTimer 1800000 T

echo.
echo 请在IDE中运行以下类的main方法：
echo com.sinoair.billing.timer.ManualBillingTimerTest
echo.
echo 或者配置好classpath后，取消注释上面的java命令行
echo.

pause
