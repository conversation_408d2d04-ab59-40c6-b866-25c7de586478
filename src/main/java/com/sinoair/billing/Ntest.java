package com.sinoair.billing;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.sinoair.billing.core.util.DateUtil;
import com.sinoair.billing.core.util.StringUtil;
import com.sinoair.billing.domain.constant.BillingConstant;
import com.sinoair.billing.domain.model.billing.PaymentRecord;
import com.sinoair.billing.domain.model.billing.ReceiptRecord;
import com.sinoair.billing.domain.model.billing.ReceiptRecordTmp;
import com.sinoair.billing.domain.model.billing.SettlementObject;
import org.apache.commons.codec.binary.Base64;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

public class Ntest {

    public static void main(String[] agres) throws Exception {

//        int num = 80001;
//        int param = 10000;
//        System.out.println("result："+(num % param));

        SettlementObject settlementObject = new SettlementObject();
        settlementObject.setSoMode("RECHARGE");
        settlementObject.setSoVendorType("OFFLINE");
        if (settlementObject == null || !"RECHARGE".equals(settlementObject.getSoMode())
                || !"ONLINE".equals(settlementObject.getSoVendorType())){
            System.out.println("result：true");
        }

    }


    public static String format(String value, Object... paras) {
        return MessageFormat.format(value, paras);
    }

}
