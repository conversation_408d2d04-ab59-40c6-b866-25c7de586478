package com.sinoair.billing.core.mail;

import com.sinoair.billing.core.util.AppConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSenderImpl;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: 大雄
 * @date: 2019-03-18
 * @time: 14:29
 * @description: To change this template use File | Settings | File Templates.
 */
public class MailUtil {

    private static Logger logger = LoggerFactory.getLogger(MailUtil.class);
    private static final String MAIL_HOST = AppConfig.getStringByProp("props/mail","system.mail.host");
    private static final String MAIL_PORT = AppConfig.getStringByProp("props/mail","system.mail.port");
    private static final String MAIL_USERNAME = AppConfig.getStringByProp("props/mail","system.mail.username");
    private static final String MAIL_PASSWORD = AppConfig.getStringByProp("props/mail","system.mail.password");
    private static final String MAIL_SMS = AppConfig.getStringByProp("props/mail","system.mail.sms");

    private static JavaMailSenderImpl mailSender = null;

    static {
        synchronized (MailUtil.class){
            if (mailSender == null){
                mailSender = new JavaMailSenderImpl();
                //指定用来发送Email的邮件服务器主机名
                mailSender.setHost(MAIL_HOST);
                //默认端口，标准的SMTP端口
                mailSender.setPort(Integer.valueOf(MAIL_PORT));
                //用户名
                mailSender.setUsername(MAIL_USERNAME);
                //密码
                mailSender.setPassword(MAIL_PASSWORD);
            }
        }
    }

    /**
     * 发送文本短信
     * @param to
     * @param cc
     * @param subject
     * @param body
     */
    public static void sendSimpleText(String[] to,String[] cc,String subject,String body){
        //消息构造器
        SimpleMailMessage message = new SimpleMailMessage();
        //发件人
        message.setFrom(MAIL_USERNAME);
        //收件人
        message.setTo(to);
        message.setCc(cc);
        //主题
        message.setSubject(subject);
        //正文
        message.setText(body);
        mailSender.send(message);
    }

    /**
     * 给手机发短信，多的逗号分隔,
     * @param phones
     * @param body
     */
    public static void sentSmsText(String phones,String body){

        //消息构造器
        SimpleMailMessage message = new SimpleMailMessage();
        //发件人
        message.setFrom(MAIL_USERNAME);
        message.setTo(MAIL_SMS);
        //主题
        message.setSubject(phones);
        //正文
        message.setText(body);
        mailSender.send(message);
    }

}
