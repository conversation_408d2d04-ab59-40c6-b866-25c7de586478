package com.sinoair.billing.core.util;

import java.util.Hashtable;
import java.util.Map;
import java.util.ResourceBundle;

/**
 * <AUTHOR>
 * @Date 2016/6/16 13:31
 * @Description 应用参数工具类，获得config配置文件中定义的参数
 **/
public class AppConfig {

    private static Map<String, ResourceBundle> bundleMap = new Hashtable<String, ResourceBundle>();

    private AppConfig() {
    }
    /**
     * 根据props获取内容
     *
     * @param propName
     * @param key
     * @return
     */
    public static String getStringByProp(String propName, String key) {
        ResourceBundle resourceBundle = bundleMap.get(propName);
        if (resourceBundle == null) {
            resourceBundle = ResourceBundle.getBundle(propName);
            bundleMap.put(propName, resourceBundle);
        }
        try {
            String messager = resourceBundle.getString(key);
            return new String(messager.getBytes("ISO-8859-1"), "UTF-8");
        } catch (Exception e) {
            String messager = "不能在配置文件" + propName + "中发现参数：" + '!' + key
                    + '!';
            throw new RuntimeException(messager);
        }
    }
}
