package com.sinoair.billing.core.util;

import com.sinoair.billing.domain.model.billing.ReceiptEawb;
import com.sinoair.billing.domain.model.billing.ReceiptRecordTmp;

public class BeanConvertUtil {

    public static ReceiptEawb record2eawb(ReceiptRecordTmp rr){
        ReceiptEawb receiptEawb = new ReceiptEawb();
        receiptEawb.setEawbPrintcode(rr.getEawbPrintcode());
        receiptEawb.setRrOccurtime(rr.getRrOccurtime());
        receiptEawb.setSoCode(rr.getSoCode());
        return receiptEawb;
    }
}
