package com.sinoair.billing.core.util;

import com.sinoair.billing.domain.constant.EbaConstant;
import com.sinoair.billing.domain.model.billing.*;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static java.util.stream.Collectors.toList;

/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2019-06-17
 * @time: 17:42
 * @description: To change this template use File | Settings | File Templates.
 */
public class CheckUtil {

    private static BigDecimal volume_number = BigDecimal.valueOf(6000);
    private static BigDecimal volume_number_5 = BigDecimal.valueOf(5000);

//    private

    private static List<String> epKeys = new ArrayList<>();
    private static List<String> prices = new ArrayList<>();

    private static List<String> commonEadCodes = new ArrayList<>();
    private static List<String> commonEastCodes = new ArrayList<>();

    private static List<String> cnEadCodes = new ArrayList<>();
    private static List<String> cnEastCodes = new ArrayList<>();
    private static List<String> cnServiceTypes = new ArrayList<>();

    private static List<String> nonEadCodes = new ArrayList<>();
    private static List<String> nonEastCodes = new ArrayList<>();

    private static List<String> detailEastCodes = new ArrayList<>();

    private static List<String> rrEastCodes = new ArrayList<>();

    private static List<String> destSacIds = new ArrayList<>();  //出口口岸 临时采购

    private static List<String> ebaCnEadCodes = new ArrayList<>();
    private static List<String> ebaCnEastCodes = new ArrayList<>();

    private static List<String> allEadCodes = new ArrayList<>();
    private static List<String> allEastCodes = new ArrayList<>();

    private static List<String> cnRRPdIds = new ArrayList<>();
    private static List<String> cnRRPdNames = new ArrayList<>();

    private static List<String> mawbRRNames = new ArrayList<>();  //主单涉及费用名称

    private static List<String> notCnEpKeys = new ArrayList<>(); //非菜鸟不计费线路

    static {
//        epKeys.add("TRUNK_13439270");
//        epKeys.add("L_CN_PREMIUM_SINONAQEL");
        epKeys.add("TRUNK_13422207");
        epKeys.add("L_CN_PREMIUM_SINONAQEL_PREPAID");

        prices.add("清关服务费");

        commonEadCodes.add("INTERNATIONAL");
        commonEadCodes.add("LIQUIDATION");

        commonEastCodes.add("OK");
        commonEastCodes.add("REFUND_HANDOVER");
        commonEastCodes.add("REFUND_OUTBOUND_ZH");
        commonEastCodes.add("REFUND_DESTROY_ZH");
        commonEastCodes.add("REFUND_INBOUND_ZH");
        commonEastCodes.add("REFUND_HANDOVER_ZH");
        commonEastCodes.add("ZLYC");
        commonEastCodes.add("DR");


        cnEadCodes.add("INTERNATIONAL");
        cnEadCodes.add("FC_INBOUND");
//        cnEastCodes.add("CP");
//        cnEastCodes.add("CPT");
        cnEastCodes.add("ROE");
        cnEastCodes.add("OK");
        cnEastCodes.add("SI");
        cnEastCodes.add("ROEO");

        nonEadCodes.add("INTERNATIONAL");
        nonEadCodes.add("FC_INBOUND");
        nonEadCodes.add("FC_OUTBOUND");
        nonEastCodes.add("DHL03");
        nonEastCodes.add("OK");
        nonEastCodes.add("ASS");
        nonEastCodes.add("ASF");
        nonEastCodes.add("CPT");

        cnServiceTypes.add("L_AE_ONLINE_4PXBPOSTTRAIN_OM");


        detailEastCodes.add("ASS");
        detailEastCodes.add("ASF");
        detailEastCodes.add("CPT");
        detailEastCodes.add("ROE");


        rrEastCodes.add("REFUND_INBOUND_ZH");
        rrEastCodes.add("REFUND_OUTBOUND_ZH");
//        rrEastCodes.add("");
//        rrEastCodes.add("");

        destSacIds.add("KHN");

        ebaCnEadCodes.add("INTERNATIONAL");
        ebaCnEadCodes.add("LOCKER");
        ebaCnEastCodes.add("ASS");
        ebaCnEastCodes.add("ASF");
        ebaCnEastCodes.add("CPT");
        ebaCnEastCodes.add("CP");
        ebaCnEastCodes.add("ROE");
        ebaCnEastCodes.add("SI");
        ebaCnEastCodes.add("ROEO");
        ebaCnEastCodes.add("LOCKER_INBOUND");


        allEadCodes.add("INTERNATIONAL");
        allEadCodes.add("FC_INBOUND");
        allEadCodes.add("FC_OUTBOUND");
        allEadCodes.add("LIQUIDATION");

        allEastCodes.add("DHL03");
        allEastCodes.add("ASS");
        allEastCodes.add("ASF");
        allEastCodes.add("CPT");
        allEastCodes.add("CP");
        allEastCodes.add("ROE");
        allEastCodes.add("OK");
        allEastCodes.add("REFUND_HANDOVER");
        allEastCodes.add("REFUND_OUTBOUND_ZH");
        allEastCodes.add("REFUND_DESTROY_ZH");
        allEastCodes.add("REFUND_INBOUND_ZH");
        allEastCodes.add("REFUND_HANDOVER_ZH");
        allEastCodes.add("ZLYC");
        allEastCodes.add("DR");
        allEastCodes.add("SI");
        allEastCodes.add("ROEO");

        cnRRPdIds.add("100497");
        cnRRPdIds.add("100096");
        cnRRPdIds.add("100358");
        cnRRPdIds.add("100074");
        cnRRPdIds.add("100130");

        cnRRPdNames.add("包机费");
        cnRRPdNames.add("操作费");
        cnRRPdNames.add("道路运输费");
        cnRRPdNames.add("货值赔付");
        cnRRPdNames.add("运费");

        mawbRRNames.add("出口报关费");
        mawbRRNames.add("国际干线费");
        mawbRRNames.add("国内调拨费");


//        notCnEpKeys.add("L_TMI_TW_SEA_NEWHOMEDELIVERY");  //韩国退运

    }

    /**
     * 验证符合条件
     * @param epKey
     * @param priceName
     * @return
     */
    public static boolean checkSacAndPrice(String epKey,String priceName){

        if (epKeys.stream().anyMatch(epKey::contains) && prices.stream().anyMatch(priceName::contains)){
            return true;
        }

        return false;
    }
    /**
     * 验证符合公共环节条件
     * @param eadCode
     * @param eastCode
     * @return
     */
    public static boolean checkCommonEad(String eadCode,String eastCode){

        if (commonEadCodes.stream().anyMatch(eadCode::contains) && commonEastCodes.stream().anyMatch(eastCode::contains)){
            return true;
        }

        return false;
    }

    /**
     * 验证符合菜鸟环节条件
     * @param eadCode
     * @param eastCode
     * @return
     */
    public static boolean checkCnEad(String eadCode,String eastCode){

        if (cnEadCodes.stream().anyMatch(eadCode::contains) && cnEastCodes.stream().anyMatch(eastCode::contains)){
            return true;
        }

        return false;
    }

    /**
     * 验证符合非菜鸟环节条件
     * @param eadCode
     * @param eastCode
     * @return
     */
    public static boolean checkNonEad(String eadCode,String eastCode){

        if (nonEadCodes.stream().anyMatch(eadCode::contains) && nonEastCodes.stream().anyMatch(eastCode::contains)){
            return true;
        }

        return false;
    }

    public static boolean checkAllEad(String eadCode,String eastCode){

        if (allEadCodes.stream().anyMatch(eadCode::contains) && allEastCodes.stream().anyMatch(eastCode::contains)){
            return true;
        }

        return false;
    }

    /**
     * 验证符合菜鸟线路条件
     * @param serviceType
     * @return
     */
    public static boolean checkCnServiceType(String serviceType){

        if (cnServiceTypes.stream().anyMatch(serviceType::equals)){
            return true;
        }

        return false;
    }

    /**
     * 验证RR退货环节条件
     * @param eastCode
     * @return
     */
    public static boolean checkRREast(String eastCode){

        if (rrEastCodes.stream().anyMatch(eastCode::contains)){
            return true;
        }

        return false;
    }

    /**
     * 验证出口口岸-临时采购条件，符合则不计费
     * @param destSac
     * @return
     */
    public static boolean checkDestSac(String destSac){

        if (destSacIds.stream().anyMatch(destSac::contains)){
            return true;
        }

        return false;
    }

    /**
     * 验证环节同步PUSH符合环节节点条件
     * @param eadCode
     * @param eastCode
     * @return
     */
    public static boolean checkCnEba(String eadCode,String eastCode){

        if (ebaCnEadCodes.stream().anyMatch(eadCode::contains) && ebaCnEastCodes.stream().anyMatch(eastCode::contains)){
            return true;
        }

        return false;
    }

    /**
     * 结果集比较-差集
     * @param beList
     * @return
     */
    public static List<String> checkReduce(List<String> beList){
        // 差集 (list1 - list2)
        List<String> reduce1 = detailEastCodes.stream().filter(item -> !beList.contains(item)).collect(toList());
//        reduce1.parallelStream().forEach(System.out :: println);
        return reduce1;
    }

    public static String getSoSacId(String soCode,List<SettlementObject> soList){
        Optional<SettlementObject> optional = soList.stream().filter(property -> property.getSoCode().equals(soCode)).findFirst();
        if (optional.isPresent()) {
            // 存在
            SettlementObject so =  optional.get();
            return so.getSacId();
        } else {
            // 不存在
        }
        return null;
    }


    public static List<String> getEbaCode(List<CheckSynEbaConfig> configList,int so_type,String eba_type){
        List<String> ebaCodes = new ArrayList<>();
        for (CheckSynEbaConfig ebaConfig: configList){
            if (so_type == ebaConfig.getSoType() && eba_type.equals(ebaConfig.getEbaType())){
                ebaCodes.add(ebaConfig.getEbaCode());
            }
        }
        return ebaCodes;
    }

    public static List<String> getCommonEadCodes(List<CheckSynEbaConfig> configList){
        return getEbaCode(configList, EbaConstant.SO_TYPE_COMMON,EbaConstant.EBA_EAD);
    }
    public static List<String> getCommonEastCodes(List<CheckSynEbaConfig> configList){
        return getEbaCode(configList, EbaConstant.SO_TYPE_COMMON,EbaConstant.EBA_EAST);
    }

    public static List<String> getNonEadCodes(List<CheckSynEbaConfig> configList){
        return getEbaCode(configList, EbaConstant.SO_TYPE_NON,EbaConstant.EBA_EAD);
    }
    public static List<String> getNonEastCodes(List<CheckSynEbaConfig> configList){
        return getEbaCode(configList, EbaConstant.SO_TYPE_NON,EbaConstant.EBA_EAST);
    }
    public static List<String> getCnEadCodes(List<CheckSynEbaConfig> configList){
        return getEbaCode(configList, EbaConstant.SO_TYPE_CN,EbaConstant.EBA_EAD);
    }
    public static List<String> getCnEastCodes(List<CheckSynEbaConfig> configList){
        return getEbaCode(configList, EbaConstant.SO_TYPE_CN,EbaConstant.EBA_EAST);
    }

    public static boolean checkCommonEba(List<CheckSynEbaConfig> configList,String eadCode,String eastCode){
        List<String> commonEadCodes = getCommonEadCodes(configList);
        List<String> commonEastCodes = getCommonEastCodes(configList);
        if (commonEadCodes.stream().anyMatch(eadCode::contains) && commonEastCodes.stream().anyMatch(eastCode::contains)){
            return true;
        }
        return false;
    }

    public static boolean checkNonEba(List<CheckSynEbaConfig> configList,String eadCode,String eastCode){
        List<String> nonEadCodes = getNonEadCodes(configList);
        List<String> nonEastCodes = getNonEastCodes(configList);
        if (nonEadCodes.stream().anyMatch(eadCode::contains) && nonEastCodes.stream().anyMatch(eastCode::contains)){
            return true;
        }
        return false;
    }

    public static boolean checkCnEba(List<CheckSynEbaConfig> configList,String eadCode,String eastCode){
        List<String> cnEadCodes = getCnEadCodes(configList);
        List<String> cnEastCodes = getCnEastCodes(configList);
        if (cnEadCodes.stream().anyMatch(eadCode::contains) && cnEastCodes.stream().anyMatch(eastCode::contains)){
            return true;
        }
        return false;
    }

    public static boolean checkCnRrPdId(String pdSyscode){
        if (cnRRPdIds.stream().anyMatch(pdSyscode::contains)){
            return true;
        }
        return false;
    }
    public static boolean checkCnRrPdName(String pdName){
        if (cnRRPdNames.stream().anyMatch(pdName::contains)){
            return true;
        }
        return false;
    }
    /**
     * 验证主单涉及的费用名称
     * @param pdName
     * @return
     */
    public static boolean checkMawbRRNames(String pdName){
        if (mawbRRNames.stream().anyMatch(pdName::contains)){
            return true;
        }
        return false;
    }

    public static BigDecimal compareWeight(BigDecimal weight, BigDecimal volume,BigDecimal volumeNumber){

        if (Objects.isNull(weight) && Objects.isNull(volume)) return null;
        if (Objects.isNull(volume)) return weight;
        if (Objects.isNull(weight)) return volume.divide(volumeNumber,3,BigDecimal.ROUND_HALF_UP);
        BigDecimal volumeWeight = volume.divide(volumeNumber,3,BigDecimal.ROUND_HALF_UP);
        return weight.compareTo(volumeWeight) > 0?weight:volumeWeight;

    }

    public static BigDecimal getCompareWeight(ExpressAirWayBill bill){
        BigDecimal eawbLength = bill.getEawbLength() == null?BigDecimal.ZERO:bill.getEawbLength();
        BigDecimal eawbWidth = bill.getEawbWidth() == null?BigDecimal.ZERO:bill.getEawbWidth();
        BigDecimal eawbHeight = bill.getEawbHeight() == null?BigDecimal.ZERO:bill.getEawbHeight();
        BigDecimal eawbVolume = eawbLength.multiply(eawbWidth).multiply(eawbHeight);
        BigDecimal eawbTotalWight = bill.getWeightValue();

        return compareWeight(eawbTotalWight,eawbVolume,volume_number);
    }

    public static BigDecimal getFedexCompareWeight(ExpressAirWayBill bill){
        BigDecimal eawbLength = bill.getEawbLength() == null?BigDecimal.ZERO:bill.getEawbLength();
        BigDecimal eawbWidth = bill.getEawbWidth() == null?BigDecimal.ZERO:bill.getEawbWidth();
        BigDecimal eawbHeight = bill.getEawbHeight() == null?BigDecimal.ZERO:bill.getEawbHeight();
        BigDecimal eawbVolume = eawbLength.multiply(eawbWidth).multiply(eawbHeight);
        BigDecimal eawbTotalWight = bill.getEawbGrossweight();

        return compareWeight(eawbTotalWight,eawbVolume,volume_number_5);
    }

    public static boolean checkNotCnServiceType(String serviceType){

        if (notCnEpKeys.stream().anyMatch(serviceType::equals)){
            return true;
        }

        return false;
    }

    public static Integer getEpValue(String epKey,List<ExpressProperty> epList){
        Optional<ExpressProperty> optional = epList.stream().filter(property -> property.getEpKey().equals(epKey)).findFirst();
        if (optional.isPresent()) {
            // 存在
            ExpressProperty ep =  optional.get();
            return ep.getEpValueInt();
        } else {
            // 不存在
        }
        return null;
    }

    public static void main(String[] args) {
        ExpressAirWayBill bill = new ExpressAirWayBill();
        bill.setEawbLength(new BigDecimal(37));
        bill.setEawbWidth(new BigDecimal(27));
        bill.setEawbHeight(new BigDecimal(10));
        bill.setWeightValue(new BigDecimal(0.950));
        System.out.println(CheckUtil.getCompareWeight(bill));
//        System.out.println(CheckUtil.checkNonEad("FC_INBOUND","OK"));
//
//        List<String> list2 = new ArrayList<>();
//        list2.add("ASS");
//        list2.add("ASF");
//        list2.add("CPT");
//        list2.add("ROE");
//
//        CheckUtil.checkReduce(list2);
    }

}
