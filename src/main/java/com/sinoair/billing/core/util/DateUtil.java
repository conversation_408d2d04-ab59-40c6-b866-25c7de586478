package com.sinoair.billing.core.util;

import com.alibaba.fastjson.JSONObject;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: 大雄
 * @date: 2019-04-10
 * @time: 17:42
 * @description: To change this template use File | Settings | File Templates.
 */
public class DateUtil {

    public static final String YYYY_MM_DD = "yyyy-MM-dd";

    public static final String YYYY_MM = "yyyy-MM";

    public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    public static final String YYYYMMDDHHMMSSS = "yyyyMMddHHmmsss";

    public static final String DDMMYYHHMM = "ddMMyyHHmm";

    public static final String YYYYMMDD = "yyyyMMdd";

    public static final String YYYYMM = "yyyyMM";

    /**
     * 日志转换为时间
     * @param dateStr
     * @param pattern
     * @return
     * @throws ParseException
     */
    public static Date str2date(String dateStr, String pattern) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        return sdf.parse(dateStr);
    }

    /**
     * 日志转换为时间
     * @param date
     * @param pattern
     * @return
     * @throws ParseException
     */
    public static String date2str(Date date,String pattern) {
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        return sdf.format(date);
    }

    /**
     * 获取本月初的时间
     * @return
     */
    public static Date getThisMonthFirstDay() throws ParseException {
        Date date = new Date();
        String dateStr = date2str(date,YYYY_MM);
        return str2date(dateStr+"-01",YYYY_MM_DD);
    }

    /**
     * 获取上月初的时间
     * @return
     * @throws ParseException
     */
    public static Date getLastMonthFirstDay() throws ParseException {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(getThisMonthFirstDay());
        calendar.add(Calendar.MONTH, -1);
        calendar.set(Calendar.DATE, 1);
        return calendar.getTime();
    }

    /**
     * 获取当前时间前一天日期
     * @return
     * @throws ParseException
     */
    public static String getBeforeDay(){
        Calendar cal=Calendar.getInstance();
        cal.add(Calendar.DATE,-1);
        Date d=cal.getTime();

        SimpleDateFormat sp=new SimpleDateFormat(YYYYMMDD);
        String beforeDay=sp.format(d);//获取昨天日期
        return beforeDay;
    }

    /**
     * 获取当前时间前一天日期
     * @return
     * @throws ParseException
     */
    public static String getPastDay(int past){
        Calendar cal=Calendar.getInstance();
        cal.add(Calendar.DATE,past);
        Date d=cal.getTime();

        SimpleDateFormat sp=new SimpleDateFormat(YYYYMMDD);
        String pastDay=sp.format(d);//获取昨天日期
        return pastDay;
    }

    public static String getPastDayByDate(Date date,int past){
        Calendar cal=Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DATE,past);
        Date d=cal.getTime();

        SimpleDateFormat sp=new SimpleDateFormat(YYYYMMDD);
        String pastDay=sp.format(d);//获取昨天日期
        return pastDay;
    }

    public static int getHours(){
        Calendar c = Calendar.getInstance();//可以对每个时间域单独修改

        int year = c.get(Calendar.YEAR);

        int month = c.get(Calendar.MONTH);

        int date = c.get(Calendar.DATE);

        int hour = c.get(Calendar.HOUR_OF_DAY);

        int minute = c.get(Calendar.MINUTE);

        int second = c.get(Calendar.SECOND);

        System.out.println(year + "/" + month + "/" + date + " " +hour + ":" +minute + ":" + second);
        return hour;
    }

    /**
     * 获取当前天（几号）
     * @return
     * @throws ParseException
     */
    public static int getDay(Date date){
        int day = 0;
        try{
            Calendar c = Calendar.getInstance();

            c.setTime(date);
            day = c.get(Calendar.DATE);
        }catch (Exception e){
            e.printStackTrace();
        }

        return day;
    }

    public static String getYear(Date date){
        String year = "";
        try {
            Calendar c = Calendar.getInstance();

            c.setTime(date);
            int y = c.get(Calendar.YEAR);
            year = y+"";
        }catch (Exception e){
            e.printStackTrace();
        }
        return year;
    }

    public static String getYearMonth(Date date){
        String yearMonth = "";
        try{
            Calendar c = Calendar.getInstance();

            c.setTime(date);
            int year = c.get(Calendar.YEAR);
            int month = c.get(Calendar.MONTH)+1;
            String m = month<10?"0"+month:month+"";
            yearMonth = year+""+m;
        }catch (Exception e){
            e.printStackTrace();
        }

        return yearMonth;
    }

    public static String getCurDate(){
        return date2str(new Date(),YYYYMMDD);
    }

    /**
     * 比较日期大小
     * <p>
     * 2017年9月7日 16:15:53
     * xj
     *
     * @param DATE1      第一个时间
     * @param DATE2      第二个时间
     * @param dateFormat 日期格式
     * @return Integer null日期格式有误，1：第一个日期大，0：两个日期一样，-1：第二个日期大
     */
    public static Integer compareDate(String DATE1, String DATE2, String dateFormat) {
        DateFormat df = new SimpleDateFormat(dateFormat);
        try {
            Date dt1 = df.parse(DATE1);
            Date dt2 = df.parse(DATE2);
            if (dt1.getTime() > dt2.getTime()) {
                return 1;
            } else if (dt1.getTime() < dt2.getTime()) {
                return -1;
            } else {
                return 0;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

    public static Date getNowDateTime() {

        GregorianCalendar calendar = new GregorianCalendar();
        return calendar.getTime();
    }

    //获取年后2位
    public static String getYearAfter() {
        Calendar cal = Calendar.getInstance();
        //当前年
        String year = String.valueOf(cal.get(Calendar.YEAR));
        return year.substring(2, 4);
    }

    /**
     * 用SimpleDateFormat计算时间差
     * @throws ParseException
     */
    public static int calculateDayDiff(Date fromDate,Date toDate)  {
        SimpleDateFormat simpleFormat = new SimpleDateFormat("yyyy-MM-dd hh:mm");
        /*天数差*/
        Date fromDate1 = fromDate;
        Date toDate1 = toDate;
        long from1 = fromDate1.getTime();
        long to1 = toDate1.getTime();
        int days = (int) ((to1 - from1) / (1000 * 60 * 60 * 24));
//        System.out.println("两个时间之间的天数差为：" + days);
        return days;

    }

    /**
     * 用SimpleDateFormat计算时间差
     * @throws ParseException
     */
    public static void calculateHourDiff() throws ParseException {
        SimpleDateFormat simpleFormat = new SimpleDateFormat("yyyy-MM-dd hh:mm");


        /*小时差*/
        Date fromDate2 = simpleFormat.parse("2018-03-01 12:00");
        Date toDate2 = simpleFormat.parse("2018-03-12 12:00");
        long from2 = fromDate2.getTime();
        long to2 = toDate2.getTime();
        int hours = (int) ((to2 - from2) / (1000 * 60 * 60));
        System.out.println("两个时间之间的小时差为：" + hours);

    }

    /**
     * 用SimpleDateFormat计算时间差
     * @throws ParseException
     */
    public static void calculateminuteDiff() throws ParseException {
        SimpleDateFormat simpleFormat = new SimpleDateFormat("yyyy-MM-dd hh:mm");

        /*分钟差*/
        Date fromDate3 = simpleFormat.parse("2018-03-01 12:00");
        Date toDate3 = simpleFormat.parse("2018-03-12 12:00");
        long from3 = fromDate3.getTime();
        long to3 = toDate3.getTime();
        int minutes = (int) ((to3 - from3) / (1000 * 60));
        System.out.println("两个时间之间的分钟差为：" + minutes);
    }

    /**
     * 获取指定日期当月的第一天
     * @param date_str
     * @param
     * @return
     */
    public static String getFirstDayOfGivenMonth(String date_str){
        try {
            Calendar cale = Calendar.getInstance();
            SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
            cale.setTime(formatter.parse(date_str));
            cale.add(Calendar.MONTH, 0);
            cale.set(Calendar.DAY_OF_MONTH, 1);
            String firstDayOfMonth = formatter.format(cale.getTime()); // 当月第一天 2019-02-01
            return firstDayOfMonth;
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**

      * 获取指定日期下个月的第一天
     * @param date_str
     * @param
     * @return
     */
    public static String getFirstDayOfNextMonth(String date_str){
        try {
            Calendar cale = Calendar.getInstance();
            SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
            cale.setTime(formatter.parse(date_str));
            cale.add(Calendar.MONTH, 1);
            cale.set(Calendar.DAY_OF_MONTH, 1);
            String lastDayOfMonth = formatter.format(cale.getTime()); // 当月最后一天 2019-02-28
            return lastDayOfMonth;
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static Date getFirstDayOfPreMonth(Date date){
        try {
            Calendar cale = Calendar.getInstance();
            cale.setTime(date);
            cale.add(Calendar.MONTH, -1);
            cale.set(Calendar.DAY_OF_MONTH, 1);
//            String lastDayOfMonth = formatter.format(cale.getTime()); // 当月最后一天 2019-02-28
            return cale.getTime();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static Date getLastDayOfPreMonth(Date date){
        try {
            Calendar c = Calendar.getInstance();
            //当前日期设置为指定日期
            c.setTime(date);
            //指定日期月份减去一
            c.add(Calendar.MONTH, -1);
            //指定日期月份减去一后的 最大天数
            c.set(Calendar.DATE, c.getActualMaximum(Calendar.DATE));
            //获取上给月最后一天的日期
            Date lastDateOfPrevMonth = c.getTime();
            return lastDateOfPrevMonth;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    //两位年+两位月+两位日期
    public static String getYearMonthDay() {
        Calendar cal = Calendar.getInstance();
        //当前年
        String year = String.valueOf(cal.get(Calendar.YEAR));
        year = year.substring(2, 4);
        //当前月
        String month = String.valueOf((cal.get(Calendar.MONTH)) + 1);
        if (month.length() == 1) {
            month = "0" + month;
        }
        //当前月的第几天：即当前日
        String day_of_month = String.valueOf(cal.get(Calendar.DAY_OF_MONTH));
        if (day_of_month.length() == 1) {
            day_of_month = "0" + day_of_month;
        }

        String date = year + month + day_of_month;
        return date;
    }



    public static void main(String[] args) {
//        System.out.println(date2str(new Date(),YYYYMMDD));
//        System.out.println(getBeforeDay());
//        System.out.println(getCurDate());
//        System.out.println(getDay(new Date()));
//        try {
//            System.out.println(str2date("20190626",YYYYMMDD));
//        } catch (ParseException e) {
//            e.printStackTrace();
//        }
//        System.out.println(getPastDay(-1));
//        System.out.println(getPastDay(1));
//        System.out.println(getHours());
//
//        System.out.println(getHours());

//        String aa = "{\"nonSoCode\":\"00060491\"}";
//        JSONObject obj = JSONObject.parseObject(aa);
//        System.out.println(obj.getString("nonSoCode"));
//        System.out.println(obj.containsKey("eawbServicetype"));
//        System.out.println(obj.getString("eawbServicetype"));

        Date date = new Date();
        System.out.println(getFirstDayOfPreMonth(new Date()));
        System.out.println(getLastDayOfPreMonth(new Date()));
//        String MMdd = "MMdd";
//        String ddMM = "ddMM";
//        String MMyy = "MMyy";
//        String yyMM = "yyMM";
//        String MMyyyy = "MMyyyy";
//        String yyyyMM = "yyyyMM";
//        String yyMMdd = "yyMMdd";
//        String yyyyMMdd = "yyyyMMdd";
//
//        System.out.println("MMdd："+date2str(date,MMdd));
//        System.out.println("ddMM："+date2str(date,ddMM));
//        System.out.println("MMyy："+date2str(date,MMyy));
//        System.out.println("yyMM："+date2str(date,yyMM));
//        System.out.println("MMyyyy："+date2str(date,MMyyyy));
//        System.out.println("yyyyMM："+date2str(date,yyyyMM));
//        System.out.println("yyMMdd："+date2str(date,yyMMdd));
//        System.out.println("yyyyMMdd："+date2str(date,yyyyMMdd));

        try {
//            System.out.println("riqi："+str2date(date2str(date,YYYY_MM_DD),YYYY_MM_DD));
//            System.out.println("riqi："+calculateDayDiff(str2date("2020-03-20",YYYY_MM_DD),date));
//            System.out.println(DateUtil.date2str(DateUtil.getLastMonthFirstDay(),DateUtil.YYYYMMDD));
//            System.out.println(DateUtil.date2str(DateUtil.getThisMonthFirstDay(),DateUtil.YYYYMMDD));

//            String strDate = "2020-4-15 19:34:50";
//            Date date1 = DateUtil.str2date(strDate,DateUtil.YYYY_MM_DD_HH_MM_SS);

//            System.out.println(DateUtil.getFirstDayOfGivenMonth("2020-02-26"));
//            System.out.println(DateUtil.getFirstDayOfNextMonth("2020-02-26"));

//            String rrOccurtime = DateUtil.date2str(date1,DateUtil.YYYYMMDD);
//            System.out.println("rrOccurtime:"+rrOccurtime);
//            System.out.println(DateUtil.getFirstDayOfGivenMonth(rrOccurtime));
//            System.out.println(DateUtil.getFirstDayOfNextMonth(rrOccurtime));

//            System.out.println("指定日期："+getPastDayByDate(DateUtil.str2date("20200502",DateUtil.YYYYMMDD),1));

        } catch (Exception e) {
            e.printStackTrace();
        }

    }

}
