package com.sinoair.billing.core.util;

import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.nio.charset.Charset;
import java.util.Enumeration;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

/**
 * Created by IntelliJ IDEA.
 * User: <PERSON>
 * Date: 2016/12/19
 * Time: 13:34
 * Description: 文件操作工具类
 * To change this template use File | Settings | File Templates.
 */
public class FileUtils {

    /**
     * 获取文件目录
     * @param path
     * @return
     */
    public static File getDirByPath(String path){
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String strDirPath = request.getSession().getServletContext().getRealPath(path);
        File file = new File(strDirPath);
        if(!file.isDirectory()){
            file.mkdirs();
        }
        return file;
    }

    /**
     * 获取upload目录
     * @return
     */
    public static File getUploadFile(){
        File file = new File(getDirPath()+File.separator+"upload");
        if(!file.isDirectory()){
            file.mkdirs();
        }
        return file;
    }

    /**
     * 获取项目根目录
     * @return
     */
    public static String getDirPath(){
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String strDirPath = request.getSession().getServletContext().getRealPath(File.separator);
        return strDirPath;
    }

    public static void unZip(MultipartFile srcFile, String destDirPath, String savePath) throws RuntimeException, IOException {
        long startTime = System.currentTimeMillis();

        File file = null;
        InputStream ins = srcFile.getInputStream();
        file=new File(savePath+srcFile.getOriginalFilename());
//        LogUtils.info("MultipartFile transform to File,MultipartFile name:"+srcFile.getOriginalFilename());
        inputStreamToFile(ins, file);

        if (!file.exists()) {
            throw new RuntimeException(file.getPath() + ",file is not found");
        }
        ZipFile zipFile = null;
        try {
            zipFile = new ZipFile(file);
            Enumeration<?> entries = zipFile.entries();
            while (entries.hasMoreElements()) {
                ZipEntry entry = (ZipEntry) entries.nextElement();
//                LogUtils.info("zipFile context name:"+entry.getName());
                if (entry.isDirectory()) {
                    String dirPath = destDirPath + File.separator+ entry.getName();
                    File dir = new File(dirPath);
                    dir.mkdirs();
                }else {
                    File targetFile = new File(destDirPath + File.separator + entry.getName());
                    targetFile.setExecutable(true);
                    if(!targetFile.getParentFile().exists()){
                        targetFile.getParentFile().mkdirs();
                    }
                    targetFile.createNewFile();
                    InputStream is = zipFile.getInputStream(entry);
                    FileOutputStream fos = new FileOutputStream(targetFile);
                    int len;
                    byte[] buf = new byte[1024];
                    while ((len = is.read(buf)) != -1) {
                        fos.write(buf, 0, len);
                    }
                    fos.close();
                    is.close();
                }
            }
            long endTime = System.currentTimeMillis();
//            LogUtils.info("unZip time-->" + (endTime - startTime) + " ms");
        }catch(Exception e) {
            throw new RuntimeException("unzip error from FileUtil", e);
        } finally {
            if(zipFile != null){
                try {
                    zipFile.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }

            //MultipartFile change to file may create a temp file in the project root folder(delete the temp file)
            File del = new File(file.toURI());
            del.delete();
        }
    }

    public static void unZip(File file,String destDirPath) throws RuntimeException, IOException {
        long startTime = System.currentTimeMillis();


        if (!file.exists()) {
            throw new RuntimeException(file.getPath() + ",file is not found");
        }
        ZipFile zipFile = null;
        try {
            Charset gbk = Charset.forName("GBK");
            zipFile = new ZipFile(file,gbk);
            Enumeration<?> entries = zipFile.entries();
            while (entries.hasMoreElements()) {
                ZipEntry entry = (ZipEntry) entries.nextElement();
//                LogUtils.info("zipFile context name:"+entry.getName());
                if (entry.isDirectory()) {
                    String dirPath = destDirPath + File.separator+ entry.getName();
                    File dir = new File(dirPath);
                    dir.mkdirs();
                }else {
                    String fileName = file.getName().substring(0,file.getName().indexOf("."));
                    File targetDir = new File(destDirPath + File.separator + fileName);
                    if (!targetDir.exists()){
                        targetDir.mkdirs();
                    }
                    File targetFile = new File(targetDir + File.separator + entry.getName());
                    targetFile.setExecutable(true);
                    if(!targetFile.getParentFile().exists()){
                        targetFile.getParentFile().mkdirs();
                    }
                    targetFile.createNewFile();
                    InputStream is = zipFile.getInputStream(entry);
                    FileOutputStream fos = new FileOutputStream(targetFile);
                    int len;
                    byte[] buf = new byte[1024];
                    while ((len = is.read(buf)) != -1) {
                        fos.write(buf, 0, len);
                    }
                    fos.close();
                    is.close();
                }
            }
            long endTime = System.currentTimeMillis();
//            LogUtils.info("unZip time-->" + (endTime - startTime) + " ms");
        }catch(Exception e) {
            e.printStackTrace();
            throw new RuntimeException("unzip error from FileUtil", e);
        } finally {
            if(zipFile != null){
                try {
                    zipFile.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }

            //MultipartFile change to file may create a temp file in the project root folder(delete the temp file)
            File del = new File(file.toURI());
            del.delete();
        }
    }

    private static void inputStreamToFile(InputStream ins, File file) {
        try {
            OutputStream os = new FileOutputStream(file);
            int bytesRead = 0;
            byte[] buffer = new byte[8192];
            while ((bytesRead = ins.read(buffer, 0, 8192)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            os.close();
            ins.close();
//            LogUtils.info("MultipartFile transform to File completed!");
        }catch(Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * <AUTHOR>
     */
    public static List<File> getSubFiles(String  desFile, List<File> fileList) {
        File file = new File(desFile);
        File[] files = file.listFiles();
        for (File fileIndex : files) {
            if (!fileIndex.exists()) {
                throw new NullPointerException("Cannot find " + fileIndex);
            } else if (fileIndex.isFile()) {
                fileList.add(fileIndex);
            } else {
                if (fileIndex.isDirectory()) {
                    getSubFiles(fileIndex.getAbsolutePath(),fileList);
                }
            }
        }
        return fileList;
    }

    /**

     * 解压到指定目录

     * @param zipPath

     * @param descDir

     */

    public static void unZipFiles(String zipPath, String descDir) throws IOException {

//        unZipFiles(new File(zipPath), descDir);

    }

    /**

     * 解压文件到指定目录

     * 解压后的文件名，和之前一致

     * @param zipFile 待解压的zip文件

     * @param descDir 指定目录

     */

//    @SuppressWarnings("rawtypes")
//    public static void unZipFiles(File zipFile, String descDir) throws IOException {
//
//        ZipFile zip = new ZipFile(zipFile,Charset.forName("GBK"));//解决中文文件夹乱码
//
//        String name = zip.getName().substring(zip.getName().lastIndexOf('\\')+1, zip.getName().lastIndexOf('.'));
//
//        File pathFile = new File(descDir+name);
//
//        if (!pathFile.exists()) {
//
//            pathFile.mkdirs();
//
//        }
//
//        for (Enumeration extends ZipEntry> entries = zip.entries(); entries.hasMoreElements();) {
//
//            ZipEntry entry = (ZipEntry) entries.nextElement();
//
//            String zipEntryName = entry.getName();
//
//            InputStream in = zip.getInputStream(entry);
//
//            String outPath = (descDir + name +"/"+ zipEntryName).replaceAll("\\*", "/");
//
//// 判断路径是否存在,不存在则创建文件路径
//
//            File file = new File(outPath.substring(0, outPath.lastIndexOf('/')));
//
//            if (!file.exists()) {
//
//                file.mkdirs();
//
//            }
//
//// 判断文件全路径是否为文件夹,如果是上面已经上传,不需要解压
//
//            if (new File(outPath).isDirectory()) {
//
//                continue;
//
//            }
//
//// 输出文件路径信息
//
//// System.out.println(outPath);
//
//            FileOutputStream out = new FileOutputStream(outPath);
//
//            byte[] buf1 = new byte[1024];
//
//            int len;
//
//            while ((len = in.read(buf1)) > 0) {
//
//                out.write(buf1, 0, len);
//
//            }
//
//            in.close();
//
//            out.close();
//
//        }
//
//        System.out.println("******************解压完毕********************");
//
//        return;
//
//    }


}
