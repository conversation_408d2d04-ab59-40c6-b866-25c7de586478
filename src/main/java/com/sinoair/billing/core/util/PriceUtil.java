package com.sinoair.billing.core.util;

import com.sinoair.billing.domain.model.billing.ReceiptRecordTmp;
import com.sinoair.billing.domain.vo.price.PriceGradeVO;
import com.sinoair.billing.domain.vo.price.PriceReceiptVO;

import java.math.BigDecimal;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2019-06-17
 * @time: 17:42
 * @description: To change this template use File | Settings | File Templates.
 */
public class PriceUtil {

    public static BigDecimal initPrice = null;

    static BigDecimal zero = new BigDecimal(0);


    public static BigDecimal getPrice(List<PriceReceiptVO> priceReceiptList, ReceiptRecordTmp recordTmp){
        if (recordTmp == null){
            return initPrice;
        }
        if (priceReceiptList == null || priceReceiptList.size() == 0){
            return initPrice;
        }
        Integer prId = recordTmp.getPrId();
        PriceReceiptVO priceReceiptVO = getCheckByPrId(prId,priceReceiptList);
        if (priceReceiptVO == null){
            return initPrice;
        }
        if (!priceReceiptVO.getPrStatus().equals("ON")){
            return initPrice;
        }
        BigDecimal price = computePrice(priceReceiptVO,recordTmp);

        if (price != null){
            price = price.setScale(2, BigDecimal.ROUND_HALF_UP);
        }

        return price;
    }

    public static PriceReceiptVO getCheckByPrId(Integer prId,List<PriceReceiptVO> priceReceiptList){
        for (PriceReceiptVO priceReceiptVO : priceReceiptList){
            if (prId.intValue() == priceReceiptVO.getPrId().intValue()){
                return priceReceiptVO;
            }
        }
        return null;
    }

    public static BigDecimal computePrice(PriceReceiptVO priceReceiptVO,ReceiptRecordTmp recordTmp){
        String prType = priceReceiptVO.getPrType();
        BigDecimal price = null;
        BigDecimal prPrice = priceReceiptVO.getPrPrice();
        BigDecimal prBasePrice = priceReceiptVO.getPrBaseprice();
        BigDecimal weightValue = recordTmp.getWeightValue();
        switch (prType) {
            case "PERKG":
                //e.weight_value * pr.pr_price+pr.pr_baseprice
                price = prPrice.multiply(weightValue).add(prBasePrice);
                break;
            case "FIX":
                //pr.pr_price+pr.pr_baseprice
                price = prPrice.add(prBasePrice);
                break;
            case "ADD":
                price = getPriceAdd(priceReceiptVO,recordTmp);
                break;
            case "PERPC":
                price = getPricePerpc(priceReceiptVO,recordTmp);
                break;
            case "PERKG_MIN":
                price = getPricePerkgMin(priceReceiptVO,recordTmp);
                break;
            case "GRADE":
                price = getPriceGrade(priceReceiptVO,recordTmp);
                break;
            case "PERKGPC":
                price = getPricePerkgpc(priceReceiptVO,recordTmp);

                break;
            case "VAT":
                price = getPriceVat(priceReceiptVO,recordTmp);
                break;

            default:

        }

        return price;
    }

    private static BigDecimal getPriceAdd(PriceReceiptVO prVO,ReceiptRecordTmp recordTmp){
        BigDecimal price = null;
        BigDecimal weight_value = recordTmp.getWeightValue();
        BigDecimal pr_firstweight = prVO.getPrFirstweight();
        if (weight_value.compareTo(pr_firstweight) < 1){
            //and e.weight_value <= pr.pr_firstweight then
            // pr.pr_firstprice+pr.pr_baseprice
            price = prVO.getPrFirstprice().add(prVO.getPrBaseprice());
        }else{
            //and e.weight_value > pr.pr_firstweight and pr.pr_additionalweight > 0 then
            //pr.pr_firstprice +ceil((e.weight_value - pr.pr_firstweight)/pr.pr_additionalweight)*
            //pr.pr_additionalprice+pr.pr_baseprice
            if (prVO.getPrAdditionalweight().compareTo(zero) == 1){
                BigDecimal bd = (weight_value.subtract(pr_firstweight)).divide(prVO.getPrAdditionalweight());
                BigDecimal l = bd.setScale( 0, BigDecimal.ROUND_UP ); // 向上取整
                price = prVO.getPrFirstprice().add(l.multiply(prVO.getPrAdditionalprice())).add(prVO.getPrBaseprice());
            }
        }
        return price;
    }

    private static BigDecimal getPricePerpc(PriceReceiptVO prVO,ReceiptRecordTmp recordTmp){
        BigDecimal price = null;
        BigDecimal weight_value = recordTmp.getWeightValue();
        BigDecimal pr_firstweight = prVO.getPrFirstweight();
        Integer eawbPieces = recordTmp.getEawbPieces();
        if (pr_firstweight == null){
            //pr_firstweight is null then e.eawb_pieces*pr.pr_price+pr.pr_baseprice
            price = new BigDecimal(eawbPieces).multiply(prVO.getPrPrice()).add(prVO.getPrBaseprice());
        }else{
            if (weight_value.compareTo(pr_firstweight) == 1){
                //and pr_firstweight is not null and e.weight_value>pr_firstweight then
                //e.eawb_pieces*pr.pr_price+pr.pr_firstprice+pr.pr_baseprice
                price = new BigDecimal(eawbPieces).multiply(prVO.getPrPrice()).add(prVO.getPrBaseprice()).add(prVO.getPrFirstprice());

            }else{
                //and pr_firstweight is not null and e.weight_value<=pr_firstweight then
                //pr.pr_firstprice+pr.pr_baseprice
                price = prVO.getPrFirstprice().add(prVO.getPrBaseprice());
            }
        }

        return price;
    }

    private static BigDecimal getPricePerkgMin(PriceReceiptVO prVO,ReceiptRecordTmp recordTmp){
        BigDecimal price = null;
        BigDecimal weight_value = recordTmp.getWeightValue();
        BigDecimal pr_firstweight = prVO.getPrFirstweight();
        Integer eawbPieces = recordTmp.getEawbPieces();
        BigDecimal tmpPrice = weight_value.multiply(prVO.getPrPrice()).add(prVO.getPrBaseprice());
        //小于
        if (tmpPrice.compareTo(prVO.getPrMinprice()) == -1){
            //e.weight_value * pr.pr_price+pr.pr_baseprice >=pr.pr_minprice then
            //e.weight_value * pr.pr_price+pr.pr_baseprice
            price = prVO.getPrMinprice();
        }else{
            //e.weight_value * pr.pr_price+pr.pr_baseprice < pr.pr_minprice then pr.pr_minprice
            price = tmpPrice;
        }

        return price;
    }

    private static BigDecimal getPriceGrade(PriceReceiptVO prVO,ReceiptRecordTmp recordTmp){
        BigDecimal price = null;
        List<PriceGradeVO> priceGradeList = prVO.getPriceGradeList();
        String pgType = "";
        BigDecimal weightValue = recordTmp.getWeightValue();
        if (priceGradeList != null && priceGradeList.size() > 0){
            for (PriceGradeVO vo : priceGradeList){
                pgType = vo.getPgType();
                BigDecimal kgPrice = vo.getPgKgPrice() == null?zero:vo.getPgKgPrice();
                if (weightValue.compareTo(vo.getPgStart()) == 1 &&
                        weightValue.compareTo(vo.getPgEnd()) < 1){
                    if (pgType.equals("KG")){
                        //pg.pg_fixprice+nvl(pg.pg_kgprice,0)
                        price = vo.getPgFixprice().add(kgPrice);
                    }else if(pgType.equals("PERKG")){
                        //pg.pg_fixprice+nvl(v_num*pg.pg_kgprice,0)
                        price = weightValue.multiply(kgPrice).add(vo.getPgFixprice());
                    }
                    break;
                }
            }
        }

        return price;
    }

    private static BigDecimal getPricePerkgpc(PriceReceiptVO prVO,ReceiptRecordTmp recordTmp){
        BigDecimal price = null;
        //e.weight_value * pr.pr_price+e.eawb_pieces*pr.pr_baseprice
        BigDecimal wPrice = recordTmp.getWeightValue().multiply(prVO.getPrPrice());
        BigDecimal pbPrice = new BigDecimal(recordTmp.getEawbPieces()).multiply(prVO.getPrBaseprice());
        price = wPrice.add(pbPrice);

        return price;
    }

    private static BigDecimal getPriceVat(PriceReceiptVO prVO,ReceiptRecordTmp recordTmp){
        BigDecimal price = null;
        BigDecimal eawbCustdeclval = new BigDecimal(0);
        if (recordTmp.getEawbCustdeclval()!=null){
            eawbCustdeclval = recordTmp.getEawbCustdeclval();
        }
        //nvl(e.eawb_custdeclval,0)*pr.pr_price+pr.pr_baseprice
        price = eawbCustdeclval.multiply(prVO.getPrPrice()).add(prVO.getPrBaseprice());
        return price;
    }

}
