package com.sinoair.billing.core.util;


import com.sinoair.billing.domain.vo.system.ReturnObject;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: 大雄
 * @date: 2018/7/12
 * @time: 9:41
 * @description: 通用返回值工具类
 * To change this template use File | Settings | File Templates.
 */
public class ReturnObjectUtil {

    /**
     * 通用版本
     */
    private static final String VERSION = "v1.0";

    /**
     * 成功消息
     * @return
     */
    public static ReturnObject success(){
        return success(null);
    }

    /**
     * 成功消息
     * @param data
     * @return
     */
    public static  ReturnObject success(Object data){
        return success(data,ReturnObject.MESSAGE_SUCCESS);
    }

    /**
     * 成功消息
     * @param data
     * @param successMessage
     * @return
     */
    public static  ReturnObject success(Object data,String successMessage){
        ReturnObject returnObject = new ReturnObject<>();
        returnObject.setData(data);
        returnObject.setVersion(VERSION);
        returnObject.setMsgCode(ReturnObject.MSG_CODE_SUCCESS);
        returnObject.setMessage(successMessage);
        return returnObject;
    }

    /**
     * 失败消息
     * @return
     */
    public static ReturnObject fali(){
        return fail(null);
    }

    /**
     * 失败消息
     * @param data
     * @param errorMessage
     * @return
     */
    public static ReturnObject fail(Object data,String errorMessage){
        ReturnObject returnObject = new ReturnObject<>();
        returnObject.setData(data);
        returnObject.setVersion(VERSION);
        returnObject.setMsgCode(ReturnObject.MSG_CODE_FAIL);
        returnObject.setMessage(errorMessage);
        return returnObject;
    }

    /**
     * 失败消息
     * @param data
     * @return
     */
    public static ReturnObject fail(Object data){
        return fail(data,ReturnObject.MESSAGE_FAIL);
    }

    /**
     * 无数据
     * @return
     */
    public ReturnObject noData(){
        return noData(null);
    }

    /**
     * 无数据
     * @param data
     * @param noDataMessage
     * @return
     */
    public static ReturnObject noData(Object data,String noDataMessage){
        ReturnObject returnObject = new ReturnObject<>();
        returnObject.setData(data);
        returnObject.setVersion(VERSION);
        returnObject.setMsgCode(ReturnObject.MSG_CODE_NODATA);
        returnObject.setMessage(noDataMessage);
        return returnObject;
    }

    /**
     * 无数据
     * @param data
     * @return
     */
    public static ReturnObject noData(Object data){
        return noData(data,ReturnObject.MESSAGE_NODATA);
    }

    /**
     * 异常
     * @return
     */
    public static ReturnObject exception(){
        return exception(null);
    }

    /**
     * 异常
     * @param data
     * @param exceptionMessage
     * @return
     */
    public static ReturnObject exception(Object data,String exceptionMessage){
        ReturnObject returnObject = new ReturnObject<>();
        returnObject.setData(data);
        returnObject.setVersion(VERSION);
        returnObject.setMsgCode(ReturnObject.MSG_CODE_SERVER_EXCEPTION);
        returnObject.setMessage(exceptionMessage);
        return returnObject;
    }

    /**
     * 异常
     * @param data
     * @return
     */
    public static ReturnObject exception(Object data){
        return noData(data,ReturnObject.MESSAGE_SERVER_EXCEPTION);
    }

    /**
     * 超时
     * @return
     */
    public static ReturnObject timeout(){
        return timeout(null);
    }

    /**
     * 超时
     * @param data
     * @param timeoutMessage
     * @return
     */
    public static ReturnObject timeout(Object data,String timeoutMessage){
        ReturnObject returnObject = new ReturnObject<>();
        returnObject.setData(data);
        returnObject.setVersion(VERSION);
        returnObject.setMsgCode(ReturnObject.MSG_CODE_SESSION_TIMEOUT);
        returnObject.setMessage(timeoutMessage);
        return returnObject;
    }

    /**
     * 超时
     * @param data
     * @return
     */
    public static ReturnObject timeout(Object data){
        return noData(data,ReturnObject.MESSAGE_SESSION_TIMEOUT);
    }

    /**
     * token校验错误
     * @return
     */
    public static ReturnObject errorToken(){
        return errorToken(null);
    }

    /**
     * token校验错误
     * @param data
     * @param errorTokenMessage
     * @return
     */
    public static ReturnObject errorToken(Object data,String errorTokenMessage){
        ReturnObject returnObject = new ReturnObject<>();
        returnObject.setData(data);
        returnObject.setVersion(VERSION);
        returnObject.setMsgCode(ReturnObject.MSG_CODE_ERROR_TOKEN);
        returnObject.setMessage(errorTokenMessage);
        return returnObject;
    }

    /**
     * token校验错误
     * @param data
     * @return
     */
    public static ReturnObject errorToken(Object data){
        return noData(data,ReturnObject.MESSAGE_ERROR_TOKEN);
    }

}
