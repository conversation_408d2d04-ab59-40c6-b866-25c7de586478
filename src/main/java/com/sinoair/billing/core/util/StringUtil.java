package com.sinoair.billing.core.util;

import java.io.File;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.*;

public class StringUtil {

    public static int nullToNumber(String str, int defalut) {
        if (isEmpty(str)) {
            return defalut;
        }
        return Integer.parseInt(str);
    }

    public static boolean isEmpty(String str) {
        return str == null || "".equals(str.trim());
    }

    public static boolean isNotEmpty(String str) {
        return str != null && !"".equals(str);
    }

    public static String nullToBlank(Object obj) {
        return obj != null ? obj.toString() : "";
    }

    public static String getFilePath(String fileName) {
        String date = getFormatTime(new Date(), "yyyyMMdd-hhmmss");
        //String name = fileName.substring(fileName.lastIndexOf("\\")+1,fileName.lastIndexOf("."));
        String name = fileName.substring(0, fileName.lastIndexOf("."));
        return name + date;
    }

    public static String getFilePath(String dir, String fileName) {
        String d = dir;
        if (!fileName.endsWith("\\") && !fileName.endsWith("/")) {
            d += File.separator;
        }

        return d + fileName;
    }

    public static String getFileName(String fileName) {
        if (fileName.indexOf("\\") > -1)
            return fileName.substring(fileName.lastIndexOf("\\") + 1, fileName.length());
        else if (fileName.indexOf("/") > -1) {
            return fileName.substring(fileName.lastIndexOf("/") + 1, fileName.length());
        } else {
            return fileName;
        }
    }

    public static String getFileEXTName(String fileName) {
        return fileName.substring(fileName.lastIndexOf(".") + 1, fileName.length()).toLowerCase();
    }

    public static String getFileEXTName(String fileName, String newFilename) {
        return newFilename + '.' + getFileEXTName(fileName);
    }

    //如果date为空则取当前日期
    public static String getFormatTime(Date date, String format) {
        SimpleDateFormat df = new SimpleDateFormat(format);
        if (date == null) {
            return df.format(new Date());
        } else {
            return df.format(date);
        }
    }

    //如果date为空则返回""
    public static String getShowTime(Date date, String format) {
        SimpleDateFormat df = new SimpleDateFormat(format);
        if (date == null) {
            return "";
        } else {
            return df.format(date);
        }
    }

    public static String roundDoule(double value) {
        DecimalFormat format = new DecimalFormat("0.00");
        return format.format(value);
    }

    public static String[] split(String s, String separator) {
        if (s == null || s.trim().length() == 0)
            return null;
        String result[] = (String[]) null;
        String tmp[] = new String[500];
        String parseStr = s;
        int k = 0;
        int j = 0;
        for (int pos = 0; pos != -1; ) {
            pos = parseStr.indexOf(separator);
            if (pos != -1) {
                tmp[k++] = parseStr.substring(j, pos);
                parseStr = parseStr.substring(pos + separator.length(),
                        parseStr.length());
            }
        }

        tmp[k++] = parseStr.substring(j, parseStr.length());
        if (k == 0)
            return result;
        result = new String[k];
        for (int i = 0; i < k; i++)
            result[i] = tmp[i];

        return result;
    }

    /**
     * 如果null则转换为空字符串
     * zmj 2016-7-1
     *
     * @param str
     * @param ifTrim 是不是要去掉前后空格：true，是;false 否；
     * @return
     */
    public static String null2Empty(String str, boolean ifTrim) {
        String result = "";
        if (null != str) {
            result = str;
            if (ifTrim) {
                result = result.trim();
            }
        }
        return result;
    }

    /**
     * 如果null或null字符串或单个0则转换为空字符串
     * zmj 2016-7-1
     */
    public static String null3Empty(String str) {
        String result = "";
        if (null != str && !"null".equals(str) && !"0".equals(str)) {
            result = str;
        }
        return result;
    }

    public static String null2Empty(String str) {
        return null2Empty(str, false);
    }

    /**
     * 把特殊字符替换掉，目前用于CSV文件生成时，字符的过滤，CSV有这些字符格式就乱了
     * zmj 2016-7-1
     *
     * @param str
     * @param newStr 替代字符，如果null，则替换成""
     * @return
     */
    public static String filterCharacter(String str, String newStr) {
        String filterArr[] = {",", "\n", "\r", "\""}; //要过滤这些字符，把这些字符变成newStr;
        String result = "";
        if (newStr == null) {
            newStr = "";//如果替换字符为null，默认替换成空
        }
        if (null != str) {
            result = str;
            for (int i = 0; i < filterArr.length; i++) {
                result = result.replace(filterArr[i], newStr);
            }
        }
        return result;
    }

    /**
     * 把特殊字符替换掉
     * zmj 2016-7-1
     *
     * @param str
     * @return
     */
    public static String filterCharacter(String str) {
        return filterCharacter(str, null);
    }

    /**
     * 把特殊字符替换成空格
     * zmj 2016-7-1
     *
     * @param str
     * @return
     */
    public static String filterCharacterSpace(String str) {
        return filterCharacter(str, " ");
    }

    /**
     * 在字符串后追加字符
     * zmj 2016-7-1
     *
     * @param str
     * @param addStr
     * @return
     */
    public static String additionalCharacter(String str, String addStr) {
        String result = "";
        if (addStr == null) {
            addStr = "";
        }
        if (str != null) {
            result = str;
        }
        return result + addStr;
    }

    /**
     * 在字符串后面追加逗号
     * zmj 2016-7-1
     *
     * @param str
     * @return
     */
    public static String additionalComma(String str) {
        return additionalCharacter(str, ",");
    }

    public static String additionalNewline(String str) {
        return additionalCharacter(str, "\n");
    }

    /**
     * csv过滤字符
     * zmj 2016-7-1
     *
     * @param str
     * @return
     */
    public static String csvDealString(String str) {
        return filterCharacterSpace(null2Empty(str, false));
    }

    /**
     * csv过滤完字符，在字符后边追加逗号
     *
     * @param str
     * @return
     */
    public static String csvDealStringAddComma(String str) {
        return additionalComma(csvDealString(str));
    }

    /**
     * csv过滤完字符，在字符后边追加换行符“/n”
     *
     * @param str
     * @return
     */
    public static String csvDealStringAddNewline(String str) {
        return additionalNewline(csvDealString(str));
    }

    /**
     * trim和upper
     *
     * @param str
     * @return
     */
    public static String trimAndUpper(String str) {
        return null2Empty(str, true).toUpperCase();
    }

    /**
     * 截掉所有非数字字符，如果长度超过N，截取前20位，一般用于纯数字的电话号码
     *
     * @param cclPhoneStr
     * @return
     */
    public static String getCclPhoneNum(String cclPhoneStr) {
        if (cclPhoneStr == null || "".equals(cclPhoneStr)) return "";
        String result = "";
        if (cclPhoneStr == null) {
            result = "";
        } else {
            result = cutBegin(getPureNumStr(cclPhoneStr), "0");
        }
        if (result.length() > 20) {
            result = result.substring(0, 20);
        }
        return result;
    }


    /**
     * 截取掉开头的某些字符窜，这些字符可能出现
     *
     * @param str
     * @return
     */
    public static String cutBegin(String str, String cutStr) {
        if (str != null && !"".equals(str) && cutStr != null && !"".equals(cutStr)) {
            while (str.startsWith(cutStr)) {
                if (str.length() > cutStr.length()) {
                    str = str.substring(cutStr.length());
                } else {
                    str = "";
                }
            }
        } else if (str == null || "".equals(str)) {
            str = "";
        }
        return str;
    }

    /**
     * 过滤掉非数据字符
     *
     * @param input
     * @return
     */
    public static String getPureNumStr(String input) {
        String result = "";
        if (input != null && !"".equals(input)) {
            result = input.toString().replaceAll("\\D", "");
        } else {
            result = "";
        }
        return result;
    }

    public static String filterAndQuotesAndCommaForCSV(String str) {
        return additionalComma("\"" + csvDealString(str) + "\"");
    }

    public static String getTotalWeight(BigDecimal totalWeight) {
        String totalWeightStr = "0";
        totalWeight = totalWeight.setScale(2, BigDecimal.ROUND_DOWN);
        if (totalWeight.doubleValue() <= 0) {
            totalWeightStr = "0.01";
        } else {
            totalWeightStr = totalWeight.doubleValue() + "";
        }
        return totalWeightStr;
    }

    /**
     * 检查关键字
     *
     * @param str
     * @param arrStr
     * @return
     */
    public static String getExistKeyValue(String str, String[] arrStr) {
        String result = "";
        if (str != null && arrStr != null) {
            for (int i = 0; i < arrStr.length; i++) {
                if (getNumAndLetter(str.toUpperCase()).contains(getNumAndLetter(arrStr[i]).toUpperCase())) {
                    result = result + "," + arrStr[i];
                }
            }
        }
        if (!"".equals(result)) {
            result = result.substring(1);
        }
        return result;
    }

    /**
     * 截掉所有非字母数字的字符
     *
     * @param obj
     * @return
     */
    public static String getNumAndLetter(Object obj) {
        String str = "";
        if (obj == null) {
            str = "";
        } else {
            str = obj.toString().replaceAll("[^0-9a-zA-Z]", "");
        }
        return str;
    }

    /**
     * 获取格式化整数
     *
     * @param len 格式化后的数据长度
     * @param num 需要格式化的单号
     *            <p>
     *            如果数字长度超过len，则从头截取长度为len的字符
     *            返回值:String 格式化后的整数</p>
     */
    public static String getIntFormString(int len, String num) throws Exception {
        String strRecord = String.valueOf(num);
        return getFixedString(strRecord, len, "0", true);
    }

    /**
     * 获取格式化整数
     * len：int 格式化后的数据长度;num:int 需要格式化的整数;
     * 如果数字长度超过len，则从头截取长度为len的字符
     * 返回值:String 格式化后的整数
     */
    public static String getIntFormString(int len, int num) throws Exception {
        String strRecord = String.valueOf(num);
        return getFixedString(strRecord, len, "0", true);
    }

    /**
     * 取得定长的字符串
     *
     * @param str
     * @param len
     * @param addStr
     * @param ahead  补充字符串放到前面
     * @return
     */
    public static String getFixedString(String str, int len, String addStr, boolean ahead) {
        if (len <= 0) {
            return "";
        }
        if (addStr == null || "".equals(addStr) || addStr.length() != 1) {
            addStr = " ";
        }
        int strLen = str.length();
        if (strLen > len) {
            str = str.substring(0, len);
        } else if (strLen < len) {
            for (int i = 0; i < len - strLen; i++) {
                if (ahead) {
                    str = addStr + str;
                } else {
                    str = str + addStr;
                }

            }
        }
        return str;
    }


    public static String nullToEmpty(String src) {
        if (src == null) {
            return "";
        } else {
            return src.trim();
        }
    }

    public static String format(String value, Object... paras) {
        return MessageFormat.format(value, paras);
    }

//    public static boolean isRealNull(String str) {
//        boolean isRealNull=false;
//        if(){
//
//        }
//    }

    //参数类型是Map<String,String> 因为支付只能用string的参数。如果诸君还需要修改的话，那也可以适当的做调整
    /**
     *
     * map转str
     * @param map
     * @return
     */
    public static String getMapToString(Map<String,String> map){
        Set<String> keySet = map.keySet();
        //将set集合转换为数组
        String[] keyArray = keySet.toArray(new String[keySet.size()]);
        //给数组排序(升序)
        Arrays.sort(keyArray);
        //因为String拼接效率会很低的，所以转用StringBuilder。博主会在这篇博文发后不久，会更新一篇String与StringBuilder开发时的抉择的博文。
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < keyArray.length; i++) {
            // 参数值为空，则不参与签名 这个方法trim()是去空格
            if (map.get(keyArray[i]).trim().length() > 0) {
                sb.append(keyArray[i]).append("=").append(map.get(keyArray[i]).trim());
            }
            if(i != keyArray.length-1){
                sb.append("&");
            }
        }
        return sb.toString();
    }

    /**
     * 2018年10月24日更新
     * String转map
     * @param str
     * @return
     */
    public static Map<String,String> getStringToMap(String str,String separator){
        //判断str是否有值
        if(null == str || "".equals(str)){
            return null;
        }
        //根据&截取
        String[] strings = str.split(separator);
        //设置HashMap长度
        int mapLength = strings.length;
        //判断hashMap的长度是否是2的幂。
        if((strings.length % 2) != 0){
            mapLength = mapLength+1;
        }

        Map<String,String> map = new HashMap<>(mapLength);
        //循环加入map集合
        for (int i = 0; i < strings.length; i++) {
            //截取一组字符串
//            String[] strArray = strings[i].split("=");
            //strArray[0]为KEY  strArray[1]为值
//            map.put(strArray[0],strArray[1]);
            map.put(i+"",strings[i].trim());
        }
        return map;
    }
}
