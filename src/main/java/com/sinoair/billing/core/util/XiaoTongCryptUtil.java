package com.sinoair.billing.core.util;

import org.apache.commons.codec.digest.DigestUtils;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: 大雄
 * @date: 2021/1/22
 * @time: 16:15
 * @description: To change this template use File | Settings | File Templates.
 */
public class XiaoTongCryptUtil {


    /**
     * 加签计算
     * @param appId 注册id
     * @param secret 加签key
     * @param requestBody 请求内容
     * @param timestamp 时间戳 需要同步返回到接口
     * @return
     */
    public static String sign(String appId,String secret,String requestBody,Long timestamp){
        return DigestUtils.md5Hex(timestamp + "|" + requestBody + "|" + appId + "|" + secret);
    }

}
