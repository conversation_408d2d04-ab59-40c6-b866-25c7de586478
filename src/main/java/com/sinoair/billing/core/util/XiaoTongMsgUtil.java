package com.sinoair.billing.core.util;


import com.alibaba.fastjson.JSONObject;
import com.sinoair.billing.domain.constant.XiaoTongMsgConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: 大雄
 * @date: 2021/2/2
 * @time: 16:26
 * @description: To change this template use File | Settings | File Templates.
 */
public class XiaoTongMsgUtil {

    private static Logger logger = LoggerFactory.getLogger(XiaoTongMsgUtil.class);

    /**
     * 小通发送
     * @param appId
     * @param secert
     * @param title
     * @param message
     */
    public static void sendMsg(String appId,String secert,String title,String message) throws Exception {
        JSONObject jo = new JSONObject();
        jo.put("title",title);
        jo.put("msgText",message);
        String requestBody = jo.toJSONString();
        Long timestamp = System.currentTimeMillis();
        String sign = XiaoTongCryptUtil.sign(appId,secert,requestBody,timestamp);
        String url = XiaoTongMsgConstant.XIAO_TONG_MESSAGE_UTL
                +"?timestamp="+timestamp
                +"&appId="+appId
                +"&sign="+sign;
        String result = HttpClientUtil.postStr(url,requestBody);
        logger.info("消息请求地址：" + url);
        logger.info("消息请求内容：" + requestBody);
        logger.info("响应内容：" + result);
    }

    /**
     * 系统通知：短信、邮件
     * @param title
     * @param message
     */
    public static void ceosNotify(String title,String message) throws Exception {
        sendMsg(XiaoTongMsgConstant.CEOS_NOTIFY_APP_ID,XiaoTongMsgConstant.CEOS_NOTIFY_SECERT,title,message);
    }


    /**
     * 预警通知
     * @param title
     * @param message
     * @throws Exception
     */
    public static void notifyCeosWarning(String title,String message) throws Exception {
        sendMsg(XiaoTongMsgConstant.CEOS_WARNING_NOTIFY_APP_ID,XiaoTongMsgConstant.CEOS_WARNING_NOTIFY_SECERT,title,message);
    }
}
