package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.AppDebitManifest;

import java.util.List;

public interface AppDebitManifestMapper {
    int deleteByPrimaryKey(Integer dmId);

    int insert(AppDebitManifest record);

    int insertSelective(AppDebitManifest record);

    AppDebitManifest selectByPrimaryKey(Integer dmId);

    int updateByPrimaryKeySelective(AppDebitManifest record);

    int updateByPrimaryKey(AppDebitManifest record);

    List<AppDebitManifest> selectStatusIsN();

    void batchUpdateById(List dmsList);
}