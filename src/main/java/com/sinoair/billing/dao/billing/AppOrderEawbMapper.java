package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.AppOrderEawb;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AppOrderEawbMapper {
    int deleteByPrimaryKey(Short id);

    int insert(AppOrderEawb record);

    int insertSelective(AppOrderEawb record);

    AppOrderEawb selectByPrimaryKey(Short id);

    int updateByPrimaryKeySelective(AppOrderEawb record);

    int updateByPrimaryKey(AppOrderEawb record);

    List<AppOrderEawb> selectListByOrderId(@Param("orderId") Integer orderId);
}