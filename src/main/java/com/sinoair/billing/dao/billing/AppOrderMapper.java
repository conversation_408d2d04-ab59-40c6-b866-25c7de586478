package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.AppOrder;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AppOrderMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AppOrder record);

    int insertSelective(AppOrder record);

    AppOrder selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppOrder record);

    int updateByPrimaryKey(AppOrder record);

    List<AppOrder> selectNotExistInEawb();

    void batchUpdateById(@Param("list") List dmList);
}