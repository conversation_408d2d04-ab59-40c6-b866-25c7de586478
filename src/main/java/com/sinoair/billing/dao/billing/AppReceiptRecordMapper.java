package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.AppOrder;
import com.sinoair.billing.domain.model.billing.AppReceiptRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AppReceiptRecordMapper {
    int deleteByPrimaryKey(Long rrId);

    int insert(AppReceiptRecord record);

    int insertSelective(AppReceiptRecord record);

    AppReceiptRecord selectByPrimaryKey(Long rrId);

    int updateByPrimaryKeySelective(AppReceiptRecord record);

    int updateByPrimaryKey(AppReceiptRecord record);

    List<AppReceiptRecord> selectListByDmId(@Param("dmId") Integer dmId);
}