package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.BillManifest;
import com.sinoair.billing.domain.model.billing.CreditManifest;
import com.sinoair.billing.domain.model.billing.DebitManifest;
import com.sinoair.billing.domain.vo.manifest.RelationCreditVO;
import com.sinoair.billing.domain.vo.manifest.RelationDebitVO;
import com.sinoair.billing.domain.vo.receipt.ReceiptVo;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository("BillManifestMapper")
public interface BillManifestMapper {

    int deleteByPrimaryKey(Long id);

    int insert(BillManifest record);

    int insertSelective(BillManifest record);

    BillManifest selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(BillManifest record);

    int updateByPrimaryKey(BillManifest record);

    int deleteByManifestId(Long mId);


    List<RelationDebitVO> listBillManifestByMId(Long mId);

    List<RelationCreditVO> listBillCreditManifestByMId(Long mId);

    List<ReceiptVo> listDebitManifest(DebitManifest debitManifest);

    List<RelationCreditVO> listCreditManifest(CreditManifest creditManifest);

    int deleteByDmId(Map map);

    int deleteByCmId(Map map);

    List<Long> selectDmByMId(Long mId);
}