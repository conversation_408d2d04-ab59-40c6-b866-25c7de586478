package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.BillingDetails;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository("BillingDetailsMapper")
public interface BillingDetailsMapper {
    int deleteByPrimaryKey(Short bSyscode);

    int insert(BillingDetails record);

    int insertSelective(BillingDetails record);

    BillingDetails selectByPrimaryKey(Short bSyscode);

    int updateByPrimaryKeySelective(BillingDetails record);

    int updateByPrimaryKey(BillingDetails record);

    List<Map> querydiff(@Param(value = "b_code") String b_code, @Param(value = "is_close") String is_close);

    List<Map> selectByBId(@Param(value = "bSyscode") String bSyscode);
}