package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.ceosbi.EawbEadFlat;

import java.math.BigDecimal;
import java.util.List;

public interface BillingEawbEadFlatMapper {
    int deleteByPrimaryKey(BigDecimal eawbSyscode);

    int insert(EawbEadFlat record);

    int insertSelective(EawbEadFlat record);

    EawbEadFlat selectByPrimaryKey(BigDecimal eawbSyscode);
    EawbEadFlat selectByPrintcode(String eawbPrintcode);

    int updateByPrimaryKeySelective(EawbEadFlat record);

    int updateByPrimaryKey(EawbEadFlat record);

    int insertBatch(List<EawbEadFlat> recordList);

    int updateBatch(List<EawbEadFlat> recordList);
}