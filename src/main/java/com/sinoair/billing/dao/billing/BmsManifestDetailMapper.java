package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.BmsManifestDetail;
import org.springframework.stereotype.Repository;

@Repository("BmsManifestDetailMapper")
public interface BmsManifestDetailMapper {
//    int deleteByPrimaryKey(Long bmdSyscode);
//
//    int insert(BmsManifestDetail record);
//
//    int insertSelective(BmsManifestDetail record);
//
//    BmsManifestDetail selectByPrimaryKey(Long bmdSyscode);
//
//    int updateByPrimaryKeySelective(BmsManifestDetail record);
//
//    int updateByPrimaryKey(BmsManifestDetail record);
}