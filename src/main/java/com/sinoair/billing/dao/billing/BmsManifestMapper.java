package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.BmsManifest;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("BmsManifestMapper")
public interface BmsManifestMapper {
    int deleteByPrimaryKey(Long bmSyscode);

    int insert(BmsManifest record);

    int insertSelective(BmsManifest record);

    BmsManifest selectByPrimaryKey(Long bmSyscode);

    int updateByPrimaryKeySelective(BmsManifest record);

    int updateByPrimaryKey(BmsManifest record);

    List<BmsManifest> selectByMId(Long mId);

    String selectBmsBussnissSeq();
}