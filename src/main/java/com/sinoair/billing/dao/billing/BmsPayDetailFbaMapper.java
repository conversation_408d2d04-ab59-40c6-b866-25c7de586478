package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.BmsPayDetailFba;
import org.springframework.stereotype.Repository;

@Repository("BmsPayDetailFbaMapper")
public interface BmsPayDetailFbaMapper {
    int deleteByPrimaryKey(Long bpdfSyscode);

    int insert(BmsPayDetailFba record);

    int insertSelective(BmsPayDetailFba record);

    BmsPayDetailFba selectByPrimaryKey(Long bpdfSyscode);

    int updateByPrimaryKeySelective(BmsPayDetailFba record);

    int updateByPrimaryKey(BmsPayDetailFba record);
}