package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.BmsPayDetail;
import org.springframework.stereotype.Repository;

@Repository("BmsPayDetailMapper")
public interface BmsPayDetailMapper {
    int deleteByPrimaryKey(Long bpdSyscode);

    int insert(BmsPayDetail record);

    int insertSelective(BmsPayDetail record);

    BmsPayDetail selectByPrimaryKey(Long bpdSyscode);

    int updateByPrimaryKeySelective(BmsPayDetail record);

    int updateByPrimaryKey(BmsPayDetail record);

    int updateAmount(Long bpdSyscode);
}