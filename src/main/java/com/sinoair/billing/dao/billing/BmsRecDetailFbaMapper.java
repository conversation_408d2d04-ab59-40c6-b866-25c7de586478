package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.BmsRecDetailFba;
import org.springframework.stereotype.Repository;

@Repository("BmsRecDetailFbaMapper")
public interface BmsRecDetailFbaMapper {
    int deleteByPrimaryKey(Long bmdfSyscode);

    int insert(BmsRecDetailFba record);

    int insertSelective(BmsRecDetailFba record);

    BmsRecDetailFba selectByPrimaryKey(Long bmdfSyscode);

    int updateByPrimaryKeySelective(BmsRecDetailFba record);

    int updateByPrimaryKey(BmsRecDetailFba record);
}