package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.BmsManifestDetail;
import org.springframework.stereotype.Repository;

@Repository("BmsRecDetailMapper")
public interface BmsRecDetailMapper {
    int deleteByPrimaryKey(Long bmdSyscode);

    int insert(BmsManifestDetail record);

    int insertSelective(BmsManifestDetail record);

    BmsManifestDetail selectByPrimaryKey(Long bmdSyscode);

    int updateByPrimaryKeySelective(BmsManifestDetail record);

    int updateByPrimaryKey(BmsManifestDetail record);

    int updateAmount(Long bmdSyscode);
}