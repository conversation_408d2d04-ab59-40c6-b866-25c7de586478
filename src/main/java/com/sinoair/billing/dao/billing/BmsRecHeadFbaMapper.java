package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.BmsRecHeadFba;
import com.sinoair.billing.domain.model.billing.SinotransOrderWeight;
import org.springframework.stereotype.Repository;

@Repository("BmsRecHeadFbaMapper")
public interface BmsRecHeadFbaMapper {
    int insert(BmsRecHeadFba record);

    int insertSelective(BmsRecHeadFba record);

    int deleteByMonth(Integer collectMonth);

    int updateByPrimaryKeySelective(BmsRecHeadFba record);
}