package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.BusinessCompare;
import com.sinoair.billing.domain.model.billing.BusinessCompareKey;
import org.springframework.stereotype.Component;

import java.util.List;

@Component("BusinessCompareMapper")
public interface BusinessCompareMapper {
    int deleteByPrimaryKey(BusinessCompareKey key);

    int insert(BusinessCompare record);

    int insertSelective(BusinessCompare record);

    BusinessCompare selectByPrimaryKey(BusinessCompareKey key);

    int updateByPrimaryKeySelective(BusinessCompare record);

    int updateByPrimaryKey(BusinessCompare record);

    void insertBatch(List<BusinessCompare> list);
}