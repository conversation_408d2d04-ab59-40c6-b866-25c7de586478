package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.CainiaoSettlementFail;

import java.util.List;

public interface CainiaoSettlementFailMapper {
    int deleteByPrimaryKey(Integer cnsfailId);

    int insert(CainiaoSettlementFail record);

    int insertSelective(CainiaoSettlementFail record);

    CainiaoSettlementFail selectByPrimaryKey(Integer cnsfailId);

    int updateByPrimaryKeySelective(CainiaoSettlementFail record);

    int updateByPrimaryKey(CainiaoSettlementFail record);

    void insertBatch(List<CainiaoSettlementFail> list);
}