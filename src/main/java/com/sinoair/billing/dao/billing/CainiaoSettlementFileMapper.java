package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.CainiaoSettlementFile;

public interface CainiaoSettlementFileMapper {
    int deleteByPrimaryKey(Integer cnsfId);

    int insert(CainiaoSettlementFile record);

    int insertSelective(CainiaoSettlementFile record);

    CainiaoSettlementFile selectByPrimaryKey(Integer cnsfId);

    int updateByPrimaryKeySelective(CainiaoSettlementFile record);

    int updateByPrimaryKey(CainiaoSettlementFile record);
}