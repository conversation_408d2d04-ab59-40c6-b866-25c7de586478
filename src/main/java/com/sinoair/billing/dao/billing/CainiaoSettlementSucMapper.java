package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.CainiaoSettlementSuc;
import org.springframework.stereotype.Component;

import java.util.List;

@Component("CainiaoSettlementSucMapper")
public interface CainiaoSettlementSucMapper {
    int insert(CainiaoSettlementSuc record);

    int insertSelective(CainiaoSettlementSuc record);

    int insertBatch(List<CainiaoSettlementSuc> list);

    int countCainiaoSettlement(CainiaoSettlementSuc record);
}