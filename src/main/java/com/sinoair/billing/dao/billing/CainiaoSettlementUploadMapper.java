package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.CainiaoSettlementUpload;
import org.springframework.stereotype.Component;

import java.util.List;

@Component("CainiaoSettlementUploadMapper")
public interface CainiaoSettlementUploadMapper {
    int deleteByPrimaryKey(Integer cnsuId);

    int insert(CainiaoSettlementUpload record);

    int insertSelective(CainiaoSettlementUpload record);

    CainiaoSettlementUpload selectByPrimaryKey(Integer cnsuId);

    int updateByPrimaryKeySelective(CainiaoSettlementUpload record);

    int updateByPrimaryKey(CainiaoSettlementUpload record);

    List<CainiaoSettlementUpload> selectList(CainiaoSettlementUpload record);

    List<CainiaoSettlementUpload> selectPendingList(CainiaoSettlementUpload record);
}