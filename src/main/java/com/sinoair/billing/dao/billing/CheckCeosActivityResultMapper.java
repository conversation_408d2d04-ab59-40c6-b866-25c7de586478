package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.CheckCeosActivityResult;

import java.util.Date;
import java.util.List;

public interface CheckCeosActivityResultMapper {
    int deleteByPrimaryKey(Date checkDate);

    int insert(CheckCeosActivityResult record);

    int insertSelective(CheckCeosActivityResult record);

    CheckCeosActivityResult selectByPrimaryKey(Date checkDate);

    int updateByPrimaryKeySelective(CheckCeosActivityResult record);

    int updateByPrimaryKey(CheckCeosActivityResult record);

    int insertBatch(List<CheckCeosActivityResult> list);

    int deleteBatch(String resultType);

    List<CheckCeosActivityResult> list(String resultType);

    List<CheckCeosActivityResult> getCeosActivityResult();

    List<CheckCeosActivityResult> getActivityResult();

    List<CheckCeosActivityResult> getRRResult();

}