package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.CheckEawbNo;

public interface CheckEawbNoMapper {
    int deleteByPrimaryKey(Short checkId);

    int insert(CheckEawbNo record);

    int insertSelective(CheckEawbNo record);

    CheckEawbNo selectByPrimaryKey(Short checkId);

    int updateByPrimaryKeySelective(CheckEawbNo record);

    int updateByPrimaryKey(CheckEawbNo record);

    CheckEawbNo selectByType(String checkType);
}