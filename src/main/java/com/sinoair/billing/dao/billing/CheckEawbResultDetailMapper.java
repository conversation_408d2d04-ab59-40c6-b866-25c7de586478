package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.CheckEawbResultDetail;
import com.sinoair.billing.domain.model.billing.CheckLackRRMawb;
import com.sinoair.billing.domain.model.billing.PaymentRecord;
import com.sinoair.billing.domain.vo.check.EawbCheckVO;

import java.util.List;

public interface CheckEawbResultDetailMapper {
    int deleteByPrimaryKey(Long cerdSyscode);

    int insert(CheckEawbResultDetail record);

    int insertSelective(CheckEawbResultDetail record);

    CheckEawbResultDetail selectByPrimaryKey(Long cerdSyscode);

    int updateByPrimaryKeySelective(CheckEawbResultDetail record);

    int updateByPrimaryKey(CheckEawbResultDetail record);

    int insertBatch(List<CheckEawbResultDetail> list);

    List<CheckEawbResultDetail> selectDetailByPrintCode(String eawbPrintcode);

    List<CheckEawbResultDetail> selectResultDetailList(EawbCheckVO vo);

    int countResultDetailList(EawbCheckVO vo);

    int updateBatch(List<CheckEawbResultDetail> list);

    Long selectMaxSysCode();

    Long selectMinDetailSysCode();

    List<CheckEawbResultDetail> selectEawbBySysCode(EawbCheckVO vo);

    int insertBatchHis(List<CheckEawbResultDetail> list);

    int deleteBatch(List<CheckEawbResultDetail> list);

    int updateBatchByOther(List<CheckEawbResultDetail> list);

    Long selectDetailEndNo(EawbCheckVO vo);

    int countByPrintcode(String eawbPrintcode);

    List<CheckEawbResultDetail> selectDetailByCondition(EawbCheckVO vo);

    List<CheckEawbResultDetail> selectActivityCeosList(EawbCheckVO vo);

    int countActivityCeosList(EawbCheckVO vo);

    List<CheckEawbResultDetail> selectNonDetailByCondition(EawbCheckVO vo);

    int insertBatchLackEawb(List<String> list);
    int insertBatchLackEawbMo(List<String> list);

    List<String> listLackEawb();

    int countListLackEawb();

    int deleteBatchLackEawb(List<String> list);

    int insertBatchLackRRMawb(List<CheckLackRRMawb> list);

    List<CheckLackRRMawb> listLackRRMawb();

    int countListLackRRMawb();

    int deleteBatchLackRRMawb(List<String> list);

    List<PaymentRecord> selectPrByCondition(EawbCheckVO param);


}