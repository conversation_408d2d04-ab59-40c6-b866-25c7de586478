package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.CheckEawbResult;
import com.sinoair.billing.domain.model.billing.CheckSummaryResult;
import com.sinoair.billing.domain.vo.check.EawbCheckVO;

import java.util.List;

public interface CheckEawbResultMapper {
    int deleteByPrimaryKey(String eawbPrintcode);

    int insert(CheckEawbResult record);

    int insertSelective(CheckEawbResult record);

    CheckEawbResult selectByPrimaryKey(String eawbPrintcode);

    int updateByPrimaryKeySelective(CheckEawbResult record);

    int updateByPrimaryKey(CheckEawbResult record);

    int countResult(String eawbPrintcode);

    void insertBatch(List<CheckEawbResult> list);

    void updateBatch(List<CheckEawbResult> list);

    Long selectMinEawbSysCode();

    List<CheckSummaryResult> selectSummaryResult();

    List<CheckEawbResult> selectCheckResultList(EawbCheckVO param);

    CheckSummaryResult selectResultNumByDate(Long checkDate);

    List<CheckSummaryResult> selectCheckDate();

    int selectCNNum(Long checkDate);

    int selectOtherNum(Long checkDate);

    int selectCodeNum(Long checkDate);

    int selectActivityNum(Long checkDate);

    int selectRRNum(Long checkDate);

}