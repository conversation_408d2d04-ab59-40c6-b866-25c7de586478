package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.CheckEbaCeosTmp;

import java.util.List;

public interface CheckEbaCeosTmpMapper {
    int deleteByPrimaryKey(Long cectSyscode);

    int insert(CheckEbaCeosTmp record);

    int insertSelective(CheckEbaCeosTmp record);

    CheckEbaCeosTmp selectByPrimaryKey(Long cectSyscode);

    int updateByPrimaryKeySelective(CheckEbaCeosTmp record);

    int updateByPrimaryKey(CheckEbaCeosTmp record);

    int insertBatch(List<CheckEbaCeosTmp> list);

    int deleteBatch(List<CheckEbaCeosTmp> list);

    List<CheckEbaCeosTmp> list();

    int count();

    int insertBatchError(List<CheckEbaCeosTmp> list);

    int deleteBatchError(List<CheckEbaCeosTmp> list);

    List<CheckEbaCeosTmp> listError();
}