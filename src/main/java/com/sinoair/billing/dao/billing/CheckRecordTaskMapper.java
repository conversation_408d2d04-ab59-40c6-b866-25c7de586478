package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.CheckRecordTask;
import com.sinoair.billing.domain.model.billing.RecordTask;
import com.sinoair.billing.domain.vo.query.InsRecordQuery;

import java.math.BigDecimal;
import java.util.List;

public interface CheckRecordTaskMapper {
    int deleteByPrimaryKey(BigDecimal crtId);

    int insert(CheckRecordTask record);

    int insertSelective(CheckRecordTask record);

    CheckRecordTask selectByPrimaryKey(BigDecimal crtId);

    int updateByPrimaryKeySelective(CheckRecordTask record);

    int updateByPrimaryKey(CheckRecordTask record);

    /**
     * 查询序列
     * @return
     */
    Integer selectSeq();

    List<CheckRecordTask> selectByCondition(InsRecordQuery param);
}