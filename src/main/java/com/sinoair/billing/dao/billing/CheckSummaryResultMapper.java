package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.CheckSummaryResult;

import java.util.Date;
import java.util.List;

public interface CheckSummaryResultMapper {
    int deleteByPrimaryKey(Date checkDate);

    int insert(CheckSummaryResult record);

    int insertSelective(CheckSummaryResult record);

    CheckSummaryResult selectByPrimaryKey(Date checkDate);

    int updateByPrimaryKeySelective(CheckSummaryResult record);

    int updateByPrimaryKey(CheckSummaryResult record);

    int insertBatch(List<CheckSummaryResult> list);

    int deleteBatch();
}