package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.CheckSynEbaConfig;

import java.util.List;

public interface CheckSynEbaConfigMapper {
    int insert(CheckSynEbaConfig record);

    int insertSelective(CheckSynEbaConfig record);

    List<CheckSynEbaConfig> listSynEbaConfig(CheckSynEbaConfig record);

    List<CheckSynEbaConfig> listFlatEbaConfig(CheckSynEbaConfig record);
}