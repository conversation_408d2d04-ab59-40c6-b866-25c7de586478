package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.Company;
import com.sinoair.billing.domain.vo.FilterParam;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository("CompanyMapper")
public interface CompanyMapper {
    int deleteByPrimaryKey(String companyId);

    int insert(Company record);

    int insertSelective(Company record);

    Company selectByPrimaryKey(String companyId);

    Company selectTreeByPrimaryKey(String companyId);

    int updateByPrimaryKeySelective(Company record);

    int updateByPrimaryKey(Company record);
    //模糊查询供应商
    List<Company> selectVonder(@Param(value = "companyName") String companyName);
    //查询登录人的下级供应商
    List<Company> selectNextVonder(@Param(value = "company_p_id") String company_p_id);

    /**
     * 获取可用的公司列表
     * @return
     */
    List<Company> selectListON();
    List<Map> getCompanyListMap(FilterParam filter);

    List<Company> getCompanyListByPid(FilterParam filterParam);

    int selectCountByCode(@Param("sac_id") String sac_id);

}