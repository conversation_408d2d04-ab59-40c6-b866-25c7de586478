package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.CorreosDetail;
import com.sinoair.billing.domain.model.billing.PaymentBillDetail;

import java.util.List;

public interface CorreosDetailMapper {
    int insert(CorreosDetail record);

    int insertSelective(CorreosDetail record);

    int insertBatch(List<CorreosDetail> detailList);

    List<PaymentBillDetail> selectCorreosDetail(CorreosDetail record);
}