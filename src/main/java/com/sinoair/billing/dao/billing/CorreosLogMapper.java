package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.CorreosLog;

import java.util.List;

public interface CorreosLogMapper {
    int insert(CorreosLog record);

    int insertSelective(CorreosLog record);

    List<String> listCorreosFile(String fileMonth);

    int updateByMonthAndFile(CorreosLog record);

    int countCorreosFile(String fileMonth);

    CorreosLog selectSumCorreosFile(String fileMonth);
}