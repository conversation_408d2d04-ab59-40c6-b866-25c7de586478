package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.CorreosManifest;

import java.util.List;

public interface CorreosManifestMapper {
    int deleteByPrimaryKey(String fileMonth);

    int insert(CorreosManifest record);

    int insertSelective(CorreosManifest record);

    CorreosManifest selectByPrimaryKey(String fileMonth);

    int updateByPrimaryKeySelective(CorreosManifest record);

    int updateByPrimaryKey(CorreosManifest record);

    List<CorreosManifest> listCorreosManifest(CorreosManifest record);

    int updateHandleCount(CorreosManifest record);
}