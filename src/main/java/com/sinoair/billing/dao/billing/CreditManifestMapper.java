package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.BmsManifestDetail;
import com.sinoair.billing.domain.model.billing.BmsPayDetail;
import com.sinoair.billing.domain.model.billing.CreditManifest;
import com.sinoair.billing.domain.vo.FilterParam;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository("CreditManifestMapper")
public interface CreditManifestMapper {
    int deleteByPrimaryKey(int cmId);

    int insert(CreditManifest record);

    int insertSelective(CreditManifest record);

    CreditManifest selectByPrimaryKey(Short cmId);

    int updateByPrimaryKeySelective(CreditManifest record);

    int updateByPrimaryKey(CreditManifest record);

    List<Map<String,Object>> selectInvoiceInfoForSearch(Map<String, Object> keyword);

    Map<String,Object> selectInvoiceInfoById(String invoiceId);

    List<Map> selectBalanceManifestList(FilterParam filterParam);

    int updateCreditManifest(Integer cmId);

    CreditManifest getByCmCode(@Param(value = "cmCode") String cmCode);

    void deletePaymentMinusByCmId(@Param(value = "cmId") Integer cmId);

    List<BmsPayDetail> getBmsPayAmount(@Param(value = "list") List<Integer> list);

    List<BmsPayDetail>  getBmsPayBySo(@Param("soCode") String soCode,@Param("endTime") String endTime);

    List<BmsPayDetail>  getBmsPayAll();
}