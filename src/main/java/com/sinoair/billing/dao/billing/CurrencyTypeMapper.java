package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.CurrencyType;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository("CurrencyTypeMapper")
public interface CurrencyTypeMapper {
    int deleteByPrimaryKey(String ctCode);

    int insert(CurrencyType record);

    int insertSelective(CurrencyType record);

    CurrencyType selectByPrimaryKey(String ctCode);

    int updateByPrimaryKeySelective(CurrencyType record);

    int updateByPrimaryKey(CurrencyType record);

    List<CurrencyType> getCurrencyType();

    String getCurrencyTypeByctCode(@Param(value = "ctCode") String ctCode);


    CurrencyType getCurrencyCodeByType(String ctCode);

    List<Map<String,String>> selectAllCodeNameForSelector();
}