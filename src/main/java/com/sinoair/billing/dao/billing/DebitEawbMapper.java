package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.BmsManifestDetail;
import com.sinoair.billing.domain.model.billing.DebitEawb;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository("DebitEawbMapper")
public interface DebitEawbMapper {


    List<DebitEawb> selectDebitEawb();

    void deleteBatchByEawb(List<DebitEawb> list);

    int insertByDmId(@Param(value = "dmId") Long dmId);

    int insertCNByDmId(@Param(value = "dmId") Long dmId);

    int insertCNTemporaryByDmId(@Param(value = "dmId") Long dmId);
    int insertCNTemporaryByTempId(@Param(value = "tempId") Long tempId);

    int countDebitEawb();


    List<BmsManifestDetail> selectBmsManifestAmount(DebitEawb param);

    void deleteAll();

    void truncateTable();
}