package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.DebitHandleTask;

import java.util.List;

public interface DebitHandleTaskMapper {
    int deleteByPrimaryKey(Short dmId);

    int insert(DebitHandleTask record);

    int insertSelective(DebitHandleTask record);

    DebitHandleTask selectByPrimaryKey(Short dmId);

    int updateByPrimaryKeySelective(DebitHandleTask record);

    int updateByPrimaryKey(DebitHandleTask record);

    List<DebitHandleTask> listWaitRun();

    List<DebitHandleTask> listWaitPending();

    int countRun();

    int countRelation();
}