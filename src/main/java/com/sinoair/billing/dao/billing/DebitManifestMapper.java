package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.BmsManifestDetail;
import com.sinoair.billing.domain.model.billing.DebitManifest;
import com.sinoair.billing.domain.model.billing.SettlementObject;
import com.sinoair.billing.domain.vo.FilterParam;
import com.sinoair.billing.domain.vo.receipt.ReceiptCsvVo;
import com.sinoair.billing.domain.vo.receipt.ReceiptVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository("DebitManifestMapper")
public interface DebitManifestMapper {
    int deleteByPrimaryKey(Short dmId);

    int insert(DebitManifest record);

    int insertSelective(DebitManifest record);

    DebitManifest selectByPrimaryKey(Integer dmId);

    int updateByPrimaryKeySelective(DebitManifest record);

    int updateByPrimaryKey(DebitManifest record);

    int getSeqDebitManifest();

    List<Map> getDebitManifestListMap(FilterParam filter);

    List<ReceiptVo> getDebitManifestBydmCode(String dmCode);

    void updateTotaDebitManifestBydmId(DebitManifest debitManifest);

    void updateNullTotaDebitManifestBydmId(DebitManifest debitManifest);

    void updatedmStatusdBydmCode(DebitManifest debitManifest);

    void updateOffdBydmCode(DebitManifest debitManifest);

    DebitManifest getDmIdByDmCode(@Param(value = "dmCode") String dmCode);

    ReceiptVo getToTalDebitManifestByDmCode(@Param(value = "dmCode") String dmCode);

    List<ReceiptCsvVo> selectCsvInfo(Map map);

    SettlementObject selectSoTypeByDmid(@Param(value = "dmId") Integer dmId);

    void deleteReceiptMinusByDmCode(@Param(value = "dmCode") String dmCode);

    Map<String,Object> getSumDmTotalfcByDmCodes(Map<String, Object> record);

    void updateDividedByDmCodes(Map<String, Object> record);

    ReceiptVo getToTalDebitManifestByDmId(@Param(value = "dmId") Integer dmId);

    void updateEmailStatusByDmIds(@Param(value = "dmIds") List<Integer> dmIds);

    List<Map<String,Object>> selectBillEmail(Map<String, Object> param);

    List<DebitManifest> listEmailStatus(Map<String, Object> param);

    ReceiptVo getToTalEawbByDmId(@Param(value = "dmId") Integer dmId);

    ReceiptVo getToTalMawbByDmId(@Param(value = "dmId") Integer dmId);

    List<BmsManifestDetail> getBmsManifestAmount(@Param(value = "list") List<Integer> list);

    List<BmsManifestDetail>  getBmsDetailBySo(@Param("soCode") String soCode,@Param("endTime") String endTime);
    List<BmsManifestDetail> getBmsDetailAll();

    void insertBatch(@Param("list") List<DebitManifest> manifestList);
}