package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.DebitManifestTemporary;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("DebitManifestTemporaryMapper")
public interface DebitManifestTemporaryMapper {
    int deleteByPrimaryKey(Short dmTempId);

    int insert(DebitManifestTemporary record);

    int insertSelective(DebitManifestTemporary record);

    DebitManifestTemporary selectByPrimaryKey(Short dmTempId);

    int updateByPrimaryKeySelective(DebitManifestTemporary record);

    int updateByPrimaryKey(DebitManifestTemporary record);

    List<DebitManifestTemporary> selectList();
    List<DebitManifestTemporary> selectTmpList();

    int updateTemporaryWeight(DebitManifestTemporary record);
    int updateTemporaryAmount(DebitManifestTemporary record);

    List<Long> selectIdByDmId(Long dmId);

    List<DebitManifestTemporary> selectTemporaryByDmId(Long dmId);
}