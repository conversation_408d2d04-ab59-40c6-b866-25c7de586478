package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.DebitManifest;
import com.sinoair.billing.domain.model.billing.DebitOrder;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository("DebitOrderMapper")
public interface DebitOrderMapper {
    int deleteByPrimaryKey(Long dmId);

    int insert(DebitOrder record);

    int insertSelective(DebitOrder record);

    DebitOrder selectByPrimaryKey(Long dmId);

    int updateByPrimaryKeySelective(DebitOrder record);

    int updateByPrimaryKey(DebitOrder record);

    List<DebitOrder> listDebitOrder(String sysStatus);

    int countDebitOrder(String sysStatus);

    List<DebitOrder> listDebitManifestDetail(Map<String, Object> param);
}