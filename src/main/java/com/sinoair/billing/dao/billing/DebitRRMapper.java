package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.DebitRR;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("DebitRRMapper")
public interface DebitRRMapper {


    List<DebitRR> selectDebitRR(@Param(value = "dmId") Long dmId);

    void deleteBatchByRR(List<DebitRR> list);

    int countDebitRR(@Param(value = "dmId") Long dmId);

}