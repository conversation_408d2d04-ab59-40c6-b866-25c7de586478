package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.BmsManifestDetail;
import com.sinoair.billing.domain.model.billing.DebitServicetype;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("DebitServicetypeMapper")
public interface DebitServicetypeMapper {
    int insert(DebitServicetype record);

    int insertSelective(DebitServicetype record);

    void updateByPrimaryKeySelective(DebitServicetype record);

    List<DebitServicetype> selectByStatus(DebitServicetype record);

    List<BmsManifestDetail> selectBmsManifestWeight(DebitServicetype record);
}