package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.EawbCustomTax;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("EawbCustomTaxMapper")
public interface EawbCustomTaxMapper {
    int deleteByPrimaryKey(Long ectSyscode);

    int insert(EawbCustomTax record);

    int insertSelective(EawbCustomTax record);

    EawbCustomTax selectByPrimaryKey(Long ectSyscode);

    int updateByPrimaryKeySelective(EawbCustomTax record);

    int updateByPrimaryKey(EawbCustomTax record);

    int insertBatch(List<EawbCustomTax> list);
}