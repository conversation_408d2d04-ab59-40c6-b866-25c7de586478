package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.EawbEbaTrackDis;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("EawbEbaTrackDisMapper")
public interface EawbEbaTrackDisMapper {
    int deleteByPrimaryKey(Short etdSyscode);

    int insert(EawbEbaTrackDis record);

    int insertSelective(EawbEbaTrackDis record);

    EawbEbaTrackDis selectByPrimaryKey(Short etdSyscode);

    int updateByPrimaryKeySelective(EawbEbaTrackDis record);

    int updateByPrimaryKey(EawbEbaTrackDis record);

    List<EawbEbaTrackDis> list(EawbEbaTrackDis record);
}