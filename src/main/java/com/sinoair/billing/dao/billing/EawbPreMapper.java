package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.EawbPre;
import org.springframework.stereotype.Component;

import java.util.List;

@Component("EawbPreMapper")
public interface EawbPreMapper {
    int deleteByPrimaryKey(Long eawbSyscode);

    int insert(EawbPre record);

    int insertSelective(EawbPre record);

    EawbPre selectByPrimaryKey(Long eawbSyscode);

    int updateByPrimaryKeySelective(EawbPre record);

    int updateByPrimaryKey(EawbPre record);

    int insertBatch(List<EawbPre> recordList);

    int updateBatch(List<EawbPre> recordList);

    EawbPre selectByPrintcode(String eawbPrintcode);
}