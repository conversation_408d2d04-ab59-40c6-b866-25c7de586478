package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.EbaTask;
import com.sinoair.billing.domain.vo.query.CeosQuery;

import java.math.BigDecimal;
import java.util.List;

public interface EbaTaskMapper {
    int deleteByPrimaryKey(BigDecimal taskId);

    int insert(EbaTask record);

    int insertSelective(EbaTask record);

    EbaTask selectByPrimaryKey(BigDecimal taskId);

    int updateByPrimaryKeySelective(EbaTask record);

    int updateByPrimaryKey(EbaTask record);

    List<EbaTask> selectByCondition(CeosQuery param);

    EbaTask selectByTaskType(String taskType);
}