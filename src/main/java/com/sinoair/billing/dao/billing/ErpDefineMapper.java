package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.ErpDefine;

import java.util.List;

public interface ErpDefineMapper {
    int deleteByPrimaryKey(String erpCode);

    int insert(ErpDefine record);

    int insertSelective(ErpDefine record);

    ErpDefine selectByPrimaryKey(String erpCode);

    int updateByPrimaryKeySelective(ErpDefine record);

    int updateByPrimaryKey(ErpDefine record);

    List<ErpDefine> selectErpList();
}