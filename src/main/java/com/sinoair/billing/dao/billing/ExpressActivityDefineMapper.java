package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.ExpressActivityDefine;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository("ExpressActivityDefineMapper")
public interface ExpressActivityDefineMapper {
    int deleteByPrimaryKey(Short eadSyscode);

    int insert(ExpressActivityDefine record);

    int insertSelective(ExpressActivityDefine record);

    ExpressActivityDefine selectByPrimaryKey(Short eadSyscode);

    int updateByPrimaryKeySelective(ExpressActivityDefine record);

    int updateByPrimaryKey(ExpressActivityDefine record);

    List<Map> selectEadCodeName();
}