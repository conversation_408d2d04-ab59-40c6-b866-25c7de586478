package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.ExpressActivityStatusType;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository("ExpressActivityStatusTypeMapper")
public interface ExpressActivityStatusTypeMapper {
    int deleteByPrimaryKey(Short eastSyscode);

    int insert(ExpressActivityStatusType record);

    int insertSelective(ExpressActivityStatusType record);

    ExpressActivityStatusType selectByPrimaryKey(Short eastSyscode);

    int updateByPrimaryKeySelective(ExpressActivityStatusType record);

    int updateByPrimaryKey(ExpressActivityStatusType record);

    List<Map> selectEastCodeName();
}