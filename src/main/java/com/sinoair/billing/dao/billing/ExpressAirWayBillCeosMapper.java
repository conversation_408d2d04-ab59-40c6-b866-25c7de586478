package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.ExpressAirWayBillCeos;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("ExpressAirWayBillCeosMapper")
public interface ExpressAirWayBillCeosMapper {
    int deleteByPrimaryKey(Short eawbSyscode);

    int insert(ExpressAirWayBillCeos record);

    int insertSelective(ExpressAirWayBillCeos record);

    ExpressAirWayBillCeos selectByPrimaryKey(Short eawbSyscode);

    int updateByPrimaryKeySelective(ExpressAirWayBillCeos record);

    int updateByPrimaryKey(ExpressAirWayBillCeos record);

    int insertBatch(List<ExpressAirWayBillCeos> list);

    int deleteBySoCodeEmpty();
}