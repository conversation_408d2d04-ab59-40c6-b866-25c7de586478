package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.BmsRecHeadFba;
import com.sinoair.billing.domain.model.billing.ExpressAirWayBill;
import com.sinoair.billing.domain.model.billing.SinotransOrderWeight;
import com.sinoair.billing.domain.vo.FilterParam;
import com.sinoair.billing.domain.vo.ceos.CeosEawbEaVO;
import com.sinoair.billing.domain.vo.query.ActivityQuery;
import com.sinoair.billing.domain.vo.query.CommonQuery;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Component("ExpressAirWayBillMapper")
public interface ExpressAirWayBillMapper {
    int deleteByPrimaryKey(Integer eawbSyscode);

    int insert(ExpressAirWayBill record);

    int insertSelective(ExpressAirWayBill record);

    ExpressAirWayBill selectByPrimaryKey(Integer eawbSyscode);

    int updateByPrimaryKeySelective(ExpressAirWayBill record);

    int updateByPrimaryKey(ExpressAirWayBill record);

    List<ExpressAirWayBill> selectBySacIDAndMawbCodeAndRef2(ExpressAirWayBill expressAirWayBill);
    List<ExpressAirWayBill> selectByPrintCodeAndRef2(ExpressAirWayBill expressAirWayBill);
    /**
     * 根据sacid mawb 统计条数
     * @param expressAirWayBill
     * @return
     */
    int selectCountBySacIDandMawbCode(ExpressAirWayBill expressAirWayBill);
    /**
     * 统计总毛重
     * @param expressAirWayBill
     * @return
     */
    BigDecimal selectCountGWeight(ExpressAirWayBill expressAirWayBill);

    /**
     * 统计计费重量
     * @param expressAirWayBill
     * @return
     */
    BigDecimal selectCountCWeight(ExpressAirWayBill expressAirWayBill);

    /**
     * 通过printCode查询数量
     * @param eawbPrintcode
     * @return
     */
    int selectCountByPrintCode(String eawbPrintcode);

    List<Map> searchExpressAirWayBillTool(FilterParam filterParam);

    List<Map> selectEawbList(FilterParam filterParam);
    List<Map> selectPriceReceipt(@Param(value = "transmodeId") String transmodeId,
                                 @Param(value = "serviceType") String serviceType,
                                 @Param(value = "companyId") String companyId,
                                 @Param(value = "soCode") String soCode,
                                 @Param(value = "entryTime") String entryTime);
    List<Map> selectPriceReceiptPlan(@Param(value = "transmodeId") String transmodeId,
                                     @Param(value = "serviceType") String serviceType,
                                     @Param(value = "companyId") String companyId,
                                     @Param(value = "soCode") String soCode,
                                     @Param(value = "printCode") String printCode);
    List<Map> selectPricePay(@Param(value = "sid") int sid,
                             @Param(value = "entryTime") String entryTime);
    List<Map> selectServerPay(@Param(value = "transmodeId") String transmodeId,
                              @Param(value = "serviceType") String serviceType,
                              @Param(value = "companyId") String companyId
    );
    List<Map> selectPricePayPlan(@Param(value = "transmodeId") String transmodeId,
                                 @Param(value = "serviceType") String serviceType,
                                 @Param(value = "companyId") String companyId,
                                 @Param(value = "printCode") String printCode);

    List<String> selectEawbPrintcodeList(@Param("eawbPintCodes") List eawbPintCodes);


    void insertBatch(@Param("list") List list);


    void deleteEawbByPrintCode(@Param("eawbPintCodes") List eawbPintCodes);
    void deleteActivityByPrintCode(@Param("eawbPintCodes") List eawbPintCodes);

    void batchUpdateEawbTrackingNoByCode(@Param("list") List list);

    void updateEawbTrackingNoByMawbCode(@Param("eawbTrackingNo") String eawbTrackingNo,
                                        @Param("eawbUpdatetime") Date eawbUpdatetime,
                                        @Param("mawbCode") String mawbCode,
                                        @Param("serviceType") String serviceType);

    void batchUpdateEawbWeightByReference(@Param("list") List list);

    List<ExpressAirWayBill> selectByRef1(@Param(value = "eawbReference1") String eawbReference1);

    int countBySoCode(ActivityQuery query);

    void batchUpdateHandletime(@Param("list") List list);

    ExpressAirWayBill selectByEawbPrintCode(@Param("eawbPrintCode") String eawbPrintCode);
    //非菜鸟
    ExpressAirWayBill selectOtherByEawbPrintCode(@Param("eawbPrintCode") String eawbPrintCode);

    ExpressAirWayBill selectByRef2(@Param(value = "eawbReference2") String eawbReference2);

    ExpressAirWayBill selectOtherByRef2(@Param(value = "eawbReference2") String eawbReference2);


    void batchUpdatMawb(@Param("list") List<CeosEawbEaVO> list);

    void batchUpdateMawbOther(@Param("list") List<CeosEawbEaVO> list);

    void batchUpdatInfo(@Param("list") List<ExpressAirWayBill> list);


    int countEawbByMawbCode(@Param("eawbPrintCode") String eawbPrintCode, @Param("mawbCode") String mawbCode);
    int countEawbOtherByMawbCode(@Param("eawbPrintCode") String eawbPrintCode, @Param("mawbCode") String mawbCode);

    int countByMawbCode(@Param("mawbCode") String mawbCode);
    int countOtherByMawbCode(@Param("mawbCode") String mawbCode);

    List<SinotransOrderWeight> selectOrderWeightCollect();

    void updateSinotransIdBySo(CommonQuery param);

    List<String> selectUpdateTimeNull(CommonQuery param);

    void updateEawbTime(ExpressAirWayBill expressAirWayBill);

    List<BmsRecHeadFba> selectOrderWeightCollectFba(CommonQuery param);

    void updateSinotransId(ExpressAirWayBill expressAirWayBill);

    ExpressAirWayBill selectByOrderId(@Param("orderId") Integer orderId);

    void updateStatusByEawb(@Param("params") Map<String,Object> params);

}
