package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.ExpressAssignment;
import com.sinoair.billing.domain.model.billing.ExpressAssignmentActual;
import com.sinoair.billing.domain.vo.query.CommonQuery;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("ExpressAssignmentActualMapper")
public interface ExpressAssignmentActualMapper {
    int deleteByPrimaryKey(Long eaaSyscode);

    int insert(ExpressAssignmentActual record);

    int insertSelective(ExpressAssignmentActual record);

    ExpressAssignmentActual selectByPrimaryKey(Long eaaSyscode);

    int updateByPrimaryKeySelective(ExpressAssignmentActual record);

    int updateByPrimaryKey(ExpressAssignmentActual record);

    List<ExpressAssignmentActual> selectByEaCode(ExpressAssignment expressAssignment);

    List<ExpressAssignmentActual> selectByDate(CommonQuery commonQuery);

    int insertBatch(List list);
    int updateBatch(List list);
}