package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.ExpressAssignment;
import com.sinoair.billing.domain.vo.query.CommonQuery;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("ExpressAssignmentMapper")
public interface ExpressAssignmentMapper {
    int deleteByPrimaryKey(Integer eaSyscode);

    int insert(ExpressAssignment record);

    int insertSelective(ExpressAssignment record);

    ExpressAssignment selectByPrimaryKey(Integer eaSyscode);

    int updateByPrimaryKeySelective(ExpressAssignment record);

    int updateByPrimaryKey(ExpressAssignment record);

    List<ExpressAssignment> selectByEaCode(ExpressAssignment expressAssignment);

    List<ExpressAssignment> selectByDate(CommonQuery commonQuery);
}