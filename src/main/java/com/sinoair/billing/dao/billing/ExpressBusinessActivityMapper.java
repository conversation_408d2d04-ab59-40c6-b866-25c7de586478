package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.ExpressBusinessActivity;
import com.sinoair.billing.domain.model.billing.ReceiptEawb;
import com.sinoair.billing.domain.vo.check.EawbCheckVO;
import com.sinoair.billing.domain.vo.price.GenerateFee;
import com.sinoair.billing.domain.vo.price.GenerateReceiptFee;
import com.sinoair.billing.domain.vo.query.ActivityQuery;
import com.sinoair.billing.domain.vo.query.CeosQuery;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

@Component("ExpressBusinessActivityMapper")
public interface ExpressBusinessActivityMapper {
    int deleteByPrimaryKey(Integer ebaSyscode);

    int insert(ExpressBusinessActivity record);

    int insertSelective(ExpressBusinessActivity record);

    ExpressBusinessActivity selectByPrimaryKey(Integer ebaSyscode);

    int updateByPrimaryKeySelective(ExpressBusinessActivity record);

    int updateByPrimaryKey(ExpressBusinessActivity record);

    /**
     * 批量录入
     * @param list
     */
    void insertBatch(List<ExpressBusinessActivity> list);

    /**
     * 获取序列
     * @return
     */
    int selectSequenceNextVal();

    int countByAllCode(ActivityQuery record);

    int countByAllCode_N(ActivityQuery record);

    void insertBatch_N(List<ExpressBusinessActivity> list);

    void deleteEbaDuplicate_N(CeosQuery param);

    List<ExpressBusinessActivity> selectTimeByAllCode(ActivityQuery record);

    Long selectMaxEbaCode();

    List<ReceiptEawb> selectActivityList(EawbCheckVO param);

    List<ExpressBusinessActivity> selectsysCodeList(@Param("beginNo") Long beginNo);

    List<GenerateFee> generateFeeList(@Param("ebaSyscode") String ebaSyscode,@Param("eawbPrintcode") String eawbPrintcode);

    List<GenerateFee> generateFeeByEba(@Param("eba") ExpressBusinessActivity businessActivity);

    BigDecimal selectExchangeRate(@Param("ctCode") String ctCode);

    BigDecimal selectMaxBillingSyscodeByBeginNo(@Param("beginNo") Long beginNo);

    List<GenerateReceiptFee> generateReceiptFeeList(@Param("ebaSyscode") String ebaSyscode,@Param("eawbPrintcode") String eawbPrintcode);

    List<GenerateReceiptFee> generateReceiptFeeByEba(@Param("eba") ExpressBusinessActivity businessActivity);

    List<ExpressBusinessActivity> selectFixEba(@Param("beginNo") Long beginNo,@Param("endNo") Long endNo);
}