package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.ExpressProperty;
import org.springframework.stereotype.Component;

import java.util.List;

@Component("ExpressPropertyMapper")
public interface ExpressPropertyMapper {
    int insert(ExpressProperty record);

    int insertSelective(ExpressProperty record);

    List<ExpressProperty> selectServiceTypeList();
    List<ExpressProperty> selectEpListFlatByGroup(String epGroup);

    List<String> selectMxxbKeyList();

}