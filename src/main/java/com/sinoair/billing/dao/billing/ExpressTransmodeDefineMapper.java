package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.ExpressTransmodeDefine;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component("ExpressTransmodeDefineMapper")
public interface ExpressTransmodeDefineMapper {
    int deleteByPrimaryKey(String transmodeid);

    int insert(ExpressTransmodeDefine record);

    int insertSelective(ExpressTransmodeDefine record);

    ExpressTransmodeDefine selectByPrimaryKey(String transmodeid);

    int updateByPrimaryKeySelective(ExpressTransmodeDefine record);

    int updateByPrimaryKey(ExpressTransmodeDefine record);

    List<ExpressTransmodeDefine> getTransmodeList();

    List<Map> selectListIdName();
}