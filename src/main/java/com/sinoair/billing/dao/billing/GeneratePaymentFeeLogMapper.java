package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.GeneratePaymentFeeLog;
import java.math.BigDecimal;
import java.util.List;

public interface GeneratePaymentFeeLogMapper {
    int deleteByPrimaryKey(BigDecimal id);

    int insert(GeneratePaymentFeeLog record);

    int insertSelective(GeneratePaymentFeeLog record);

    GeneratePaymentFeeLog selectByPrimaryKey(BigDecimal id);

    int updateByPrimaryKeySelective(GeneratePaymentFeeLog record);

    int updateByPrimaryKey(GeneratePaymentFeeLog record);

    List<GeneratePaymentFeeLog> selectDealStatusIsN();
}