package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.Home;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

@Component("HomeMapper")
public interface HomeMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(Home record);

    int insertSelective(Home record);

    Home selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(Home record);

    int updateByPrimaryKeyWithBLOBs(Home record);

    int updateByPrimaryKey(Home record);

    List<Home> selectTopHomes();

    List<Home> selectNoTopHomes();

    List<Home> selectHome(@Param(value = "search") String search);
}