package com.sinoair.billing.dao.billing;


import com.sinoair.billing.domain.model.billing.InqueryTemplate;
import com.sinoair.billing.domain.model.billing.InqueryTemplateWithBLOBs;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component("InqueryTemplateMapper")
public interface InqueryTemplateMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(InqueryTemplateWithBLOBs record);

    int insertSelective(InqueryTemplateWithBLOBs record);

    InqueryTemplateWithBLOBs selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(InqueryTemplateWithBLOBs record);

    int updateByPrimaryKeyWithBLOBs(InqueryTemplateWithBLOBs record);

    int updateByPrimaryKey(InqueryTemplate record);

     List<Map> executeSqlQuery(@Param("sql") String sql);
}