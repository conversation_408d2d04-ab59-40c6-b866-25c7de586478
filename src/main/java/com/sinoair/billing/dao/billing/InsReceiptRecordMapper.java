package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.ReceiptRecordTmp;
import com.sinoair.billing.domain.vo.query.InsRecordQuery;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: 大雄
 * @date: 2018/1/30
 * @time: 9:44
 * @description:
 * 不对应表结构，空工程时候构建
 * To change this template use File | Settings | File Templates.
 */
@Component
public interface InsReceiptRecordMapper {



    Double getReceiptPrice(@Param("eawbPrintcode") String eawbPrintcode, @Param("prId") Integer prId);

    Double getNonReceiptPrice(@Param("eawbPrintcode") String eawbPrintcode, @Param("prId") Integer prId);


}
