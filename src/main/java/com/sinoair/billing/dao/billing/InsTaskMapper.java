package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.InsTask;
import com.sinoair.billing.domain.model.billing.RecordTask;
import com.sinoair.billing.domain.vo.query.InsRecordQuery;

import java.math.BigDecimal;
import java.util.List;

public interface InsTaskMapper {
    int deleteByPrimaryKey(BigDecimal taskId);

    int insert(InsTask record);

    int insertSelective(InsTask record);

    InsTask selectByPrimaryKey(BigDecimal taskId);

    int updateByPrimaryKeySelective(InsTask record);

    int updateByPrimaryKey(InsTask record);

    List<InsTask> selectByCondition(InsRecordQuery param);

    InsTask selectByTaskType(String taskType);
}