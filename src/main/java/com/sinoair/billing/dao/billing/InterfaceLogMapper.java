package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.InterfaceLog;
import com.sinoair.billing.domain.model.billing.InterfaceLogWithBLOBs;
import com.sinoair.billing.domain.vo.FilterParam;

import java.math.BigDecimal;
import java.util.List;

public interface InterfaceLogMapper {
    int deleteByPrimaryKey(BigDecimal logId);

    int insert(InterfaceLogWithBLOBs record);

    int insertSelective(InterfaceLogWithBLOBs record);

    InterfaceLogWithBLOBs selectByPrimaryKey(BigDecimal logId);

    int updateByPrimaryKeySelective(InterfaceLogWithBLOBs record);

    int updateByPrimaryKeyWithBLOBs(InterfaceLogWithBLOBs record);

    int updateByPrimaryKey(InterfaceLog record);

    /**
     * 查询序列
     * @return
     */
    Integer selectSeq();

    List<InterfaceLog> selectRecord(FilterParam param);
}