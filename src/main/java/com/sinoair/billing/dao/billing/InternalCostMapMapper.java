package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.InternalCostMap;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

@Component("InternalCostMapMapper")
public interface InternalCostMapMapper {
    int deleteByPrimaryKey(Short id);

    int insert(InternalCostMap record);

    int insertSelective(InternalCostMap record);

    InternalCostMap selectByPrimaryKey(Short id);

    int updateByPrimaryKeySelective(InternalCostMap record);

    int updateByPrimaryKey(InternalCostMap record);

    /**
     * 查询内部费用执行记录
     * @param soCode
     * @param sacIdPay
     * @return
     */
    List<InternalCostMap> selectRecord(@Param("soCode") String soCode,
                                       @Param("sacIdPay") String sacIdPay);

    /**
     * 查询内部费用执行记录
     * @param soCode
     * @return
     */
    List<InternalCostMap> selectRecordBySoCode(@Param("soCode") String soCode);
 }