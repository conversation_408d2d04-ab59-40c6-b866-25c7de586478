package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.InternalReceiptMap;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

@Component("InternalReceiptMapMapper")
public interface InternalReceiptMapMapper {
    int deleteByPrimaryKey(Short irmId);

    int insert(InternalReceiptMap record);

    int insertSelective(InternalReceiptMap record);

    InternalReceiptMap selectByPrimaryKey(Short irmId);

    int updateByPrimaryKeySelective(InternalReceiptMap record);

    int updateByPrimaryKey(InternalReceiptMap record);

    List<InternalReceiptMap> selectIsExsite(@Param("spCode") String spCode, @Param("companyId") String companyId, @Param("cmType") String cmType);
}