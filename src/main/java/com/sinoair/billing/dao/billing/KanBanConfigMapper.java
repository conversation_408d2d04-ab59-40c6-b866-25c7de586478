package com.sinoair.billing.dao.billing;


import com.sinoair.billing.domain.model.billing.KanBanConfig;
import com.sinoair.billing.domain.model.billing.KanBanConfigWithBLOBs;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository("KanBanConfigMapper")
public interface KanBanConfigMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(KanBanConfigWithBLOBs record);

    int insertSelective(KanBanConfigWithBLOBs record);

    KanBanConfigWithBLOBs selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(KanBanConfigWithBLOBs record);

    int updateByPrimaryKeyWithBLOBs(KanBanConfigWithBLOBs record);

    int updateByPrimaryKey(KanBanConfig record);

    List<Map> executeSqlQuery(@Param("sql") String sql);
}