package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.MailLog;

import java.math.BigDecimal;
import java.util.List;

public interface MailLogMapper {
    int deleteByPrimaryKey(BigDecimal logId);

    int insert(MailLog record);

    int insertSelective(MailLog record);

    MailLog selectByPrimaryKey(BigDecimal logId);

    int updateByPrimaryKeySelective(MailLog record);

    int updateByPrimaryKey(MailLog record);

    List<MailLog> selectToSendList();
}