package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.DebitEawb;
import com.sinoair.billing.domain.model.billing.ManifestEawb;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository("ManifestEawbMapper")
public interface ManifestEawbMapper {


    List<ManifestEawb> selectManifestEawb();

    void deleteBatchByEawb(List<ManifestEawb> list);

    int insertByDmId(@Param(value = "dmId") Long dmId, @Param(value = "mId") Long mId, @Param(value = "statusTime") Date statusTime);

    int countManifestEawb();

    int insertByTemporaryDmId(@Param(value = "dmId") Long dmId, @Param(value = "mId") Long mId, @Param(value = "statusTime") Date statusTime);


}