package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.BmsManifest;
import com.sinoair.billing.domain.model.billing.ManifestList;
import com.sinoair.billing.domain.vo.FilterParam;
import com.sinoair.billing.domain.vo.manifest.ManifestListVO;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("ManifestListMapper")
public interface ManifestListMapper {
    int deleteByPrimaryKey(Long mId);

    int insert(ManifestList record);

    int insertSelective(ManifestList record);

    ManifestList selectByPrimaryKey(Long mId);

    int updateByPrimaryKeySelective(ManifestList record);

    int updateByPrimaryKey(ManifestList record);

    List<ManifestListVO> getManifestList(FilterParam filter);

    List<ManifestList> getDebitManifestSummary(Long mId);

    List<ManifestList> getCreditManifestSummary(Long mId);

    int countDebitManifestDraft(Long mId);

    int countCreditManifestDraft(Long mId);

    List<ManifestList> listManifest();

    List<BmsManifest> selectCollectBmsList(Long mId);

    List<BmsManifest> selectCreditCollectBmsList(Long mId);

    int deleteByInit();
}