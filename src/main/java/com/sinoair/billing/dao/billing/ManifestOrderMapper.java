package com.sinoair.billing.dao.billing;



import com.sinoair.billing.domain.model.billing.ManifestOrder;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("ManifestOrderMapper")
public interface ManifestOrderMapper {
    int deleteByPrimaryKey(Long mId);

    int insert(ManifestOrder record);

    int insertSelective(ManifestOrder record);

    ManifestOrder selectByPrimaryKey(Long dmId);

    int updateByPrimaryKeySelective(ManifestOrder record);

    int updateByPrimaryKey(ManifestOrder record);

    List<ManifestOrder> listManifestOrder(String sysStatus);

    int countManifestOrder(String sysStatus);
}