package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.Menu;
import com.sinoair.billing.domain.vo.FilterParam;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository("MenuMapper")
public interface MenuMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(Menu record);

    int insertSelective(Menu record);

    Menu selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(Menu record);

    int updateByPrimaryKey(Menu record);

    /**
     * 测试 bootstrap table
     * @param filterParam
     * @return
     */
    List<Map> selectTest(FilterParam filterParam);

    /**
     * 通过权限id 获得一级菜单列表
     * @param role_id
     * @return
     */
    List<Menu> getMenuListByRoleID(int role_id);

    /**
     * 通过一级菜单列表，获得二级菜单列表
     * @param menu_id
     * @return
     */
    List<Menu> getMenuListByMenuID(int menu_id);

    /**
     * 获取可用的菜单列表
     * @return
     */
    List<Menu> getMenuListY();

    /**
     * 获取可用的菜单列表
     * @return
     */
    List<Map> getMenuListMap(FilterParam filter);

    /**
     * 获取改类最大的排序数
     * @param parent_id
     * @return
     */
    Integer getMaxSortNumByParentId(int parent_id);

    /**
     * 根据子节点 获取父节点
     * @param menuIds
     * @return
     */
    List<Integer> selectParentId(List<Integer> menuIds);
}