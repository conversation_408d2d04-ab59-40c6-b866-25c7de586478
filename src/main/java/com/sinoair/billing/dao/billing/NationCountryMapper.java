package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.NationCountry;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("NationCountryMapper")
public interface NationCountryMapper {
    int deleteByPrimaryKey(Long ncId);

    int insert(NationCountry record);

    int insertSelective(NationCountry record);

    NationCountry selectByPrimaryKey(Long ncId);

    int updateByPrimaryKeySelective(NationCountry record);

    int updateByPrimaryKey(NationCountry record);

    List<NationCountry> listAll();
}