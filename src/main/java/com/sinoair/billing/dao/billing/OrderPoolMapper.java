package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.SinotransOrderPool;
import com.sinoair.billing.domain.vo.check.EawbCheckVO;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("OrderPoolMapper")
public interface OrderPoolMapper {


    Long selectMaxEawbSysCode();

    List<SinotransOrderPool> selectOrderAccept(EawbCheckVO param);

    int countOrderAccept(EawbCheckVO param);

    String selectSinotransIdSequence();

    int countEawbPrintcodeByDmId(Long dmId);

    List<String> selectEawbPrintcodeByDmId(Long dmId);

    List<String> selectDebitEawb();
}