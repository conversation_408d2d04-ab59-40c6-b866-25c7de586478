package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.PaymentBillDetail;
import com.sinoair.billing.domain.vo.FilterParam;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component("PaymentBillDetailMapper")
public interface PaymentBillDetailMapper {
    int deleteByPrimaryKey(Integer pbdSyscode);

    int insert(PaymentBillDetail record);

    int insertSelective(PaymentBillDetail record);

    PaymentBillDetail selectByPrimaryKey(Integer pbdSyscode);

    int updateByPrimaryKeySelective(PaymentBillDetail record);

    int updateByPrimaryKey(PaymentBillDetail record);

    void insertBatch(List<PaymentBillDetail> list);

    List<Map> selectDifferenceRecord(Integer cmId);

    void updateBatch(List<PaymentBillDetail> list);

    int deleteByCmId(Integer cmId);
    int deleteBCByCmId(Integer cmId);

    List<Map> selectPaymentRecordTool(FilterParam filterParam);
}