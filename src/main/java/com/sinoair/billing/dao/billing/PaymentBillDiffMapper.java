package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.PaymentBillDiff;
import org.springframework.stereotype.Component;

import java.util.List;

@Component("PaymentBillDiffMapper")
public interface PaymentBillDiffMapper {
    int insert(PaymentBillDiff record);

    int insertSelective(PaymentBillDiff record);

    void insertBatch(List<PaymentBillDiff> list);

    int deleteByCmId(Integer cmId);
}