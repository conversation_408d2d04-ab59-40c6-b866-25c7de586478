package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.CreditManifest;
import com.sinoair.billing.domain.model.billing.PaymentRecord;
import com.sinoair.billing.domain.model.billing.PriceGrade;
import com.sinoair.billing.domain.model.billing.Supplier;
import com.sinoair.billing.domain.vo.FilterParam;
import com.sinoair.billing.domain.vo.price.RecordTem;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Component("PaymentRecordMapper")
public interface PaymentRecordMapper {
    int deleteByPrimaryKey(Integer prId);

    int insert(PaymentRecord record);

    int insertSelective(PaymentRecord record);

    PaymentRecord selectByPrimaryKey(Long prId);

    int updateByPrimaryKeySelective(PaymentRecord record);

    int updateByPrimaryKey(PaymentRecord record);

    List<Map> getAwbPayMent(FilterParam filter);

    List<PaymentRecord> selectPaymentForTools(Map<String, Object> keyword);

    void updateToUnlockCredit(String invoiceId);

    Map<String, Object> countByCM(Integer cmId);

    List<PriceGrade> getGradePrice(@Param(value = "val") String val, @Param(value = "ppid") String ppid);

    List<PaymentRecord> selectPaymentByMawbCodeAndPrName(PaymentRecord paymentRecord);

    List<Map<String, Object>> selectPaymentForDownload(Map<String, Object> keyword);

    List<Map<String, Object>> selectPaymentForDownload2(Map<String, Object> keyword);

    List<Map<String, Object>> selectPaymentForToolsByPrintCode(Map<String, Object> keyword);

    List<Map<String, Object>> selectPaymentForToolsByMawbCode(Map<String, Object> keyword);

    List<Map<String, Object>> selectPaymentForToolsByRefernce1(Map<String, Object> keyword);

    List<Map<String, Object>> selectPaymentForToolsByRefrence2(Map<String, Object> keyword);

    List<Map<String, Object>> selectPaymentForToolsByTrackingNo(Map<String, Object> keyword);

    List<Map> selectInciPayList(FilterParam filterParam);

    void updateCmIdByDmId(@Param(value = "cmId") Integer cmId, @Param(value = "dmId") Integer dmId);

    void batchUpdateEawbTrackingNoByCode(@Param("list") List list);

    void updateEawbTrackingNoByMawbCode(@Param("eawbTrackingNo") String eawbTrackingNo,
                                        @Param("prHandletime") Date prHandletime,
                                        @Param("mawbCode") String mawbCode,
                                        @Param("serviceType") String serviceType);

    void updateBacth(List<PaymentRecord> list);

    Set<String> selectPaymentCodeSetByTime(Map<String, String> assets);

    PaymentRecord selectByPrimaryKey2(@Param("prId") Long prId);

    List<BigDecimal> selectUploadVerifyIdList(Map<String, String> param);

    void updatePRCMID(@Param("s") String s);

    PaymentRecord selectOneline(@Param("cmId") Integer cmId);

    PaymentRecord selectByReference1(@Param(value = "eawbReference1") String eawbReference1);

    PaymentRecord selectByReference2(@Param(value = "eawbReference1") String eawbReference1);
    PaymentRecord selectByTrackingNo(@Param(value = "eawbReference1") String eawbReference1);

    void insertBatch(List<PaymentRecord> list);

    int countPaymentRecord(PaymentRecord param);

    Double getPaymentPrice1(@Param("eawbPrintcode") String eawbPrintcode, @Param("ppId") Integer ppId);
    Double getPaymentPrice2(@Param("eawbPrintcode") String eawbPrintcode, @Param("ppId") Integer ppId);

    int updateCM(Map<String,Object> param);
    int getCMByOccurtime(Map<String,Object> param);

    List<Supplier> selectPpSupplierList(CreditManifest creditManifest);

    void updateBpdSyscode(Map map);

    List<RecordTem> selectRecordCountByServiceId(@Param("list") Set<Integer> list);

    int existPaymentRecordActualityList(@Param("eawbPrintcode") String eawbPrintcode,@Param("serviceId") Integer serviceId,@Param("pdSyscode") Integer pdSyscode,@Param("soCode") String soCode);


    int existPaymentRecordPlanList(@Param("eawbPrintcode") String eawbPrintcode,@Param("serviceId") Integer serviceId,@Param("pdSyscode") Integer pdSyscode);

    PaymentRecord selectRecordIdByDetailIdAndFeeId(@Param("eawbPrintCode") String eawbPrintCode,@Param("serviceDetailId") Integer detailId,@Param("pdSyscode") Integer pdSyscode);

    void updateTimeAndStatusById(PaymentRecord record);
}