package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.PriceDefine;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component("PriceDefineMapper")
public interface PriceDefineMapper {
    int deleteByPrimaryKey(Integer pdSyscode);

    int insert(PriceDefine record);

    int insertSelective(PriceDefine record);

    PriceDefine selectByPrimaryKey(Integer pdSyscode);

    int updateByPrimaryKeySelective(PriceDefine record);

    int updateByPrimaryKey(PriceDefine record);

    List<PriceDefine> selectPriceIncideList();

    List<PriceDefine> selectPrinceByName(String pdName);
    List<PriceDefine> selectPriceList();

    List<PriceDefine> getReceiptPdNameList();

    List<Map<String,String>> selectAllPriceForSelector();

    List<Map<String,String>> selectAllPriceForSelectorForPayInvoice();

    List<PriceDefine> selectPrinceByCondition(PriceDefine param);

    int countPrinceByName(@Param("pdName") String pdName);

    String getSeq();
}