package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.PriceGrade;
import com.sinoair.billing.domain.vo.price.PriceGradeVO;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository("PriceGradeMapper")
public interface PriceGradeMapper {
    int deleteByPrimaryKey(Integer pgId);

    int insert(PriceGrade record);

    int insertSelective(PriceGrade record);

    PriceGrade selectByPrimaryKey(Integer pgId);

    int updateByPrimaryKeySelective(PriceGrade record);

    int updateByPrimaryKey(PriceGrade record);

    List<Map<String,Object>> selectGradeByprid(PriceGrade grade);
    List<PriceGrade> selectGradeBypriceId(PriceGrade grade);

    int deleteGradeByPrId(Integer prId);

    List<Map<String,Object>> selectGradeZoneByprid(Integer prId);

    List<String> selectZoneNumBypriceId(PriceGrade record);

    List<PriceGrade> selectGradeBypriceIdAndZone(PriceGrade record);

    List<PriceGradeVO> selectPriceGradeByPrId(Integer prId);


}