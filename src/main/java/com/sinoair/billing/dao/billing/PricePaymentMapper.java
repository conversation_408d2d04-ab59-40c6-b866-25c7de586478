package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.PricePayment;
import com.sinoair.billing.domain.model.billing.Service;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository("PricePaymentMapper")
public interface PricePaymentMapper {
    int deleteByPrimaryKey(Integer ppId);

    int insert(PricePayment record);

    int insertSelective(PricePayment record);

    PricePayment selectByPrimaryKey(Integer ppId);

    int updateByPrimaryKeySelective(PricePayment record);

    int updateByPrimaryKey(PricePayment record);

    void updateOFFBySID(@Param("sId") int sId);

    List<Map<String,Object>> selectAllSupplier();

    List<Map<String,Object>> selectAllService();

    List<Map<String,Object>> selectPriceForSearch(Map<String, Object> keyword);

    Service selectServiceByPk(Map<String, Object> keyword);

    List<Map> supplierQuery(@Param("q") String q, @Param("companyId") String companyId);

    List<Map<String,Object>> selectServiceBySupplier(@Param("curSupplier") String curSupplier);

    List<PricePayment> selectPriceBySid(@Param("sId") String sId);

    List<PricePayment> selectPriceByExample(PricePayment pricePayment);

    List<Map<String,String>> selectAllPriceForSelector(@Param("companyId") String companyId);

    List<Map> routeQuery(@Param("q") String q);
}