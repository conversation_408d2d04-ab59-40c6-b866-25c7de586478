package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.PriceReceipt;
import com.sinoair.billing.domain.vo.price.PriceReceiptVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository("PriceReceiptMapper")
public interface PriceReceiptMapper {
    int deleteByPrimaryKey(Integer prId);

    int insert(PriceReceipt record);

    int insertSelective(PriceReceipt record);

    PriceReceipt selectByPrimaryKey(Integer prId);

    int updateByPrimaryKeySelective(PriceReceipt record);

    int updateByPrimaryKey(PriceReceipt record);

   List<PriceReceipt> getPriceReceipt();

    List<Map<String,Object>> selectAllSettlementObject();

    List<Map<String,Object>> selectAllProduct();

    List<Map<String,Object>> selectAllEAD();
    List<Map<String,Object>> selectAllEAST();

    List<Map<String,Object>> selectAllPrice();

    List<Map<String,Object>> selectPriceForSearch(Map<String, Object> keyword);

    List<Map> querySettlementObj(@Param("q") String q, @Param("companyId") String companyId, @Param("soCate") String soCate);

    List<Map> queryProduct(@Param("q") String q, @Param("companyId") String companyId);

    List<Map<String,Object>> selectProductBysettle(@Param("settlment") String settlment);

    List<Map> priceNameQuery(@Param("q") String q);

    List<Map> eadQuery(@Param("q") String q);

    List<Map> eastQuery(@Param("q") String q);

    List<PriceReceipt> selectPriceByExample(PriceReceipt priceReceipt);

    List<PriceReceiptVO> selectPriceReceipt();

    List<PriceReceiptVO> selectPriceReceiptCustomer();
}