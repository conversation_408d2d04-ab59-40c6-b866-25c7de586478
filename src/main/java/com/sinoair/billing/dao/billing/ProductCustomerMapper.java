package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.ProductCustomer;
import org.springframework.stereotype.Repository;

import java.util.Map;

@Repository("ProductCustomerMapper")
public interface ProductCustomerMapper {
    int deleteByPrimaryKey(Integer pcId);

    int insert(ProductCustomer record);

    int insertSelective(ProductCustomer record);

    ProductCustomer selectByPrimaryKey(Integer pcId);

    int updateByPrimaryKeySelective(ProductCustomer record);

    int updateByPrimaryKey(ProductCustomer record);

    ProductCustomer selectByExample(Map<String, Object> pcKeyword);
}