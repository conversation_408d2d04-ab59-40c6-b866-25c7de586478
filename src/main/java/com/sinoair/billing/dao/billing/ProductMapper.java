package com.sinoair.billing.dao.billing;

import com.alibaba.fastjson.JSONObject;
import com.sinoair.billing.domain.model.billing.Product;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository("ProductMapper")
public interface ProductMapper {
    int deleteByPrimaryKey(Integer pId);

    int insert(Product record);

    int insertSelective(Product record);

    Product selectByPrimaryKey(Integer pId);

    int updateByPrimaryKeySelective(Product record);

    int updateByPrimaryKey(Product record);

    //渠道
    //线路
    //目的国
    //进口出口
    //产品所属公司，操作公司
    List<JSONObject> selectPageInfo(@Param("name") String name, @Param("company_id") String company_id,
                                    @Param("occ_company_id") String occ_company_id, @Param("transmode_id") String transmode_id,
                                    @Param("service_tpye") String service_tpye, @Param("dest") String dest, @Param("business_type") String business_type);

    List<Product> selectListByCode(String code);

    List<Map> queryMainProduct(@Param("companyId") String companyId);

}