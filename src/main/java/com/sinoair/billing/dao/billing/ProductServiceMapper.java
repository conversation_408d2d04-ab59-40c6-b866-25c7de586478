package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.ProductService;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("ProductServiceMapper")
public interface ProductServiceMapper {
    int deleteByPrimaryKey(Integer psId);

    int insert(ProductService record);

    int insertSelective(ProductService record);

    ProductService selectByPrimaryKey(Integer psId);

    int updateByPrimaryKeySelective(ProductService record);

    int updateByPrimaryKey(ProductService record);

    void deleteByProductId(Integer pId);

    List<ProductService> selectByProductId(Integer pId);

    void deleteBySupplierCode(@Param("so_code") String so_code, @Param("p_id") Integer p_id);
}