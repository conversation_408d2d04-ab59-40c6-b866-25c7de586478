package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.ReceiptEawb;
import com.sinoair.billing.domain.model.billing.ReceiptRecord;
import com.sinoair.billing.domain.model.billing.ReceiptRecordTmp;
import com.sinoair.billing.domain.vo.FilterParam;
import com.sinoair.billing.domain.vo.check.EawbCheckVO;
import com.sinoair.billing.domain.vo.query.CeosQuery;
import com.sinoair.billing.domain.vo.query.CommonQuery;
import com.sinoair.billing.domain.vo.receipt.ReceiptExcelHongVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Component("ReceiptEawbMapper")
public interface ReceiptEawbMapper {


    int countReceiptEawb(String eawbPrintcode);

    void insertBatchRrEawb(List<ReceiptEawb> list);

    int countReceiptEawbN(String eawbPrintcode);

    void insertBatchRrEawbN(List<ReceiptEawb> list);

    void updateBatchRrEawbN(List<Long> list);

    Long selectMaxReId();
    Long selectMaxRrId();

    List<ReceiptEawb> listReceiptEawb(EawbCheckVO param);
    List<ReceiptEawb> listReceiptEawb_rr(EawbCheckVO param);

}