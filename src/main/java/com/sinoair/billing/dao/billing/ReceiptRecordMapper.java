package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.*;
import com.sinoair.billing.domain.vo.FilterParam;
import com.sinoair.billing.domain.vo.check.EawbCheckVO;
import com.sinoair.billing.domain.vo.manifest.RechargeDMListVO;
import com.sinoair.billing.domain.vo.query.CeosQuery;
import com.sinoair.billing.domain.vo.query.CommonQuery;
import com.sinoair.billing.domain.vo.receipt.ReceiptExcelHongVo;
import com.sinoair.billing.domain.vo.receipt.ReceiptVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Component("ReceiptRecordMapper")
public interface ReceiptRecordMapper {
    int deleteByPrimaryKey(Integer rrId);

    int insert(ReceiptRecord record);

    int insertSelective(ReceiptRecord record);

    int insertSelectiveTmp(ReceiptRecordTmp recordTmp);

    ReceiptRecord selectByPrimaryKey(Integer rrId);

    int updateByPrimaryKeySelective(ReceiptRecord record);

    int updateByPrimaryKey(ReceiptRecord record);

    int getReceiptRecorddByRrOccurtime(Map map);

    void updateDmIdByRrId(Map map);

    List<ReceiptRecord> getReceiptRecordByDmId(int dmId);

    void updateUploadReceiptRecordByRrId(ReceiptRecord record);

    List<ReceiptExcelHongVo> getExcelHongByEawbReference2(@Param(value = "eawbReference2s") String eawbReference2s[]);

    void updateDmIdByTRrId(@Param(value = "rrId") BigDecimal rrId);

    void updateBatchAppend(@Param(value = "list") List list);

    void updateBatchT(@Param(value = "list") List list);

    void updateBatchRrActualAmount(@Param(value = "list") List list);

    void updateBatchDmIdByRrId(@Param(value = "list") List list);

    List<Map> selectInciReceiptList(FilterParam filterParam);

    List<Map> selectAssReceiptMap(FilterParam filterParam);

    List<ReceiptRecord> selectReceiptByMawbCodeAndPdSyscode(ReceiptRecord receiptRecord);

    List<ReceiptRecord> selectReceiptByMawbCodeAndPdSyscodeAndSoCode(ReceiptRecord receiptRecord);
    /**
     * 统计该账单下所属公司的操作总重量,总票数
     * @param dmId
     * @param sacId
     * @return
     */
    BigDecimal selectTotalWeight(@Param("dmId") Integer dmId, @Param("sacId") String sacId);

    Integer  selectTotalPieces(@Param("dmId") Integer dmId, @Param("sacId") String sacId);

    Map selectTotalWeightPieces(@Param("dmId") Integer dmId, @Param("sacId") String sacId);

    int countReceiptRecord(ReceiptRecord receiptRecord);

    int countReceiptRecordTmp(ReceiptRecordTmp receiptRecord);

    int insertBatch(List<ReceiptRecordTmp> list);

    int insertOtherBatch(List<ReceiptRecordTmp> list);

    int countByPdSyscode(ReceiptRecordTmp receiptRecord);

    int countOtherByPdSyscode(ReceiptRecordTmp receiptRecord);

    int updateBatchMawb(@Param(value = "list") List list);

    int updateBatchMawbOther(@Param(value = "list") List list);

    List<ReceiptRecord> selectByEawbPrintcode(String eawbPrintcode);
    List<ReceiptRecord> selectOtherByEawbPrintcode(String eawbPrintcode);

    int countReceiptRecordHis(ReceiptRecord receiptRecord);

    Long selectMaxRrId();

    List<ReceiptRecord> selectListByRrId(CeosQuery ceosQuery);

    List<ReceiptRecord> selectListByDate(CommonQuery commonQuery);

    void updateBatchDmTempId(@Param(value = "list") List list);

    BigDecimal selectTotalWeightByDmId(@Param("dmId") Integer dmId);
    BigDecimal selectSumActualAmount(@Param("dmId") Integer dmId);

    DebitManifestTemporary selectCollect(@Param("dmId") Integer dmId);

    List<ReceiptEawb> selectBillListByRrId(EawbCheckVO param);

    List<ReceiptEawb> selectBillListInnerByRrId(EawbCheckVO param);

    List<ReceiptRecord> selectOuterRRByEawbPrintcode(String eawbPrintcode);

    List<ReceiptRecord> listPriceReceipt_nc(@Param("eawbPrintcode") String eawbPrintcode,@Param("rrName") String rrName);

    List<BmsRecDetailFba> getBmsManifestFba(CommonQuery param);

    void updateByPrintcode(ReceiptRecord receiptRecord);

    //获取月账单
    List<DebitManifest> getMonthlyDebitManifest();

    //获取周账单
    List<DebitManifest> getWeekDebitManifest();

    BigDecimal getMonthlyTotalWeight(Map map);
    BigDecimal getWeekTotalWeight(Map map);

    void updateDmIdMonthly(DebitManifest debitManifest);

    void updateDmIdWeek(DebitManifest debitManifest);

    void updateBmdSyscode(Map map);

    List<RechargeDMListVO> getReceiptRecordForDm();

    void timerInsertBatch(List<ReceiptRecord> list);

    void receiptRecordBatch(@Param("list") List<ReceiptRecord> list);

    List<ReceiptRecord> queryByCondition(@Param("eawbPrintcode") String eawbPrintcode,@Param("prId") Integer prId,@Param("pdSyscode") Integer pdSyscode);

    void updateTimeAndStatusByRrId(@Param("rrId") BigDecimal rrId,@Param("rrOccurtime") Date rrOccurtime);

    int existReceiptRecordList(@Param("eawbPrintcode") String eawbPrintcode,@Param("prId") Integer prId,@Param("pdSyscode") Integer pdSyscode,@Param("soCode") String soCode);

    BigDecimal selectSequence();
}
