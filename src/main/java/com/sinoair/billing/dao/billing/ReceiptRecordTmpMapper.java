package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.ReceiptRecordTmp;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

@Component("ReceiptRecordTmpMapper")
public interface ReceiptRecordTmpMapper {
    int insert(ReceiptRecordTmp record);

    int insertSelective(ReceiptRecordTmp record);

    void insertBatch(List<ReceiptRecordTmp> list);

    int insertTemp(String param);

    int countReceiptRecord(ReceiptRecordTmp record);

    List<ReceiptRecordTmp> list();

    List<ReceiptRecordTmp> listByRRName(String rrName);

    void deleteBatch(List<ReceiptRecordTmp> list);

    List<ReceiptRecordTmp> listNonPriceReceipt_other(@Param("eawbPrintcode") String eawbPrintcode,@Param("prName") String prName);
    List<ReceiptRecordTmp> listNonPriceReceipt_other_nc(@Param("eawbPrintcode") String eawbPrintcode,@Param("prName") String prName);
    //内部公司计费
    List<ReceiptRecordTmp> listInnerPriceReceipt(@Param("eawbPrintcode") String eawbPrintcode,@Param("soCode") String soCode
            ,@Param("pCode") String pCode);

    //内部公司交易费
    List<ReceiptRecordTmp> listInnerPriceReceiptNew(@Param("eawbPrintcode") String eawbPrintcode,@Param("soCode") String soCode
            ,@Param("pCode") String pCode);
}
