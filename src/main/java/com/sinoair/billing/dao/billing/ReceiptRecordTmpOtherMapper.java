package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.ReceiptRecord;
import com.sinoair.billing.domain.model.billing.ReceiptRecordTmp;
import org.springframework.stereotype.Component;

import java.util.List;

@Component("ReceiptRecordTmpOtherMapper")
public interface ReceiptRecordTmpOtherMapper {
    int insert(ReceiptRecordTmp record);

    int insertSelective(ReceiptRecordTmp record);

    void insertBatch(List<ReceiptRecordTmp> list);

    int countReceiptRecordOther(ReceiptRecordTmp record);

    List<ReceiptRecordTmp> list();

    void deleteBatch(List<ReceiptRecordTmp> list);
}