package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.RecordLog;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

@Component("RecordLogMapper")
public interface RecordLogMapper {
    int deleteByPrimaryKey(BigDecimal logId);

    int insert(RecordLog record);

    int insertSelective(RecordLog record);

    RecordLog selectByPrimaryKey(BigDecimal logId);

    int updateByPrimaryKeySelective(RecordLog record);

    int updateByPrimaryKey(RecordLog record);

    /**
     * 查询序列
     * @return
     */
    Integer selectSeq();
}