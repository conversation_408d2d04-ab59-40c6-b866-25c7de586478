package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.RecordTask;
import com.sinoair.billing.domain.vo.query.InsRecordQuery;

import java.math.BigDecimal;
import java.util.List;

public interface RecordTaskMapper {
    int deleteByPrimaryKey(BigDecimal taskId);

    int insert(RecordTask record);

    int insertSelective(RecordTask record);

    RecordTask selectByPrimaryKey(BigDecimal taskId);

    int updateByPrimaryKeySelective(RecordTask record);

    int updateByPrimaryKey(RecordTask record);

    List<RecordTask> selectByCondition(InsRecordQuery param);
}