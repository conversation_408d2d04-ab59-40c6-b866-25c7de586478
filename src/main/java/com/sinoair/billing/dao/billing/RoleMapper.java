package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.Role;
import com.sinoair.billing.domain.vo.FilterParam;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository("RoleMapper")
public interface RoleMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(Role record);

    int insertSelective(Role record);

    Role selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(Role record);

    int updateByPrimaryKey(Role record);

    List<Role> getRoleListByUserID(int user_id);
    /**
     * 查看所有权限 搜索
     * @param filter
     * @return
     */
    List<Map> selectRoleList(FilterParam filter);

    /**
     * 获取所有Role
     * @return
     */
    List<Role> selectListAll();
    List<Role> selectRoleListByCompanyID(FilterParam filterParam);
    List<Role> selectRolesByCompanyId(List<Integer> roleCompanyId);
}