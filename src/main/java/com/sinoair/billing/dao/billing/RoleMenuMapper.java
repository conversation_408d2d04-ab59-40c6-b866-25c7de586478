package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.RoleMenuKey;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("RoleMenuMapper")
public interface RoleMenuMapper {
    int deleteByPrimaryKey(RoleMenuKey key);

    int insert(RoleMenuKey record);

    int insertSelective(RoleMenuKey record);

    List<Integer> selectRoleMenuIdSet(int roltId);

    void deleteByRoleID(int roleId);

    void insertByList(List list);
}