package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.SerialNO;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("SerialNOMapper")
public interface SerialNOMapper {
    int deleteByPrimaryKey(Long snId);

    int insert(SerialNO record);

    int insertSelective(SerialNO record);

    SerialNO selectByPrimaryKey(Long snId);

    int updateByPrimaryKeySelective(SerialNO record);

    int updateByPrimaryKey(SerialNO record);

    List<SerialNO> select(SerialNO serialNO);
}