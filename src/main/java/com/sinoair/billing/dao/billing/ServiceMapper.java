package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.Service;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component("ServiceMapper")
public interface ServiceMapper {
    int deleteByPrimaryKey(Integer sId);

    int insert(Service record);

    int insertSelective(Service record);

    Service selectByPrimaryKey(Integer sId);

    int updateByPrimaryKeySelective(Service record);

    int updateByPrimaryKey(Service record);

    List<Service> selectServiceList(String soCode);

    List<Map> selectService(@Param("s_name") String s_name, @Param("so_code") String so_code, @Param("company_id") String company_id);

    int selectServiceCount(@Param("so_code") String so_code, @Param("s_name") String s_name,
                           @Param("s_id") Integer s_id);

    List<Service> selectAllON();

    List<Service> selectBySoCode(@Param("so_code") String so_code);

    List<Map> selectServiceBySelected(@Param("p_id") int p_id);
}