package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.SettlementObjectBalance;


public interface SettlementObjectBalanceMapper {

    int deleteByPrimaryKey(Integer sbId);

    int insert(SettlementObjectBalance record);

    int insertSelective(SettlementObjectBalance record);

    SettlementObjectBalance selectByPrimaryKey(Integer sbId);

    int updateByPrimaryKeySelective(SettlementObjectBalance record);

    int updateByPrimaryKey(SettlementObjectBalance record);
}