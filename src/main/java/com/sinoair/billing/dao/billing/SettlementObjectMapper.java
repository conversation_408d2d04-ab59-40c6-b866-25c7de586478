package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.SettlementObject;
import com.sinoair.billing.domain.vo.query.CustomerQuery;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Component("SettlementObjectMapper")
public interface SettlementObjectMapper {
    int deleteByPrimaryKey(SettlementObject key);

    int insert(SettlementObject record);

    int insertSelective(SettlementObject record);

    SettlementObject selectByPrimaryKey(SettlementObject key);

    int updateByPrimaryKeySelective(SettlementObject record);

    int updateByPrimaryKey(SettlementObject record);

    List<Map> selectList();

   String getsoNameBysoCode(@Param(value = "soCode") String soCode);

    List<Map> selectListByCompanyId(@Param(value = "companyId") String companyId, @Param(value = "soCate") String soCate);

    List<Map> selectInsideByCompanyId(String companyId);

    List<Map> selectCompanyIdBySoCode(String soCode);

    List<Map<String,String>> selectListMapByCompanyId(String companyId);

    Map<String,String> selectSoTypeBySoCode(@Param(value = "soCode") String so_code);

    List<String> selectSacIdBySoCode(@Param("soCode") String soCode);

    List<String> selectCCodeBySoCode(@Param("soCode") String soCode);

    List<Map> getInfoBySoCode(@Param(value = "soCode") String soCode);

    void updateCustCodeBySoCode(@Param(value = "custCode") String custCode, @Param(value = "soCode") String soCode);

    List<Map> selectQuery(@Param("query") CustomerQuery query);

    /**
     * 根据socode查询客户
     * @param soCode
     * @return
     */
    SettlementObject selectBySoCode(@Param("soCode") String soCode);

    /**
     * 查询序列
     * @return
     */
    Integer selectSeq();

    /**
     * 根据ccode查询客户
     * @param cCode
     * @return
     */
    int selectByCCode(@Param("cCode") String cCode);

    /**
     * 根据soKey查询客户
     * @param soKey
     * @return
     */
    int selectBySoKey(@Param("soKey") String soKey);

    int selectBySoName(@Param("soCode") String soCode, @Param("soName") String soName);

    Map<String,Object> getSoProByPrId(@Param("prId") String prId);

    List<Map<String,Object>> getSoProListByPrId(@Param("prId") String prId);

    List<Map<String,Object>> getSoBalanceList(@Param("toSys") String toSys);

    List<SettlementObject> selectSettlementList();

    List<SettlementObject> selectAutoSoList();

    List<SettlementObject> selectOnlineSoList();

    List<String> selectFlatSoList();

    int addSoBanlance(@Param("soCode") String soCode,@Param("balance") BigDecimal balance);

    int cutSoBanlance(@Param("soCode") String soCode, @Param("balance") double balance);
}