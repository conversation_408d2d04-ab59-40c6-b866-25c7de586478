package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.SettlementObjectPrice;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component("SettlementObjectPriceMapper")
public interface SettlementObjectPriceMapper {
    int deleteByPrimaryKey(Long sopId);

    int insert(SettlementObjectPrice record);

    int insertSelective(SettlementObjectPrice record);

    SettlementObjectPrice selectByPrimaryKey(Long sopId);

    int updateByPrimaryKeySelective(SettlementObjectPrice record);

    int updateByPrimaryKey(SettlementObjectPrice record);

    List<Map<String,Object>> selectPriceBySoCode(@Param("soCode") String soCode);
}