package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.SettlementRecord;
import com.sinoair.billing.domain.vo.FilterParam;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component("SettlementRecordMapper")
public interface SettlementRecordMapper {
    int deleteByPrimaryKey(String reference1);

    int insert(SettlementRecord record);

    int insertSelective(SettlementRecord record);

    SettlementRecord selectByPrimaryKey(String reference1);

    int updateByPrimaryKeySelective(SettlementRecord record);

    int updateByPrimaryKey(SettlementRecord record);

    int countByPrimaryKey(String reference1);

    int insertBatch(List<SettlementRecord> list);

    int updateBatch(List<SettlementRecord> list);

    int updateBatchByRef2(List<SettlementRecord> list);

    List<SettlementRecord> selectRecordList(FilterParam record);

    int batchUpdateByRef2(Map<String, Object> map);
}