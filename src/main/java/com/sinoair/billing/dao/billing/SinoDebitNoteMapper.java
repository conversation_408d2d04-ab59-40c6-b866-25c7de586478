package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.SinoDebitNote;
import com.sinoair.billing.domain.vo.FilterParam;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component("SinoDebitNoteMapper")
public interface SinoDebitNoteMapper {
    int deleteByPrimaryKey(Integer sdId);

    int insert(SinoDebitNote record);

    int insertSelective(SinoDebitNote record);

    SinoDebitNote selectByPrimaryKey(Integer sdId);

    int updateByPrimaryKeySelective(SinoDebitNote record);

    int updateByPrimaryKey(SinoDebitNote record);

    List<Map> selectSoByPercent();

    List<Map> getDebitNoteListMap(FilterParam param);
}