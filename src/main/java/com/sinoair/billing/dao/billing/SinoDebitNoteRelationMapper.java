package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.SinoDebitNoteRelation;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component("SinoDebitNoteRelationMapper")
public interface SinoDebitNoteRelationMapper {
    int deleteByPrimaryKey(Short sdrId);

    int insert(SinoDebitNoteRelation record);

    int insertSelective(SinoDebitNoteRelation record);

    SinoDebitNoteRelation selectByPrimaryKey(Short sdrId);

    int updateByPrimaryKeySelective(SinoDebitNoteRelation record);

    int updateByPrimaryKey(SinoDebitNoteRelation record);

    void insertBatchByDmCode(Map<String, Object> map);
}