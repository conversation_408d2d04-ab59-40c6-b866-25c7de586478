package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.SinotransOrderPool;
import com.sinoair.billing.domain.model.billing.SinotransOrderWeight;
import com.sinoair.billing.domain.vo.check.EawbCheckVO;
import com.sinoair.billing.domain.vo.query.CommonQuery;
import org.springframework.stereotype.Repository;

import java.util.List;
@Repository("SinotransOrderPoolMapper")
public interface SinotransOrderPoolMapper {
    int deleteByPrimaryKey(Long sopSyscode);

    int insert(SinotransOrderPool record);

    int insertSelective(SinotransOrderPool record);

    SinotransOrderPool selectByPrimaryKey(Long sopSyscode);

    int updateByPrimaryKeySelective(SinotransOrderPool record);

    int updateByPrimaryKey(SinotransOrderPool record);

    int insertBatch(List<SinotransOrderPool> list);

    int countOrderPool(String eawbPrintcode);

    List<SinotransOrderWeight> selectOrderWeightCollect(CommonQuery param);
}