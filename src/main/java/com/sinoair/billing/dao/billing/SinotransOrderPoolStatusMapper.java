package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.SinotransOrderPoolStatus;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("SinotransOrderPoolStatusMapper")
public interface SinotransOrderPoolStatusMapper {
    int deleteByPrimaryKey(Long sopsSyscode);

    int insert(SinotransOrderPoolStatus record);

    int insertSelective(SinotransOrderPoolStatus record);

    SinotransOrderPoolStatus selectByPrimaryKey(Long sopsSyscode);

    int updateByPrimaryKeySelective(SinotransOrderPoolStatus record);

    int updateByPrimaryKey(SinotransOrderPoolStatus record);

    int insertBatch(List<SinotransOrderPoolStatus> list);

    int countOrderPoolStatus(@Param(value = "eawbPrintcode") String eawbPrintcode,
                             @Param(value = "statusCode") String statusCode);
}