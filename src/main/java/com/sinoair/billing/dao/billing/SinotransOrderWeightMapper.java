package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.SinotransOrderWeight;
import org.springframework.stereotype.Repository;

@Repository("SinotransOrderWeightMapper")
public interface SinotransOrderWeightMapper {
//    int insert(SinotransOrderWeight record);
//
//    int insertSelective(SinotransOrderWeight record);
//
//    int deleteByMonth(Integer collectMonth);
//
//    int updateByPrimaryKeySelective(SinotransOrderWeight record);
}