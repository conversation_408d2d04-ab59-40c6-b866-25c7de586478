package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.SinotransOriginalOrg;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("SinotransOriginalOrgMapper")
public interface SinotransOriginalOrgMapper {
    int deleteByPrimaryKey(String sacId);

    int insert(SinotransOriginalOrg record);

    int insertSelective(SinotransOriginalOrg record);

    SinotransOriginalOrg selectByPrimaryKey(String sacId);

    int updateByPrimaryKeySelective(SinotransOriginalOrg record);

    int updateByPrimaryKey(SinotransOriginalOrg record);

    List<SinotransOriginalOrg> listSinotransOrg();
}