package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.Supplier;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component("SupplierMapper")
public interface SupplierMapper {
    int deleteByPrimaryKey(String spSyscode);

    int insert(Supplier record);

    int insertSelective(Supplier record);

    Supplier selectByPrimaryKey(String spSyscode);

    int updateByPrimaryKeySelective(Supplier record);

    int updateByPrimaryKey(Supplier record);

    List<Supplier> selectSupplierList(@Param(value = "companyId") String companyId);

    List<Map> selectListByCompanyId(String companyId);

    List<Map> querysupplier(@Param("companyId") String companyId,
                            @Param("spName") String spName,
                            @Param("spCode") String spCode,
                            @Param("spContractinfo") String spContractinfo);

    String getSeq();

    int countSupplierBySpName(@Param("spName") String spName);

    List<Map> querySupplierListSelect2(@Param("q") String q);

    List<Map> selectListOnByCompanyIdandSNR(@Param("companyId") String companyId);

    List<Map<String,Object>> selectByspCode(@Param("soCode") String soCode);

    Map<String,String> selectSpTypeBySoCode(@Param("soCode") String so_code);

    List<Map<String,String>> selectAllCodeNameForSelector(@Param(value = "companyId") String companyId);

    List<Map<String, String>> selectSNRCodeNameForSelector(@Param(value = "companyId") String companyId);

    String getSpNameBySpCode(String spCode);

    List<Supplier> selectAutoSpList();
}