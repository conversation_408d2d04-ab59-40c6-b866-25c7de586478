package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.TaxRate;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository("TaxRateMapper")
public interface TaxRateMapper {
    int deleteByPrimaryKey(Integer trId);

    int insert(TaxRate record);

    int insertSelective(TaxRate record);

    TaxRate selectByPrimaryKey(Integer trId);

    int updateByPrimaryKeySelective(TaxRate record);

    int updateByPrimaryKey(TaxRate record);

    List<Map<String,Object>> selectCreditTax();

    List<TaxRate> getCreditTaxByTrName(@Param(value = "trName") String trName);
}