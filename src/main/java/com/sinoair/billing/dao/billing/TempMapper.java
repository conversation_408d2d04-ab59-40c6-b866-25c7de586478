package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.ExpressAirWayBill;
import com.sinoair.billing.domain.model.billing.PaymentRecord;
import com.sinoair.billing.domain.model.billing.ReceiptRecord;
import com.sinoair.billing.domain.vo.temp.AirVO;
import com.sinoair.billing.domain.vo.temp.LpInfoWeightVO;

import java.util.List;

public interface TempMapper {


    int insertBatchTemp(List<String> list);

    int insertBatchAsendia(List<String> list);

    int insertBatchAsendiaResult(List<PaymentRecord> list);

    int deleteBatchTemp(List<String> list);

    int deleteTempRr(String eawbPrintcode);

    int deleteBatchTempLocal(List<String> list);

    int deleteBatchTempAsendia(List<String> list);

    List<String> selectTemp();

    List<String> selectTempLocal();

    List<String> selectTempAsendia();

    int countTempRecord(ReceiptRecord param);

    List<ExpressAirWayBill> selectTempEawb();

    int selectCountPaymentByCode(String eawbPrintcode);

    int batchUpdateEawbTrackingNo(List<PaymentRecord> list);
    //临时处理，删除重复数据
    int deleteBatchTempN(List<Long> list);

    int deleteBatchRRN(List<Long> list);

    List<Long> selectTempRRN();

    List<PaymentRecord> selectTempAsendiaResult();

    List<String> selectTempMawbEawb();

    int deleteBatchTempMawbEawb(List<String> list);

    int insertTempErr(String eawbPrintcode);

    List<String> selectTempEa();

    int deleteBatchTempEa(List<String> list);

    int insertTempEa(String eaCode);

    int insertBatchTempEa(List<String> list);

    int countTemp();

    int batchUpdateEawbSacId(List<ExpressAirWayBill> list);
    int batchUpdateRRSacId(List<ExpressAirWayBill> list);
    int batchUpdateRROtherSacId(List<ExpressAirWayBill> list);
    int batchUpdatePrSacId(List<ExpressAirWayBill> list);

    List<ExpressAirWayBill> listEawbSacIdEmpty();

    int insertBatchEbaIn(List<String> list);
    int insertBatchEbaAdc(List<String> list);

    int insertBatchTempLpInfoWeight(List<LpInfoWeightVO> list);

    int insertBatchTempAir(List<AirVO> list);
    int insertBatchTempTrain(List<AirVO> list);

}