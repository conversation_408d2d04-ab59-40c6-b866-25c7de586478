package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.TrapsRRConfig;
import org.springframework.stereotype.Component;

@Component("TrapsRRConfigMapper")
public interface TrapsRRConfigMapper {
    int insert(TrapsRRConfig record);

    int insertSelective(TrapsRRConfig record);

    TrapsRRConfig selectById(Integer trapsId);

    void updateTrapsRRConfig(TrapsRRConfig record);
}