package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.User;
import com.sinoair.billing.domain.vo.FilterParam;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository("UserMapper")
public interface UserMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(User record);

    int insertSelective(User record);

    User selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(User record);

    int updateByPrimaryKey(User record);

    /**
     * 根据用户名，获取用户信息
     *
     * @param username
     * @return
     */
    List<User> getUserListByUserName(String username);

    List<User> selectUserList(FilterParam filter);

    List<Map> selectUserListMap(FilterParam filter);

    List<User> selectUserByAwbCode(String awbCode);
}