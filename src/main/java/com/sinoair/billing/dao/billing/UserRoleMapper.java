package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.UserRoleKey;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("UserRoleMapper")
public interface UserRoleMapper {
    int deleteByPrimaryKey(UserRole<PERSON>ey key);

    int insert(UserRoleKey record);

    int insertSelective(UserRole<PERSON>ey record);

    List<UserRoleKey> selectUserRoleKey(int userId);

    void updateUserRoleKey(UserRoleKey key);
}