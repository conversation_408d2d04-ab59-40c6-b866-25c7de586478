package com.sinoair.billing.dao.ceos;

import com.sinoair.billing.domain.model.billing.ExpressBusinessActivity;
import com.sinoair.billing.domain.vo.query.ActivityQuery;
import com.sinoair.billing.domain.vo.query.CeosQuery;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: 大雄
 * @date: 2019-07-04
 * @time: 11:26
 * @description: To change this template use File | Settings | File Templates.
 */
@Component
public interface CeosActivityMapper {

    /**
     * 查询ceos只读库的用户信息
     * @return
     */
    List<ExpressBusinessActivity> selectCeosActivityPush(CeosQuery param);

    int countCeosActivityPush(CeosQuery param);

    List<ExpressBusinessActivity> selectCeosActivity(CeosQuery param);
    int selectCeosActivityCount(CeosQuery param);

    List<ExpressBusinessActivity> selectCeosActivitySoCode(CeosQuery param);
    int countCeosActivitySoCode(CeosQuery param);

    int countCeosActivity (ActivityQuery record);

    List<ExpressBusinessActivity> selectCheckCeosActivity(CeosQuery param);

    int countCheckCeosActivity (CeosQuery param);

    List<ExpressBusinessActivity> selectCeosActivityTemp(ActivityQuery param);

    int countCeosActivityTemp (ActivityQuery param);

    List<ExpressBusinessActivity> selectCeosActivityPushBySysCode(CeosQuery param);

    int countCeosActivityPushBySysCode(CeosQuery param);

    Long selectMaxEbapSysCode();

    Long selectMaxEbaCode();

    BigDecimal selectCeosActivityMax(CeosQuery query);
}
