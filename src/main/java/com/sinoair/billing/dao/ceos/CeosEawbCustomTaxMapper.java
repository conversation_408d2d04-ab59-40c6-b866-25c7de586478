package com.sinoair.billing.dao.ceos;

import com.sinoair.billing.domain.model.billing.EawbCustomTax;
import com.sinoair.billing.domain.model.billing.ExpressAirWayBillCeos;
import com.sinoair.billing.domain.vo.ceos.CeosEawb;
import com.sinoair.billing.domain.vo.query.CeosQuery;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: nhb
 * @date: 2020-04-22
 * @time: 11:26
 * @description: To change this template use File | Settings | File Templates.
 */
@Repository("CeosEawbCustomTaxMapper")
public interface CeosEawbCustomTaxMapper {

    /**
     * 查询ceos只读库的eawb信息
     * @return
     */


    Long selectMaxEawbTaxSysCode();

    int countCeosEawbTaxBySysCode(CeosQuery ceosQuery);

    List<EawbCustomTax> selectCeosEawbTaxBySysCode(CeosQuery ceosQuery);

}
