package com.sinoair.billing.dao.ceos;

import com.sinoair.billing.domain.model.billing.ExpressAssignmentActual;
import com.sinoair.billing.domain.vo.ceos.CeosEawbEaVO;
import com.sinoair.billing.domain.vo.query.CeosQuery;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: nhb
 * @date: 2020-04-22
 * @time: 11:26
 * @description: To change this template use File | Settings | File Templates.
 */
@Repository("CeosEawbEaMapper")
public interface CeosEawbEaMapper {

    /**
     * 查询ceos只读库的主单信息
     * @return
     */

    int countCeosMawbEawb(CeosQuery ceosQuery);

    List<CeosEawbEaVO> selectCeosMawbEawb(CeosQuery ceosQuery);

    int countCeosMawbEawbByEaCode(String eaCode);

    List<CeosEawbEaVO> selectCeosMawbEawbByEaCode(String eaCode);

    CeosEawbEaVO selectCeosMawbEawbByEawbPrintcode(String eawbPrintcode);

    CeosEawbEaVO selectCeosMawbActualByPrintcode(String eawbPrintcode);

    List<ExpressAssignmentActual> selectCeosEaaList(CeosQuery ceosQuery);

    int countCeosEaaCn(String eaCode);



}
