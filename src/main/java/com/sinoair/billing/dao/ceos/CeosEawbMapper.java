package com.sinoair.billing.dao.ceos;

import com.sinoair.billing.domain.model.billing.EawbPre;
import com.sinoair.billing.domain.model.billing.ExpressAirWayBill;
import com.sinoair.billing.domain.model.billing.ExpressAirWayBillCeos;
import com.sinoair.billing.domain.vo.ceos.CeosEawb;
import com.sinoair.billing.domain.vo.query.CeosQuery;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: 大雄
 * @date: 2019-07-04
 * @time: 11:26
 * @description: To change this template use File | Settings | File Templates.
 */
@Repository("CeosEawbMapper")
public interface CeosEawbMapper {

    /**
     * 查询ceos只读库的eawb信息
     * @return
     */
    List<CeosEawb> selectEawbBySysCode(CeosQuery ceosQuery);

    Long selectMaxEawbSysCode(@Param("soCode") String soCode);

    List<CeosEawb> selectEawbByHandleTime(CeosQuery ceosQuery);

    int countEawbByHandleTime(CeosQuery ceosQuery);

    int countCeosEawbBySysCode(CeosQuery ceosQuery);

    List<ExpressAirWayBill> selectCeosEawbBySysCode(CeosQuery ceosQuery);

    List<CeosEawb> selectCeosEawbByTime(CeosQuery ceosQuery);

    ExpressAirWayBillCeos selectCeosEawbByEawbPrintcode(@Param("eawbPrintcode") String eawbPrintcode);

    int countCeosEawbByEawbPrintcode(@Param("eawbPrintcode") String eawbPrintcode);

    ExpressAirWayBill selectCeosEawbCNBycode(@Param("eawbPrintcode") String eawbPrintcode);

    ExpressAirWayBill selectCeosEawbOtherByCode(@Param("eawbPrintcode") String eawbPrintcode);

    ExpressAirWayBill selectCeosFlatPre(@Param("eawbPrintcode") String eawbPrintcode);

    EawbPre selectCeosPre(@Param("eawbPrintcode") String eawbPrintcode);

    int countPreByHandleTime(CeosQuery ceosQuery);

    List<EawbPre> selectPreByHandleTime(CeosQuery ceosQuery);


}
