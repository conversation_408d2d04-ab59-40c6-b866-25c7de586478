package com.sinoair.billing.domain.constant;

import com.sinoair.billing.core.util.AppConfig;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: 大雄
 * @date: 2018/1/22
 * @time: 13:27
 * @description: To change this template use File | Settings | File Templates.
 */
public class BillingConstant {

    /**
     * 系统版本号
     */
    public static final String APP_VERSION = AppConfig.getStringByProp("props/config","webapp_version");

    /**
     * 系统环境
     */
    public static final String ENVIRONMENT_NAME = AppConfig.getStringByProp("props/config","environment.name");


    /**
     * 系统环境
     */
    public static final String SYSTEM = "SYSTEM";

    /**
     * 应用名称
     */
    public static final String APP_NAME = AppConfig.getStringByProp("props/config","app.name");

    public static String PUSH_CEOS_RECORD_URL = AppConfig.getStringByProp("props/config","ceos_push_record_url");

}
