package com.sinoair.billing.domain.constant;

import java.math.BigDecimal;

/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2018/1/22
 * @time: 13:27
 * @description: To change this template use File | Settings | File Templates.
 */
public class CheckConstant {


    public final static String STATUS_Y = "Y";

    public final static String STATUS_N = "N";

    public final static String STATUS_I = "I";

    public final static String STATUS_O = "O";

    public final static String CHECK_TYPE = "EAWB";

    public final static String CHECK_TYPE_CEOS = "EAWB_CEOS_RESULT";

    public final static String CHECK_TYPE_IS_DATE = "CHECK_TYPE_IS_DATE";

    public final static String ORDER_POOL = "ORDER_POOL";

    public final static String MONITOR_ACTIVITY_CEOS = "MONITOR_ACTIVITY_CEOS";

    public final static String CEOS_EAWB = "CEOS_EAWB";

    public final static String CEOS_EAWB_CUSTOM = "CEOS_EAWB_CUSTOM";

    public final static String CN_RR_DM_LS = "CN_RR_DM_LS"; //菜鸟临时账单与计费关联

    public final static BigDecimal totalizer = new BigDecimal(1000);

    public final static String CN_SO_CODE = "00060491";

    public final static String XJ_SO_CODE = "Z9991610"; //现结客户和预充值无财务编码统一用该账户

    public final static BigDecimal SELECT_COUNT = new BigDecimal(20000);

    public final static int PAGE_SIZE = 100000;

    public final static int PAGE_SIZE_5W = 50000;
    public final static int PAGE_SIZE_1W = 10000;

    public final static int PAGE_SIZE_20W = 200000;
    public final static int PAGE_SIZE_50W = 500000;
    public final static int PAGE_SIZE_100W = 1000000;

    public final static int so_cainiao = 1;
    public final static int so_other = 99;

    public final static String CHECK_INS_EBA = "CHECK_INS_EBA";

    public final static String CHECK_INS_EAWB = "CHECK_INS_EAWB";

    public final static String SYN_CEOS_EAWB= "SYN_CEOS_EAWB";
    public final static String SYN_CEOS_EAWB_PRE= "SYN_CEOS_EAWB_PRE";
    public final static String MONITOR_SYN_CEOS_EAWB= "MONITOR_SYN_CEOS_EAWB";
    public final static String SYN_CEOS_EAWB_MAWB= "SYN_CEOS_EAWB_MAWB";
    public final static String SYN_RR_CN= "SYN_RR_CN";
    public final static String SYN_CEOS_EBA_FLAT= "SYN_CEOS_EBA_FLAT";
    public final static String SYN_CEOS_EBA_FLAT_V2= "SYN_CEOS_EBA_FLAT_V2";

    public final static String SYN_ORDER_WEIGHT= "SYN_ORDER_WEIGHT";
    public final static String SYN_DEBIT_MANIFEST= "SYN_DEBIT_MANIFEST";
    public final static String SYN_CREDIT_MANIFEST= "SYN_CREDIT_MANIFEST";

    public final static String SYN_ORDER_WEIGHT_FBA= "SYN_ORDER_WEIGHT_FBA";
    public final static String SYN_DEBIT_MANIFEST_FBA= "SYN_DEBIT_MANIFEST_FBA";
    public final static String SYN_CREDIT_MANIFEST_FBA= "SYN_CREDIT_MANIFEST_FBA";

    public final static String HANDLE_INNER_BILL= "HANDLE_INNER_BILL";

    public final static String CHECK_TASK_QUERYING = "QUERYING";
    public final static String CHECK_TASK_QUERYED = "QUERYED";
    public final static String CHECK_TASK_PENDING = "PENDING";
    public final static String CHECK_TASK_FINISH = "FINISH";

    public final static String CEOS = "CEOS";
    public final static String ACTIVITY = "ACTIVITY";
    public final static String RR = "RR";


    public final static String FC_INBOUND = "FC_INBOUND";
    public final static String LOCKER = "LOCKER";

    public final static String NC_3CODE_CN = "ACN"; //中国

    public final static String BE = "BE";
    public final static String BI = "BI";

    public final static String RC_TYPE_R = "R";
    public final static String RC_TYPE_C = "C";

    public final static String IK_NAME = "快件运费";

    public final static String ASF = "ASF";

    public final static String RR_SYN_PENDING_TRANSFER = "PENDING_TRANSFER";  //等待同步中间转换表
    public final static String RR_SYN_TRANSFERING = "TRANSFERING";  //正在转换中
    public final static String RR_SYN_TRANSFERED = "TRANSFERED";   //转换完成
    public final static String RR_SYN_RRING = "RRING";  //正在同步RR表
    public final static String RR_SYN_RRED = "RRED";  //正在同步RR表


    public final static String GX_NAME_LS = "国际干线费临";
    public final static int GX_ID_LS = 100417;
    public final static int TEMPORARY_BEGIN_DATE = 20201001;

    public final static String RR_OTHER_DATE = "2021-01-01";
    public final static String DEBIT_ORDER_DATE = "2020-12-01";

    public final static String DEFAULT_HGH = "HGH";
    public final static String E_TYPE = "E";

    public final static String CORREOS_SP_CODE = "1000002";
    public final static String CORREOS_PS_NAME = "配送服务费";
    public final static Integer CORREOS_PS_CODE = 100118;
    public final static String CORREOS_PAQ_SERVICETYPE = "L_AE_STANDARD_SINOES_CACESA_RM";
    public final static String CORREOS_HWC_SERVICETYPE = "DISTRIBUTOR_13164962";
    public final static String USPS_PM_SERVICETYPE = "US_AP_STANDARD_PM"; //美线标准USPS-PM
    public final static String L_AE_STANDARD_SINOJP_RM = "L_AE_STANDARD_SINOJP_RM"; //日本线
    public final static String L_AE_PREMIUM_SINOFEDEX = "L_AE_PREMIUM_SINOFEDEX"; //
    public final static String CORREOS_CT_ID = "300";

    public final static String SPECIAL_SO = "ZSU168192";

    public final static String FEIYU_SO = "HGH168133"; //飛魚供應鏈有限公司


    public final static String APP_SO = "ZSU1000001058"; //小程序现结客户


    public final static String CN_OP_SERVICETYPE = "EUR_EXPRESS_COLISSIMO"; //菜鸟外单线路

    public static String SO_MODE_RECHARGE = "RECHARGE";  //预充值
    public static String SO_MODE_CASH = "CASH";  //现结

    public static String ON_LINE = "ONLINE";  //现结

    public static final String EAWBIETYPE_E="E";//出口






}
