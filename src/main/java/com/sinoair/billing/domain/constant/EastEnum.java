package com.sinoair.billing.domain.constant;

/**
 * @Auther: nanhb
 * @Date: 2020-03-18 10:53
 * @Description:
 */
public enum EastEnum {

    ASS("ASS", "出口报关费"),
    ASF("ASF","国内调拨费"),
    CPT("CPT","清关服务费"),
    ROE("ROE","挂号服务费"),
    GX("GX","国际干线费"),
    CK("CK","出口报关费"),
    DB("DB","国内调拨费");

    // 成员变量
    private String eastCode;
    private String prName;

    // 构造方法
    private EastEnum(String eastCode, String prName) {
        this.eastCode = eastCode;
        this.prName = prName;
    }

    public static String getName(String eastCode) {
        for (EastEnum c : EastEnum.values()) {
            if (c.getEastCode().equals(eastCode)) {
                return c.getPrName();
            }
        }
        return null;
    }

    public String getEastCode() {
        return eastCode;
    }

    public void setEastCode(String eastCode) {
        this.eastCode = eastCode;
    }

    public String getPrName() {
        return prName;
    }

    public void setPrName(String prName) {
        this.prName = prName;
    }
}
