package com.sinoair.billing.domain.constant;

/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2018/1/22
 * @time: 13:27
 * @description: To change this template use File | Settings | File Templates.
 */
public class EbaConstant {

    public final static String CN_EBA_PUSH_1 = "CN_EBA_PUSH_1";
    public final static String CN_EBA_PUSH_2 = "CN_EBA_PUSH_2";
    public final static String CN_EBA = "CN_EBA";
    public final static String CN_EBA_INBOUND_CN = "CN_EBA_INBOUND_CN";
    public final static String CN_EBA_NON_CN = "CN_EBA_NON_CN";
    public final static String BILLING_PR = "BILLING_PR";
    public final static String SYN_EBA_NON = "SYN_EBA_NON";
    public final static String INNER_BILL = "INNER_BILL";

    public final static String R_TYPE_I = "INNER";
    public final static String R_TYPE_O = "OUTER";

    public final static String M_CEOS_EBA = "CEOS_E<PERSON>";

    public final static String SUCCESS = "SUCCESS";

    public final static String PENDING = "PENDING";

    public final static String ERROR = "ERROR";

    public final static int PAGE_SIZE = 100000;
    public final static int PAGE_SIZE_1W = 10000;

    public final static int SO_TYPE_CN = 1;  //菜鸟
    public final static int SO_TYPE_NON = 10; //非菜鸟
    public final static int SO_TYPE_COMMON = 99; //公共

    public final static String EBA_EAD = "EAD_CODE"; //
    public final static String EBA_EAST = "EAST_CODE"; //

    public final static String DB = "国内调拨费"; //
    public final static String GX = "国际干线费"; //
    public final static String CK = "出口报关费"; //


    public final static String PR_CATE_A = "A"; //实际
    public final static String PR_CATE_P = "P"; //计划
    public final static String PR_CATE_C = "C"; //冲减







}
