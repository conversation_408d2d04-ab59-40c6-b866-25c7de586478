package com.sinoair.billing.domain.constant;

/**
 * @Auther: nanhb
 * @Date: 2020-03-18 10:53
 * @Description:
 */
public enum FlatStatusEnum {

    DECLARE("DECLARE", 10),
    INBOUND("INBOUND",20),
    OUTBOUND("OUTBOUND",30),
    EXCEPTION("EXCEPTION",40),
    SENDING("SENDING",50),
    UNDELIVERY("UNDELIVERY",99),
    DELIVERY("DELIVERY",100);

    // 成员变量
    private String key;
    private Integer value;

    // 构造方法
    private FlatStatusEnum(String key, Integer value) {
        this.key = key;
        this.value = value;
    }

    public static Integer getValue(String key) {
        for (FlatStatusEnum c : FlatStatusEnum.values()) {
            if (c.getKey().equals(key)) {
                return c.getValue();
            }
        }
        return null;
    }

    public static String getKey(Integer value) {
        for (FlatStatusEnum c : FlatStatusEnum.values()) {
            if (value == c.getValue()) {
                return c.getKey();
            }
        }
        return null;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }
}
