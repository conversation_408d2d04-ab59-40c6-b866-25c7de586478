package com.sinoair.billing.domain.constant;

/**
 * @Auther: nanhb
 * @Date: 2018-08-17 10:42
 * @Description:
 */
public class MailConstant {


    //轨迹来源

    public static final String MAIL_TO = "<EMAIL>";
    public static final String MAIL_TO_ONE = "<EMAIL>";
    public static final String MAIL_SUBJECT = "应收计费任务出现错误-没有找到计费任务";
    public static final String MAIL_SUBJECT_EBA = "环节同步任务出现错误-没有找到环节任务";
    public static final String MAIL_SUBJECT_RECEIPT = "应收计费任务出现错误-计费任务出现异常";
    public static final String MAIL_SUBJECT_INS_EAWB = "CEOS订单同步监控【CHECK_INS_EAWB】-出现异常";
    public static final String MAIL_SUBJECT_INS_EBA = "CEOS环节同步监控【CHECK_INS_EBA】-出现异常";
    public static final String MAIL_SUBJECT_BMS = "BMS清单汇总任务【BmsManifest】-出现异常";
    public static final String MAIL_SUBJECT_OREDER_ACCEPT = "订单池-受理订单同步异常";
    public static final String MAIL_SUBJECT_OREDER_BILL = "订单池-结审订单同步异常";
    public static final String MAIL_SUBJECT_M_EAWB = "订单池-同步清单下的eawb-出现异常";
    public static final String MAIL_SUBJECT_INS_RR = "计费信息同步正式表-出现异常";
    public static final String MAIL_SUBJECT_CEOS_EAWB = "CEOS同步BILLING系统EAWB数据异常";
    public static final String MAIL_SUBJECT_CEOS_EAD_FLAT = "同步CEOS环节拉平数据异常";
    public static final String MAIL_SUBJECT_CEOS_EAWB_CODE = "CEOS同步BILLING系统EAWB数据异常-按code";
    public static final String MAIL_SUBJECT_CEOS_EBA = "CEOS同步BILLING系统EBA数据异常";
    public static final String MAIL_SUBJECT_CEOS_CUSTOM_TAX = "CEOS同步BILLING系统关务税金数据异常";
    public static final String MAIL_SUBJECT_DM_AMOUNT = "账单汇总金额任务-出现异常";

    public static final String MAIL_BILL_TO = "<EMAIL> <EMAIL> <EMAIL> <EMAIL>";
    public static final String MAIL_BILL_SUBJECT = "账单已确认通知邮件:";
    public static final String MAIL_BILL_SUBJECT_ERROR = "【ConfirmBill】账单确认通知邮件发送异常";
    public static final String MAIL_BILL_SUBJECT_ORDER = "账单汇总明细监控邮件异常";

    public static final String MAIL_SUBJECT_TEMPORARY = "临时账单生成失败";
    public static final String MAIL_SUBJECT_CEOS_EAWB_ERR = "eawbPrintcode数据有重复";





}
