package com.sinoair.billing.domain.constant;

/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2018/1/22
 * @time: 13:27
 * @description: To change this template use File | Settings | File Templates.
 */
public class OrderPoolConstant {


    public final static int SINOTRANS_ID_LEN = 11;  //业务流水号长度

    public final static String SYS_CODE = "03012"; //系统编码（2位公司代码+1位类别代码+2位流水号）

    public final static String STATUS_CODE_ACCEPT = "POACP";
    public final static String ACCEPT_DESC = "委托受理";

    public final static String STATUS_CODE_BALANCE = "FTCFM";
    public final static String BALANCE_DESC = "结算审核";

    public final static String  BIZ_TYPE= "1301"; //业务类别  电商物流

    public final static String  IEM_E= "E";  //进出口标识符  E:出口 I:进口

    public final static String STATUS_TEMPLATE = "130001"; //状态模板

    public final static String  SENDER_CODE = "03012"; //业务系统代码 主数据系统代码

    public final static String ATTRIBUTE_5 = "1301";  //业务子类

    public final static String ATTRIBUTE_7 = "9610";  //贸易监管代码 9610/1210

    public final static String ORIGINAL_ORG = "100084"; //原业务系统ORG

    public final static String CARRIER_CODE = ""; //承运商编码


    public final static String DM_STATUS_PENDING = "PENDING";
    public final static String DM_STATUS_PROCESSING = "PROCESSING";
    public final static String DM_STATUS_FINISHED = "FINISHED";
    public final static String ERROR = "ERROR";




}
