package com.sinoair.billing.domain.constant;

/**
 * @Auther: nanhb
 * @Date: 2020-03-18 10:53
 * @Description:
 */
public enum StatusEnum {

    ACCEPT("POACP", "委托受理"),
    BALANCE("FTCFM","结算审核"),;


    // 成员变量
    private String statusCode;
    private String statusDesc;

    // 构造方法
    private StatusEnum(String statusCode, String statusDesc) {
        this.statusCode = statusCode;
        this.statusDesc = statusDesc;
    }

    public static String getName(String statusCode) {
        for (StatusEnum c : StatusEnum.values()) {
            if (c.getStatusCode().equals(statusCode)) {
                return c.getStatusDesc();
            }
        }
        return null;
    }

    public String getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(String statusCode) {
        this.statusCode = statusCode;
    }

    public String getStatusDesc() {
        return statusDesc;
    }

    public void setStatusDesc(String statusDesc) {
        this.statusDesc = statusDesc;
    }
}
