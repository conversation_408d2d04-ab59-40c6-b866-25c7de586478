package com.sinoair.billing.domain.constant;


import com.sinoair.billing.core.util.AppConfig;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: 大雄
 * @date: 2021/2/2
 * @time: 16:31
 * @description: To change this template use File | Settings | File Templates.
 */
public class XiaoTongMsgConstant {

    /**
     * 请求地址
     */
    public static final String XIAO_TONG_MESSAGE_UTL = AppConfig.getStringByProp("props/xiao_tong_msg","xiao_tong_url");

    /**
     * app id
     */
    public static final String CEOS_NOTIFY_APP_ID = AppConfig.getStringByProp("props/xiao_tong_msg","ceos_notify_app_id");

    /**
     * secert
     */
    public static final String CEOS_NOTIFY_SECERT = AppConfig.getStringByProp("props/xiao_tong_msg","ceos_notify_secert");

    /**
     * CEOS 预警通知
     */
    public static final String CEOS_WARNING_NOTIFY_APP_ID = AppConfig.getStringByProp("props/xiao_tong_msg","ceos_warning_notify_app_id");
    public static final String CEOS_WARNING_NOTIFY_SECERT = AppConfig.getStringByProp("props/xiao_tong_msg","ceos_warning_notify_secert");

}
