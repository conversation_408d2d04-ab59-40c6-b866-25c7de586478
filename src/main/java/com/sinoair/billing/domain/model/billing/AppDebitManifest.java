package com.sinoair.billing.domain.model.billing;

import java.math.BigDecimal;
import java.util.Date;

public class AppDebitManifest {
    private Integer dmId;

    private String companyId;

    private String soCode;

    private String ctCode;

    private BigDecimal dmCurrencyrate;

    private BigDecimal dmTotalrmb;

    private BigDecimal dmTotalfc;

    private BigDecimal soTax;

    private BigDecimal taxAmount;

    private BigDecimal notaxAmount;

    private BigDecimal taxAmountFc;

    private BigDecimal notaxAmountFc;

    private String invoiceId;

    private Integer dmUserId;

    private String dmStatus;

    private Date dmCreateTime;

    private Date dmHandleTime;

    private String dmDirty;

    private Date dmDirtyTime;

    private String dmCode;

    private BigDecimal dmPlanAmount;

    private Date dmStartTime;

    private Date dmEndTime;

    private Integer dmTotalPieces;

    private BigDecimal dmTotalWeight;

    private String dmRemark;

    private String dmType;

    private String dmDivideStatus;

    private String dmEmailStatus;

    private Integer orderId;

    private String unionid;

    private String couponCode;

    private BigDecimal couponAmount;

    private String dmTransactionId;

    private String outTransactionId;

    private String synStatus;

    public Integer getDmId() {
        return dmId;
    }

    public void setDmId(Integer dmId) {
        this.dmId = dmId;
    }

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId == null ? null : companyId.trim();
    }

    public String getSoCode() {
        return soCode;
    }

    public void setSoCode(String soCode) {
        this.soCode = soCode == null ? null : soCode.trim();
    }

    public String getCtCode() {
        return ctCode;
    }

    public void setCtCode(String ctCode) {
        this.ctCode = ctCode == null ? null : ctCode.trim();
    }

    public BigDecimal getDmCurrencyrate() {
        return dmCurrencyrate;
    }

    public void setDmCurrencyrate(BigDecimal dmCurrencyrate) {
        this.dmCurrencyrate = dmCurrencyrate;
    }

    public BigDecimal getDmTotalrmb() {
        return dmTotalrmb;
    }

    public void setDmTotalrmb(BigDecimal dmTotalrmb) {
        this.dmTotalrmb = dmTotalrmb;
    }

    public BigDecimal getDmTotalfc() {
        return dmTotalfc;
    }

    public void setDmTotalfc(BigDecimal dmTotalfc) {
        this.dmTotalfc = dmTotalfc;
    }

    public BigDecimal getSoTax() {
        return soTax;
    }

    public void setSoTax(BigDecimal soTax) {
        this.soTax = soTax;
    }

    public BigDecimal getTaxAmount() {
        return taxAmount;
    }

    public void setTaxAmount(BigDecimal taxAmount) {
        this.taxAmount = taxAmount;
    }

    public BigDecimal getNotaxAmount() {
        return notaxAmount;
    }

    public void setNotaxAmount(BigDecimal notaxAmount) {
        this.notaxAmount = notaxAmount;
    }

    public BigDecimal getTaxAmountFc() {
        return taxAmountFc;
    }

    public void setTaxAmountFc(BigDecimal taxAmountFc) {
        this.taxAmountFc = taxAmountFc;
    }

    public BigDecimal getNotaxAmountFc() {
        return notaxAmountFc;
    }

    public void setNotaxAmountFc(BigDecimal notaxAmountFc) {
        this.notaxAmountFc = notaxAmountFc;
    }

    public String getInvoiceId() {
        return invoiceId;
    }

    public void setInvoiceId(String invoiceId) {
        this.invoiceId = invoiceId == null ? null : invoiceId.trim();
    }

    public Integer getDmUserId() {
        return dmUserId;
    }

    public void setDmUserId(Integer dmUserId) {
        this.dmUserId = dmUserId;
    }

    public String getDmStatus() {
        return dmStatus;
    }

    public void setDmStatus(String dmStatus) {
        this.dmStatus = dmStatus == null ? null : dmStatus.trim();
    }

    public Date getDmCreateTime() {
        return dmCreateTime;
    }

    public void setDmCreateTime(Date dmCreateTime) {
        this.dmCreateTime = dmCreateTime;
    }

    public Date getDmHandleTime() {
        return dmHandleTime;
    }

    public void setDmHandleTime(Date dmHandleTime) {
        this.dmHandleTime = dmHandleTime;
    }

    public String getDmDirty() {
        return dmDirty;
    }

    public void setDmDirty(String dmDirty) {
        this.dmDirty = dmDirty == null ? null : dmDirty.trim();
    }

    public Date getDmDirtyTime() {
        return dmDirtyTime;
    }

    public void setDmDirtyTime(Date dmDirtyTime) {
        this.dmDirtyTime = dmDirtyTime;
    }

    public String getDmCode() {
        return dmCode;
    }

    public void setDmCode(String dmCode) {
        this.dmCode = dmCode == null ? null : dmCode.trim();
    }

    public BigDecimal getDmPlanAmount() {
        return dmPlanAmount;
    }

    public void setDmPlanAmount(BigDecimal dmPlanAmount) {
        this.dmPlanAmount = dmPlanAmount;
    }

    public Date getDmStartTime() {
        return dmStartTime;
    }

    public void setDmStartTime(Date dmStartTime) {
        this.dmStartTime = dmStartTime;
    }

    public Date getDmEndTime() {
        return dmEndTime;
    }

    public void setDmEndTime(Date dmEndTime) {
        this.dmEndTime = dmEndTime;
    }

    public Integer getDmTotalPieces() {
        return dmTotalPieces;
    }

    public void setDmTotalPieces(Integer dmTotalPieces) {
        this.dmTotalPieces = dmTotalPieces;
    }

    public BigDecimal getDmTotalWeight() {
        return dmTotalWeight;
    }

    public void setDmTotalWeight(BigDecimal dmTotalWeight) {
        this.dmTotalWeight = dmTotalWeight;
    }

    public String getDmRemark() {
        return dmRemark;
    }

    public void setDmRemark(String dmRemark) {
        this.dmRemark = dmRemark == null ? null : dmRemark.trim();
    }

    public String getDmType() {
        return dmType;
    }

    public void setDmType(String dmType) {
        this.dmType = dmType == null ? null : dmType.trim();
    }

    public String getDmDivideStatus() {
        return dmDivideStatus;
    }

    public void setDmDivideStatus(String dmDivideStatus) {
        this.dmDivideStatus = dmDivideStatus == null ? null : dmDivideStatus.trim();
    }

    public String getDmEmailStatus() {
        return dmEmailStatus;
    }

    public void setDmEmailStatus(String dmEmailStatus) {
        this.dmEmailStatus = dmEmailStatus == null ? null : dmEmailStatus.trim();
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getUnionid() {
        return unionid;
    }

    public void setUnionid(String unionid) {
        this.unionid = unionid == null ? null : unionid.trim();
    }

    public String getCouponCode() {
        return couponCode;
    }

    public void setCouponCode(String couponCode) {
        this.couponCode = couponCode == null ? null : couponCode.trim();
    }

    public BigDecimal getCouponAmount() {
        return couponAmount;
    }

    public void setCouponAmount(BigDecimal couponAmount) {
        this.couponAmount = couponAmount;
    }

    public String getDmTransactionId() {
        return dmTransactionId;
    }

    public void setDmTransactionId(String dmTransactionId) {
        this.dmTransactionId = dmTransactionId == null ? null : dmTransactionId.trim();
    }

    public String getOutTransactionId() {
        return outTransactionId;
    }

    public void setOutTransactionId(String outTransactionId) {
        this.outTransactionId = outTransactionId == null ? null : outTransactionId.trim();
    }

    public String getSynStatus() {
        return synStatus;
    }

    public void setSynStatus(String synStatus) {
        this.synStatus = synStatus == null ? null : synStatus.trim();
    }
}