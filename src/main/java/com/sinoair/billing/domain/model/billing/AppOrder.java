package com.sinoair.billing.domain.model.billing;

import java.math.BigDecimal;
import java.util.Date;

public class AppOrder {
    private Integer id;

    private Integer outProductid;

    private Integer productid;

    private String unionid;

    private String eawbPrintcode;

    private String orderCode;

    private BigDecimal vat;

    private BigDecimal tax;

    private BigDecimal premiums;

    private BigDecimal pickupValue;

    private BigDecimal fare;

    private String outboundCompanyId;

    private Integer pieces;

    private BigDecimal prePrice;

    private BigDecimal preTotalWeight;

    private BigDecimal preTotalVolume;

    private BigDecimal actualPrice;

    private BigDecimal actualTotalWeight;

    private BigDecimal actualTotalVolume;

    private Integer createId;

    private Date createTime;

    private Integer handler;

    private Date handleTime;

    private String remark;

    private String status;

    private String paymentMode;

    private String insuranceService;

    private String pickupType;

    private String senderName;

    private String senderContry;

    private String senderProvic;

    private String senderCity;

    private String senderArea;

    private String senderAdress;

    private String senderPostcode;

    private String senderPhone;

    private String receiveName;

    private String receiveContry;

    private String receiveProvic;

    private String receiveCity;

    private String receiveArea;

    private String receiveAdress;

    private String receivePostcode;

    private String receivePhone;

    private String receiveMobile;

    private String receiveEmail;

    private BigDecimal planPrice;

    private String orderType;

    private BigDecimal declareValue;

    private String singleClearance;

    private String isWarehouse;

    private Integer itemNum;

    private BigDecimal chargeableTotalWeight;

    private String isInsured;

    private BigDecimal insuredPrice;

    private String isClear;

    private String isDefer;

    private String isElectrici;

    private String isMagnetism;

    private String referenceId;

    private String declareCurrency;

    private String fbaProductName;

    private String senderCompany;

    private String receiveCompany;

    private String clientCode;

    private String receiveWarehouse;

    private Date arrivalWarehouseDate;

    private String receiveAmzWarehouse;

    private String receiveAmzWarehouseName;

    private String eori;

    private String soCode;

    private String vatNumber;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOutProductid() {
        return outProductid;
    }

    public void setOutProductid(Integer outProductid) {
        this.outProductid = outProductid;
    }

    public Integer getProductid() {
        return productid;
    }

    public void setProductid(Integer productid) {
        this.productid = productid;
    }

    public String getUnionid() {
        return unionid;
    }

    public void setUnionid(String unionid) {
        this.unionid = unionid == null ? null : unionid.trim();
    }

    public String getEawbPrintcode() {
        return eawbPrintcode;
    }

    public void setEawbPrintcode(String eawbPrintcode) {
        this.eawbPrintcode = eawbPrintcode == null ? null : eawbPrintcode.trim();
    }

    public String getOrderCode() {
        return orderCode;
    }

    public void setOrderCode(String orderCode) {
        this.orderCode = orderCode == null ? null : orderCode.trim();
    }

    public BigDecimal getVat() {
        return vat;
    }

    public void setVat(BigDecimal vat) {
        this.vat = vat;
    }

    public BigDecimal getTax() {
        return tax;
    }

    public void setTax(BigDecimal tax) {
        this.tax = tax;
    }

    public BigDecimal getPremiums() {
        return premiums;
    }

    public void setPremiums(BigDecimal premiums) {
        this.premiums = premiums;
    }

    public BigDecimal getPickupValue() {
        return pickupValue;
    }

    public void setPickupValue(BigDecimal pickupValue) {
        this.pickupValue = pickupValue;
    }

    public BigDecimal getFare() {
        return fare;
    }

    public void setFare(BigDecimal fare) {
        this.fare = fare;
    }

    public String getOutboundCompanyId() {
        return outboundCompanyId;
    }

    public void setOutboundCompanyId(String outboundCompanyId) {
        this.outboundCompanyId = outboundCompanyId == null ? null : outboundCompanyId.trim();
    }

    public Integer getPieces() {
        return pieces;
    }

    public void setPieces(Integer pieces) {
        this.pieces = pieces;
    }

    public BigDecimal getPrePrice() {
        return prePrice;
    }

    public void setPrePrice(BigDecimal prePrice) {
        this.prePrice = prePrice;
    }

    public BigDecimal getPreTotalWeight() {
        return preTotalWeight;
    }

    public void setPreTotalWeight(BigDecimal preTotalWeight) {
        this.preTotalWeight = preTotalWeight;
    }

    public BigDecimal getPreTotalVolume() {
        return preTotalVolume;
    }

    public void setPreTotalVolume(BigDecimal preTotalVolume) {
        this.preTotalVolume = preTotalVolume;
    }

    public BigDecimal getActualPrice() {
        return actualPrice;
    }

    public void setActualPrice(BigDecimal actualPrice) {
        this.actualPrice = actualPrice;
    }

    public BigDecimal getActualTotalWeight() {
        return actualTotalWeight;
    }

    public void setActualTotalWeight(BigDecimal actualTotalWeight) {
        this.actualTotalWeight = actualTotalWeight;
    }

    public BigDecimal getActualTotalVolume() {
        return actualTotalVolume;
    }

    public void setActualTotalVolume(BigDecimal actualTotalVolume) {
        this.actualTotalVolume = actualTotalVolume;
    }

    public Integer getCreateId() {
        return createId;
    }

    public void setCreateId(Integer createId) {
        this.createId = createId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getHandler() {
        return handler;
    }

    public void setHandler(Integer handler) {
        this.handler = handler;
    }

    public Date getHandleTime() {
        return handleTime;
    }

    public void setHandleTime(Date handleTime) {
        this.handleTime = handleTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    public String getPaymentMode() {
        return paymentMode;
    }

    public void setPaymentMode(String paymentMode) {
        this.paymentMode = paymentMode == null ? null : paymentMode.trim();
    }

    public String getInsuranceService() {
        return insuranceService;
    }

    public void setInsuranceService(String insuranceService) {
        this.insuranceService = insuranceService == null ? null : insuranceService.trim();
    }

    public String getPickupType() {
        return pickupType;
    }

    public void setPickupType(String pickupType) {
        this.pickupType = pickupType == null ? null : pickupType.trim();
    }

    public String getSenderName() {
        return senderName;
    }

    public void setSenderName(String senderName) {
        this.senderName = senderName == null ? null : senderName.trim();
    }

    public String getSenderContry() {
        return senderContry;
    }

    public void setSenderContry(String senderContry) {
        this.senderContry = senderContry == null ? null : senderContry.trim();
    }

    public String getSenderProvic() {
        return senderProvic;
    }

    public void setSenderProvic(String senderProvic) {
        this.senderProvic = senderProvic == null ? null : senderProvic.trim();
    }

    public String getSenderCity() {
        return senderCity;
    }

    public void setSenderCity(String senderCity) {
        this.senderCity = senderCity == null ? null : senderCity.trim();
    }

    public String getSenderArea() {
        return senderArea;
    }

    public void setSenderArea(String senderArea) {
        this.senderArea = senderArea == null ? null : senderArea.trim();
    }

    public String getSenderAdress() {
        return senderAdress;
    }

    public void setSenderAdress(String senderAdress) {
        this.senderAdress = senderAdress == null ? null : senderAdress.trim();
    }

    public String getSenderPostcode() {
        return senderPostcode;
    }

    public void setSenderPostcode(String senderPostcode) {
        this.senderPostcode = senderPostcode == null ? null : senderPostcode.trim();
    }

    public String getSenderPhone() {
        return senderPhone;
    }

    public void setSenderPhone(String senderPhone) {
        this.senderPhone = senderPhone == null ? null : senderPhone.trim();
    }

    public String getReceiveName() {
        return receiveName;
    }

    public void setReceiveName(String receiveName) {
        this.receiveName = receiveName == null ? null : receiveName.trim();
    }

    public String getReceiveContry() {
        return receiveContry;
    }

    public void setReceiveContry(String receiveContry) {
        this.receiveContry = receiveContry == null ? null : receiveContry.trim();
    }

    public String getReceiveProvic() {
        return receiveProvic;
    }

    public void setReceiveProvic(String receiveProvic) {
        this.receiveProvic = receiveProvic == null ? null : receiveProvic.trim();
    }

    public String getReceiveCity() {
        return receiveCity;
    }

    public void setReceiveCity(String receiveCity) {
        this.receiveCity = receiveCity == null ? null : receiveCity.trim();
    }

    public String getReceiveArea() {
        return receiveArea;
    }

    public void setReceiveArea(String receiveArea) {
        this.receiveArea = receiveArea == null ? null : receiveArea.trim();
    }

    public String getReceiveAdress() {
        return receiveAdress;
    }

    public void setReceiveAdress(String receiveAdress) {
        this.receiveAdress = receiveAdress == null ? null : receiveAdress.trim();
    }

    public String getReceivePostcode() {
        return receivePostcode;
    }

    public void setReceivePostcode(String receivePostcode) {
        this.receivePostcode = receivePostcode == null ? null : receivePostcode.trim();
    }

    public String getReceivePhone() {
        return receivePhone;
    }

    public void setReceivePhone(String receivePhone) {
        this.receivePhone = receivePhone == null ? null : receivePhone.trim();
    }

    public String getReceiveMobile() {
        return receiveMobile;
    }

    public void setReceiveMobile(String receiveMobile) {
        this.receiveMobile = receiveMobile == null ? null : receiveMobile.trim();
    }

    public String getReceiveEmail() {
        return receiveEmail;
    }

    public void setReceiveEmail(String receiveEmail) {
        this.receiveEmail = receiveEmail == null ? null : receiveEmail.trim();
    }

    public BigDecimal getPlanPrice() {
        return planPrice;
    }

    public void setPlanPrice(BigDecimal planPrice) {
        this.planPrice = planPrice;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType == null ? null : orderType.trim();
    }

    public BigDecimal getDeclareValue() {
        return declareValue;
    }

    public void setDeclareValue(BigDecimal declareValue) {
        this.declareValue = declareValue;
    }

    public String getSingleClearance() {
        return singleClearance;
    }

    public void setSingleClearance(String singleClearance) {
        this.singleClearance = singleClearance == null ? null : singleClearance.trim();
    }

    public String getIsWarehouse() {
        return isWarehouse;
    }

    public void setIsWarehouse(String isWarehouse) {
        this.isWarehouse = isWarehouse == null ? null : isWarehouse.trim();
    }

    public Integer getItemNum() {
        return itemNum;
    }

    public void setItemNum(Integer itemNum) {
        this.itemNum = itemNum;
    }

    public BigDecimal getChargeableTotalWeight() {
        return chargeableTotalWeight;
    }

    public void setChargeableTotalWeight(BigDecimal chargeableTotalWeight) {
        this.chargeableTotalWeight = chargeableTotalWeight;
    }

    public String getIsInsured() {
        return isInsured;
    }

    public void setIsInsured(String isInsured) {
        this.isInsured = isInsured == null ? null : isInsured.trim();
    }

    public BigDecimal getInsuredPrice() {
        return insuredPrice;
    }

    public void setInsuredPrice(BigDecimal insuredPrice) {
        this.insuredPrice = insuredPrice;
    }

    public String getIsClear() {
        return isClear;
    }

    public void setIsClear(String isClear) {
        this.isClear = isClear == null ? null : isClear.trim();
    }

    public String getIsDefer() {
        return isDefer;
    }

    public void setIsDefer(String isDefer) {
        this.isDefer = isDefer == null ? null : isDefer.trim();
    }

    public String getIsElectrici() {
        return isElectrici;
    }

    public void setIsElectrici(String isElectrici) {
        this.isElectrici = isElectrici == null ? null : isElectrici.trim();
    }

    public String getIsMagnetism() {
        return isMagnetism;
    }

    public void setIsMagnetism(String isMagnetism) {
        this.isMagnetism = isMagnetism == null ? null : isMagnetism.trim();
    }

    public String getReferenceId() {
        return referenceId;
    }

    public void setReferenceId(String referenceId) {
        this.referenceId = referenceId == null ? null : referenceId.trim();
    }

    public String getDeclareCurrency() {
        return declareCurrency;
    }

    public void setDeclareCurrency(String declareCurrency) {
        this.declareCurrency = declareCurrency == null ? null : declareCurrency.trim();
    }

    public String getFbaProductName() {
        return fbaProductName;
    }

    public void setFbaProductName(String fbaProductName) {
        this.fbaProductName = fbaProductName == null ? null : fbaProductName.trim();
    }

    public String getSenderCompany() {
        return senderCompany;
    }

    public void setSenderCompany(String senderCompany) {
        this.senderCompany = senderCompany == null ? null : senderCompany.trim();
    }

    public String getReceiveCompany() {
        return receiveCompany;
    }

    public void setReceiveCompany(String receiveCompany) {
        this.receiveCompany = receiveCompany == null ? null : receiveCompany.trim();
    }

    public String getClientCode() {
        return clientCode;
    }

    public void setClientCode(String clientCode) {
        this.clientCode = clientCode == null ? null : clientCode.trim();
    }

    public String getReceiveWarehouse() {
        return receiveWarehouse;
    }

    public void setReceiveWarehouse(String receiveWarehouse) {
        this.receiveWarehouse = receiveWarehouse == null ? null : receiveWarehouse.trim();
    }

    public Date getArrivalWarehouseDate() {
        return arrivalWarehouseDate;
    }

    public void setArrivalWarehouseDate(Date arrivalWarehouseDate) {
        this.arrivalWarehouseDate = arrivalWarehouseDate;
    }

    public String getReceiveAmzWarehouse() {
        return receiveAmzWarehouse;
    }

    public void setReceiveAmzWarehouse(String receiveAmzWarehouse) {
        this.receiveAmzWarehouse = receiveAmzWarehouse == null ? null : receiveAmzWarehouse.trim();
    }

    public String getReceiveAmzWarehouseName() {
        return receiveAmzWarehouseName;
    }

    public void setReceiveAmzWarehouseName(String receiveAmzWarehouseName) {
        this.receiveAmzWarehouseName = receiveAmzWarehouseName == null ? null : receiveAmzWarehouseName.trim();
    }

    public String getEori() {
        return eori;
    }

    public void setEori(String eori) {
        this.eori = eori == null ? null : eori.trim();
    }

    public String getSoCode() {
        return soCode;
    }

    public void setSoCode(String soCode) {
        this.soCode = soCode == null ? null : soCode.trim();
    }

    public String getVatNumber() {
        return vatNumber;
    }

    public void setVatNumber(String vatNumber) {
        this.vatNumber = vatNumber == null ? null : vatNumber.trim();
    }
}