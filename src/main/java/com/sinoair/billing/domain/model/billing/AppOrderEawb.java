package com.sinoair.billing.domain.model.billing;

import java.math.BigDecimal;

public class AppOrderEawb {
    private Integer id;

    private Short orderId;

    private String eawbPrintcode;

    private String eawbReference2;

    private String remark;

    private String eawbReference1;

    private String labelUrl;

    private String status;

    private BigDecimal actualWeight;

    private BigDecimal actualVolume;

    private BigDecimal chargeableWeight;

    private BigDecimal reportWeight;

    private BigDecimal reportLength;

    private BigDecimal reportWidth;

    private BigDecimal reportHight;

    private String isElectrici;

    private String isMagnetism;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Short getOrderId() {
        return orderId;
    }

    public void setOrderId(Short orderId) {
        this.orderId = orderId;
    }

    public String getEawbPrintcode() {
        return eawbPrintcode;
    }

    public void setEawbPrintcode(String eawbPrintcode) {
        this.eawbPrintcode = eawbPrintcode == null ? null : eawbPrintcode.trim();
    }

    public String getEawbReference2() {
        return eawbReference2;
    }

    public void setEawbReference2(String eawbReference2) {
        this.eawbReference2 = eawbReference2 == null ? null : eawbReference2.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public String getEawbReference1() {
        return eawbReference1;
    }

    public void setEawbReference1(String eawbReference1) {
        this.eawbReference1 = eawbReference1 == null ? null : eawbReference1.trim();
    }

    public String getLabelUrl() {
        return labelUrl;
    }

    public void setLabelUrl(String labelUrl) {
        this.labelUrl = labelUrl == null ? null : labelUrl.trim();
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    public BigDecimal getActualWeight() {
        return actualWeight;
    }

    public void setActualWeight(BigDecimal actualWeight) {
        this.actualWeight = actualWeight;
    }

    public BigDecimal getActualVolume() {
        return actualVolume;
    }

    public void setActualVolume(BigDecimal actualVolume) {
        this.actualVolume = actualVolume;
    }

    public BigDecimal getChargeableWeight() {
        return chargeableWeight;
    }

    public void setChargeableWeight(BigDecimal chargeableWeight) {
        this.chargeableWeight = chargeableWeight;
    }

    public BigDecimal getReportWeight() {
        return reportWeight;
    }

    public void setReportWeight(BigDecimal reportWeight) {
        this.reportWeight = reportWeight;
    }

    public BigDecimal getReportLength() {
        return reportLength;
    }

    public void setReportLength(BigDecimal reportLength) {
        this.reportLength = reportLength;
    }

    public BigDecimal getReportWidth() {
        return reportWidth;
    }

    public void setReportWidth(BigDecimal reportWidth) {
        this.reportWidth = reportWidth;
    }

    public BigDecimal getReportHight() {
        return reportHight;
    }

    public void setReportHight(BigDecimal reportHight) {
        this.reportHight = reportHight;
    }

    public String getIsElectrici() {
        return isElectrici;
    }

    public void setIsElectrici(String isElectrici) {
        this.isElectrici = isElectrici == null ? null : isElectrici.trim();
    }

    public String getIsMagnetism() {
        return isMagnetism;
    }

    public void setIsMagnetism(String isMagnetism) {
        this.isMagnetism = isMagnetism == null ? null : isMagnetism.trim();
    }
}