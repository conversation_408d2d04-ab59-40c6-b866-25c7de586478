package com.sinoair.billing.domain.model.billing;

import java.math.BigDecimal;
import java.util.Date;

public class AppReceiptRecord {
    private Long rrId;

    private String eawbPrintcode;

    private String mawbCode;

    private Integer prId;

    private Integer dmId;

    private String soCode;

    private String ctCode;

    private String companyId;

    private String rrName;

    private String rrType;

    private BigDecimal rrPlanAmount;

    private BigDecimal rrActualAmount;

    private String rrStatus;

    private Short rrUserId;

    private Date rrHandletime;

    private String rrAwbType;

    private String rrRemark;

    private String eawbReference1;

    private String eawbReference2;

    private BigDecimal eawbChargeableweight;

    private Integer eawbHawbQty;

    private Date rrOccurtime;

    private Object epKey;

    private BigDecimal chargeableweight;

    private Integer pdSyscode;

    private String outboundCompanyId;

    private String eawbIetype;

    private String eawbDestcountry;

    private String eawbDestination;

    private String eawbDepartcountry;

    private String eawbDeparture;

    private String bmsNum;

    private String bmsDirty;

    private Date rrOccurtime2;

    private String estimateStatus;

    private String unionid;

    private String couponCode;

    private BigDecimal couponAmount;

    private Integer orderId;

    private Short payStatus;

    private BigDecimal rrAmountOriginal;

    public Long getRrId() {
        return rrId;
    }

    public void setRrId(Long rrId) {
        this.rrId = rrId;
    }

    public String getEawbPrintcode() {
        return eawbPrintcode;
    }

    public void setEawbPrintcode(String eawbPrintcode) {
        this.eawbPrintcode = eawbPrintcode == null ? null : eawbPrintcode.trim();
    }

    public String getMawbCode() {
        return mawbCode;
    }

    public void setMawbCode(String mawbCode) {
        this.mawbCode = mawbCode == null ? null : mawbCode.trim();
    }

    public Integer getPrId() {
        return prId;
    }

    public void setPrId(Integer prId) {
        this.prId = prId;
    }

    public Integer getDmId() {
        return dmId;
    }

    public void setDmId(Integer dmId) {
        this.dmId = dmId;
    }

    public String getSoCode() {
        return soCode;
    }

    public void setSoCode(String soCode) {
        this.soCode = soCode == null ? null : soCode.trim();
    }

    public String getCtCode() {
        return ctCode;
    }

    public void setCtCode(String ctCode) {
        this.ctCode = ctCode == null ? null : ctCode.trim();
    }

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId == null ? null : companyId.trim();
    }

    public String getRrName() {
        return rrName;
    }

    public void setRrName(String rrName) {
        this.rrName = rrName == null ? null : rrName.trim();
    }

    public String getRrType() {
        return rrType;
    }

    public void setRrType(String rrType) {
        this.rrType = rrType == null ? null : rrType.trim();
    }

    public BigDecimal getRrPlanAmount() {
        return rrPlanAmount;
    }

    public void setRrPlanAmount(BigDecimal rrPlanAmount) {
        this.rrPlanAmount = rrPlanAmount;
    }

    public BigDecimal getRrActualAmount() {
        return rrActualAmount;
    }

    public void setRrActualAmount(BigDecimal rrActualAmount) {
        this.rrActualAmount = rrActualAmount;
    }

    public String getRrStatus() {
        return rrStatus;
    }

    public void setRrStatus(String rrStatus) {
        this.rrStatus = rrStatus == null ? null : rrStatus.trim();
    }

    public Short getRrUserId() {
        return rrUserId;
    }

    public void setRrUserId(Short rrUserId) {
        this.rrUserId = rrUserId;
    }

    public Date getRrHandletime() {
        return rrHandletime;
    }

    public void setRrHandletime(Date rrHandletime) {
        this.rrHandletime = rrHandletime;
    }

    public String getRrAwbType() {
        return rrAwbType;
    }

    public void setRrAwbType(String rrAwbType) {
        this.rrAwbType = rrAwbType == null ? null : rrAwbType.trim();
    }

    public String getRrRemark() {
        return rrRemark;
    }

    public void setRrRemark(String rrRemark) {
        this.rrRemark = rrRemark == null ? null : rrRemark.trim();
    }

    public String getEawbReference1() {
        return eawbReference1;
    }

    public void setEawbReference1(String eawbReference1) {
        this.eawbReference1 = eawbReference1 == null ? null : eawbReference1.trim();
    }

    public String getEawbReference2() {
        return eawbReference2;
    }

    public void setEawbReference2(String eawbReference2) {
        this.eawbReference2 = eawbReference2 == null ? null : eawbReference2.trim();
    }

    public BigDecimal getEawbChargeableweight() {
        return eawbChargeableweight;
    }

    public void setEawbChargeableweight(BigDecimal eawbChargeableweight) {
        this.eawbChargeableweight = eawbChargeableweight;
    }

    public Integer getEawbHawbQty() {
        return eawbHawbQty;
    }

    public void setEawbHawbQty(Integer eawbHawbQty) {
        this.eawbHawbQty = eawbHawbQty;
    }

    public Date getRrOccurtime() {
        return rrOccurtime;
    }

    public void setRrOccurtime(Date rrOccurtime) {
        this.rrOccurtime = rrOccurtime;
    }

    public Object getEpKey() {
        return epKey;
    }

    public void setEpKey(Object epKey) {
        this.epKey = epKey;
    }

    public BigDecimal getChargeableweight() {
        return chargeableweight;
    }

    public void setChargeableweight(BigDecimal chargeableweight) {
        this.chargeableweight = chargeableweight;
    }

    public Integer getPdSyscode() {
        return pdSyscode;
    }

    public void setPdSyscode(Integer pdSyscode) {
        this.pdSyscode = pdSyscode;
    }

    public String getOutboundCompanyId() {
        return outboundCompanyId;
    }

    public void setOutboundCompanyId(String outboundCompanyId) {
        this.outboundCompanyId = outboundCompanyId == null ? null : outboundCompanyId.trim();
    }

    public String getEawbIetype() {
        return eawbIetype;
    }

    public void setEawbIetype(String eawbIetype) {
        this.eawbIetype = eawbIetype == null ? null : eawbIetype.trim();
    }

    public String getEawbDestcountry() {
        return eawbDestcountry;
    }

    public void setEawbDestcountry(String eawbDestcountry) {
        this.eawbDestcountry = eawbDestcountry == null ? null : eawbDestcountry.trim();
    }

    public String getEawbDestination() {
        return eawbDestination;
    }

    public void setEawbDestination(String eawbDestination) {
        this.eawbDestination = eawbDestination == null ? null : eawbDestination.trim();
    }

    public String getEawbDepartcountry() {
        return eawbDepartcountry;
    }

    public void setEawbDepartcountry(String eawbDepartcountry) {
        this.eawbDepartcountry = eawbDepartcountry == null ? null : eawbDepartcountry.trim();
    }

    public String getEawbDeparture() {
        return eawbDeparture;
    }

    public void setEawbDeparture(String eawbDeparture) {
        this.eawbDeparture = eawbDeparture == null ? null : eawbDeparture.trim();
    }

    public String getBmsNum() {
        return bmsNum;
    }

    public void setBmsNum(String bmsNum) {
        this.bmsNum = bmsNum == null ? null : bmsNum.trim();
    }

    public String getBmsDirty() {
        return bmsDirty;
    }

    public void setBmsDirty(String bmsDirty) {
        this.bmsDirty = bmsDirty == null ? null : bmsDirty.trim();
    }

    public Date getRrOccurtime2() {
        return rrOccurtime2;
    }

    public void setRrOccurtime2(Date rrOccurtime2) {
        this.rrOccurtime2 = rrOccurtime2;
    }

    public String getEstimateStatus() {
        return estimateStatus;
    }

    public void setEstimateStatus(String estimateStatus) {
        this.estimateStatus = estimateStatus == null ? null : estimateStatus.trim();
    }

    public String getUnionid() {
        return unionid;
    }

    public void setUnionid(String unionid) {
        this.unionid = unionid == null ? null : unionid.trim();
    }

    public String getCouponCode() {
        return couponCode;
    }

    public void setCouponCode(String couponCode) {
        this.couponCode = couponCode == null ? null : couponCode.trim();
    }

    public BigDecimal getCouponAmount() {
        return couponAmount;
    }

    public void setCouponAmount(BigDecimal couponAmount) {
        this.couponAmount = couponAmount;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public Short getPayStatus() {
        return payStatus;
    }

    public void setPayStatus(Short payStatus) {
        this.payStatus = payStatus;
    }

    public BigDecimal getRrAmountOriginal() {
        return rrAmountOriginal;
    }

    public void setRrAmountOriginal(BigDecimal rrAmountOriginal) {
        this.rrAmountOriginal = rrAmountOriginal;
    }
}