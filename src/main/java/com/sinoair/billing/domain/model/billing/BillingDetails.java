package com.sinoair.billing.domain.model.billing;

import java.math.BigDecimal;
import java.util.Date;

public class BillingDetails {
    private Integer bSyscode;

    private Integer bId;

    private String bType;

    private String diffType;

    private BigDecimal diffWeight;

    private Integer diffQuantity;

    private BigDecimal diffAmount;

    private String diffReason;

    private String replyStatus;

    private String replyRemarks;

    private String replyPerson;

    private Date replyTime;

    private Date diffHandletime;

    private Integer diffUserId;

    private BigDecimal confirmAmount;

    private String isClose;

    private String bCode;

    private String ctCode;

    public Integer getbSyscode() {
        return bSyscode;
    }

    public void setbSyscode(Integer bSyscode) {
        this.bSyscode = bSyscode;
    }

    public Integer getbId() {
        return bId;
    }

    public void setbId(Integer bId) {
        this.bId = bId;
    }

    public String getbType() {
        return bType;
    }

    public void setbType(String bType) {
        this.bType = bType == null ? null : bType.trim();
    }

    public String getDiffType() {
        return diffType;
    }

    public void setDiffType(String diffType) {
        this.diffType = diffType == null ? null : diffType.trim();
    }

    public BigDecimal getDiffWeight() {
        return diffWeight;
    }

    public void setDiffWeight(BigDecimal diffWeight) {
        this.diffWeight = diffWeight;
    }

    public Integer getDiffQuantity() {
        return diffQuantity;
    }

    public void setDiffQuantity(Integer diffQuantity) {
        this.diffQuantity = diffQuantity;
    }

    public BigDecimal getDiffAmount() {
        return diffAmount;
    }

    public void setDiffAmount(BigDecimal diffAmount) {
        this.diffAmount = diffAmount;
    }

    public String getDiffReason() {
        return diffReason;
    }

    public void setDiffReason(String diffReason) {
        this.diffReason = diffReason == null ? null : diffReason.trim();
    }

    public String getReplyStatus() {
        return replyStatus;
    }

    public void setReplyStatus(String replyStatus) {
        this.replyStatus = replyStatus == null ? null : replyStatus.trim();
    }

    public String getReplyRemarks() {
        return replyRemarks;
    }

    public void setReplyRemarks(String replyRemarks) {
        this.replyRemarks = replyRemarks == null ? null : replyRemarks.trim();
    }

    public String getReplyPerson() {
        return replyPerson;
    }

    public void setReplyPerson(String replyPerson) {
        this.replyPerson = replyPerson == null ? null : replyPerson.trim();
    }

    public Date getReplyTime() {
        return replyTime;
    }

    public void setReplyTime(Date replyTime) {
        this.replyTime = replyTime;
    }

    public Date getDiffHandletime() {
        return diffHandletime;
    }

    public void setDiffHandletime(Date diffHandletime) {
        this.diffHandletime = diffHandletime;
    }

    public Integer getDiffUserId() {
        return diffUserId;
    }

    public void setDiffUserId(Integer diffUserId) {
        this.diffUserId = diffUserId;
    }

    public BigDecimal getConfirmAmount() {
        return confirmAmount;
    }

    public void setConfirmAmount(BigDecimal confirmAmount) {
        this.confirmAmount = confirmAmount;
    }

    public String getIsClose() {
        return isClose;
    }

    public void setIsClose(String isClose) {
        this.isClose = isClose == null ? null : isClose.trim();
    }

    public String getbCode() {
        return bCode;
    }

    public void setbCode(String bCode) {
        this.bCode = bCode == null ? null : bCode.trim();
    }

    public String getCtCode() {
        return ctCode;
    }

    public void setCtCode(String ctCode) {
        this.ctCode = ctCode == null ? null : ctCode.trim();
    }
}