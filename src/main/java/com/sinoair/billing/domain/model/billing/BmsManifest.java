package com.sinoair.billing.domain.model.billing;

import java.math.BigDecimal;
import java.util.Date;

public class BmsManifest {
    private Long bmSyscode;

    private String companyId;

    private String soCode;

    private String sinotransId;

    private String ikName;

    private String ctSign;

    private BigDecimal ctRate;

    private BigDecimal bmTotalrmb;

    private BigDecimal bmTotalfc;

    private BigDecimal taxAmount;

    private BigDecimal notaxAmount;

    private BigDecimal taxAmountFc;

    private BigDecimal notaxAmountFc;

    private Integer bmPiece;

    private BigDecimal bmChargeableweight;

    private Date completionDate;

    private String indentifierRc;

    private String invoiceType;

    private String invoiceNum;

    private BigDecimal taxRate;

    private String soName;

    private Date bmHandleTime;

    private Long mId;

    private String mCode;

    private String remark;

    private String ieType;

    private BigDecimal receiptAmount;

    private String businessType;

    private String bmType;

    private String salesman;

    private String salesmanCode;

    private String epKey;

    private String epValue;

    private String departureCode;

    private String destCode;

    public Long getBmSyscode() {
        return bmSyscode;
    }

    public void setBmSyscode(Long bmSyscode) {
        this.bmSyscode = bmSyscode;
    }

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId == null ? null : companyId.trim();
    }

    public String getSoCode() {
        return soCode;
    }

    public void setSoCode(String soCode) {
        this.soCode = soCode == null ? null : soCode.trim();
    }

    public String getSinotransId() {
        return sinotransId;
    }

    public void setSinotransId(String sinotransId) {
        this.sinotransId = sinotransId == null ? null : sinotransId.trim();
    }

    public String getIkName() {
        return ikName;
    }

    public void setIkName(String ikName) {
        this.ikName = ikName == null ? null : ikName.trim();
    }

    public String getCtSign() {
        return ctSign;
    }

    public void setCtSign(String ctSign) {
        this.ctSign = ctSign == null ? null : ctSign.trim();
    }

    public BigDecimal getCtRate() {
        return ctRate;
    }

    public void setCtRate(BigDecimal ctRate) {
        this.ctRate = ctRate;
    }

    public BigDecimal getBmTotalrmb() {
        return bmTotalrmb;
    }

    public void setBmTotalrmb(BigDecimal bmTotalrmb) {
        this.bmTotalrmb = bmTotalrmb;
    }

    public BigDecimal getBmTotalfc() {
        return bmTotalfc;
    }

    public void setBmTotalfc(BigDecimal bmTotalfc) {
        this.bmTotalfc = bmTotalfc;
    }

    public BigDecimal getTaxAmount() {
        return taxAmount;
    }

    public void setTaxAmount(BigDecimal taxAmount) {
        this.taxAmount = taxAmount;
    }

    public BigDecimal getNotaxAmount() {
        return notaxAmount;
    }

    public void setNotaxAmount(BigDecimal notaxAmount) {
        this.notaxAmount = notaxAmount;
    }

    public BigDecimal getTaxAmountFc() {
        return taxAmountFc;
    }

    public void setTaxAmountFc(BigDecimal taxAmountFc) {
        this.taxAmountFc = taxAmountFc;
    }

    public BigDecimal getNotaxAmountFc() {
        return notaxAmountFc;
    }

    public void setNotaxAmountFc(BigDecimal notaxAmountFc) {
        this.notaxAmountFc = notaxAmountFc;
    }

    public Integer getBmPiece() {
        return bmPiece;
    }

    public void setBmPiece(Integer bmPiece) {
        this.bmPiece = bmPiece;
    }

    public BigDecimal getBmChargeableweight() {
        return bmChargeableweight;
    }

    public void setBmChargeableweight(BigDecimal bmChargeableweight) {
        this.bmChargeableweight = bmChargeableweight;
    }

    public Date getCompletionDate() {
        return completionDate;
    }

    public void setCompletionDate(Date completionDate) {
        this.completionDate = completionDate;
    }

    public String getIndentifierRc() {
        return indentifierRc;
    }

    public void setIndentifierRc(String indentifierRc) {
        this.indentifierRc = indentifierRc == null ? null : indentifierRc.trim();
    }

    public String getInvoiceType() {
        return invoiceType;
    }

    public void setInvoiceType(String invoiceType) {
        this.invoiceType = invoiceType == null ? null : invoiceType.trim();
    }

    public String getInvoiceNum() {
        return invoiceNum;
    }

    public void setInvoiceNum(String invoiceNum) {
        this.invoiceNum = invoiceNum == null ? null : invoiceNum.trim();
    }

    public BigDecimal getTaxRate() {
        return taxRate;
    }

    public void setTaxRate(BigDecimal taxRate) {
        this.taxRate = taxRate;
    }

    public String getSoName() {
        return soName;
    }

    public void setSoName(String soName) {
        this.soName = soName == null ? null : soName.trim();
    }

    public Date getBmHandleTime() {
        return bmHandleTime;
    }

    public void setBmHandleTime(Date bmHandleTime) {
        this.bmHandleTime = bmHandleTime;
    }

    public Long getmId() {
        return mId;
    }

    public void setmId(Long mId) {
        this.mId = mId;
    }

    public String getmCode() {
        return mCode;
    }

    public void setmCode(String mCode) {
        this.mCode = mCode == null ? null : mCode.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public String getIeType() {
        return ieType;
    }

    public void setIeType(String ieType) {
        this.ieType = ieType == null ? null : ieType.trim();
    }

    public BigDecimal getReceiptAmount() {
        return receiptAmount;
    }

    public void setReceiptAmount(BigDecimal receiptAmount) {
        this.receiptAmount = receiptAmount;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType == null ? null : businessType.trim();
    }

    public String getBmType() {
        return bmType;
    }

    public void setBmType(String bmType) {
        this.bmType = bmType == null ? null : bmType.trim();
    }

    public String getSalesman() {
        return salesman;
    }

    public void setSalesman(String salesman) {
        this.salesman = salesman == null ? null : salesman.trim();
    }

    public String getSalesmanCode() {
        return salesmanCode;
    }

    public void setSalesmanCode(String salesmanCode) {
        this.salesmanCode = salesmanCode == null ? null : salesmanCode.trim();
    }

    public String getEpKey() {
        return epKey;
    }

    public void setEpKey(String epKey) {
        this.epKey = epKey;
    }

    public String getEpValue() {
        return epValue;
    }

    public void setEpValue(String epValue) {
        this.epValue = epValue;
    }

    public String getDepartureCode() {
        return departureCode;
    }

    public void setDepartureCode(String departureCode) {
        this.departureCode = departureCode;
    }

    public String getDestCode() {
        return destCode;
    }

    public void setDestCode(String destCode) {
        this.destCode = destCode;
    }
}