package com.sinoair.billing.domain.model.billing;

import java.math.BigDecimal;
import java.util.Date;

public class BmsPayDetail {
    private Long bpdSyscode;

    private Integer cmId;

    private String epKey;

    private String sinotransId;

    private BigDecimal payAmount;

    private String sacId;

    private String soCode;

    private Date bpdHandletime;

    private String ctSign;
    private String ctCode;

    private Integer cmMonth;

    private String prType;

    private String dDirty;

    private Date createtime;

    public String getCtCode() {
        return ctCode;
    }

    public void setCtCode(String ctCode) {
        this.ctCode = ctCode;
    }

    public Long getBpdSyscode() {
        return bpdSyscode;
    }

    public void setBpdSyscode(Long bpdSyscode) {
        this.bpdSyscode = bpdSyscode;
    }

    public Integer getCmId() {
        return cmId;
    }

    public void setCmId(Integer cmId) {
        this.cmId = cmId;
    }

    public String getEpKey() {
        return epKey;
    }

    public void setEpKey(String epKey) {
        this.epKey = epKey == null ? null : epKey.trim();
    }

    public String getSinotransId() {
        return sinotransId;
    }

    public void setSinotransId(String sinotransId) {
        this.sinotransId = sinotransId == null ? null : sinotransId.trim();
    }

    public BigDecimal getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(BigDecimal payAmount) {
        this.payAmount = payAmount;
    }

    public String getSacId() {
        return sacId;
    }

    public void setSacId(String sacId) {
        this.sacId = sacId == null ? null : sacId.trim();
    }

    public String getSoCode() {
        return soCode;
    }

    public void setSoCode(String soCode) {
        this.soCode = soCode == null ? null : soCode.trim();
    }

    public Date getBpdHandletime() {
        return bpdHandletime;
    }

    public void setBpdHandletime(Date bpdHandletime) {
        this.bpdHandletime = bpdHandletime;
    }

    public String getCtSign() {
        return ctSign;
    }

    public void setCtSign(String ctSign) {
        this.ctSign = ctSign == null ? null : ctSign.trim();
    }

    public Integer getCmMonth() {
        return cmMonth;
    }

    public void setCmMonth(Integer cmMonth) {
        this.cmMonth = cmMonth;
    }

    public String getPrType() {
        return prType;
    }

    public void setPrType(String prType) {
        this.prType = prType == null ? null : prType.trim();
    }

    public String getdDirty() {
        return dDirty;
    }

    public void setdDirty(String dDirty) {
        this.dDirty = dDirty == null ? null : dDirty.trim();
    }

    public Date getCreatetime() {
        return createtime;
    }

    public void setCreatetime(Date createtime) {
        this.createtime = createtime;
    }
}