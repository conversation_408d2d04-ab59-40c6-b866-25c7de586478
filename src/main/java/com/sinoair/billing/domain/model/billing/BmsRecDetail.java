package com.sinoair.billing.domain.model.billing;

import java.math.BigDecimal;
import java.util.Date;

public class BmsRecDetail {
    private Long bmdSyscode;

    private Integer dmId;

    private String epKey;

    private String sinotransId;

    private BigDecimal receiptAmount;

    private String sacId;

    private String soCode;

    private Date bmdHandletime;

    private String ctSign;

    private Integer dmMonth;

    public Long getBmdSyscode() {
        return bmdSyscode;
    }

    public void setBmdSyscode(Long bmdSyscode) {
        this.bmdSyscode = bmdSyscode;
    }

    public Integer getDmId() {
        return dmId;
    }

    public void setDmId(Integer dmId) {
        this.dmId = dmId;
    }

    public String getEpKey() {
        return epKey;
    }

    public void setEpKey(String epKey) {
        this.epKey = epKey == null ? null : epKey.trim();
    }

    public String getSinotransId() {
        return sinotransId;
    }

    public void setSinotransId(String sinotransId) {
        this.sinotransId = sinotransId == null ? null : sinotransId.trim();
    }

    public BigDecimal getReceiptAmount() {
        return receiptAmount;
    }

    public void setReceiptAmount(BigDecimal receiptAmount) {
        this.receiptAmount = receiptAmount;
    }

    public String getSacId() {
        return sacId;
    }

    public void setSacId(String sacId) {
        this.sacId = sacId == null ? null : sacId.trim();
    }

    public String getSoCode() {
        return soCode;
    }

    public void setSoCode(String soCode) {
        this.soCode = soCode == null ? null : soCode.trim();
    }

    public Date getBmdHandletime() {
        return bmdHandletime;
    }

    public void setBmdHandletime(Date bmdHandletime) {
        this.bmdHandletime = bmdHandletime;
    }

    public String getCtSign() {
        return ctSign;
    }

    public void setCtSign(String ctSign) {
        this.ctSign = ctSign == null ? null : ctSign.trim();
    }

    public Integer getDmMonth() {
        return dmMonth;
    }

    public void setDmMonth(Integer dmMonth) {
        this.dmMonth = dmMonth;
    }
}