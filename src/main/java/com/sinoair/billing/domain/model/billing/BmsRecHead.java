package com.sinoair.billing.domain.model.billing;

import java.math.BigDecimal;

public class BmsRecHead {
    private Integer collectMonth;

    private String epKey;

    private String sinotransId;

    private Integer bmPiece;

    private BigDecimal bmChargeableweight;

    private String departureCode;

    private String destCode;

    private String sacId;

    private String soCode;

    private String soMode;

    public Integer getCollectMonth() {
        return collectMonth;
    }

    public void setCollectMonth(Integer collectMonth) {
        this.collectMonth = collectMonth;
    }

    public String getEpKey() {
        return epKey;
    }

    public void setEpKey(String epKey) {
        this.epKey = epKey == null ? null : epKey.trim();
    }

    public String getSinotransId() {
        return sinotransId;
    }

    public void setSinotransId(String sinotransId) {
        this.sinotransId = sinotransId == null ? null : sinotransId.trim();
    }

    public Integer getBmPiece() {
        return bmPiece;
    }

    public void setBmPiece(Integer bmPiece) {
        this.bmPiece = bmPiece;
    }

    public BigDecimal getBmChargeableweight() {
        return bmChargeableweight;
    }

    public void setBmChargeableweight(BigDecimal bmChargeableweight) {
        this.bmChargeableweight = bmChargeableweight;
    }

    public String getDepartureCode() {
        return departureCode;
    }

    public void setDepartureCode(String departureCode) {
        this.departureCode = departureCode == null ? null : departureCode.trim();
    }

    public String getDestCode() {
        return destCode;
    }

    public void setDestCode(String destCode) {
        this.destCode = destCode == null ? null : destCode.trim();
    }

    public String getSacId() {
        return sacId;
    }

    public void setSacId(String sacId) {
        this.sacId = sacId;
    }

    public String getSoCode() {
        return soCode;
    }

    public void setSoCode(String soCode) {
        this.soCode = soCode;
    }

    public String getSoMode() {
        return soMode;
    }

    public void setSoMode(String soMode) {
        this.soMode = soMode;
    }
}