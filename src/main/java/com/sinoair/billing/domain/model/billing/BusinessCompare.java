package com.sinoair.billing.domain.model.billing;

import java.util.Date;

public class BusinessCompare extends BusinessCompareKey {
    private String eawbServicetype;

    private Date pbdDate;

    private String fieldType;

    private Date bcHandletime;

    private Integer bcUserId;

    public String getEawbServicetype() {
        return eawbServicetype;
    }

    public void setEawbServicetype(String eawbServicetype) {
        this.eawbServicetype = eawbServicetype == null ? null : eawbServicetype.trim();
    }

    public Date getPbdDate() {
        return pbdDate;
    }

    public void setPbdDate(Date pbdDate) {
        this.pbdDate = pbdDate;
    }

    public String getFieldType() {
        return fieldType;
    }

    public void setFieldType(String fieldType) {
        this.fieldType = fieldType == null ? null : fieldType.trim();
    }

    public Date getBcHandletime() {
        return bcHandletime;
    }

    public void setBcHandletime(Date bcHandletime) {
        this.bcHandletime = bcHandletime;
    }

    public Integer getBcUserId() {
        return bcUserId;
    }

    public void setBcUserId(Integer bcUserId) {
        this.bcUserId = bcUserId;
    }
}