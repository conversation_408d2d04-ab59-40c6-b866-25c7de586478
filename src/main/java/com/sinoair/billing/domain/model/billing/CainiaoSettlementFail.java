package com.sinoair.billing.domain.model.billing;

import java.util.Date;

public class CainiaoSettlementFail {
    private Integer cnsfailId;

    private String eawbReference2;

    private String rrName;

    private String cnDmId;

    private Date createDate;

    private String paymentOrderId;

    private String fileName;

    private String remarks;

    private String failType;

    public Integer getCnsfailId() {
        return cnsfailId;
    }

    public void setCnsfailId(Integer cnsfailId) {
        this.cnsfailId = cnsfailId;
    }

    public String getEawbReference2() {
        return eawbReference2;
    }

    public void setEawbReference2(String eawbReference2) {
        this.eawbReference2 = eawbReference2 == null ? null : eawbReference2.trim();
    }

    public String getRrName() {
        return rrName;
    }

    public void setRrName(String rrName) {
        this.rrName = rrName == null ? null : rrName.trim();
    }

    public String getCnDmId() {
        return cnDmId;
    }

    public void setCnDmId(String cnDmId) {
        this.cnDmId = cnDmId == null ? null : cnDmId.trim();
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getPaymentOrderId() {
        return paymentOrderId;
    }

    public void setPaymentOrderId(String paymentOrderId) {
        this.paymentOrderId = paymentOrderId == null ? null : paymentOrderId.trim();
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName == null ? null : fileName.trim();
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks == null ? null : remarks.trim();
    }

    public String getFailType() {
        return failType;
    }

    public void setFailType(String failType) {
        this.failType = failType;
    }
}