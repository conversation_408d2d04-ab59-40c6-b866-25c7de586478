package com.sinoair.billing.domain.model.billing;

import java.math.BigDecimal;

public class CainiaoSettlementSuc {
    private String eawbReference2;

    private String mawbCode;

    private String eawbKeyentrytime;

    private String rrName;

    private String rrOccurtime;

    private String cnProduct;

    private BigDecimal rrAmount;

    private String ctSign;

    private String cnDmId;

    private String paymentOrderId;

    private String fileName;

    public String getEawbReference2() {
        return eawbReference2;
    }

    public void setEawbReference2(String eawbReference2) {
        this.eawbReference2 = eawbReference2 == null ? null : eawbReference2.trim();
    }

    public String getMawbCode() {
        return mawbCode;
    }

    public void setMawbCode(String mawbCode) {
        this.mawbCode = mawbCode == null ? null : mawbCode.trim();
    }

    public String getEawbKeyentrytime() {
        return eawbKeyentrytime;
    }

    public void setEawbKeyentrytime(String eawbKeyentrytime) {
        this.eawbKeyentrytime = eawbKeyentrytime == null ? null : eawbKeyentrytime.trim();
    }

    public String getRrName() {
        return rrName;
    }

    public void setRrName(String rrName) {
        this.rrName = rrName == null ? null : rrName.trim();
    }

    public String getRrOccurtime() {
        return rrOccurtime;
    }

    public void setRrOccurtime(String rrOccurtime) {
        this.rrOccurtime = rrOccurtime == null ? null : rrOccurtime.trim();
    }

    public String getCnProduct() {
        return cnProduct;
    }

    public void setCnProduct(String cnProduct) {
        this.cnProduct = cnProduct == null ? null : cnProduct.trim();
    }

    public BigDecimal getRrAmount() {
        return rrAmount;
    }

    public void setRrAmount(BigDecimal rrAmount) {
        this.rrAmount = rrAmount;
    }

    public String getCtSign() {
        return ctSign;
    }

    public void setCtSign(String ctSign) {
        this.ctSign = ctSign == null ? null : ctSign.trim();
    }

    public String getCnDmId() {
        return cnDmId;
    }

    public void setCnDmId(String cnDmId) {
        this.cnDmId = cnDmId == null ? null : cnDmId.trim();
    }

    public String getPaymentOrderId() {
        return paymentOrderId;
    }

    public void setPaymentOrderId(String paymentOrderId) {
        this.paymentOrderId = paymentOrderId;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }
}