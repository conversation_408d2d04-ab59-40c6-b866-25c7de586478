package com.sinoair.billing.domain.model.billing;

import java.math.BigDecimal;

public class Cainiaowulish {
    private String zwypart;

    private String lujing;

    private String zwybiaoming;

    private String zwyfoldername;

    private String zwytablename;

    private String zwybillmonth;

    private String zwylpno;

    private BigDecimal zwyweightkg;

    private String zwyfeename;

    private String zwycurrency;

    private BigDecimal zwyamount;

    private String zwycomment;

    private String remark;

    private String entityValue;

    private String serviceItemCode;

    private String serviceItemName;

    private String userId;

    private String maxCreateTime;

    private String minCreateTime;

    private String userNick;

    private BigDecimal weightSum;

    private String feeName;

    private String isPaySuccess;

    private String payCurrency;

    private BigDecimal chargeAmount;

    private String istype;

    private String type;

    private String fileBatch;

    public String getZwypart() {
        return zwypart;
    }

    public void setZwypart(String zwypart) {
        this.zwypart = zwypart == null ? null : zwypart.trim();
    }

    public String getLujing() {
        return lujing;
    }

    public void setLujing(String lujing) {
        this.lujing = lujing == null ? null : lujing.trim();
    }

    public String getZwybiaoming() {
        return zwybiaoming;
    }

    public void setZwybiaoming(String zwybiaoming) {
        this.zwybiaoming = zwybiaoming == null ? null : zwybiaoming.trim();
    }

    public String getZwyfoldername() {
        return zwyfoldername;
    }

    public void setZwyfoldername(String zwyfoldername) {
        this.zwyfoldername = zwyfoldername == null ? null : zwyfoldername.trim();
    }

    public String getZwytablename() {
        return zwytablename;
    }

    public void setZwytablename(String zwytablename) {
        this.zwytablename = zwytablename == null ? null : zwytablename.trim();
    }

    public String getZwybillmonth() {
        return zwybillmonth;
    }

    public void setZwybillmonth(String zwybillmonth) {
        this.zwybillmonth = zwybillmonth == null ? null : zwybillmonth.trim();
    }

    public String getZwylpno() {
        return zwylpno;
    }

    public void setZwylpno(String zwylpno) {
        this.zwylpno = zwylpno == null ? null : zwylpno.trim();
    }



    public String getZwyfeename() {
        return zwyfeename;
    }

    public void setZwyfeename(String zwyfeename) {
        this.zwyfeename = zwyfeename == null ? null : zwyfeename.trim();
    }

    public String getZwycurrency() {
        return zwycurrency;
    }

    public void setZwycurrency(String zwycurrency) {
        this.zwycurrency = zwycurrency == null ? null : zwycurrency.trim();
    }



    public String getZwycomment() {
        return zwycomment;
    }

    public void setZwycomment(String zwycomment) {
        this.zwycomment = zwycomment == null ? null : zwycomment.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public String getEntityValue() {
        return entityValue;
    }

    public void setEntityValue(String entityValue) {
        this.entityValue = entityValue == null ? null : entityValue.trim();
    }

    public String getServiceItemCode() {
        return serviceItemCode;
    }

    public void setServiceItemCode(String serviceItemCode) {
        this.serviceItemCode = serviceItemCode == null ? null : serviceItemCode.trim();
    }

    public String getServiceItemName() {
        return serviceItemName;
    }

    public void setServiceItemName(String serviceItemName) {
        this.serviceItemName = serviceItemName == null ? null : serviceItemName.trim();
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId == null ? null : userId.trim();
    }

    public String getMaxCreateTime() {
        return maxCreateTime;
    }

    public void setMaxCreateTime(String maxCreateTime) {
        this.maxCreateTime = maxCreateTime == null ? null : maxCreateTime.trim();
    }

    public String getMinCreateTime() {
        return minCreateTime;
    }

    public void setMinCreateTime(String minCreateTime) {
        this.minCreateTime = minCreateTime == null ? null : minCreateTime.trim();
    }

    public String getUserNick() {
        return userNick;
    }

    public void setUserNick(String userNick) {
        this.userNick = userNick == null ? null : userNick.trim();
    }


    public String getFeeName() {
        return feeName;
    }

    public void setFeeName(String feeName) {
        this.feeName = feeName == null ? null : feeName.trim();
    }

    public String getIsPaySuccess() {
        return isPaySuccess;
    }

    public void setIsPaySuccess(String isPaySuccess) {
        this.isPaySuccess = isPaySuccess == null ? null : isPaySuccess.trim();
    }

    public String getPayCurrency() {
        return payCurrency;
    }

    public void setPayCurrency(String payCurrency) {
        this.payCurrency = payCurrency == null ? null : payCurrency.trim();
    }

    public String getIstype() {
        return istype;
    }

    public void setIstype(String istype) {
        this.istype = istype == null ? null : istype.trim();
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type == null ? null : type.trim();
    }

    public String getFileBatch() {
        return fileBatch;
    }

    public void setFileBatch(String fileBatch) {
        this.fileBatch = fileBatch == null ? null : fileBatch.trim();
    }

    public BigDecimal getZwyamount() {
        return zwyamount;
    }

    public void setZwyamount(BigDecimal zwyamount) {
        this.zwyamount = zwyamount;
    }

    public BigDecimal getWeightSum() {
        return weightSum;
    }

    public void setWeightSum(BigDecimal weightSum) {
        this.weightSum = weightSum;
    }

    public BigDecimal getChargeAmount() {
        return chargeAmount;
    }

    public void setChargeAmount(BigDecimal chargeAmount) {
        this.chargeAmount = chargeAmount;
    }

    public BigDecimal getZwyweightkg() {
        return zwyweightkg;
    }

    public void setZwyweightkg(BigDecimal zwyweightkg) {
        this.zwyweightkg = zwyweightkg;
    }
}