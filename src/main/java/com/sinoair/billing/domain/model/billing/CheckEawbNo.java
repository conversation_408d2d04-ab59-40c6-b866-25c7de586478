package com.sinoair.billing.domain.model.billing;

import java.math.BigDecimal;
import java.util.Date;

public class CheckEawbNo {
    private Short checkId;

    private String checkType;

    private BigDecimal fromNo;

    private BigDecimal beginNo;

    private BigDecimal endNo;

    private Date handleDate;

    private String remarks;

    private Long handleSize;

    public Short getCheckId() {
        return checkId;
    }

    public void setCheckId(Short checkId) {
        this.checkId = checkId;
    }

    public String getCheckType() {
        return checkType;
    }

    public void setCheckType(String checkType) {
        this.checkType = checkType == null ? null : checkType.trim();
    }

    public BigDecimal getFromNo() {
        return fromNo;
    }

    public void setFromNo(BigDecimal fromNo) {
        this.fromNo = fromNo;
    }

    public BigDecimal getBeginNo() {
        return beginNo;
    }

    public void setBeginNo(BigDecimal beginNo) {
        this.beginNo = beginNo;
    }

    public BigDecimal getEndNo() {
        return endNo;
    }

    public void setEndNo(BigDecimal endNo) {
        this.endNo = endNo;
    }

    public Date getHandleDate() {
        return handleDate;
    }

    public void setHandleDate(Date handleDate) {
        this.handleDate = handleDate;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public Long getHandleSize() {
        return handleSize;
    }

    public void setHandleSize(Long handleSize) {
        this.handleSize = handleSize;
    }
}