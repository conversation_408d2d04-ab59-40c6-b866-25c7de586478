package com.sinoair.billing.domain.model.billing;

import java.math.BigDecimal;
import java.util.Date;

public class CheckEawbResult {
    private String eawbPrintcode;

    private BigDecimal eawbSyscodeCeos;

    private Date eawbKeyentrytime;

    private String codeStatus;

    private String activityStatus;

    private String rrStatus;

    private String resultStatus;

    private String activityStatusCeos;

    private Date checkHandletime;

    private int eawbSoType;

    private Long checkDate;

    public String getEawbPrintcode() {
        return eawbPrintcode;
    }

    public void setEawbPrintcode(String eawbPrintcode) {
        this.eawbPrintcode = eawbPrintcode == null ? null : eawbPrintcode.trim();
    }

    public BigDecimal getEawbSyscodeCeos() {
        return eawbSyscodeCeos;
    }

    public void setEawbSyscodeCeos(BigDecimal eawbSyscodeCeos) {
        this.eawbSyscodeCeos = eawbSyscodeCeos;
    }

    public Date getEawbKeyentrytime() {
        return eawbKeyentrytime;
    }

    public void setEawbKeyentrytime(Date eawbKeyentrytime) {
        this.eawbKeyentrytime = eawbKeyentrytime;
    }

    public String getCodeStatus() {
        return codeStatus;
    }

    public void setCodeStatus(String codeStatus) {
        this.codeStatus = codeStatus == null ? null : codeStatus.trim();
    }

    public String getActivityStatus() {
        return activityStatus;
    }

    public void setActivityStatus(String activityStatus) {
        this.activityStatus = activityStatus == null ? null : activityStatus.trim();
    }

    public String getRrStatus() {
        return rrStatus;
    }

    public void setRrStatus(String rrStatus) {
        this.rrStatus = rrStatus == null ? null : rrStatus.trim();
    }

    public String getResultStatus() {
        return resultStatus;
    }

    public void setResultStatus(String resultStatus) {
        this.resultStatus = resultStatus == null ? null : resultStatus.trim();
    }

    public String getActivityStatusCeos() {
        return activityStatusCeos;
    }

    public void setActivityStatusCeos(String activityStatusCeos) {
        this.activityStatusCeos = activityStatusCeos;
    }

    public Date getCheckHandletime() {
        return checkHandletime;
    }

    public void setCheckHandletime(Date checkHandletime) {
        this.checkHandletime = checkHandletime;
    }

    public int getEawbSoType() {
        return eawbSoType;
    }

    public void setEawbSoType(int eawbSoType) {
        this.eawbSoType = eawbSoType;
    }

    public Long getCheckDate() {
        return checkDate;
    }

    public void setCheckDate(Long checkDate) {
        this.checkDate = checkDate;
    }
}