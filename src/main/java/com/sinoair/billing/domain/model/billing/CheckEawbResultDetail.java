package com.sinoair.billing.domain.model.billing;

import java.util.Date;

public class CheckEawbResultDetail {
    private Long cerdSyscode;

    private String eawbPrintcode;

    private String soCode;

    private String prName;

    private String eadCode;

    private String eastCode;

    private String servicetype;

    private String occCompanyId;

    private String activityStatusCeos;

    private String activityStatus;

    private String rrStatus;

    private String billStatus;

    private Long checkDate;

    private Long checkMonth;

    private Long checkYear;

    private Date checkHandletime;

    private Integer prId;

    private Date ebaHandletime;

    private String prSpecialKey;

    private String resSpecialKey;

    //临时字段
    private String destSacId;

    private String mawbCode;

    public Long getCerdSyscode() {
        return cerdSyscode;
    }

    public void setCerdSyscode(Long cerdSyscode) {
        this.cerdSyscode = cerdSyscode;
    }

    public String getEawbPrintcode() {
        return eawbPrintcode;
    }

    public void setEawbPrintcode(String eawbPrintcode) {
        this.eawbPrintcode = eawbPrintcode == null ? null : eawbPrintcode.trim();
    }

    public String getSoCode() {
        return soCode;
    }

    public void setSoCode(String soCode) {
        this.soCode = soCode == null ? null : soCode.trim();
    }

    public String getPrName() {
        return prName;
    }

    public void setPrName(String prName) {
        this.prName = prName == null ? null : prName.trim();
    }

    public String getEadCode() {
        return eadCode;
    }

    public void setEadCode(String eadCode) {
        this.eadCode = eadCode == null ? null : eadCode.trim();
    }

    public String getEastCode() {
        return eastCode;
    }

    public void setEastCode(String eastCode) {
        this.eastCode = eastCode == null ? null : eastCode.trim();
    }

    public String getServicetype() {
        return servicetype;
    }

    public void setServicetype(String servicetype) {
        this.servicetype = servicetype == null ? null : servicetype.trim();
    }

    public String getOccCompanyId() {
        return occCompanyId;
    }

    public void setOccCompanyId(String occCompanyId) {
        this.occCompanyId = occCompanyId == null ? null : occCompanyId.trim();
    }

    public String getActivityStatusCeos() {
        return activityStatusCeos;
    }

    public void setActivityStatusCeos(String activityStatusCeos) {
        this.activityStatusCeos = activityStatusCeos == null ? null : activityStatusCeos.trim();
    }

    public String getActivityStatus() {
        return activityStatus;
    }

    public void setActivityStatus(String activityStatus) {
        this.activityStatus = activityStatus == null ? null : activityStatus.trim();
    }

    public String getRrStatus() {
        return rrStatus;
    }

    public void setRrStatus(String rrStatus) {
        this.rrStatus = rrStatus == null ? null : rrStatus.trim();
    }

    public String getBillStatus() {
        return billStatus;
    }

    public void setBillStatus(String billStatus) {
        this.billStatus = billStatus == null ? null : billStatus.trim();
    }

    public Long getCheckDate() {
        return checkDate;
    }

    public void setCheckDate(Long checkDate) {
        this.checkDate = checkDate;
    }

    public Long getCheckMonth() {
        return checkMonth;
    }

    public void setCheckMonth(Long checkMonth) {
        this.checkMonth = checkMonth;
    }

    public Long getCheckYear() {
        return checkYear;
    }

    public void setCheckYear(Long checkYear) {
        this.checkYear = checkYear;
    }

    public Date getCheckHandletime() {
        return checkHandletime;
    }

    public void setCheckHandletime(Date checkHandletime) {
        this.checkHandletime = checkHandletime;
    }

    public Integer getPrId() {
        return prId;
    }

    public void setPrId(Integer prId) {
        this.prId = prId;
    }

    public Date getEbaHandletime() {
        return ebaHandletime;
    }

    public void setEbaHandletime(Date ebaHandletime) {
        this.ebaHandletime = ebaHandletime;
    }

    public String getPrSpecialKey() {
        return prSpecialKey;
    }

    public void setPrSpecialKey(String prSpecialKey) {
        this.prSpecialKey = prSpecialKey;
    }

    public String getResSpecialKey() {
        return resSpecialKey;
    }

    public void setResSpecialKey(String resSpecialKey) {
        this.resSpecialKey = resSpecialKey;
    }

    public String getDestSacId() {
        return destSacId;
    }

    public void setDestSacId(String destSacId) {
        this.destSacId = destSacId;
    }

    public String getMawbCode() {
        return mawbCode;
    }

    public void setMawbCode(String mawbCode) {
        this.mawbCode = mawbCode;
    }
}