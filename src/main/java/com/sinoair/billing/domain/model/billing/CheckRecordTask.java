package com.sinoair.billing.domain.model.billing;

import java.math.BigDecimal;
import java.util.Date;

public class CheckRecordTask {
    private BigDecimal crtId;

    private String taskBatch;

    private String taskModule;

    private String taskType;

    private String taskName;

    private String taskStatus;

    private BigDecimal queryCount;

    private Long handleCount;

    private Date taskCreatetime;

    private Date taskHandletime;

    private String taskParam;

    public BigDecimal getCrtId() {
        return crtId;
    }

    public void setCrtId(BigDecimal crtId) {
        this.crtId = crtId;
    }

    public String getTaskBatch() {
        return taskBatch;
    }

    public void setTaskBatch(String taskBatch) {
        this.taskBatch = taskBatch == null ? null : taskBatch.trim();
    }

    public String getTaskModule() {
        return taskModule;
    }

    public void setTaskModule(String taskModule) {
        this.taskModule = taskModule == null ? null : taskModule.trim();
    }

    public String getTaskType() {
        return taskType;
    }

    public void setTaskType(String taskType) {
        this.taskType = taskType == null ? null : taskType.trim();
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName == null ? null : taskName.trim();
    }

    public String getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(String taskStatus) {
        this.taskStatus = taskStatus == null ? null : taskStatus.trim();
    }

    public BigDecimal getQueryCount() {
        return queryCount;
    }

    public void setQueryCount(BigDecimal queryCount) {
        this.queryCount = queryCount;
    }

    public Long getHandleCount() {
        return handleCount;
    }

    public void setHandleCount(Long handleCount) {
        this.handleCount = handleCount;
    }

    public Date getTaskCreatetime() {
        return taskCreatetime;
    }

    public void setTaskCreatetime(Date taskCreatetime) {
        this.taskCreatetime = taskCreatetime;
    }

    public Date getTaskHandletime() {
        return taskHandletime;
    }

    public void setTaskHandletime(Date taskHandletime) {
        this.taskHandletime = taskHandletime;
    }

    public String getTaskParam() {
        return taskParam;
    }

    public void setTaskParam(String taskParam) {
        this.taskParam = taskParam;
    }
}