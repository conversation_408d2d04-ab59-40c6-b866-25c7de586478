package com.sinoair.billing.domain.model.billing;

import java.util.Date;

public class CheckSummaryResult {
    private Long checkDate;

    private Long allNum;

    private Long codeNum;

    private Long activityNum;

    private Long rrNum;

    private Long resultNum;

    private Long checkMonth;

    private Long checkYear;

    private Date keyDate;

    private Long activityNumCeos;

    private Long cnNum;

    private Long otherNum;

    public Long getCheckDate() {
        return checkDate;
    }

    public void setCheckDate(Long checkDate) {
        this.checkDate = checkDate;
    }

    public Long getAllNum() {
        return allNum;
    }

    public void setAllNum(Long allNum) {
        this.allNum = allNum;
    }

    public Long getCodeNum() {
        return codeNum;
    }

    public void setCodeNum(Long codeNum) {
        this.codeNum = codeNum;
    }

    public Long getActivityNum() {
        return activityNum;
    }

    public void setActivityNum(Long activityNum) {
        this.activityNum = activityNum;
    }

    public Long getRrNum() {
        return rrNum;
    }

    public void setRrNum(Long rrNum) {
        this.rrNum = rrNum;
    }

    public Long getResultNum() {
        return resultNum;
    }

    public void setResultNum(Long resultNum) {
        this.resultNum = resultNum;
    }

    public Long getCheckMonth() {
        return checkMonth;
    }

    public void setCheckMonth(Long checkMonth) {
        this.checkMonth = checkMonth;
    }

    public Long getCheckYear() {
        return checkYear;
    }

    public void setCheckYear(Long checkYear) {
        this.checkYear = checkYear;
    }

    public Date getKeyDate() {
        return keyDate;
    }

    public void setKeyDate(Date keyDate) {
        this.keyDate = keyDate;
    }

    public Long getActivityNumCeos() {
        return activityNumCeos;
    }

    public void setActivityNumCeos(Long activityNumCeos) {
        this.activityNumCeos = activityNumCeos;
    }

    public Long getCnNum() {
        return cnNum;
    }

    public void setCnNum(Long cnNum) {
        this.cnNum = cnNum;
    }

    public Long getOtherNum() {
        return otherNum;
    }

    public void setOtherNum(Long otherNum) {
        this.otherNum = otherNum;
    }
}