package com.sinoair.billing.domain.model.billing;

import java.util.Date;
import java.util.List;

public class Company {
    private String companyId;

    private String companyName;

    private String companyCode;

    private String companyType;

    private Short timeInterval;

    private String orgId;

    private String companyStatus;

    private Date createTime;

    private Integer createUserId;

    private String companyAddress;

    private String companyPhone;

    private String companyPId;

    private List<Company> childrenCompanyList;

    public List<Company> getChildrenCompanyList() {
        return childrenCompanyList;
    }

    public void setChildrenCompanyList(List<Company> childrenCompanyList) {
        this.childrenCompanyList = childrenCompanyList;
    }

    public void addChildrenCompanyIntoList(Company company) {

        this.childrenCompanyList.add(company);
    }

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName == null ? null : companyName.trim();
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode == null ? null : companyCode.trim();
    }

    public String getCompanyType() {
        return companyType;
    }

    public void setCompanyType(String companyType) {
        this.companyType = companyType == null ? null : companyType.trim();
    }

    public Short getTimeInterval() {
        return timeInterval;
    }

    public void setTimeInterval(Short timeInterval) {
        this.timeInterval = timeInterval;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId == null ? null : orgId.trim();
    }

    public String getCompanyStatus() {
        return companyStatus;
    }

    public void setCompanyStatus(String companyStatus) {
        this.companyStatus = companyStatus == null ? null : companyStatus.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Integer createUserId) {
        this.createUserId = createUserId;
    }

    public String getCompanyAddress() {
        return companyAddress;
    }

    public void setCompanyAddress(String companyAddress) {
        this.companyAddress = companyAddress == null ? null : companyAddress.trim();
    }

    public String getCompanyPhone() {
        return companyPhone;
    }

    public void setCompanyPhone(String companyPhone) {
        this.companyPhone = companyPhone == null ? null : companyPhone.trim();
    }

    public String getCompanyPId() {
        return companyPId;
    }

    public void setCompanyPId(String companyPId) {
        this.companyPId = companyPId;
    }

    // TODO: Simon 2016/7/19 针对Company与数据库表字段的不一只，添加此字段来调整
    public String getSacId() {
        return this.getCompanyCode();
    }

    public void setSacId(String sacId) {
        this.setCompanyCode(sacId);
    }

}