package com.sinoair.billing.domain.model.billing;

import java.math.BigDecimal;

public class CorreosCollect {
    private String debitNo;

    private String descriptions;

    private BigDecimal units;

    private BigDecimal price;

    private BigDecimal grossAm;

    private BigDecimal discs;

    private BigDecimal amount;

    private String correosFile;

    private String fileMonth;

    private String services;

    public String getDebitNo() {
        return debitNo;
    }

    public void setDebitNo(String debitNo) {
        this.debitNo = debitNo == null ? null : debitNo.trim();
    }

    public String getDescriptions() {
        return descriptions;
    }

    public void setDescriptions(String descriptions) {
        this.descriptions = descriptions == null ? null : descriptions.trim();
    }

    public BigDecimal getUnits() {
        return units;
    }

    public void setUnits(BigDecimal units) {
        this.units = units;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getGrossAm() {
        return grossAm;
    }

    public void setGrossAm(BigDecimal grossAm) {
        this.grossAm = grossAm;
    }

    public BigDecimal getDiscs() {
        return discs;
    }

    public void setDiscs(BigDecimal discs) {
        this.discs = discs;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getCorreosFile() {
        return correosFile;
    }

    public void setCorreosFile(String correosFile) {
        this.correosFile = correosFile == null ? null : correosFile.trim();
    }

    public String getFileMonth() {
        return fileMonth;
    }

    public void setFileMonth(String fileMonth) {
        this.fileMonth = fileMonth;
    }

    public String getServices() {
        return services;
    }

    public void setServices(String services) {
        this.services = services;
    }
}