package com.sinoair.billing.domain.model.billing;

import java.math.BigDecimal;

public class CorreosDetail {
    private Long debitNo;

    private String shptNo;

    private String scton;

    private BigDecimal grossAmt;

    private String cdName;

    private String cdMonth;

    private String correosAdm;

    private String correosFile;

    private String descriptions;

    private String fileMonth;

    public Long getDebitNo() {
        return debitNo;
    }

    public void setDebitNo(Long debitNo) {
        this.debitNo = debitNo;
    }

    public String getShptNo() {
        return shptNo;
    }

    public void setShptNo(String shptNo) {
        this.shptNo = shptNo == null ? null : shptNo.trim();
    }

    public String getScton() {
        return scton;
    }

    public void setScton(String scton) {
        this.scton = scton == null ? null : scton.trim();
    }

    public BigDecimal getGrossAmt() {
        return grossAmt;
    }

    public void setGrossAmt(BigDecimal grossAmt) {
        this.grossAmt = grossAmt;
    }

    public String getCdName() {
        return cdName;
    }

    public void setCdName(String cdName) {
        this.cdName = cdName == null ? null : cdName.trim();
    }

    public String getCdMonth() {
        return cdMonth;
    }

    public void setCdMonth(String cdMonth) {
        this.cdMonth = cdMonth == null ? null : cdMonth.trim();
    }

    public String getCorreosAdm() {
        return correosAdm;
    }

    public void setCorreosAdm(String correosAdm) {
        this.correosAdm = correosAdm == null ? null : correosAdm.trim();
    }

    public String getCorreosFile() {
        return correosFile;
    }

    public void setCorreosFile(String correosFile) {
        this.correosFile = correosFile == null ? null : correosFile.trim();
    }

    public String getDescriptions() {
        return descriptions;
    }

    public void setDescriptions(String descriptions) {
        this.descriptions = descriptions;
    }

    public String getFileMonth() {
        return fileMonth;
    }

    public void setFileMonth(String fileMonth) {
        this.fileMonth = fileMonth;
    }
}