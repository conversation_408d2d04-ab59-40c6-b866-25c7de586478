package com.sinoair.billing.domain.model.billing;

import java.math.BigDecimal;
import java.util.Date;

public class CorreosLog {
    private String correosFile;

    private Integer detailCount;

    private Integer noCount;

    private Integer collectCount;

    private Date createTime;

    private String fileMonth;

    private String isManifest;

    private Integer manifestCount;
    private BigDecimal manifestAmount;
    private BigDecimal manifestWeight;

    public String getCorreosFile() {
        return correosFile;
    }

    public void setCorreosFile(String correosFile) {
        this.correosFile = correosFile == null ? null : correosFile.trim();
    }

    public Integer getDetailCount() {
        return detailCount;
    }

    public void setDetailCount(Integer detailCount) {
        this.detailCount = detailCount;
    }

    public Integer getNoCount() {
        return noCount;
    }

    public void setNoCount(Integer noCount) {
        this.noCount = noCount;
    }

    public Integer getCollectCount() {
        return collectCount;
    }

    public void setCollectCount(Integer collectCount) {
        this.collectCount = collectCount;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getFileMonth() {
        return fileMonth;
    }

    public void setFileMonth(String fileMonth) {
        this.fileMonth = fileMonth;
    }

    public String getIsManifest() {
        return isManifest;
    }

    public void setIsManifest(String isManifest) {
        this.isManifest = isManifest;
    }

    public Integer getManifestCount() {
        return manifestCount;
    }

    public void setManifestCount(Integer manifestCount) {
        this.manifestCount = manifestCount;
    }

    public BigDecimal getManifestAmount() {
        return manifestAmount;
    }

    public void setManifestAmount(BigDecimal manifestAmount) {
        this.manifestAmount = manifestAmount;
    }

    public BigDecimal getManifestWeight() {
        return manifestWeight;
    }

    public void setManifestWeight(BigDecimal manifestWeight) {
        this.manifestWeight = manifestWeight;
    }
}