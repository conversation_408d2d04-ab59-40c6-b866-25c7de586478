package com.sinoair.billing.domain.model.billing;

import java.util.Date;

public class CorreosManifest {
    private String fileMonth;

    private Integer cmId;

    private String correosStatus;

    private Integer correosAccount;

    private Integer handleAccount;

    private Date createTime;

    private Date handleTime;

    private String correosFile;

    public String getFileMonth() {
        return fileMonth;
    }

    public void setFileMonth(String fileMonth) {
        this.fileMonth = fileMonth == null ? null : fileMonth.trim();
    }

    public Integer getCmId() {
        return cmId;
    }

    public void setCmId(Integer cmId) {
        this.cmId = cmId;
    }

    public String getCorreosStatus() {
        return correosStatus;
    }

    public void setCorreosStatus(String correosStatus) {
        this.correosStatus = correosStatus == null ? null : correosStatus.trim();
    }

    public Integer getCorreosAccount() {
        return correosAccount;
    }

    public void setCorreosAccount(Integer correosAccount) {
        this.correosAccount = correosAccount;
    }

    public Integer getHandleAccount() {
        return handleAccount;
    }

    public void setHandleAccount(Integer handleAccount) {
        this.handleAccount = handleAccount;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getHandleTime() {
        return handleTime;
    }

    public void setHandleTime(Date handleTime) {
        this.handleTime = handleTime;
    }

    public String getCorreosFile() {
        return correosFile;
    }

    public void setCorreosFile(String correosFile) {
        this.correosFile = correosFile;
    }
}