package com.sinoair.billing.domain.model.billing;

public class CorreosNo {
    private Long delivsNo;

    private String shptNo;

    private String detalleBultos;

    private String cnName;

    private String cnMonth;

    private String debitNo;

    private String correosFile;

    private String correosAdm;

    private String fileMonth;

    public Long getDelivsNo() {
        return delivsNo;
    }

    public void setDelivsNo(Long delivsNo) {
        this.delivsNo = delivsNo;
    }

    public String getShptNo() {
        return shptNo;
    }

    public void setShptNo(String shptNo) {
        this.shptNo = shptNo == null ? null : shptNo.trim();
    }

    public String getDetalleBultos() {
        return detalleBultos;
    }

    public void setDetalleBultos(String detalleBultos) {
        this.detalleBultos = detalleBultos == null ? null : detalleBultos.trim();
    }

    public String getCnName() {
        return cnName;
    }

    public void setCnName(String cnName) {
        this.cnName = cnName == null ? null : cnName.trim();
    }

    public String getCnMonth() {
        return cnMonth;
    }

    public void setCnMonth(String cnMonth) {
        this.cnMonth = cnMonth == null ? null : cnMonth.trim();
    }

    public String getDebitNo() {
        return debitNo;
    }

    public void setDebitNo(String debitNo) {
        this.debitNo = debitNo == null ? null : debitNo.trim();
    }

    public String getCorreosFile() {
        return correosFile;
    }

    public void setCorreosFile(String correosFile) {
        this.correosFile = correosFile == null ? null : correosFile.trim();
    }

    public String getCorreosAdm() {
        return correosAdm;
    }

    public void setCorreosAdm(String correosAdm) {
        this.correosAdm = correosAdm == null ? null : correosAdm.trim();
    }

    public String getFileMonth() {
        return fileMonth;
    }

    public void setFileMonth(String fileMonth) {
        this.fileMonth = fileMonth;
    }
}