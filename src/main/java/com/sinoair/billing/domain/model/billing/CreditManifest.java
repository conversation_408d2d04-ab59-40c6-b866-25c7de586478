package com.sinoair.billing.domain.model.billing;

import java.math.BigDecimal;
import java.util.Date;

public class CreditManifest {
    private Integer cmId;

    private String companyId;

    private String soCode;

    private String ctCode;

    private BigDecimal cmCurrencyrate;

    private BigDecimal cmTotalrmb;

    private BigDecimal cmTotalfc;

    private BigDecimal soTax;

    private BigDecimal taxAmount;

    private BigDecimal notaxAmount;

    private BigDecimal taxAmountFc;

    private BigDecimal notaxAmountFc;

    private String invoiceCode;

    private Integer cmUserId;

    private Date cmCreateTime;

    private Date cmHandleTime;

    private String cmStatus;

    private String cmDirty;

    private Date cmDirtyTime;

    private String cmCode;

    private BigDecimal cmPlanAmount;

    private Date cmStartTime;

    private Date cmEndTime;

    private Integer cmTotalPieces;

    private BigDecimal cmTotalWeight;

    private String cmRemark;

    private String cmType;

    public Integer getCmId() {
        return cmId;
    }

    public void setCmId(Integer cmId) {
        this.cmId = cmId;
    }

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId == null ? null : companyId.trim();
    }

    public String getSoCode() {
        return soCode;
    }

    public void setSoCode(String soCode) {
        this.soCode = soCode == null ? null : soCode.trim();
    }

    public String getCtCode() {
        return ctCode;
    }

    public void setCtCode(String ctCode) {
        this.ctCode = ctCode == null ? null : ctCode.trim();
    }

    public BigDecimal getCmCurrencyrate() {
        return cmCurrencyrate;
    }

    public void setCmCurrencyrate(BigDecimal cmCurrencyrate) {
        this.cmCurrencyrate = cmCurrencyrate;
    }

    public BigDecimal getCmTotalrmb() {
        return cmTotalrmb;
    }

    public void setCmTotalrmb(BigDecimal cmTotalrmb) {
        this.cmTotalrmb = cmTotalrmb;
    }

    public BigDecimal getCmTotalfc() {
        return cmTotalfc;
    }

    public void setCmTotalfc(BigDecimal cmTotalfc) {
        this.cmTotalfc = cmTotalfc;
    }

    public BigDecimal getSoTax() {
        return soTax;
    }

    public void setSoTax(BigDecimal soTax) {
        this.soTax = soTax;
    }

    public BigDecimal getTaxAmount() {
        return taxAmount;
    }

    public void setTaxAmount(BigDecimal taxAmount) {
        this.taxAmount = taxAmount;
    }

    public BigDecimal getNotaxAmount() {
        return notaxAmount;
    }

    public void setNotaxAmount(BigDecimal notaxAmount) {
        this.notaxAmount = notaxAmount;
    }

    public BigDecimal getTaxAmountFc() {
        return taxAmountFc;
    }

    public void setTaxAmountFc(BigDecimal taxAmountFc) {
        this.taxAmountFc = taxAmountFc;
    }

    public BigDecimal getNotaxAmountFc() {
        return notaxAmountFc;
    }

    public void setNotaxAmountFc(BigDecimal notaxAmountFc) {
        this.notaxAmountFc = notaxAmountFc;
    }

    public String getInvoiceCode() {
        return invoiceCode;
    }

    public void setInvoiceCode(String invoiceCode) {
        this.invoiceCode = invoiceCode == null ? null : invoiceCode.trim();
    }

    public Integer getCmUserId() {
        return cmUserId;
    }

    public void setCmUserId(Integer cmUserId) {
        this.cmUserId = cmUserId;
    }

    public Date getCmCreateTime() {
        return cmCreateTime;
    }

    public void setCmCreateTime(Date cmCreateTime) {
        this.cmCreateTime = cmCreateTime;
    }

    public Date getCmHandleTime() {
        return cmHandleTime;
    }

    public void setCmHandleTime(Date cmHandleTime) {
        this.cmHandleTime = cmHandleTime;
    }

    public String getCmStatus() {
        return cmStatus;
    }

    public void setCmStatus(String cmStatus) {
        this.cmStatus = cmStatus == null ? null : cmStatus.trim();
    }

    public String getCmDirty() {
        return cmDirty;
    }

    public void setCmDirty(String cmDirty) {
        this.cmDirty = cmDirty == null ? null : cmDirty.trim();
    }

    public Date getCmDirtyTime() {
        return cmDirtyTime;
    }

    public void setCmDirtyTime(Date cmDirtyTime) {
        this.cmDirtyTime = cmDirtyTime;
    }

    public String getCmCode() {
        return cmCode;
    }

    public void setCmCode(String cmCode) {
        this.cmCode = cmCode == null ? null : cmCode.trim();
    }

    public BigDecimal getCmPlanAmount() {
        return cmPlanAmount;
    }

    public void setCmPlanAmount(BigDecimal cmPlanAmount) {
        this.cmPlanAmount = cmPlanAmount;
    }

    public Date getCmStartTime() {
        return cmStartTime;
    }

    public void setCmStartTime(Date cmStartTime) {
        this.cmStartTime = cmStartTime;
    }

    public Date getCmEndTime() {
        return cmEndTime;
    }

    public void setCmEndTime(Date cmEndTime) {
        this.cmEndTime = cmEndTime;
    }

    public Integer getCmTotalPieces() {
        return cmTotalPieces;
    }

    public void setCmTotalPieces(Integer cmTotalPieces) {
        this.cmTotalPieces = cmTotalPieces;
    }

    public BigDecimal getCmTotalWeight() {
        return cmTotalWeight;
    }

    public void setCmTotalWeight(BigDecimal cmTotalWeight) {
        this.cmTotalWeight = cmTotalWeight;
    }

    public String getCmRemark() {
        return cmRemark;
    }

    public void setCmRemark(String cmRemark) {
        this.cmRemark = cmRemark == null ? null : cmRemark.trim();
    }

    public String getCmType() {
        return cmType;
    }

    public void setCmType(String cmType) {
        this.cmType = cmType == null ? null : cmType.trim();
    }
}