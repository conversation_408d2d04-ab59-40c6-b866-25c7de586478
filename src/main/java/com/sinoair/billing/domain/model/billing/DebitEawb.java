package com.sinoair.billing.domain.model.billing;


import java.math.BigDecimal;
import java.util.Date;

public class DebitEawb {
    private Long dmId;

    private String eawbPrintcode;

    private BigDecimal rrActualAmount;

    private String epKey;

    private String sinotransId;

    //查询参数
    private Date dmStartTime;

    private Date dmEndTime;

    public Long getDmId() {
        return dmId;
    }

    public void setDmId(Long dmId) {
        this.dmId = dmId;
    }

    public String getEawbPrintcode() {
        return eawbPrintcode;
    }

    public void setEawbPrintcode(String eawbPrintcode) {
        this.eawbPrintcode = eawbPrintcode;
    }

    public BigDecimal getRrActualAmount() {
        return rrActualAmount;
    }

    public void setRrActualAmount(BigDecimal rrActualAmount) {
        this.rrActualAmount = rrActualAmount;
    }

    public String getEpKey() {
        return epKey;
    }

    public void setEpKey(String epKey) {
        this.epKey = epKey;
    }

    public String getSinotransId() {
        return sinotransId;
    }

    public void setSinotransId(String sinotransId) {
        this.sinotransId = sinotransId;
    }

    public Date getDmStartTime() {
        return dmStartTime;
    }

    public void setDmStartTime(Date dmStartTime) {
        this.dmStartTime = dmStartTime;
    }

    public Date getDmEndTime() {
        return dmEndTime;
    }

    public void setDmEndTime(Date dmEndTime) {
        this.dmEndTime = dmEndTime;
    }
}