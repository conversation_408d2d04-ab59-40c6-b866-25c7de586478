package com.sinoair.billing.domain.model.billing;


import java.math.BigDecimal;
import java.util.Date;

public class DebitManifest{
    private Integer dmId;

    private String companyId;

    private String soCode;

    private String ctCode;

    private BigDecimal dmCurrencyrate;

    private BigDecimal dmTotalrmb;

    private BigDecimal dmTotalfc;

    private BigDecimal soTax;

    private BigDecimal taxAmount;

    private BigDecimal notaxAmount;

    private BigDecimal taxAmountFc;

    private BigDecimal notaxAmountFc;

    private String invoiceCode;

    private Integer dmUserId;

    private String dmStatus;

    private Date dmCreateTime;

    private Date dmHandleTime;

    private String dmDirty;

    private Date dmDirtyTime;

    private String dmCode;

    private BigDecimal dmPlanAmount;

    private Date dmStartTime;

    private Date dmEndTime;

    private Integer dmTotalPieces;

    private BigDecimal dmTotalWeight;

    private String dmRemark;

    private String dmType;

    private String costCtCode;

    private String costCurrencyrate;

    private String dmDivideStatus;

    private String dmEmailStatus;

    private String eawbServicetypeOriginal;

    private String soMode;

    private String eawbPrintcode;

    private String eawbReference1;

    public String getEawbPrintcode() {
        return eawbPrintcode;
    }

    public void setEawbPrintcode(String eawbPrintcode) {
        this.eawbPrintcode = eawbPrintcode;
    }

    public String getEawbReference1() {
        return eawbReference1;
    }

    public void setEawbReference1(String eawbReference1) {
        this.eawbReference1 = eawbReference1;
    }

    public Integer getDmId() {
        return dmId;
    }

    public void setDmId(Integer dmId) {
        this.dmId = dmId;
    }

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId == null ? null : companyId.trim();
    }

    public String getSoCode() {
        return soCode;
    }

    public void setSoCode(String soCode) {
        this.soCode = soCode == null ? null : soCode.trim();
    }

    public String getCtCode() {
        return ctCode;
    }

    public void setCtCode(String ctCode) {
        this.ctCode = ctCode == null ? null : ctCode.trim();
    }

    public BigDecimal getDmCurrencyrate() {
        return dmCurrencyrate;
    }

    public void setDmCurrencyrate(BigDecimal dmCurrencyrate) {
        this.dmCurrencyrate = dmCurrencyrate;
    }

    public BigDecimal getDmTotalrmb() {
        return dmTotalrmb;
    }

    public void setDmTotalrmb(BigDecimal dmTotalrmb) {
        this.dmTotalrmb = dmTotalrmb;
    }

    public BigDecimal getDmTotalfc() {
        return dmTotalfc;
    }

    public void setDmTotalfc(BigDecimal dmTotalfc) {
        this.dmTotalfc = dmTotalfc;
    }

    public BigDecimal getSoTax() {
        return soTax;
    }

    public void setSoTax(BigDecimal soTax) {
        this.soTax = soTax;
    }

    public BigDecimal getTaxAmount() {
        return taxAmount;
    }

    public void setTaxAmount(BigDecimal taxAmount) {
        this.taxAmount = taxAmount;
    }

    public BigDecimal getNotaxAmount() {
        return notaxAmount;
    }

    public void setNotaxAmount(BigDecimal notaxAmount) {
        this.notaxAmount = notaxAmount;
    }

    public BigDecimal getTaxAmountFc() {
        return taxAmountFc;
    }

    public void setTaxAmountFc(BigDecimal taxAmountFc) {
        this.taxAmountFc = taxAmountFc;
    }

    public BigDecimal getNotaxAmountFc() {
        return notaxAmountFc;
    }

    public void setNotaxAmountFc(BigDecimal notaxAmountFc) {
        this.notaxAmountFc = notaxAmountFc;
    }

    public String getInvoiceCode() {
        return invoiceCode;
    }

    public void setInvoiceCode(String invoiceCode) {
        this.invoiceCode = invoiceCode == null ? null : invoiceCode.trim();
    }

    public Integer getDmUserId() {
        return dmUserId;
    }

    public void setDmUserId(Integer dmUserId) {
        this.dmUserId = dmUserId;
    }

    public String getDmStatus() {
        return dmStatus;
    }

    public void setDmStatus(String dmStatus) {
        this.dmStatus = dmStatus == null ? null : dmStatus.trim();
    }

    public Date getDmCreateTime(Date nowDateTime) {
        return dmCreateTime;
    }

    public void setDmCreateTime(Date dmCreateTime) {
        this.dmCreateTime = dmCreateTime;
    }

    public Date getDmHandleTime() {
        return dmHandleTime;
    }

    public void setDmHandleTime(Date dmHandleTime) {
        this.dmHandleTime = dmHandleTime;
    }

    public String getDmDirty() {
        return dmDirty;
    }

    public void setDmDirty(String dmDirty) {
        this.dmDirty = dmDirty == null ? null : dmDirty.trim();
    }

    public Date getDmDirtyTime() {
        return dmDirtyTime;
    }

    public void setDmDirtyTime(Date dmDirtyTime) {
        this.dmDirtyTime = dmDirtyTime;
    }

    public String getDmCode() {
        return dmCode;
    }

    public void setDmCode(String dmCode) {
        this.dmCode = dmCode == null ? null : dmCode.trim();
    }

    public BigDecimal getDmPlanAmount() {
        return dmPlanAmount;
    }

    public void setDmPlanAmount(BigDecimal dmPlanAmount) {
        this.dmPlanAmount = dmPlanAmount;
    }

    public Date getDmStartTime() {
        return dmStartTime;
    }

    public void setDmStartTime(Date dmStartTime) {
        this.dmStartTime = dmStartTime;
    }

    public Date getDmEndTime() {
        return dmEndTime;
    }

    public void setDmEndTime(Date dmEndTime) {
        this.dmEndTime = dmEndTime;
    }

    public Integer getDmTotalPieces() {
        return dmTotalPieces;
    }

    public void setDmTotalPieces(Integer dmTotalPieces) {
        this.dmTotalPieces = dmTotalPieces;
    }

    public BigDecimal getDmTotalWeight() {
        return dmTotalWeight;
    }

    public void setDmTotalWeight(BigDecimal dmTotalWeight) {
        this.dmTotalWeight = dmTotalWeight;
    }

    public String getDmRemark() {
        return dmRemark;
    }

    public void setDmRemark(String dmRemark) {
        this.dmRemark = dmRemark;
    }

    public String getDmType() {
        return dmType;
    }

    public void setDmType(String dmType) {
        this.dmType = dmType;
    }

    public Date getDmCreateTime() {
        return dmCreateTime;
    }

    public String getCostCurrencyrate() {
        return costCurrencyrate;
    }

    public void setCostCurrencyrate(String costCurrencyrate) {
        this.costCurrencyrate = costCurrencyrate;
    }

    public String getCostCtCode() {
        return costCtCode;
    }

    public void setCostCtCode(String costCtCode) {
        this.costCtCode = costCtCode;
    }

    public String getDmDivideStatus() {
        return dmDivideStatus;
    }

    public void setDmDivideStatus(String dmDivideStatus) {
        this.dmDivideStatus = dmDivideStatus == null ? null : dmDivideStatus.trim();
    }

    public String getDmEmailStatus() {
        return dmEmailStatus;
    }

    public void setDmEmailStatus(String dmEmailStatus) {
        this.dmEmailStatus = dmEmailStatus;
    }

    public String getEawbServicetypeOriginal() {
        return eawbServicetypeOriginal;
    }

    public void setEawbServicetypeOriginal(String eawbServicetypeOriginal) {
        this.eawbServicetypeOriginal = eawbServicetypeOriginal;
    }

    public String getSoMode() {
        return soMode;
    }

    public void setSoMode(String soMode) {
        this.soMode = soMode;
    }
}
