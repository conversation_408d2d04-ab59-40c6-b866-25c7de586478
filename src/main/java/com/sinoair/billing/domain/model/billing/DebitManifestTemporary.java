package com.sinoair.billing.domain.model.billing;

import java.math.BigDecimal;
import java.util.Date;

public class DebitManifestTemporary {
    private Integer dmTempId;

    private Integer dmId;

    private String companyId;

    private String soCode;

    private Integer pdSyscode;

    private String pdName;

    private String ctCode;

    private BigDecimal dmAmount;

    private String dmStatus;

    private Date dmCreateTime;

    private Date dmHandleTime;

    private Long dmDay;

    private Long dmOriDay;

    private Integer dmTotalPieces;

    private BigDecimal dmTotalWeight;

    private String dmRemark;

    private String dmIetype;

    private String isBalance;

    private String isDebiteawb;

    public Integer getDmTempId() {
        return dmTempId;
    }

    public void setDmTempId(Integer dmTempId) {
        this.dmTempId = dmTempId;
    }

    public Integer getDmId() {
        return dmId;
    }

    public void setDmId(Integer dmId) {
        this.dmId = dmId;
    }

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId == null ? null : companyId.trim();
    }

    public String getSoCode() {
        return soCode;
    }

    public void setSoCode(String soCode) {
        this.soCode = soCode == null ? null : soCode.trim();
    }

    public Integer getPdSyscode() {
        return pdSyscode;
    }

    public void setPdSyscode(Integer pdSyscode) {
        this.pdSyscode = pdSyscode;
    }

    public String getPdName() {
        return pdName;
    }

    public void setPdName(String pdName) {
        this.pdName = pdName == null ? null : pdName.trim();
    }

    public String getCtCode() {
        return ctCode;
    }

    public void setCtCode(String ctCode) {
        this.ctCode = ctCode == null ? null : ctCode.trim();
    }

    public BigDecimal getDmAmount() {
        return dmAmount;
    }

    public void setDmAmount(BigDecimal dmAmount) {
        this.dmAmount = dmAmount;
    }

    public String getDmStatus() {
        return dmStatus;
    }

    public void setDmStatus(String dmStatus) {
        this.dmStatus = dmStatus == null ? null : dmStatus.trim();
    }

    public Date getDmCreateTime() {
        return dmCreateTime;
    }

    public void setDmCreateTime(Date dmCreateTime) {
        this.dmCreateTime = dmCreateTime;
    }

    public Date getDmHandleTime() {
        return dmHandleTime;
    }

    public void setDmHandleTime(Date dmHandleTime) {
        this.dmHandleTime = dmHandleTime;
    }

    public Long getDmDay() {
        return dmDay;
    }

    public void setDmDay(Long dmDay) {
        this.dmDay = dmDay;
    }

    public Long getDmOriDay() {
        return dmOriDay;
    }

    public void setDmOriDay(Long dmOriDay) {
        this.dmOriDay = dmOriDay;
    }

    public Integer getDmTotalPieces() {
        return dmTotalPieces;
    }

    public void setDmTotalPieces(Integer dmTotalPieces) {
        this.dmTotalPieces = dmTotalPieces;
    }

    public BigDecimal getDmTotalWeight() {
        return dmTotalWeight;
    }

    public void setDmTotalWeight(BigDecimal dmTotalWeight) {
        this.dmTotalWeight = dmTotalWeight;
    }

    public String getDmRemark() {
        return dmRemark;
    }

    public void setDmRemark(String dmRemark) {
        this.dmRemark = dmRemark == null ? null : dmRemark.trim();
    }

    public String getDmIetype() {
        return dmIetype;
    }

    public void setDmIetype(String dmIetype) {
        this.dmIetype = dmIetype == null ? null : dmIetype.trim();
    }

    public String getIsBalance() {
        return isBalance;
    }

    public void setIsBalance(String isBalance) {
        this.isBalance = isBalance;
    }

    public String getIsDebiteawb() {
        return isDebiteawb;
    }

    public void setIsDebiteawb(String isDebiteawb) {
        this.isDebiteawb = isDebiteawb;
    }
}