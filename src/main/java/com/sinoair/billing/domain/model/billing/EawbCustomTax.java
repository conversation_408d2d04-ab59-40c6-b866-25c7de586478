package com.sinoair.billing.domain.model.billing;

import java.math.BigDecimal;
import java.util.Date;

public class EawbCustomTax {
    private Long ectSyscode;

    private String trackingNumber;

    private String logisticsordercode;

    private String ctCode;

    private String eawbCustcurrency;

    private BigDecimal eawbCustdeclval;

    private Date eawbKeyentrytime;

    private Long eawbcSyscode;

    private Long eawbciSyscode;

    private Long eawbcitSyscode;

    private String hscode;

    private String mawbCode;

    private String eawbcitTaxitemtype;

    private BigDecimal eawbcitTaxes;

    private BigDecimal eawbcitTaxtate;

    private Date eawbcitCreatetime;

    private String eawbcitPushStatus;

    private Date eawbcitPushTime;

    private String eawbcitPaycurrency;

    private BigDecimal eawbcitExchangeRate;

    public Long getEctSyscode() {
        return ectSyscode;
    }

    public void setEctSyscode(Long ectSyscode) {
        this.ectSyscode = ectSyscode;
    }

    public String getTrackingNumber() {
        return trackingNumber;
    }

    public void setTrackingNumber(String trackingNumber) {
        this.trackingNumber = trackingNumber == null ? null : trackingNumber.trim();
    }

    public String getLogisticsordercode() {
        return logisticsordercode;
    }

    public void setLogisticsordercode(String logisticsordercode) {
        this.logisticsordercode = logisticsordercode == null ? null : logisticsordercode.trim();
    }

    public String getCtCode() {
        return ctCode;
    }

    public void setCtCode(String ctCode) {
        this.ctCode = ctCode == null ? null : ctCode.trim();
    }

    public String getEawbCustcurrency() {
        return eawbCustcurrency;
    }

    public void setEawbCustcurrency(String eawbCustcurrency) {
        this.eawbCustcurrency = eawbCustcurrency == null ? null : eawbCustcurrency.trim();
    }

    public BigDecimal getEawbCustdeclval() {
        return eawbCustdeclval;
    }

    public void setEawbCustdeclval(BigDecimal eawbCustdeclval) {
        this.eawbCustdeclval = eawbCustdeclval;
    }

    public Date getEawbKeyentrytime() {
        return eawbKeyentrytime;
    }

    public void setEawbKeyentrytime(Date eawbKeyentrytime) {
        this.eawbKeyentrytime = eawbKeyentrytime;
    }

    public Long getEawbcSyscode() {
        return eawbcSyscode;
    }

    public void setEawbcSyscode(Long eawbcSyscode) {
        this.eawbcSyscode = eawbcSyscode;
    }

    public Long getEawbciSyscode() {
        return eawbciSyscode;
    }

    public void setEawbciSyscode(Long eawbciSyscode) {
        this.eawbciSyscode = eawbciSyscode;
    }

    public Long getEawbcitSyscode() {
        return eawbcitSyscode;
    }

    public void setEawbcitSyscode(Long eawbcitSyscode) {
        this.eawbcitSyscode = eawbcitSyscode;
    }

    public String getHscode() {
        return hscode;
    }

    public void setHscode(String hscode) {
        this.hscode = hscode == null ? null : hscode.trim();
    }

    public String getMawbCode() {
        return mawbCode;
    }

    public void setMawbCode(String mawbCode) {
        this.mawbCode = mawbCode == null ? null : mawbCode.trim();
    }

    public String getEawbcitTaxitemtype() {
        return eawbcitTaxitemtype;
    }

    public void setEawbcitTaxitemtype(String eawbcitTaxitemtype) {
        this.eawbcitTaxitemtype = eawbcitTaxitemtype == null ? null : eawbcitTaxitemtype.trim();
    }

    public BigDecimal getEawbcitTaxes() {
        return eawbcitTaxes;
    }

    public void setEawbcitTaxes(BigDecimal eawbcitTaxes) {
        this.eawbcitTaxes = eawbcitTaxes;
    }

    public BigDecimal getEawbcitTaxtate() {
        return eawbcitTaxtate;
    }

    public void setEawbcitTaxtate(BigDecimal eawbcitTaxtate) {
        this.eawbcitTaxtate = eawbcitTaxtate;
    }

    public Date getEawbcitCreatetime() {
        return eawbcitCreatetime;
    }

    public void setEawbcitCreatetime(Date eawbcitCreatetime) {
        this.eawbcitCreatetime = eawbcitCreatetime;
    }

    public String getEawbcitPushStatus() {
        return eawbcitPushStatus;
    }

    public void setEawbcitPushStatus(String eawbcitPushStatus) {
        this.eawbcitPushStatus = eawbcitPushStatus == null ? null : eawbcitPushStatus.trim();
    }

    public Date getEawbcitPushTime() {
        return eawbcitPushTime;
    }

    public void setEawbcitPushTime(Date eawbcitPushTime) {
        this.eawbcitPushTime = eawbcitPushTime;
    }

    public String getEawbcitPaycurrency() {
        return eawbcitPaycurrency;
    }

    public void setEawbcitPaycurrency(String eawbcitPaycurrency) {
        this.eawbcitPaycurrency = eawbcitPaycurrency == null ? null : eawbcitPaycurrency.trim();
    }

    public BigDecimal getEawbcitExchangeRate() {
        return eawbcitExchangeRate;
    }

    public void setEawbcitExchangeRate(BigDecimal eawbcitExchangeRate) {
        this.eawbcitExchangeRate = eawbcitExchangeRate;
    }
}