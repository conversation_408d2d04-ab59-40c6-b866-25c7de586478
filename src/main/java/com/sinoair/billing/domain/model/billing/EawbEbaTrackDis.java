package com.sinoair.billing.domain.model.billing;

public class EawbEbaTrackDis {
    private Short etdSyscode;

    private String companyId;

    private String soCode;

    private String etColumnname;

    private String eadCode;

    private String eastCode;

    private String etpColumnshowname;

    public Short getEtdSyscode() {
        return etdSyscode;
    }

    public void setEtdSyscode(Short etdSyscode) {
        this.etdSyscode = etdSyscode;
    }

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId == null ? null : companyId.trim();
    }

    public String getSoCode() {
        return soCode;
    }

    public void setSoCode(String soCode) {
        this.soCode = soCode == null ? null : soCode.trim();
    }

    public String getEtColumnname() {
        return etColumnname;
    }

    public void setEtColumnname(String etColumnname) {
        this.etColumnname = etColumnname == null ? null : etColumnname.trim();
    }

    public String getEadCode() {
        return eadCode;
    }

    public void setEadCode(String eadCode) {
        this.eadCode = eadCode == null ? null : eadCode.trim();
    }

    public String getEastCode() {
        return eastCode;
    }

    public void setEastCode(String eastCode) {
        this.eastCode = eastCode == null ? null : eastCode.trim();
    }

    public String getEtpColumnshowname() {
        return etpColumnshowname;
    }

    public void setEtpColumnshowname(String etpColumnshowname) {
        this.etpColumnshowname = etpColumnshowname == null ? null : etpColumnshowname.trim();
    }
}