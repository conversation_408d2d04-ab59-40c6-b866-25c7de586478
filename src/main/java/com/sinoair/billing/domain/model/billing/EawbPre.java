package com.sinoair.billing.domain.model.billing;

import java.math.BigDecimal;
import java.util.Date;

public class EawbPre {
    private Long eawbSyscode;

    private String eawbPrintcode;

    private Date eawbHandletime;

    private String eawbDeliverAddress;

    private String eawbDeliverContact;

    private String eawbDeliverPhone;

    private String eawbDeliverMobile;

    private String eawbDeliverEmail;

    private String eawbDestination;

    private String eawbSoCode;

    private String eawbReference1;

    private String eawbReference2;

    private String eawbStatus;

    private Date eawbKeyentrytime;

    private String eawbDestcountry;

    private String eawbCustprodname;

    private String eawbTransmodeid;

    private String eawbServicetype;

    private String eawbDeliverPostcode;

    private BigDecimal eawbCustdeclval;

    private String eawbDeststate;

    private String eawbDestcity;

    private BigDecimal eawbDeclaregrossweight;

    private String pkgPrintcode;

    public Long getEawbSyscode() {
        return eawbSyscode;
    }

    public void setEawbSyscode(Long eawbSyscode) {
        this.eawbSyscode = eawbSyscode;
    }

    public String getEawbPrintcode() {
        return eawbPrintcode;
    }

    public void setEawbPrintcode(String eawbPrintcode) {
        this.eawbPrintcode = eawbPrintcode == null ? null : eawbPrintcode.trim();
    }

    public Date getEawbHandletime() {
        return eawbHandletime;
    }

    public void setEawbHandletime(Date eawbHandletime) {
        this.eawbHandletime = eawbHandletime;
    }

    public String getEawbDeliverAddress() {
        return eawbDeliverAddress;
    }

    public void setEawbDeliverAddress(String eawbDeliverAddress) {
        this.eawbDeliverAddress = eawbDeliverAddress == null ? null : eawbDeliverAddress.trim();
    }

    public String getEawbDeliverContact() {
        return eawbDeliverContact;
    }

    public void setEawbDeliverContact(String eawbDeliverContact) {
        this.eawbDeliverContact = eawbDeliverContact == null ? null : eawbDeliverContact.trim();
    }

    public String getEawbDeliverPhone() {
        return eawbDeliverPhone;
    }

    public void setEawbDeliverPhone(String eawbDeliverPhone) {
        this.eawbDeliverPhone = eawbDeliverPhone == null ? null : eawbDeliverPhone.trim();
    }

    public String getEawbDeliverMobile() {
        return eawbDeliverMobile;
    }

    public void setEawbDeliverMobile(String eawbDeliverMobile) {
        this.eawbDeliverMobile = eawbDeliverMobile == null ? null : eawbDeliverMobile.trim();
    }

    public String getEawbDeliverEmail() {
        return eawbDeliverEmail;
    }

    public void setEawbDeliverEmail(String eawbDeliverEmail) {
        this.eawbDeliverEmail = eawbDeliverEmail == null ? null : eawbDeliverEmail.trim();
    }

    public String getEawbDestination() {
        return eawbDestination;
    }

    public void setEawbDestination(String eawbDestination) {
        this.eawbDestination = eawbDestination == null ? null : eawbDestination.trim();
    }

    public String getEawbSoCode() {
        return eawbSoCode;
    }

    public void setEawbSoCode(String eawbSoCode) {
        this.eawbSoCode = eawbSoCode == null ? null : eawbSoCode.trim();
    }

    public String getEawbReference1() {
        return eawbReference1;
    }

    public void setEawbReference1(String eawbReference1) {
        this.eawbReference1 = eawbReference1 == null ? null : eawbReference1.trim();
    }

    public String getEawbReference2() {
        return eawbReference2;
    }

    public void setEawbReference2(String eawbReference2) {
        this.eawbReference2 = eawbReference2 == null ? null : eawbReference2.trim();
    }

    public String getEawbStatus() {
        return eawbStatus;
    }

    public void setEawbStatus(String eawbStatus) {
        this.eawbStatus = eawbStatus == null ? null : eawbStatus.trim();
    }

    public Date getEawbKeyentrytime() {
        return eawbKeyentrytime;
    }

    public void setEawbKeyentrytime(Date eawbKeyentrytime) {
        this.eawbKeyentrytime = eawbKeyentrytime;
    }

    public String getEawbDestcountry() {
        return eawbDestcountry;
    }

    public void setEawbDestcountry(String eawbDestcountry) {
        this.eawbDestcountry = eawbDestcountry == null ? null : eawbDestcountry.trim();
    }

    public String getEawbCustprodname() {
        return eawbCustprodname;
    }

    public void setEawbCustprodname(String eawbCustprodname) {
        this.eawbCustprodname = eawbCustprodname == null ? null : eawbCustprodname.trim();
    }

    public String getEawbTransmodeid() {
        return eawbTransmodeid;
    }

    public void setEawbTransmodeid(String eawbTransmodeid) {
        this.eawbTransmodeid = eawbTransmodeid == null ? null : eawbTransmodeid.trim();
    }

    public String getEawbServicetype() {
        return eawbServicetype;
    }

    public void setEawbServicetype(String eawbServicetype) {
        this.eawbServicetype = eawbServicetype == null ? null : eawbServicetype.trim();
    }

    public String getEawbDeliverPostcode() {
        return eawbDeliverPostcode;
    }

    public void setEawbDeliverPostcode(String eawbDeliverPostcode) {
        this.eawbDeliverPostcode = eawbDeliverPostcode == null ? null : eawbDeliverPostcode.trim();
    }

    public BigDecimal getEawbCustdeclval() {
        return eawbCustdeclval;
    }

    public void setEawbCustdeclval(BigDecimal eawbCustdeclval) {
        this.eawbCustdeclval = eawbCustdeclval;
    }

    public String getEawbDeststate() {
        return eawbDeststate;
    }

    public void setEawbDeststate(String eawbDeststate) {
        this.eawbDeststate = eawbDeststate == null ? null : eawbDeststate.trim();
    }

    public String getEawbDestcity() {
        return eawbDestcity;
    }

    public void setEawbDestcity(String eawbDestcity) {
        this.eawbDestcity = eawbDestcity == null ? null : eawbDestcity.trim();
    }

    public BigDecimal getEawbDeclaregrossweight() {
        return eawbDeclaregrossweight;
    }

    public void setEawbDeclaregrossweight(BigDecimal eawbDeclaregrossweight) {
        this.eawbDeclaregrossweight = eawbDeclaregrossweight;
    }

    public String getPkgPrintcode() {
        return pkgPrintcode;
    }

    public void setPkgPrintcode(String pkgPrintcode) {
        this.pkgPrintcode = pkgPrintcode == null ? null : pkgPrintcode.trim();
    }
}