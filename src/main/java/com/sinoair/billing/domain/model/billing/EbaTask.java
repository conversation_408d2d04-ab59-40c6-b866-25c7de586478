package com.sinoair.billing.domain.model.billing;

import java.math.BigDecimal;
import java.util.Date;

public class EbaTask {
    private BigDecimal taskId;

    private String taskType;

    private String taskSoCode;

    private String taskStartDate;

    private String taskEndDate;

    private String taskEadCode;

    private String taskEastCode;

    private String taskEbapStatus;

    private String taskRemark;

    private String taskStatus;

    private String taskField1;

    private String taskField2;

    private String taskField3;

    private Date taskCreatetime;

    private Date taskHandletime;

    public BigDecimal getTaskId() {
        return taskId;
    }

    public void setTaskId(BigDecimal taskId) {
        this.taskId = taskId;
    }

    public String getTaskType() {
        return taskType;
    }

    public void setTaskType(String taskType) {
        this.taskType = taskType == null ? null : taskType.trim();
    }

    public String getTaskSoCode() {
        return taskSoCode;
    }

    public void setTaskSoCode(String taskSoCode) {
        this.taskSoCode = taskSoCode == null ? null : taskSoCode.trim();
    }

    public String getTaskStartDate() {
        return taskStartDate;
    }

    public void setTaskStartDate(String taskStartDate) {
        this.taskStartDate = taskStartDate == null ? null : taskStartDate.trim();
    }

    public String getTaskEndDate() {
        return taskEndDate;
    }

    public void setTaskEndDate(String taskEndDate) {
        this.taskEndDate = taskEndDate == null ? null : taskEndDate.trim();
    }

    public String getTaskEadCode() {
        return taskEadCode;
    }

    public void setTaskEadCode(String taskEadCode) {
        this.taskEadCode = taskEadCode == null ? null : taskEadCode.trim();
    }

    public String getTaskEastCode() {
        return taskEastCode;
    }

    public void setTaskEastCode(String taskEastCode) {
        this.taskEastCode = taskEastCode == null ? null : taskEastCode.trim();
    }

    public String getTaskEbapStatus() {
        return taskEbapStatus;
    }

    public void setTaskEbapStatus(String taskEbapStatus) {
        this.taskEbapStatus = taskEbapStatus == null ? null : taskEbapStatus.trim();
    }

    public String getTaskRemark() {
        return taskRemark;
    }

    public void setTaskRemark(String taskRemark) {
        this.taskRemark = taskRemark == null ? null : taskRemark.trim();
    }

    public String getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(String taskStatus) {
        this.taskStatus = taskStatus == null ? null : taskStatus.trim();
    }

    public String getTaskField1() {
        return taskField1;
    }

    public void setTaskField1(String taskField1) {
        this.taskField1 = taskField1 == null ? null : taskField1.trim();
    }

    public String getTaskField2() {
        return taskField2;
    }

    public void setTaskField2(String taskField2) {
        this.taskField2 = taskField2 == null ? null : taskField2.trim();
    }

    public String getTaskField3() {
        return taskField3;
    }

    public void setTaskField3(String taskField3) {
        this.taskField3 = taskField3 == null ? null : taskField3.trim();
    }

    public Date getTaskCreatetime() {
        return taskCreatetime;
    }

    public void setTaskCreatetime(Date taskCreatetime) {
        this.taskCreatetime = taskCreatetime;
    }

    public Date getTaskHandletime() {
        return taskHandletime;
    }

    public void setTaskHandletime(Date taskHandletime) {
        this.taskHandletime = taskHandletime;
    }
}