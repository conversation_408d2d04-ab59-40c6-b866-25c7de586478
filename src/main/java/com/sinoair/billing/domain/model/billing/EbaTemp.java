package com.sinoair.billing.domain.model.billing;

import java.math.BigDecimal;
import java.util.Date;

public class EbaTemp {
    private String eawbPrintcode;

    private String eawbServicetypeOriginal;

    private String eastCode;

    private Date qaPushtime;

    private String epGroup;

    private String epKey;

    public String getEawbPrintcode() {
        return eawbPrintcode;
    }

    public void setEawbPrintcode(String eawbPrintcode) {
        this.eawbPrintcode = eawbPrintcode;
    }

    public String getEawbServicetypeOriginal() {
        return eawbServicetypeOriginal;
    }

    public void setEawbServicetypeOriginal(String eawbServicetypeOriginal) {
        this.eawbServicetypeOriginal = eawbServicetypeOriginal;
    }

    public String getEastCode() {
        return eastCode;
    }

    public void setEastCode(String eastCode) {
        this.eastCode = eastCode;
    }

    public Date getQaPushtime() {
        return qaPushtime;
    }

    public void setQaPushtime(Date qaPushtime) {
        this.qaPushtime = qaPushtime;
    }

    public String getEpGroup() {
        return epGroup;
    }

    public void setEpGroup(String epGroup) {
        this.epGroup = epGroup;
    }

    public String getEpKey() {
        return epKey;
    }

    public void setEpKey(String epKey) {
        this.epKey = epKey;
    }

    public String getEpValue() {
        return epValue;
    }

    public void setEpValue(String epValue) {
        this.epValue = epValue;
    }

    public String getEpId() {
        return epId;
    }

    public void setEpId(String epId) {
        this.epId = epId;
    }

    public String getEpType() {
        return epType;
    }

    public void setEpType(String epType) {
        this.epType = epType;
    }

    public String getDestCountry() {
        return destCountry;
    }

    public void setDestCountry(String destCountry) {
        this.destCountry = destCountry;
    }

    private String epValue;

    private String epId;

    private String epType;

    private String destCountry;


}