package com.sinoair.billing.domain.model.billing;

import java.util.Date;

public class ErpDefine {
    private String erpCode;

    private String erpName;

    private String erpMode;

    private String erpPlatform;

    private String erpStatus;

    private Date handletime;

    public String getErpCode() {
        return erpCode;
    }

    public void setErpCode(String erpCode) {
        this.erpCode = erpCode == null ? null : erpCode.trim();
    }

    public String getErpName() {
        return erpName;
    }

    public void setErpName(String erpName) {
        this.erpName = erpName == null ? null : erpName.trim();
    }

    public String getErpMode() {
        return erpMode;
    }

    public void setErpMode(String erpMode) {
        this.erpMode = erpMode == null ? null : erpMode.trim();
    }

    public String getErpPlatform() {
        return erpPlatform;
    }

    public void setErpPlatform(String erpPlatform) {
        this.erpPlatform = erpPlatform == null ? null : erpPlatform.trim();
    }

    public String getErpStatus() {
        return erpStatus;
    }

    public void setErpStatus(String erpStatus) {
        this.erpStatus = erpStatus == null ? null : erpStatus.trim();
    }

    public Date getHandletime() {
        return handletime;
    }

    public void setHandletime(Date handletime) {
        this.handletime = handletime;
    }
}