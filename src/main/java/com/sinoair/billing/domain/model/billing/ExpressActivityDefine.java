package com.sinoair.billing.domain.model.billing;

public class ExpressActivityDefine {
    private Short eadSyscode;

    private String eadCode;

    private String eadName;

    private String eadStatus;

    private String eadType;

    private String eadUnit;

    private String eadActivity;

    private String eadDisplayname;

    private String eadPublic;

    public Short getEadSyscode() {
        return eadSyscode;
    }

    public void setEadSyscode(Short eadSyscode) {
        this.eadSyscode = eadSyscode;
    }

    public String getEadCode() {
        return eadCode;
    }

    public void setEadCode(String eadCode) {
        this.eadCode = eadCode == null ? null : eadCode.trim();
    }

    public String getEadName() {
        return eadName;
    }

    public void setEadName(String eadName) {
        this.eadName = eadName == null ? null : eadName.trim();
    }

    public String getEadStatus() {
        return eadStatus;
    }

    public void setEadStatus(String eadStatus) {
        this.eadStatus = eadStatus == null ? null : eadStatus.trim();
    }

    public String getEadType() {
        return eadType;
    }

    public void setEadType(String eadType) {
        this.eadType = eadType == null ? null : eadType.trim();
    }

    public String getEadUnit() {
        return eadUnit;
    }

    public void setEadUnit(String eadUnit) {
        this.eadUnit = eadUnit == null ? null : eadUnit.trim();
    }

    public String getEadActivity() {
        return eadActivity;
    }

    public void setEadActivity(String eadActivity) {
        this.eadActivity = eadActivity == null ? null : eadActivity.trim();
    }

    public String getEadDisplayname() {
        return eadDisplayname;
    }

    public void setEadDisplayname(String eadDisplayname) {
        this.eadDisplayname = eadDisplayname == null ? null : eadDisplayname.trim();
    }

    public String getEadPublic() {
        return eadPublic;
    }

    public void setEadPublic(String eadPublic) {
        this.eadPublic = eadPublic == null ? null : eadPublic.trim();
    }
}