package com.sinoair.billing.domain.model.billing;

import java.math.BigDecimal;
import java.util.Date;

public class ExpressAirWayBill {
    private BigDecimal eawbSyscode;

    private String eawbPrintcode;

    private String estCode;

    private Date eawbHandletime;

    private String eawbDeparture;

    private String eawbDestination;

    private BigDecimal eawbPieces;

    private BigDecimal eawbVolume;

    private BigDecimal eawbGrossweight;

    private BigDecimal eawbChargeableweight;

    private String ctCode;

    private String sacId;

    private String eawbSoCode;

    private String eawbReference1;

    private String eawbReference2;

    private String eawbStatus;

    private String eawbOutboundSacId;

    private Date eawbKeyentrytime;

    private String eawbDepartcountry;

    private String eawbDestcountry;

    private String eawbCustprodname;

    private String eawbTransmodeid;

    private String eawbServicetype;

    private String mawbCode;

    private BigDecimal eawbLength;

    private BigDecimal eawbWidth;

    private BigDecimal eawbHeight;

    private String eawbShipperAccountname;

    private String eawbConsigneeAccountname;

    private String eawbBtCode;

    private String eawbPickupPostcode;

    private String eawbDeliverPostcode;

    private String eawbTrackingNo;

    private Integer eawbQuantity;

    private BigDecimal eawbCustdeclval;

    private String eawbSpecification;

    private String eawbHscode;

    private String eawbCustprodenname;

    private String customerOrderCode;

    private String orderCodeIn;

    private String eawbPartition;

    private String eawbSfCode;

    private String eawbPlateCode;

    private String eawbTransmodeIdOriginal;

    private String eawbServiceTypeOriginal;

    private Date eawbUpdatetime;

    private String eawbIetype;

    private BigDecimal weightValue;

    private String weightUnit;

    private String zoneCode;

    //字段同步
    private String eawbDestcity;

    private BigDecimal eawbDeclarevolume;

    private BigDecimal eawbDeclaregrossweight;

    private BigDecimal eawbDeclarechargeable;

    private String eawbDeststate;

    private String refundStatus;

    private String transpostType;

    private String eaFlightType;

    private String eawbEcommerce;

    private BigDecimal eawbDeclarevalue;

    private String eawbCod;

    private BigDecimal eawbCodvalue;

    private String eawbCodcurrency;

    private String eawbFirstmile;

    private String destSacId;

    private String eawbReference3;

    private String eawbInner;

    private String ebCode;

    private String sinotransId;

    private String eawbDeliverAddress;
    private String eawbDeliverContact;
    private String eawbDeliverPhone;
    private String eawbDeliverMobile;
    private String eawbDeliverEmail;
    private String eawbServicerequirement;
    private String eawbBillingStatus;

    public String getEawbBillingStatus() {
        return eawbBillingStatus;
    }

    public void setEawbBillingStatus(String eawbBillingStatus) {
        this.eawbBillingStatus = eawbBillingStatus;
    }

    public BigDecimal getEawbSyscode() {
        return eawbSyscode;
    }

    public void setEawbSyscode(BigDecimal eawbSyscode) {
        this.eawbSyscode = eawbSyscode;
    }

    public String getEawbPrintcode() {
        return eawbPrintcode;
    }

    public void setEawbPrintcode(String eawbPrintcode) {
        this.eawbPrintcode = eawbPrintcode == null ? null : eawbPrintcode.trim();
    }

    public String getEstCode() {
        return estCode;
    }

    public void setEstCode(String estCode) {
        this.estCode = estCode == null ? null : estCode.trim();
    }

    public Date getEawbHandletime() {
        return eawbHandletime;
    }

    public void setEawbHandletime(Date eawbHandletime) {
        this.eawbHandletime = eawbHandletime;
    }

    public String getEawbDeparture() {
        return eawbDeparture;
    }

    public void setEawbDeparture(String eawbDeparture) {
        this.eawbDeparture = eawbDeparture == null ? null : eawbDeparture.trim();
    }

    public String getEawbDestination() {
        return eawbDestination;
    }

    public void setEawbDestination(String eawbDestination) {
        this.eawbDestination = eawbDestination == null ? null : eawbDestination.trim();
    }

    public BigDecimal getEawbPieces() {
        return eawbPieces;
    }

    public void setEawbPieces(BigDecimal eawbPieces) {
        this.eawbPieces = eawbPieces;
    }

    public BigDecimal getEawbVolume() {
        return eawbVolume;
    }

    public void setEawbVolume(BigDecimal eawbVolume) {
        this.eawbVolume = eawbVolume;
    }

    public BigDecimal getEawbGrossweight() {
        return eawbGrossweight;
    }

    public void setEawbGrossweight(BigDecimal eawbGrossweight) {
        this.eawbGrossweight = eawbGrossweight;
    }

    public BigDecimal getEawbChargeableweight() {
        return eawbChargeableweight;
    }

    public void setEawbChargeableweight(BigDecimal eawbChargeableweight) {
        this.eawbChargeableweight = eawbChargeableweight;
    }

    public String getCtCode() {
        return ctCode;
    }

    public void setCtCode(String ctCode) {
        this.ctCode = ctCode == null ? null : ctCode.trim();
    }

    public String getSacId() {
        return sacId;
    }

    public void setSacId(String sacId) {
        this.sacId = sacId == null ? null : sacId.trim();
    }

    public String getEawbSoCode() {
        return eawbSoCode;
    }

    public void setEawbSoCode(String eawbSoCode) {
        this.eawbSoCode = eawbSoCode == null ? null : eawbSoCode.trim();
    }

    public String getEawbReference1() {
        return eawbReference1;
    }

    public void setEawbReference1(String eawbReference1) {
        this.eawbReference1 = eawbReference1 == null ? null : eawbReference1.trim();
    }

    public String getEawbReference2() {
        return eawbReference2;
    }

    public void setEawbReference2(String eawbReference2) {
        this.eawbReference2 = eawbReference2 == null ? null : eawbReference2.trim();
    }

    public String getEawbStatus() {
        return eawbStatus;
    }

    public void setEawbStatus(String eawbStatus) {
        this.eawbStatus = eawbStatus == null ? null : eawbStatus.trim();
    }

    public String getEawbOutboundSacId() {
        return eawbOutboundSacId;
    }

    public void setEawbOutboundSacId(String eawbOutboundSacId) {
        this.eawbOutboundSacId = eawbOutboundSacId == null ? null : eawbOutboundSacId.trim();
    }

    public Date getEawbKeyentrytime() {
        return eawbKeyentrytime;
    }

    public void setEawbKeyentrytime(Date eawbKeyentrytime) {
        this.eawbKeyentrytime = eawbKeyentrytime;
    }

    public String getEawbDepartcountry() {
        return eawbDepartcountry;
    }

    public void setEawbDepartcountry(String eawbDepartcountry) {
        this.eawbDepartcountry = eawbDepartcountry == null ? null : eawbDepartcountry.trim();
    }

    public String getEawbDestcountry() {
        return eawbDestcountry;
    }

    public void setEawbDestcountry(String eawbDestcountry) {
        this.eawbDestcountry = eawbDestcountry == null ? null : eawbDestcountry.trim();
    }

    public String getEawbCustprodname() {
        return eawbCustprodname;
    }

    public void setEawbCustprodname(String eawbCustprodname) {
        this.eawbCustprodname = eawbCustprodname == null ? null : eawbCustprodname.trim();
    }

    public String getEawbTransmodeid() {
        return eawbTransmodeid;
    }

    public void setEawbTransmodeid(String eawbTransmodeid) {
        this.eawbTransmodeid = eawbTransmodeid == null ? null : eawbTransmodeid.trim();
    }

    public String getEawbServicetype() {
        return eawbServicetype;
    }

    public void setEawbServicetype(String eawbServicetype) {
        this.eawbServicetype = eawbServicetype == null ? null : eawbServicetype.trim();
    }

    public String getMawbCode() {
        return mawbCode;
    }

    public void setMawbCode(String mawbCode) {
        this.mawbCode = mawbCode == null ? null : mawbCode.trim();
    }

    public BigDecimal getEawbLength() {
        return eawbLength;
    }

    public void setEawbLength(BigDecimal eawbLength) {
        this.eawbLength = eawbLength;
    }

    public BigDecimal getEawbWidth() {
        return eawbWidth;
    }

    public void setEawbWidth(BigDecimal eawbWidth) {
        this.eawbWidth = eawbWidth;
    }

    public BigDecimal getEawbHeight() {
        return eawbHeight;
    }

    public void setEawbHeight(BigDecimal eawbHeight) {
        this.eawbHeight = eawbHeight;
    }

    public String getEawbShipperAccountname() {
        return eawbShipperAccountname;
    }

    public void setEawbShipperAccountname(String eawbShipperAccountname) {
        this.eawbShipperAccountname = eawbShipperAccountname == null ? null : eawbShipperAccountname.trim();
    }

    public String getEawbConsigneeAccountname() {
        return eawbConsigneeAccountname;
    }

    public void setEawbConsigneeAccountname(String eawbConsigneeAccountname) {
        this.eawbConsigneeAccountname = eawbConsigneeAccountname == null ? null : eawbConsigneeAccountname.trim();
    }

    public String getEawbBtCode() {
        return eawbBtCode;
    }

    public void setEawbBtCode(String eawbBtCode) {
        this.eawbBtCode = eawbBtCode == null ? null : eawbBtCode.trim();
    }

    public String getEawbPickupPostcode() {
        return eawbPickupPostcode;
    }

    public void setEawbPickupPostcode(String eawbPickupPostcode) {
        this.eawbPickupPostcode = eawbPickupPostcode == null ? null : eawbPickupPostcode.trim();
    }

    public String getEawbDeliverPostcode() {
        return eawbDeliverPostcode;
    }

    public void setEawbDeliverPostcode(String eawbDeliverPostcode) {
        this.eawbDeliverPostcode = eawbDeliverPostcode == null ? null : eawbDeliverPostcode.trim();
    }

    public String getEawbTrackingNo() {
        return eawbTrackingNo;
    }

    public void setEawbTrackingNo(String eawbTrackingNo) {
        this.eawbTrackingNo = eawbTrackingNo == null ? null : eawbTrackingNo.trim();
    }

    public Integer getEawbQuantity() {
        return eawbQuantity;
    }

    public void setEawbQuantity(Integer eawbQuantity) {
        this.eawbQuantity = eawbQuantity;
    }

    public BigDecimal getEawbCustdeclval() {
        return eawbCustdeclval;
    }

    public void setEawbCustdeclval(BigDecimal eawbCustdeclval) {
        this.eawbCustdeclval = eawbCustdeclval;
    }

    public String getEawbSpecification() {
        return eawbSpecification;
    }

    public void setEawbSpecification(String eawbSpecification) {
        this.eawbSpecification = eawbSpecification == null ? null : eawbSpecification.trim();
    }

    public String getEawbHscode() {
        return eawbHscode;
    }

    public void setEawbHscode(String eawbHscode) {
        this.eawbHscode = eawbHscode == null ? null : eawbHscode.trim();
    }

    public String getEawbCustprodenname() {
        return eawbCustprodenname;
    }

    public void setEawbCustprodenname(String eawbCustprodenname) {
        this.eawbCustprodenname = eawbCustprodenname == null ? null : eawbCustprodenname.trim();
    }

    public String getCustomerOrderCode() {
        return customerOrderCode;
    }

    public void setCustomerOrderCode(String customerOrderCode) {
        this.customerOrderCode = customerOrderCode == null ? null : customerOrderCode.trim();
    }

    public String getOrderCodeIn() {
        return orderCodeIn;
    }

    public void setOrderCodeIn(String orderCodeIn) {
        this.orderCodeIn = orderCodeIn == null ? null : orderCodeIn.trim();
    }

    public String getEawbSfCode() {
        return eawbSfCode;
    }

    public void setEawbSfCode(String eawbSfCode) {
        this.eawbSfCode = eawbSfCode == null ? null : eawbSfCode.trim();
    }

    public String getEawbPlateCode() {
        return eawbPlateCode;
    }

    public void setEawbPlateCode(String eawbPlateCode) {
        this.eawbPlateCode = eawbPlateCode == null ? null : eawbPlateCode.trim();
    }

    public String getEawbTransmodeIdOriginal() {
        return eawbTransmodeIdOriginal;
    }

    public void setEawbTransmodeIdOriginal(String eawbTransmodeIdOriginal) {
        this.eawbTransmodeIdOriginal = eawbTransmodeIdOriginal == null? null : eawbTransmodeIdOriginal.trim();
    }

    public String getEawbServiceTypeOriginal() {
        return eawbServiceTypeOriginal;
    }

    public void setEawbServiceTypeOriginal(String eawbServiceTypeOriginal) {
        this.eawbServiceTypeOriginal = eawbServiceTypeOriginal == null ? null : eawbServiceTypeOriginal.trim();
    }

    public Date getEawbUpdatetime() {
        return eawbUpdatetime;
    }

    public void setEawbUpdatetime(Date eawbUpdatetime) {
        this.eawbUpdatetime = eawbUpdatetime;
    }

    public String getEawbIetype() {
        return eawbIetype;
    }

    public void setEawbIetype(String eawbIetype) {
        this.eawbIetype = eawbIetype;
    }

    public BigDecimal getWeightValue() {
        return weightValue;
    }

    public void setWeightValue(BigDecimal weightValue) {
        this.weightValue = weightValue;
    }

    public String getWeightUnit() {
        return weightUnit;
    }

    public void setWeightUnit(String weightUnit) {
        this.weightUnit = weightUnit;
    }

    public String getZoneCode() {
        return zoneCode;
    }

    public void setZoneCode(String zoneCode) {
        this.zoneCode = zoneCode;
    }

    public String getEawbPartition() {
        return eawbPartition;
    }

    public void setEawbPartition(String eawbPartition) {
        this.eawbPartition = eawbPartition;
    }

    public String getEawbDestcity() {
        return eawbDestcity;
    }

    public void setEawbDestcity(String eawbDestcity) {
        this.eawbDestcity = eawbDestcity;
    }

    public BigDecimal getEawbDeclarevolume() {
        return eawbDeclarevolume;
    }

    public void setEawbDeclarevolume(BigDecimal eawbDeclarevolume) {
        this.eawbDeclarevolume = eawbDeclarevolume;
    }

    public BigDecimal getEawbDeclaregrossweight() {
        return eawbDeclaregrossweight;
    }

    public void setEawbDeclaregrossweight(BigDecimal eawbDeclaregrossweight) {
        this.eawbDeclaregrossweight = eawbDeclaregrossweight;
    }

    public BigDecimal getEawbDeclarechargeable() {
        return eawbDeclarechargeable;
    }

    public void setEawbDeclarechargeable(BigDecimal eawbDeclarechargeable) {
        this.eawbDeclarechargeable = eawbDeclarechargeable;
    }

    public String getEawbDeststate() {
        return eawbDeststate;
    }

    public void setEawbDeststate(String eawbDeststate) {
        this.eawbDeststate = eawbDeststate;
    }

    public String getRefundStatus() {
        return refundStatus;
    }

    public void setRefundStatus(String refundStatus) {
        this.refundStatus = refundStatus;
    }

    public String getTranspostType() {
        return transpostType;
    }

    public void setTranspostType(String transpostType) {
        this.transpostType = transpostType;
    }

    public String getEaFlightType() {
        return eaFlightType;
    }

    public void setEaFlightType(String eaFlightType) {
        this.eaFlightType = eaFlightType;
    }

    public String getEawbEcommerce() {
        return eawbEcommerce;
    }

    public void setEawbEcommerce(String eawbEcommerce) {
        this.eawbEcommerce = eawbEcommerce;
    }

    public BigDecimal getEawbDeclarevalue() {
        return eawbDeclarevalue;
    }

    public void setEawbDeclarevalue(BigDecimal eawbDeclarevalue) {
        this.eawbDeclarevalue = eawbDeclarevalue;
    }

    public String getEawbCod() {
        return eawbCod;
    }

    public void setEawbCod(String eawbCod) {
        this.eawbCod = eawbCod;
    }

    public BigDecimal getEawbCodvalue() {
        return eawbCodvalue;
    }

    public void setEawbCodvalue(BigDecimal eawbCodvalue) {
        this.eawbCodvalue = eawbCodvalue;
    }

    public String getEawbCodcurrency() {
        return eawbCodcurrency;
    }

    public void setEawbCodcurrency(String eawbCodcurrency) {
        this.eawbCodcurrency = eawbCodcurrency;
    }

    public String getEawbFirstmile() {
        return eawbFirstmile;
    }

    public void setEawbFirstmile(String eawbFirstmile) {
        this.eawbFirstmile = eawbFirstmile;
    }

    public String getDestSacId() {
        return destSacId;
    }

    public void setDestSacId(String destSacId) {
        this.destSacId = destSacId;
    }

    public String getEawbReference3() {
        return eawbReference3;
    }

    public void setEawbReference3(String eawbReference3) {
        this.eawbReference3 = eawbReference3;
    }

    public String getEawbInner() {
        return eawbInner;
    }

    public void setEawbInner(String eawbInner) {
        this.eawbInner = eawbInner;
    }

    public String getEbCode() {
        return ebCode;
    }

    public void setEbCode(String ebCode) {
        this.ebCode = ebCode;
    }

    public String getSinotransId() {
        return sinotransId;
    }

    public void setSinotransId(String sinotransId) {
        this.sinotransId = sinotransId;
    }

    public String getEawbDeliverAddress() {
        return eawbDeliverAddress;
    }

    public void setEawbDeliverAddress(String eawbDeliverAddress) {
        this.eawbDeliverAddress = eawbDeliverAddress;
    }

    public String getEawbDeliverContact() {
        return eawbDeliverContact;
    }

    public void setEawbDeliverContact(String eawbDeliverContact) {
        this.eawbDeliverContact = eawbDeliverContact;
    }

    public String getEawbDeliverPhone() {
        return eawbDeliverPhone;
    }

    public void setEawbDeliverPhone(String eawbDeliverPhone) {
        this.eawbDeliverPhone = eawbDeliverPhone;
    }

    public String getEawbDeliverMobile() {
        return eawbDeliverMobile;
    }

    public void setEawbDeliverMobile(String eawbDeliverMobile) {
        this.eawbDeliverMobile = eawbDeliverMobile;
    }

    public String getEawbDeliverEmail() {
        return eawbDeliverEmail;
    }

    public void setEawbDeliverEmail(String eawbDeliverEmail) {
        this.eawbDeliverEmail = eawbDeliverEmail;
    }

    public String getEawbServicerequirement() {
        return eawbServicerequirement;
    }

    public void setEawbServicerequirement(String eawbServicerequirement) {
        this.eawbServicerequirement = eawbServicerequirement;
    }
}