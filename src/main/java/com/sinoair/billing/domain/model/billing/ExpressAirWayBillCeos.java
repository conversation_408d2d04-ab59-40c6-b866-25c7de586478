package com.sinoair.billing.domain.model.billing;

import java.math.BigDecimal;
import java.util.Date;

public class ExpressAirWayBillCeos {
    private Long eawbSyscode;

    private String eawbCode;

    private String eawbPrintcode;

    private BigDecimal eawbEIdHandler;

    private String estCode;

    private Date eawbHandletime;

    private String eawbShipperAccount;

    private String eawbPickupAddress;

    private String eawbPickupPostcode;

    private String eawbPickupContact;

    private String eawbPickupPhone;

    private String eawbConsigneeAccount;

    private String eawbDeliverAddress;

    private String eawbDeliverPostcode;

    private String eawbDeliverContact;

    private String eawbDeliverPhone;

    private String eawbDeparture;

    private String eawbDestination;

    private String eawbProductname;

    private BigDecimal eawbTotalpieces;

    private BigDecimal eawbTotalvolume;

    private BigDecimal eawbTotalgrossweight;

    private BigDecimal eawbTotalchargeableweight;

    private BigDecimal eawbDeclarevolume;

    private BigDecimal eawbDeclaregrossweight;

    private BigDecimal eawbDeclarechargeable;

    private String eawbPaymentmode;

    private String eawbThirdpartyAccount;

    private String ctCode;

    private String eawbCurrencyrate;

    private BigDecimal eawbTotalrmb;

    private BigDecimal eawbTotalfc;

    private String sacId;

    private String eawbSacCode;

    private String ebCode;

    private String eawbSoCode;

    private String eawbShipperAccountname;

    private String eawbConsigneeAccountname;

    private String eawbPickupBlock;

    private Date eawbPickupTime;

    private BigDecimal eawbFreightcharge;

    private BigDecimal eawbIncidentalcharge;

    private BigDecimal eawbInsurancecharge;

    private String eawbPpcc;

    private BigDecimal eawbDeclarevalue;

    private String eawbReference1;

    private String eawbReference2;

    private BigDecimal eawbStandardfreightprice;

    private String eawbStatus;

    private String eawbPickupbyconsignee;

    private String eawbDeliveryatholiday;

    private String eawbInner;

    private String eawbInboundSacId;

    private String eawbOutboundSacId;

    private String eawbProductdeclare;

    private String eawbServicerequirement;

    private String eawbReference3;

    private String eawbCCode;

    private Date eawbTimeLimit;

    private BigDecimal eawbCollectpaymentforgoods;

    private String eawbSelfinsurance;

    private Date eawbKeyentrytime;

    private String eawbPreservation;

    private String eawbInsuranceservice;

    private String eawbAgentCode;

    private String eawbBusinessmode;

    private BigDecimal eawbPreFreightprice;

    private String eawbSendvoicerequest;

    private String cbcShipCode;

    private String eawbRoute;

    private String eawbDepartcity;

    private String eawbDepartstate;

    private String eawbDepartcountry;

    private String eawbDestcity;

    private String eawbDeststate;

    private String eawbDestcountry;

    private String eawbType;

    private String eawbCustprodname;

    private Integer eawbQuantity;

    private BigDecimal eawbCustdeclval;

    private String eawbCustcurrency;

    private String eawbDeclcurrency;

    private String eawbConsignmentno;

    private String eawbKjtype;

    private String eawbIetype;

    private String bCode;

    private BigDecimal eawbFuelcharge;

    private String eawbInsutype;

    private String eawbInsuamounttype;

    private BigDecimal eawbInsuamount;

    private BigDecimal eawbPreFuelprice;

    private String handsetCargo;

    private String infoxml;

    private BigDecimal eawbDiscountvalue;

    private String eawbTransmodeid;

    private String eawbProducttype;

    private String eawbOrigincity;

    private String eawbShipperCaccountname;

    private String eawbDeliverFax;

    private String eawbSpecification;

    private String eawbCctype;

    private String eawbCustregistrationcode;

    private String eawbCustregistrationame;

    private String eawbEntrustcode;

    private String eawbHscode;

    private String eawbCustprodenname;

    private String eawbOperatestatus;

    private String eawbUnit;

    private String eawbUnitcode;

    private String eawbCheckstatus;

    private String eawbServicetype;

    private String mawbCode;

    private String flightNo;

    private String customType;

    private String eawbEcommerce;

    private String eawbTaxcode;

    private String eawbDeliverIndentitycardno;

    private String customsStatus;

    private BigDecimal csefSyscode;

    private BigDecimal etcopy;

    private String eawbTrack;

    private String eawbDeliverEmail;

    private String eawbPartition;

    private String eawbDeliveryPostcodeCorrect;

    private String eawbChangeLabelStatus;

    private String eawbRefundWangwangId;

    private String eawbRefundName;

    private String eawbRefundPhone;

    private String eawbRefundMobile;

    private String eawbRefundEmail;

    private String eawbRefundCountry;

    private String eawbRefundPrivince;

    private String eawbRefundCity;

    private String eawbRefundDistrict;

    private String eawbRefundStreet;

    private String eawbRefundZipcode;

    private String eawbUndeliveryOption;

    private String eawbCarriername;

    private String eawbCarrierno;

    private String orderCodeIn;

    private Long orderSyscodeIn;

    private String orderCodeOut;

    private Long orderSyscodeOut;

    private String customerOrderCode;

    private String eawbServicetypeOriginal;

    private BigDecimal eawbLength;

    private BigDecimal eawbWidth;

    private BigDecimal eawbHeight;

    private String eawbDeliverMobile;

    private String eawbCod;

    private BigDecimal eawbCodvalue;

    private String eawbCodcurrency;

    private String eawbRefundReference;

    private String eawbTransmodeidOriginal;

    private String eawbRefundSupplier;

    private String eawbRefundReference1;

    private String eawbRefundReference2;

    private String eawbRefundReference3;

    private String eawbRefundAddress;

    private String refundStatus;

    private String eawbDeliverStreet;

    private String eawbNextOptCenter;

    private String eawbPreOptCenter;

    private String eawbTrunkCode;

    private String eawbChannelCode;

    private String eawbDeliverDistrict;

    private String eawbPickupDistrict;

    private String eawbSenderAddress;

    private String eawbSortcode;

    private String eawbConsigneeLatitude;

    private String eawbConsigneeLongitude;

    private String eawbFirstmile;

    private String eawbPaymentid;

    private String eawbPaymentemail;

    private String eawbPaymentcontactname;

    private String eawbPaymentphonenumber;

    private String eawbSellertaxnumber;

    private String eawbInvoicenumber;

    private String eawbProducturl;

    public Long getEawbSyscode() {
        return eawbSyscode;
    }

    public void setEawbSyscode(Long eawbSyscode) {
        this.eawbSyscode = eawbSyscode;
    }

    public String getEawbCode() {
        return eawbCode;
    }

    public void setEawbCode(String eawbCode) {
        this.eawbCode = eawbCode == null ? null : eawbCode.trim();
    }

    public String getEawbPrintcode() {
        return eawbPrintcode;
    }

    public void setEawbPrintcode(String eawbPrintcode) {
        this.eawbPrintcode = eawbPrintcode == null ? null : eawbPrintcode.trim();
    }

    public BigDecimal getEawbEIdHandler() {
        return eawbEIdHandler;
    }

    public void setEawbEIdHandler(BigDecimal eawbEIdHandler) {
        this.eawbEIdHandler = eawbEIdHandler;
    }

    public String getEstCode() {
        return estCode;
    }

    public void setEstCode(String estCode) {
        this.estCode = estCode == null ? null : estCode.trim();
    }

    public Date getEawbHandletime() {
        return eawbHandletime;
    }

    public void setEawbHandletime(Date eawbHandletime) {
        this.eawbHandletime = eawbHandletime;
    }

    public String getEawbShipperAccount() {
        return eawbShipperAccount;
    }

    public void setEawbShipperAccount(String eawbShipperAccount) {
        this.eawbShipperAccount = eawbShipperAccount == null ? null : eawbShipperAccount.trim();
    }

    public String getEawbPickupAddress() {
        return eawbPickupAddress;
    }

    public void setEawbPickupAddress(String eawbPickupAddress) {
        this.eawbPickupAddress = eawbPickupAddress == null ? null : eawbPickupAddress.trim();
    }

    public String getEawbPickupPostcode() {
        return eawbPickupPostcode;
    }

    public void setEawbPickupPostcode(String eawbPickupPostcode) {
        this.eawbPickupPostcode = eawbPickupPostcode == null ? null : eawbPickupPostcode.trim();
    }

    public String getEawbPickupContact() {
        return eawbPickupContact;
    }

    public void setEawbPickupContact(String eawbPickupContact) {
        this.eawbPickupContact = eawbPickupContact == null ? null : eawbPickupContact.trim();
    }

    public String getEawbPickupPhone() {
        return eawbPickupPhone;
    }

    public void setEawbPickupPhone(String eawbPickupPhone) {
        this.eawbPickupPhone = eawbPickupPhone == null ? null : eawbPickupPhone.trim();
    }

    public String getEawbConsigneeAccount() {
        return eawbConsigneeAccount;
    }

    public void setEawbConsigneeAccount(String eawbConsigneeAccount) {
        this.eawbConsigneeAccount = eawbConsigneeAccount == null ? null : eawbConsigneeAccount.trim();
    }

    public String getEawbDeliverAddress() {
        return eawbDeliverAddress;
    }

    public void setEawbDeliverAddress(String eawbDeliverAddress) {
        this.eawbDeliverAddress = eawbDeliverAddress == null ? null : eawbDeliverAddress.trim();
    }

    public String getEawbDeliverPostcode() {
        return eawbDeliverPostcode;
    }

    public void setEawbDeliverPostcode(String eawbDeliverPostcode) {
        this.eawbDeliverPostcode = eawbDeliverPostcode == null ? null : eawbDeliverPostcode.trim();
    }

    public String getEawbDeliverContact() {
        return eawbDeliverContact;
    }

    public void setEawbDeliverContact(String eawbDeliverContact) {
        this.eawbDeliverContact = eawbDeliverContact == null ? null : eawbDeliverContact.trim();
    }

    public String getEawbDeliverPhone() {
        return eawbDeliverPhone;
    }

    public void setEawbDeliverPhone(String eawbDeliverPhone) {
        this.eawbDeliverPhone = eawbDeliverPhone == null ? null : eawbDeliverPhone.trim();
    }

    public String getEawbDeparture() {
        return eawbDeparture;
    }

    public void setEawbDeparture(String eawbDeparture) {
        this.eawbDeparture = eawbDeparture == null ? null : eawbDeparture.trim();
    }

    public String getEawbDestination() {
        return eawbDestination;
    }

    public void setEawbDestination(String eawbDestination) {
        this.eawbDestination = eawbDestination == null ? null : eawbDestination.trim();
    }

    public String getEawbProductname() {
        return eawbProductname;
    }

    public void setEawbProductname(String eawbProductname) {
        this.eawbProductname = eawbProductname == null ? null : eawbProductname.trim();
    }

    public BigDecimal getEawbTotalpieces() {
        return eawbTotalpieces;
    }

    public void setEawbTotalpieces(BigDecimal eawbTotalpieces) {
        this.eawbTotalpieces = eawbTotalpieces;
    }

    public BigDecimal getEawbTotalvolume() {
        return eawbTotalvolume;
    }

    public void setEawbTotalvolume(BigDecimal eawbTotalvolume) {
        this.eawbTotalvolume = eawbTotalvolume;
    }

    public BigDecimal getEawbTotalgrossweight() {
        return eawbTotalgrossweight;
    }

    public void setEawbTotalgrossweight(BigDecimal eawbTotalgrossweight) {
        this.eawbTotalgrossweight = eawbTotalgrossweight;
    }

    public BigDecimal getEawbTotalchargeableweight() {
        return eawbTotalchargeableweight;
    }

    public void setEawbTotalchargeableweight(BigDecimal eawbTotalchargeableweight) {
        this.eawbTotalchargeableweight = eawbTotalchargeableweight;
    }

    public BigDecimal getEawbDeclarevolume() {
        return eawbDeclarevolume;
    }

    public void setEawbDeclarevolume(BigDecimal eawbDeclarevolume) {
        this.eawbDeclarevolume = eawbDeclarevolume;
    }

    public BigDecimal getEawbDeclaregrossweight() {
        return eawbDeclaregrossweight;
    }

    public void setEawbDeclaregrossweight(BigDecimal eawbDeclaregrossweight) {
        this.eawbDeclaregrossweight = eawbDeclaregrossweight;
    }

    public BigDecimal getEawbDeclarechargeable() {
        return eawbDeclarechargeable;
    }

    public void setEawbDeclarechargeable(BigDecimal eawbDeclarechargeable) {
        this.eawbDeclarechargeable = eawbDeclarechargeable;
    }

    public String getEawbPaymentmode() {
        return eawbPaymentmode;
    }

    public void setEawbPaymentmode(String eawbPaymentmode) {
        this.eawbPaymentmode = eawbPaymentmode == null ? null : eawbPaymentmode.trim();
    }

    public String getEawbThirdpartyAccount() {
        return eawbThirdpartyAccount;
    }

    public void setEawbThirdpartyAccount(String eawbThirdpartyAccount) {
        this.eawbThirdpartyAccount = eawbThirdpartyAccount == null ? null : eawbThirdpartyAccount.trim();
    }

    public String getCtCode() {
        return ctCode;
    }

    public void setCtCode(String ctCode) {
        this.ctCode = ctCode == null ? null : ctCode.trim();
    }

    public String getEawbCurrencyrate() {
        return eawbCurrencyrate;
    }

    public void setEawbCurrencyrate(String eawbCurrencyrate) {
        this.eawbCurrencyrate = eawbCurrencyrate == null ? null : eawbCurrencyrate.trim();
    }

    public BigDecimal getEawbTotalrmb() {
        return eawbTotalrmb;
    }

    public void setEawbTotalrmb(BigDecimal eawbTotalrmb) {
        this.eawbTotalrmb = eawbTotalrmb;
    }

    public BigDecimal getEawbTotalfc() {
        return eawbTotalfc;
    }

    public void setEawbTotalfc(BigDecimal eawbTotalfc) {
        this.eawbTotalfc = eawbTotalfc;
    }

    public String getSacId() {
        return sacId;
    }

    public void setSacId(String sacId) {
        this.sacId = sacId == null ? null : sacId.trim();
    }

    public String getEawbSacCode() {
        return eawbSacCode;
    }

    public void setEawbSacCode(String eawbSacCode) {
        this.eawbSacCode = eawbSacCode == null ? null : eawbSacCode.trim();
    }

    public String getEbCode() {
        return ebCode;
    }

    public void setEbCode(String ebCode) {
        this.ebCode = ebCode == null ? null : ebCode.trim();
    }

    public String getEawbSoCode() {
        return eawbSoCode;
    }

    public void setEawbSoCode(String eawbSoCode) {
        this.eawbSoCode = eawbSoCode == null ? null : eawbSoCode.trim();
    }

    public String getEawbShipperAccountname() {
        return eawbShipperAccountname;
    }

    public void setEawbShipperAccountname(String eawbShipperAccountname) {
        this.eawbShipperAccountname = eawbShipperAccountname == null ? null : eawbShipperAccountname.trim();
    }

    public String getEawbConsigneeAccountname() {
        return eawbConsigneeAccountname;
    }

    public void setEawbConsigneeAccountname(String eawbConsigneeAccountname) {
        this.eawbConsigneeAccountname = eawbConsigneeAccountname == null ? null : eawbConsigneeAccountname.trim();
    }

    public String getEawbPickupBlock() {
        return eawbPickupBlock;
    }

    public void setEawbPickupBlock(String eawbPickupBlock) {
        this.eawbPickupBlock = eawbPickupBlock == null ? null : eawbPickupBlock.trim();
    }

    public Date getEawbPickupTime() {
        return eawbPickupTime;
    }

    public void setEawbPickupTime(Date eawbPickupTime) {
        this.eawbPickupTime = eawbPickupTime;
    }

    public BigDecimal getEawbFreightcharge() {
        return eawbFreightcharge;
    }

    public void setEawbFreightcharge(BigDecimal eawbFreightcharge) {
        this.eawbFreightcharge = eawbFreightcharge;
    }

    public BigDecimal getEawbIncidentalcharge() {
        return eawbIncidentalcharge;
    }

    public void setEawbIncidentalcharge(BigDecimal eawbIncidentalcharge) {
        this.eawbIncidentalcharge = eawbIncidentalcharge;
    }

    public BigDecimal getEawbInsurancecharge() {
        return eawbInsurancecharge;
    }

    public void setEawbInsurancecharge(BigDecimal eawbInsurancecharge) {
        this.eawbInsurancecharge = eawbInsurancecharge;
    }

    public String getEawbPpcc() {
        return eawbPpcc;
    }

    public void setEawbPpcc(String eawbPpcc) {
        this.eawbPpcc = eawbPpcc == null ? null : eawbPpcc.trim();
    }

    public BigDecimal getEawbDeclarevalue() {
        return eawbDeclarevalue;
    }

    public void setEawbDeclarevalue(BigDecimal eawbDeclarevalue) {
        this.eawbDeclarevalue = eawbDeclarevalue;
    }

    public String getEawbReference1() {
        return eawbReference1;
    }

    public void setEawbReference1(String eawbReference1) {
        this.eawbReference1 = eawbReference1 == null ? null : eawbReference1.trim();
    }

    public String getEawbReference2() {
        return eawbReference2;
    }

    public void setEawbReference2(String eawbReference2) {
        this.eawbReference2 = eawbReference2 == null ? null : eawbReference2.trim();
    }

    public BigDecimal getEawbStandardfreightprice() {
        return eawbStandardfreightprice;
    }

    public void setEawbStandardfreightprice(BigDecimal eawbStandardfreightprice) {
        this.eawbStandardfreightprice = eawbStandardfreightprice;
    }

    public String getEawbStatus() {
        return eawbStatus;
    }

    public void setEawbStatus(String eawbStatus) {
        this.eawbStatus = eawbStatus == null ? null : eawbStatus.trim();
    }

    public String getEawbPickupbyconsignee() {
        return eawbPickupbyconsignee;
    }

    public void setEawbPickupbyconsignee(String eawbPickupbyconsignee) {
        this.eawbPickupbyconsignee = eawbPickupbyconsignee == null ? null : eawbPickupbyconsignee.trim();
    }

    public String getEawbDeliveryatholiday() {
        return eawbDeliveryatholiday;
    }

    public void setEawbDeliveryatholiday(String eawbDeliveryatholiday) {
        this.eawbDeliveryatholiday = eawbDeliveryatholiday == null ? null : eawbDeliveryatholiday.trim();
    }

    public String getEawbInner() {
        return eawbInner;
    }

    public void setEawbInner(String eawbInner) {
        this.eawbInner = eawbInner == null ? null : eawbInner.trim();
    }

    public String getEawbInboundSacId() {
        return eawbInboundSacId;
    }

    public void setEawbInboundSacId(String eawbInboundSacId) {
        this.eawbInboundSacId = eawbInboundSacId == null ? null : eawbInboundSacId.trim();
    }

    public String getEawbOutboundSacId() {
        return eawbOutboundSacId;
    }

    public void setEawbOutboundSacId(String eawbOutboundSacId) {
        this.eawbOutboundSacId = eawbOutboundSacId == null ? null : eawbOutboundSacId.trim();
    }

    public String getEawbProductdeclare() {
        return eawbProductdeclare;
    }

    public void setEawbProductdeclare(String eawbProductdeclare) {
        this.eawbProductdeclare = eawbProductdeclare == null ? null : eawbProductdeclare.trim();
    }

    public String getEawbServicerequirement() {
        return eawbServicerequirement;
    }

    public void setEawbServicerequirement(String eawbServicerequirement) {
        this.eawbServicerequirement = eawbServicerequirement == null ? null : eawbServicerequirement.trim();
    }

    public String getEawbReference3() {
        return eawbReference3;
    }

    public void setEawbReference3(String eawbReference3) {
        this.eawbReference3 = eawbReference3 == null ? null : eawbReference3.trim();
    }

    public String getEawbCCode() {
        return eawbCCode;
    }

    public void setEawbCCode(String eawbCCode) {
        this.eawbCCode = eawbCCode == null ? null : eawbCCode.trim();
    }

    public Date getEawbTimeLimit() {
        return eawbTimeLimit;
    }

    public void setEawbTimeLimit(Date eawbTimeLimit) {
        this.eawbTimeLimit = eawbTimeLimit;
    }

    public BigDecimal getEawbCollectpaymentforgoods() {
        return eawbCollectpaymentforgoods;
    }

    public void setEawbCollectpaymentforgoods(BigDecimal eawbCollectpaymentforgoods) {
        this.eawbCollectpaymentforgoods = eawbCollectpaymentforgoods;
    }

    public String getEawbSelfinsurance() {
        return eawbSelfinsurance;
    }

    public void setEawbSelfinsurance(String eawbSelfinsurance) {
        this.eawbSelfinsurance = eawbSelfinsurance == null ? null : eawbSelfinsurance.trim();
    }

    public Date getEawbKeyentrytime() {
        return eawbKeyentrytime;
    }

    public void setEawbKeyentrytime(Date eawbKeyentrytime) {
        this.eawbKeyentrytime = eawbKeyentrytime;
    }

    public String getEawbPreservation() {
        return eawbPreservation;
    }

    public void setEawbPreservation(String eawbPreservation) {
        this.eawbPreservation = eawbPreservation == null ? null : eawbPreservation.trim();
    }

    public String getEawbInsuranceservice() {
        return eawbInsuranceservice;
    }

    public void setEawbInsuranceservice(String eawbInsuranceservice) {
        this.eawbInsuranceservice = eawbInsuranceservice == null ? null : eawbInsuranceservice.trim();
    }

    public String getEawbAgentCode() {
        return eawbAgentCode;
    }

    public void setEawbAgentCode(String eawbAgentCode) {
        this.eawbAgentCode = eawbAgentCode == null ? null : eawbAgentCode.trim();
    }

    public String getEawbBusinessmode() {
        return eawbBusinessmode;
    }

    public void setEawbBusinessmode(String eawbBusinessmode) {
        this.eawbBusinessmode = eawbBusinessmode == null ? null : eawbBusinessmode.trim();
    }

    public BigDecimal getEawbPreFreightprice() {
        return eawbPreFreightprice;
    }

    public void setEawbPreFreightprice(BigDecimal eawbPreFreightprice) {
        this.eawbPreFreightprice = eawbPreFreightprice;
    }

    public String getEawbSendvoicerequest() {
        return eawbSendvoicerequest;
    }

    public void setEawbSendvoicerequest(String eawbSendvoicerequest) {
        this.eawbSendvoicerequest = eawbSendvoicerequest == null ? null : eawbSendvoicerequest.trim();
    }

    public String getCbcShipCode() {
        return cbcShipCode;
    }

    public void setCbcShipCode(String cbcShipCode) {
        this.cbcShipCode = cbcShipCode == null ? null : cbcShipCode.trim();
    }

    public String getEawbRoute() {
        return eawbRoute;
    }

    public void setEawbRoute(String eawbRoute) {
        this.eawbRoute = eawbRoute == null ? null : eawbRoute.trim();
    }

    public String getEawbDepartcity() {
        return eawbDepartcity;
    }

    public void setEawbDepartcity(String eawbDepartcity) {
        this.eawbDepartcity = eawbDepartcity == null ? null : eawbDepartcity.trim();
    }

    public String getEawbDepartstate() {
        return eawbDepartstate;
    }

    public void setEawbDepartstate(String eawbDepartstate) {
        this.eawbDepartstate = eawbDepartstate == null ? null : eawbDepartstate.trim();
    }

    public String getEawbDepartcountry() {
        return eawbDepartcountry;
    }

    public void setEawbDepartcountry(String eawbDepartcountry) {
        this.eawbDepartcountry = eawbDepartcountry == null ? null : eawbDepartcountry.trim();
    }

    public String getEawbDestcity() {
        return eawbDestcity;
    }

    public void setEawbDestcity(String eawbDestcity) {
        this.eawbDestcity = eawbDestcity == null ? null : eawbDestcity.trim();
    }

    public String getEawbDeststate() {
        return eawbDeststate;
    }

    public void setEawbDeststate(String eawbDeststate) {
        this.eawbDeststate = eawbDeststate == null ? null : eawbDeststate.trim();
    }

    public String getEawbDestcountry() {
        return eawbDestcountry;
    }

    public void setEawbDestcountry(String eawbDestcountry) {
        this.eawbDestcountry = eawbDestcountry == null ? null : eawbDestcountry.trim();
    }

    public String getEawbType() {
        return eawbType;
    }

    public void setEawbType(String eawbType) {
        this.eawbType = eawbType == null ? null : eawbType.trim();
    }

    public String getEawbCustprodname() {
        return eawbCustprodname;
    }

    public void setEawbCustprodname(String eawbCustprodname) {
        this.eawbCustprodname = eawbCustprodname;
    }

    public Integer getEawbQuantity() {
        return eawbQuantity;
    }

    public void setEawbQuantity(Integer eawbQuantity) {
        this.eawbQuantity = eawbQuantity;
    }

    public BigDecimal getEawbCustdeclval() {
        return eawbCustdeclval;
    }

    public void setEawbCustdeclval(BigDecimal eawbCustdeclval) {
        this.eawbCustdeclval = eawbCustdeclval;
    }

    public String getEawbCustcurrency() {
        return eawbCustcurrency;
    }

    public void setEawbCustcurrency(String eawbCustcurrency) {
        this.eawbCustcurrency = eawbCustcurrency == null ? null : eawbCustcurrency.trim();
    }

    public String getEawbDeclcurrency() {
        return eawbDeclcurrency;
    }

    public void setEawbDeclcurrency(String eawbDeclcurrency) {
        this.eawbDeclcurrency = eawbDeclcurrency == null ? null : eawbDeclcurrency.trim();
    }

    public String getEawbConsignmentno() {
        return eawbConsignmentno;
    }

    public void setEawbConsignmentno(String eawbConsignmentno) {
        this.eawbConsignmentno = eawbConsignmentno == null ? null : eawbConsignmentno.trim();
    }

    public String getEawbKjtype() {
        return eawbKjtype;
    }

    public void setEawbKjtype(String eawbKjtype) {
        this.eawbKjtype = eawbKjtype == null ? null : eawbKjtype.trim();
    }

    public String getEawbIetype() {
        return eawbIetype;
    }

    public void setEawbIetype(String eawbIetype) {
        this.eawbIetype = eawbIetype == null ? null : eawbIetype.trim();
    }

    public String getbCode() {
        return bCode;
    }

    public void setbCode(String bCode) {
        this.bCode = bCode == null ? null : bCode.trim();
    }

    public BigDecimal getEawbFuelcharge() {
        return eawbFuelcharge;
    }

    public void setEawbFuelcharge(BigDecimal eawbFuelcharge) {
        this.eawbFuelcharge = eawbFuelcharge;
    }

    public String getEawbInsutype() {
        return eawbInsutype;
    }

    public void setEawbInsutype(String eawbInsutype) {
        this.eawbInsutype = eawbInsutype == null ? null : eawbInsutype.trim();
    }

    public String getEawbInsuamounttype() {
        return eawbInsuamounttype;
    }

    public void setEawbInsuamounttype(String eawbInsuamounttype) {
        this.eawbInsuamounttype = eawbInsuamounttype == null ? null : eawbInsuamounttype.trim();
    }

    public BigDecimal getEawbInsuamount() {
        return eawbInsuamount;
    }

    public void setEawbInsuamount(BigDecimal eawbInsuamount) {
        this.eawbInsuamount = eawbInsuamount;
    }

    public BigDecimal getEawbPreFuelprice() {
        return eawbPreFuelprice;
    }

    public void setEawbPreFuelprice(BigDecimal eawbPreFuelprice) {
        this.eawbPreFuelprice = eawbPreFuelprice;
    }

    public String getHandsetCargo() {
        return handsetCargo;
    }

    public void setHandsetCargo(String handsetCargo) {
        this.handsetCargo = handsetCargo == null ? null : handsetCargo.trim();
    }

    public String getInfoxml() {
        return infoxml;
    }

    public void setInfoxml(String infoxml) {
        this.infoxml = infoxml == null ? null : infoxml.trim();
    }

    public BigDecimal getEawbDiscountvalue() {
        return eawbDiscountvalue;
    }

    public void setEawbDiscountvalue(BigDecimal eawbDiscountvalue) {
        this.eawbDiscountvalue = eawbDiscountvalue;
    }

    public String getEawbTransmodeid() {
        return eawbTransmodeid;
    }

    public void setEawbTransmodeid(String eawbTransmodeid) {
        this.eawbTransmodeid = eawbTransmodeid == null ? null : eawbTransmodeid.trim();
    }

    public String getEawbProducttype() {
        return eawbProducttype;
    }

    public void setEawbProducttype(String eawbProducttype) {
        this.eawbProducttype = eawbProducttype == null ? null : eawbProducttype.trim();
    }

    public String getEawbOrigincity() {
        return eawbOrigincity;
    }

    public void setEawbOrigincity(String eawbOrigincity) {
        this.eawbOrigincity = eawbOrigincity == null ? null : eawbOrigincity.trim();
    }

    public String getEawbShipperCaccountname() {
        return eawbShipperCaccountname;
    }

    public void setEawbShipperCaccountname(String eawbShipperCaccountname) {
        this.eawbShipperCaccountname = eawbShipperCaccountname == null ? null : eawbShipperCaccountname.trim();
    }

    public String getEawbDeliverFax() {
        return eawbDeliverFax;
    }

    public void setEawbDeliverFax(String eawbDeliverFax) {
        this.eawbDeliverFax = eawbDeliverFax == null ? null : eawbDeliverFax.trim();
    }

    public String getEawbSpecification() {
        return eawbSpecification;
    }

    public void setEawbSpecification(String eawbSpecification) {
        this.eawbSpecification = eawbSpecification == null ? null : eawbSpecification.trim();
    }

    public String getEawbCctype() {
        return eawbCctype;
    }

    public void setEawbCctype(String eawbCctype) {
        this.eawbCctype = eawbCctype == null ? null : eawbCctype.trim();
    }

    public String getEawbCustregistrationcode() {
        return eawbCustregistrationcode;
    }

    public void setEawbCustregistrationcode(String eawbCustregistrationcode) {
        this.eawbCustregistrationcode = eawbCustregistrationcode == null ? null : eawbCustregistrationcode.trim();
    }

    public String getEawbCustregistrationame() {
        return eawbCustregistrationame;
    }

    public void setEawbCustregistrationame(String eawbCustregistrationame) {
        this.eawbCustregistrationame = eawbCustregistrationame == null ? null : eawbCustregistrationame.trim();
    }

    public String getEawbEntrustcode() {
        return eawbEntrustcode;
    }

    public void setEawbEntrustcode(String eawbEntrustcode) {
        this.eawbEntrustcode = eawbEntrustcode == null ? null : eawbEntrustcode.trim();
    }

    public String getEawbHscode() {
        return eawbHscode;
    }

    public void setEawbHscode(String eawbHscode) {
        this.eawbHscode = eawbHscode == null ? null : eawbHscode.trim();
    }

    public String getEawbCustprodenname() {
        return eawbCustprodenname;
    }

    public void setEawbCustprodenname(String eawbCustprodenname) {
        this.eawbCustprodenname = eawbCustprodenname;
    }

    public String getEawbOperatestatus() {
        return eawbOperatestatus;
    }

    public void setEawbOperatestatus(String eawbOperatestatus) {
        this.eawbOperatestatus = eawbOperatestatus == null ? null : eawbOperatestatus.trim();
    }

    public String getEawbUnit() {
        return eawbUnit;
    }

    public void setEawbUnit(String eawbUnit) {
        this.eawbUnit = eawbUnit == null ? null : eawbUnit.trim();
    }

    public String getEawbUnitcode() {
        return eawbUnitcode;
    }

    public void setEawbUnitcode(String eawbUnitcode) {
        this.eawbUnitcode = eawbUnitcode == null ? null : eawbUnitcode.trim();
    }

    public String getEawbCheckstatus() {
        return eawbCheckstatus;
    }

    public void setEawbCheckstatus(String eawbCheckstatus) {
        this.eawbCheckstatus = eawbCheckstatus == null ? null : eawbCheckstatus.trim();
    }

    public String getEawbServicetype() {
        return eawbServicetype;
    }

    public void setEawbServicetype(String eawbServicetype) {
        this.eawbServicetype = eawbServicetype == null ? null : eawbServicetype.trim();
    }

    public String getMawbCode() {
        return mawbCode;
    }

    public void setMawbCode(String mawbCode) {
        this.mawbCode = mawbCode == null ? null : mawbCode.trim();
    }

    public String getFlightNo() {
        return flightNo;
    }

    public void setFlightNo(String flightNo) {
        this.flightNo = flightNo == null ? null : flightNo.trim();
    }

    public String getCustomType() {
        return customType;
    }

    public void setCustomType(String customType) {
        this.customType = customType == null ? null : customType.trim();
    }

    public String getEawbEcommerce() {
        return eawbEcommerce;
    }

    public void setEawbEcommerce(String eawbEcommerce) {
        this.eawbEcommerce = eawbEcommerce == null ? null : eawbEcommerce.trim();
    }

    public String getEawbTaxcode() {
        return eawbTaxcode;
    }

    public void setEawbTaxcode(String eawbTaxcode) {
        this.eawbTaxcode = eawbTaxcode == null ? null : eawbTaxcode.trim();
    }

    public String getEawbDeliverIndentitycardno() {
        return eawbDeliverIndentitycardno;
    }

    public void setEawbDeliverIndentitycardno(String eawbDeliverIndentitycardno) {
        this.eawbDeliverIndentitycardno = eawbDeliverIndentitycardno == null ? null : eawbDeliverIndentitycardno.trim();
    }

    public String getCustomsStatus() {
        return customsStatus;
    }

    public void setCustomsStatus(String customsStatus) {
        this.customsStatus = customsStatus == null ? null : customsStatus.trim();
    }

    public BigDecimal getCsefSyscode() {
        return csefSyscode;
    }

    public void setCsefSyscode(BigDecimal csefSyscode) {
        this.csefSyscode = csefSyscode;
    }

    public BigDecimal getEtcopy() {
        return etcopy;
    }

    public void setEtcopy(BigDecimal etcopy) {
        this.etcopy = etcopy;
    }

    public String getEawbTrack() {
        return eawbTrack;
    }

    public void setEawbTrack(String eawbTrack) {
        this.eawbTrack = eawbTrack == null ? null : eawbTrack.trim();
    }

    public String getEawbDeliverEmail() {
        return eawbDeliverEmail;
    }

    public void setEawbDeliverEmail(String eawbDeliverEmail) {
        this.eawbDeliverEmail = eawbDeliverEmail == null ? null : eawbDeliverEmail.trim();
    }

    public String getEawbPartition() {
        return eawbPartition;
    }

    public void setEawbPartition(String eawbPartition) {
        this.eawbPartition = eawbPartition == null ? null : eawbPartition.trim();
    }

    public String getEawbDeliveryPostcodeCorrect() {
        return eawbDeliveryPostcodeCorrect;
    }

    public void setEawbDeliveryPostcodeCorrect(String eawbDeliveryPostcodeCorrect) {
        this.eawbDeliveryPostcodeCorrect = eawbDeliveryPostcodeCorrect == null ? null : eawbDeliveryPostcodeCorrect.trim();
    }

    public String getEawbChangeLabelStatus() {
        return eawbChangeLabelStatus;
    }

    public void setEawbChangeLabelStatus(String eawbChangeLabelStatus) {
        this.eawbChangeLabelStatus = eawbChangeLabelStatus == null ? null : eawbChangeLabelStatus.trim();
    }

    public String getEawbRefundWangwangId() {
        return eawbRefundWangwangId;
    }

    public void setEawbRefundWangwangId(String eawbRefundWangwangId) {
        this.eawbRefundWangwangId = eawbRefundWangwangId == null ? null : eawbRefundWangwangId.trim();
    }

    public String getEawbRefundName() {
        return eawbRefundName;
    }

    public void setEawbRefundName(String eawbRefundName) {
        this.eawbRefundName = eawbRefundName == null ? null : eawbRefundName.trim();
    }

    public String getEawbRefundPhone() {
        return eawbRefundPhone;
    }

    public void setEawbRefundPhone(String eawbRefundPhone) {
        this.eawbRefundPhone = eawbRefundPhone == null ? null : eawbRefundPhone.trim();
    }

    public String getEawbRefundMobile() {
        return eawbRefundMobile;
    }

    public void setEawbRefundMobile(String eawbRefundMobile) {
        this.eawbRefundMobile = eawbRefundMobile == null ? null : eawbRefundMobile.trim();
    }

    public String getEawbRefundEmail() {
        return eawbRefundEmail;
    }

    public void setEawbRefundEmail(String eawbRefundEmail) {
        this.eawbRefundEmail = eawbRefundEmail == null ? null : eawbRefundEmail.trim();
    }

    public String getEawbRefundCountry() {
        return eawbRefundCountry;
    }

    public void setEawbRefundCountry(String eawbRefundCountry) {
        this.eawbRefundCountry = eawbRefundCountry == null ? null : eawbRefundCountry.trim();
    }

    public String getEawbRefundPrivince() {
        return eawbRefundPrivince;
    }

    public void setEawbRefundPrivince(String eawbRefundPrivince) {
        this.eawbRefundPrivince = eawbRefundPrivince == null ? null : eawbRefundPrivince.trim();
    }

    public String getEawbRefundCity() {
        return eawbRefundCity;
    }

    public void setEawbRefundCity(String eawbRefundCity) {
        this.eawbRefundCity = eawbRefundCity == null ? null : eawbRefundCity.trim();
    }

    public String getEawbRefundDistrict() {
        return eawbRefundDistrict;
    }

    public void setEawbRefundDistrict(String eawbRefundDistrict) {
        this.eawbRefundDistrict = eawbRefundDistrict == null ? null : eawbRefundDistrict.trim();
    }

    public String getEawbRefundStreet() {
        return eawbRefundStreet;
    }

    public void setEawbRefundStreet(String eawbRefundStreet) {
        this.eawbRefundStreet = eawbRefundStreet == null ? null : eawbRefundStreet.trim();
    }

    public String getEawbRefundZipcode() {
        return eawbRefundZipcode;
    }

    public void setEawbRefundZipcode(String eawbRefundZipcode) {
        this.eawbRefundZipcode = eawbRefundZipcode == null ? null : eawbRefundZipcode.trim();
    }

    public String getEawbUndeliveryOption() {
        return eawbUndeliveryOption;
    }

    public void setEawbUndeliveryOption(String eawbUndeliveryOption) {
        this.eawbUndeliveryOption = eawbUndeliveryOption == null ? null : eawbUndeliveryOption.trim();
    }

    public String getEawbCarriername() {
        return eawbCarriername;
    }

    public void setEawbCarriername(String eawbCarriername) {
        this.eawbCarriername = eawbCarriername == null ? null : eawbCarriername.trim();
    }

    public String getEawbCarrierno() {
        return eawbCarrierno;
    }

    public void setEawbCarrierno(String eawbCarrierno) {
        this.eawbCarrierno = eawbCarrierno == null ? null : eawbCarrierno.trim();
    }

    public String getOrderCodeIn() {
        return orderCodeIn;
    }

    public void setOrderCodeIn(String orderCodeIn) {
        this.orderCodeIn = orderCodeIn == null ? null : orderCodeIn.trim();
    }

    public Long getOrderSyscodeIn() {
        return orderSyscodeIn;
    }

    public void setOrderSyscodeIn(Long orderSyscodeIn) {
        this.orderSyscodeIn = orderSyscodeIn;
    }

    public String getOrderCodeOut() {
        return orderCodeOut;
    }

    public void setOrderCodeOut(String orderCodeOut) {
        this.orderCodeOut = orderCodeOut == null ? null : orderCodeOut.trim();
    }

    public Long getOrderSyscodeOut() {
        return orderSyscodeOut;
    }

    public void setOrderSyscodeOut(Long orderSyscodeOut) {
        this.orderSyscodeOut = orderSyscodeOut;
    }

    public String getCustomerOrderCode() {
        return customerOrderCode;
    }

    public void setCustomerOrderCode(String customerOrderCode) {
        this.customerOrderCode = customerOrderCode == null ? null : customerOrderCode.trim();
    }

    public String getEawbServicetypeOriginal() {
        return eawbServicetypeOriginal;
    }

    public void setEawbServicetypeOriginal(String eawbServicetypeOriginal) {
        this.eawbServicetypeOriginal = eawbServicetypeOriginal == null ? null : eawbServicetypeOriginal.trim();
    }

    public BigDecimal getEawbLength() {
        return eawbLength;
    }

    public void setEawbLength(BigDecimal eawbLength) {
        this.eawbLength = eawbLength;
    }

    public BigDecimal getEawbWidth() {
        return eawbWidth;
    }

    public void setEawbWidth(BigDecimal eawbWidth) {
        this.eawbWidth = eawbWidth;
    }

    public BigDecimal getEawbHeight() {
        return eawbHeight;
    }

    public void setEawbHeight(BigDecimal eawbHeight) {
        this.eawbHeight = eawbHeight;
    }

    public String getEawbDeliverMobile() {
        return eawbDeliverMobile;
    }

    public void setEawbDeliverMobile(String eawbDeliverMobile) {
        this.eawbDeliverMobile = eawbDeliverMobile == null ? null : eawbDeliverMobile.trim();
    }

    public String getEawbCod() {
        return eawbCod;
    }

    public void setEawbCod(String eawbCod) {
        this.eawbCod = eawbCod == null ? null : eawbCod.trim();
    }

    public BigDecimal getEawbCodvalue() {
        return eawbCodvalue;
    }

    public void setEawbCodvalue(BigDecimal eawbCodvalue) {
        this.eawbCodvalue = eawbCodvalue;
    }

    public String getEawbCodcurrency() {
        return eawbCodcurrency;
    }

    public void setEawbCodcurrency(String eawbCodcurrency) {
        this.eawbCodcurrency = eawbCodcurrency == null ? null : eawbCodcurrency.trim();
    }

    public String getEawbRefundReference() {
        return eawbRefundReference;
    }

    public void setEawbRefundReference(String eawbRefundReference) {
        this.eawbRefundReference = eawbRefundReference == null ? null : eawbRefundReference.trim();
    }

    public String getEawbTransmodeidOriginal() {
        return eawbTransmodeidOriginal;
    }

    public void setEawbTransmodeidOriginal(String eawbTransmodeidOriginal) {
        this.eawbTransmodeidOriginal = eawbTransmodeidOriginal == null ? null : eawbTransmodeidOriginal.trim();
    }

    public String getEawbRefundSupplier() {
        return eawbRefundSupplier;
    }

    public void setEawbRefundSupplier(String eawbRefundSupplier) {
        this.eawbRefundSupplier = eawbRefundSupplier == null ? null : eawbRefundSupplier.trim();
    }

    public String getEawbRefundReference1() {
        return eawbRefundReference1;
    }

    public void setEawbRefundReference1(String eawbRefundReference1) {
        this.eawbRefundReference1 = eawbRefundReference1 == null ? null : eawbRefundReference1.trim();
    }

    public String getEawbRefundReference2() {
        return eawbRefundReference2;
    }

    public void setEawbRefundReference2(String eawbRefundReference2) {
        this.eawbRefundReference2 = eawbRefundReference2 == null ? null : eawbRefundReference2.trim();
    }

    public String getEawbRefundReference3() {
        return eawbRefundReference3;
    }

    public void setEawbRefundReference3(String eawbRefundReference3) {
        this.eawbRefundReference3 = eawbRefundReference3 == null ? null : eawbRefundReference3.trim();
    }

    public String getEawbRefundAddress() {
        return eawbRefundAddress;
    }

    public void setEawbRefundAddress(String eawbRefundAddress) {
        this.eawbRefundAddress = eawbRefundAddress == null ? null : eawbRefundAddress.trim();
    }

    public String getRefundStatus() {
        return refundStatus;
    }

    public void setRefundStatus(String refundStatus) {
        this.refundStatus = refundStatus == null ? null : refundStatus.trim();
    }

    public String getEawbDeliverStreet() {
        return eawbDeliverStreet;
    }

    public void setEawbDeliverStreet(String eawbDeliverStreet) {
        this.eawbDeliverStreet = eawbDeliverStreet == null ? null : eawbDeliverStreet.trim();
    }

    public String getEawbNextOptCenter() {
        return eawbNextOptCenter;
    }

    public void setEawbNextOptCenter(String eawbNextOptCenter) {
        this.eawbNextOptCenter = eawbNextOptCenter == null ? null : eawbNextOptCenter.trim();
    }

    public String getEawbPreOptCenter() {
        return eawbPreOptCenter;
    }

    public void setEawbPreOptCenter(String eawbPreOptCenter) {
        this.eawbPreOptCenter = eawbPreOptCenter == null ? null : eawbPreOptCenter.trim();
    }

    public String getEawbTrunkCode() {
        return eawbTrunkCode;
    }

    public void setEawbTrunkCode(String eawbTrunkCode) {
        this.eawbTrunkCode = eawbTrunkCode == null ? null : eawbTrunkCode.trim();
    }

    public String getEawbChannelCode() {
        return eawbChannelCode;
    }

    public void setEawbChannelCode(String eawbChannelCode) {
        this.eawbChannelCode = eawbChannelCode == null ? null : eawbChannelCode.trim();
    }

    public String getEawbDeliverDistrict() {
        return eawbDeliverDistrict;
    }

    public void setEawbDeliverDistrict(String eawbDeliverDistrict) {
        this.eawbDeliverDistrict = eawbDeliverDistrict == null ? null : eawbDeliverDistrict.trim();
    }

    public String getEawbPickupDistrict() {
        return eawbPickupDistrict;
    }

    public void setEawbPickupDistrict(String eawbPickupDistrict) {
        this.eawbPickupDistrict = eawbPickupDistrict == null ? null : eawbPickupDistrict.trim();
    }

    public String getEawbSenderAddress() {
        return eawbSenderAddress;
    }

    public void setEawbSenderAddress(String eawbSenderAddress) {
        this.eawbSenderAddress = eawbSenderAddress == null ? null : eawbSenderAddress.trim();
    }

    public String getEawbSortcode() {
        return eawbSortcode;
    }

    public void setEawbSortcode(String eawbSortcode) {
        this.eawbSortcode = eawbSortcode == null ? null : eawbSortcode.trim();
    }

    public String getEawbConsigneeLatitude() {
        return eawbConsigneeLatitude;
    }

    public void setEawbConsigneeLatitude(String eawbConsigneeLatitude) {
        this.eawbConsigneeLatitude = eawbConsigneeLatitude == null ? null : eawbConsigneeLatitude.trim();
    }

    public String getEawbConsigneeLongitude() {
        return eawbConsigneeLongitude;
    }

    public void setEawbConsigneeLongitude(String eawbConsigneeLongitude) {
        this.eawbConsigneeLongitude = eawbConsigneeLongitude == null ? null : eawbConsigneeLongitude.trim();
    }

    public String getEawbFirstmile() {
        return eawbFirstmile;
    }

    public void setEawbFirstmile(String eawbFirstmile) {
        this.eawbFirstmile = eawbFirstmile == null ? null : eawbFirstmile.trim();
    }

    public String getEawbPaymentid() {
        return eawbPaymentid;
    }

    public void setEawbPaymentid(String eawbPaymentid) {
        this.eawbPaymentid = eawbPaymentid == null ? null : eawbPaymentid.trim();
    }

    public String getEawbPaymentemail() {
        return eawbPaymentemail;
    }

    public void setEawbPaymentemail(String eawbPaymentemail) {
        this.eawbPaymentemail = eawbPaymentemail == null ? null : eawbPaymentemail.trim();
    }

    public String getEawbPaymentcontactname() {
        return eawbPaymentcontactname;
    }

    public void setEawbPaymentcontactname(String eawbPaymentcontactname) {
        this.eawbPaymentcontactname = eawbPaymentcontactname == null ? null : eawbPaymentcontactname.trim();
    }

    public String getEawbPaymentphonenumber() {
        return eawbPaymentphonenumber;
    }

    public void setEawbPaymentphonenumber(String eawbPaymentphonenumber) {
        this.eawbPaymentphonenumber = eawbPaymentphonenumber == null ? null : eawbPaymentphonenumber.trim();
    }

    public String getEawbSellertaxnumber() {
        return eawbSellertaxnumber;
    }

    public void setEawbSellertaxnumber(String eawbSellertaxnumber) {
        this.eawbSellertaxnumber = eawbSellertaxnumber == null ? null : eawbSellertaxnumber.trim();
    }

    public String getEawbInvoicenumber() {
        return eawbInvoicenumber;
    }

    public void setEawbInvoicenumber(String eawbInvoicenumber) {
        this.eawbInvoicenumber = eawbInvoicenumber == null ? null : eawbInvoicenumber.trim();
    }

    public String getEawbProducturl() {
        return eawbProducturl;
    }

    public void setEawbProducturl(String eawbProducturl) {
        this.eawbProducturl = eawbProducturl == null ? null : eawbProducturl.trim();
    }
}