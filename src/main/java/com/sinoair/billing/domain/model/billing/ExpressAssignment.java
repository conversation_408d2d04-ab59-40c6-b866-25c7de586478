package com.sinoair.billing.domain.model.billing;

import java.math.BigDecimal;
import java.util.Date;

public class ExpressAssignment {
    private Integer eaSyscode;

    private String eaCode;

    private String flightNumber;

    private String oriSacId;

    private Integer eaTotalEawbs;

    private Integer eaTotalMawbs;

    private BigDecimal mawbPWeight;

    private BigDecimal mawbCWeight;

    private Date flightDate;

    private Date eta;

    private String destSacId;

    private String remark;

    private Date eaHandletime;

    private String eaStatus;

    private String transmodeid;

    private String sacId;

    private String eawbIetype;

    public Integer getEaSyscode() {
        return eaSyscode;
    }

    public void setEaSyscode(Integer eaSyscode) {
        this.eaSyscode = eaSyscode;
    }

    public void setEaTotalMawbs(Integer eaTotalMawbs) {
        this.eaTotalMawbs = eaTotalMawbs;
    }

    public String getEaCode() {
        return eaCode;
    }

    public void setEaCode(String eaCode) {
        this.eaCode = eaCode == null ? null : eaCode.trim();
    }

    public String getFlightNumber() {
        return flightNumber;
    }

    public void setFlightNumber(String flightNumber) {
        this.flightNumber = flightNumber == null ? null : flightNumber.trim();
    }

    public String getOriSacId() {
        return oriSacId;
    }

    public void setOriSacId(String oriSacId) {
        this.oriSacId = oriSacId == null ? null : oriSacId.trim();
    }

    public Integer getEaTotalEawbs() {
        return eaTotalEawbs;
    }

    public void setEaTotalEawbs(Integer eaTotalEawbs) {
        this.eaTotalEawbs = eaTotalEawbs;
    }

    public BigDecimal getMawbPWeight() {
        return mawbPWeight;
    }

    public void setMawbPWeight(BigDecimal mawbPWeight) {
        this.mawbPWeight = mawbPWeight;
    }

    public BigDecimal getMawbCWeight() {
        return mawbCWeight;
    }

    public void setMawbCWeight(BigDecimal mawbCWeight) {
        this.mawbCWeight = mawbCWeight;
    }

    public Date getFlightDate() {
        return flightDate;
    }

    public void setFlightDate(Date flightDate) {
        this.flightDate = flightDate;
    }

    public Date getEta() {
        return eta;
    }

    public void setEta(Date eta) {
        this.eta = eta;
    }

    public String getDestSacId() {
        return destSacId;
    }

    public void setDestSacId(String destSacId) {
        this.destSacId = destSacId == null ? null : destSacId.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public Date getEaHandletime() {
        return eaHandletime;
    }

    public void setEaHandletime(Date eaHandletime) {
        this.eaHandletime = eaHandletime;
    }

    public String getEaStatus() {
        return eaStatus;
    }

    public void setEaStatus(String eaStatus) {
        this.eaStatus = eaStatus == null ? null : eaStatus.trim();
    }

    public String getTransmodeid() {
        return transmodeid;
    }

    public void setTransmodeid(String transmodeid) {
        this.transmodeid = transmodeid == null ? null : transmodeid.trim();
    }

    public String getSacId() {
        return sacId;
    }

    public void setSacId(String sacId) {
        this.sacId = sacId == null ? null : sacId.trim();
    }

    public String getEawbIetype() {
        return eawbIetype;
    }

    public void setEawbIetype(String eawbIetype) {
        this.eawbIetype = eawbIetype;
    }
}