package com.sinoair.billing.domain.model.billing;

import java.math.BigDecimal;
import java.util.Date;

public class ExpressAssignmentActual {
    private Long eaaSyscode;

    private String eaaCode;

    private String flightNumber;

    private String handler;

    private String oriSacId;

    private Integer eaaTotalEawbs;

    private Integer eaaTotalMawbs;

    private BigDecimal mawbPWeight;

    private BigDecimal mawbCWeight;

    private Date etdDate;

    private Date eaaCreatetime;

    private Date eaaPushtime;

    private String destSacId;

    private Date eaaHandletime;

    private String eaaStatus;

    private String eaaPushstatus;

    private String transmodeid;

    private String sacId;

    private String transportType;

    private String eaaRemark;

    private String eaaFlightType;

    private Date etaDate;

    private Long mawbVolumn;

    private String mawbUldtype;

    private String exportclearancecity;

    private String agent;

    private String foreignclearancesacId;

    private Date eaaInsertpushtime;

    private String eaaInsertpushstatus;

    private String eaaClearancetype;

    private String firstFlightNumber;

    private String firstOriSacId;

    private String firstDestSacId;

    private Date firstEtd;

    private Date firstEta;

    private String secondFlightNumber;

    private String secondOriSacId;

    private String secondDestSacId;

    private Date secondEtd;

    private Date secondEta;

    private String thirdFlightNumber;

    private String thirdOriSacId;

    private String thirdDestSacId;

    private Date thirdEtd;

    private Date thirdEta;

    private String aircrafttype;

    private String freightagent;

    private String terminalcode;

    private Date eaaAsstime;

    private String airlinecode;

    private String eaaComment;

    private Date std;

    private String soCode;

    private String iscndirect;

    private BigDecimal eaaAmount;

    public Long getEaaSyscode() {
        return eaaSyscode;
    }

    public void setEaaSyscode(Long eaaSyscode) {
        this.eaaSyscode = eaaSyscode;
    }

    public String getEaaCode() {
        return eaaCode;
    }

    public void setEaaCode(String eaaCode) {
        this.eaaCode = eaaCode == null ? null : eaaCode.trim();
    }

    public String getFlightNumber() {
        return flightNumber;
    }

    public void setFlightNumber(String flightNumber) {
        this.flightNumber = flightNumber == null ? null : flightNumber.trim();
    }

    public String getHandler() {
        return handler;
    }

    public void setHandler(String handler) {
        this.handler = handler == null ? null : handler.trim();
    }

    public String getOriSacId() {
        return oriSacId;
    }

    public void setOriSacId(String oriSacId) {
        this.oriSacId = oriSacId == null ? null : oriSacId.trim();
    }

    public Integer getEaaTotalEawbs() {
        return eaaTotalEawbs;
    }

    public void setEaaTotalEawbs(Integer eaaTotalEawbs) {
        this.eaaTotalEawbs = eaaTotalEawbs;
    }

    public Integer getEaaTotalMawbs() {
        return eaaTotalMawbs;
    }

    public void setEaaTotalMawbs(Integer eaaTotalMawbs) {
        this.eaaTotalMawbs = eaaTotalMawbs;
    }

    public BigDecimal getMawbPWeight() {
        return mawbPWeight;
    }

    public void setMawbPWeight(BigDecimal mawbPWeight) {
        this.mawbPWeight = mawbPWeight;
    }

    public BigDecimal getMawbCWeight() {
        return mawbCWeight;
    }

    public void setMawbCWeight(BigDecimal mawbCWeight) {
        this.mawbCWeight = mawbCWeight;
    }

    public Date getEtdDate() {
        return etdDate;
    }

    public void setEtdDate(Date etdDate) {
        this.etdDate = etdDate;
    }

    public Date getEaaCreatetime() {
        return eaaCreatetime;
    }

    public void setEaaCreatetime(Date eaaCreatetime) {
        this.eaaCreatetime = eaaCreatetime;
    }

    public Date getEaaPushtime() {
        return eaaPushtime;
    }

    public void setEaaPushtime(Date eaaPushtime) {
        this.eaaPushtime = eaaPushtime;
    }

    public String getDestSacId() {
        return destSacId;
    }

    public void setDestSacId(String destSacId) {
        this.destSacId = destSacId == null ? null : destSacId.trim();
    }

    public Date getEaaHandletime() {
        return eaaHandletime;
    }

    public void setEaaHandletime(Date eaaHandletime) {
        this.eaaHandletime = eaaHandletime;
    }

    public String getEaaStatus() {
        return eaaStatus;
    }

    public void setEaaStatus(String eaaStatus) {
        this.eaaStatus = eaaStatus == null ? null : eaaStatus.trim();
    }

    public String getEaaPushstatus() {
        return eaaPushstatus;
    }

    public void setEaaPushstatus(String eaaPushstatus) {
        this.eaaPushstatus = eaaPushstatus == null ? null : eaaPushstatus.trim();
    }

    public String getTransmodeid() {
        return transmodeid;
    }

    public void setTransmodeid(String transmodeid) {
        this.transmodeid = transmodeid == null ? null : transmodeid.trim();
    }

    public String getSacId() {
        return sacId;
    }

    public void setSacId(String sacId) {
        this.sacId = sacId == null ? null : sacId.trim();
    }

    public String getTransportType() {
        return transportType;
    }

    public void setTransportType(String transportType) {
        this.transportType = transportType == null ? null : transportType.trim();
    }

    public String getEaaRemark() {
        return eaaRemark;
    }

    public void setEaaRemark(String eaaRemark) {
        this.eaaRemark = eaaRemark == null ? null : eaaRemark.trim();
    }

    public String getEaaFlightType() {
        return eaaFlightType;
    }

    public void setEaaFlightType(String eaaFlightType) {
        this.eaaFlightType = eaaFlightType == null ? null : eaaFlightType.trim();
    }

    public Date getEtaDate() {
        return etaDate;
    }

    public void setEtaDate(Date etaDate) {
        this.etaDate = etaDate;
    }

    public Long getMawbVolumn() {
        return mawbVolumn;
    }

    public void setMawbVolumn(Long mawbVolumn) {
        this.mawbVolumn = mawbVolumn;
    }

    public String getMawbUldtype() {
        return mawbUldtype;
    }

    public void setMawbUldtype(String mawbUldtype) {
        this.mawbUldtype = mawbUldtype == null ? null : mawbUldtype.trim();
    }

    public String getExportclearancecity() {
        return exportclearancecity;
    }

    public void setExportclearancecity(String exportclearancecity) {
        this.exportclearancecity = exportclearancecity == null ? null : exportclearancecity.trim();
    }

    public String getAgent() {
        return agent;
    }

    public void setAgent(String agent) {
        this.agent = agent == null ? null : agent.trim();
    }

    public String getForeignclearancesacId() {
        return foreignclearancesacId;
    }

    public void setForeignclearancesacId(String foreignclearancesacId) {
        this.foreignclearancesacId = foreignclearancesacId == null ? null : foreignclearancesacId.trim();
    }

    public Date getEaaInsertpushtime() {
        return eaaInsertpushtime;
    }

    public void setEaaInsertpushtime(Date eaaInsertpushtime) {
        this.eaaInsertpushtime = eaaInsertpushtime;
    }

    public String getEaaInsertpushstatus() {
        return eaaInsertpushstatus;
    }

    public void setEaaInsertpushstatus(String eaaInsertpushstatus) {
        this.eaaInsertpushstatus = eaaInsertpushstatus == null ? null : eaaInsertpushstatus.trim();
    }

    public String getEaaClearancetype() {
        return eaaClearancetype;
    }

    public void setEaaClearancetype(String eaaClearancetype) {
        this.eaaClearancetype = eaaClearancetype == null ? null : eaaClearancetype.trim();
    }

    public String getFirstFlightNumber() {
        return firstFlightNumber;
    }

    public void setFirstFlightNumber(String firstFlightNumber) {
        this.firstFlightNumber = firstFlightNumber == null ? null : firstFlightNumber.trim();
    }

    public String getFirstOriSacId() {
        return firstOriSacId;
    }

    public void setFirstOriSacId(String firstOriSacId) {
        this.firstOriSacId = firstOriSacId == null ? null : firstOriSacId.trim();
    }

    public String getFirstDestSacId() {
        return firstDestSacId;
    }

    public void setFirstDestSacId(String firstDestSacId) {
        this.firstDestSacId = firstDestSacId == null ? null : firstDestSacId.trim();
    }

    public Date getFirstEtd() {
        return firstEtd;
    }

    public void setFirstEtd(Date firstEtd) {
        this.firstEtd = firstEtd;
    }

    public Date getFirstEta() {
        return firstEta;
    }

    public void setFirstEta(Date firstEta) {
        this.firstEta = firstEta;
    }

    public String getSecondFlightNumber() {
        return secondFlightNumber;
    }

    public void setSecondFlightNumber(String secondFlightNumber) {
        this.secondFlightNumber = secondFlightNumber == null ? null : secondFlightNumber.trim();
    }

    public String getSecondOriSacId() {
        return secondOriSacId;
    }

    public void setSecondOriSacId(String secondOriSacId) {
        this.secondOriSacId = secondOriSacId == null ? null : secondOriSacId.trim();
    }

    public String getSecondDestSacId() {
        return secondDestSacId;
    }

    public void setSecondDestSacId(String secondDestSacId) {
        this.secondDestSacId = secondDestSacId == null ? null : secondDestSacId.trim();
    }

    public Date getSecondEtd() {
        return secondEtd;
    }

    public void setSecondEtd(Date secondEtd) {
        this.secondEtd = secondEtd;
    }

    public Date getSecondEta() {
        return secondEta;
    }

    public void setSecondEta(Date secondEta) {
        this.secondEta = secondEta;
    }

    public String getThirdFlightNumber() {
        return thirdFlightNumber;
    }

    public void setThirdFlightNumber(String thirdFlightNumber) {
        this.thirdFlightNumber = thirdFlightNumber == null ? null : thirdFlightNumber.trim();
    }

    public String getThirdOriSacId() {
        return thirdOriSacId;
    }

    public void setThirdOriSacId(String thirdOriSacId) {
        this.thirdOriSacId = thirdOriSacId == null ? null : thirdOriSacId.trim();
    }

    public String getThirdDestSacId() {
        return thirdDestSacId;
    }

    public void setThirdDestSacId(String thirdDestSacId) {
        this.thirdDestSacId = thirdDestSacId == null ? null : thirdDestSacId.trim();
    }

    public Date getThirdEtd() {
        return thirdEtd;
    }

    public void setThirdEtd(Date thirdEtd) {
        this.thirdEtd = thirdEtd;
    }

    public Date getThirdEta() {
        return thirdEta;
    }

    public void setThirdEta(Date thirdEta) {
        this.thirdEta = thirdEta;
    }

    public String getAircrafttype() {
        return aircrafttype;
    }

    public void setAircrafttype(String aircrafttype) {
        this.aircrafttype = aircrafttype == null ? null : aircrafttype.trim();
    }

    public String getFreightagent() {
        return freightagent;
    }

    public void setFreightagent(String freightagent) {
        this.freightagent = freightagent == null ? null : freightagent.trim();
    }

    public String getTerminalcode() {
        return terminalcode;
    }

    public void setTerminalcode(String terminalcode) {
        this.terminalcode = terminalcode == null ? null : terminalcode.trim();
    }

    public Date getEaaAsstime() {
        return eaaAsstime;
    }

    public void setEaaAsstime(Date eaaAsstime) {
        this.eaaAsstime = eaaAsstime;
    }

    public String getAirlinecode() {
        return airlinecode;
    }

    public void setAirlinecode(String airlinecode) {
        this.airlinecode = airlinecode == null ? null : airlinecode.trim();
    }

    public String getEaaComment() {
        return eaaComment;
    }

    public void setEaaComment(String eaaComment) {
        this.eaaComment = eaaComment == null ? null : eaaComment.trim();
    }

    public Date getStd() {
        return std;
    }

    public void setStd(Date std) {
        this.std = std;
    }

    public String getSoCode() {
        return soCode;
    }

    public void setSoCode(String soCode) {
        this.soCode = soCode == null ? null : soCode.trim();
    }

    public String getIscndirect() {
        return iscndirect;
    }

    public void setIscndirect(String iscndirect) {
        this.iscndirect = iscndirect;
    }

    public BigDecimal getEaaAmount() {
        return eaaAmount;
    }

    public void setEaaAmount(BigDecimal eaaAmount) {
        this.eaaAmount = eaaAmount;
    }
}