package com.sinoair.billing.domain.model.billing;

import java.math.BigDecimal;
import java.util.Date;

public class ExpressBusinessActivity {
    private BigDecimal ebaSyscode;

    private String eawbPrintcode;

    private Long eawbSyscode;

    private String eadCode;

    private String eastCode;

    private Integer ebaEIdHandler;

    private Date ebaHandletime;

    private String ebaRemark;

    private String sacId;

    private Date ebaOccurtime;

    private String ebaSource;

    private String ebaOccurplace;

    private String flag;

    private String qa;

    private String ukTrack;

    private String eatPartnerActivityCode;

    private String eatPartnerOrigin;

    private String eatPartnerId;

    private String ebaSacCode;

    private Date qaPushtime;

    private String qa2;

    private Date qa2Pushtime;

    private String qa3;

    private Date qa3Pushtime;

    private BigDecimal ceosEbaSyscode;

    private String soCode;
    private String mawbCode;

    private String eawbDestcountry;

    private String eawbServicetype;

    private String eawbSoCode;

    private String eawbDeliverPostcode;

    private String eawbServiceTypeOriginal;

    private BigDecimal billingSyscode;

    private String pEadcode;

    private String pEastcode;

    public String getpEadcode() {
        return pEadcode;
    }

    public void setpEadcode(String pEadcode) {
        this.pEadcode = pEadcode;
    }

    public String getpEastcode() {
        return pEastcode;
    }

    public void setpEastcode(String pEastcode) {
        this.pEastcode = pEastcode;
    }

    public BigDecimal getBillingSyscode() {
        return billingSyscode;
    }

    public void setBillingSyscode(BigDecimal billingSyscode) {
        this.billingSyscode = billingSyscode;
    }

    public BigDecimal getEbaSyscode() {
        return ebaSyscode;
    }

    public void setEbaSyscode(BigDecimal ebaSyscode) {
        this.ebaSyscode = ebaSyscode;
    }

    public String getEawbPrintcode() {
        return eawbPrintcode;
    }

    public void setEawbPrintcode(String eawbPrintcode) {
        this.eawbPrintcode = eawbPrintcode == null ? null : eawbPrintcode.trim();
    }

    public Long getEawbSyscode() {
        return eawbSyscode;
    }

    public void setEawbSyscode(Long eawbSyscode) {
        this.eawbSyscode = eawbSyscode;
    }

    public String getEadCode() {
        return eadCode;
    }

    public void setEadCode(String eadCode) {
        this.eadCode = eadCode == null ? null : eadCode.trim();
    }

    public String getEastCode() {
        return eastCode;
    }

    public void setEastCode(String eastCode) {
        this.eastCode = eastCode == null ? null : eastCode.trim();
    }

    public Integer getEbaEIdHandler() {
        return ebaEIdHandler;
    }

    public void setEbaEIdHandler(Integer ebaEIdHandler) {
        this.ebaEIdHandler = ebaEIdHandler;
    }

    public Date getEbaHandletime() {
        return ebaHandletime;
    }

    public void setEbaHandletime(Date ebaHandletime) {
        this.ebaHandletime = ebaHandletime;
    }

    public String getEbaRemark() {
        return ebaRemark;
    }

    public void setEbaRemark(String ebaRemark) {
        this.ebaRemark = ebaRemark == null ? null : ebaRemark.trim();
    }

    public String getSacId() {
        return sacId;
    }

    public void setSacId(String sacId) {
        this.sacId = sacId == null ? null : sacId.trim();
    }

    public Date getEbaOccurtime() {
        return ebaOccurtime;
    }

    public void setEbaOccurtime(Date ebaOccurtime) {
        this.ebaOccurtime = ebaOccurtime;
    }

    public String getEbaSource() {
        return ebaSource;
    }

    public void setEbaSource(String ebaSource) {
        this.ebaSource = ebaSource == null ? null : ebaSource.trim();
    }

    public String getEbaOccurplace() {
        return ebaOccurplace;
    }

    public void setEbaOccurplace(String ebaOccurplace) {
        this.ebaOccurplace = ebaOccurplace == null ? null : ebaOccurplace.trim();
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag == null ? null : flag.trim();
    }

    public String getQa() {
        return qa;
    }

    public void setQa(String qa) {
        this.qa = qa == null ? null : qa.trim();
    }

    public String getUkTrack() {
        return ukTrack;
    }

    public void setUkTrack(String ukTrack) {
        this.ukTrack = ukTrack == null ? null : ukTrack.trim();
    }

    public String getEatPartnerActivityCode() {
        return eatPartnerActivityCode;
    }

    public void setEatPartnerActivityCode(String eatPartnerActivityCode) {
        this.eatPartnerActivityCode = eatPartnerActivityCode == null ? null : eatPartnerActivityCode.trim();
    }

    public String getEatPartnerOrigin() {
        return eatPartnerOrigin;
    }

    public void setEatPartnerOrigin(String eatPartnerOrigin) {
        this.eatPartnerOrigin = eatPartnerOrigin == null ? null : eatPartnerOrigin.trim();
    }

    public String getEatPartnerId() {
        return eatPartnerId;
    }

    public void setEatPartnerId(String eatPartnerId) {
        this.eatPartnerId = eatPartnerId == null ? null : eatPartnerId.trim();
    }

    public String getEbaSacCode() {
        return ebaSacCode;
    }

    public void setEbaSacCode(String ebaSacCode) {
        this.ebaSacCode = ebaSacCode == null ? null : ebaSacCode.trim();
    }

    public Date getQaPushtime() {
        return qaPushtime;
    }

    public void setQaPushtime(Date qaPushtime) {
        this.qaPushtime = qaPushtime;
    }

    public String getQa2() {
        return qa2;
    }

    public void setQa2(String qa2) {
        this.qa2 = qa2;
    }

    public Date getQa2Pushtime() {
        return qa2Pushtime;
    }

    public void setQa2Pushtime(Date qa2Pushtime) {
        this.qa2Pushtime = qa2Pushtime;
    }

    public String getQa3() {
        return qa3;
    }

    public void setQa3(String qa3) {
        this.qa3 = qa3;
    }

    public Date getQa3Pushtime() {
        return qa3Pushtime;
    }

    public void setQa3Pushtime(Date qa3Pushtime) {
        this.qa3Pushtime = qa3Pushtime;
    }

    public BigDecimal getCeosEbaSyscode() {
        return ceosEbaSyscode;
    }

    public void setCeosEbaSyscode(BigDecimal ceosEbaSyscode) {
        this.ceosEbaSyscode = ceosEbaSyscode;
    }

    public String getSoCode() {
        return soCode;
    }

    public void setSoCode(String soCode) {
        this.soCode = soCode;
    }

    public String getMawbCode() {
        return mawbCode;
    }

    public void setMawbCode(String mawbCode) {
        this.mawbCode = mawbCode;
    }

    public String getEawbDeliverPostcode() {
        return eawbDeliverPostcode;
    }

    public void setEawbDeliverPostcode(String eawbDeliverPostcode) {
        this.eawbDeliverPostcode = eawbDeliverPostcode == null ? null : eawbDeliverPostcode.trim();
    }

    public String getEawbDestcountry() {
        return eawbDestcountry;
    }

    public void setEawbDestcountry(String eawbDestcountry) {
        this.eawbDestcountry = eawbDestcountry == null ? null : eawbDestcountry.trim();
    }

    public String getEawbServicetype() {
        return eawbServicetype;
    }

    public void setEawbServicetype(String eawbServicetype) {
        this.eawbServicetype = eawbServicetype == null ? null : eawbServicetype.trim();
    }

    public String getEawbSoCode() {
        return eawbSoCode;
    }

    public void setEawbSoCode(String eawbSoCode) {
        this.eawbSoCode = eawbSoCode == null ? null : eawbSoCode.trim();
    }

    public String getEawbServiceTypeOriginal() {
        return eawbServiceTypeOriginal;
    }

    public void setEawbServiceTypeOriginal(String eawbServiceTypeOriginal) {
        this.eawbServiceTypeOriginal = eawbServiceTypeOriginal;
    }
}