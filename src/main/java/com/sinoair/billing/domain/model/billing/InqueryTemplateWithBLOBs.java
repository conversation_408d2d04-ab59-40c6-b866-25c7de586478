package com.sinoair.billing.domain.model.billing;

public class InqueryTemplateWithBLOBs extends InqueryTemplate {
    private String sqlConfig;

    private String whereConfig;

    private String tableConfig;

    private String headConfig;


    public String getSqlConfig() {
        return sqlConfig;
    }

    public void setSqlConfig(String sqlConfig) {
        this.sqlConfig = sqlConfig == null ? null : sqlConfig.trim();
    }

    public String getWhereConfig() {
        return whereConfig;
    }

    public void setWhereConfig(String whereConfig) {
        this.whereConfig = whereConfig == null ? null : whereConfig.trim();
    }

    public String getTableConfig() {
        return tableConfig;
    }

    public void setTableConfig(String tableConfig) {
        this.tableConfig = tableConfig == null ? null : tableConfig.trim();
    }

    public String getHeadConfig() {
        return headConfig;
    }

    public void setHeadConfig(String headConfig) {
        this.headConfig = headConfig == null ? null : headConfig.trim();
    }

}