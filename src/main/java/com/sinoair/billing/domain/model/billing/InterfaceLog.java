package com.sinoair.billing.domain.model.billing;

import java.math.BigDecimal;
import java.util.Date;

public class InterfaceLog {
    private BigDecimal logId;

    private String interfaceSystem;

    private String interfaceModule;

    private String interfaceName;

    private String interfaceOperation;

    private String interfaceUrl;

    private String interfaceStatus;

    private String interfaceReason;

    private Date interfacePushtime;

    private Date logHandletime;

    public BigDecimal getLogId() {
        return logId;
    }

    public void setLogId(BigDecimal logId) {
        this.logId = logId;
    }

    public String getInterfaceSystem() {
        return interfaceSystem;
    }

    public void setInterfaceSystem(String interfaceSystem) {
        this.interfaceSystem = interfaceSystem == null ? null : interfaceSystem.trim();
    }

    public String getInterfaceModule() {
        return interfaceModule;
    }

    public void setInterfaceModule(String interfaceModule) {
        this.interfaceModule = interfaceModule == null ? null : interfaceModule.trim();
    }

    public String getInterfaceName() {
        return interfaceName;
    }

    public void setInterfaceName(String interfaceName) {
        this.interfaceName = interfaceName == null ? null : interfaceName.trim();
    }

    public String getInterfaceOperation() {
        return interfaceOperation;
    }

    public void setInterfaceOperation(String interfaceOperation) {
        this.interfaceOperation = interfaceOperation == null ? null : interfaceOperation.trim();
    }

    public String getInterfaceUrl() {
        return interfaceUrl;
    }

    public void setInterfaceUrl(String interfaceUrl) {
        this.interfaceUrl = interfaceUrl == null ? null : interfaceUrl.trim();
    }

    public String getInterfaceStatus() {
        return interfaceStatus;
    }

    public void setInterfaceStatus(String interfaceStatus) {
        this.interfaceStatus = interfaceStatus == null ? null : interfaceStatus.trim();
    }

    public String getInterfaceReason() {
        return interfaceReason;
    }

    public void setInterfaceReason(String interfaceReason) {
        this.interfaceReason = interfaceReason == null ? null : interfaceReason.trim();
    }

    public Date getInterfacePushtime() {
        return interfacePushtime;
    }

    public void setInterfacePushtime(Date interfacePushtime) {
        this.interfacePushtime = interfacePushtime;
    }

    public Date getLogHandletime() {
        return logHandletime;
    }

    public void setLogHandletime(Date logHandletime) {
        this.logHandletime = logHandletime;
    }
}