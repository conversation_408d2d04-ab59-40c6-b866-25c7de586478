package com.sinoair.billing.domain.model.billing;

public class InterfaceLogWithBLOBs extends InterfaceLog {
    private String interfaceParam;

    private String interfaceResult;

    public String getInterfaceParam() {
        return interfaceParam;
    }

    public void setInterfaceParam(String interfaceParam) {
        this.interfaceParam = interfaceParam == null ? null : interfaceParam.trim();
    }

    public String getInterfaceResult() {
        return interfaceResult;
    }

    public void setInterfaceResult(String interfaceResult) {
        this.interfaceResult = interfaceResult == null ? null : interfaceResult.trim();
    }
}