package com.sinoair.billing.domain.model.billing;

import java.math.BigDecimal;

public class InternalCostMap {
    private Short id;

    private String soCode;

    private String sacIdPay;

    private String sacIdRec;

    private String spCodePay;

    private String soCodeRec;

    private String billingType;

    private String ctCode;

    private Integer pdSyscode;

    private String dmType;

    private String businessCode;

    private String priceId;

    private BigDecimal pricePiecesPer;

    private BigDecimal priceWeightPer;

    private String icmStatus;

    private String remark;

    private BigDecimal tax;

    private BigDecimal currencyrate;

    public Short getId() {
        return id;
    }

    public void setId(Short id) {
        this.id = id;
    }

    public String getSoCode() {
        return soCode;
    }

    public void setSoCode(String soCode) {
        this.soCode = soCode == null ? null : soCode.trim();
    }

    public String getSacIdPay() {
        return sacIdPay;
    }

    public void setSacIdPay(String sacIdPay) {
        this.sacIdPay = sacIdPay == null ? null : sacIdPay.trim();
    }

    public String getSacIdRec() {
        return sacIdRec;
    }

    public void setSacIdRec(String sacIdRec) {
        this.sacIdRec = sacIdRec == null ? null : sacIdRec.trim();
    }

    public String getSpCodePay() {
        return spCodePay;
    }

    public void setSpCodePay(String spCodePay) {
        this.spCodePay = spCodePay == null ? null : spCodePay.trim();
    }

    public String getSoCodeRec() {
        return soCodeRec;
    }

    public void setSoCodeRec(String soCodeRec) {
        this.soCodeRec = soCodeRec == null ? null : soCodeRec.trim();
    }

    public String getBillingType() {
        return billingType;
    }

    public void setBillingType(String billingType) {
        this.billingType = billingType == null ? null : billingType.trim();
    }

    public String getCtCode() {
        return ctCode;
    }

    public void setCtCode(String ctCode) {
        this.ctCode = ctCode == null ? null : ctCode.trim();
    }

    public Integer getPdSyscode() {
        return pdSyscode;
    }

    public void setPdSyscode(Integer pdSyscode) {
        this.pdSyscode = pdSyscode;
    }

    public String getDmType() {
        return dmType;
    }

    public void setDmType(String dmType) {
        this.dmType = dmType == null ? null : dmType.trim();
    }

    public String getBusinessCode() {
        return businessCode;
    }

    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode == null ? null : businessCode.trim();
    }

    public String getPriceId() {
        return priceId;
    }

    public void setPriceId(String priceId) {
        this.priceId = priceId == null ? null : priceId.trim();
    }

    public BigDecimal getPricePiecesPer() {
        return pricePiecesPer;
    }

    public void setPricePiecesPer(BigDecimal pricePiecesPer) {
        this.pricePiecesPer = pricePiecesPer;
    }

    public BigDecimal getPriceWeightPer() {
        return priceWeightPer;
    }

    public void setPriceWeightPer(BigDecimal priceWeightPer) {
        this.priceWeightPer = priceWeightPer;
    }

    public String getIcmStatus() {
        return icmStatus;
    }

    public void setIcmStatus(String icmStatus) {
        this.icmStatus = icmStatus == null ? null : icmStatus.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public BigDecimal getTax() {
        return tax;
    }

    public void setTax(BigDecimal tax) {
        this.tax = tax;
    }

    public BigDecimal getCurrencyrate() {
        return currencyrate;
    }

    public void setCurrencyrate(BigDecimal currencyrate) {
        this.currencyrate = currencyrate;
    }
}