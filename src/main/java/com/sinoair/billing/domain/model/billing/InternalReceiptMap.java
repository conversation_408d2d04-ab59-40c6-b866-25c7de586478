package com.sinoair.billing.domain.model.billing;

public class InternalReceiptMap {
    private Short irmId;

    private String spCode;

    private String sacIdPay;

    private String sacIdRec;

    private String spCodePay;

    private String soCodeRec;

    private String confirmType;

    private String ctCode;

    private String cmType;

    private String dmType;

    private String irmStatus;

    private String remark;

    public Short getIrmId() {
        return irmId;
    }

    public void setIrmId(Short irmId) {
        this.irmId = irmId;
    }

    public String getSpCode() {
        return spCode;
    }

    public void setSpCode(String spCode) {
        this.spCode = spCode == null ? null : spCode.trim();
    }

    public String getSacIdPay() {
        return sacIdPay;
    }

    public void setSacIdPay(String sacIdPay) {
        this.sacIdPay = sacIdPay == null ? null : sacIdPay.trim();
    }

    public String getSacIdRec() {
        return sacIdRec;
    }

    public void setSacIdRec(String sacIdRec) {
        this.sacIdRec = sacIdRec == null ? null : sacIdRec.trim();
    }

    public String getSpCodePay() {
        return spCodePay;
    }

    public void setSpCodePay(String spCodePay) {
        this.spCodePay = spCodePay == null ? null : spCodePay.trim();
    }

    public String getSoCodeRec() {
        return soCodeRec;
    }

    public void setSoCodeRec(String soCodeRec) {
        this.soCodeRec = soCodeRec == null ? null : soCodeRec.trim();
    }

    public String getConfirmType() {
        return confirmType;
    }

    public void setConfirmType(String confirmType) {
        this.confirmType = confirmType == null ? null : confirmType.trim();
    }

    public String getCtCode() {
        return ctCode;
    }

    public void setCtCode(String ctCode) {
        this.ctCode = ctCode == null ? null : ctCode.trim();
    }

    public String getCmType() {
        return cmType;
    }

    public void setCmType(String cmType) {
        this.cmType = cmType == null ? null : cmType.trim();
    }

    public String getDmType() {
        return dmType;
    }

    public void setDmType(String dmType) {
        this.dmType = dmType == null ? null : dmType.trim();
    }

    public String getIrmStatus() {
        return irmStatus;
    }

    public void setIrmStatus(String irmStatus) {
        this.irmStatus = irmStatus == null ? null : irmStatus.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }
}