package com.sinoair.billing.domain.model.billing;

public class KanBanConfigWithBLOBs extends KanBanConfig {
    private String headConfig;

    private String sqlConfig;

    private String whereConfig;

    public String getHeadConfig() {
        return headConfig;
    }

    public void setHeadConfig(String headConfig) {
        this.headConfig = headConfig == null ? null : headConfig.trim();
    }

    public String getSqlConfig() {
        return sqlConfig;
    }

    public void setSqlConfig(String sqlConfig) {
        this.sqlConfig = sqlConfig == null ? null : sqlConfig.trim();
    }

    public String getWhereConfig() {
        return whereConfig;
    }

    public void setWhereConfig(String whereConfig) {
        this.whereConfig = whereConfig == null ? null : whereConfig.trim();
    }
}