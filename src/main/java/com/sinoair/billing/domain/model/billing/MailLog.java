package com.sinoair.billing.domain.model.billing;

import java.math.BigDecimal;
import java.util.Date;

public class MailLog {
    private BigDecimal logId;

    private String logType;

    private BigDecimal logBatchId;

    private String mailSystem;

    private String mailModule;

    private String mailSender;

    private String mailReceiver;

    private String mailSubject;

    private String mailStatus;

    private String pushUrl;

    private String pushParam;

    private String pushResult;

    private String pushStatus;

    private Date pushTime;

    private Date logHandletime;

    public BigDecimal getLogId() {
        return logId;
    }

    public void setLogId(BigDecimal logId) {
        this.logId = logId;
    }

    public String getLogType() {
        return logType;
    }

    public void setLogType(String logType) {
        this.logType = logType == null ? null : logType.trim();
    }

    public BigDecimal getLogBatchId() {
        return logBatchId;
    }

    public void setLogBatchId(BigDecimal logBatchId) {
        this.logBatchId = logBatchId;
    }

    public String getMailSystem() {
        return mailSystem;
    }

    public void setMailSystem(String mailSystem) {
        this.mailSystem = mailSystem == null ? null : mailSystem.trim();
    }

    public String getMailModule() {
        return mailModule;
    }

    public void setMailModule(String mailModule) {
        this.mailModule = mailModule == null ? null : mailModule.trim();
    }

    public String getMailSender() {
        return mailSender;
    }

    public void setMailSender(String mailSender) {
        this.mailSender = mailSender == null ? null : mailSender.trim();
    }

    public String getMailReceiver() {
        return mailReceiver;
    }

    public void setMailReceiver(String mailReceiver) {
        this.mailReceiver = mailReceiver == null ? null : mailReceiver.trim();
    }

    public String getMailSubject() {
        return mailSubject;
    }

    public void setMailSubject(String mailSubject) {
        this.mailSubject = mailSubject == null ? null : mailSubject.trim();
    }

    public String getMailStatus() {
        return mailStatus;
    }

    public void setMailStatus(String mailStatus) {
        this.mailStatus = mailStatus == null ? null : mailStatus.trim();
    }

    public String getPushUrl() {
        return pushUrl;
    }

    public void setPushUrl(String pushUrl) {
        this.pushUrl = pushUrl == null ? null : pushUrl.trim();
    }

    public String getPushParam() {
        return pushParam;
    }

    public void setPushParam(String pushParam) {
        this.pushParam = pushParam == null ? null : pushParam.trim();
    }

    public String getPushResult() {
        return pushResult;
    }

    public void setPushResult(String pushResult) {
        this.pushResult = pushResult == null ? null : pushResult.trim();
    }

    public String getPushStatus() {
        return pushStatus;
    }

    public void setPushStatus(String pushStatus) {
        this.pushStatus = pushStatus == null ? null : pushStatus.trim();
    }

    public Date getPushTime() {
        return pushTime;
    }

    public void setPushTime(Date pushTime) {
        this.pushTime = pushTime;
    }

    public Date getLogHandletime() {
        return logHandletime;
    }

    public void setLogHandletime(Date logHandletime) {
        this.logHandletime = logHandletime;
    }
}