package com.sinoair.billing.domain.model.billing;

import java.math.BigDecimal;
import java.util.Date;

public class ManifestList {
    private Long mId;

    private String mCode;

    private String mStatus;

    private String mRcType;

    private String btCode;

    private String companyId;

    private String invoiceType;

    private String invoiceNum;

    private String soCode;

    private String soName;

    private String ctCode;

    private BigDecimal currencyrate;

    private BigDecimal soTax;

    private BigDecimal mTotalrmb;

    private BigDecimal mTotalfc;

    private BigDecimal taxAmount;

    private BigDecimal notaxAmount;

    private BigDecimal taxAmountFc;

    private BigDecimal notaxAmountFc;

    private Integer mTotalPieces;

    private BigDecimal mTotalWeight;

    private Integer mEId;

    private Date mHandletime;

    private String mType;

    private String mRemark;

    private Date mConfirmtime;

    private String isBmsCollect;

    private String ctSign;

    public Long getmId() {
        return mId;
    }

    public void setmId(Long mId) {
        this.mId = mId;
    }

    public String getmCode() {
        return mCode;
    }

    public void setmCode(String mCode) {
        this.mCode = mCode == null ? null : mCode.trim();
    }

    public String getmStatus() {
        return mStatus;
    }

    public void setmStatus(String mStatus) {
        this.mStatus = mStatus == null ? null : mStatus.trim();
    }

    public String getmRcType() {
        return mRcType;
    }

    public void setmRcType(String mRcType) {
        this.mRcType = mRcType == null ? null : mRcType.trim();
    }

    public String getBtCode() {
        return btCode;
    }

    public void setBtCode(String btCode) {
        this.btCode = btCode == null ? null : btCode.trim();
    }

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId == null ? null : companyId.trim();
    }

    public String getInvoiceType() {
        return invoiceType;
    }

    public void setInvoiceType(String invoiceType) {
        this.invoiceType = invoiceType == null ? null : invoiceType.trim();
    }

    public String getInvoiceNum() {
        return invoiceNum;
    }

    public void setInvoiceNum(String invoiceNum) {
        this.invoiceNum = invoiceNum == null ? null : invoiceNum.trim();
    }

    public String getSoCode() {
        return soCode;
    }

    public void setSoCode(String soCode) {
        this.soCode = soCode == null ? null : soCode.trim();
    }

    public String getSoName() {
        return soName;
    }

    public void setSoName(String soName) {
        this.soName = soName == null ? null : soName.trim();
    }

    public String getCtCode() {
        return ctCode;
    }

    public void setCtCode(String ctCode) {
        this.ctCode = ctCode == null ? null : ctCode.trim();
    }

    public BigDecimal getCurrencyrate() {
        return currencyrate;
    }

    public void setCurrencyrate(BigDecimal currencyrate) {
        this.currencyrate = currencyrate;
    }

    public BigDecimal getSoTax() {
        return soTax;
    }

    public void setSoTax(BigDecimal soTax) {
        this.soTax = soTax;
    }

    public BigDecimal getmTotalrmb() {
        return mTotalrmb;
    }

    public void setmTotalrmb(BigDecimal mTotalrmb) {
        this.mTotalrmb = mTotalrmb;
    }

    public BigDecimal getmTotalfc() {
        return mTotalfc;
    }

    public void setmTotalfc(BigDecimal mTotalfc) {
        this.mTotalfc = mTotalfc;
    }

    public BigDecimal getTaxAmount() {
        return taxAmount;
    }

    public void setTaxAmount(BigDecimal taxAmount) {
        this.taxAmount = taxAmount;
    }

    public BigDecimal getNotaxAmount() {
        return notaxAmount;
    }

    public void setNotaxAmount(BigDecimal notaxAmount) {
        this.notaxAmount = notaxAmount;
    }

    public BigDecimal getTaxAmountFc() {
        return taxAmountFc;
    }

    public void setTaxAmountFc(BigDecimal taxAmountFc) {
        this.taxAmountFc = taxAmountFc;
    }

    public BigDecimal getNotaxAmountFc() {
        return notaxAmountFc;
    }

    public void setNotaxAmountFc(BigDecimal notaxAmountFc) {
        this.notaxAmountFc = notaxAmountFc;
    }

    public Integer getmTotalPieces() {
        return mTotalPieces;
    }

    public void setmTotalPieces(Integer mTotalPieces) {
        this.mTotalPieces = mTotalPieces;
    }

    public BigDecimal getmTotalWeight() {
        return mTotalWeight;
    }

    public void setmTotalWeight(BigDecimal mTotalWeight) {
        this.mTotalWeight = mTotalWeight;
    }

    public Integer getmEId() {
        return mEId;
    }

    public void setmEId(Integer mEId) {
        this.mEId = mEId;
    }

    public Date getmHandletime() {
        return mHandletime;
    }

    public void setmHandletime(Date mHandletime) {
        this.mHandletime = mHandletime;
    }

    public String getmType() {
        return mType;
    }

    public void setmType(String mType) {
        this.mType = mType == null ? null : mType.trim();
    }

    public String getmRemark() {
        return mRemark;
    }

    public void setmRemark(String mRemark) {
        this.mRemark = mRemark == null ? null : mRemark.trim();
    }

    public Date getmConfirmtime() {
        return mConfirmtime;
    }

    public void setmConfirmtime(Date mConfirmtime) {
        this.mConfirmtime = mConfirmtime;
    }

    public String getIsBmsCollect() {
        return isBmsCollect;
    }

    public void setIsBmsCollect(String isBmsCollect) {
        this.isBmsCollect = isBmsCollect;
    }

    public String getCtSign() {
        return ctSign;
    }

    public void setCtSign(String ctSign) {
        this.ctSign = ctSign;
    }
}