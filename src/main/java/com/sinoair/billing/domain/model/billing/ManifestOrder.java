package com.sinoair.billing.domain.model.billing;

import java.util.Date;

public class ManifestOrder {
    private Long mId;

    private String sysStatus;

    private Long threadNum;

    private Long handleNum;

    private Date sysHandletime;

    public Long getmId() {
        return mId;
    }

    public void setmId(Long mId) {
        this.mId = mId;
    }

    public String getSysStatus() {
        return sysStatus;
    }

    public void setSysStatus(String sysStatus) {
        this.sysStatus = sysStatus == null ? null : sysStatus.trim();
    }

    public Long getThreadNum() {
        return threadNum;
    }

    public void setThreadNum(Long threadNum) {
        this.threadNum = threadNum;
    }

    public Long getHandleNum() {
        return handleNum;
    }

    public void setHandleNum(Long handleNum) {
        this.handleNum = handleNum;
    }

    public Date getSysHandletime() {
        return sysHandletime;
    }

    public void setSysHandletime(Date sysHandletime) {
        this.sysHandletime = sysHandletime;
    }
}