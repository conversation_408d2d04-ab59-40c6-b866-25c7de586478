package com.sinoair.billing.domain.model.billing;

import java.math.BigDecimal;
import java.util.Date;

public class PaymentBillDetail {
    private Integer pbdSyscode;

    private String invoiceCode;

    private String businessCode;

    private String dsbmsCode;

    private String spCode;

    private Integer pbdPieces;

    private String chargeweightScope;

    private BigDecimal pbdChargeweight;

    private BigDecimal pbdAmount;

    private String ctId;

    private Date pbdDate;

    private String eawbServicetype;

    private Integer cmId;

    private String pbdRemark;

    private String pbdFilename;

    private Date pbdHandletime;

    private Integer pbdUserId;

    private BigDecimal pbdActualAmount;

    private BigDecimal pbdActualChargeweight;

    private Integer pbdActualPieces;

    private String prName;

    private Integer pdSyscode;

    private String companyId;

    private String eawbIetype;

    public Integer getPbdSyscode() {
        return pbdSyscode;
    }

    public void setPbdSyscode(Integer pbdSyscode) {
        this.pbdSyscode = pbdSyscode;
    }

    public String getInvoiceCode() {
        return invoiceCode;
    }

    public void setInvoiceCode(String invoiceCode) {
        this.invoiceCode = invoiceCode == null ? null : invoiceCode.trim();
    }

    public String getBusinessCode() {
        return businessCode;
    }

    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode == null ? null : businessCode.trim();
    }

    public String getDsbmsCode() {
        return dsbmsCode;
    }

    public void setDsbmsCode(String dsbmsCode) {
        this.dsbmsCode = dsbmsCode == null ? null : dsbmsCode.trim();
    }

    public String getSpCode() {
        return spCode;
    }

    public void setSpCode(String spCode) {
        this.spCode = spCode == null ? null : spCode.trim();
    }

    public Integer getPbdPieces() {
        return pbdPieces;
    }

    public void setPbdPieces(Integer pbdPieces) {
        this.pbdPieces = pbdPieces;
    }

    public String getChargeweightScope() {
        return chargeweightScope;
    }

    public void setChargeweightScope(String chargeweightScope) {
        this.chargeweightScope = chargeweightScope == null ? null : chargeweightScope.trim();
    }

    public BigDecimal getPbdChargeweight() {
        return pbdChargeweight;
    }

    public void setPbdChargeweight(BigDecimal pbdChargeweight) {
        this.pbdChargeweight = pbdChargeweight;
    }

    public BigDecimal getPbdAmount() {
        return pbdAmount;
    }

    public void setPbdAmount(BigDecimal pbdAmount) {
        this.pbdAmount = pbdAmount;
    }

    public String getCtId() {
        return ctId;
    }

    public void setCtId(String ctId) {
        this.ctId = ctId == null ? null : ctId.trim();
    }

    public Date getPbdDate() {
        return pbdDate;
    }

    public void setPbdDate(Date pbdDate) {
        this.pbdDate = pbdDate;
    }

    public String getEawbServicetype() {
        return eawbServicetype;
    }

    public void setEawbServicetype(String eawbServicetype) {
        this.eawbServicetype = eawbServicetype == null ? null : eawbServicetype.trim();
    }

    public Integer getCmId() {
        return cmId;
    }

    public void setCmId(Integer cmId) {
        this.cmId = cmId;
    }

    public String getPbdRemark() {
        return pbdRemark;
    }

    public void setPbdRemark(String pbdRemark) {
        this.pbdRemark = pbdRemark == null ? null : pbdRemark.trim();
    }

    public String getPbdFilename() {
        return pbdFilename;
    }

    public void setPbdFilename(String pbdFilename) {
        this.pbdFilename = pbdFilename == null ? null : pbdFilename.trim();
    }

    public Date getPbdHandletime() {
        return pbdHandletime;
    }

    public void setPbdHandletime(Date pbdHandletime) {
        this.pbdHandletime = pbdHandletime;
    }

    public Integer getPbdUserId() {
        return pbdUserId;
    }

    public void setPbdUserId(Integer pbdUserId) {
        this.pbdUserId = pbdUserId;
    }

    public BigDecimal getPbdActualAmount() {
        return pbdActualAmount;
    }

    public void setPbdActualAmount(BigDecimal pbdActualAmount) {
        this.pbdActualAmount = pbdActualAmount;
    }

    public BigDecimal getPbdActualChargeweight() {
        return pbdActualChargeweight;
    }

    public void setPbdActualChargeweight(BigDecimal pbdActualChargeweight) {
        this.pbdActualChargeweight = pbdActualChargeweight;
    }

    public Integer getPbdActualPieces() {
        return pbdActualPieces;
    }

    public void setPbdActualPieces(Integer pbdActualPieces) {
        this.pbdActualPieces = pbdActualPieces;
    }

    public String getPrName() {
        return prName;
    }

    public void setPrName(String prName) {
        this.prName = prName == null ? null : prName.trim();
    }

    public Integer getPdSyscode() {
        return pdSyscode;
    }

    public void setPdSyscode(Integer pdSyscode) {
        this.pdSyscode = pdSyscode;
    }

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId == null ? null : companyId.trim();
    }

    public String getEawbIetype() {
        return eawbIetype;
    }

    public void setEawbIetype(String eawbIetype) {
        this.eawbIetype = eawbIetype;
    }
}