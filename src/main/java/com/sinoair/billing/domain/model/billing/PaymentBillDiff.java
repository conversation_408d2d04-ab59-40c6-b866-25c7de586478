package com.sinoair.billing.domain.model.billing;

import java.math.BigDecimal;
import java.util.Date;

public class PaymentBillDiff {
    private String invoiceCode;

    private String businessCode;

    private String dsbmsCode;

    private String spCode;

    private Integer pbdPieces;

    private String chargeweightScope;

    private BigDecimal pbdChargeweight;

    private BigDecimal dsbmsChargeweight;

    private BigDecimal pbdAmount;

    private BigDecimal dsbmsAmount;

    private BigDecimal actualAmount;

    private String ctId;

    private Date pbdDate;

    private String eawbServicetype;

    private Integer cmId;

    private String pbdRemark;

    private String pbdFilename;

    private Integer dmbmsItems;

    private BigDecimal actualChargeweight;

    private Integer actualItems;

    public String getInvoiceCode() {
        return invoiceCode;
    }

    public void setInvoiceCode(String invoiceCode) {
        this.invoiceCode = invoiceCode == null ? null : invoiceCode.trim();
    }

    public String getBusinessCode() {
        return businessCode;
    }

    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode == null ? null : businessCode.trim();
    }

    public String getDsbmsCode() {
        return dsbmsCode;
    }

    public void setDsbmsCode(String dsbmsCode) {
        this.dsbmsCode = dsbmsCode == null ? null : dsbmsCode.trim();
    }

    public String getSpCode() {
        return spCode;
    }

    public void setSpCode(String spCode) {
        this.spCode = spCode == null ? null : spCode.trim();
    }

    public Integer getPbdPieces() {
        return pbdPieces;
    }

    public void setPbdPieces(Integer pbdPieces) {
        this.pbdPieces = pbdPieces;
    }

    public String getChargeweightScope() {
        return chargeweightScope;
    }

    public void setChargeweightScope(String chargeweightScope) {
        this.chargeweightScope = chargeweightScope == null ? null : chargeweightScope.trim();
    }

    public BigDecimal getPbdChargeweight() {
        return pbdChargeweight;
    }

    public void setPbdChargeweight(BigDecimal pbdChargeweight) {
        this.pbdChargeweight = pbdChargeweight;
    }

    public BigDecimal getDsbmsChargeweight() {
        return dsbmsChargeweight;
    }

    public void setDsbmsChargeweight(BigDecimal dsbmsChargeweight) {
        this.dsbmsChargeweight = dsbmsChargeweight;
    }

    public BigDecimal getPbdAmount() {
        return pbdAmount;
    }

    public void setPbdAmount(BigDecimal pbdAmount) {
        this.pbdAmount = pbdAmount;
    }

    public BigDecimal getDsbmsAmount() {
        return dsbmsAmount;
    }

    public void setDsbmsAmount(BigDecimal dsbmsAmount) {
        this.dsbmsAmount = dsbmsAmount;
    }

    public BigDecimal getActualAmount() {
        return actualAmount;
    }

    public void setActualAmount(BigDecimal actualAmount) {
        this.actualAmount = actualAmount;
    }

    public String getCtId() {
        return ctId;
    }

    public void setCtId(String ctId) {
        this.ctId = ctId == null ? null : ctId.trim();
    }

    public Date getPbdDate() {
        return pbdDate;
    }

    public void setPbdDate(Date pbdDate) {
        this.pbdDate = pbdDate;
    }

    public String getEawbServicetype() {
        return eawbServicetype;
    }

    public void setEawbServicetype(String eawbServicetype) {
        this.eawbServicetype = eawbServicetype == null ? null : eawbServicetype.trim();
    }

    public Integer getCmId() {
        return cmId;
    }

    public void setCmId(Integer cmId) {
        this.cmId = cmId;
    }

    public String getPbdRemark() {
        return pbdRemark;
    }

    public void setPbdRemark(String pbdRemark) {
        this.pbdRemark = pbdRemark == null ? null : pbdRemark.trim();
    }

    public String getPbdFilename() {
        return pbdFilename;
    }

    public void setPbdFilename(String pbdFilename) {
        this.pbdFilename = pbdFilename == null ? null : pbdFilename.trim();
    }

    public Integer getDmbmsItems() {
        return dmbmsItems;
    }

    public void setDmbmsItems(Integer dmbmsItems) {
        this.dmbmsItems = dmbmsItems;
    }

    public BigDecimal getActualChargeweight() {
        return actualChargeweight;
    }

    public void setActualChargeweight(BigDecimal actualChargeweight) {
        this.actualChargeweight = actualChargeweight;
    }

    public Integer getActualItems() {
        return actualItems;
    }

    public void setActualItems(Integer actualItems) {
        this.actualItems = actualItems;
    }
}