package com.sinoair.billing.domain.model.billing;

import java.math.BigDecimal;
import java.util.Date;

public class PaymentRecord {
    private Long prId;

    private String eawbPrintcode;

    private String mawbCode;

    private Integer ppId;

    private Integer cmId;

    private String soCode;

    private String ctCode;

    private String companyId;

    private String prName;

    private String prType;

    private BigDecimal prPlanAmount;

    private BigDecimal prActualAmount;

    private String prStatus;

    private Integer prUserId;

    private Date prHandletime;

    private String prAwbType;

    private String prRemark;

    private String eawbReference1;

    private String eawbReference2;

    private BigDecimal eawbChargeableweight;

    private Integer eawbHawbQty;

    private String agentCode;

    private Date prOccurtime;

    private String eawbTrackingNo;

    private String epKey;

    private BigDecimal chargeableweight;

    private Integer pdSyscode;

    private String outboundCompanyId;

    private String eawbIetype;

    private String estimateStatus;

    private String prCate;

    private BigDecimal ctRate;
    private BigDecimal prTotalRmb;
    private BigDecimal prTotalFc;
    private BigDecimal rrId;
    private Integer serviceDetailId;
    private Integer feeStatus;

    public Integer getFeeStatus() {
        return feeStatus;
    }

    public void setFeeStatus(Integer feeStatus) {
        this.feeStatus = feeStatus;
    }

    public Integer getServiceDetailId() {
        return serviceDetailId;
    }

    public void setServiceDetailId(Integer serviceDetailId) {
        this.serviceDetailId = serviceDetailId;
    }

    //临时
    private String ppDest;
    private String eawbDestcountry;
    private BigDecimal ppPrice; //对应getPaymentPrice2
    private BigDecimal ppPrice1; //对应getPaymentPrice1
    private Long reId;

    private String eawbSoCode;
    private String ppSoCode;
    private Integer serviceId;
    private String partitionCode;

    public Integer getServiceId() {
        return serviceId;
    }

    public void setServiceId(Integer serviceId) {
        this.serviceId = serviceId;
    }

    public String getPartitionCode() {
        return partitionCode;
    }

    public void setPartitionCode(String partitionCode) {
        this.partitionCode = partitionCode;
    }

    public Long getPrId() {
        return prId;
    }

    public void setPrId(Long prId) {
        this.prId = prId;
    }

    public String getEawbPrintcode() {
        return eawbPrintcode;
    }

    public void setEawbPrintcode(String eawbPrintcode) {
        this.eawbPrintcode = eawbPrintcode == null ? null : eawbPrintcode.trim();
    }

    public String getMawbCode() {
        return mawbCode;
    }

    public void setMawbCode(String mawbCode) {
        this.mawbCode = mawbCode == null ? null : mawbCode.trim();
    }

    public Integer getPpId() {
        return ppId;
    }

    public void setPpId(Integer ppId) {
        this.ppId = ppId;
    }

    public Integer getCmId() {
        return cmId;
    }

    public void setCmId(Integer cmId) {
        this.cmId = cmId;
    }

    public String getSoCode() {
        return soCode;
    }

    public void setSoCode(String soCode) {
        this.soCode = soCode == null ? null : soCode.trim();
    }

    public String getCtCode() {
        return ctCode;
    }

    public void setCtCode(String ctCode) {
        this.ctCode = ctCode == null ? null : ctCode.trim();
    }

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId == null ? null : companyId.trim();
    }

    public String getPrName() {
        return prName;
    }

    public void setPrName(String prName) {
        this.prName = prName == null ? null : prName.trim();
    }

    public String getPrType() {
        return prType;
    }

    public void setPrType(String prType) {
        this.prType = prType == null ? null : prType.trim();
    }

    public BigDecimal getPrPlanAmount() {
        return prPlanAmount;
    }

    public void setPrPlanAmount(BigDecimal prPlanAmount) {
        this.prPlanAmount = prPlanAmount;
    }

    public BigDecimal getPrActualAmount() {
        return prActualAmount;
    }

    public void setPrActualAmount(BigDecimal prActualAmount) {
        this.prActualAmount = prActualAmount;
    }

    public String getPrStatus() {
        return prStatus;
    }

    public void setPrStatus(String prStatus) {
        this.prStatus = prStatus == null ? null : prStatus.trim();
    }

    public Integer getPrUserId() {
        return prUserId;
    }

    public void setPrUserId(Integer prUserId) {
        this.prUserId = prUserId;
    }

    public Date getPrHandletime() {
        return prHandletime;
    }

    public void setPrHandletime(Date prHandletime) {
        this.prHandletime = prHandletime;
    }

    public String getPrAwbType() {
        return prAwbType;
    }

    public void setPrAwbType(String prAwbType) {
        this.prAwbType = prAwbType == null ? null : prAwbType.trim();
    }

    public String getPrRemark() {
        return prRemark;
    }

    public void setPrRemark(String prRemark) {
        this.prRemark = prRemark == null ? null : prRemark.trim();
    }

    public String getEawbReference1() {
        return eawbReference1;
    }

    public void setEawbReference1(String eawbReference1) {
        this.eawbReference1 = eawbReference1 == null ? null : eawbReference1.trim();
    }

    public String getEawbReference2() {
        return eawbReference2;
    }

    public void setEawbReference2(String eawbReference2) {
        this.eawbReference2 = eawbReference2 == null ? null : eawbReference2.trim();
    }

    public BigDecimal getEawbChargeableweight() {
        return eawbChargeableweight;
    }

    public void setEawbChargeableweight(BigDecimal eawbChargeableweight) {
        this.eawbChargeableweight = eawbChargeableweight;
    }

    public Integer getEawbHawbQty() {
        return eawbHawbQty;
    }

    public void setEawbHawbQty(Integer eawbHawbQty) {
        this.eawbHawbQty = eawbHawbQty;
    }

    public String getAgentCode() {
        return agentCode;
    }

    public void setAgentCode(String agentCode) {
        this.agentCode = agentCode == null ? null : agentCode.trim();
    }

    public Date getPrOccurtime() {
        return prOccurtime;
    }

    public void setPrOccurtime(Date prOccurtime) {
        this.prOccurtime = prOccurtime;
    }

    public String getEawbTrackingNo() {
        return eawbTrackingNo;
    }

    public void setEawbTrackingNo(String eawbTrackingNo) {
        this.eawbTrackingNo = eawbTrackingNo == null ? null : eawbTrackingNo.trim();
    }

    public String getEpKey() {
        return epKey;
    }

    public void setEpKey(String epKey) {
        this.epKey = epKey == null ? null : epKey.trim();
    }

    public BigDecimal getChargeableweight() {
        return chargeableweight;
    }

    public void setChargeableweight(BigDecimal chargeableweight) {
        this.chargeableweight = chargeableweight;
    }

    public Integer getPdSyscode() {
        return pdSyscode;
    }

    public void setPdSyscode(Integer pdSyscode) {
        this.pdSyscode = pdSyscode;
    }

    public String getOutboundCompanyId() {
        return outboundCompanyId;
    }

    public void setOutboundCompanyId(String outboundCompanyId) {
        this.outboundCompanyId = outboundCompanyId == null ? null : outboundCompanyId.trim();
    }

    public String getEawbIetype() {
        return eawbIetype;
    }

    public void setEawbIetype(String eawbIetype) {
        this.eawbIetype = eawbIetype == null ? null : eawbIetype.trim();
    }

    public String getEstimateStatus() {
        return estimateStatus;
    }

    public void setEstimateStatus(String estimateStatus) {
        this.estimateStatus = estimateStatus;
    }

    public String getPpDest() {
        return ppDest;
    }

    public void setPpDest(String ppDest) {
        this.ppDest = ppDest;
    }

    public String getEawbDestcountry() {
        return eawbDestcountry;
    }

    public void setEawbDestcountry(String eawbDestcountry) {
        this.eawbDestcountry = eawbDestcountry;
    }

    public BigDecimal getPpPrice() {
        return ppPrice;
    }

    public void setPpPrice(BigDecimal ppPrice) {
        this.ppPrice = ppPrice;
    }

    public BigDecimal getPpPrice1() {
        return ppPrice1;
    }

    public void setPpPrice1(BigDecimal ppPrice1) {
        this.ppPrice1 = ppPrice1;
    }

    public Long getReId() {
        return reId;
    }

    public void setReId(Long reId) {
        this.reId = reId;
    }

    public String getEawbSoCode() {
        return eawbSoCode;
    }

    public void setEawbSoCode(String eawbSoCode) {
        this.eawbSoCode = eawbSoCode;
    }

    public String getPpSoCode() {
        return ppSoCode;
    }

    public void setPpSoCode(String ppSoCode) {
        this.ppSoCode = ppSoCode;
    }

    public String getPrCate() {
        return prCate;
    }

    public void setPrCate(String prCate) {
        this.prCate = prCate;
    }

    public BigDecimal getCtRate() {
        return ctRate;
    }

    public void setCtRate(BigDecimal ctRate) {
        this.ctRate = ctRate;
    }

    public BigDecimal getPrTotalRmb() {
        return prTotalRmb;
    }

    public void setPrTotalRmb(BigDecimal prTotalRmb) {
        this.prTotalRmb = prTotalRmb;
    }

    public BigDecimal getPrTotalFc() {
        return prTotalFc;
    }

    public void setPrTotalFc(BigDecimal prTotalFc) {
        this.prTotalFc = prTotalFc;
    }

    public BigDecimal getRrId() {
        return rrId;
    }

    public void setRrId(BigDecimal rrId) {
        this.rrId = rrId;
    }
}