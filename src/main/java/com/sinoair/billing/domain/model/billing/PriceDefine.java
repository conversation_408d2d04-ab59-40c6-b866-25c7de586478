package com.sinoair.billing.domain.model.billing;

import java.util.Date;

public class PriceDefine {
    private Integer pdSyscode;

    private String pdName;

    private String pdType;

    private String pdMode;

    private String sacId;

    private String btCode;

    private String pdUnit;

    private String pdRemark;

    private Date pdHandletime;

    private Integer pdUserId;

    private String pdStatus;

    private String pdEname;

    public Integer getPdSyscode() {
        return pdSyscode;
    }

    public void setPdSyscode(Integer pdSyscode) {
        this.pdSyscode = pdSyscode;
    }

    public String getPdName() {
        return pdName;
    }

    public void setPdName(String pdName) {
        this.pdName = pdName == null ? null : pdName.trim();
    }

    public String getPdType() {
        return pdType;
    }

    public void setPdType(String pdType) {
        this.pdType = pdType == null ? null : pdType.trim();
    }

    public String getPdMode() {
        return pdMode;
    }

    public void setPdMode(String pdMode) {
        this.pdMode = pdMode == null ? null : pdMode.trim();
    }

    public String getSacId() {
        return sacId;
    }

    public void setSacId(String sacId) {
        this.sacId = sacId;
    }

    public String getBtCode() {
        return btCode;
    }

    public void setBtCode(String btCode) {
        this.btCode = btCode == null ? null : btCode.trim();
    }

    public String getPdUnit() {
        return pdUnit;
    }

    public void setPdUnit(String pdUnit) {
        this.pdUnit = pdUnit == null ? null : pdUnit.trim();
    }

    public String getPdRemark() {
        return pdRemark;
    }

    public void setPdRemark(String pdRemark) {
        this.pdRemark = pdRemark == null ? null : pdRemark.trim();
    }

    public Date getPdHandletime() {
        return pdHandletime;
    }

    public void setPdHandletime(Date pdHandletime) {
        this.pdHandletime = pdHandletime;
    }

    public Integer getPdUserId() {
        return pdUserId;
    }

    public void setPdUserId(Integer pdUserId) {
        this.pdUserId = pdUserId;
    }

    public String getPdStatus() {
        return pdStatus;
    }

    public void setPdStatus(String pdStatus) {
        this.pdStatus = pdStatus == null ? null : pdStatus.trim();
    }

    public String getPdEname() {
        return pdEname;
    }

    public void setPdEname(String pdEname) {
        this.pdEname = pdEname == null ? null : pdEname.trim();
    }
}