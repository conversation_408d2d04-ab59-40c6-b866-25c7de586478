package com.sinoair.billing.domain.model.billing;

import java.math.BigDecimal;
import java.util.Date;

public class PriceGrade implements Cloneable {
    private Integer pgId;

    private Integer prId;

    private Integer ppId;

    private String pgType;

    private BigDecimal pgStart;

    private BigDecimal pgEnd;

    private BigDecimal pgFixprice;

    private BigDecimal pgInnerprice;

    private BigDecimal pgInnerweight;

    private Date pgHandletime;

    private Integer pgUserId;

    private BigDecimal pgKgPrice;

    private String kgType;

    private String zoneNum;

    public Integer getPgId() {
        return pgId;
    }

    public void setPgId(Integer pgId) {
        this.pgId = pgId;
    }

    public Integer getPrId() {
        return prId;
    }

    public void setPrId(Integer prId) {
        this.prId = prId;
    }

    public Integer getPpId() {
        return ppId;
    }

    public void setPpId(Integer ppId) {
        this.ppId = ppId;
    }

    public String getPgType() {
        return pgType;
    }

    public void setPgType(String pgType) {
        this.pgType = pgType == null ? null : pgType.trim();
    }

    public BigDecimal getPgStart() {
        return pgStart;
    }

    public void setPgStart(BigDecimal pgStart) {
        this.pgStart = pgStart;
    }

    public BigDecimal getPgEnd() {
        return pgEnd;
    }

    public void setPgEnd(BigDecimal pgEnd) {
        this.pgEnd = pgEnd;
    }

    public BigDecimal getPgFixprice() {
        return pgFixprice;
    }

    public void setPgFixprice(BigDecimal pgFixprice) {
        this.pgFixprice = pgFixprice;
    }

    public BigDecimal getPgInnerprice() {
        return pgInnerprice;
    }

    public void setPgInnerprice(BigDecimal pgInnerprice) {
        this.pgInnerprice = pgInnerprice;
    }

    public BigDecimal getPgInnerweight() {
        return pgInnerweight;
    }

    public void setPgInnerweight(BigDecimal pgInnerweight) {
        this.pgInnerweight = pgInnerweight;
    }

    public Date getPgHandletime() {
        return pgHandletime;
    }

    public void setPgHandletime(Date pgHandletime) {
        this.pgHandletime = pgHandletime;
    }

    public Integer getPgUserId() {
        return pgUserId;
    }

    public void setPgUserId(Integer pgUserId) {
        this.pgUserId = pgUserId;
    }

    public BigDecimal getPgKgPrice() {
        return pgKgPrice;
    }

    public void setPgKgPrice(BigDecimal pgKgPrice) {
        this.pgKgPrice = pgKgPrice;
    }

    public String getKgType() {
        return kgType;
    }

    public void setKgType(String kgType) {
        this.kgType = kgType;
    }

    public String getZoneNum() {
        return zoneNum;
    }

    public void setZoneNum(String zoneNum) {
        this.zoneNum = zoneNum;
    }

    @Override
    public Object clone() throws CloneNotSupportedException {
        PriceGrade pr = null;
        try{
            pr = (PriceGrade)super.clone();
        }catch(CloneNotSupportedException e) {
            e.printStackTrace();
        }
        return pr;
    }
}