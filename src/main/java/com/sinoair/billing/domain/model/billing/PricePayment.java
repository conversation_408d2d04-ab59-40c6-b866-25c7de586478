package com.sinoair.billing.domain.model.billing;

import java.math.BigDecimal;
import java.util.Date;

public class PricePayment implements Cloneable {
    private Integer ppId;

    private Integer sId;

    private String ppName;

    private String ppType;

    private BigDecimal ppPrice;

    private BigDecimal ppMinprice;

    private BigDecimal ppFirstweight;

    private BigDecimal ppFirstprice;

    private BigDecimal ppAdditionalweight;

    private BigDecimal ppAdditionalprice;

    private Integer ctCode;

    private Date ppEffectivedate;

    private Date ppExpireddate;

    private Date ppHandletime;

    private Integer ppUserId;

    private String ppStatus;

    private String ppSpecialKey;

    private BigDecimal ppBaseprice;

    private String ppAuto;

    private Integer pdSyscode;

    private String ppDest;

    private String ppAwbType;

    private String companyId;

    private String weightUnit;

    public Integer getPpId() {
        return ppId;
    }

    public void setPpId(Integer ppId) {
        this.ppId = ppId;
    }

    public Integer getsId() {
        return sId;
    }

    public void setsId(Integer sId) {
        this.sId = sId;
    }

    public String getPpName() {
        return ppName;
    }

    public void setPpName(String ppName) {
        this.ppName = ppName == null ? null : ppName.trim();
    }

    public String getPpType() {
        return ppType;
    }

    public void setPpType(String ppType) {
        this.ppType = ppType == null ? null : ppType.trim();
    }

    public BigDecimal getPpPrice() {
        return ppPrice;
    }

    public void setPpPrice(BigDecimal ppPrice) {
        this.ppPrice = ppPrice;
    }

    public BigDecimal getPpMinprice() {
        return ppMinprice;
    }

    public void setPpMinprice(BigDecimal ppMinprice) {
        this.ppMinprice = ppMinprice;
    }

    public BigDecimal getPpFirstweight() {
        return ppFirstweight;
    }

    public void setPpFirstweight(BigDecimal ppFirstweight) {
        this.ppFirstweight = ppFirstweight;
    }

    public BigDecimal getPpFirstprice() {
        return ppFirstprice;
    }

    public void setPpFirstprice(BigDecimal ppFirstprice) {
        this.ppFirstprice = ppFirstprice;
    }

    public BigDecimal getPpAdditionalweight() {
        return ppAdditionalweight;
    }

    public void setPpAdditionalweight(BigDecimal ppAdditionalweight) {
        this.ppAdditionalweight = ppAdditionalweight;
    }

    public BigDecimal getPpAdditionalprice() {
        return ppAdditionalprice;
    }

    public void setPpAdditionalprice(BigDecimal ppAdditionalprice) {
        this.ppAdditionalprice = ppAdditionalprice;
    }

    public Integer getCtCode() {
        return ctCode;
    }

    public void setCtCode(Integer ctCode) {
        this.ctCode = ctCode;
    }

    public Date getPpEffectivedate() {
        return ppEffectivedate;
    }

    public void setPpEffectivedate(Date ppEffectivedate) {
        this.ppEffectivedate = ppEffectivedate;
    }

    public Date getPpExpireddate() {
        return ppExpireddate;
    }

    public void setPpExpireddate(Date ppExpireddate) {
        this.ppExpireddate = ppExpireddate;
    }

    public Date getPpHandletime() {
        return ppHandletime;
    }

    public void setPpHandletime(Date ppHandletime) {
        this.ppHandletime = ppHandletime;
    }

    public Integer getPpUserId() {
        return ppUserId;
    }

    public void setPpUserId(Integer ppUserId) {
        this.ppUserId = ppUserId;
    }

    public String getPpStatus() {
        return ppStatus;
    }

    public void setPpStatus(String ppStatus) {
        this.ppStatus = ppStatus == null ? null : ppStatus.trim();
    }

    public String getPpSpecialKey() {
        return ppSpecialKey;
    }

    public void setPpSpecialKey(String ppSpecialKey) {
        this.ppSpecialKey = ppSpecialKey == null ? null : ppSpecialKey.trim();
    }

    public BigDecimal getPpBaseprice() {
        return ppBaseprice;
    }

    public void setPpBaseprice(BigDecimal ppBaseprice) {
        this.ppBaseprice = ppBaseprice;
    }

    public String getPpAuto() {
        return ppAuto;
    }

    public void setPpAuto(String ppAuto) {
        this.ppAuto = ppAuto == null ? null : ppAuto.trim();
    }

    public Integer getPdSyscode() {
        return pdSyscode;
    }

    public void setPdSyscode(Integer pdSyscode) {
        this.pdSyscode = pdSyscode;
    }

    public String getPpDest() {
        return ppDest;
    }

    public void setPpDest(String ppDest) {
        this.ppDest = ppDest == null ? null : ppDest.trim();
    }

    public String getPpAwbType() {
        return ppAwbType;
    }

    public void setPpAwbType(String ppAwbType) {
        this.ppAwbType = ppAwbType == null ? null : ppAwbType.trim();
    }

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId == null ? null : companyId.trim();
    }

    public String getWeightUnit() {
        return weightUnit;
    }

    public void setWeightUnit(String weightUnit) {
        this.weightUnit = weightUnit == null ? null : weightUnit.trim();
    }

    @Override
    public Object clone() throws CloneNotSupportedException {
        PricePayment pr = null;
        try {
            pr = (PricePayment) super.clone();
        } catch (CloneNotSupportedException e) {
            e.printStackTrace();
        }
        return pr;
    }
}