package com.sinoair.billing.domain.model.billing;

import java.math.BigDecimal;
import java.util.Date;

public class PriceReceipt implements Cloneable {
    private Integer prId;

    private Integer pcId;

    private String prName;

    private String eadCode;

    private String eastCode;

    private String prType;

    private BigDecimal prPrice;

    private BigDecimal prMinprice;

    private BigDecimal prFirstweight;

    private BigDecimal prFirstprice;

    private BigDecimal prAdditionalweight;

    private BigDecimal prAdditionalprice;

    private Integer ctCode;

    private Date prEffectivedate;

    private Date prExpireddate;

    private Date prHandletime;

    private Integer prUserId;

    private String prStatus;

    private BigDecimal prBaseprice;

    private String prSpecialKey;

    private Integer pdSyscode;

    private String prAwbType;

    private String prAuto;

    private String prDest;

    private String companyId;

    private String weightUnit;

    public Integer getPrId() {
        return prId;
    }

    public void setPrId(Integer prId) {
        this.prId = prId;
    }

    public Integer getPcId() {
        return pcId;
    }

    public void setPcId(Integer pcId) {
        this.pcId = pcId;
    }

    public String getPrName() {
        return prName;
    }

    public void setPrName(String prName) {
        this.prName = prName == null ? null : prName.trim();
    }

    public String getEadCode() {
        return eadCode;
    }

    public void setEadCode(String eadCode) {
        this.eadCode = eadCode == null ? null : eadCode.trim();
    }

    public String getEastCode() {
        return eastCode;
    }

    public void setEastCode(String eastCode) {
        this.eastCode = eastCode == null ? null : eastCode.trim();
    }

    public String getPrType() {
        return prType;
    }

    public void setPrType(String prType) {
        this.prType = prType == null ? null : prType.trim();
    }

    public BigDecimal getPrPrice() {
        return prPrice;
    }

    public void setPrPrice(BigDecimal prPrice) {
        this.prPrice = prPrice;
    }

    public BigDecimal getPrMinprice() {
        return prMinprice;
    }

    public void setPrMinprice(BigDecimal prMinprice) {
        this.prMinprice = prMinprice;
    }

    public BigDecimal getPrFirstweight() {
        return prFirstweight;
    }

    public void setPrFirstweight(BigDecimal prFirstweight) {
        this.prFirstweight = prFirstweight;
    }

    public BigDecimal getPrFirstprice() {
        return prFirstprice;
    }

    public void setPrFirstprice(BigDecimal prFirstprice) {
        this.prFirstprice = prFirstprice;
    }

    public BigDecimal getPrAdditionalweight() {
        return prAdditionalweight;
    }

    public void setPrAdditionalweight(BigDecimal prAdditionalweight) {
        this.prAdditionalweight = prAdditionalweight;
    }

    public BigDecimal getPrAdditionalprice() {
        return prAdditionalprice;
    }

    public void setPrAdditionalprice(BigDecimal prAdditionalprice) {
        this.prAdditionalprice = prAdditionalprice;
    }

    public Integer getCtCode() {
        return ctCode;
    }

    public void setCtCode(Integer ctCode) {
        this.ctCode = ctCode;
    }

    public Date getPrEffectivedate() {
        return prEffectivedate;
    }

    public void setPrEffectivedate(Date prEffectivedate) {
        this.prEffectivedate = prEffectivedate;
    }

    public Date getPrExpireddate() {
        return prExpireddate;
    }

    public void setPrExpireddate(Date prExpireddate) {
        this.prExpireddate = prExpireddate;
    }

    public Date getPrHandletime() {
        return prHandletime;
    }

    public void setPrHandletime(Date prHandletime) {
        this.prHandletime = prHandletime;
    }

    public Integer getPrUserId() {
        return prUserId;
    }

    public void setPrUserId(Integer prUserId) {
        this.prUserId = prUserId;
    }

    public String getPrStatus() {
        return prStatus;
    }

    public void setPrStatus(String prStatus) {
        this.prStatus = prStatus == null ? null : prStatus.trim();
    }

    public BigDecimal getPrBaseprice() {
        return prBaseprice;
    }

    public void setPrBaseprice(BigDecimal prBaseprice) {
        this.prBaseprice = prBaseprice;
    }

    public String getPrSpecialKey() {
        return prSpecialKey;
    }

    public void setPrSpecialKey(String prSpecialKey) {
        this.prSpecialKey = prSpecialKey == null ? null : prSpecialKey.trim();
    }

    public Integer getPdSyscode() {
        return pdSyscode;
    }

    public void setPdSyscode(Integer pdSyscode) {
        this.pdSyscode = pdSyscode;
    }

    public String getPrAwbType() {
        return prAwbType;
    }

    public void setPrAwbType(String prAwbType) {
        this.prAwbType = prAwbType == null ? null : prAwbType.trim();
    }

    public String getPrAuto() {
        return prAuto;
    }

    public void setPrAuto(String prAuto) {
        this.prAuto = prAuto == null ? null : prAuto.trim();
    }

    public String getPrDest() {
        return prDest;
    }

    public void setPrDest(String prDest) {
        this.prDest = prDest == null ? null : prDest.trim();
    }

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId == null ? null : companyId.trim();
    }

    public String getWeightUnit() {
        return weightUnit;
    }

    public void setWeightUnit(String weightUnit) {
        this.weightUnit = weightUnit == null ? null : weightUnit.trim();
    }

    @Override
    public Object clone() throws CloneNotSupportedException {
        PriceReceipt pr = null;
        try {
            pr = (PriceReceipt) super.clone();
        } catch (CloneNotSupportedException e) {
            e.printStackTrace();
        }
        return pr;
    }
}