package com.sinoair.billing.domain.model.billing;

import java.util.Date;

public class Product {
    private Integer pId;

    private String pCode;

    private String pName;

    private String companyId;

    private String occCompanyId;

    private String pTransmodeid;

    private String pServicetype;

    private String pDepart;

    private String pDest;

    private String pBusinesstype;

    private String pType;

    private String pDesc;

    private String pStatus;

    private Date pHandletime;

    private Integer pUserId;

    private String pTransmodeIdOriginal;

    private String pServicetypeOriginal;

    private String mPId;  //主产品ID

    public Integer getpId() {
        return pId;
    }

    public void setpId(Integer pId) {
        this.pId = pId;
    }

    public String getpCode() {
        return pCode;
    }

    public void setpCode(String pCode) {
        this.pCode = pCode == null ? null : pCode.trim();
    }

    public String getpName() {
        return pName;
    }

    public void setpName(String pName) {
        this.pName = pName == null ? null : pName.trim();
    }

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId == null ? null : companyId.trim();
    }

    public String getOccCompanyId() {
        return occCompanyId;
    }

    public void setOccCompanyId(String occCompanyId) {
        this.occCompanyId = occCompanyId == null ? null : occCompanyId.trim();
    }

    public String getpTransmodeid() {
        return pTransmodeid;
    }

    public void setpTransmodeid(String pTransmodeid) {
        this.pTransmodeid = pTransmodeid == null ? null : pTransmodeid.trim();
    }

    public String getpServicetype() {
        return pServicetype;
    }

    public void setpServicetype(String pServicetype) {
        this.pServicetype = pServicetype == null ? null : pServicetype.trim();
    }

    public String getpDepart() {
        return pDepart;
    }

    public void setpDepart(String pDepart) {
        this.pDepart = pDepart == null ? null : pDepart.trim();
    }

    public String getpDest() {
        return pDest;
    }

    public void setpDest(String pDest) {
        this.pDest = pDest == null ? null : pDest.trim();
    }

    public String getpBusinesstype() {
        return pBusinesstype;
    }

    public void setpBusinesstype(String pBusinesstype) {
        this.pBusinesstype = pBusinesstype == null ? null : pBusinesstype.trim();
    }

    public String getpType() {
        return pType;
    }

    public void setpType(String pType) {
        this.pType = pType == null ? null : pType.trim();
    }

    public String getpDesc() {
        return pDesc;
    }

    public void setpDesc(String pDesc) {
        this.pDesc = pDesc == null ? null : pDesc.trim();
    }

    public String getpStatus() {
        return pStatus;
    }

    public void setpStatus(String pStatus) {
        this.pStatus = pStatus == null ? null : pStatus.trim();
    }

    public Date getpHandletime() {
        return pHandletime;
    }

    public void setpHandletime(Date pHandletime) {
        this.pHandletime = pHandletime;
    }

    public Integer getpUserId() {
        return pUserId;
    }

    public void setpUserId(Integer pUserId) {
        this.pUserId = pUserId;
    }

    public String getpTransmodeIdOriginal() {
        return pTransmodeIdOriginal;
    }

    public void setpTransmodeIdOriginal(String pTransmodeIdOriginal) {
        this.pTransmodeIdOriginal = pTransmodeIdOriginal == null ? null : pTransmodeIdOriginal.trim();
    }

    public String getpServicetypeOriginal() {
        return pServicetypeOriginal;
    }

    public void setpServicetypeOriginal(String pServicetypeOriginal) {
        this.pServicetypeOriginal = pServicetypeOriginal == null ? null : pServicetypeOriginal;
    }

    public String getmPId() {
        return mPId;
    }

    public void setmPId(String mPId) {
        this.mPId = mPId;
    }
}