package com.sinoair.billing.domain.model.billing;


import java.math.BigDecimal;
import java.util.Date;

public class ReceiptRecord {
    private BigDecimal rrId;

    private String eawbPrintcode;

    private String mawbCode;

    private String prId;

    private Integer dmId;

    private String soCode;

    private String ctCode;

    private String companyId;

    private String rrName;

    private String rrType;

    private BigDecimal rrPlanAmount;

    private BigDecimal rrActualAmount;

    private String rrStatus;

    private int rrUserId;

    private Date rrHandletime;

    private String rrAwbType;

    private String rrRemark;

    private String eawbReference1;

    private String eawbReference2;

    private BigDecimal eawbChargeableweight;

    private Integer eawbHawbQty;

    private Date rrOccurtime;

    private String epKey;

    private Integer pdSyscode;

    private BigDecimal chargeableweight;

    private String outboundCompanyId;

    private String eawbIeType;

    private String rrGroupKey;

    private String rrDay;

    private String estimateStatus;

    private String eawbDestcountry;

    private String eawbDestination;

    private String eawbDepartcountry;

    private String eawbDeparture;

    private String bmsNum;

    private String bmsDirty;

    private String soMode;

    private Integer feeStatus;

    private Date auditTime;

    private Integer auditUserId;

    private Integer consumeId;

    private BigDecimal ctRate;

    private BigDecimal rrTotalRmb;

    private BigDecimal rrTotalfc;

    private Integer bmdSyscode;

    private String partitionCode;

    private String eawbSoCode;

    private String ptType;

    private String ccCode;

    public String getCcCode() {
        return ccCode;
    }

    public void setCcCode(String ccCode) {
        this.ccCode = ccCode;
    }

    public String getPtType() {
        return ptType;
    }

    public void setPtType(String ptType) {
        this.ptType = ptType;
    }

    public String getEawbSoCode() {
        return eawbSoCode;
    }

    public void setEawbSoCode(String eawbSoCode) {
        this.eawbSoCode = eawbSoCode;
    }

    public String getPartitionCode() {
        return partitionCode;
    }

    public void setPartitionCode(String partitionCode) {
        this.partitionCode = partitionCode;
    }

    public Integer getFeeStatus() {
        return feeStatus;
    }

    public void setFeeStatus(Integer feeStatus) {
        this.feeStatus = feeStatus;
    }

    public Date getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(Date auditTime) {
        this.auditTime = auditTime;
    }

    public Integer getAuditUserId() {
        return auditUserId;
    }

    public void setAuditUserId(Integer auditUserId) {
        this.auditUserId = auditUserId;
    }

    public Integer getConsumeId() {
        return consumeId;
    }

    public void setConsumeId(Integer consumeId) {
        this.consumeId = consumeId;
    }

    public BigDecimal getCtRate() {
        return ctRate;
    }

    public void setCtRate(BigDecimal ctRate) {
        this.ctRate = ctRate;
    }

    public BigDecimal getRrTotalRmb() {
        return rrTotalRmb;
    }

    public void setRrTotalRmb(BigDecimal rrTotalRmb) {
        this.rrTotalRmb = rrTotalRmb;
    }

    public BigDecimal getRrTotalfc() {
        return rrTotalfc;
    }

    public void setRrTotalfc(BigDecimal rrTotalfc) {
        this.rrTotalfc = rrTotalfc;
    }

    public Integer getBmdSyscode() {
        return bmdSyscode;
    }

    public void setBmdSyscode(Integer bmdSyscode) {
        this.bmdSyscode = bmdSyscode;
    }

    public String getSoMode() {
        return soMode;
    }

    public void setSoMode(String soMode) {
        this.soMode = soMode;
    }

    public String getEawbDestcountry() {
        return eawbDestcountry;
    }

    public void setEawbDestcountry(String eawbDestcountry) {
        this.eawbDestcountry = eawbDestcountry;
    }

    public String getEawbDestination() {
        return eawbDestination;
    }

    public void setEawbDestination(String eawbDestination) {
        this.eawbDestination = eawbDestination;
    }

    public String getEawbDepartcountry() {
        return eawbDepartcountry;
    }

    public void setEawbDepartcountry(String eawbDepartcountry) {
        this.eawbDepartcountry = eawbDepartcountry;
    }

    public String getEawbDeparture() {
        return eawbDeparture;
    }

    public void setEawbDeparture(String eawbDeparture) {
        this.eawbDeparture = eawbDeparture;
    }

    public String getBmsNum() {
        return bmsNum;
    }

    public void setBmsNum(String bmsNum) {
        this.bmsNum = bmsNum;
    }

    public String getBmsDirty() {
        return bmsDirty;
    }

    public void setBmsDirty(String bmsDirty) {
        this.bmsDirty = bmsDirty;
    }

    public String getEstimateStatus() {
        return estimateStatus;
    }

    public void setEstimateStatus(String estimateStatus) {
        this.estimateStatus = estimateStatus;
    }

    public String getOutboundCompanyId() {
        return outboundCompanyId;
    }

    public void setOutboundCompanyId(String outboundCompanyId) {
        this.outboundCompanyId = outboundCompanyId;
    }

    public BigDecimal getChargeableweight() {
        return chargeableweight;
    }

    public void setChargeableweight(BigDecimal chargeableweight) {
        this.chargeableweight = chargeableweight;
    }

    public Date getRrOccurtime() {
        return rrOccurtime;
    }

    public void setRrOccurtime(Date rrOccurtime) {
        this.rrOccurtime = rrOccurtime;
    }

    public BigDecimal getRrId() {
        return rrId;
    }

    public void setRrId(BigDecimal rrId) {
        this.rrId = rrId;
    }

    public String getEawbPrintcode() {
        return eawbPrintcode;
    }

    public void setEawbPrintcode(String eawbPrintcode) {
        this.eawbPrintcode = eawbPrintcode == null ? null : eawbPrintcode.trim();
    }

    public String getMawbCode() {
        return mawbCode;
    }

    public void setMawbCode(String mawbCode) {
        this.mawbCode = mawbCode == null ? null : mawbCode.trim();
    }

    public String getPrId() {
        return prId;
    }

    public void setPrId(String prId) {
        this.prId = prId;
    }

    public Integer getDmId() {
        return dmId;
    }

    public void setDmId(Integer dmId) {
        this.dmId = dmId;
    }

    public String getSoCode() {
        return soCode;
    }

    public void setSoCode(String soCode) {
        this.soCode = soCode == null ? null : soCode.trim();
    }

    public String getCtCode() {
        return ctCode;
    }

    public void setCtCode(String ctCode) {
        this.ctCode = ctCode == null ? null : ctCode.trim();
    }

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId == null ? null : companyId.trim();
    }

    public String getRrName() {
        return rrName;
    }

    public void setRrName(String rrName) {
        this.rrName = rrName == null ? null : rrName.trim();
    }

    public String getRrType() {
        return rrType;
    }

    public void setRrType(String rrType) {
        this.rrType = rrType == null ? null : rrType.trim();
    }

    public BigDecimal getRrPlanAmount() {
        return rrPlanAmount;
    }

    public void setRrPlanAmount(BigDecimal rrPlanAmount) {
        this.rrPlanAmount = rrPlanAmount;
    }

    public BigDecimal getRrActualAmount() {
        return rrActualAmount;
    }

    public void setRrActualAmount(BigDecimal rrActualAmount) {
        this.rrActualAmount = rrActualAmount;
    }

    public String getRrStatus() {
        return rrStatus;
    }

    public void setRrStatus(String rrStatus) {
        this.rrStatus = rrStatus == null ? null : rrStatus.trim();
    }

    public int getRrUserId() {
        return rrUserId;
    }

    public void setRrUserId(int rrUserId) {
        this.rrUserId = rrUserId;
    }

    public Date getRrHandletime() {
        return rrHandletime;
    }

    public void setRrHandletime(Date rrHandletime) {
        this.rrHandletime = rrHandletime;
    }

    public String getRrAwbType() {
        return rrAwbType;
    }

    public void setRrAwbType(String rrAwbType) {
        this.rrAwbType = rrAwbType == null ? null : rrAwbType.trim();
    }

    public String getRrRemark() {
        return rrRemark;
    }

    public void setRrRemark(String rrRemark) {
        this.rrRemark = rrRemark == null ? null : rrRemark.trim();
    }

    public String getEawbReference1() {
        return eawbReference1;
    }

    public void setEawbReference1(String eawbReference1) {
        this.eawbReference1 = eawbReference1 == null ? null : eawbReference1.trim();
    }

    public String getEawbReference2() {
        return eawbReference2;
    }

    public void setEawbReference2(String eawbReference2) {
        this.eawbReference2 = eawbReference2 == null ? null : eawbReference2.trim();
    }

    public BigDecimal getEawbChargeableweight() {
        return eawbChargeableweight;
    }

    public void setEawbChargeableweight(BigDecimal eawbChargeableweight) {
        this.eawbChargeableweight = eawbChargeableweight;
    }

    public Integer getEawbHawbQty() {
        return eawbHawbQty;
    }

    public void setEawbHawbQty(Integer eawbHawbQty) {
        this.eawbHawbQty = eawbHawbQty;
    }

    public String getEpKey() {
        return epKey;
    }

    public void setEpKey(String epKey) {
        this.epKey = epKey;
    }

    public Integer getPdSyscode() {
        return pdSyscode;
    }

    public void setPdSyscode(Integer pdSyscode) {
        this.pdSyscode = pdSyscode;
    }


    public String getRrGroupKey() {
        return rrGroupKey;
    }


    public void setRrGroupKey(String rrGroupKey) {
        this.rrGroupKey = rrGroupKey;
    }

    public String getRrDay() {
        return rrDay;
    }

    public void setRrDay(String rrDay) {
        this.rrDay = rrDay;
    }

    public String getEawbIeType() {
        return eawbIeType;
    }

    public void setEawbIeType(String eawbIeType) {
        this.eawbIeType = eawbIeType;
    }
}