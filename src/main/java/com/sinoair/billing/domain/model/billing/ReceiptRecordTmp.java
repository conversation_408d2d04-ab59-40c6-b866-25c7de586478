package com.sinoair.billing.domain.model.billing;

import java.math.BigDecimal;
import java.util.Date;

public class ReceiptRecordTmp {
    private BigDecimal rrId;

    private String eawbPrintcode;

    private String mawbCode;

    private Integer prId;

    private Integer dmId;

    private String soCode;

    private String ctCode;

    private String companyId;

    private String rrName;

    private String rrType;

    private BigDecimal rrPlanAmount;

    private BigDecimal rrActualAmount;

    private String rrStatus = "ON";

    private Short rrUserId = 1;

    private Date rrHandletime;

    private String rrAwbType;

    private String rrRemark;

    private String eawbReference1;

    private String eawbReference2;

    private BigDecimal eawbChargeableweight;

    private Integer eawbHawbQty;

    private Date rrOccurtime;

    private String epKey;

    private BigDecimal chargeableweight;

    private Integer pdSyscode;

    private String outboundCompanyId;

    private String estimateStatus;

    private String eawbIetype;

    private String eawbDestcountry;

    private String eawbDestination;

    private String eawbDepartcountry;

    private String eawbDeparture;

    private String bmsNum;

    private String bmsDirty;

    private Date rrOccurtime2;

    private String flightNumber;

    private String destSacId;

    private String specialKey = "N";

    private BigDecimal eawbCustdeclval;

    //private String eawbServiceType;

    private String eawbDestCity;

    private Integer eawbPieces;

    private BigDecimal weightValue;

    private String couSpecialKey;

    //临时字段
    private String startOccurtime;

    private String endOccurtime;

    private String eawbTrackingNo;

    private String spCompanyId;

    private String soMode;


    public BigDecimal getRrId() {
        return rrId;
    }

    public void setRrId(BigDecimal rrId) {
        this.rrId = rrId;
    }

    public String getEawbPrintcode() {
        return eawbPrintcode;
    }

    public void setEawbPrintcode(String eawbPrintcode) {
        this.eawbPrintcode = eawbPrintcode == null ? null : eawbPrintcode.trim();
    }

    public String getMawbCode() {
        return mawbCode;
    }

    public void setMawbCode(String mawbCode) {
        this.mawbCode = mawbCode == null ? null : mawbCode.trim();
    }

    public Integer getPrId() {
        return prId;
    }

    public void setPrId(Integer prId) {
        this.prId = prId;
    }

    public Integer getDmId() {
        return dmId;
    }

    public void setDmId(Integer dmId) {
        this.dmId = dmId;
    }

    public String getSoCode() {
        return soCode;
    }

    public void setSoCode(String soCode) {
        this.soCode = soCode == null ? null : soCode.trim();
    }

    public String getCtCode() {
        return ctCode;
    }

    public void setCtCode(String ctCode) {
        this.ctCode = ctCode == null ? null : ctCode.trim();
    }

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId == null ? null : companyId.trim();
    }

    public String getRrName() {
        return rrName;
    }

    public void setRrName(String rrName) {
        this.rrName = rrName == null ? null : rrName.trim();
    }

    public String getRrType() {
        return rrType;
    }

    public void setRrType(String rrType) {
        this.rrType = rrType == null ? null : rrType.trim();
    }

    public BigDecimal getRrPlanAmount() {
        return rrPlanAmount;
    }

    public void setRrPlanAmount(BigDecimal rrPlanAmount) {
        this.rrPlanAmount = rrPlanAmount;
    }

    public BigDecimal getRrActualAmount() {
        return rrActualAmount;
    }

    public void setRrActualAmount(BigDecimal rrActualAmount) {
        this.rrActualAmount = rrActualAmount;
    }

    public String getRrStatus() {
        return rrStatus;
    }

    public void setRrStatus(String rrStatus) {
        this.rrStatus = rrStatus == null ? null : rrStatus.trim();
    }

    public Short getRrUserId() {
        return rrUserId;
    }

    public void setRrUserId(Short rrUserId) {
        this.rrUserId = rrUserId;
    }

    public Date getRrHandletime() {
        return rrHandletime;
    }

    public void setRrHandletime(Date rrHandletime) {
        this.rrHandletime = rrHandletime;
    }

    public String getRrAwbType() {
        return rrAwbType;
    }

    public void setRrAwbType(String rrAwbType) {
        this.rrAwbType = rrAwbType == null ? null : rrAwbType.trim();
    }

    public String getRrRemark() {
        return rrRemark;
    }

    public void setRrRemark(String rrRemark) {
        this.rrRemark = rrRemark == null ? null : rrRemark.trim();
    }

    public String getEawbReference1() {
        return eawbReference1;
    }

    public void setEawbReference1(String eawbReference1) {
        this.eawbReference1 = eawbReference1 == null ? null : eawbReference1.trim();
    }

    public String getEawbReference2() {
        return eawbReference2;
    }

    public void setEawbReference2(String eawbReference2) {
        this.eawbReference2 = eawbReference2 == null ? null : eawbReference2.trim();
    }

    public BigDecimal getEawbChargeableweight() {
        return eawbChargeableweight;
    }

    public void setEawbChargeableweight(BigDecimal eawbChargeableweight) {
        this.eawbChargeableweight = eawbChargeableweight;
    }

    public Integer getEawbHawbQty() {
        return eawbHawbQty;
    }

    public void setEawbHawbQty(Integer eawbHawbQty) {
        this.eawbHawbQty = eawbHawbQty;
    }

    public Date getRrOccurtime() {
        return rrOccurtime;
    }

    public void setRrOccurtime(Date rrOccurtime) {
        this.rrOccurtime = rrOccurtime;
    }

    public String getEpKey() {
        return epKey;
    }

    public void setEpKey(String epKey) {
        this.epKey = epKey;
    }

    public BigDecimal getChargeableweight() {
        return chargeableweight;
    }

    public void setChargeableweight(BigDecimal chargeableweight) {
        this.chargeableweight = chargeableweight;
    }

    public Integer getPdSyscode() {
        return pdSyscode;
    }

    public void setPdSyscode(Integer pdSyscode) {
        this.pdSyscode = pdSyscode;
    }

    public String getOutboundCompanyId() {
        return outboundCompanyId;
    }

    public void setOutboundCompanyId(String outboundCompanyId) {
        this.outboundCompanyId = outboundCompanyId == null ? null : outboundCompanyId.trim();
    }

    public String getEstimateStatus() {
        return estimateStatus;
    }

    public void setEstimateStatus(String estimateStatus) {
        this.estimateStatus = estimateStatus == null ? null : estimateStatus.trim();
    }

    public String getEawbIetype() {
        return eawbIetype;
    }

    public void setEawbIetype(String eawbIetype) {
        this.eawbIetype = eawbIetype == null ? null : eawbIetype.trim();
    }

    public String getEawbDestcountry() {
        return eawbDestcountry;
    }

    public void setEawbDestcountry(String eawbDestcountry) {
        this.eawbDestcountry = eawbDestcountry == null ? null : eawbDestcountry.trim();
    }

    public String getEawbDestination() {
        return eawbDestination;
    }

    public void setEawbDestination(String eawbDestination) {
        this.eawbDestination = eawbDestination == null ? null : eawbDestination.trim();
    }

    public String getEawbDepartcountry() {
        return eawbDepartcountry;
    }

    public void setEawbDepartcountry(String eawbDepartcountry) {
        this.eawbDepartcountry = eawbDepartcountry == null ? null : eawbDepartcountry.trim();
    }

    public String getEawbDeparture() {
        return eawbDeparture;
    }

    public void setEawbDeparture(String eawbDeparture) {
        this.eawbDeparture = eawbDeparture == null ? null : eawbDeparture.trim();
    }

    public String getBmsNum() {
        return bmsNum;
    }

    public void setBmsNum(String bmsNum) {
        this.bmsNum = bmsNum == null ? null : bmsNum.trim();
    }

    public String getBmsDirty() {
        return bmsDirty;
    }

    public void setBmsDirty(String bmsDirty) {
        this.bmsDirty = bmsDirty == null ? null : bmsDirty.trim();
    }

    public Date getRrOccurtime2() {
        return rrOccurtime2;
    }

    public void setRrOccurtime2(Date rrOccurtime2) {
        this.rrOccurtime2 = rrOccurtime2;
    }

    public String getFlightNumber() {
        return flightNumber;
    }

    public void setFlightNumber(String flightNumber) {
        this.flightNumber = flightNumber;
    }

    public String getDestSacId() {
        return destSacId;
    }

    public void setDestSacId(String destSacId) {
        this.destSacId = destSacId;
    }

    public String getSpecialKey() {
        return specialKey;
    }

    public void setSpecialKey(String specialKey) {
        this.specialKey = specialKey;
    }

    public BigDecimal getEawbCustdeclval() {
        return eawbCustdeclval;
    }

    public void setEawbCustdeclval(BigDecimal eawbCustdeclval) {
        this.eawbCustdeclval = eawbCustdeclval;
    }

//    public String getEawbServiceType() {
//        return eawbServiceType;
//    }
//
//    public void setEawbServiceType(String eawbServiceType) {
//        this.eawbServiceType = eawbServiceType;
//    }

    public String getEawbDestCity() {
        return eawbDestCity;
    }

    public void setEawbDestCity(String eawbDestCity) {
        this.eawbDestCity = eawbDestCity;
    }

    public Integer getEawbPieces() {
        return eawbPieces;
    }

    public void setEawbPieces(Integer eawbPieces) {
        this.eawbPieces = eawbPieces;
    }

    public BigDecimal getWeightValue() {
        return weightValue;
    }

    public void setWeightValue(BigDecimal weightValue) {
        this.weightValue = weightValue;
    }

    public String getCouSpecialKey() {
        return couSpecialKey;
    }

    public void setCouSpecialKey(String couSpecialKey) {
        this.couSpecialKey = couSpecialKey;
    }

    public String getStartOccurtime() {
        return startOccurtime;
    }

    public void setStartOccurtime(String startOccurtime) {
        this.startOccurtime = startOccurtime;
    }

    public String getEndOccurtime() {
        return endOccurtime;
    }

    public void setEndOccurtime(String endOccurtime) {
        this.endOccurtime = endOccurtime;
    }

    public String getEawbTrackingNo() {
        return eawbTrackingNo;
    }

    public void setEawbTrackingNo(String eawbTrackingNo) {
        this.eawbTrackingNo = eawbTrackingNo;
    }

    public String getSpCompanyId() {
        return spCompanyId;
    }

    public void setSpCompanyId(String spCompanyId) {
        this.spCompanyId = spCompanyId;
    }

    public String getSoMode() {
        return soMode;
    }

    public void setSoMode(String soMode) {
        this.soMode = soMode;
    }
}
