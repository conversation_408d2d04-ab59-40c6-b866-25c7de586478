package com.sinoair.billing.domain.model.billing;

import java.math.BigDecimal;
import java.util.Date;

public class RecordLog {
    private BigDecimal logId;

    private String recordBatch;

    private String recordModule;

    private String recordType;

    private String recordName;

    private Short recordCount;

    private String recordParam;

    private String recordRemark;

    private BigDecimal allSize;

    private Long querySize;

    private Long failSize;

    private Long checkSize;

    private Long handleSize;

    private BigDecimal queryCost;

    private BigDecimal checkCost;

    private BigDecimal handleCost;

    private BigDecimal allCost;

    private Date taskStarttime;

    private Date logCreatetime;

    private Date logHandletime;

    private Date logSystime;

    private BigDecimal actualAmount;

    private Date handleDate;

    private String handleDateStr;

    private Date deadlineDate;

    private String deadlineDateStr;

    public BigDecimal getLogId() {
        return logId;
    }

    public void setLogId(BigDecimal logId) {
        this.logId = logId;
    }

    public String getRecordBatch() {
        return recordBatch;
    }

    public void setRecordBatch(String recordBatch) {
        this.recordBatch = recordBatch == null ? null : recordBatch.trim();
    }

    public String getRecordModule() {
        return recordModule;
    }

    public void setRecordModule(String recordModule) {
        this.recordModule = recordModule == null ? null : recordModule.trim();
    }

    public String getRecordType() {
        return recordType;
    }

    public void setRecordType(String recordType) {
        this.recordType = recordType == null ? null : recordType.trim();
    }

    public String getRecordName() {
        return recordName;
    }

    public void setRecordName(String recordName) {
        this.recordName = recordName == null ? null : recordName.trim();
    }

    public Short getRecordCount() {
        return recordCount;
    }

    public void setRecordCount(Short recordCount) {
        this.recordCount = recordCount;
    }

    public String getRecordParam() {
        return recordParam;
    }

    public void setRecordParam(String recordParam) {
        this.recordParam = recordParam == null ? null : recordParam.trim();
    }

    public String getRecordRemark() {
        return recordRemark;
    }

    public void setRecordRemark(String recordRemark) {
        this.recordRemark = recordRemark == null ? null : recordRemark.trim();
    }

    public BigDecimal getAllSize() {
        return allSize;
    }

    public void setAllSize(BigDecimal allSize) {
        this.allSize = allSize;
    }

    public Long getQuerySize() {
        return querySize;
    }

    public void setQuerySize(Long querySize) {
        this.querySize = querySize;
    }

    public Long getFailSize() {
        return failSize;
    }

    public void setFailSize(Long failSize) {
        this.failSize = failSize;
    }

    public Long getCheckSize() {
        return checkSize;
    }

    public void setCheckSize(Long checkSize) {
        this.checkSize = checkSize;
    }

    public Long getHandleSize() {
        return handleSize;
    }

    public void setHandleSize(Long handleSize) {
        this.handleSize = handleSize;
    }

    public BigDecimal getQueryCost() {
        return queryCost;
    }

    public void setQueryCost(BigDecimal queryCost) {
        this.queryCost = queryCost;
    }

    public BigDecimal getCheckCost() {
        return checkCost;
    }

    public void setCheckCost(BigDecimal checkCost) {
        this.checkCost = checkCost;
    }

    public BigDecimal getHandleCost() {
        return handleCost;
    }

    public void setHandleCost(BigDecimal handleCost) {
        this.handleCost = handleCost;
    }

    public BigDecimal getAllCost() {
        return allCost;
    }

    public void setAllCost(BigDecimal allCost) {
        this.allCost = allCost;
    }

    public Date getTaskStarttime() {
        return taskStarttime;
    }

    public void setTaskStarttime(Date taskStarttime) {
        this.taskStarttime = taskStarttime;
    }

    public Date getLogCreatetime() {
        return logCreatetime;
    }

    public void setLogCreatetime(Date logCreatetime) {
        this.logCreatetime = logCreatetime;
    }

    public Date getLogHandletime() {
        return logHandletime;
    }

    public void setLogHandletime(Date logHandletime) {
        this.logHandletime = logHandletime;
    }

    public Date getLogSystime() {
        return logSystime;
    }

    public void setLogSystime(Date logSystime) {
        this.logSystime = logSystime;
    }

    public BigDecimal getActualAmount() {
        return actualAmount;
    }

    public void setActualAmount(BigDecimal actualAmount) {
        this.actualAmount = actualAmount;
    }

    public Date getHandleDate() {
        return handleDate;
    }

    public void setHandleDate(Date handleDate) {
        this.handleDate = handleDate;
    }

    public String getHandleDateStr() {
        return handleDateStr;
    }

    public void setHandleDateStr(String handleDateStr) {
        this.handleDateStr = handleDateStr == null ? null : handleDateStr.trim();
    }

    public Date getDeadlineDate() {
        return deadlineDate;
    }

    public void setDeadlineDate(Date deadlineDate) {
        this.deadlineDate = deadlineDate;
    }

    public String getDeadlineDateStr() {
        return deadlineDateStr;
    }

    public void setDeadlineDateStr(String deadlineDateStr) {
        this.deadlineDateStr = deadlineDateStr == null ? null : deadlineDateStr.trim();
    }
}