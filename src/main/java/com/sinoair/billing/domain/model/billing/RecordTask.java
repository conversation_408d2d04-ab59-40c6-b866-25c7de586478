package com.sinoair.billing.domain.model.billing;

import java.math.BigDecimal;
import java.util.Date;

public class RecordTask {
    private BigDecimal taskId;

    private String taskType;

    private String taskSoCode;

    private String taskStartDate;

    private String taskEndDate;

    private String taskKeyentrytime;

    private String taskRemark;

    private String taskStatus;

    private Date taskCreatetime;

    private Date taskHandletime;

    public BigDecimal getTaskId() {
        return taskId;
    }

    public void setTaskId(BigDecimal taskId) {
        this.taskId = taskId;
    }

    public String getTaskType() {
        return taskType;
    }

    public void setTaskType(String taskType) {
        this.taskType = taskType == null ? null : taskType.trim();
    }

    public String getTaskSoCode() {
        return taskSoCode;
    }

    public void setTaskSoCode(String taskSoCode) {
        this.taskSoCode = taskSoCode == null ? null : taskSoCode.trim();
    }

    public String getTaskStartDate() {
        return taskStartDate;
    }

    public void setTaskStartDate(String taskStartDate) {
        this.taskStartDate = taskStartDate == null ? null : taskStartDate.trim();
    }

    public String getTaskEndDate() {
        return taskEndDate;
    }

    public void setTaskEndDate(String taskEndDate) {
        this.taskEndDate = taskEndDate == null ? null : taskEndDate.trim();
    }

    public String getTaskKeyentrytime() {
        return taskKeyentrytime;
    }

    public void setTaskKeyentrytime(String taskKeyentrytime) {
        this.taskKeyentrytime = taskKeyentrytime == null ? null : taskKeyentrytime.trim();
    }

    public String getTaskRemark() {
        return taskRemark;
    }

    public void setTaskRemark(String taskRemark) {
        this.taskRemark = taskRemark == null ? null : taskRemark.trim();
    }

    public String getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(String taskStatus) {
        this.taskStatus = taskStatus == null ? null : taskStatus.trim();
    }

    public Date getTaskCreatetime() {
        return taskCreatetime;
    }

    public void setTaskCreatetime(Date taskCreatetime) {
        this.taskCreatetime = taskCreatetime;
    }

    public Date getTaskHandletime() {
        return taskHandletime;
    }

    public void setTaskHandletime(Date taskHandletime) {
        this.taskHandletime = taskHandletime;
    }
}