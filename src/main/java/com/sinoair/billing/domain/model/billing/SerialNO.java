package com.sinoair.billing.domain.model.billing;

import java.util.Date;

public class SerialNO {

    public static final String SNVALID_Y = "Y";
    public static final String SNVALID_N = "N";
    public static final String SNPURPOSE_DM = "DM";
    public static final String SNPURPOSE_CM = "CM";
    public static final String SNTYPE_INCREASE = "INCREASE";
    public static final String SNTYPE_INITDAILY = "INITDAILY";
    public static final String SNPURPOSE_TRANSFER = "TRANSFER";

    private Long snId;

    private String snName;

    private Long projectId;

    private Long companyId;

    private String snPurpose;

    private Long snBeginNo;

    private Long snCurrentNo;

    private Long snEndNo;

    private Long snAlertNo;

    private String snType;

    private String snValid;

    private Date snDate;

    private String snComments;

    public Long getSnId() {
        return snId;
    }

    public void setSnId(Long snId) {
        this.snId = snId;
    }

    public String getSnName() {
        return snName;
    }

    public void setSnName(String snName) {
        this.snName = snName == null ? null : snName.trim();
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public String getSnPurpose() {
        return snPurpose;
    }

    public void setSnPurpose(String snPurpose) {
        this.snPurpose = snPurpose == null ? null : snPurpose.trim();
    }

    public Long getSnBeginNo() {
        return snBeginNo;
    }

    public void setSnBeginNo(Long snBeginNo) {
        this.snBeginNo = snBeginNo;
    }

    public Long getSnCurrentNo() {
        return snCurrentNo;
    }

    public void setSnCurrentNo(Long snCurrentNo) {
        this.snCurrentNo = snCurrentNo;
    }

    public Long getSnEndNo() {
        return snEndNo;
    }

    public void setSnEndNo(Long snEndNo) {
        this.snEndNo = snEndNo;
    }

    public Long getSnAlertNo() {
        return snAlertNo;
    }

    public void setSnAlertNo(Long snAlertNo) {
        this.snAlertNo = snAlertNo;
    }

    public String getSnType() {
        return snType;
    }

    public void setSnType(String snType) {
        this.snType = snType == null ? null : snType.trim();
    }

    public String getSnValid() {
        return snValid;
    }

    public void setSnValid(String snValid) {
        this.snValid = snValid == null ? null : snValid.trim();
    }

    public Date getSnDate() {
        return snDate;
    }

    public void setSnDate(Date snDate) {
        this.snDate = snDate;
    }

    public String getSnComments() {
        return snComments;
    }

    public void setSnComments(String snComments) {
        this.snComments = snComments == null ? null : snComments.trim();
    }
}