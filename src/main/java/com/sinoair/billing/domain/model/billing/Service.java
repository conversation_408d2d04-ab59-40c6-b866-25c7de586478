package com.sinoair.billing.domain.model.billing;

import java.util.Date;

public class Service {
    private Integer sId;

    private String sName;

    private String transmodeId;

    private String soCode;

    private String eadCode;

    private String eastCode;

    private String sDesc;

    private String sAwbType;

    private String sStatus;

    private Date sHandletime;

    private Integer sUserId;

    public Integer getsId() {
        return sId;
    }

    public void setsId(Integer sId) {
        this.sId = sId;
    }

    public String getsName() {
        return sName;
    }

    public void setsName(String sName) {
        this.sName = sName == null ? null : sName.trim();
    }

    public String getTransmodeId() {
        return transmodeId;
    }

    public void setTransmodeId(String transmodeId) {
        this.transmodeId = transmodeId == null ? null : transmodeId.trim();
    }

    public String getSoCode() {
        return soCode;
    }

    public void setSoCode(String soCode) {
        this.soCode = soCode == null ? null : soCode.trim();
    }

    public String getEadCode() {
        return eadCode;
    }

    public void setEadCode(String eadCode) {
        this.eadCode = eadCode == null ? null : eadCode.trim();
    }

    public String getEastCode() {
        return eastCode;
    }

    public void setEastCode(String eastCode) {
        this.eastCode = eastCode == null ? null : eastCode.trim();
    }

    public String getsDesc() {
        return sDesc;
    }

    public void setsDesc(String sDesc) {
        this.sDesc = sDesc == null ? null : sDesc.trim();
    }

    public String getsAwbType() {
        return sAwbType;
    }

    public void setsAwbType(String sAwbType) {
        this.sAwbType = sAwbType == null ? null : sAwbType.trim();
    }

    public String getsStatus() {
        return sStatus;
    }

    public void setsStatus(String sStatus) {
        this.sStatus = sStatus == null ? null : sStatus.trim();
    }

    public Date getsHandletime() {
        return sHandletime;
    }

    public void setsHandletime(Date sHandletime) {
        this.sHandletime = sHandletime;
    }

    public Integer getsUserId() {
        return sUserId;
    }

    public void setsUserId(Integer sUserId) {
        this.sUserId = sUserId;
    }
}