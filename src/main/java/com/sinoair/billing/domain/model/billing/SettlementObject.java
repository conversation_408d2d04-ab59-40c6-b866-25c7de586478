package com.sinoair.billing.domain.model.billing;

import java.math.BigDecimal;
import java.util.Date;

public class SettlementObject {
    private Integer soSyscode;

    private String soCode;

    private String sacId;

    private String cCode;

    private String soFinancialcode;

    private String soBank;

    private String soBankid;

    private String soBankfc;

    private String soBankidfc;

    private String soName;

    private String soEname;

    private String soContactman;

    private String soAddress;

    private String soPostcode;

    private String soTelephone;

    private String soFax;

    private String soEmail;

    private String soStatus;

    private String soLmCodeDefault;

    private String soSacCode;

    private BigDecimal soEIdHandler;

    private Date soHandletime;

    private Date soArchivetime;

    private String custCode;

    private String custSiteCode;

    private String vendorCode;

    private String vendorSiteCode;

    private String soInternalmark;

    private String soCc;

    private String soVendorType;

    private String soType;

    private String cdhNote;

    private String soShortname;

    private String soNameOrg;

    private Integer taxRate;

    private String soKey;

    private String soErp;

    private BigDecimal soBalance;

    private BigDecimal threshold;

    private String approver;

    private String soCate;

    private String soMode;

    private String soBalanceStatus;

    private Date soSmsSendtime;

    private String ctCode;

    public Integer getSoSyscode() {
        return soSyscode;
    }

    public void setSoSyscode(Integer soSyscode) {
        this.soSyscode = soSyscode;
    }

    public String getSoCode() {
        return soCode;
    }

    public void setSoCode(String soCode) {
        this.soCode = soCode == null ? null : soCode.trim();
    }

    public String getSacId() {
        return sacId;
    }

    public void setSacId(String sacId) {
        this.sacId = sacId == null ? null : sacId.trim();
    }

    public String getcCode() {
        return cCode;
    }

    public void setcCode(String cCode) {
        this.cCode = cCode == null ? null : cCode.trim();
    }

    public String getSoFinancialcode() {
        return soFinancialcode;
    }

    public void setSoFinancialcode(String soFinancialcode) {
        this.soFinancialcode = soFinancialcode == null ? null : soFinancialcode.trim();
    }

    public String getSoBank() {
        return soBank;
    }

    public void setSoBank(String soBank) {
        this.soBank = soBank == null ? null : soBank.trim();
    }

    public String getSoBankid() {
        return soBankid;
    }

    public void setSoBankid(String soBankid) {
        this.soBankid = soBankid == null ? null : soBankid.trim();
    }

    public String getSoBankfc() {
        return soBankfc;
    }

    public void setSoBankfc(String soBankfc) {
        this.soBankfc = soBankfc == null ? null : soBankfc.trim();
    }

    public String getSoBankidfc() {
        return soBankidfc;
    }

    public void setSoBankidfc(String soBankidfc) {
        this.soBankidfc = soBankidfc == null ? null : soBankidfc.trim();
    }

    public String getSoName() {
        return soName;
    }

    public void setSoName(String soName) {
        this.soName = soName == null ? null : soName.trim();
    }

    public String getSoEname() {
        return soEname;
    }

    public void setSoEname(String soEname) {
        this.soEname = soEname == null ? null : soEname.trim();
    }

    public String getSoContactman() {
        return soContactman;
    }

    public void setSoContactman(String soContactman) {
        this.soContactman = soContactman == null ? null : soContactman.trim();
    }

    public String getSoAddress() {
        return soAddress;
    }

    public void setSoAddress(String soAddress) {
        this.soAddress = soAddress == null ? null : soAddress.trim();
    }

    public String getSoPostcode() {
        return soPostcode;
    }

    public void setSoPostcode(String soPostcode) {
        this.soPostcode = soPostcode == null ? null : soPostcode.trim();
    }

    public String getSoTelephone() {
        return soTelephone;
    }

    public void setSoTelephone(String soTelephone) {
        this.soTelephone = soTelephone == null ? null : soTelephone.trim();
    }

    public String getSoFax() {
        return soFax;
    }

    public void setSoFax(String soFax) {
        this.soFax = soFax == null ? null : soFax.trim();
    }

    public String getSoEmail() {
        return soEmail;
    }

    public void setSoEmail(String soEmail) {
        this.soEmail = soEmail == null ? null : soEmail.trim();
    }

    public String getSoStatus() {
        return soStatus;
    }

    public void setSoStatus(String soStatus) {
        this.soStatus = soStatus == null ? null : soStatus.trim();
    }

    public String getSoLmCodeDefault() {
        return soLmCodeDefault;
    }

    public void setSoLmCodeDefault(String soLmCodeDefault) {
        this.soLmCodeDefault = soLmCodeDefault == null ? null : soLmCodeDefault.trim();
    }

    public String getSoSacCode() {
        return soSacCode;
    }

    public void setSoSacCode(String soSacCode) {
        this.soSacCode = soSacCode == null ? null : soSacCode.trim();
    }

    public BigDecimal getSoEIdHandler() {
        return soEIdHandler;
    }

    public void setSoEIdHandler(BigDecimal soEIdHandler) {
        this.soEIdHandler = soEIdHandler;
    }

    public Date getSoHandletime() {
        return soHandletime;
    }

    public void setSoHandletime(Date soHandletime) {
        this.soHandletime = soHandletime;
    }

    public Date getSoArchivetime() {
        return soArchivetime;
    }

    public void setSoArchivetime(Date soArchivetime) {
        this.soArchivetime = soArchivetime;
    }

    public String getCustCode() {
        return custCode;
    }

    public void setCustCode(String custCode) {
        this.custCode = custCode == null ? null : custCode.trim();
    }

    public String getCustSiteCode() {
        return custSiteCode;
    }

    public void setCustSiteCode(String custSiteCode) {
        this.custSiteCode = custSiteCode == null ? null : custSiteCode.trim();
    }

    public String getVendorCode() {
        return vendorCode;
    }

    public void setVendorCode(String vendorCode) {
        this.vendorCode = vendorCode == null ? null : vendorCode.trim();
    }

    public String getVendorSiteCode() {
        return vendorSiteCode;
    }

    public void setVendorSiteCode(String vendorSiteCode) {
        this.vendorSiteCode = vendorSiteCode == null ? null : vendorSiteCode.trim();
    }

    public String getSoInternalmark() {
        return soInternalmark;
    }

    public void setSoInternalmark(String soInternalmark) {
        this.soInternalmark = soInternalmark == null ? null : soInternalmark.trim();
    }

    public String getSoCc() {
        return soCc;
    }

    public void setSoCc(String soCc) {
        this.soCc = soCc == null ? null : soCc.trim();
    }

    public String getSoVendorType() {
        return soVendorType;
    }

    public void setSoVendorType(String soVendorType) {
        this.soVendorType = soVendorType == null ? null : soVendorType.trim();
    }

    public String getSoType() {
        return soType;
    }

    public void setSoType(String soType) {
        this.soType = soType == null ? null : soType.trim();
    }

    public String getCdhNote() {
        return cdhNote;
    }

    public void setCdhNote(String cdhNote) {
        this.cdhNote = cdhNote == null ? null : cdhNote.trim();
    }

    public String getSoShortname() {
        return soShortname;
    }

    public void setSoShortname(String soShortname) {
        this.soShortname = soShortname == null ? null : soShortname.trim();
    }

    public String getSoNameOrg() {
        return soNameOrg;
    }

    public void setSoNameOrg(String soNameOrg) {
        this.soNameOrg = soNameOrg == null ? null : soNameOrg.trim();
    }

    public Integer getTaxRate() {
        return taxRate;
    }

    public void setTaxRate(Integer taxRate) {
        this.taxRate = taxRate;
    }

    public String getSoKey() {
        return soKey;
    }

    public void setSoKey(String soKey) {
        this.soKey = soKey;
    }

    public String getSoErp() {
        return soErp;
    }

    public void setSoErp(String soErp) {
        this.soErp = soErp;
    }

    public BigDecimal getSoBalance() {
        return soBalance;
    }

    public void setSoBalance(BigDecimal soBalance) {
        this.soBalance = soBalance;
    }

    public BigDecimal getThreshold() {
        return threshold;
    }

    public void setThreshold(BigDecimal threshold) {
        this.threshold = threshold;
    }

    public String getApprover() {
        return approver;
    }

    public void setApprover(String approver) {
        this.approver = approver;
    }

    public String getSoCate() {
        return soCate;
    }

    public void setSoCate(String soCate) {
        this.soCate = soCate;
    }

    public String getSoMode() {
        return soMode;
    }

    public void setSoMode(String soMode) {
        this.soMode = soMode;
    }

    public String getSoBalanceStatus() {
        return soBalanceStatus;
    }

    public void setSoBalanceStatus(String soBalanceStatus) {
        this.soBalanceStatus = soBalanceStatus;
    }

    public Date getSoSmsSendtime() {
        return soSmsSendtime;
    }

    public void setSoSmsSendtime(Date soSmsSendtime) {
        this.soSmsSendtime = soSmsSendtime;
    }

    public String getCtCode() {
        return ctCode;
    }

    public void setCtCode(String ctCode) {
        this.ctCode = ctCode;
    }
}