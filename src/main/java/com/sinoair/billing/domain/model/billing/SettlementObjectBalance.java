package com.sinoair.billing.domain.model.billing;

import java.math.BigDecimal;
import java.util.Date;

public class SettlementObjectBalance {
    private Long sbId;

    private String soCode;

    private String sbType;

    private BigDecimal sbAmount;

    private String amountType;

    private String eawbPrintcode;

    private BigDecimal eawbChargeableweight;

    private String eawbReference1;

    private String eawbReference2;

    private String eawbReference3;

    private String approver;

    private String sbFlowno;

    private String sbExplain;

    private String sbRemark;

    private Date rrOccurtime;

    private Date sbHandletime;

    private Integer sbHandler;

    private Integer soSysCode;

    private BigDecimal soBalance;

    private Integer rrID;

    private String prName;

    public Long getSbId() {
        return sbId;
    }

    public void setSbId(Long sbId) {
        this.sbId = sbId;
    }

    public String getSoCode() {
        return soCode;
    }

    public void setSoCode(String soCode) {
        this.soCode = soCode == null ? null : soCode.trim();
    }

    public String getSbType() {
        return sbType;
    }

    public void setSbType(String sbType) {
        this.sbType = sbType == null ? null : sbType.trim();
    }

    public BigDecimal getSbAmount() {
        return sbAmount;
    }

    public void setSbAmount(BigDecimal sbAmount) {
        this.sbAmount = sbAmount;
    }

    public String getAmountType() {
        return amountType;
    }

    public void setAmountType(String amountType) {
        this.amountType = amountType == null ? null : amountType.trim();
    }

    public String getEawbPrintcode() {
        return eawbPrintcode;
    }

    public void setEawbPrintcode(String eawbPrintcode) {
        this.eawbPrintcode = eawbPrintcode == null ? null : eawbPrintcode.trim();
    }

    public BigDecimal getEawbChargeableweight() {
        return eawbChargeableweight;
    }

    public void setEawbChargeableweight(BigDecimal eawbChargeableweight) {
        this.eawbChargeableweight = eawbChargeableweight;
    }

    public String getEawbReference1() {
        return eawbReference1;
    }

    public void setEawbReference1(String eawbReference1) {
        this.eawbReference1 = eawbReference1 == null ? null : eawbReference1.trim();
    }

    public String getEawbReference2() {
        return eawbReference2;
    }

    public void setEawbReference2(String eawbReference2) {
        this.eawbReference2 = eawbReference2 == null ? null : eawbReference2.trim();
    }

    public String getEawbReference3() {
        return eawbReference3;
    }

    public void setEawbReference3(String eawbReference3) {
        this.eawbReference3 = eawbReference3 == null ? null : eawbReference3.trim();
    }

    public String getApprover() {
        return approver;
    }

    public void setApprover(String approver) {
        this.approver = approver == null ? null : approver.trim();
    }

    public String getSbFlowno() {
        return sbFlowno;
    }

    public void setSbFlowno(String sbFlowno) {
        this.sbFlowno = sbFlowno == null ? null : sbFlowno.trim();
    }

    public String getSbExplain() {
        return sbExplain;
    }

    public void setSbExplain(String sbExplain) {
        this.sbExplain = sbExplain == null ? null : sbExplain.trim();
    }

    public String getSbRemark() {
        return sbRemark;
    }

    public void setSbRemark(String sbRemark) {
        this.sbRemark = sbRemark == null ? null : sbRemark.trim();
    }

    public Date getRrOccurtime() {
        return rrOccurtime;
    }

    public void setRrOccurtime(Date rrOccurtime) {
        this.rrOccurtime = rrOccurtime;
    }

    public Date getSbHandletime() {
        return sbHandletime;
    }

    public void setSbHandletime(Date sbHandletime) {
        this.sbHandletime = sbHandletime;
    }

    public Integer getSbHandler() {
        return sbHandler;
    }

    public void setSbHandler(Integer sbHandler) {
        this.sbHandler = sbHandler;
    }

    public Integer getSoSysCode() {
        return soSysCode;
    }

    public void setSoSysCode(Integer soSysCode) {
        this.soSysCode = soSysCode;
    }

    public BigDecimal getSoBalance() {
        return soBalance;
    }

    public void setSoBalance(BigDecimal soBalance) {
        this.soBalance = soBalance;
    }

    public Integer getRrID() {
        return rrID;
    }

    public void setRrID(Integer rrID) {
        this.rrID = rrID;
    }

    public String getPrName() {
        return prName;
    }

    public void setPrName(String prName) {
        this.prName = prName;
    }
}