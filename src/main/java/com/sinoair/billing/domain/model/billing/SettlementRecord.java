package com.sinoair.billing.domain.model.billing;

import java.math.BigDecimal;
import java.util.Date;

public class SettlementRecord {
    private String reference1;

    private String ctCode;

    private BigDecimal srAmount;

    private Date srHandletime;

    private String pullState;

    private Date pullTime;

    private String srRemark;

    private String reference2;

    private String ctName;

    public String getReference1() {
        return reference1;
    }

    public void setReference1(String reference1) {
        this.reference1 = reference1 == null ? null : reference1.trim();
    }

    public String getCtCode() {
        return ctCode;
    }

    public void setCtCode(String ctCode) {
        this.ctCode = ctCode == null ? null : ctCode.trim();
    }

    public BigDecimal getSrAmount() {
        return srAmount;
    }

    public void setSrAmount(BigDecimal srAmount) {
        this.srAmount = srAmount;
    }

    public Date getSrHandletime() {
        return srHandletime;
    }

    public void setSrHandletime(Date srHandletime) {
        this.srHandletime = srHandletime;
    }

    public String getPullState() {
        return pullState;
    }

    public void setPullState(String pullState) {
        this.pullState = pullState == null ? null : pullState.trim();
    }

    public Date getPullTime() {
        return pullTime;
    }

    public void setPullTime(Date pullTime) {
        this.pullTime = pullTime;
    }

    public String getSrRemark() {
        return srRemark;
    }

    public void setSrRemark(String srRemark) {
        this.srRemark = srRemark == null ? null : srRemark.trim();
    }

    public String getReference2() {
        return reference2;
    }

    public void setReference2(String reference2) {
        this.reference2 = reference2 == null ? null : reference2.trim();
    }

    public String getCtName() {
        return ctName;
    }

    public void setCtName(String ctName) {
        this.ctName = ctName;
    }
}