package com.sinoair.billing.domain.model.billing;

import java.math.BigDecimal;
import java.util.Date;

public class SinoDebitNote {
    private Integer sdId;

    private String companyId;

    private String soCode;

    private String ctCode;

    private BigDecimal sdCurrencyrate;

    private String pdName;

    private Integer pdSyscode;

    private BigDecimal sdTotalrmb;

    private BigDecimal sdTotalfc;

    private BigDecimal soTax;

    private BigDecimal taxAmount;

    private BigDecimal notaxAmount;

    private BigDecimal taxAmountFc;

    private BigDecimal notaxAmountFc;

    private String invoiceCode;

    private Integer sdUserId;

    private String sdStatus;

    private Date sdCreateTime;

    private Date sdHandleTime;

    private String sdDirty;

    private Date sdDirtyTime;

    private String sdCode;

    private BigDecimal sdPlanAmount;

    private Date sdStartTime;

    private Date sdEndTime;

    private Integer sdTotalPieces;

    private BigDecimal sdTotalWeight;

    private String sdRemark;

    private String sdType;

    private String estimateStatus;

    public Integer getSdId() {
        return sdId;
    }

    public void setSdId(Integer sdId) {
        this.sdId = sdId;
    }

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId == null ? null : companyId.trim();
    }

    public String getSoCode() {
        return soCode;
    }

    public void setSoCode(String soCode) {
        this.soCode = soCode == null ? null : soCode.trim();
    }

    public String getCtCode() {
        return ctCode;
    }

    public void setCtCode(String ctCode) {
        this.ctCode = ctCode == null ? null : ctCode.trim();
    }

    public BigDecimal getSdCurrencyrate() {
        return sdCurrencyrate;
    }

    public void setSdCurrencyrate(BigDecimal sdCurrencyrate) {
        this.sdCurrencyrate = sdCurrencyrate;
    }

    public String getPdName() {
        return pdName;
    }

    public void setPdName(String pdName) {
        this.pdName = pdName == null ? null : pdName.trim();
    }

    public Integer getPdSyscode() {
        return pdSyscode;
    }

    public void setPdSyscode(Integer pdSyscode) {
        this.pdSyscode = pdSyscode;
    }

    public BigDecimal getSdTotalrmb() {
        return sdTotalrmb;
    }

    public void setSdTotalrmb(BigDecimal sdTotalrmb) {
        this.sdTotalrmb = sdTotalrmb;
    }

    public BigDecimal getSdTotalfc() {
        return sdTotalfc;
    }

    public void setSdTotalfc(BigDecimal sdTotalfc) {
        this.sdTotalfc = sdTotalfc;
    }

    public BigDecimal getSoTax() {
        return soTax;
    }

    public void setSoTax(BigDecimal soTax) {
        this.soTax = soTax;
    }

    public BigDecimal getTaxAmount() {
        return taxAmount;
    }

    public void setTaxAmount(BigDecimal taxAmount) {
        this.taxAmount = taxAmount;
    }

    public BigDecimal getNotaxAmount() {
        return notaxAmount;
    }

    public void setNotaxAmount(BigDecimal notaxAmount) {
        this.notaxAmount = notaxAmount;
    }

    public BigDecimal getTaxAmountFc() {
        return taxAmountFc;
    }

    public void setTaxAmountFc(BigDecimal taxAmountFc) {
        this.taxAmountFc = taxAmountFc;
    }

    public BigDecimal getNotaxAmountFc() {
        return notaxAmountFc;
    }

    public void setNotaxAmountFc(BigDecimal notaxAmountFc) {
        this.notaxAmountFc = notaxAmountFc;
    }

    public String getInvoiceCode() {
        return invoiceCode;
    }

    public void setInvoiceCode(String invoiceCode) {
        this.invoiceCode = invoiceCode == null ? null : invoiceCode.trim();
    }

    public Integer getSdUserId() {
        return sdUserId;
    }

    public void setSdUserId(Integer sdUserId) {
        this.sdUserId = sdUserId;
    }

    public String getSdStatus() {
        return sdStatus;
    }

    public void setSdStatus(String sdStatus) {
        this.sdStatus = sdStatus == null ? null : sdStatus.trim();
    }

    public Date getSdCreateTime() {
        return sdCreateTime;
    }

    public void setSdCreateTime(Date sdCreateTime) {
        this.sdCreateTime = sdCreateTime;
    }

    public Date getSdHandleTime() {
        return sdHandleTime;
    }

    public void setSdHandleTime(Date sdHandleTime) {
        this.sdHandleTime = sdHandleTime;
    }

    public String getSdDirty() {
        return sdDirty;
    }

    public void setSdDirty(String sdDirty) {
        this.sdDirty = sdDirty == null ? null : sdDirty.trim();
    }

    public Date getSdDirtyTime() {
        return sdDirtyTime;
    }

    public void setSdDirtyTime(Date sdDirtyTime) {
        this.sdDirtyTime = sdDirtyTime;
    }

    public String getSdCode() {
        return sdCode;
    }

    public void setSdCode(String sdCode) {
        this.sdCode = sdCode == null ? null : sdCode.trim();
    }

    public BigDecimal getSdPlanAmount() {
        return sdPlanAmount;
    }

    public void setSdPlanAmount(BigDecimal sdPlanAmount) {
        this.sdPlanAmount = sdPlanAmount;
    }

    public Date getSdStartTime() {
        return sdStartTime;
    }

    public void setSdStartTime(Date sdStartTime) {
        this.sdStartTime = sdStartTime;
    }

    public Date getSdEndTime() {
        return sdEndTime;
    }

    public void setSdEndTime(Date sdEndTime) {
        this.sdEndTime = sdEndTime;
    }

    public Integer getSdTotalPieces() {
        return sdTotalPieces;
    }

    public void setSdTotalPieces(Integer sdTotalPieces) {
        this.sdTotalPieces = sdTotalPieces;
    }

    public BigDecimal getSdTotalWeight() {
        return sdTotalWeight;
    }

    public void setSdTotalWeight(BigDecimal sdTotalWeight) {
        this.sdTotalWeight = sdTotalWeight;
    }

    public String getSdRemark() {
        return sdRemark;
    }

    public void setSdRemark(String sdRemark) {
        this.sdRemark = sdRemark == null ? null : sdRemark.trim();
    }

    public String getSdType() {
        return sdType;
    }

    public void setSdType(String sdType) {
        this.sdType = sdType == null ? null : sdType.trim();
    }

    public String getEstimateStatus() {
        return estimateStatus;
    }

    public void setEstimateStatus(String estimateStatus) {
        this.estimateStatus = estimateStatus == null ? null : estimateStatus.trim();
    }
}