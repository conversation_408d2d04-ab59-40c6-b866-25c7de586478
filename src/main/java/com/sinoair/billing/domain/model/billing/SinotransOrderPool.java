package com.sinoair.billing.domain.model.billing;

import java.math.BigDecimal;
import java.util.Date;

public class SinotransOrderPool {
    private Long sopSyscode;

    private String eawbPrintcode;

    private String sinotransId;

    private String orderId;

    private String sourceId;

    private String originalOrg;

    private String statusTemplate;

    private String originalCustId;

    private String standardCustName;

    private String senderCode;

    private BigDecimal businessVolume;

    private String bizType;

    private String importExportMarks;

    private String blNo;

    private String carrierCode;

    private String attribute5;

    private String attribute7;

    private Date createTime;

    private String extend1;

    private String extend2;

    private String extend3;

    private String extend4;

    private String extend5;

    private String extend6;

    private String extend7;

    private String extend8;

    private Integer bmPiece;

    private BigDecimal bmChargeableweight;

    private String taNcCodeDeparture;

    private String taNcCodeDestination;

    private String epKey;

    //临时字段
    private Date statusTime;

    private String sacId;

    public Long getSopSyscode() {
        return sopSyscode;
    }

    public void setSopSyscode(Long sopSyscode) {
        this.sopSyscode = sopSyscode;
    }

    public String getEawbPrintcode() {
        return eawbPrintcode;
    }

    public void setEawbPrintcode(String eawbPrintcode) {
        this.eawbPrintcode = eawbPrintcode == null ? null : eawbPrintcode.trim();
    }

    public String getSinotransId() {
        return sinotransId;
    }

    public void setSinotransId(String sinotransId) {
        this.sinotransId = sinotransId == null ? null : sinotransId.trim();
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId == null ? null : orderId.trim();
    }

    public String getSourceId() {
        return sourceId;
    }

    public void setSourceId(String sourceId) {
        this.sourceId = sourceId == null ? null : sourceId.trim();
    }

    public String getOriginalOrg() {
        return originalOrg;
    }

    public void setOriginalOrg(String originalOrg) {
        this.originalOrg = originalOrg == null ? null : originalOrg.trim();
    }

    public String getStatusTemplate() {
        return statusTemplate;
    }

    public void setStatusTemplate(String statusTemplate) {
        this.statusTemplate = statusTemplate == null ? null : statusTemplate.trim();
    }

    public String getOriginalCustId() {
        return originalCustId;
    }

    public void setOriginalCustId(String originalCustId) {
        this.originalCustId = originalCustId == null ? null : originalCustId.trim();
    }

    public String getStandardCustName() {
        return standardCustName;
    }

    public void setStandardCustName(String standardCustName) {
        this.standardCustName = standardCustName == null ? null : standardCustName.trim();
    }

    public String getSenderCode() {
        return senderCode;
    }

    public void setSenderCode(String senderCode) {
        this.senderCode = senderCode == null ? null : senderCode.trim();
    }

    public BigDecimal getBusinessVolume() {
        return businessVolume;
    }

    public void setBusinessVolume(BigDecimal businessVolume) {
        this.businessVolume = businessVolume;
    }

    public String getBizType() {
        return bizType;
    }

    public void setBizType(String bizType) {
        this.bizType = bizType == null ? null : bizType.trim();
    }

    public String getImportExportMarks() {
        return importExportMarks;
    }

    public void setImportExportMarks(String importExportMarks) {
        this.importExportMarks = importExportMarks == null ? null : importExportMarks.trim();
    }

    public String getBlNo() {
        return blNo;
    }

    public void setBlNo(String blNo) {
        this.blNo = blNo == null ? null : blNo.trim();
    }

    public String getCarrierCode() {
        return carrierCode;
    }

    public void setCarrierCode(String carrierCode) {
        this.carrierCode = carrierCode == null ? null : carrierCode.trim();
    }

    public String getAttribute5() {
        return attribute5;
    }

    public void setAttribute5(String attribute5) {
        this.attribute5 = attribute5 == null ? null : attribute5.trim();
    }

    public String getAttribute7() {
        return attribute7;
    }

    public void setAttribute7(String attribute7) {
        this.attribute7 = attribute7 == null ? null : attribute7.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getExtend1() {
        return extend1;
    }

    public void setExtend1(String extend1) {
        this.extend1 = extend1 == null ? null : extend1.trim();
    }

    public String getExtend2() {
        return extend2;
    }

    public void setExtend2(String extend2) {
        this.extend2 = extend2 == null ? null : extend2.trim();
    }

    public String getExtend3() {
        return extend3;
    }

    public void setExtend3(String extend3) {
        this.extend3 = extend3 == null ? null : extend3.trim();
    }

    public String getExtend4() {
        return extend4;
    }

    public void setExtend4(String extend4) {
        this.extend4 = extend4 == null ? null : extend4.trim();
    }

    public String getExtend5() {
        return extend5;
    }

    public void setExtend5(String extend5) {
        this.extend5 = extend5 == null ? null : extend5.trim();
    }

    public String getExtend6() {
        return extend6;
    }

    public void setExtend6(String extend6) {
        this.extend6 = extend6 == null ? null : extend6.trim();
    }

    public String getExtend7() {
        return extend7;
    }

    public void setExtend7(String extend7) {
        this.extend7 = extend7 == null ? null : extend7.trim();
    }

    public String getExtend8() {
        return extend8;
    }

    public void setExtend8(String extend8) {
        this.extend8 = extend8 == null ? null : extend8.trim();
    }

    public Integer getBmPiece() {
        return bmPiece;
    }

    public void setBmPiece(Integer bmPiece) {
        this.bmPiece = bmPiece;
    }

    public BigDecimal getBmChargeableweight() {
        return bmChargeableweight;
    }

    public void setBmChargeableweight(BigDecimal bmChargeableweight) {
        this.bmChargeableweight = bmChargeableweight;
    }

    public String getTaNcCodeDeparture() {
        return taNcCodeDeparture;
    }

    public void setTaNcCodeDeparture(String taNcCodeDeparture) {
        this.taNcCodeDeparture = taNcCodeDeparture == null ? null : taNcCodeDeparture.trim();
    }

    public String getTaNcCodeDestination() {
        return taNcCodeDestination;
    }

    public void setTaNcCodeDestination(String taNcCodeDestination) {
        this.taNcCodeDestination = taNcCodeDestination == null ? null : taNcCodeDestination.trim();
    }

    public Date getStatusTime() {
        return statusTime;
    }

    public void setStatusTime(Date statusTime) {
        this.statusTime = statusTime;
    }

    public String getSacId() {
        return sacId;
    }

    public void setSacId(String sacId) {
        this.sacId = sacId;
    }

    public String getEpKey() {
        return epKey;
    }

    public void setEpKey(String epKey) {
        this.epKey = epKey;
    }
}