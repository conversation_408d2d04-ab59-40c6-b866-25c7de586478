package com.sinoair.billing.domain.model.billing;

import java.util.Date;

public class SinotransOrderPoolStatus {
    private Long sopsSyscode;

    private String eawbPrintcode;

    private String statusCode;

    private String statusDesc;

    private Date statusTime;

    private Date createTime;

    public Long getSopsSyscode() {
        return sopsSyscode;
    }

    public void setSopsSyscode(Long sopsSyscode) {
        this.sopsSyscode = sopsSyscode;
    }

    public String getEawbPrintcode() {
        return eawbPrintcode;
    }

    public void setEawbPrintcode(String eawbPrintcode) {
        this.eawbPrintcode = eawbPrintcode == null ? null : eawbPrintcode.trim();
    }

    public String getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(String statusCode) {
        this.statusCode = statusCode == null ? null : statusCode.trim();
    }

    public String getStatusDesc() {
        return statusDesc;
    }

    public void setStatusDesc(String statusDesc) {
        this.statusDesc = statusDesc == null ? null : statusDesc.trim();
    }

    public Date getStatusTime() {
        return statusTime;
    }

    public void setStatusTime(Date statusTime) {
        this.statusTime = statusTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

}