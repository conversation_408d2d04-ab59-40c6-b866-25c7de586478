package com.sinoair.billing.domain.model.billing;

import java.math.BigDecimal;
import java.util.Date;

public class Supplier {
    private String spSyscode;

    private String companyId;

    private String spCode;

    private String spName;

    private String spEname;

    private String spAddress;

    private String spEaddress;

    private String spPostcode;

    private BigDecimal spUserId;

    private Date spHandletime;

    private String spStatus;

    private String spTelephone;

    private String spFax;

    private String spContractinfo;

    private String spType;

    private String spCompanyId;

    private String vendorCode;

    private BigDecimal taxRate;

    private String bmsStatus;

    private String ctCode;

    public String getSpSyscode() {
        return spSyscode;
    }

    public void setSpSyscode(String spSyscode) {
        this.spSyscode = spSyscode == null ? null : spSyscode.trim();
    }

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId == null ? null : companyId.trim();
    }

    public String getSpCode() {
        return spCode;
    }

    public void setSpCode(String spCode) {
        this.spCode = spCode == null ? null : spCode.trim();
    }

    public String getSpName() {
        return spName;
    }

    public void setSpName(String spName) {
        this.spName = spName == null ? null : spName.trim();
    }

    public String getSpEname() {
        return spEname;
    }

    public void setSpEname(String spEname) {
        this.spEname = spEname == null ? null : spEname.trim();
    }

    public String getSpAddress() {
        return spAddress;
    }

    public void setSpAddress(String spAddress) {
        this.spAddress = spAddress == null ? null : spAddress.trim();
    }

    public String getSpEaddress() {
        return spEaddress;
    }

    public void setSpEaddress(String spEaddress) {
        this.spEaddress = spEaddress == null ? null : spEaddress.trim();
    }

    public String getSpPostcode() {
        return spPostcode;
    }

    public void setSpPostcode(String spPostcode) {
        this.spPostcode = spPostcode == null ? null : spPostcode.trim();
    }

    public BigDecimal getSpUserId() {
        return spUserId;
    }

    public void setSpUserId(BigDecimal spUserId) {
        this.spUserId = spUserId;
    }

    public Date getSpHandletime() {
        return spHandletime;
    }

    public void setSpHandletime(Date spHandletime) {
        this.spHandletime = spHandletime;
    }

    public String getSpStatus() {
        return spStatus;
    }

    public void setSpStatus(String spStatus) {
        this.spStatus = spStatus == null ? null : spStatus.trim();
    }

    public String getSpTelephone() {
        return spTelephone;
    }

    public void setSpTelephone(String spTelephone) {
        this.spTelephone = spTelephone == null ? null : spTelephone.trim();
    }

    public String getSpFax() {
        return spFax;
    }

    public void setSpFax(String spFax) {
        this.spFax = spFax == null ? null : spFax.trim();
    }

    public String getSpContractinfo() {
        return spContractinfo;
    }

    public void setSpContractinfo(String spContractinfo) {
        this.spContractinfo = spContractinfo == null ? null : spContractinfo.trim();
    }

    public String getSpType() {
        return spType;
    }

    public void setSpType(String spType) {
        this.spType = spType == null ? null : spType.trim();
    }

    public String getSpCompanyId() {
        return spCompanyId;
    }

    public void setSpCompanyId(String spCompanyId) {
        this.spCompanyId = spCompanyId == null ? null : spCompanyId.trim();
    }

    public String getVendorCode() {
        return vendorCode;
    }

    public void setVendorCode(String vendorCode) {
        this.vendorCode = vendorCode == null ? null : vendorCode.trim();
    }

    public BigDecimal getTaxRate() {
        return taxRate;
    }

    public void setTaxRate(BigDecimal taxRate) {
        this.taxRate = taxRate;
    }

    public String getBmsStatus() {
        return bmsStatus;
    }

    public void setBmsStatus(String bmsStatus) {
        this.bmsStatus = bmsStatus;
    }

    public String getCtCode() {
        return ctCode;
    }

    public void setCtCode(String ctCode) {
        this.ctCode = ctCode;
    }
}