package com.sinoair.billing.domain.model.ceosbi;

import java.math.BigDecimal;
import java.util.Date;

public class EawbEadFlat {
    private BigDecimal eawbSyscode;

    private String eawbPrintcode;

    private Date fcInbound;

    private Date fcOutbound;

    private Date ass;

    private Date unass;

    private Date adc;

    private Date cpt;

    private Date roe;

    private Date delivery;

    private Date undelivery;
    private Date declare;
    private Date clrdt;

    private Date eefUpdatetime;

    private String eawbServicetype;

    private String eawbSoCode;

    private String eawbDestcountry;

    private String eawbPostcode;

    private String postcodeFirst;

    private String status;

    private Integer statusValueInt;

    private BigDecimal declareWeight;

    private Date atd;

    public BigDecimal getEawbSyscode() {
        return eawbSyscode;
    }

    public void setEawbSyscode(BigDecimal eawbSyscode) {
        this.eawbSyscode = eawbSyscode;
    }

    public String getEawbPrintcode() {
        return eawbPrintcode;
    }

    public void setEawbPrintcode(String eawbPrintcode) {
        this.eawbPrintcode = eawbPrintcode == null ? null : eawbPrintcode.trim();
    }

    public Date getFcInbound() {
        return fcInbound;
    }

    public void setFcInbound(Date fcInbound) {
        this.fcInbound = fcInbound;
    }

    public Date getFcOutbound() {
        return fcOutbound;
    }

    public void setFcOutbound(Date fcOutbound) {
        this.fcOutbound = fcOutbound;
    }

    public Date getAss() {
        return ass;
    }

    public void setAss(Date ass) {
        this.ass = ass;
    }

    public Date getUnass() {
        return unass;
    }

    public void setUnass(Date unass) {
        this.unass = unass;
    }

    public Date getAdc() {
        return adc;
    }

    public void setAdc(Date adc) {
        this.adc = adc;
    }

    public Date getCpt() {
        return cpt;
    }

    public void setCpt(Date cpt) {
        this.cpt = cpt;
    }

    public Date getRoe() {
        return roe;
    }

    public void setRoe(Date roe) {
        this.roe = roe;
    }

    public Date getDelivery() {
        return delivery;
    }

    public void setDelivery(Date delivery) {
        this.delivery = delivery;
    }

    public Date getEefUpdatetime() {
        return eefUpdatetime;
    }

    public void setEefUpdatetime(Date eefUpdatetime) {
        this.eefUpdatetime = eefUpdatetime;
    }

    public String getEawbServicetype() {
        return eawbServicetype;
    }

    public void setEawbServicetype(String eawbServicetype) {
        this.eawbServicetype = eawbServicetype;
    }

    public String getEawbSoCode() {
        return eawbSoCode;
    }

    public void setEawbSoCode(String eawbSoCode) {
        this.eawbSoCode = eawbSoCode;
    }

    public String getEawbDestcountry() {
        return eawbDestcountry;
    }

    public void setEawbDestcountry(String eawbDestcountry) {
        this.eawbDestcountry = eawbDestcountry;
    }

    public String getEawbPostcode() {
        return eawbPostcode;
    }

    public void setEawbPostcode(String eawbPostcode) {
        this.eawbPostcode = eawbPostcode;
    }

    public String getPostcodeFirst() {
        return postcodeFirst;
    }

    public void setPostcodeFirst(String postcodeFirst) {
        this.postcodeFirst = postcodeFirst;
    }

    public Date getUndelivery() {
        return undelivery;
    }

    public void setUndelivery(Date undelivery) {
        this.undelivery = undelivery;
    }

    public Date getDeclare() {
        return declare;
    }

    public void setDeclare(Date declare) {
        this.declare = declare;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getStatusValueInt() {
        return statusValueInt;
    }

    public void setStatusValueInt(Integer statusValueInt) {
        this.statusValueInt = statusValueInt;
    }

    public BigDecimal getDeclareWeight() {
        return declareWeight;
    }

    public void setDeclareWeight(BigDecimal declareWeight) {
        this.declareWeight = declareWeight;
    }

    public Date getClrdt() {
        return clrdt;
    }

    public void setClrdt(Date clrdt) {
        this.clrdt = clrdt;
    }

    public Date getAtd() {
        return atd;
    }

    public void setAtd(Date atd) {
        this.atd = atd;
    }
}