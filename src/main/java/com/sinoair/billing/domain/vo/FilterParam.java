package com.sinoair.billing.domain.vo;

import java.util.HashMap;
import java.util.Map;

/**
 * 此Model 是仅供存储过滤条件的数据
 * Created by <PERSON><PERSON>xiongwei on 2016/2/15.
 */
public class FilterParam {

    //如果一个输入框对应多个字段
    private String search;
    private Object object;
    private int parent_menu_id;
    private String menu_name;
    private String url;
    private int id;
    private String code;
    private int code1;
    private String name;
    private String username;
    private int sex;
    private String status;
    private String sort;
    private String order = "ASC";
    private String pid;
    private String pid2;
    private String companyId;
    private int pipeiStatus;
    private String starttime;
    private String endtime;
    private String[] mawbs;
    private String[] servers;
    private String serviceName;
    private int pdSyscode;


    private String type;
    //taoastr显示
    private String showToastr;
    private String toastrTitle;
    private String toastrContent;
    private String toastrType;
    private String transmodeId;
    private String serviceType;


    //空港查询
    private String soCode;//结算对象代码
    private String ncCode;//洲、国家或省代码
    private String apCode;//港口三字码
    private String apName;//港口中文名称
    private String apEname;//港口英文名称
    //配舱查询 zmj 2016-07-16 开始
    private String starttime1;//配舱日期开始
    private String endtime1;//配舱日期结束
    private String sacId;
    private String eastCode;
    //1.总单号用 code
    //2.目的地港 apCode
    //3.航班号 ncCode
    //4.航班日期：starttime  endtime
    //配舱查询 zmj 2016-07-16 结束
    // 菜鸟货物销毁查询 dengqing 2016.07.18
    private String eawbReference1;

    //异常处理查询 zmj 2016-07-19开始
      //1.code 用于外运单号或者追踪号
      //2.status 异常状态
      //3.ncCode 异常代码
      //4.starttime endtime 异常日期
      //5.starttime1 endtime1 录单日期
    //异常处理查询 zmj 2016-07-19结束

    //客户类别 用于不同角色查询不同结算对象的账单
    private String soCate;

    //分成状态 Y：已分成
    private String dmDivideStatus;
    //账单类型 应收/应付 内部账单费用使用
    private String sdType;


    private String pullstarttime;
    private String pullendtime;


    public Map toMap() {
        Map map = new HashMap();

        map.put("search", search);

        map.put("parent_menu_id", parent_menu_id);
        map.put("menu_name", menu_name);
        map.put("url", url);
        map.put("id", id);
        map.put("code", code);
        map.put("name", name);
        map.put("username", username);
        map.put("sex", sex);
        map.put("status", status);
        map.put("sort", sort);
        map.put("order", order);
        map.put("starttime", starttime);
        map.put("endtime", endtime);
        map.put("pipeiStatus", pipeiStatus);
        map.put("pid2", pid2);
        map.put("companyId", companyId);
        map.put("order", order);
        map.put("sort", sort);
        map.put("code1", code1);

        //空港查询
        map.put("ncCode", ncCode);//港口三字码
        map.put("apCode", apCode);//港口三字码
        map.put("apName", apName);//港口中文名称
        map.put("apEname", apEname);//港口英文名称

        //配舱查询
        map.put("starttime1", starttime1);//配舱日期开始
        map.put("endtime1", endtime1);//配舱日期结束
        map.put("sacId", sacId);//分公司三字码

        // 菜鸟货物销毁查询
        map.put("eawbReference1", eawbReference1);//运单号

        map.put("mawbs", mawbs);//总单号数组
        map.put("servers", servers);//服务名数组
        map.put("serviceName", serviceName);
        map.put("transmodeId", transmodeId);
        map.put("serviceType", serviceType);
        map.put("soCode", soCode);
        map.put("eastCode", eastCode);
        map.put("pdSyscode", pdSyscode);
        map.put("type", type);

        map.put("soCate", soCate);


        return map;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getEndtime() {
        return endtime;
    }

    public void setEndtime(String endtime) {
        this.endtime = endtime;
    }

    public String getStarttime() {
        return starttime;
    }

    public void setStarttime(String starttime) {
        this.starttime = starttime;
    }


    public int getPipeiStatus() {
        return pipeiStatus;
    }

    public void setPipeiStatus(int pipeiStatus) {
        this.pipeiStatus = pipeiStatus;
    }

    public int getCode1() {
        return code1;
    }

    public void setCode1(int code1) {
        this.code1 = code1;
    }

    public int getParent_menu_id() {
        return parent_menu_id;
    }

    public void setParent_menu_id(int parent_menu_id) {
        this.parent_menu_id = parent_menu_id;
    }

    public String getMenu_name() {
        return menu_name;
    }

    public void setMenu_name(String menu_name) {
        this.menu_name = menu_name;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public int getSex() {
        return sex;
    }

    public void setSex(int sex) {
        this.sex = sex;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getSort() {
        return sort;
    }

    public void setSort(String sort) {
        this.sort = sort;
    }

    public String getOrder() {
        return order;
    }

    public void setOrder(String order) {
        this.order = order;
    }

    public String getSearch() {
        return search;
    }

    public void setSearch(String search) {
        this.search = search;
    }

    public String getShowToastr() {
        return showToastr;
    }

    public void setShowToastr(String showToastr) {
        this.showToastr = showToastr;
    }

    public String getToastrTitle() {
        return toastrTitle;
    }

    public void setToastrTitle(String toastrTitle) {
        this.toastrTitle = toastrTitle;
    }

    public String getToastrContent() {
        return toastrContent;
    }

    public void setToastrContent(String toastrContent) {
        this.toastrContent = toastrContent;
    }

    public String getToastrType() {
        return toastrType;
    }

    public void setToastrType(String toastrType) {
        this.toastrType = toastrType;
    }

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public String getPid2() {
        return pid2;
    }

    public void setPid2(String pid2) {
        this.pid2 = pid2;
    }

    public Object getObject() {
        return object;
    }

    public void setObject(Object object) {
        this.object = object;
    }

    public String getApCode() {
        return apCode;
    }

    public void setApCode(String apCode) {
        this.apCode = apCode;
    }

    public String getApName() {
        return apName;
    }

    public void setApName(String apName) {
        this.apName = apName;
    }

    public String getApEname() {
        return apEname;
    }

    public void setApEname(String apEname) {
        this.apEname = apEname;
    }

    public String getNcCode() {
        return ncCode;
    }

    public void setNcCode(String ncCode) {
        this.ncCode = ncCode;
    }

    public String getStarttime1() {
        return starttime1;
    }

    public void setStarttime1(String starttime1) {
        this.starttime1 = starttime1;
    }

    public String getEndtime1() {
        return endtime1;
    }

    public void setEndtime1(String endtime1) {
        this.endtime1 = endtime1;
    }

    public String getSacId() {
        return sacId;
    }

    public void setSacId(String sacId) {
        this.sacId = sacId;
    }

    public String getEawbReference1() {
        return eawbReference1;
    }

    public void setEawbReference1(String eawbReference1) {
        this.eawbReference1 = eawbReference1;
    }

    public String[] getMawbs() {
        return mawbs;
    }

    public void setMawbs(String[] mawbs) {
        this.mawbs = mawbs;
    }

    public String[] getServers() {
        return servers;
    }

    public void setServers(String[] servers) {
        this.servers = servers;
    }

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public String getTransmodeId() {
        return transmodeId;
    }

    public void setTransmodeId(String transmodeId) {
        this.transmodeId = transmodeId;
    }

    public String getServiceType() {
        return serviceType;
    }

    public void setServiceType(String serviceType) {
        this.serviceType = serviceType;
    }

    public String getSoCode() {
        return soCode;
    }

    public void setSoCode(String soCode) {
        this.soCode = soCode;
    }

    public String getEastCode() {
        return eastCode;
    }

    public void setEastCode(String eastCode) {
        this.eastCode = eastCode;
    }

    public int getPdSyscode() {
        return pdSyscode;
    }

    public void setPdSyscode(int pdSyscode) {
        this.pdSyscode = pdSyscode;
    }

    public String getSoCate() {
        return soCate;
    }

    public void setSoCate(String soCate) {
        this.soCate = soCate;
    }

    public String getDmDivideStatus() {
        return dmDivideStatus;
    }

    public void setDmDivideStatus(String dmDivideStatus) {
        this.dmDivideStatus = dmDivideStatus;
    }

    public String getSdType() {
        return sdType;
    }

    public void setSdType(String sdType) {
        this.sdType = sdType;
    }

    public String getPullstarttime() {
        return pullstarttime;
    }

    public void setPullstarttime(String pullstarttime) {
        this.pullstarttime = pullstarttime;
    }

    public String getPullendtime() {
        return pullendtime;
    }

    public void setPullendtime(String pullendtime) {
        this.pullendtime = pullendtime;
    }
}


