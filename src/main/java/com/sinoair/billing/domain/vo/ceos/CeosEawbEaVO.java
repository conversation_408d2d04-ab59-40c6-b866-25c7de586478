package com.sinoair.billing.domain.vo.ceos;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date:
 * @time: 16:44
 * @description: ceos系统主单部分信息
 * To change this template use File | Settings | File Templates.
 */
public class CeosEawbEaVO {

    private String eawbPrintcode;

    private String eawbSoCode;

    private String mawbCode;
    private String transportType;
    private String eaFlightType;
    private String eawbSfCode;
    private String destSacId;
    private Date eawbHandletime;

    public String getEawbSoCode() {
        return eawbSoCode;
    }

    public void setEawbSoCode(String eawbSoCode) {
        this.eawbSoCode = eawbSoCode;
    }

    public String getEawbPrintcode() {
        return eawbPrintcode;
    }

    public void setEawbPrintcode(String eawbPrintcode) {
        this.eawbPrintcode = eawbPrintcode;
    }

    public String getMawbCode() {
        return mawbCode;
    }

    public void setMawbCode(String mawbCode) {
        this.mawbCode = mawbCode;
    }

    public String getTransportType() {
        return transportType;
    }

    public void setTransportType(String transportType) {
        this.transportType = transportType;
    }

    public String getEaFlightType() {
        return eaFlightType;
    }

    public void setEaFlightType(String eaFlightType) {
        this.eaFlightType = eaFlightType;
    }

    public String getEawbSfCode() {
        return eawbSfCode;
    }

    public void setEawbSfCode(String eawbSfCode) {
        this.eawbSfCode = eawbSfCode;
    }

    public String getDestSacId() {
        return destSacId;
    }

    public void setDestSacId(String destSacId) {
        this.destSacId = destSacId;
    }

    public Date getEawbHandletime() {
        return eawbHandletime;
    }

    public void setEawbHandletime(Date eawbHandletime) {
        this.eawbHandletime = eawbHandletime;
    }
}
