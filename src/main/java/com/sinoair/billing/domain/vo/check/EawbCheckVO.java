package com.sinoair.billing.domain.vo.check;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date:
 * @time: 16:44
 * @description: ceos系统eawb部分字段
 * To change this template use File | Settings | File Templates.
 */
public class EawbCheckVO {

    private BigDecimal eawbSyscode;

    private String eawbPrintcode;

    private String codeStatus;

    private String activityStatus;

    private String rrStatus;

    private String resultStatus;

    private Date eawbKeyentrytime;

    private String activityStatusCeos;

    private int pageNum;

    private int pageSize;

    private int eawbSoType;

    private String startDate;

    private String endDate;

    private Long beginNo;

    private Long endNo;

    private String checkPr;

    private String checkHandleTime;

    private Long allCount;

    private String eadCode;

    private String eastCode;

    private String prName;

    private Integer prId;

    private Long checkDate;

    private Date rrOccurtime;

    private String tableParam;

    private String eawbSoCode;

    public BigDecimal getEawbSyscode() {
        return eawbSyscode;
    }

    public void setEawbSyscode(BigDecimal eawbSyscode) {
        this.eawbSyscode = eawbSyscode;
    }

    public String getEawbPrintcode() {
        return eawbPrintcode;
    }

    public void setEawbPrintcode(String eawbPrintcode) {
        this.eawbPrintcode = eawbPrintcode;
    }

    public String getCodeStatus() {
        return codeStatus;
    }

    public void setCodeStatus(String codeStatus) {
        this.codeStatus = codeStatus;
    }

    public String getActivityStatus() {
        return activityStatus;
    }

    public void setActivityStatus(String activityStatus) {
        this.activityStatus = activityStatus;
    }

    public String getRrStatus() {
        return rrStatus;
    }

    public void setRrStatus(String rrStatus) {
        this.rrStatus = rrStatus;
    }

    public String getResultStatus() {
        return resultStatus;
    }

    public void setResultStatus(String resultStatus) {
        this.resultStatus = resultStatus;
    }

    public Date getEawbKeyentrytime() {
        return eawbKeyentrytime;
    }

    public void setEawbKeyentrytime(Date eawbKeyentrytime) {
        this.eawbKeyentrytime = eawbKeyentrytime;
    }

    public String getActivityStatusCeos() {
        return activityStatusCeos;
    }

    public void setActivityStatusCeos(String activityStatusCeos) {
        this.activityStatusCeos = activityStatusCeos;
    }

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getEawbSoType() {
        return eawbSoType;
    }

    public void setEawbSoType(int eawbSoType) {
        this.eawbSoType = eawbSoType;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public Long getBeginNo() {
        return beginNo;
    }

    public void setBeginNo(Long beginNo) {
        this.beginNo = beginNo;
    }

    public Long getEndNo() {
        return endNo;
    }

    public void setEndNo(Long endNo) {
        this.endNo = endNo;
    }

    public String getCheckPr() {
        return checkPr;
    }

    public void setCheckPr(String checkPr) {
        this.checkPr = checkPr;
    }

    public String getCheckHandleTime() {
        return checkHandleTime;
    }

    public void setCheckHandleTime(String checkHandleTime) {
        this.checkHandleTime = checkHandleTime;
    }

    public Long getAllCount() {
        return allCount;
    }

    public void setAllCount(Long allCount) {
        this.allCount = allCount;
    }

    public String getEadCode() {
        return eadCode;
    }

    public void setEadCode(String eadCode) {
        this.eadCode = eadCode;
    }

    public String getEastCode() {
        return eastCode;
    }

    public void setEastCode(String eastCode) {
        this.eastCode = eastCode;
    }

    public String getPrName() {
        return prName;
    }

    public void setPrName(String prName) {
        this.prName = prName;
    }

    public Integer getPrId() {
        return prId;
    }

    public void setPrId(Integer prId) {
        this.prId = prId;
    }

    public Long getCheckDate() {
        return checkDate;
    }

    public void setCheckDate(Long checkDate) {
        this.checkDate = checkDate;
    }

    public Date getRrOccurtime() {
        return rrOccurtime;
    }

    public void setRrOccurtime(Date rrOccurtime) {
        this.rrOccurtime = rrOccurtime;
    }

    public String getTableParam() {
        return tableParam;
    }

    public void setTableParam(String tableParam) {
        this.tableParam = tableParam;
    }

    public String getEawbSoCode() {
        return eawbSoCode;
    }

    public void setEawbSoCode(String eawbSoCode) {
        this.eawbSoCode = eawbSoCode;
    }

    public String toString(){
        return "[eawbSyscode=" + eawbSyscode +
                ", eawbPrintcode=" + eawbPrintcode+
                ", codeStatus=" + codeStatus+
                ", activityStatus=" + activityStatus+
                ", rrStatus=" + rrStatus+
                ", resultStatus=" + resultStatus+
                "]";
    }
}
