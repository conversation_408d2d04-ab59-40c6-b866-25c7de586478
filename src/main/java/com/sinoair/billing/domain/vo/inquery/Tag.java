package com.sinoair.billing.domain.vo.inquery;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2016/5/17 16:47
 * @Description 总标签父类
 **/
public class Tag {

    private String tag_type;
    private String label_zh;
    private String label_en;
    private String placeholder_zh;
    private String placeholder_en;
    private List<Multiple> multiples;

    public String getTag_type() {
        return tag_type;
    }

    public void setTag_type(String tag_type) {
        this.tag_type = tag_type;
    }

    public String getLabel_zh() {
        return label_zh;
    }

    public void setLabel_zh(String label_zh) {
        this.label_zh = label_zh;
    }

    public String getLabel_en() {
        return label_en;
    }

    public void setLabel_en(String label_en) {
        this.label_en = label_en;
    }

    public String getPlaceholder_zh() {
        return placeholder_zh;
    }

    public void setPlaceholder_zh(String placeholder_zh) {
        this.placeholder_zh = placeholder_zh;
    }

    public String getPlaceholder_en() {
        return placeholder_en;
    }

    public void setPlaceholder_en(String placeholder_en) {
        this.placeholder_en = placeholder_en;
    }

    public List<Multiple> getMultiples() {
        return multiples;
    }

    public void setMultiples(List<Multiple> multiples) {
        this.multiples = multiples;
    }
}
