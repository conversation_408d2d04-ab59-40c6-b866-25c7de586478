package com.sinoair.billing.domain.vo.manifest;

import com.sinoair.billing.domain.model.billing.CreditManifest;
import com.sinoair.billing.domain.model.billing.DebitManifest;
import com.sinoair.billing.domain.model.billing.ManifestList;

import java.util.List;

/**
 * Created by .
 */
public class ManifestListVO extends ManifestList{

    private String ctName;

    //private String soName;

    private int manifestCount;

    private List<DebitManifest> debitManifestList;

    private List<CreditManifest> creditManifestList;

    public String getCtName() {
        return ctName;
    }

    public void setCtName(String ctName) {
        this.ctName = ctName;
    }

//    @Override
//    public String getSoName() {
//        return soName;
//    }
//
//    @Override
//    public void setSoName(String soName) {
//        this.soName = soName;
//    }


    public int getManifestCount() {
        return manifestCount;
    }

    public void setManifestCount(int manifestCount) {
        this.manifestCount = manifestCount;
    }

    public List<DebitManifest> getDebitManifestList() {
        return debitManifestList;
    }

    public void setDebitManifestList(List<DebitManifest> debitManifestList) {
        this.debitManifestList = debitManifestList;
    }

    public List<CreditManifest> getCreditManifestList() {
        return creditManifestList;
    }

    public void setCreditManifestList(List<CreditManifest> creditManifestList) {
        this.creditManifestList = creditManifestList;
    }
}
