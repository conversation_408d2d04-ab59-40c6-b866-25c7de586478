package com.sinoair.billing.domain.vo.mobile;

/**
 * Created by IntelliJ IDEA.
 * User: <PERSON>
 * Date: 2016/8/10
 * Time: 9:07
 * Description: 常量码
 * To change this template use File | Settings | File Templates.
 */
public class MobileConstant {

    /**
     * 请求成功
     */
    public static String MSG_CODE_SUCCESS = "0000";
    public static String MSG_MESSAGE_SUCCESS = "SUCCESS";

    /**
     * 请求失败
     */
    public static String MSG_CODE_FAIL = "0001";
    public static String MSG_MESSAGE_FAIL = "FAIL";

    /**
     * 请求内容报文不合法
     */
    public static String MSG_CODE_ERROR_REQ = "0002";
    public static String MSG_MESSAGE_ERROR_REQ = "ERROR REQUEST";


    /**
     * token错误
     */
    public static String MSG_CODE_ERROR_TOKEN = "0003";
    public static String MSG_MESSAGE_ERROR_TOKEN = "ERROR TOKEN";

    /**
     * 验签失败
     */
    public static String MSG_CODE_ERROR_SIGN = "0004";
    public static String MSG_MESSAGE_ERROR_SIGN = "ERROR SIGNATURE";


    /**
     * req_data内容不合法
     */
    public static String MSG_CODE_ERROR_REQ_DATA = "0005";
    public static String MSG_MESSAGE_ERROR_REQ_DATA = "ERROR REQUEST DATA";

    /**
     * 服务器异常
     */
    public static String MSG_CODE_SERVER_EXCEPTION = "9999";
    public static String MSG_MESSAGE_SERVER_EXCEPTION = "SERVER EXCEPTION";

}
