package com.sinoair.billing.domain.vo.mobile;


import com.sinoair.billing.domain.model.billing.User;

/**
 * Created by IntelliJ IDEA.
 * User: <PERSON>
 * Date: 2016/10/18
 * Time: 16:03
 * Description: 移动端验证
 * To change this template use File | Settings | File Templates.
 */
public class MobileValidate {

    /**
     * 是否通过
     */
    private boolean validate;

    /**
     * 请求对象
     */
    private MobileRequestBody mobileRequestBody;

    /**
     * 响应对象
     */
    private MobileResponseBody mobileResponseBody;

    /**
     * 操作用户
     */
    private User user;

    public boolean isValidate() {
        return validate;
    }

    public void setValidate(boolean validate) {
        this.validate = validate;
    }

    public MobileRequestBody getMobileRequestBody() {
        return mobileRequestBody;
    }

    public void setMobileRequestBody(MobileRequestBody mobileRequestBody) {
        this.mobileRequestBody = mobileRequestBody;
    }

    public MobileResponseBody getMobileResponseBody() {
        return mobileResponseBody;
    }

    public void setMobileResponseBody(MobileResponseBody mobileResponseBody) {
        this.mobileResponseBody = mobileResponseBody;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }
}
