package com.sinoair.billing.domain.vo.mobile.request;

/**
 * Created by DengQing on 2016/10/19.
 */
public class MobileScanPackageRequest {
    private String sacId;
    private Integer userId;
    private String pkgPrintcode;
    private String epValue;
    private String saveHiddenEpValue;
    private String saveHiddenEpValuCount;
    private String scanCodeHidden;
    private String pkgActualWeight;
    private String totalPieces;
    private String[] Pres;
    private String[] PrePulls;

    public String getSacId() {
        return sacId;
    }

    public void setSacId(String sacId) {
        this.sacId = sacId;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getPkgPrintcode() {
        return pkgPrintcode;
    }

    public void setPkgPrintcode(String pkgPrintcode) {
        this.pkgPrintcode = pkgPrintcode;
    }

    public String getEpValue() {
        return epValue;
    }

    public void setEpValue(String epValue) {
        this.epValue = epValue;
    }

    public String getSaveHiddenEpValue() {
        return saveHiddenEpValue;
    }

    public void setSaveHiddenEpValue(String saveHiddenEpValue) {
        this.saveHiddenEpValue = saveHiddenEpValue;
    }

    public String getSaveHiddenEpValuCount() {
        return saveHiddenEpValuCount;
    }

    public void setSaveHiddenEpValuCount(String saveHiddenEpValuCount) {
        this.saveHiddenEpValuCount = saveHiddenEpValuCount;
    }

    public String getScanCodeHidden() {
        return scanCodeHidden;
    }

    public void setScanCodeHidden(String scanCodeHidden) {
        this.scanCodeHidden = scanCodeHidden;
    }

    public String getPkgActualWeight() {
        return pkgActualWeight;
    }

    public void setPkgActualWeight(String pkgActualWeight) {
        this.pkgActualWeight = pkgActualWeight;
    }

    public String getTotalPieces() {
        return totalPieces;
    }

    public void setTotalPieces(String totalPieces) {
        this.totalPieces = totalPieces;
    }

    public String[] getPres() {
        return Pres;
    }

    public void setPres(String[] pres) {
        Pres = pres;
    }

    public String[] getPrePulls() {
        return PrePulls;
    }

    public void setPrePulls(String[] prePulls) {
        PrePulls = prePulls;
    }
}
