package com.sinoair.billing.domain.vo.price;

import java.math.BigDecimal;
import java.util.Date;

public class GenerateFee {

    private BigDecimal planAmount;
    private String formula;
    private String psdName;
    private String eawbPrintcode;//运单号
    private String pdName;//费用名称
    private Integer serviceId;//服务Id
    private String partitionCode;//分区
    private String eawbSoCode;//客户code
    private String soCode;//供应商编码
    private String cId;//分公司
    private Integer pdSyscode;//费用ID
    private Integer ppId;//报价ID
    private String ctCode;//币种
    private String ppCate;//应付类别
    private Integer psdId;//服务详情Id
    private Date ebaOccurtime;//轨迹发生时间
    private String eawbServicetype;
    private BigDecimal eawbChargeableweight;
    private String eawbOutboundSacId;
    private String eawbTrackingNo;
    private String eawbReference2;
    private String mawbCode;

    public String getMawbCode() {
        return mawbCode;
    }

    public void setMawbCode(String mawbCode) {
        this.mawbCode = mawbCode;
    }

    public String getEawbReference2() {
        return eawbReference2;
    }

    public void setEawbReference2(String eawbReference2) {
        this.eawbReference2 = eawbReference2;
    }

    public BigDecimal getEawbChargeableweight() {
        return eawbChargeableweight;
    }

    public void setEawbChargeableweight(BigDecimal eawbChargeableweight) {
        this.eawbChargeableweight = eawbChargeableweight;
    }

    public String getEawbOutboundSacId() {
        return eawbOutboundSacId;
    }

    public void setEawbOutboundSacId(String eawbOutboundSacId) {
        this.eawbOutboundSacId = eawbOutboundSacId;
    }

    public String getEawbTrackingNo() {
        return eawbTrackingNo;
    }

    public void setEawbTrackingNo(String eawbTrackingNo) {
        this.eawbTrackingNo = eawbTrackingNo;
    }

    public String getEawbServicetype() {
        return eawbServicetype;
    }

    public void setEawbServicetype(String eawbServicetype) {
        this.eawbServicetype = eawbServicetype;
    }

    public Date getEbaOccurtime() {
        return ebaOccurtime;
    }

    public void setEbaOccurtime(Date ebaOccurtime) {
        this.ebaOccurtime = ebaOccurtime;
    }

    public Integer getPsdId() {
        return psdId;
    }

    public void setPsdId(Integer psdId) {
        this.psdId = psdId;
    }

    public String getPpCate() {
        return ppCate;
    }

    public void setPpCate(String ppCate) {
        this.ppCate = ppCate;
    }

    public BigDecimal getPlanAmount() {
        return planAmount;
    }

    public void setPlanAmount(BigDecimal planAmount) {
        this.planAmount = planAmount;
    }

    public String getFormula() {
        return formula;
    }

    public void setFormula(String formula) {
        this.formula = formula;
    }

    public String getPsdName() {
        return psdName;
    }

    public void setPsdName(String psdName) {
        this.psdName = psdName;
    }

    public String getEawbPrintcode() {
        return eawbPrintcode;
    }

    public void setEawbPrintcode(String eawbPrintcode) {
        this.eawbPrintcode = eawbPrintcode;
    }

    public String getPdName() {
        return pdName;
    }

    public void setPdName(String pdName) {
        this.pdName = pdName;
    }

    public Integer getServiceId() {
        return serviceId;
    }

    public void setServiceId(Integer serviceId) {
        this.serviceId = serviceId;
    }

    public String getPartitionCode() {
        return partitionCode;
    }

    public void setPartitionCode(String partitionCode) {
        this.partitionCode = partitionCode;
    }

    public String getEawbSoCode() {
        return eawbSoCode;
    }

    public void setEawbSoCode(String eawbSoCode) {
        this.eawbSoCode = eawbSoCode;
    }

    public String getSoCode() {
        return soCode;
    }

    public void setSoCode(String soCode) {
        this.soCode = soCode;
    }

    public String getcId() {
        return cId;
    }

    public void setcId(String cId) {
        this.cId = cId;
    }

    public Integer getPdSyscode() {
        return pdSyscode;
    }

    public void setPdSyscode(Integer pdSyscode) {
        this.pdSyscode = pdSyscode;
    }

    public Integer getPpId() {
        return ppId;
    }

    public void setPpId(Integer ppId) {
        this.ppId = ppId;
    }

    public String getCtCode() {
        return ctCode;
    }

    public void setCtCode(String ctCode) {
        this.ctCode = ctCode;
    }
}
