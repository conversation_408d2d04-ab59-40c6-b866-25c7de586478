package com.sinoair.billing.domain.vo.price;

import java.math.BigDecimal;
import java.util.Date;

public class GenerateReceiptFee {

    private BigDecimal planAmount;
    private String formula;
    private String partitionCode;
    private String rpName;
    private String ptType;
    private String eawbPrintcode;
    private String eawbSoCode;
    private Date ebaOccurtime;
    private String eawbServicetype;
    private BigDecimal eawbChargeableweight;
    private String eawbOutboundSacId;
    private String eawbTrackingNo;
    private String eawbReference2;
    private String mawbCode;
    private Integer pdSyscode;//费用ID
    private String soCode;//供应商编码
    private Integer rpId;
    private String cId;//分公司
    private String ctCode;//币种
    private String rpdCate;
    private String pdName;
    private String epKey;
    private String soMode;
    private String eawbDestcountry;

    private String eawbDestination;

    private String eawbDepartcountry;

    private String eawbDeparture;

    private String cCode;

    public String getcCode() {
        return cCode;
    }

    public void setcCode(String cCode) {
        this.cCode = cCode;
    }

    public String getSoMode() {
        return soMode;
    }

    public void setSoMode(String soMode) {
        this.soMode = soMode;
    }

    public String getEawbDestcountry() {
        return eawbDestcountry;
    }

    public void setEawbDestcountry(String eawbDestcountry) {
        this.eawbDestcountry = eawbDestcountry;
    }

    public String getEawbDestination() {
        return eawbDestination;
    }

    public void setEawbDestination(String eawbDestination) {
        this.eawbDestination = eawbDestination;
    }

    public String getEawbDepartcountry() {
        return eawbDepartcountry;
    }

    public void setEawbDepartcountry(String eawbDepartcountry) {
        this.eawbDepartcountry = eawbDepartcountry;
    }

    public String getEawbDeparture() {
        return eawbDeparture;
    }

    public void setEawbDeparture(String eawbDeparture) {
        this.eawbDeparture = eawbDeparture;
    }

    public String getEpKey() {
        return epKey;
    }

    public void setEpKey(String epKey) {
        this.epKey = epKey;
    }

    public BigDecimal getPlanAmount() {
        return planAmount;
    }

    public void setPlanAmount(BigDecimal planAmount) {
        this.planAmount = planAmount;
    }

    public String getFormula() {
        return formula;
    }

    public void setFormula(String formula) {
        this.formula = formula;
    }

    public String getPartitionCode() {
        return partitionCode;
    }

    public void setPartitionCode(String partitionCode) {
        this.partitionCode = partitionCode;
    }

    public String getRpName() {
        return rpName;
    }

    public void setRpName(String rpName) {
        this.rpName = rpName;
    }

    public String getPtType() {
        return ptType;
    }

    public void setPtType(String ptType) {
        this.ptType = ptType;
    }

    public String getEawbPrintcode() {
        return eawbPrintcode;
    }

    public void setEawbPrintcode(String eawbPrintcode) {
        this.eawbPrintcode = eawbPrintcode;
    }

    public String getEawbSoCode() {
        return eawbSoCode;
    }

    public void setEawbSoCode(String eawbSoCode) {
        this.eawbSoCode = eawbSoCode;
    }

    public Date getEbaOccurtime() {
        return ebaOccurtime;
    }

    public void setEbaOccurtime(Date ebaOccurtime) {
        this.ebaOccurtime = ebaOccurtime;
    }

    public String getEawbServicetype() {
        return eawbServicetype;
    }

    public void setEawbServicetype(String eawbServicetype) {
        this.eawbServicetype = eawbServicetype;
    }

    public BigDecimal getEawbChargeableweight() {
        return eawbChargeableweight;
    }

    public void setEawbChargeableweight(BigDecimal eawbChargeableweight) {
        this.eawbChargeableweight = eawbChargeableweight;
    }

    public String getEawbOutboundSacId() {
        return eawbOutboundSacId;
    }

    public void setEawbOutboundSacId(String eawbOutboundSacId) {
        this.eawbOutboundSacId = eawbOutboundSacId;
    }

    public String getEawbTrackingNo() {
        return eawbTrackingNo;
    }

    public void setEawbTrackingNo(String eawbTrackingNo) {
        this.eawbTrackingNo = eawbTrackingNo;
    }

    public String getEawbReference2() {
        return eawbReference2;
    }

    public void setEawbReference2(String eawbReference2) {
        this.eawbReference2 = eawbReference2;
    }

    public String getMawbCode() {
        return mawbCode;
    }

    public void setMawbCode(String mawbCode) {
        this.mawbCode = mawbCode;
    }

    public Integer getPdSyscode() {
        return pdSyscode;
    }

    public void setPdSyscode(Integer pdSyscode) {
        this.pdSyscode = pdSyscode;
    }

    public String getSoCode() {
        return soCode;
    }

    public void setSoCode(String soCode) {
        this.soCode = soCode;
    }

    public Integer getRpId() {
        return rpId;
    }

    public void setRpId(Integer rpId) {
        this.rpId = rpId;
    }

    public String getcId() {
        return cId;
    }

    public void setcId(String cId) {
        this.cId = cId;
    }

    public String getCtCode() {
        return ctCode;
    }

    public void setCtCode(String ctCode) {
        this.ctCode = ctCode;
    }

    public String getRpdCate() {
        return rpdCate;
    }

    public void setRpdCate(String rpdCate) {
        this.rpdCate = rpdCate;
    }

    public String getPdName() {
        return pdName;
    }

    public void setPdName(String pdName) {
        this.pdName = pdName;
    }
}
