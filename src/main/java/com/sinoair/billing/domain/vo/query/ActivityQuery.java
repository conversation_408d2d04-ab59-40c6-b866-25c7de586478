package com.sinoair.billing.domain.vo.query;

import java.util.Date;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2017/11/9
 * @time: 16:44
 * @description: 应收费用同步查询参数
 * To change this template use File | Settings | File Templates.
 */
public class ActivityQuery {

    /**
     *  sql 查询条件
     */
    private String eawbPrintcode;

    private String eadCode;

    private String eastCode;

    private String soCode;

    private String nonSoCode;  //非结算对象

    private String eawbServicetype;

    private String isCheckDate;

    public String getEawbPrintcode() {
        return eawbPrintcode;
    }

    public void setEawbPrintcode(String eawbPrintcode) {
        this.eawbPrintcode = eawbPrintcode;
    }

    public String getEadCode() {
        return eadCode;
    }

    public void setEadCode(String eadCode) {
        this.eadCode = eadCode;
    }

    public String getEastCode() {
        return eastCode;
    }

    public void setEastCode(String eastCode) {
        this.eastCode = eastCode;
    }

    public String getSoCode() {
        return soCode;
    }

    public void setSoCode(String soCode) {
        this.soCode = soCode;
    }

    public String getNonSoCode() {
        return nonSoCode;
    }

    public void setNonSoCode(String nonSoCode) {
        this.nonSoCode = nonSoCode;
    }

    public String getEawbServicetype() {
        return eawbServicetype;
    }

    public void setEawbServicetype(String eawbServicetype) {
        this.eawbServicetype = eawbServicetype;
    }

    public String getIsCheckDate() {
        return isCheckDate;
    }

    public void setIsCheckDate(String isCheckDate) {
        this.isCheckDate = isCheckDate;
    }

    public String toString(){
        return "[eawbPrintcode=" + eawbPrintcode +
                ", eadCode=" + eadCode+
                ", eastCode=" + eastCode+
                ", soCode=" + soCode+
                ", nonSoCode=" + nonSoCode+
                ", eawbServicetype="+eawbServicetype+
                "]";
    }
}
