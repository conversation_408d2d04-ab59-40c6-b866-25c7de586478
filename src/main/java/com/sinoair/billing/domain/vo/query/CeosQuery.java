package com.sinoair.billing.domain.vo.query;

import com.sinoair.billing.domain.vo.price.PriceReceiptVO;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2017/11/9
 * @time: 16:44
 * @description: 应收费用同步查询参数
 * To change this template use File | Settings | File Templates.
 */
public class CeosQuery {

    /**
     *  sql 查询条件
     */
    private String soCode;

    private String nonSoCode;  //非结算对象

    private String eadCode;

    private String eadCodes;

    private String eastCode;

    private String eastCodes;

    private String ebapStatus;

    private String startEbaHandletime;

    private String endEbaHandletime;

    private String eawbServicetype;

    private String taskEadCode;

    private String taskEastCode;

    private BigDecimal beginNo;

    private BigDecimal endNo;

    //辅助条件
    private int pageNum;

    private int pageSize;

    private int serialNumber; //序号

    private String handleType;

    private String handleName;

    private Date taskStartTime;

    private String batchId;

    private int allSize;

    private int cainiaoCount;

    private String eawbPrintcode;

    public String rrName;
    public String printcodeType;
    public String soType;

    private String eaCode;

    private Long dmId;

    public String toString(){
        return "[soCode=" + soCode +
                ", eadCode=" + eadCode+
                ", eastCode=" + eastCode+
                ", eadCodes=" + eadCodes+
                ", eastCodes=" + eastCodes+
                ", taskEadCode=" + taskEadCode+
                ", taskEastCode=" + taskEastCode+
                ", ebapStatus=" + ebapStatus+
                ", startEbaHandletime="+ startEbaHandletime +
                ", endEbaHandletime="+ endEbaHandletime +
                ", pageNum="+ pageNum +
                ", pageSize="+ pageSize +
                ", handleType="+ handleType +
                ", handleName="+handleName+
                ", taskStartTime="+taskStartTime+
                ", batchId="+batchId+
                ", allSize="+allSize+
                ", cainiaoCount="+cainiaoCount+
                ", beginNo="+beginNo+
                ", endNo="+endNo+
                "]";
    }


    public String getSoCode() {
        return soCode;
    }

    public void setSoCode(String soCode) {
        this.soCode = soCode;
    }

    public String getStartEbaHandletime() {
        return startEbaHandletime;
    }

    public void setStartEbaHandletime(String startEbaHandletime) {
        this.startEbaHandletime = startEbaHandletime;
    }

    public String getEndEbaHandletime() {
        return endEbaHandletime;
    }

    public void setEndEbaHandletime(String endEbaHandletime) {
        this.endEbaHandletime = endEbaHandletime;
    }

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public String getHandleType() {
        return handleType;
    }

    public void setHandleType(String handleType) {
        this.handleType = handleType;
    }

    public String getHandleName() {
        return handleName;
    }

    public void setHandleName(String handleName) {
        this.handleName = handleName;
    }

    public Date getTaskStartTime() {
        return taskStartTime;
    }

    public void setTaskStartTime(Date taskStartTime) {
        this.taskStartTime = taskStartTime;
    }

    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    public int getAllSize() {
        return allSize;
    }

    public void setAllSize(int allSize) {
        this.allSize = allSize;
    }

    public int getCainiaoCount() {
        return cainiaoCount;
    }

    public void setCainiaoCount(int cainiaoCount) {
        this.cainiaoCount = cainiaoCount;
    }

    public String getEadCode() {
        return eadCode;
    }

    public void setEadCode(String eadCode) {
        this.eadCode = eadCode;
    }

    public String getEastCode() {
        return eastCode;
    }

    public void setEastCode(String eastCode) {
        this.eastCode = eastCode;
    }

    public String getEbapStatus() {
        return ebapStatus;
    }

    public void setEbapStatus(String ebapStatus) {
        this.ebapStatus = ebapStatus;
    }

    public String getEadCodes() {
        return eadCodes;
    }

    public void setEadCodes(String eadCodes) {
        this.eadCodes = eadCodes;
    }

    public String getEastCodes() {
        return eastCodes;
    }

    public void setEastCodes(String eastCodes) {
        this.eastCodes = eastCodes;
    }

    public String getNonSoCode() {
        return nonSoCode;
    }

    public void setNonSoCode(String nonSoCode) {
        this.nonSoCode = nonSoCode;
    }

    public String getEawbServicetype() {
        return eawbServicetype;
    }

    public void setEawbServicetype(String eawbServicetype) {
        this.eawbServicetype = eawbServicetype;
    }

    public String getTaskEadCode() {
        return taskEadCode;
    }

    public void setTaskEadCode(String taskEadCode) {
        this.taskEadCode = taskEadCode;
    }

    public String getTaskEastCode() {
        return taskEastCode;
    }

    public void setTaskEastCode(String taskEastCode) {
        this.taskEastCode = taskEastCode;
    }

    public BigDecimal getBeginNo() {
        return beginNo;
    }

    public void setBeginNo(BigDecimal beginNo) {
        this.beginNo = beginNo;
    }

    public BigDecimal getEndNo() {
        return endNo;
    }

    public void setEndNo(BigDecimal endNo) {
        this.endNo = endNo;
    }

    public String getEawbPrintcode() {
        return eawbPrintcode;
    }

    public void setEawbPrintcode(String eawbPrintcode) {
        this.eawbPrintcode = eawbPrintcode;
    }

    public String getRrName() {
        return rrName;
    }

    public void setRrName(String rrName) {
        this.rrName = rrName;
    }

    public String getPrintcodeType() {
        return printcodeType;
    }

    public void setPrintcodeType(String printcodeType) {
        this.printcodeType = printcodeType;
    }

    public String getEaCode() {
        return eaCode;
    }

    public void setEaCode(String eaCode) {
        this.eaCode = eaCode;
    }

    public Long getDmId() {
        return dmId;
    }

    public void setDmId(Long dmId) {
        this.dmId = dmId;
    }

    public int getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(int serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getSoType() {
        return soType;
    }

    public void setSoType(String soType) {
        this.soType = soType;
    }
}
