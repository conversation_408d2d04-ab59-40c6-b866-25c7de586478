package com.sinoair.billing.domain.vo.query;

import com.sinoair.billing.domain.vo.BootstrapTableQuery;
import com.sinoair.billing.domain.vo.price.PriceReceiptVO;

import java.util.Date;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2017/11/9
 * @time: 16:44
 * @description: 应收费用同步查询参数
 * To change this template use File | Settings | File Templates.
 */
public class InsRecordQuery{

    /**
     *  sql 查询条件
     */
    private String soCode;

    private String eawbKeyentrytime;

    private String startEbaHandletime;

    private String endEbaHandletime;

    //辅助条件
    private int pageNum;

    private int pageSize;

    private List<PriceReceiptVO> priceReceiptList;

    private String handleType;

    private String handleName;

    private Date taskStartTime;

    private String batchId;

    private int allSize;

    private int cainiaoCount;

    private String taskStatus;

    public String toString(){
        return "[soCode=" + soCode +
                ", eawbKeyentrytime=" + eawbKeyentrytime+
                ", startEbaHandletime="+ startEbaHandletime +
                ", endEbaHandletime="+ endEbaHandletime +
                ", pageNum="+ pageNum +
                ", pageSize="+ pageSize +
                ", handleType="+ handleType +
                ", handleName="+handleName+
                ", taskStartTime="+taskStartTime+
                ", batchId="+batchId+
                ", allSize="+allSize+
                ", cainiaoCount="+cainiaoCount+
                "]";
    }


    public String getSoCode() {
        return soCode;
    }

    public void setSoCode(String soCode) {
        this.soCode = soCode;
    }

    public String getEawbKeyentrytime() {
        return eawbKeyentrytime;
    }

    public void setEawbKeyentrytime(String eawbKeyentrytime) {
        this.eawbKeyentrytime = eawbKeyentrytime;
    }

    public String getStartEbaHandletime() {
        return startEbaHandletime;
    }

    public void setStartEbaHandletime(String startEbaHandletime) {
        this.startEbaHandletime = startEbaHandletime;
    }

    public String getEndEbaHandletime() {
        return endEbaHandletime;
    }

    public void setEndEbaHandletime(String endEbaHandletime) {
        this.endEbaHandletime = endEbaHandletime;
    }

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public List<PriceReceiptVO> getPriceReceiptList() {
        return priceReceiptList;
    }

    public void setPriceReceiptList(List<PriceReceiptVO> priceReceiptList) {
        this.priceReceiptList = priceReceiptList;
    }

    public String getHandleType() {
        return handleType;
    }

    public void setHandleType(String handleType) {
        this.handleType = handleType;
    }

    public String getHandleName() {
        return handleName;
    }

    public void setHandleName(String handleName) {
        this.handleName = handleName;
    }

    public Date getTaskStartTime() {
        return taskStartTime;
    }

    public void setTaskStartTime(Date taskStartTime) {
        this.taskStartTime = taskStartTime;
    }

    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    public int getAllSize() {
        return allSize;
    }

    public void setAllSize(int allSize) {
        this.allSize = allSize;
    }

    public int getCainiaoCount() {
        return cainiaoCount;
    }

    public void setCainiaoCount(int cainiaoCount) {
        this.cainiaoCount = cainiaoCount;
    }

    public String getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(String taskStatus) {
        this.taskStatus = taskStatus;
    }
}
