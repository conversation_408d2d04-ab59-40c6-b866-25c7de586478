package com.sinoair.billing.domain.vo.receipt;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by DengQing on 2016/12/17 0017.
 */
public class ReceiptCsvVo {
    private int id;
    private Integer dmId;
    private String dmCode;
    private BigDecimal rrId;
    private int userId;
    private String companyId;
    private String eawbPrintcode;
    private String eawbReference1;
    private String eawbReference2;
    private String rrName;
    private String pName;
    private String ctCode;
    private String prEffectivedate;
    private BigDecimal eawbChargeableweight;
    private BigDecimal rrPlanAmount;
    private BigDecimal rrActualAmount;
    private String soCode;
    private String eastCode;
    private Date prStartEffectivedate;
    private Date prEndEffectivedate;
    private Date rrHandletime;
    private Date startEbaOccurtime;
    private Date endEbaOccurtime;

    private String state;
    private String epValue;

    private Integer tSize;
    private Integer aSize;
    private Integer updSize;

    private String rrRemark;
    private String epKey;
    private String rrType;

    private String soName;
    private String eawbDestcountry;
    private String rrOccurtime;
    private String mawbCode;
    private String eawbTrackingNo;

    public Integer getDmId() {
        return dmId;
    }

    public void setDmId(Integer dmId) {
        this.dmId = dmId;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public BigDecimal getRrId() {
        return rrId;
    }

    public void setRrId(BigDecimal rrId) {
        this.rrId = rrId;
    }

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }

    public String getEawbReference1() {
        return eawbReference1;
    }

    public void setEawbReference1(String eawbReference1) {
        this.eawbReference1 = eawbReference1;
    }

    public String getEawbReference2() {
        return eawbReference2;
    }

    public void setEawbReference2(String eawbReference2) {
        this.eawbReference2 = eawbReference2;
    }

    public String getRrName() {
        return rrName;
    }

    public void setRrName(String rrName) {
        this.rrName = rrName;
    }

    public String getCtCode() {
        return ctCode;
    }

    public void setCtCode(String ctCode) {
        this.ctCode = ctCode;
    }

    public String getPrEffectivedate() {
        return prEffectivedate;
    }

    public void setPrEffectivedate(String prEffectivedate) {
        this.prEffectivedate = prEffectivedate;
    }

    public BigDecimal getRrPlanAmount() {
        return rrPlanAmount;
    }

    public void setRrPlanAmount(BigDecimal rrPlanAmount) {
        this.rrPlanAmount = rrPlanAmount;
    }

    public BigDecimal getRrActualAmount() {
        return rrActualAmount;
    }

    public void setRrActualAmount(BigDecimal rrActualAmount) {
        this.rrActualAmount = rrActualAmount;
    }

    public String getSoCode() {
        return soCode;
    }

    public void setSoCode(String soCode) {
        this.soCode = soCode;
    }

    public BigDecimal getEawbChargeableweight() {
        return eawbChargeableweight;
    }

    public void setEawbChargeableweight(BigDecimal eawbChargeableweight) {
        this.eawbChargeableweight = eawbChargeableweight;
    }

    public String getEastCode() {
        return eastCode;
    }

    public void setEastCode(String eastCode) {
        this.eastCode = eastCode;
    }

    public Date getPrStartEffectivedate() {
        return prStartEffectivedate;
    }

    public void setPrStartEffectivedate(Date prStartEffectivedate) {
        this.prStartEffectivedate = prStartEffectivedate;
    }

    public Date getPrEndEffectivedate() {
        return prEndEffectivedate;
    }

    public void setPrEndEffectivedate(Date prEndEffectivedate) {
        this.prEndEffectivedate = prEndEffectivedate;
    }

    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
    }

    public Date getRrHandletime() {
        return rrHandletime;
    }

    public void setRrHandletime(Date rrHandletime) {
        this.rrHandletime = rrHandletime;
    }

    public String getEawbPrintcode() {
        return eawbPrintcode;
    }

    public void setEawbPrintcode(String eawbPrintcode) {
        this.eawbPrintcode = eawbPrintcode;
    }

    public Date getStartEbaOccurtime() {
        return startEbaOccurtime;
    }

    public void setStartEbaOccurtime(Date startEbaOccurtime) {
        this.startEbaOccurtime = startEbaOccurtime;
    }

    public String getDmCode() {
        return dmCode;
    }

    public void setDmCode(String dmCode) {
        this.dmCode = dmCode;
    }

    public String getpName() {
        return pName;
    }

    public void setpName(String pName) {
        this.pName = pName;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getEpValue() {
        return epValue;
    }

    public void setEpValue(String epValue) {
        this.epValue = epValue;
    }

    public Date getEndEbaOccurtime() {
        return endEbaOccurtime;
    }

    public void setEndEbaOccurtime(Date endEbaOccurtime) {
        this.endEbaOccurtime = endEbaOccurtime;
    }

    public Integer gettSize() {
        return tSize;
    }

    public void settSize(Integer tSize) {
        this.tSize = tSize;
    }

    public Integer getaSize() {
        return aSize;
    }

    public void setaSize(Integer aSize) {
        this.aSize = aSize;
    }

    public Integer getUpdSize() {
        return updSize;
    }

    public void setUpdSize(Integer updSize) {
        this.updSize = updSize;
    }

    public String getRrRemark() {
        return rrRemark;
    }

    public void setRrRemark(String rrRemark) {
        this.rrRemark = rrRemark;
    }

    public String getEpKey() {
        return epKey;
    }

    public void setEpKey(String epKey) {
        this.epKey = epKey;
    }

    public String getRrType() {
        return rrType;
    }

    public void setRrType(String rrType) {
        this.rrType = rrType;
    }

    public String getSoName() {
        return soName;
    }

    public void setSoName(String soName) {
        this.soName = soName;
    }

    public String getEawbDestcountry() {
        return eawbDestcountry;
    }

    public void setEawbDestcountry(String eawbDestcountry) {
        this.eawbDestcountry = eawbDestcountry;
    }

    public String getRrOccurtime() {
        return rrOccurtime;
    }

    public void setRrOccurtime(String rrOccurtime) {
        this.rrOccurtime = rrOccurtime;
    }

    public String getMawbCode() {
        return mawbCode;
    }

    public void setMawbCode(String mawbCode) {
        this.mawbCode = mawbCode;
    }

    public String getEawbTrackingNo() {
        return eawbTrackingNo;
    }

    public void setEawbTrackingNo(String eawbTrackingNo) {
        this.eawbTrackingNo = eawbTrackingNo;
    }
}
