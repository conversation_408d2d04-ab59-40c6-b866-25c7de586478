package com.sinoair.billing.domain.vo.receipt;

import java.math.BigDecimal;

/**
 * Created by DengQing on 2017/1/11 0011.
 */
public class ReceiptExcelHongVo {
    private String rdEawbReference2;
    private String rrName;
    private String eawbReference2;
    private String eawbReference1;
    private String eawbPrintcode;
    private Integer  rdDmId;
    private String   eawbEpValue;
    private String eawbDestcountry;
    private String sacId;
    private BigDecimal rrId;
    private BigDecimal prId;
    private BigDecimal chargeableweight;
    private String eawbServiceType;


    public String getRdEawbReference2() {
        return rdEawbReference2;
    }

    public void setRdEawbReference2(String rdEawbReference2) {
        this.rdEawbReference2 = rdEawbReference2;
    }

    public String getEawbReference2() {
        return eawbReference2;
    }

    public void setEawbReference2(String eawbReference2) {
        this.eawbReference2 = eawbReference2;
    }

    public String getEawbReference1() {
        return eawbReference1;
    }

    public void setEawbReference1(String eawbReference1) {
        this.eawbReference1 = eawbReference1;
    }

    public String getEawbPrintcode() {
        return eawbPrintcode;
    }

    public void setEawbPrintcode(String eawbPrintcode) {
        this.eawbPrintcode = eawbPrintcode;
    }

    public Integer getRdDmId() {
        return rdDmId;
    }

    public void setRdDmId(Integer rdDmId) {
        this.rdDmId = rdDmId;
    }

    public String getEawbEpValue() {
        return eawbEpValue;
    }

    public void setEawbEpValue(String eawbEpValue) {
        this.eawbEpValue = eawbEpValue;
    }

    public String getEawbDestcountry() {
        return eawbDestcountry;
    }

    public void setEawbDestcountry(String eawbDestcountry) {
        this.eawbDestcountry = eawbDestcountry;
    }

    public String getRrName() {
        return rrName;
    }

    public void setRrName(String rrName) {
        this.rrName = rrName;
    }

    public String getSacId() {
        return sacId;
    }

    public void setSacId(String sacId) {
        this.sacId = sacId;
    }

    public BigDecimal getRrId() {
        return rrId;
    }

    public void setRrId(BigDecimal rrId) {
        this.rrId = rrId;
    }

    public BigDecimal getPrId() {
        return prId;
    }

    public void setPrId(BigDecimal prId) {
        this.prId = prId;
    }

    public BigDecimal getChargeableweight() {
        return chargeableweight;
    }

    public void setChargeableweight(BigDecimal chargeableweight) {
        this.chargeableweight = chargeableweight;
    }

    public String getEawbServiceType() {
        return eawbServiceType;
    }

    public void setEawbServiceType(String eawbServiceType) {
        this.eawbServiceType = eawbServiceType;
    }
}
