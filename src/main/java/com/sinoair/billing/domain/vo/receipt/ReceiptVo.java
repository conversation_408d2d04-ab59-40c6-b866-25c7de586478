package com.sinoair.billing.domain.vo.receipt;

import com.sinoair.billing.domain.model.billing.DebitManifest;

import java.math.BigDecimal;

/**
 * Created by DengQing on 2016/12/16 0016.
 */
public class ReceiptVo extends DebitManifest {
    private int dmUserId;

    private String  companyId;


    private String dmCode;

    private int countNum;

    private BigDecimal sumRrPlanAmount;

    private BigDecimal sumRactualAmount;

    private BigDecimal sumEawbChargeableweight;

    private String soName;


    public int getCountNum() {
        return countNum;
    }

    public void setCountNum(int countNum) {
        this.countNum = countNum;
    }

    public String getDmCode() {
        return dmCode;
    }

    public void setDmCode(String dmCode) {
        this.dmCode = dmCode;
    }

    public BigDecimal getSumRrPlanAmount() {
        return sumRrPlanAmount;
    }

    public void setSumRrPlanAmount(BigDecimal sumRrPlanAmount) {
        this.sumRrPlanAmount = sumRrPlanAmount;
    }

    public BigDecimal getSumRactualAmount() {
        return sumRactualAmount;
    }

    public void setSumRactualAmount(BigDecimal sumRactualAmount) {
        this.sumRactualAmount = sumRactualAmount;
    }

    public BigDecimal getSumEawbChargeableweight() {
        return sumEawbChargeableweight;
    }

    public void setSumEawbChargeableweight(BigDecimal sumEawbChargeableweight) {
        this.sumEawbChargeableweight = sumEawbChargeableweight;
    }


    public void setDmUserId(int dmUserId) {
        this.dmUserId = dmUserId;
    }

    @Override
    public String getCompanyId() {
        return companyId;
    }

    @Override
    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }

    public String getSoName() {
        return soName;
    }

    public void setSoName(String soName) {
        this.soName = soName;
    }
}
