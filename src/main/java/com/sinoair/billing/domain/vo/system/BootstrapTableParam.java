package com.sinoair.billing.domain.vo.system;

/**
 * Created by IntelliJ IDEA.
 * User: <PERSON>
 * Date: 2017/9/25
 * Time: 14:06
 * Description:
 * To change this template use File | Settings | File Templates.
 */
public class BootstrapTableParam {

    /**
     * bootstrap table 左上角单框搜索
     */
    private String search;
    /**
     * 分页时数据的偏移量
     */
    private Integer offset = 0;
    /**
     * 每页的数量 默认10
     */
    private Integer limit = 10;
    /**
     * 排序字段
     */
    private String sort;
    /**
     * asc desc
     */
    private String order;

    public String getSearch() {
        return search;
    }

    public void setSearch(String search) {
        this.search = search;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public String getSort() {
        return sort;
    }

    public void setSort(String sort) {
        this.sort = sort;
    }

    public String getOrder() {
        return order;
    }

    public void setOrder(String order) {
        this.order = order;
    }
}
