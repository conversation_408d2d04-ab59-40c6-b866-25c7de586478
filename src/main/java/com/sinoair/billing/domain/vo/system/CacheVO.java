package com.sinoair.billing.domain.vo.system;

import java.util.Date;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: 大雄
 * @date: 2018/3/22
 * @time: 16:30
 * @description: 缓存容器
 * To change this template use File | Settings | File Templates.
 */
public class CacheVO {

    /**
     * 缓存唯一id
     */
    private String cacheId;

    /**
     * 缓存内容
     */
    private String content;

    /**
     * 创建时间
     */
    private Date createTime;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCacheId() {
        return cacheId;
    }

    public void setCacheId(String cacheId) {
        this.cacheId = cacheId;
    }
}
