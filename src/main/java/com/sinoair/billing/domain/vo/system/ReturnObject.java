package com.sinoair.billing.domain.vo.system;

/**
 * ajax返回对象
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2016/2/18.
 */
public class ReturnObject<T> {

    public static final String MSG_CODE_FAIL = "-1";
    public static final String MSG_CODE_SUCCESS = "0000";
    public static final String MSG_CODE_NODATA = "0001";
    public static final String MSG_CODE_SERVER_EXCEPTION = "0002";
    public static final String MSG_CODE_SESSION_TIMEOUT = "0003";
    public static final String MSG_CODE_ERROR_TOKEN = "0004";

    public static final String MESSAGE_FAIL = "fail";
    public static final String MESSAGE_SUCCESS = "success";
    public static final String MESSAGE_NODATA = "no data";
    public static final String MESSAGE_SERVER_EXCEPTION = "server exception";
    public static final String MESSAGE_SESSION_TIMEOUT = "session time out";
    public static final String MESSAGE_ERROR_TOKEN = "error token";
    private String msgCode;
    private String message;
    private String version;
    private String token;
    private T data;

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public String getMsgCode() {
        return msgCode;
    }

    public void setMsgCode(String msgCode) {
        this.msgCode = msgCode;
    }

}
