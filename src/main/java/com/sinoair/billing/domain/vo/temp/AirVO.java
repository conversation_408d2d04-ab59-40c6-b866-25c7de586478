package com.sinoair.billing.domain.vo.temp;

import java.math.BigDecimal;

public class AirVO {


    private String eaCode;
    private String pkgPrintcode;
    private String epKey;
    private String eawbReference2;
    private String eawbReference1;
    private String eawbKeyentrytime;
    private String weight;

    public String getEaCode() {
        return eaCode;
    }

    public void setEaCode(String eaCode) {
        this.eaCode = eaCode;
    }

    public String getPkgPrintcode() {
        return pkgPrintcode;
    }

    public void setPkgPrintcode(String pkgPrintcode) {
        this.pkgPrintcode = pkgPrintcode;
    }

    public String getEpKey() {
        return epKey;
    }

    public void setEpKey(String epKey) {
        this.epKey = epKey;
    }

    public String getEawbReference1() {
        return eawbReference1;
    }

    public void setEawbReference1(String eawbReference1) {
        this.eawbReference1 = eawbReference1;
    }

    public String getEawbReference2() {
        return eawbReference2;
    }

    public void setEawbReference2(String eawbReference2) {
        this.eawbReference2 = eawbReference2;
    }

    public String getEawbKeyentrytime() {
        return eawbKeyentrytime;
    }

    public void setEawbKeyentrytime(String eawbKeyentrytime) {
        this.eawbKeyentrytime = eawbKeyentrytime;
    }

    public String getWeight() {
        return weight;
    }

    public void setWeight(String weight) {
        this.weight = weight;
    }
}
