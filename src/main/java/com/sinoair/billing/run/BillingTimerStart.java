package com.sinoair.billing.run;

import code.sinoair.core.support.cron.SinoairCron;
import com.sinoair.billing.core.exception.SinoAirException;
import com.sinoair.billing.core.util.SpringContextUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.support.AbstractApplicationContext;
import org.springframework.context.support.ClassPathXmlApplicationContext;

import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: 大雄
 * @date: 2018/7/4
 * @time: 10:33
 * @description: 定时任务启动类入口
 * 　　　　　　　　┏┓　　　┏┓
 * 　　　　　　　┏┛┻━━━┛┻┓
 * 　　　　　　　┃　　　　　　　┃
 * 　　　　　　　┃　　　━　　　┃
 * 　　　　　　　┃　＞　　　＜　┃
 * 　　　　　　　┃　　　　　　　┃
 * 　　　　　　　┃...　⌒　...　┃
 * 　　　　　　　┃　　　　　　　┃
 * 　　　　　　　┗━┓　　　┏━┛
 * 　　　　　　　　　┃　　　┃　Code is far away from bug with the animal protecting
 * 　　　　　　　　　┃　　　┃   神兽保佑,代码无bug
 * 　　　　　　　　　┃　　　┃
 * 　　　　　　　　　┃　　　┃
 * 　　　　　　　　　┃　　　┃
 * 　　　　　　　　　┃　　　┃
 * 　　　　　　　　　┃　　　┗━━━┓
 * 　　　　　　　　　┃　　　　　　　┣┓
 * 　　　　　　　　　┃　　　　　　　┏┛
 * 　　　　　　　　　┗┓┓┏━┳┓┏┛
 * 　　　　　　　　　　┃┫┫　┃┫┫
 * 　　　　　　　　　　┗┻┛　┗┻┛
 * To change this template use File | Settings | File Templates.
 */
public class BillingTimerStart {

    private static Logger logger = LoggerFactory.getLogger(BillingTimerStart.class);

    public static void main(String[] args) throws SinoAirException, ClassNotFoundException, NoSuchMethodException, IllegalAccessException, InvocationTargetException, InstantiationException {
        showAnimal();
        if (args.length == 0){
            logger.info("Args error: Please start such as 'java -jar xxx.jar 定时任务注册 [超时时间]'");
            throw new SinoAirException("Args error: Please start such as 'java -jar xxx.jar 定时任务注册 [超时时间]'");
        }else{

            logger.info("本次调用开始启动");
            String timerName = args[0];
            /**
             * 默认执行1小时
             */
            long timeout = 1000*60*60;
            if (args.length > 1){
                timeout = Long.valueOf(args[1]);
            }
            Long start = System.currentTimeMillis();
            //多线程执行判断：多线程的话不能直接退出
            boolean exit = true;
            if (args.length > 2){
                if ("T".equals(args[2])){
                    exit = false;
                }
            }
            //加载spring
            logger.info("== loading spring ==");
            AbstractApplicationContext context = new ClassPathXmlApplicationContext("conf/applicationContext.xml");
            new SpringContextUtil().setApplicationContext(context);
            logger.info("== loading spring success ==");
            //执行定时任务
            logger.info("== sinoair sorting timer start ==");
            //反射生成类
            Class clazz = Class.forName("com.sinoair.billing.timer."+timerName);
            Constructor c = clazz.getConstructor(String.class, long.class, boolean.class);
            SinoairCron cron = (SinoairCron) c.newInstance(timerName,timeout,exit);
            try {
                cron.start();
                logger.info("start timer success");
            }catch (Exception e){
                logger.info("start timer error");
                logger.error(e.getMessage(),e);
            }
            logger.info("start timer end");
        }
    }

    public static void showAnimal(){
        String animal = "\n" +
                "　┏┓　　　┏┓\n" +
                "┏┛┻━━━┛┻┓\n" +
                "┃　　　　　　　┃\n" +
                "┃　　　━　　　┃\n" +
                "┃　＞　　　＜　┃\n" +
                "┃　　　　　　　┃\n" +
                "┃...　⌒　...　┃\n" +
                "┃　　　　　　　┃\n" +
                "┗━┓　　　┏━┛\n" +
                "　　┃　　　┃　Code is far away from bug with the animal protecting\n" +
                "　　┃　　　┃   神兽保佑,代码无bug\n" +
                "　　┃　　　┃\n" +
                "　　┃　　　┃\n" +
                "　　┃　　　┃\n" +
                "　　┃　　　┃\n" +
                "　　┃　　　┗━━━┓\n" +
                "　　┃　　　　　　　┣┓\n" +
                "　　┃　　　　　　　┏┛\n" +
                "　　┗┓┓┏━┳┓┏┛\n" +
                "　　　┃┫┫　┃┫┫\n" +
                "　　　┗┻┛　┗┻┛";
        logger.info(animal);
    }

}
