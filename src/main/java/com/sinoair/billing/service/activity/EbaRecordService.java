package com.sinoair.billing.service.activity;

import com.sinoair.billing.domain.model.billing.CheckSynEbaConfig;
import com.sinoair.billing.domain.model.billing.ExpressBusinessActivity;
import com.sinoair.billing.domain.vo.query.CeosQuery;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: 大雄
 * @date: 2018/1/30
 * @time: 9:52
 * @description: To change this template use File | Settings | File Templates.
 */
public interface EbaRecordService {


    /**
     * 查询序列
     * @return
     */
    Integer selectSeq();

    Long selectMaxEbaCode();

    List<ExpressBusinessActivity> selectCeosActivityBySysCode(CeosQuery param);


}
