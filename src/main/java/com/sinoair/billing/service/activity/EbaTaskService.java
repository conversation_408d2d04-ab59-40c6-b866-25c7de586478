package com.sinoair.billing.service.activity;

import com.sinoair.billing.domain.model.billing.EbaTask;
import com.sinoair.billing.domain.vo.query.CeosQuery;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: 大雄
 * @date: 2018/1/30
 * @time: 9:52
 * @description: To change this template use File | Settings | File Templates.
 */
public interface EbaTaskService {

    List<EbaTask> selectByCondition(CeosQuery param);

    int updateByPrimaryKeySelective(EbaTask record);

    EbaTask selectByTaskType(String taskType);



}
