package com.sinoair.billing.service.activity.impl;


import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.sinoair.billing.core.util.CheckUtil;
import com.sinoair.billing.core.util.DateUtil;
import com.sinoair.billing.core.util.MailUtil;
import com.sinoair.billing.core.util.StringUtil;
import com.sinoair.billing.dao.billing.CheckEbaCeosTmpMapper;
import com.sinoair.billing.dao.billing.ExpressAirWayBillMapper;
import com.sinoair.billing.dao.billing.ExpressBusinessActivityMapper;
import com.sinoair.billing.dao.billing.RecordLogMapper;
import com.sinoair.billing.dao.ceos.CeosActivityMapper;
import com.sinoair.billing.dao.ceos.CeosEawbEaMapper;
import com.sinoair.billing.domain.constant.CheckConstant;
import com.sinoair.billing.domain.constant.EastEnum;
import com.sinoair.billing.domain.constant.EbaConstant;
import com.sinoair.billing.domain.constant.MailConstant;
import com.sinoair.billing.domain.model.billing.*;
import com.sinoair.billing.domain.vo.ceos.CeosEawbEaVO;
import com.sinoair.billing.domain.vo.query.ActivityQuery;
import com.sinoair.billing.domain.vo.query.CeosQuery;
import com.sinoair.billing.service.activity.EbaRecordService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.TreeSet;

import static java.util.Comparator.comparing;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;

/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2018/1/30
 * @time: 9:59
 * @description: To change this template use File | Settings | File Templates.
 */
@Service("EbaRecordService")
public class EbaRecordServiceImpl implements EbaRecordService {

    private Logger logger = LoggerFactory.getLogger(EbaRecordServiceImpl.class);

    @Autowired
    private CeosActivityMapper ceosActivityMapper;

    @Autowired
    private ExpressBusinessActivityMapper activityMapper;

    @Autowired
    private ExpressAirWayBillMapper eawbMapper;

    @Autowired
    private RecordLogMapper recordLogMapper;

    @Autowired
    private CheckEbaCeosTmpMapper checkEbaCeosTmpMapper;

    @Autowired
    private ExpressAirWayBillMapper billMapper;

    @Autowired
    private CeosEawbEaMapper ceosEawbEaMapper;

    private BigDecimal zero = new BigDecimal(0);

    private int length = 1000;

    @Override
    public Integer selectSeq(){
        return recordLogMapper.selectSeq();
    }

    @Override
    public Long selectMaxEbaCode(){
        return ceosActivityMapper.selectMaxEbaCode();
    }


    @Override
    public  List<ExpressBusinessActivity> selectCeosActivityBySysCode(CeosQuery param){
        return  ceosActivityMapper.selectCeosActivity(param);
    }

}
