package com.sinoair.billing.service.activity.impl;


import com.alibaba.fastjson.JSONObject;
import com.sinoair.billing.core.util.DateUtil;
import com.sinoair.billing.core.util.MailUtil;
import com.sinoair.billing.core.util.StringUtil;
import com.sinoair.billing.dao.billing.EbaTaskMapper;
import com.sinoair.billing.domain.constant.EbaConstant;
import com.sinoair.billing.domain.constant.MailConstant;
import com.sinoair.billing.domain.constant.ReceiptRecordConstant;
import com.sinoair.billing.domain.model.billing.EbaTask;
import com.sinoair.billing.domain.vo.query.CeosQuery;
import com.sinoair.billing.service.activity.EbaRecordService;
import com.sinoair.billing.service.activity.EbaTaskService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2018/1/30
 * @time: 9:59
 * @description: To change this template use File | Settings | File Templates.
 */
@Service("EbaTaskService")
public class EbaTaskServiceImpl implements EbaTaskService {

    private Logger logger = LoggerFactory.getLogger(EbaTaskServiceImpl.class);

    @Autowired
    private EbaTaskMapper ebaTaskMapper;

    @Autowired
    private EbaRecordService ebaRecordService;


    @Override
    public List<EbaTask> selectByCondition(CeosQuery param){
        return ebaTaskMapper.selectByCondition(param);
    }

    @Override
    public int updateByPrimaryKeySelective(EbaTask record){
        return ebaTaskMapper.updateByPrimaryKeySelective(record);
    }


    @Override
    public EbaTask selectByTaskType(String taskType){
        return ebaTaskMapper.selectByTaskType(taskType);
    }


}
