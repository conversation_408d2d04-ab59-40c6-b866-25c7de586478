package com.sinoair.billing.service.check;

import com.sinoair.billing.domain.model.billing.CheckSynEbaConfig;
import com.sinoair.billing.domain.model.billing.ExpressBusinessActivity;
import com.sinoair.billing.domain.vo.query.CeosQuery;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: 大雄
 * @date: 2018/1/30
 * @time: 9:52
 * @description: To change this template use File | Settings | File Templates.
 */
public interface CheckInsEbaService {


    String handleCheckInsEba(CeosQuery queryParam);

    String handleEbaTmp(CeosQuery queryParam);

    String handleEbaError(CeosQuery queryParam);

    String handleSynEba(int threadNum, List<ExpressBusinessActivity> activityList, List<CheckSynEbaConfig> configList);


}
