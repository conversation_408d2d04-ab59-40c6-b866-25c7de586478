package com.sinoair.billing.service.check;

import com.sinoair.billing.domain.model.billing.PaymentRecord;
import com.sinoair.billing.domain.vo.check.EawbCheckVO;
import com.sinoair.billing.domain.vo.query.CeosQuery;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: 大雄
 * @date: 2018/1/30
 * @time: 9:52
 * @description: To change this template use File | Settings | File Templates.
 */
public interface EawbCheckDetailService {


    List<String> listLackEawb(CeosQuery queryParam);

    List<PaymentRecord> selectPrByCondition(EawbCheckVO param);

}
