package com.sinoair.billing.service.check.impl;


import com.sinoair.billing.dao.billing.CheckEawbNoMapper;
import com.sinoair.billing.dao.billing.CheckEawbResultDetailMapper;
import com.sinoair.billing.dao.billing.CheckEawbResultMapper;
import com.sinoair.billing.dao.ceos.CeosEawbMapper;
import com.sinoair.billing.domain.constant.CheckConstant;
import com.sinoair.billing.domain.model.billing.CheckEawbNo;
import com.sinoair.billing.domain.vo.check.EawbCheckVO;
import com.sinoair.billing.service.check.CheckEawbNoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2018/1/30
 * @time: 9:59
 * @description: To change this template use File | Settings | File Templates.
 */
@Service("CheckEawbNoService")
public class CheckEawbNoServiceImpl implements CheckEawbNoService {

    private Logger logger = LoggerFactory.getLogger(CheckEawbNoServiceImpl.class);

    @Autowired
    private CeosEawbMapper ceosEawbMapper;

    @Autowired
    private CheckEawbNoMapper checkEawbNoMapper;

    @Autowired
    private CheckEawbResultMapper checkEawbResultMapper;

    @Autowired
    private CheckEawbResultDetailMapper resultDetailMapper;


    @Override
    public CheckEawbNo selectCheckNo(String checkType){
        return checkEawbNoMapper.selectByType(checkType);
    }


    @Override
    public void updateEawbNo(CheckEawbNo checkNo){
        checkEawbNoMapper.updateByPrimaryKeySelective(checkNo);
    }

}
