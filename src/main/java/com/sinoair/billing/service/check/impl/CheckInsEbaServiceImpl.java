package com.sinoair.billing.service.check.impl;


import com.github.pagehelper.PageHelper;
import com.sinoair.billing.core.util.CheckUtil;
import com.sinoair.billing.core.util.DateUtil;
import com.sinoair.billing.core.util.MailUtil;
import com.sinoair.billing.core.util.StringUtil;
import com.sinoair.billing.dao.billing.*;
import com.sinoair.billing.dao.ceos.CeosActivityMapper;
import com.sinoair.billing.dao.ceos.CeosEawbEaMapper;
import com.sinoair.billing.dao.ceos.CeosEawbMapper;
import com.sinoair.billing.domain.constant.*;
import com.sinoair.billing.domain.model.billing.*;
import com.sinoair.billing.domain.vo.ceos.CeosEawbEaVO;
import com.sinoair.billing.domain.vo.check.EawbCheckVO;
import com.sinoair.billing.domain.vo.query.ActivityQuery;
import com.sinoair.billing.domain.vo.query.CeosQuery;
import com.sinoair.billing.service.check.CheckInsEbaService;
import com.sinoair.billing.service.receipt.ReceiptRecordService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.TreeSet;

import static java.util.Comparator.comparing;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;

/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2018/1/30
 * @time: 9:59
 * @description: To change this template use File | Settings | File Templates.
 */
@Service("CheckInsEbaService")
public class CheckInsEbaServiceImpl implements CheckInsEbaService {

    private Logger logger = LoggerFactory.getLogger(CheckInsEbaServiceImpl.class);

    private final static String STATUS_Y="Y";
    private final static String STATUS_N="N";
    private int length = 1000;

    private final static String LIQUIDATION = "LIQUIDATION";
    private final static String GFC_OUTBOUND = "GFC_OUTBOUND";

    @Autowired
    private CeosActivityMapper ceosActivityMapper;

    @Autowired
    private CheckEawbResultDetailMapper eawbResultDetailMapper;

    @Autowired
    private CheckEbaCeosTmpMapper checkEbaCeosTmpMapper;

    @Autowired
    private ExpressAirWayBillMapper billMapper;

    @Autowired
    private ExpressBusinessActivityMapper activityMapper;

    @Autowired
    private ReceiptRecordMapper receiptRecordMapper;

    @Autowired
    private RecordLogMapper recordLogMapper;

    @Autowired
    private CeosEawbMapper ceosEawbMapper;

    @Autowired
    private CheckEawbResultDetailMapper checkEawbResultDetailMapper;

    @Autowired
    private ReceiptRecordTmpMapper recordTmpMapper;

    @Autowired
    private ReceiptRecordService receiptRecordService;

    @Autowired
    private SettlementObjectMapper settlementObjectMapper;
    @Autowired
    private CeosEawbEaMapper ceosEawbEaMapper;


    /**
     * 已废弃
     * @param queryParam
     * @return
     */
    @Override
    public String handleCheckInsEba(CeosQuery queryParam){
        logger.info("线程"+queryParam.getPageNum()+"开始查询===="+new Date());

        RecordLog recordLog = new RecordLog();
        recordLog.setLogId(BigDecimal.valueOf(recordLogMapper.selectSeq()));
        recordLog.setLogCreatetime(new Date());
        recordLog.setTaskStarttime(queryParam.getTaskStartTime());
        recordLog.setRecordBatch(DateUtil.date2str(new Date(),DateUtil.YYYYMMDD));
        recordLog.setRecordCount(Short.valueOf(queryParam.getPageNum()+""));
        recordLog.setRecordModule(EbaConstant.M_CEOS_EBA);
        recordLog.setRecordName("非菜鸟CEOS环节同步");
        recordLog.setRecordParam(queryParam.toString());
        recordLog.setRecordRemark(EbaConstant.SUCCESS);
        recordLog.setRecordType(CheckConstant.CHECK_INS_EBA);
        recordLog.setAllSize(BigDecimal.valueOf(queryParam.getAllSize()));
        recordLog.setHandleDateStr(queryParam.getStartEbaHandletime());
        recordLog.setDeadlineDateStr(queryParam.getEndEbaHandletime());
        try {
            recordLog.setHandleDate(DateUtil.str2date(queryParam.getStartEbaHandletime(),DateUtil.YYYYMMDD));
            recordLog.setDeadlineDate(DateUtil.str2date(queryParam.getEndEbaHandletime(),DateUtil.YYYYMMDD));
        } catch (ParseException e) {
            e.printStackTrace();
        }

        logger.info("线程"+queryParam.getPageNum()+"开始查询===="+new Date());
        long allstart = System.currentTimeMillis();
        long allcost = 0;
        long querycost = 0;
        long checkcost = 0;
        long batchcost = 0;
        int querySize = 0;
        int handleSize = 0;
        int failSize = 0;
        int checkSize = 0;

        Date sysdate = new Date();

        try {

            PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize(),false);
            List<ExpressBusinessActivity> activityList = ceosActivityMapper.selectCeosActivity(queryParam);
            logger.info("线程"+queryParam.getPageNum()+"查询结束===="+new Date());
            List<String> eawbPrintCodes = new ArrayList<>();
            if (activityList != null){

                logger.info("线程"+queryParam.getPageNum()+"==activityList.size=="+activityList.size());
                List<ExpressBusinessActivity> tmpList = new ArrayList<>();
                for (ExpressBusinessActivity activity : activityList){

                    String eawbPrintcode = activity.getEawbPrintcode();
                    String eadCode = activity.getEadCode();
                    String eastCode = activity.getEastCode();

                    if (CheckUtil.checkCommonEad(eadCode,eastCode)){
                        if (checkAllActivity(eawbPrintcode,eadCode,eastCode)){
                            checkSize++;
                            continue;
                        }
                        if (LIQUIDATION.equals(eadCode)){
                            activity.setQaPushtime(activity.getEbaOccurtime()==null?sysdate:activity.getEbaOccurtime());
                        }else{
                            activity.setQaPushtime(activity.getEbaHandletime());
                        }

                        activity.setEbaHandletime(activity.getEbaOccurtime()==null?sysdate:activity.getEbaOccurtime());

                        tmpList.add(activity);
                        continue;
                    }

                    activity.setEbaHandletime(activity.getEbaOccurtime()==null?sysdate:activity.getEbaOccurtime());
                    activity.setQaPushtime(activity.getEbaOccurtime()==null?sysdate:activity.getEbaOccurtime());


                    if (CheckUtil.checkNonEad(eadCode,eastCode)){
                        ExpressAirWayBill bill = billMapper.selectByEawbPrintCode(activity.getEawbPrintcode());
                        if (bill == null ){
                            if (checkAllActivity(eawbPrintcode,eadCode,eastCode)){
                                checkSize++;
                                continue;
                            }
                            tmpList.add(activity);
                            continue;
                        }
                        if (!CheckConstant.CN_SO_CODE.equals(bill.getEawbSoCode())){
                            if (checkAllActivity(eawbPrintcode,eadCode,eastCode)){
                                checkSize++;
                                continue;
                            }
                            tmpList.add(activity);
                            continue;
                        }
                        if (CheckConstant.CN_SO_CODE.equals(bill.getEawbSoCode())){
                            if (CheckConstant.FC_INBOUND.equals(eadCode)){
                                if (CheckUtil.checkCnServiceType(bill.getEawbServiceTypeOriginal())){
                                    if (checkAllActivity(eawbPrintcode,eadCode,eastCode)){
                                        checkSize++;
                                        continue;
                                    }
                                    tmpList.add(activity);
                                    continue;
                                }
                            }
                        }
                    }

                }


                logger.info(queryParam.getHandleType()+" 线程"+queryParam.getPageNum()+"==去重开始.size=="+tmpList.size());
                List<ExpressBusinessActivity> recordList = tmpList.stream().collect(
                        collectingAndThen(
                                toCollection(() -> new TreeSet<>(comparing(n->n.getEawbPrintcode()+";"+n.getEadCode()+";"+n.getEastCode()))),ArrayList::new)
                );
                logger.info(queryParam.getHandleType()+" 线程"+queryParam.getPageNum()+"==去重结束.size=="+recordList.size());
                checkSize = checkSize + (tmpList.size() - recordList.size());

                handleSize = recordList.size();

                List<CheckEbaCeosTmp> detailList = new ArrayList<>();
                for (ExpressBusinessActivity activity : recordList){
                    if (StringUtil.isNotEmpty(activity.getEawbPrintcode())){
                        CheckEbaCeosTmp tmp = new CheckEbaCeosTmp();
                        tmp.setEbaHandletime(activity.getEbaOccurtime());
                        tmp.setEadCode(activity.getEadCode());
                        tmp.setEastCode(activity.getEastCode());
                        tmp.setEawbPrintcode(activity.getEawbPrintcode());
                        tmp.setCectHandletime(new Date());
                        detailList.add(tmp);
                        if ("LIQUIDATION".equals(activity.getEadCode())){
                            eawbPrintCodes.add(activity.getEawbPrintcode());
                        }
                    }
                }

                if (detailList.size() > 0){
                    logger.info("新增check_eba_ceos_tmp数据："+detailList.size());
                    int dsize = detailList.size();
                    int dcount = dsize % length != 0 ? dsize / length + 1 : dsize / length;
                    for (int i = 0; i < dcount; i++) {
                        List<CheckEbaCeosTmp> subList = detailList.subList(i * length, ((1000 + i * length) < dsize ? (1000 + i * length) : dsize));
                        if (subList.size() > 0){
                            checkEbaCeosTmpMapper.insertBatch(subList);
                        }
                    }
                }

                long batchstart= System.currentTimeMillis();
                if (recordList.size() > 0){
                    logger.info("修改EBA数据："+handleSize);
                    logger.info("新增环节数据："+recordList.size());
                    int size = recordList.size();
                    int count = size % length != 0 ? size / length + 1 : size / length;
                    for (int i = 0; i < count; i++) {
                        List<ExpressBusinessActivity> subList = recordList.subList(i * length, ((1000 + i * length) < size ? (1000 + i * length) : size));
                        if (subList.size() > 0){
                            activityMapper.insertBatch(subList);
                        }
                    }
                }
                if (eawbPrintCodes.size() > 0) {
                    int eSize = eawbPrintCodes.size();
                    int eCount = eSize % length != 0 ? eSize / length + 1 : eSize / length;
                    for (int i = 0; i < eCount; i++) {
                        List<String> esubList = eawbPrintCodes.subList(i * length, ((1000 + i * length) < eSize ? (1000 + i * length) : eSize));
                        if (esubList.size() > 0){
                            billMapper.batchUpdateHandletime(esubList);
                        }
                    }
                }
                logger.info("线程"+queryParam.getPageNum()+"批量执行...end时效："+(System.currentTimeMillis()-batchstart));

            }

        } catch (Exception e){
            logger.error("CheckInsEbaTimer异常"+e.getMessage());
            recordLog.setRecordRemark(EbaConstant.ERROR);
            e.printStackTrace();
            MailUtil.postMail(MailConstant.MAIL_TO_ONE,MailConstant.MAIL_SUBJECT_INS_EBA,e.getMessage());
        } finally {
            allcost = System.currentTimeMillis() - allstart;
            recordLog.setAllCost(BigDecimal.valueOf(allcost));
            recordLog.setQueryCost(BigDecimal.valueOf(querycost));
            recordLog.setCheckCost(BigDecimal.valueOf(checkcost));
            recordLog.setHandleCost(BigDecimal.valueOf(batchcost));
            recordLog.setQuerySize(Long.valueOf(querySize));
            recordLog.setFailSize(Long.valueOf(failSize));
            recordLog.setCheckSize(Long.valueOf(checkSize));
            recordLog.setHandleSize(Long.valueOf(handleSize));
            recordLog.setActualAmount(BigDecimal.ZERO);

            recordLog.setLogHandletime(new Date());

            recordLogMapper.insertSelective(recordLog);
        }
        logger.info("线程"+queryParam.getPageNum()+"总用时===");


        return handleSize+"";
    }


    @Override
    public String handleEbaTmp(CeosQuery queryParam){
        logger.info("线程"+queryParam.getPageNum()+"开始查询===="+new Date());

        int handleSize = 0;

        try {

            PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize(),false);
            List<CheckEbaCeosTmp> recordTmpList = checkEbaCeosTmpMapper.list();
            logger.info("线程"+queryParam.getPageNum()+"查询结束===="+new Date());

            if (recordTmpList != null){
                List<CheckEbaCeosTmp> deleteList = new ArrayList<>();
                List<CheckEbaCeosTmp> errAddList = new ArrayList<>();
                List<String> lackEawbList = new ArrayList<>();
                logger.info("线程"+queryParam.getPageNum()+"==recordTmpList.size=="+recordTmpList.size());

                List<ReceiptRecordTmp> receiptRecordTmpList = new ArrayList<>();

                for (CheckEbaCeosTmp tmp : recordTmpList){

                    String eawbPrintcode = tmp.getEawbPrintcode();
                    EawbCheckVO param = new EawbCheckVO();
                    param.setEawbPrintcode(tmp.getEawbPrintcode());
                    param.setEadCode(tmp.getEadCode());
                    param.setEastCode(tmp.getEastCode());

                    if (CheckConstant.CN_SO_CODE.equals(tmp.getSoCode())){
                        continue;
                    }

                    ExpressAirWayBill bill = billMapper.selectByEawbPrintCode(eawbPrintcode);

                    if (bill == null){
                        errAddList.add(tmp);
                        deleteList.add(tmp);
                        lackEawbList.add(eawbPrintcode);
                        continue;
                    }
                    SettlementObject so = settlementObjectMapper.selectBySoCode(tmp.getSoCode());
                    if (so == null || !"MONTHLY".equals(so.getSoMode())){
                        continue;
                    }

                    try{
                        List<ReceiptRecordTmp> tmpList = null;

                        tmpList = receiptRecordService.getRRNonList(eawbPrintcode,null);

                        if (tmpList != null && tmpList.size() > 0){
                            for (ReceiptRecordTmp rrTmp : tmpList){
                                rrTmp.setRrType("1");
                                rrTmp.setRrActualAmount(rrTmp.getRrPlanAmount());
                                rrTmp.setRrStatus("ON");
                                rrTmp.setRrUserId(Short.valueOf("1"));
                                rrTmp.setRrHandletime(new Date());
                                rrTmp.setRrAwbType("H");
                                rrTmp.setRrRemark("");
                                rrTmp.setRrOccurtime2(rrTmp.getRrOccurtime());
                                rrTmp.setChargeableweight(BigDecimal.ZERO);
                            }

                            receiptRecordTmpList.addAll(tmpList);

                        }


                    }catch (Exception e){
                        logger.info("eawb单号："+eawbPrintcode);
                        e.printStackTrace();
                    }

                    deleteList.add(tmp);
                }

                long batchstart= System.currentTimeMillis();

                if (deleteList.size() > 0){
                    logger.info("删除EBA-CEOS_TMP数据："+deleteList.size());
                    int size = deleteList.size();
                    int count = size % length != 0 ? size / length + 1 : size / length;
                    for (int i = 0; i < count; i++) {
                        List<CheckEbaCeosTmp> subList = deleteList.subList(i * length, ((1000 + i * length) < size ? (1000 + i * length) : size));
                        if (subList.size() > 0){
                            checkEbaCeosTmpMapper.deleteBatch(subList);
                        }
                    }
                }

                if (errAddList.size() > 0){
                    logger.info("新增EBA-CHECK-ERROE数据："+errAddList.size());
                    int size = errAddList.size();
                    int count = size % length != 0 ? size / length + 1 : size / length;
                    for (int i = 0; i < count; i++) {
                        List<CheckEbaCeosTmp> subList = errAddList.subList(i * length, ((1000 + i * length) < size ? (1000 + i * length) : size));
                        if (subList.size() > 0){
                            checkEbaCeosTmpMapper.insertBatchError(subList);
                        }
                    }
                }
                if (lackEawbList.size() > 0){
                    logger.info("线程:checkEbaTmp 新增缺失的eawbprintcode数据："+lackEawbList.size());
                    int size = lackEawbList.size();
                    int count = size % length != 0 ? size / length + 1 : size / length;
                    for (int i = 0; i < count; i++) {
                        List<String> subList = lackEawbList.subList(i * length, ((length + i * length) < size ? (length + i * length) : size));
                        if (subList.size() > 0){
                            checkEawbResultDetailMapper.insertBatchLackEawb(subList);
                        }
                    }
                }
                List<ReceiptRecordTmp> rrList = receiptRecordTmpList.stream().collect(
                        collectingAndThen(
                                toCollection(() -> new TreeSet<>(comparing(n->n.getEawbPrintcode()+";"+n.getRrName()+";"+n.getPrId()))),ArrayList::new)
                );
                if (rrList.size() > 0){
                    logger.info("新增计费数据："+rrList.size());
                    int size = rrList.size();
                    int count = size % length != 0 ? size / length + 1 : size / length;
                    for (int i = 0; i < count; i++) {
                        List<ReceiptRecordTmp> subList = rrList.subList(i * length, ((1000 + i * length) < size ? (1000 + i * length) : size));
                        if (subList.size() > 0){
                            recordTmpMapper.insertBatch(subList);
                        }
                    }
                }

                logger.info("线程"+queryParam.getPageNum()+"批量执行...end时效："+(System.currentTimeMillis()-batchstart));

            }

        } catch (Exception e){
            e.printStackTrace();
            MailUtil.postMail(MailConstant.MAIL_TO,MailConstant.MAIL_SUBJECT_INS_EBA,e.getMessage());
        }
        logger.info("线程"+queryParam.getPageNum()+"总用时===");


        return handleSize+"";
    }

    private boolean checkAllActivity(String eawbPrintCode,String eadCode,String eastCode){
        ActivityQuery param = new ActivityQuery();
        param.setEawbPrintcode(eawbPrintCode);
        param.setEadCode(eadCode);
        param.setEastCode(eastCode);

        if (activityMapper.countByAllCode(param) > 0){
            return true;
        }
//        if (activityMapper.countByAllCode_N(param) > 0){
//            return true;
//        }
        return false;
    }


    @Override
    public String handleEbaError(CeosQuery queryParam){
        logger.info("线程"+queryParam.getPageNum()+"开始查询===="+new Date());

        int handleSize = 0;

        try {

            PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize(),false);
            List<CheckEbaCeosTmp> recordTmpList = checkEbaCeosTmpMapper.listError();
            logger.info("线程"+queryParam.getPageNum()+"查询结束===="+new Date());

            if (recordTmpList != null){
                List<CheckEbaCeosTmp> errDeleteList = new ArrayList<>();
                List<CheckEbaCeosTmp> addList = new ArrayList<>();
                logger.info("线程"+queryParam.getPageNum()+"==recordTmpList.size=="+recordTmpList.size());

                for (CheckEbaCeosTmp tmp : recordTmpList){

                    String eawbPrintcode = tmp.getEawbPrintcode();
//                    logger.info("eawbPrintcode:"+eawbPrintcode);

                    ExpressAirWayBill bill = billMapper.selectByEawbPrintCode(eawbPrintcode);
                    if (bill == null ){
                        if (ceosEawbMapper.countCeosEawbByEawbPrintcode(eawbPrintcode) == 0){
                            errDeleteList.add(tmp);
                        }
                        continue;
                    }
                    addList.add(tmp);
                    errDeleteList.add(tmp);

                }

                long batchstart= System.currentTimeMillis();

                if (errDeleteList.size() > 0){
                    logger.info("删除EBA-CEOS_ERROR数据："+errDeleteList.size());
                    int size = errDeleteList.size();
                    int count = size % length != 0 ? size / length + 1 : size / length;
                    for (int i = 0; i < count; i++) {
                        List<CheckEbaCeosTmp> subList = errDeleteList.subList(i * length, ((1000 + i * length) < size ? (1000 + i * length) : size));
                        if (subList.size() > 0){
                            checkEbaCeosTmpMapper.deleteBatchError(subList);
                        }
                    }
                }

                if (addList.size() > 0){
                    logger.info("新增EBA-CHECK-TMP数据："+addList.size());
                    int size = addList.size();
                    int count = size % length != 0 ? size / length + 1 : size / length;
                    for (int i = 0; i < count; i++) {
                        List<CheckEbaCeosTmp> subList = addList.subList(i * length, ((1000 + i * length) < size ? (1000 + i * length) : size));
                        if (subList.size() > 0){
                            checkEbaCeosTmpMapper.insertBatch(subList);
                        }
                    }
                }
                logger.info("线程"+queryParam.getPageNum()+"批量执行...end时效："+(System.currentTimeMillis()-batchstart));

            }

        } catch (Exception e){
            e.printStackTrace();
            MailUtil.postMail(MailConstant.MAIL_TO,MailConstant.MAIL_SUBJECT_INS_EBA,e.getMessage());
        }
        logger.info("线程"+queryParam.getPageNum()+"总用时===");


        return handleSize+"";
    }

    @Override
    public String handleSynEba(int threadNum,List<ExpressBusinessActivity> activityList,List<CheckSynEbaConfig> configList){
        Date sysdate = new Date();

        try {
            logger.info("线程："+threadNum +" 集合大小："+activityList.size());
            List<String> eawbPrintCodes = new ArrayList<>();
            if (activityList != null){

                List<ExpressBusinessActivity> tmpList = new ArrayList<>();
                for (ExpressBusinessActivity activity : activityList){

                    String eawbPrintcode = activity.getEawbPrintcode();
                    String eadCode = activity.getEadCode();
                    String eastCode = activity.getEastCode();

                    if (CheckConstant.CN_SO_CODE.equals(activity.getSoCode())){
                        continue;
                    }
                    if (checkAllActivity(eawbPrintcode,eadCode,eastCode)){
                        continue;
                    }

                    activity.setEbaHandletime(activity.getEbaOccurtime()==null?activity.getEbaHandletime():activity.getEbaOccurtime());
                    activity.setQaPushtime(activity.getEbaOccurtime()==null?sysdate:activity.getEbaOccurtime());
                    tmpList.add(activity);

                }

                List<ExpressBusinessActivity> recordList = tmpList.stream().collect(
                        collectingAndThen(
                                toCollection(() -> new TreeSet<>(comparing(n->n.getEawbPrintcode()+";"+n.getEadCode()+";"+n.getEastCode()))),ArrayList::new)
                );

                List<CheckEbaCeosTmp> detailList = new ArrayList<>();
                List<CeosEawbEaVO> eawbEaList = new ArrayList<>();
                for (ExpressBusinessActivity activity : recordList){
                    if (StringUtil.isNotEmpty(activity.getEawbPrintcode())){
                        CheckEbaCeosTmp tmp = new CheckEbaCeosTmp();
                        tmp.setEbaHandletime(activity.getEbaOccurtime());
                        tmp.setEadCode(activity.getEadCode());
                        tmp.setEastCode(activity.getEastCode());
                        tmp.setEawbPrintcode(activity.getEawbPrintcode());
                        tmp.setCectHandletime(new Date());
                        detailList.add(tmp);

                        if (EastEnum.ASS.getEastCode().equals(activity.getEastCode())){
                            if (StringUtil.isEmpty(activity.getMawbCode())){
                                CeosEawbEaVO eaVO = ceosEawbEaMapper.selectCeosMawbActualByPrintcode(activity.getEawbPrintcode());
                                if (eaVO != null){
                                    eawbEaList.add(eaVO);
                                }
                            }
                        }
                    }
                }

//                if (detailList.size() > 0){
//                    logger.info("新增check_eba_ceos_tmp数据："+detailList.size());
//                    int dsize = detailList.size();
//                    int dcount = dsize % length != 0 ? dsize / length + 1 : dsize / length;
//                    for (int i = 0; i < dcount; i++) {
//                        List<CheckEbaCeosTmp> subList = detailList.subList(i * length, ((1000 + i * length) < dsize ? (1000 + i * length) : dsize));
//                        if (subList.size() > 0){
//                            checkEbaCeosTmpMapper.insertBatch(subList);
//                        }
//                    }
//                }

                long batchstart= System.currentTimeMillis();
                if (recordList.size() > 0){
                    logger.info("新增环节数据："+recordList.size());
                    int size = recordList.size();
                    int count = size % length != 0 ? size / length + 1 : size / length;
                    for (int i = 0; i < count; i++) {
                        List<ExpressBusinessActivity> subList = recordList.subList(i * length, ((1000 + i * length) < size ? (1000 + i * length) : size));
                        if (subList.size() > 0){
                            activityMapper.insertBatch(subList);
                        }
                    }
                }

                if (eawbEaList.size() > 0){
                    logger.info(" 更新Ceos-eawb-mawb数据："+eawbEaList.size());
                    int size = eawbEaList.size();
                    int count = size % length != 0 ? size / length + 1 : size / length;
                    for (int i = 0; i < count; i++) {
                        List<CeosEawbEaVO> subList = eawbEaList.subList(i * length, ((length + i * length) < size ? (length + i * length) : size));
                        if (subList.size() > 0){
                            billMapper.batchUpdatMawb(subList);
                        }
                    }
                }

                logger.info("线程"+threadNum+"批量执行...end时效："+(System.currentTimeMillis()-batchstart));

            }

        } catch (Exception e){
            logger.error("CheckInsEbaTimer异常"+e.getMessage());
            e.printStackTrace();
            MailUtil.postMail(MailConstant.MAIL_TO_ONE,MailConstant.MAIL_SUBJECT_INS_EBA,e.getMessage());
        } finally {

        }
        logger.info("线程"+(threadNum+"")+"总用时===");
        return "";
    }

}
