package com.sinoair.billing.service.check.impl;


import com.github.pagehelper.PageHelper;
import com.sinoair.billing.core.util.CheckUtil;
import com.sinoair.billing.core.util.DateUtil;
import com.sinoair.billing.dao.billing.CheckEawbResultDetailMapper;
import com.sinoair.billing.dao.billing.CheckEawbResultMapper;
import com.sinoair.billing.dao.billing.ExpressAirWayBillMapper;
import com.sinoair.billing.domain.constant.CheckConstant;
import com.sinoair.billing.domain.constant.EastEnum;
import com.sinoair.billing.domain.model.billing.CheckEawbResult;
import com.sinoair.billing.domain.model.billing.CheckEawbResultDetail;
import com.sinoair.billing.domain.model.billing.ExpressAirWayBill;
import com.sinoair.billing.domain.model.billing.PaymentRecord;
import com.sinoair.billing.domain.vo.check.EawbCheckVO;
import com.sinoair.billing.domain.vo.query.CeosQuery;
import com.sinoair.billing.service.check.EawbCheckDetailService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2018/1/30
 * @time: 9:59
 * @description: To change this template use File | Settings | File Templates.
 */
@Service("EawbCheckDetailService")
public class EawbCheckDetailServiceImpl implements EawbCheckDetailService {

    private Logger logger = LoggerFactory.getLogger(EawbCheckDetailServiceImpl.class);


    @Autowired
    private CheckEawbResultDetailMapper detailMapper;



    @Override
    public List<String> listLackEawb(CeosQuery queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize(),false);
        return detailMapper.listLackEawb();
    }


    @Override
    public List<PaymentRecord> selectPrByCondition(EawbCheckVO param){
        return detailMapper.selectPrByCondition(param);
    }
}
