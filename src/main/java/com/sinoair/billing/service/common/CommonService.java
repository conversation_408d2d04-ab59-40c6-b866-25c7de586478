package com.sinoair.billing.service.common;

import com.sinoair.billing.domain.model.billing.*;
import com.sinoair.billing.domain.vo.query.CeosQuery;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Auther: nanhb
 * @Date: 2020-03-04 14:00
 * @Description:
 */
public interface CommonService {


    Date getHandleTime(CheckEawbResultDetail detail);


    int countByMawbCode(String mawbCode);
    int countOtherByMawbCode(String mawbCode);

    List<String> selectMxxbKeyList();


    List<CheckSynEbaConfig> listSynEbaConfig(CheckSynEbaConfig record);

    List<ExpressBusinessActivity> selectCeosActivity(CeosQuery param);
    int selectCeosActivityCount(CeosQuery param);

    List<SettlementObject> selectAutoSoList();

    List<Supplier> selectAutoSpList();
    List<Supplier> selectPpSupplierList(CreditManifest creditManifest);

    List<ExpressBusinessActivity> selectCeosActivitySoCode(CeosQuery param);
    int countCeosActivitySoCode(CeosQuery param);

    List<String> selectFlatSoList();

    BigDecimal selectCeosActivityMax(CeosQuery query);
}
