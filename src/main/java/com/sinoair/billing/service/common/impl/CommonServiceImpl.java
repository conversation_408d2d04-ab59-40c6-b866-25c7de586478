package com.sinoair.billing.service.common.impl;

import com.github.pagehelper.PageHelper;
import com.sinoair.billing.core.util.CheckUtil;
import com.sinoair.billing.dao.billing.*;
import com.sinoair.billing.dao.ceos.CeosActivityMapper;
import com.sinoair.billing.domain.model.billing.*;
import com.sinoair.billing.domain.vo.query.ActivityQuery;
import com.sinoair.billing.domain.vo.query.CeosQuery;
import com.sinoair.billing.service.common.CommonService;
import com.sinoair.billing.service.temp.impl.TempRRServiceImpl;
import org.apache.ibatis.annotations.Param;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Auther: nanhb
 * @Date: 2020-03-04 14:00
 * @Description:
 */
@Service("CommonService")
public class CommonServiceImpl implements CommonService {

    private Logger logger = LoggerFactory.getLogger(CommonServiceImpl.class);

    @Autowired
    private ExpressBusinessActivityMapper activityMapper;

    @Autowired
    private CeosActivityMapper ceosActivityMapper;

    @Autowired
    private ExpressAirWayBillMapper billMapper;

    @Autowired
    private ExpressPropertyMapper expressPropertyMapper;

    @Autowired
    private CheckSynEbaConfigMapper ebaConfigMapper;

    @Autowired
    private SettlementObjectMapper settlementObjectMapper;

    @Autowired
    private SupplierMapper supplierMapper;

    @Autowired
    private PaymentRecordMapper paymentRecordMapper;


    @Override
    public Date getHandleTime(CheckEawbResultDetail detail){
        ActivityQuery param = new ActivityQuery();
        param.setEawbPrintcode(detail.getEawbPrintcode());
        param.setEadCode(detail.getEadCode());
        param.setEastCode(detail.getEastCode());
        List<ExpressBusinessActivity> activityList = activityMapper.selectTimeByAllCode(param);
        if (activityList != null && activityList.size() > 0){
            return activityList.get(0).getEbaHandletime();
        }
        return null;
    }

    @Override
    public int countByMawbCode(String mawbCode){
        return billMapper.countByMawbCode(mawbCode);
    }

    @Override
    public int countOtherByMawbCode(String mawbCode){
        return billMapper.countOtherByMawbCode(mawbCode);
    }

    @Override
    public List<String> selectMxxbKeyList() {
        return expressPropertyMapper.selectMxxbKeyList();
    }


    @Override
    public  List<CheckSynEbaConfig> listSynEbaConfig(CheckSynEbaConfig record){
        return ebaConfigMapper.listSynEbaConfig(record);
    }

    @Override
    public List<ExpressBusinessActivity> selectCeosActivity(CeosQuery param) {
        PageHelper.startPage(param.getPageNum(), param.getPageSize(),false);
        return ceosActivityMapper.selectCeosActivity(param);
    }

    @Override
    public int selectCeosActivityCount(CeosQuery param) {
        return ceosActivityMapper.selectCeosActivityCount(param);
    }

    @Override
    public List<SettlementObject> selectAutoSoList() {
        return settlementObjectMapper.selectAutoSoList();
    }

    @Override
    public List<Supplier> selectAutoSpList() {
        return supplierMapper.selectAutoSpList();
    }

    @Override
    public List<Supplier> selectPpSupplierList(CreditManifest creditManifest) {
        return paymentRecordMapper.selectPpSupplierList(creditManifest);
    }

    @Override
    public List<ExpressBusinessActivity> selectCeosActivitySoCode(CeosQuery param) {
        PageHelper.startPage(param.getPageNum(), param.getPageSize(),false);
        return ceosActivityMapper.selectCeosActivitySoCode(param);
    }

    @Override
    public int countCeosActivitySoCode(CeosQuery param) {
        return ceosActivityMapper.countCeosActivitySoCode(param);
    }

    @Override
    public List<String> selectFlatSoList() {
        return settlementObjectMapper.selectFlatSoList();
    }

    @Override
    public BigDecimal selectCeosActivityMax(CeosQuery query) {
        return ceosActivityMapper.selectCeosActivityMax(query);
    }
}
