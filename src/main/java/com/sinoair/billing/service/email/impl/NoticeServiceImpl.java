package com.sinoair.billing.service.email.impl;

import com.sinoair.billing.core.util.DateUtil;
import com.sinoair.billing.core.util.MailUtil;
import com.sinoair.billing.core.util.StringUtil;
import com.sinoair.billing.dao.billing.DebitManifestMapper;
import com.sinoair.billing.dao.billing.DebitOrderMapper;
import com.sinoair.billing.domain.constant.CheckConstant;
import com.sinoair.billing.domain.constant.MailConstant;
import com.sinoair.billing.domain.constant.OrderPoolConstant;
import com.sinoair.billing.domain.model.billing.DebitManifest;
import com.sinoair.billing.domain.model.billing.DebitOrder;
import com.sinoair.billing.service.email.NoticeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Auther: nanhb
 * @Date: 2020-03-17 10:02
 * @Description:
 */
@Service("NoticeService")
public class NoticeServiceImpl implements NoticeService {

    private Logger logger = LoggerFactory.getLogger(NoticeServiceImpl.class);

    @Autowired
    private DebitManifestMapper manifestMapper;

    @Autowired
    private DebitOrderMapper debitOrderMapper;

    @Override
    public String confirmBill(){
        //查询账单已确认【菜鸟】，但没有发邮件提醒的记录
        Map<String,Object> param = new HashMap<>();
        //当前只针对菜鸟
        param.put("soCode", CheckConstant.CN_SO_CODE);
        List<DebitManifest> manifestList = manifestMapper.listEmailStatus(param);
        if (manifestList == null || manifestList.size() == 0){
            logger.info("还没有账单已确认且未发邮件通知的数据");
            return "";
        }

        List<Integer> dmIds = manifestList.stream().map(DebitManifest::getDmId).collect(Collectors.toList());
        param.put("dmIds", dmIds);

        List<Map<String,Object>> billEmailList = manifestMapper.selectBillEmail(param);
        try {
            for (Map<String,Object> map : billEmailList){
                String billMonth = map.get("BILLMONTH")+"";
                Integer billCount = Integer.valueOf(map.get("BILLCOUNT")+"");
                Integer confirmCount = Integer.valueOf(map.get("CONFIRMCOUNT")+"");
                String subject = MailConstant.MAIL_BILL_SUBJECT+"【"+billMonth+"-菜鸟-账单总数："+billCount+",已确认："+confirmCount+"】";
                String body = "以上通知仅供参考提醒";

                MailUtil.postMail(MailConstant.MAIL_BILL_TO,subject,body);
            }
            manifestMapper.updateEmailStatusByDmIds(dmIds);
        } catch (Exception e){
            e.printStackTrace();
            MailUtil.postMail(MailConstant.MAIL_TO_ONE,MailConstant.MAIL_BILL_SUBJECT_ERROR,e.getMessage());
        }

        return "";
    }

    @Override
    public String monitorManifestDetail() {
        Date curDate = new Date();
        Map<String,Object> param = new HashMap<>();
        //当前只针对菜鸟
        param.put("soCode", CheckConstant.CN_SO_CODE);
        param.put("startRrOccurtime", CheckConstant.DEBIT_ORDER_DATE);
        List<DebitOrder> debitOrderList = debitOrderMapper.listDebitManifestDetail(param);
        String subject = MailConstant.MAIL_BILL_SUBJECT_ORDER;
        for (DebitOrder debitOrder : debitOrderList){
            if (StringUtil.isEmpty(debitOrder.getSysStatus())){
                String body = "账单【"+debitOrder.getDmId()+"】"+"未生成账单ORDER状态";
                MailUtil.postMail(MailConstant.MAIL_TO,subject,body);
                return null;
            }
            if (!OrderPoolConstant.DM_STATUS_FINISHED.equals(debitOrder.getSysStatus())){
                if (DateUtil.calculateDayDiff(debitOrder.getSysHandletime(),curDate) > 2){
                    String body = "账单【"+debitOrder.getDmId()+"】"+"生成BMS汇总明细超时啦";
                    MailUtil.postMail(MailConstant.MAIL_TO,subject,body);
                    return null;
                }
            }

        }
        return null;
    }
}
