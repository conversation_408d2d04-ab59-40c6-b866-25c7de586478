package com.sinoair.billing.service.manifest;

import com.sinoair.billing.domain.model.billing.SettlementObject;
import com.sinoair.billing.domain.model.billing.Supplier;

import java.util.List;

/**
 * @Auther: nanhb
 * @Date: 2020-03-18 14:36
 * @Description:
 */
public interface BmsManifestService {

    String handleEawbUpdateTime();

    String handleBmsManifest();

    String handleBmsCollectWeight();

    String handleBmsCollectWeight_old();

    String delInitManifest();

    String insertBmsManifestDetail();

    String insertBmsPayDetail();

    String handleDebitManifest();

    String handleCreditManifest();

    String handleBmsFbaCollectWeight();

    String handleDebitManifestFba();

    String handleCreditManifestFba();
}
