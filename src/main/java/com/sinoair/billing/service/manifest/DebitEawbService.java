package com.sinoair.billing.service.manifest;

import com.sinoair.billing.domain.model.billing.DebitEawb;
import com.sinoair.billing.domain.model.billing.DebitManifest;

/**
 * @Auther: nanhb
 * @Date: 2020-03-18 14:36
 * @Description:
 */
public interface DebitEawbService {

    String handleBmsManifestAmount(Long dmId);

    void insertByDmId(Long dmId);

    void insertBmsManifestAmount(Long dmId);

    void deleteAll();

    int countDebitEawb();

    DebitManifest selectDmById(Long dmId);

    void insertByDmTempId(Long dmId);
}
