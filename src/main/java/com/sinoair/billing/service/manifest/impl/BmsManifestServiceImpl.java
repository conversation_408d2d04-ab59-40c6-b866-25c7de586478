package com.sinoair.billing.service.manifest.impl;

import com.sinoair.billing.core.util.DateUtil;
import com.sinoair.billing.core.util.MailUtil;
import com.sinoair.billing.core.util.StringUtil;
import com.sinoair.billing.dao.billing.*;
import com.sinoair.billing.dao.ceosbi.EawbEadFlatMapper;
import com.sinoair.billing.domain.constant.*;
import com.sinoair.billing.domain.model.billing.*;
import com.sinoair.billing.domain.model.ceosbi.EawbEadFlat;
import com.sinoair.billing.domain.vo.manifest.RechargeDMListVO;
import com.sinoair.billing.domain.vo.query.CommonQuery;
import com.sinoair.billing.service.email.impl.NoticeServiceImpl;
import com.sinoair.billing.service.manifest.BmsManifestService;
import com.sinoair.billing.service.receipt.PaymentService;
import com.sinoair.billing.service.receipt.ReceiptService;
import com.sinoair.billing.service.receipt.SerialNOService;
import com.sun.javafx.collections.MappingChange;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Auther: nanhb
 * @Date: 2020-03-18 14:37
 * @Description:
 */
@Service("BmsManifestService")
public class BmsManifestServiceImpl implements BmsManifestService {

    private Logger logger = LoggerFactory.getLogger(BmsManifestServiceImpl.class);

    @Resource(name = "ManifestListMapper")
    private ManifestListMapper manifestListMapper;

    @Resource(name = "NationCountryMapper")
    private NationCountryMapper nationCountryMapper;

    @Resource(name = "BmsManifestMapper")
    private BmsManifestMapper bmsManifestMapper;

    @Resource(name = "ExpressPropertyMapper")
    private ExpressPropertyMapper propertyMapper;

    @Resource(name = "SettlementObjectMapper")
    private SettlementObjectMapper settlementObjectMapper;

    @Resource(name = "SinotransOrderPoolMapper")
    private SinotransOrderPoolMapper sinotransOrderPoolMapper;

    @Resource(name = "SinotransOrderWeightMapper")
    private SinotransOrderWeightMapper orderWeightMapper;

    @Resource(name = "ExpressAirWayBillMapper")
    private ExpressAirWayBillMapper expressAirWayBillMapper;

    @Resource(name = "DebitManifestMapper")
    private DebitManifestMapper debitManifestMapper;

    @Resource(name = "CreditManifestMapper")
    private CreditManifestMapper creditManifestMapper;

    @Resource(name = "BmsManifestDetailMapper")
    private BmsManifestDetailMapper bmsManifestDetailMapper;

    @Resource(name = "BmsRecDetailMapper")
    private BmsRecDetailMapper bmsRecDetailMapper;

    @Resource(name = "BmsRecHeadMapper")
    private BmsRecHeadMapper bmsRecHeadMapper;

    @Resource(name = "BmsPayDetailMapper")
    private BmsPayDetailMapper payDetailMapper;

    @Resource(name = "BmsRecDetailFbaMapper")
    private BmsRecDetailFbaMapper bmsRecDetailFbaMapper;

    @Resource(name = "BmsRecHeadFbaMapper")
    private BmsRecHeadFbaMapper bmsRecHeadFbaMapper;

    @Resource(name = "BmsPayDetailFbaMapper")
    private BmsPayDetailFbaMapper bmsPayDetailFbaMapper;

    @Autowired
    private PaymentRecordMapper paymentRecordMapper;

    @Autowired
    private BillingEawbEadFlatMapper eadFlatMapper;

    @Autowired
    private ReceiptRecordMapper receiptRecordMapper;

    @Autowired
    private InsTaskMapper insTaskMapper;
    @Autowired
    private ReceiptService receiptService;
    @Autowired
    private PaymentService paymentService;

    @Autowired
    private SerialNOService serialNOService;


    @Override
    public String handleEawbUpdateTime(){

        InsTask insTask = insTaskMapper.selectByTaskType(CheckConstant.SYN_ORDER_WEIGHT);
        String startDate = insTask.getTaskStartDate();
        String endDate = insTask.getTaskEndDate();


        String collectMonth = "";
        try {



            logger.info("开始日期："+startDate+" 结束日期: "+endDate);
            CommonQuery query = new CommonQuery();
//            query.setStrStartDate(startDate);
//            query.setStrEndDate(endDate);

            List<String> list = expressAirWayBillMapper.selectUpdateTimeNull(query);
            for (String s : list){
                Date updateTime = null;
                EawbEadFlat eadFlat = eadFlatMapper.selectByPrintcode(s);
                if (eadFlat == null || eadFlat.getFcInbound() == null){
                    List<ReceiptRecord> receiptRecordList = receiptRecordMapper.selectByEawbPrintcode(s);
                    if (receiptRecordList.size() > 0){
                        ReceiptRecord receiptRecord = receiptRecordList.get(0);
                        updateTime = receiptRecord.getRrOccurtime();
                    }else{
                        ExpressAirWayBill eawb=expressAirWayBillMapper.selectByEawbPrintCode(s);
                        updateTime = eawb.getEawbHandletime();
                    }
                }else{
                    updateTime = eadFlat.getFcInbound();
                }
                if (updateTime != null){
                    ExpressAirWayBill expressAirWayBill = new ExpressAirWayBill();
                    expressAirWayBill.setEawbPrintcode(s);
                    expressAirWayBill.setEawbUpdatetime(updateTime);
                    expressAirWayBillMapper.updateEawbTime(expressAirWayBill);
                }
            }

        } catch (Exception e){
            e.printStackTrace();
        }

        return "";
    }


    @Override
    public String handleBmsManifest(){

        List<ManifestList> manifestLists = manifestListMapper.listManifest();
        if (manifestLists.size() == 0){
            logger.info("没有要汇总的清单数据");
            return "";
        }
        List<ExpressProperty> epList = propertyMapper.selectServiceTypeList();
        List<NationCountry> ncList = nationCountryMapper.listAll();

        Date curDate = new Date();
        try {
            for (ManifestList manifestList : manifestLists){
                List<BmsManifest> bmsManifestList = null;
//                if (CheckConstant.RC_TYPE_R.equals(manifestList.getmRcType())){
                    bmsManifestList = manifestListMapper.selectCollectBmsList(manifestList.getmId());
//                }else{
//                    bmsManifestList = manifestListMapper.selectCreditCollectBmsList(manifestList.getmId());
//                }

                for (BmsManifest bmsManifest : bmsManifestList){
//                    String bmsNo = bmsManifestMapper.selectBmsBussnissSeq();
//                    String lsh = StringUtil.getIntFormString(6,bmsNo);
//                    String bCode = manifestList.getCompanyId()+manifestList.getBtCode()+ DateUtil.getYearMonth(curDate)+lsh;
                    bmsManifest.setmCode(manifestList.getmCode());  //结算单票号  分公司代码+进出口标识+年月+四位或者六位序列号
                    bmsManifest.setmId(manifestList.getmId());
                    bmsManifest.setCompanyId(manifestList.getCompanyId());
                    bmsManifest.setSoCode(manifestList.getSoCode());
                    String soName = manifestList.getSoName();
                    if (StringUtil.isEmpty(soName)){
//                        if (CheckConstant.RC_TYPE_R.equals(manifestList.getmRcType())){
                            soName = settlementObjectMapper.getsoNameBysoCode(manifestList.getSoCode());
//                        }else{
//                            soName = supplierMapper.getSpNameBySpCode(manifestList.getSoCode());
//                        }
                    }
                    if (soName.indexOf("-") > -1 && soName.indexOf("%") > -1){
                        bmsManifest.setSoName(soName);
                    }else {
                        bmsManifest.setSoName(manifestList.getSoTax()+"%"+"-"+soName);
                    }

                    bmsManifest.setCtSign(manifestList.getCtSign());
                    bmsManifest.setCtRate(manifestList.getCurrencyrate());
                    bmsManifest.setTaxRate(manifestList.getSoTax());
                    bmsManifest.setBmTotalfc(bmsManifest.getReceiptAmount());
                    //计算税金等
                    processCurrencyrate(bmsManifest);
//                bmsManifest.setBmTotalrmb(null); //预估本币金额
//                bmsManifest.setTaxAmount(null); //税金本币
//                bmsManifest.setNotaxAmount(null); //不含税本币
//                bmsManifest.setTaxAmountFc(null);//税金原币
//                bmsManifest.setNotaxAmountFc(null);  //不含税原币
                    bmsManifest.setCompletionDate(manifestList.getmConfirmtime());
                    bmsManifest.setIndentifierRc(manifestList.getmRcType());

                    bmsManifest.setBmHandleTime(curDate);
                    bmsManifest.setRemark(manifestList.getmRemark());
                    bmsManifest.setIeType(manifestList.getBtCode());
                    bmsManifest.setInvoiceType(manifestList.getInvoiceType().equals("E")?"增票":"普票");
                    bmsManifest.setInvoiceNum(manifestList.getInvoiceNum());
                    bmsManifest.setIkName(CheckConstant.IK_NAME);

                    bmsManifest.setBusinessType(ManifestConstant.BE);
                    bmsManifest.setBmType(ManifestConstant.BT_WPX);

                    bmsManifestMapper.insertSelective(bmsManifest);


                }
                manifestList.setIsBmsCollect("Y");
                manifestListMapper.updateByPrimaryKeySelective(manifestList);
            }
        } catch (Exception e){
            e.printStackTrace();
            logger.error("汇总清单数据异常:"+e.getMessage());
            MailUtil.postMail(MailConstant.MAIL_TO_ONE,MailConstant.MAIL_SUBJECT_BMS,e.getMessage());
        }


        return "";
    }

    /**
     * 1 查询expressairwaybill  eawb_updatetime时间范围取自INS_TASK 任务表，
     * 2 按eawb_so_code,eawb_servicetype_original进行分组，汇总统计值，
     * 3 sinotrans_id规则 公司代码+年月日+soCode/********
     * 4 insert bms_rec_head
     * 5 update expressairwaybill set sinotrans_id
     * @return
     */
    @Override
    public String handleBmsCollectWeight(){
        try {

            List<SinotransOrderWeight> orderWeightList= expressAirWayBillMapper.selectOrderWeightCollect();
            if (orderWeightList.size() == 0){
                logger.info("没有要汇总重量的数据");
                return "";
            }
                for (SinotransOrderWeight orderWeight : orderWeightList){
                    //sinotrans_id规则，公司代码+年月日+soCode/********
                    String sinotransId ="";
                    if (CheckConstant.ON_LINE.equals(orderWeight.getSoVendorType())&&
                            (CheckConstant.SO_MODE_RECHARGE.equals(orderWeight.getSoMode())
                                    ||CheckConstant.SO_MODE_CASH.equals(orderWeight.getSoMode())
                                    ||orderWeight.getCustCode()==null
                                    ||"".equals(orderWeight.getCustCode()))){//如果是线上预充值的客户，默认cdh码是********
                        sinotransId = orderWeight.getSacId()+orderWeight.getCollectMonth()+CheckConstant.XJ_SO_CODE;
                        if(CheckConstant.APP_SO.equals(orderWeight.getSoCode())){
                            sinotransId = sinotransId+"APP";
                        }
                    }else{
                        sinotransId = orderWeight.getSacId()+orderWeight.getCollectMonth()+orderWeight.getCustCode();
                    }
//                    sinotransId = sinotransId + (i+"");
                    orderWeight.setSinotransId(sinotransId);

                    //insert bms_rec_head
                    bmsRecHeadMapper.insertSelective(orderWeight);
                    CommonQuery query = new CommonQuery();
                    query.setStrStartDate(String.valueOf(orderWeight.getCollectMonth()));
                    query.setStrEndDate(String.valueOf(orderWeight.getEndDay()));
                    query.setSoCode(orderWeight.getSoCode());
                    query.setEpKey(orderWeight.getEpKey());
                    query.setSinotransId(sinotransId);
                    //更新expressairwaybill   sinotrans_id
                    expressAirWayBillMapper.updateSinotransIdBySo(query);
                }
        } catch (Exception e){
            e.printStackTrace();
        }

        return "";
    }

    @Override
    public String handleBmsCollectWeight_old(){

        String curDate = DateUtil.getCurDate();
        int curDay = DateUtil.getDay(new Date());
        String startDate = null;
        String endDate = null;
        String collectMonth = "";
        try {
            //1号
            if (curDay == 1){
                //开始日期为上月1号
                startDate = DateUtil.date2str(DateUtil.getLastMonthFirstDay(),DateUtil.YYYYMMDD);
                //汇总月为上月
                collectMonth = DateUtil.getYearMonth(DateUtil.getLastMonthFirstDay());
            }else{
                //开始日期为本月1号
                startDate = DateUtil.date2str(DateUtil.getThisMonthFirstDay(),DateUtil.YYYYMMDD);
                //汇总月为本月
                collectMonth = DateUtil.getYearMonth(DateUtil.getThisMonthFirstDay());
            }
            endDate =curDate;

            //todo 临时
//            startDate = "20200401";
//            endDate = "20200501";
//            collectMonth = "202004";

            logger.info("开始日期："+startDate+" 结束日期: "+endDate);
            CommonQuery query = new CommonQuery();
            query.setStrStartDate(startDate);
            query.setStrEndDate(endDate);
//            query.setStrStartDate("20161101");
//            query.setStrEndDate("20161130");

            List<SinotransOrderWeight> orderWeightList= sinotransOrderPoolMapper.selectOrderWeightCollect(query);
            if (orderWeightList.size() == 0){
                logger.info("没有要汇总重量的数据");
                return "";
            }
            bmsRecHeadMapper.deleteByMonth(Integer.valueOf(collectMonth));
            for (SinotransOrderWeight orderWeight : orderWeightList){
                orderWeight.setCollectMonth(Integer.valueOf(collectMonth));
                if (StringUtil.isNotEmpty(orderWeight.getEpKey())){
                    bmsRecHeadMapper.insertSelective(orderWeight);
                }
            }
        } catch (Exception e){
            e.printStackTrace();
        }

        return "";
    }

    private String getEpValue(String epKey,List<ExpressProperty> epList){
        Optional<ExpressProperty> optional = epList.stream().filter(property -> property.getEpKey().equals(epKey)).findFirst();
        if (optional.isPresent()) {
            // 存在
            ExpressProperty expressProperty =  optional.get();
            return expressProperty.getEpValue();
        } else {
            // 不存在
        }
        return "";
    }

    private NationCountry getNcCode(String code,List<NationCountry> ncList){
        Optional<NationCountry> optional = ncList.stream().filter(property -> property.getNc2code().equals(code)).findFirst();
        if (optional.isPresent()) {
            // 存在
            NationCountry nc =  optional.get();
            return nc;
        } else {
            // 不存在
        }
        return null;
    }

    private void processCurrencyrate(BmsManifest bmsManifest){
        BigDecimal ctRate = bmsManifest.getCtRate(); //汇率
        BigDecimal taxRate = bmsManifest.getTaxRate(); //税率
        BigDecimal totalFc = bmsManifest.getBmTotalfc();  //原币金额
        BigDecimal totalRmb = null;  //预估本币金额
        BigDecimal taxAmount = null; //税金本币
        BigDecimal notaxAmount = null; //不含税本币
        BigDecimal taxAmountFc = null; //税金原币
        BigDecimal notaxAmountFc = null; //不含税原币

        /**
         * 预估本币金额(人民币) totalRmb = 原币金额 * 汇率
         * 税金原币 taxAmountFc = 原币金额 * 税率
         * 税金本币（人民币）taxAmount  =  原币金额*汇率*税率
         * 不含税本币（人民币） notaxAmount = 本币金额 - 税金本币
         * 不含税原币 notaxAmountFc =原币金额 - 税金原币
         */
        if (ctRate != null){
            totalRmb = totalFc.multiply(ctRate).setScale(2);
        }

        if (taxRate != null){
            taxAmountFc = totalFc.multiply(taxRate).setScale(2);
            notaxAmountFc = totalFc.subtract(taxAmountFc);
            if (ctRate != null){
                taxAmount = totalFc.multiply(ctRate).multiply(taxRate).setScale(2);
                notaxAmount = totalRmb.subtract(taxAmount);
            }
        }else{
            notaxAmountFc = totalFc;
            if (ctRate != null){
                notaxAmount = totalRmb;
            }
        }
        bmsManifest.setBmTotalrmb(totalRmb);
        bmsManifest.setTaxAmount(taxAmount);
        bmsManifest.setNotaxAmount(notaxAmount);
        bmsManifest.setTaxAmountFc(taxAmountFc);
        bmsManifest.setNotaxAmountFc(notaxAmountFc);

    }

    @Override
    public String delInitManifest(){
        manifestListMapper.deleteByInit();
        return "";
    }

    @Override
    public String insertBmsManifestDetail() {
        List<BmsManifestDetail> detailList = debitManifestMapper.getBmsDetailAll();
        logger.info("开始生成BmsManifestDetail："+detailList.size()+"个");
        for (BmsManifestDetail detail : detailList){
            detail.setBmdHandletime(new Date());
            bmsRecDetailMapper.insertSelective(detail);
            Map map = new HashMap();
            map.put("eawbPrintcode", detail.getEawbPrintcode());
            map.put("sacId", String.valueOf(detail.getSacId()));
            map.put("ctCode", String.valueOf(detail.getCtCode()));
            map.put("soCode", String.valueOf(detail.getSoCode()));
            map.put("rrUserId", 1);
            map.put("rrHandletime", DateUtil.getNowDateTime());
            map.put("day",detail.getDmMonth());
            map.put("sinotransId",detail.getSinotransId());
            map.put("epKey",detail.getEpKey());
            map.put("bmdSyscode",detail.getBmdSyscode());
            map.put("dmId",detail.getDmId());
            receiptRecordMapper.updateBmdSyscode(map);
            //更新总金额，防止数据不一致
            bmsRecDetailMapper.updateAmount(detail.getBmdSyscode());
        }
        return "";
    }

    @Override
    public String insertBmsPayDetail() {
        List<BmsPayDetail> detailList = creditManifestMapper.getBmsPayAll();
        for (BmsPayDetail detail : detailList){
            detail.setBpdHandletime(new Date());
            payDetailMapper.insertSelective(detail);
            Map map = new HashMap();
            map.put("sacId", String.valueOf(detail.getSacId()));
            map.put("ctCode", String.valueOf(detail.getCtCode()));
            map.put("soCode", detail.getSoCode());
            map.put("rrUserId", 1);
            map.put("rrHandletime", DateUtil.getNowDateTime());
            map.put("day",String.valueOf(detail.getCmMonth()));
            map.put("sinotransId",detail.getSinotransId());
            map.put("epKey",detail.getEpKey());
            map.put("cmId",detail.getCmId());
            map.put("bpdSyscode",detail.getBpdSyscode());
            paymentRecordMapper.updateBpdSyscode(map);
            //更新总金额，防止数据不一致
            payDetailMapper.updateAmount(detail.getBpdSyscode());
        }
        return "";
    }

    /**
     * 1 查询所有报价的结算对象
     * 2 生成debit_manifest反馈dm_id
     * 3 分组汇总，insert bms_rec_detail
     * @return
     */
    @Override
    public String handleDebitManifest() {

        this.insertBmsManifestDetail();


        List<RechargeDMListVO> dmListVOS=receiptRecordMapper.getReceiptRecordForDm();
        logger.info("开始生成账单："+dmListVOS.size()+"个");

        for (RechargeDMListVO dmListVO :dmListVOS){
                DebitManifest debitManifest = new DebitManifest();
                debitManifest.setDmUserId(1);
                debitManifest.setCompanyId("ZSU");
                debitManifest.setSoCode(dmListVO.getSoCode());
                debitManifest.setDmStatus("PAYED");

                debitManifest.setCtCode(dmListVO.getCtCode()); //人民币
                debitManifest.setSoMode("RECHARGE");

                debitManifest.setDmType("E");
                debitManifest.setDmCreateTime(DateUtil.getNowDateTime());
                debitManifest.setDmHandleTime(DateUtil.getNowDateTime());
                try {
                    debitManifest.setDmStartTime(DateUtil.str2date(dmListVO.getStartTime(), DateUtil.YYYYMMDD));
                    debitManifest.setDmEndTime(DateUtil.str2date(dmListVO.getEndTime(), DateUtil.YYYYMMDD));
                } catch (ParseException e) {
                    e.printStackTrace();
                }

                debitManifest.setDmDivideStatus("N");
                //生成应收账单
                Integer dmId = receiptService.insertDebitManifest(debitManifest);

        }

        //insert bms_rec_detail
//
//        insTask.setTaskStatus(ReceiptRecordConstant.TASK_PENDING);
//
////            insTask.setTaskStartDate(startDate);
////            insTask.setTaskEndDate(endDate);
//        insTask.setTaskStartDate(endDate);
//        try {
//            insTask.setTaskEndDate(DateUtil.getPastDayByDate(DateUtil.str2date(endDate,DateUtil.YYYYMMDD),1));
//        } catch (ParseException e) {
//            e.printStackTrace();
//        }
//        logger.info("下次开始日期："+insTask.getTaskStartDate());
//
//        insTask.setTaskRemark(startDate+" 监控完成");
//        insTask.setTaskHandletime(new Date());
//        insTaskMapper.updateByPrimaryKeySelective(insTask);

        return "";
    }

    /**
     * 1 获取所有报价供应商
     * 2 生成应付账单 credit_manifest
     * 3 insert bms_pay_detail
     * @return
     */
    @Override
    public String handleCreditManifest() {
        this.insertBmsPayDetail();

        return "";
    }


    @Override
    public String handleBmsFbaCollectWeight(){

        InsTask insTask = insTaskMapper.selectByTaskType(CheckConstant.SYN_ORDER_WEIGHT_FBA);
        String startDate = insTask.getTaskStartDate();
        String endDate = insTask.getTaskEndDate();

        String collectMonth = "";
        try {

            logger.info("开始日期："+startDate+" 结束日期: "+endDate);
            CommonQuery query = new CommonQuery();
            query.setStrStartDate(startDate);
            query.setStrEndDate(endDate);

            List<BmsRecHeadFba> orderWeightList= expressAirWayBillMapper.selectOrderWeightCollectFba(query);
            if (orderWeightList.size() == 0){
                logger.info("没有要汇总重量的数据");
                insTask.setTaskStartDate(endDate);
                insTask.setTaskEndDate(DateUtil.getPastDayByDate(DateUtil.str2date(endDate,DateUtil.YYYYMMDD),1));
                logger.info("下次开始编号："+insTask.getTaskStartDate());

                insTask.setTaskRemark(startDate+" 监控完成");
                insTask.setTaskHandletime(new Date());
                insTaskMapper.updateByPrimaryKeySelective(insTask);
                return "";
            }

            for (BmsRecHeadFba recHeadFba : orderWeightList){

                bmsRecHeadFbaMapper.insertSelective(recHeadFba);
                ExpressAirWayBill expressAirWayBill = new ExpressAirWayBill();

                expressAirWayBill.setEawbPrintcode(recHeadFba.getSinotransId());
                expressAirWayBill.setSinotransId(recHeadFba.getSinotransId());
                expressAirWayBillMapper.updateSinotransId(expressAirWayBill);
            }

            insTask.setTaskStartDate(endDate);
            insTask.setTaskEndDate(DateUtil.getPastDayByDate(DateUtil.str2date(endDate,DateUtil.YYYYMMDD),1));
            logger.info("下次开始编号："+insTask.getTaskStartDate());

            insTask.setTaskRemark(startDate+" 监控完成");
            insTask.setTaskHandletime(new Date());
            insTaskMapper.updateByPrimaryKeySelective(insTask);

        } catch (Exception e){
            e.printStackTrace();
        }

        return "";
    }

    @Override
    @Transactional
    public String handleDebitManifestFba() {

        InsTask insTask = insTaskMapper.selectByTaskType(CheckConstant.SYN_DEBIT_MANIFEST_FBA);
        String startDate = insTask.getTaskStartDate();
        String endDate = insTask.getTaskEndDate();
        Date curDate = DateUtil.getNowDateTime();
        if (StringUtil.isEmpty(startDate)){
            startDate = DateUtil.getCurDate();
            try {
                endDate = DateUtil.getPastDayByDate(DateUtil.str2date(startDate,DateUtil.YYYYMMDD),1);
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }

        CommonQuery param = new CommonQuery();
        param.setStrStartDate(startDate);
        param.setStrEndDate(endDate);

        List<BmsRecDetailFba> recDetailFbaList = receiptRecordMapper.getBmsManifestFba(param);

        for (BmsRecDetailFba detailFba :recDetailFbaList){
            DebitManifest debitManifest = new DebitManifest();
            debitManifest.setDmUserId(1);
//            debitManifest.setCompanyId(so.getSacId());
            debitManifest.setCompanyId("ZSU");
            debitManifest.setSoCode(detailFba.getSoCode());
            debitManifest.setDmStatus("CONFIRM");

            debitManifest.setCtCode(detailFba.getCtSign()); //人民币
            debitManifest.setSoMode("RECHARGE");

            debitManifest.setDmType("E");
            debitManifest.setDmCreateTime(DateUtil.getNowDateTime());
            debitManifest.setDmHandleTime(DateUtil.getNowDateTime());
            try {
                debitManifest.setDmStartTime(DateUtil.str2date(startDate,DateUtil.YYYYMMDD));
                debitManifest.setDmEndTime(DateUtil.str2date(endDate,DateUtil.YYYYMMDD));
            } catch (ParseException e) {
                e.printStackTrace();
            }

            debitManifest.setDmDivideStatus("N");
            SerialNO serialNO = new SerialNO();
            serialNO.setSnPurpose(SerialNO.SNPURPOSE_DM);
            BigDecimal serialNumber = serialNOService.selectCurrentNo(serialNO,1);
            String currentNO = DateUtil.getYearMonthDay() + String.format("%04d",serialNumber.intValue());
            String dmCode= debitManifest.getCompanyId() + currentNO + "BD"+CheckConstant.EAWBIETYPE_E;

            debitManifest.setDmCode(dmCode);
            debitManifest.setDmPlanAmount(detailFba.getReceiptAmount());//应收金额
            debitManifest.setDmTotalfc(detailFba.getReceiptAmount());//实收金额
            debitManifest.setDmTotalPieces(1);
            debitManifestMapper.insertSelective(debitManifest);

            ReceiptRecord receiptRecord = new ReceiptRecord();
            receiptRecord.setEawbPrintcode(detailFba.getEawbPrintcode());
            receiptRecord.setDmId(debitManifest.getDmId());
            receiptRecordMapper.updateByPrintcode(receiptRecord);
        }

        insTask.setTaskStatus(ReceiptRecordConstant.TASK_PENDING);

        insTask.setTaskStartDate(endDate);
        try {
            insTask.setTaskEndDate(DateUtil.getPastDayByDate(DateUtil.str2date(endDate,DateUtil.YYYYMMDD),1));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        logger.info("下次开始日期："+insTask.getTaskStartDate());

        insTask.setTaskRemark(startDate+" 监控完成");
        insTask.setTaskHandletime(new Date());
        insTaskMapper.updateByPrimaryKeySelective(insTask);

        return "";
    }

    @Override
    public String handleCreditManifestFba() {
        return null;
    }
}
