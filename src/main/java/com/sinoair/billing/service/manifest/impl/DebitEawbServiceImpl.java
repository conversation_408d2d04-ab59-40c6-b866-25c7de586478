package com.sinoair.billing.service.manifest.impl;

import com.sinoair.billing.core.util.DateUtil;
import com.sinoair.billing.core.util.MailUtil;
import com.sinoair.billing.dao.billing.*;
import com.sinoair.billing.domain.constant.CheckConstant;
import com.sinoair.billing.domain.constant.MailConstant;
import com.sinoair.billing.domain.constant.ReceiptRecordConstant;
import com.sinoair.billing.domain.model.billing.*;
import com.sinoair.billing.service.manifest.DebitEawbService;
import com.sinoair.billing.service.manifest.DebitServicetypeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @Auther: nanhb
 * @Date: 2020-03-18 14:37
 * @Description:
 */
@Service("DebitEawbService")
public class DebitEawbServiceImpl implements DebitEawbService {

    private Logger logger = LoggerFactory.getLogger(DebitEawbServiceImpl.class);

    @Resource(name = "ManifestListMapper")
    private ManifestListMapper manifestListMapper;

    @Resource(name = "NationCountryMapper")
    private NationCountryMapper nationCountryMapper;

    @Resource(name = "BmsManifestMapper")
    private BmsManifestMapper bmsManifestMapper;

    @Resource(name = "ExpressPropertyMapper")
    private ExpressPropertyMapper propertyMapper;

    @Resource(name = "DebitManifestMapper")
    private DebitManifestMapper debitManifestMapper;

    @Resource(name = "DebitEawbMapper")
    private DebitEawbMapper debitEawbMapper;

    @Resource(name = "BmsManifestDetailMapper")
    private BmsManifestDetailMapper bmsManifestDetailMapper;

    @Resource(name = "CurrencyTypeMapper")
    private CurrencyTypeMapper currencyTypeMapper;

    @Resource(name = "DebitManifestTemporaryMapper")
    private DebitManifestTemporaryMapper manifestTemporaryMapper;

    @Resource(name = "BmsRecDetailMapper")
    private BmsRecDetailMapper bmsRecDetailMapper;

    @Override
    public void insertByDmId(Long dmId){
        logger.info("生成明细。。。开始");
        DebitManifest debitManifest = debitManifestMapper.selectByPrimaryKey(dmId.intValue());
        if (ReceiptRecordConstant.CAINIAO_SO_CODE.equals(debitManifest.getSoCode())){
            //账单来源于临时账单汇总
            if ("T".equals(debitManifest.getDmDirty())){
//                debitEawbMapper.insertCNTemporaryByDmId(dmId);
            }else{
                debitEawbMapper.insertCNByDmId(dmId);
            }
        }else{
            debitEawbMapper.insertByDmId(dmId);
        }

    }

    @Override
    public void insertByDmTempId(Long dmTempId) {
        debitEawbMapper.insertCNTemporaryByTempId(dmTempId);
        DebitManifestTemporary manifestTemporary = new DebitManifestTemporary();
        manifestTemporary.setIsDebiteawb("Y");
        manifestTemporary.setDmTempId(dmTempId.intValue());
        manifestTemporaryMapper.updateByPrimaryKeySelective(manifestTemporary);
    }

    @Override
    public void insertBmsManifestAmount(Long dmId){
        logger.info("汇总明细。。。开始");
        DebitManifest dm = debitManifestMapper.selectByPrimaryKey(dmId.intValue());
        CurrencyType ct = currencyTypeMapper.selectByPrimaryKey(dm.getCtCode());
        Integer dmMonth = Integer.valueOf(DateUtil.getYearMonth(dm.getDmCreateTime()));

        DebitEawb eawbParam = new DebitEawb();
        eawbParam.setDmId(dmId);
        List<BmsManifestDetail> detailList = debitEawbMapper.selectBmsManifestAmount(eawbParam);
        logger.info("汇总明细。。。大小："+detailList.size());
        for (BmsManifestDetail detail : detailList){
            detail.setDmId(eawbParam.getDmId().intValue());
            detail.setBmdHandletime(new Date());
            detail.setSoCode(dm.getSoCode());
            detail.setSacId(dm.getCompanyId());
            detail.setCtSign(ct.getCtSign());
            detail.setDmMonth(dmMonth);
            bmsRecDetailMapper.insertSelective(detail);
        }
    }

    @Override
    public void deleteAll(){
        logger.info("删除明细。。。开始");
        debitEawbMapper.truncateTable();
        logger.info("明细任务。。。完成");
    }


    @Override
    @Transactional
    public String handleBmsManifestAmount(Long dmId){



//        DebitServicetype param = new DebitServicetype();
//        param.setStatusEawb(CheckConstant.STATUS_Y);
//        param.setStatusWeight(CheckConstant.STATUS_N);
//
//        Date curDate = new Date();
        try {
            logger.info("生成明细。。。开始");
            debitEawbMapper.insertByDmId(dmId);

//            DebitManifest debitManifest = debitManifestMapper.selectByPrimaryKey(dmId.intValue());

//            DebitServicetype typeParam = new DebitServicetype();
//            typeParam.setDmId(dmId.intValue());

//            List<DebitServicetype> debitServicetypeList = debitServicetypeMapper.selectByStatus(typeParam);
//            if (debitServicetypeList.size() == 0){
//
//                DebitEawb eawbParam = new DebitEawb();
//                eawbParam.setDmId(dmId);
////                eawbParam.setDmStartTime(debitManifest.getDmStartTime());
////                eawbParam.setDmEndTime(debitManifest.getDmEndTime());
//                List<BmsManifestDetail> detailList = debitEawbMapper.selectBmsManifestAmount(eawbParam);
//                for (BmsManifestDetail detail : detailList){
//                    detail.setDmId(dmId.intValue());
//                    detail.setBmdHandletime(new Date());
//                    bmsManifestDetailMapper.insertSelective(detail);
//                }
//            }else{
//                for (DebitServicetype debitServicetype : debitServicetypeList){
//                    DebitEawb eawbParam = new DebitEawb();
//                    eawbParam.setDmId(dmId);
//                    eawbParam.setEpKey(debitServicetype.getEpKey());
////                    eawbParam.setDmStartTime(debitManifest.getDmStartTime());
////                    eawbParam.setDmEndTime(debitManifest.getDmEndTime());
//                    List<BmsManifestDetail> detailList = debitEawbMapper.selectBmsManifestAmount(eawbParam);
//                    for (BmsManifestDetail detail : detailList){
//                        detail.setDmId(dmId.intValue());
//                        detail.setEpKey(debitServicetype.getEpKey());
//                        detail.setBmdHandletime(new Date());
//                        bmsManifestDetailMapper.insertSelective(detail);
//                    }
//                }
//            }
            logger.info("汇总明细。。。开始");
            DebitEawb eawbParam = new DebitEawb();
            eawbParam.setDmId(dmId);
            List<BmsManifestDetail> detailList = debitEawbMapper.selectBmsManifestAmount(eawbParam);
            logger.info("汇总明细。。。大小："+detailList.size());
            for (BmsManifestDetail detail : detailList){
                detail.setDmId(dmId.intValue());
                detail.setBmdHandletime(new Date());
                bmsRecDetailMapper.insertSelective(detail);
            }
            logger.info("删除明细。。。开始");
            debitEawbMapper.truncateTable();
            logger.info("明细任务。。。完成");

        } catch (Exception e){
            e.printStackTrace();
            logger.error("汇总清单明细数据异常:"+e.getMessage());
            MailUtil.postMail(MailConstant.MAIL_TO_ONE,MailConstant.MAIL_SUBJECT_BMS,e.getMessage());
        }


        return "";
    }

    @Override
    public int countDebitEawb() {
        return debitEawbMapper.countDebitEawb();
    }

    @Override
    public DebitManifest selectDmById(Long dmId){
        return debitManifestMapper.selectByPrimaryKey(dmId.intValue());
    }
}
