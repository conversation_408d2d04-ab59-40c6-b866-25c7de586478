package com.sinoair.billing.service.manifest.impl;

import com.sinoair.billing.core.util.MailUtil;
import com.sinoair.billing.core.util.StringUtil;
import com.sinoair.billing.dao.billing.*;
import com.sinoair.billing.domain.constant.CheckConstant;
import com.sinoair.billing.domain.constant.MailConstant;
import com.sinoair.billing.domain.constant.ManifestConstant;
import com.sinoair.billing.domain.model.billing.*;
import com.sinoair.billing.service.manifest.BmsManifestService;
import com.sinoair.billing.service.manifest.DebitServicetypeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * @Auther: nanhb
 * @Date: 2020-03-18 14:37
 * @Description:
 */
@Service("DebitServicetypeService")
public class DebitServicetypeServiceImpl implements DebitServicetypeService {

    private Logger logger = LoggerFactory.getLogger(DebitServicetypeServiceImpl.class);

    @Resource(name = "ManifestListMapper")
    private ManifestListMapper manifestListMapper;

    @Resource(name = "NationCountryMapper")
    private NationCountryMapper nationCountryMapper;

    @Resource(name = "BmsManifestMapper")
    private BmsManifestMapper bmsManifestMapper;

    @Resource(name = "ExpressPropertyMapper")
    private ExpressPropertyMapper propertyMapper;

    @Resource(name = "SettlementObjectMapper")
    private SettlementObjectMapper settlementObjectMapper;

    @Resource(name = "DebitEawbMapper")
    private DebitEawbMapper debitEawbMapper;

    @Resource(name = "BmsManifestDetailMapper")
    private BmsManifestDetailMapper bmsManifestDetailMapper;

    @Resource(name = "DebitServicetypeMapper")
    private DebitServicetypeMapper debitServicetypeMapper;

    @Resource(name = "BmsRecDetailMapper")
    private BmsRecDetailMapper bmsRecDetailMapper;


    @Override
    public String handleBmsManifestWeight(){

        DebitServicetype param = new DebitServicetype();
        param.setStatusEawb(CheckConstant.STATUS_Y);
        param.setStatusWeight(CheckConstant.STATUS_N);

        List<DebitServicetype> debitServicetypeList = debitServicetypeMapper.selectByStatus(param);
        if (debitServicetypeList.size() == 0){
            logger.info("没有要汇总的清单数据");
            return "";
        }
        List<ExpressProperty> epList = propertyMapper.selectServiceTypeList();
        List<NationCountry> ncList = nationCountryMapper.listAll();

        Date curDate = new Date();
        try {
            for (DebitServicetype debitServicetype : debitServicetypeList){
                List<BmsManifestDetail> detailList = debitServicetypeMapper.selectBmsManifestWeight(debitServicetype);
                for (BmsManifestDetail detail : detailList){
                    detail.setDmId(debitServicetype.getDmId());
                    detail.setEpKey(debitServicetype.getEpKey());
                    detail.setBmdHandletime(new Date());
                    bmsRecDetailMapper.insertSelective(detail);


                }
                debitServicetype.setStatusWeight(CheckConstant.STATUS_Y);
                debitServicetypeMapper.updateByPrimaryKeySelective(debitServicetype);
            }
        } catch (Exception e){
            e.printStackTrace();
            logger.error("汇总清单数据异常:"+e.getMessage());
            MailUtil.postMail(MailConstant.MAIL_TO_ONE,MailConstant.MAIL_SUBJECT_BMS,e.getMessage());
        }


        return "";
    }

    @Override
    public String handleBmsManifestAmount(){
        return "";
    }


}
