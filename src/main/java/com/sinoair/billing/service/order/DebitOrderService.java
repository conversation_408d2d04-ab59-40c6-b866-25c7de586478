package com.sinoair.billing.service.order;

import com.sinoair.billing.domain.model.billing.DebitOrder;
import com.sinoair.billing.domain.model.billing.SettlementObject;
import com.sinoair.billing.domain.model.billing.SinotransOrderPool;
import com.sinoair.billing.domain.vo.check.EawbCheckVO;

import java.util.List;

/**
 * @Auther: nanhb
 * @Date: 2020/3/24 11:28
 * @Description:
 */
public interface DebitOrderService {

    DebitOrder selectByPrimaryKey(Long dmId);

    int updateByPrimaryKeySelective(DebitOrder record);

    Long getPendingDebitOrder();

    DebitOrder getDebitOrder(String sysStatus);

    int countDebitOrder(String sysStatus);

    String selectSinotransIdSeq();

}
