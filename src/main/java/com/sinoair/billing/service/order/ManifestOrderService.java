package com.sinoair.billing.service.order;

import com.sinoair.billing.domain.model.billing.ManifestOrder;

/**
 * @Auther: nanhb
 * @Date: 2020/3/24 11:28
 * @Description:
 */
public interface ManifestOrderService {

    ManifestOrder selectByPrimaryKey(Long dmId);

    int updateByPrimaryKeySelective(ManifestOrder record);

    Long getPendingDebitOrder();

    ManifestOrder getManifestOrder(String sysStatus);

    int countManifestOrder(String sysStatus);

    String selectSinotransIdSeq();

}
