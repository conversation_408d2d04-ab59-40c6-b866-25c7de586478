package com.sinoair.billing.service.order;

import com.sinoair.billing.domain.model.billing.*;
import com.sinoair.billing.domain.vo.check.EawbCheckVO;

import java.util.List;

/**
 * @Auther: nanhb
 * @Date: 2020/3/24 11:28
 * @Description:
 */
public interface OrderPoolService {

    String handleAcceptedOrder(EawbCheckVO param,List<SettlementObject> soList);

    String handleBalanceOrder(int threadNum,int pageSize,List<ManifestEawb> resultList);

    String handleDebitEawb(Long dmId);

    Long selectMaxEawbSysCode();

    List<SinotransOrderPool> selectOrderAccept(EawbCheckVO param);

    int countOrderAccept(EawbCheckVO param);

    List<SettlementObject> selectSettlementList();

    int countEawbPrintcodeByDmId(Long dmId);

    List<String> selectEawbPrintcodeByDmId(Long dmId);

    void insertEawbByDmId(Long dmId);

    void insertEawbBymId(Long mId);

    void insertEawbBymIdAndDmId(Long mId,Long dmId);

    List<ManifestEawb> selectManifestEawb(int threadNum,int pageSize);

    List<Long> selectIdByDmId(Long dmId);

    List<DebitManifestTemporary> selectTemporaryByDmId(Long dmId);

    void insertBalanceByTempId(Long mId,Long dmId,Long tempId);

}
