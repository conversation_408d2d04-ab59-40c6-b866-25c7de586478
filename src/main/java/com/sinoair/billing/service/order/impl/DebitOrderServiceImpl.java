package com.sinoair.billing.service.order.impl;

import com.sinoair.billing.core.util.DateUtil;
import com.sinoair.billing.core.util.MailUtil;
import com.sinoair.billing.core.util.StringUtil;
import com.sinoair.billing.dao.billing.*;
import com.sinoair.billing.domain.constant.MailConstant;
import com.sinoair.billing.domain.constant.OrderPoolConstant;
import com.sinoair.billing.domain.constant.StatusEnum;
import com.sinoair.billing.domain.model.billing.*;
import com.sinoair.billing.domain.vo.check.EawbCheckVO;
import com.sinoair.billing.service.order.DebitOrderService;
import com.sinoair.billing.service.order.OrderPoolService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

import static java.util.Comparator.comparing;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;

/**
 * @Auther: nanhb
 * @Date: 2020/3/24 11:29
 * @Description:
 */
@Service("DebitOrderService")
public class DebitOrderServiceImpl implements DebitOrderService {

    private Logger logger = LoggerFactory.getLogger(DebitOrderServiceImpl.class);


    @Resource(name = "DebitOrderMapper")
    private DebitOrderMapper debitOrderMapper;

    @Resource(name = "OrderPoolMapper")
    private OrderPoolMapper orderPoolMapper;

    @Override
    public DebitOrder selectByPrimaryKey(Long dmId){
        return debitOrderMapper.selectByPrimaryKey(dmId);
    }

    @Override
    public int updateByPrimaryKeySelective(DebitOrder record){
        return debitOrderMapper.updateByPrimaryKeySelective(record);
    }


    @Override
    public  Long getPendingDebitOrder(){
        Long dmId = null;
        List<DebitOrder> debitOrderList = debitOrderMapper.listDebitOrder(OrderPoolConstant.DM_STATUS_PENDING);
        if (debitOrderList.size() > 0){
            return debitOrderList.get(0).getDmId();
        }
        return dmId;
    }

    @Override
    public DebitOrder getDebitOrder(String sysStatus){
        List<DebitOrder> debitOrderList = debitOrderMapper.listDebitOrder(sysStatus);
        if (debitOrderList.size() > 0){
            return debitOrderList.get(0);
        }
        return null;
    }

    @Override
    public int countDebitOrder(String sysStatus){
        return debitOrderMapper.countDebitOrder(sysStatus);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Throwable.class)
    public String selectSinotransIdSeq(){
        return orderPoolMapper.selectSinotransIdSequence();
    }



}
