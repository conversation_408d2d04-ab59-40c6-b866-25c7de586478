package com.sinoair.billing.service.order.impl;

import com.sinoair.billing.dao.billing.ManifestOrderMapper;
import com.sinoair.billing.dao.billing.OrderPoolMapper;
import com.sinoair.billing.domain.constant.OrderPoolConstant;
import com.sinoair.billing.domain.model.billing.ManifestOrder;
import com.sinoair.billing.service.order.ManifestOrderService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Auther: nanhb
 * @Date: 2020/3/24 11:29
 * @Description:
 */
@Service("ManifestOrderService")
public class ManifestOrderServiceImpl implements ManifestOrderService {

    private Logger logger = LoggerFactory.getLogger(ManifestOrderServiceImpl.class);


    @Resource(name = "ManifestOrderMapper")
    private ManifestOrderMapper manifestOrderMapper;

    @Resource(name = "OrderPoolMapper")
    private OrderPoolMapper orderPoolMapper;

    @Override
    public ManifestOrder selectByPrimaryKey(Long dmId){
        return manifestOrderMapper.selectByPrimaryKey(dmId);
    }

    @Override
    public int updateByPrimaryKeySelective(ManifestOrder record){
        return manifestOrderMapper.updateByPrimaryKeySelective(record);
    }


    @Override
    public  Long getPendingDebitOrder(){
        Long mId = null;
        List<ManifestOrder> manifestOrderList = manifestOrderMapper.listManifestOrder(OrderPoolConstant.DM_STATUS_PENDING);
        if (manifestOrderList.size() > 0){
            return manifestOrderList.get(0).getmId();
        }
        return mId;
    }

    @Override
    public ManifestOrder getManifestOrder(String sysStatus){
        List<ManifestOrder> manifestOrderList = manifestOrderMapper.listManifestOrder(sysStatus);
        if (manifestOrderList.size() > 0){
            return manifestOrderList.get(0);
        }
        return null;
    }

    @Override
    public int countManifestOrder(String sysStatus){
        return manifestOrderMapper.countManifestOrder(sysStatus);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Throwable.class)
    public String selectSinotransIdSeq(){
        return orderPoolMapper.selectSinotransIdSequence();
    }



}
