package com.sinoair.billing.service.order.impl;

import com.github.pagehelper.PageHelper;
import com.sinoair.billing.core.util.DateUtil;
import com.sinoair.billing.core.util.MailUtil;
import com.sinoair.billing.core.util.StringUtil;
import com.sinoair.billing.dao.billing.*;
import com.sinoair.billing.domain.constant.CheckConstant;
import com.sinoair.billing.domain.constant.MailConstant;
import com.sinoair.billing.domain.constant.OrderPoolConstant;
import com.sinoair.billing.domain.constant.StatusEnum;
import com.sinoair.billing.domain.model.billing.*;
import com.sinoair.billing.domain.vo.check.EawbCheckVO;
import com.sinoair.billing.service.order.DebitOrderService;
import com.sinoair.billing.service.order.OrderPoolService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.Comparator.comparing;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;

/**
 * @Auther: nanhb
 * @Date: 2020/3/24 11:29
 * @Description:
 */
@Service("OrderPoolService")
public class OrderPoolServiceImpl implements OrderPoolService {

    private Logger logger = LoggerFactory.getLogger(OrderPoolServiceImpl.class);

    private int insertLength = 1000;

    @Resource(name = "OrderPoolMapper")
    private OrderPoolMapper orderPoolMapper;

    @Resource(name = "SinotransOrderPoolMapper")
    private SinotransOrderPoolMapper sinotransOrderPoolMapper;

    @Resource(name = "SinotransOrderPoolStatusMapper")
    private SinotransOrderPoolStatusMapper orderPoolStatusMapper;

    @Resource(name = "SettlementObjectMapper")
    private SettlementObjectMapper soMapper;

    @Resource(name = "ExpressAirWayBillMapper")
    private ExpressAirWayBillMapper billMapper;

    @Resource(name = "DebitEawbMapper")
    private DebitEawbMapper debitEawbMapper;

    @Resource(name = "DebitManifestMapper")
    private DebitManifestMapper debitManifestMapper;

    @Resource(name = "NationCountryMapper")
    private NationCountryMapper nationCountryMapper;

    @Resource(name = "BillManifestMapper")
    private BillManifestMapper billManifestMapper;

    @Resource(name = "ManifestListMapper")
    private ManifestListMapper manifestListMapper;

    @Resource(name = "ManifestEawbMapper")
    private ManifestEawbMapper manifestEawbMapper;

    @Resource(name = "SinotransOriginalOrgMapper")
    private SinotransOriginalOrgMapper originalOrgMapper;

    @Resource(name = "DebitManifestTemporaryMapper")
    private DebitManifestTemporaryMapper manifestTemporaryMapper;

    @Override
    @Transactional
    public String handleAcceptedOrder(EawbCheckVO param,List<SettlementObject> soList){
        logger.info("受理订单线程"+param.getPageNum()+"开始查询===="+new Date());
        int pageNum = param.getPageNum();
        int checkCount = 0;
        try {
            List<NationCountry> ncList = nationCountryMapper.listAll();
            List<SinotransOriginalOrg> originalOrgList = originalOrgMapper.listSinotransOrg();
            String year = DateUtil.getYearAfter();
            Date curDate = new Date();
            List<SinotransOrderPoolStatus> orderPoolStatusList = new ArrayList<>();
            PageHelper.startPage(param.getPageNum(), param.getPageSize(),false);
            List<SinotransOrderPool> resultList = orderPoolMapper.selectOrderAccept(param);
            List<SinotransOrderPool> addList = new ArrayList<>();
            String acceptCode = StatusEnum.ACCEPT.getStatusCode();
            logger.info("订单集合长度："+resultList.size());
            for (SinotransOrderPool orderPool : resultList){
//                String sinotransIdSeq = debitOrderService.selectSinotransIdSeq();
//                String lsh = StringUtil.getIntFormString(OrderPoolConstant.SINOTRANS_ID_LEN,sinotransIdSeq);
                // sinotransId 定义为组号/单票号，规则为年月+socode
//                String sinotransId = OrderPoolConstant.SYS_CODE + year + lsh;

                if (orderPoolStatusMapper.countOrderPoolStatus(orderPool.getEawbPrintcode(),acceptCode) > 0){
//                    if (checkCount == 0){
//                        checkCount++;
//                        logger.info("受理订单线程"+param.getPageNum()+"单号:"+orderPool.getEawbPrintcode());
//                    }
                    continue;
                }
                Date statusTime = orderPool.getStatusTime();
                if (statusTime == null){
                    statusTime = curDate;
                }
                ////单票号  公司+年月+客户编码
                String sinotransId = orderPool.getSacId()+DateUtil.getYearMonth(statusTime)+orderPool.getOriginalCustId();
                orderPool.setSinotransId(sinotransId);
                orderPool.setCreateTime(statusTime);
                orderPool.setSenderCode(OrderPoolConstant.SENDER_CODE); //业务系统代码 主数据系统代码
                orderPool.setExtend1(orderPool.getSacId());
                orderPool.setExtend3(orderPool.getOriginalCustId());  //结算对象编码

                String originalOrg = getOrgCode(orderPool.getSacId(),originalOrgList);
                if (StringUtil.isNotEmpty(originalOrg)){
                    orderPool.setOriginalOrg(originalOrg);    //原业务系统ORG
                }else{
                    orderPool.setOriginalOrg(OrderPoolConstant.ORIGINAL_ORG);    //原业务系统ORG
                }

                orderPool.setStatusTemplate(OrderPoolConstant.STATUS_TEMPLATE); //状态模板

                orderPool.setBizType(OrderPoolConstant.BIZ_TYPE); //业务类别  电商物流
                orderPool.setImportExportMarks(OrderPoolConstant.IEM_E); //进出口标识符

                //todo 承运商编码如何获取
                orderPool.setCarrierCode(OrderPoolConstant.CARRIER_CODE);   //承运商编码
                orderPool.setAttribute5(OrderPoolConstant.ATTRIBUTE_5);   //业务子类
                orderPool.setAttribute7(OrderPoolConstant.ATTRIBUTE_7);  //贸易监管代码 9610/1210

                SettlementObject so = getSoInfo(orderPool.getOriginalCustId(),soList);
                if (so != null){
                    orderPool.setStandardCustName(so.getSoName());
                    orderPool.setOriginalCustId(so.getCdhNote());
                    orderPool.setExtend2(so.getCustCode());
                }

                if (StringUtil.isEmpty(orderPool.getTaNcCodeDeparture())){
                    orderPool.setTaNcCodeDeparture(CheckConstant.NC_3CODE_CN); //启运港国家州
                }else{
                    NationCountry nc = getNcCode(orderPool.getTaNcCodeDeparture(),ncList);
                    if (nc != null){
                        orderPool.setTaNcCodeDeparture(nc.getNc3code()); //启运港国家州
                    }
                }

                if (StringUtil.isNotEmpty(orderPool.getTaNcCodeDestination())){
                    NationCountry nc = getNcCode(orderPool.getTaNcCodeDestination(),ncList);
                    if (nc != null){
                        orderPool.setTaNcCodeDestination(nc.getNc3code()); //目的港国家州
                    }
                }
                addList.add(orderPool);

                SinotransOrderPoolStatus orderPoolStatus = new SinotransOrderPoolStatus();
                orderPoolStatus.setCreateTime(curDate);
                orderPoolStatus.setEawbPrintcode(orderPool.getEawbPrintcode());
                orderPoolStatus.setStatusCode(StatusEnum.ACCEPT.getStatusCode());
                orderPoolStatus.setStatusDesc(StatusEnum.ACCEPT.getStatusDesc());
                orderPoolStatus.setStatusTime(statusTime);

                orderPoolStatusList.add(orderPoolStatus);

            }

            long startstream = System.currentTimeMillis();
            List<SinotransOrderPool> poolList = addList.stream().collect(
                    collectingAndThen(
                            toCollection(() -> new TreeSet<>(comparing(n->n.getEawbPrintcode()))),ArrayList::new)
            );
            logger.info("线程:"+pageNum+" 去重耗时:"+(System.currentTimeMillis()-startstream)+" ms");
            if (poolList.size() > 0){
                logger.info("线程:"+pageNum+" 新增订单数据："+poolList.size());
                int size = poolList.size();
                int count = size % insertLength != 0 ? size / insertLength + 1 : size / insertLength;
                for (int i = 0; i < count; i++) {
                    List<SinotransOrderPool> subList = poolList.subList(i * insertLength, ((1000 + i * insertLength) < size ? (1000 + i * insertLength) : size));
                    if (subList.size() > 0){
                        sinotransOrderPoolMapper.insertBatch(subList);
                    }
                }
            }

            List<SinotransOrderPoolStatus> poolStatusList = orderPoolStatusList.stream().collect(
                    collectingAndThen(
                            toCollection(() -> new TreeSet<>(comparing(n->n.getEawbPrintcode()+";"+n.getStatusCode()))),ArrayList::new)
            );
            if (poolStatusList.size() > 0){
                logger.info("线程:"+pageNum+" 新增受理数据："+poolStatusList.size());
                int size = poolStatusList.size();
                int count = size % insertLength != 0 ? size / insertLength + 1 : size / insertLength;
                for (int i = 0; i < count; i++) {
                    List<SinotransOrderPoolStatus> subList = poolStatusList.subList(i * insertLength, ((1000 + i * insertLength) < size ? (1000 + i * insertLength) : size));
                    if (subList.size() > 0){
                        orderPoolStatusMapper.insertBatch(subList);
                    }
                }
            }

        }catch (Exception e){
            e.printStackTrace();
            logger.error("线程:"+pageNum+" 受理订单数据异常:"+e.getMessage());
            MailUtil.postMail(MailConstant.MAIL_TO_ONE,MailConstant.MAIL_SUBJECT_OREDER_ACCEPT,e.getMessage());
        }
        logger.info("线程:"+pageNum+" 受理订单线程结束"+param.getPageNum()+"结束===="+new Date());
        return "";
    }

    @Override
    @Transactional
    public String handleBalanceOrder(int threadNum,int pageSize,List<ManifestEawb> resultList){
        long start = System.currentTimeMillis();
        logger.info(threadNum+" threadNum开始:");
        List<SinotransOrderPoolStatus> orderPoolStatusList = new ArrayList<>();
        List<SinotransOrderPool> orderPoolList = new ArrayList<>();
        List<NationCountry> ncList = nationCountryMapper.listAll();
        List<SettlementObject> soList = soMapper.selectSettlementList();
        List<SinotransOriginalOrg> originalOrgList = originalOrgMapper.listSinotransOrg();
        Date curDate = new Date();
        String acceptCode = StatusEnum.ACCEPT.getStatusCode();
        String balanceCode = StatusEnum.BALANCE.getStatusCode();

        List<ManifestEawb> deleteList = new ArrayList<>();
        try{
//            PageHelper.startPage(threadNum, pageSize,false);
//            List<ManifestEawb> resultList = manifestEawbMapper.selectManifestEawb();

            if (resultList == null){
                logger.error("未查到eawb信息:");
                return "ERROR";
            }

//            DebitManifest debitManifest = debitManifestMapper.selectByPrimaryKey(dmId.intValue());
//            Date statusTime = debitManifest.getDmHandleTime();
//            DebitManifest preDebitManifest = null;
//            int debitCount = 0;
            for (ManifestEawb manifestEawb : resultList){
                String eawbPrintcode = manifestEawb.getEawbPrintcode();
                if (orderPoolStatusMapper.countOrderPoolStatus(eawbPrintcode,balanceCode) > 0){
                    deleteList.add(manifestEawb);
                    continue;
                }else{
                    if (orderPoolStatusMapper.countOrderPoolStatus(eawbPrintcode,acceptCode) == 0){
                        SinotransOrderPool orderPool = new SinotransOrderPool();
                        ExpressAirWayBill airWayBill = billMapper.selectByEawbPrintCode(eawbPrintcode);
                        if (airWayBill == null){
                            continue;
                        }
                        deleteList.add(manifestEawb);
                        //补充订单基本数据
                        replenishOrderPool(airWayBill,orderPool,curDate,ncList,soList,originalOrgList);
                        orderPool.setCreateTime(curDate);
                        orderPoolList.add(orderPool);

                        //补充受理环节数据
                        SinotransOrderPoolStatus statusBalance = new SinotransOrderPoolStatus();
                        statusBalance.setEawbPrintcode(eawbPrintcode);
                        statusBalance.setStatusCode(acceptCode);
                        statusBalance.setStatusDesc(StatusEnum.ACCEPT.getStatusDesc());
                        Date statusTime = airWayBill.getEawbUpdatetime();
                        if (statusTime == null){
                            statusTime = curDate;
                        }
                        statusBalance.setStatusTime(statusTime);
                        statusBalance.setCreateTime(curDate);

                        orderPoolStatusList.add(statusBalance);

                    }
                }
                deleteList.add(manifestEawb);
                //清单确认时间
                Date statusTime = manifestEawb.getStatusTime();

                SinotransOrderPoolStatus orderPoolStatus = new SinotransOrderPoolStatus();
                orderPoolStatus.setEawbPrintcode(eawbPrintcode);
                orderPoolStatus.setStatusCode(balanceCode);
                orderPoolStatus.setStatusDesc(StatusEnum.BALANCE.getStatusDesc());
                orderPoolStatus.setStatusTime(statusTime);
                orderPoolStatus.setCreateTime(curDate);

                orderPoolStatusList.add(orderPoolStatus);
            }
            if (orderPoolList.size() > 0){
                long startstream = System.currentTimeMillis();
                List<SinotransOrderPool> poolList = orderPoolList.stream().collect(
                        collectingAndThen(
                                toCollection(() -> new TreeSet<>(comparing(n->n.getEawbPrintcode()))),ArrayList::new)
                );
                logger.info(" 去重耗时:"+(System.currentTimeMillis()-startstream)+" ms");
                if (poolList.size() > 0){
                    logger.info("结审新增订单数据："+poolList.size());
                    int size = poolList.size();
                    int count = size % insertLength != 0 ? size / insertLength + 1 : size / insertLength;
                    for (int i = 0; i < count; i++) {
                        List<SinotransOrderPool> subList = poolList.subList(i * insertLength, ((1000 + i * insertLength) < size ? (1000 + i * insertLength) : size));
                        if (subList.size() > 0){
                            sinotransOrderPoolMapper.insertBatch(subList);
                        }
                    }
                }
            }


            List<SinotransOrderPoolStatus> poolStatusList = orderPoolStatusList.stream().collect(
                    collectingAndThen(
                            toCollection(() -> new TreeSet<>(comparing(n->n.getEawbPrintcode()+";"+n.getStatusCode()))),ArrayList::new)
            );
            if (poolStatusList.size() > 0){
                logger.info("新增结审数据："+poolStatusList.size());
                int size = poolStatusList.size();
                int count = size % insertLength != 0 ? size / insertLength + 1 : size / insertLength;
                for (int i = 0; i < count; i++) {
                    List<SinotransOrderPoolStatus> subList = poolStatusList.subList(i * insertLength, ((1000 + i * insertLength) < size ? (1000 + i * insertLength) : size));
                    if (subList.size() > 0){
                        orderPoolStatusMapper.insertBatch(subList);
                    }
                }
            }

            if (deleteList.size() > 0){
                logger.info("删除结审数据："+deleteList.size());
                int size = deleteList.size();
                int count = size % insertLength != 0 ? size / insertLength + 1 : size / insertLength;
                for (int i = 0; i < count; i++) {
                    List<ManifestEawb> subList = deleteList.subList(i * insertLength, ((1000 + i * insertLength) < size ? (1000 + i * insertLength) : size));
                    if (subList.size() > 0){
                        manifestEawbMapper.deleteBatchByEawb(subList);
                    }
                }
            }

        }catch (Exception e){
            e.printStackTrace();
            logger.error("结审订单数据异常:"+e.getMessage());
            MailUtil.postMail(MailConstant.MAIL_TO_ONE,MailConstant.MAIL_SUBJECT_OREDER_BILL,e.getMessage());
            return "ERROR";
        }
        logger.info(threadNum+" threadNum结束:"+(System.currentTimeMillis()-start)+" ms");

        return "";
    }

    @Override
    public String handleDebitEawb(Long dmId){
        debitEawbMapper.insertByDmId(dmId);
        return "";
    }

    @Override
    public  Long selectMaxEawbSysCode(){
        return orderPoolMapper.selectMaxEawbSysCode();
    }

    @Override
    public List<SinotransOrderPool> selectOrderAccept(EawbCheckVO param){
        return orderPoolMapper.selectOrderAccept(param);
    }

    @Override
    public  int countOrderAccept(EawbCheckVO param){
        return orderPoolMapper.countOrderAccept(param);
    }

    @Override
    public List<SettlementObject> selectSettlementList(){
        return soMapper.selectSettlementList();
    }

    private String getSoName(String soCode,List<SettlementObject> soList){
        Optional<SettlementObject> optional = soList.stream().filter(property -> property.getSoCode().equals(soCode)).findFirst();
        if (optional.isPresent()) {
            // 存在
            SettlementObject so =  optional.get();
            return so.getSoName();
        } else {
            // 不存在
        }
        return "";
    }

    private SettlementObject getSoInfo(String soCode,List<SettlementObject> soList){
        Optional<SettlementObject> optional = soList.stream().filter(property -> property.getSoCode().equals(soCode)).findFirst();
        if (optional.isPresent()) {
            // 存在
            SettlementObject so =  optional.get();
            return so;
        } else {
            // 不存在
        }
        return null;
    }

    private void replenishOrderPool(ExpressAirWayBill bill,SinotransOrderPool orderPool,Date curDate,
                                    List<NationCountry> ncList,List<SettlementObject> soList,List<SinotransOriginalOrg> originalOrgList) throws Exception{
        Date statusTime = bill.getEawbUpdatetime();
        if (statusTime == null){
            statusTime = curDate;
        }
        //单票号  公司+年月+客户编码
        String sinotransId = bill.getSacId()+DateUtil.getYearMonth(statusTime)+bill.getEawbSoCode();
//        String sinotransIdSeq = debitOrderService.selectSinotransIdSeq();
        orderPool.setCreateTime(statusTime);
        orderPool.setEawbPrintcode(bill.getEawbPrintcode());
        orderPool.setExtend1(bill.getSacId());
        orderPool.setExtend3(bill.getEawbSoCode());  //结算对象编码
//        String sinotransId = OrderPoolConstant.SYS_CODE + DateUtil.getYearAfter() + StringUtil.getIntFormString(OrderPoolConstant.SINOTRANS_ID_LEN,sinotransIdSeq);
        orderPool.setSinotransId(sinotransId); //生成规则：系统编码（2位公司代码+1位类别代码+2位流水号）+两位年+两位月+9位业务流水号
        orderPool.setOrderId(bill.getEawbPrintcode()); //全局唯一：系统主数据代码+业务源主键(SOURCE_ID)（业务源主键一般为业务运编号等）
        orderPool.setSourceId(String.valueOf(bill.getEawbSyscode()));  //对应业务系统的主键号
        //todo 需要进行ORG关联匹配？
        String originalOrg = getOrgCode(orderPool.getSacId(),originalOrgList);
        if (StringUtil.isNotEmpty(originalOrg)){
            orderPool.setOriginalOrg(originalOrg);    //原业务系统ORG
        }else{
            orderPool.setOriginalOrg(OrderPoolConstant.ORIGINAL_ORG);    //原业务系统ORG
        }
//        orderPool.setOriginalOrg(OrderPoolConstant.ORIGINAL_ORG);    //原业务系统ORG
        orderPool.setStatusTemplate(OrderPoolConstant.STATUS_TEMPLATE); //状态模板
        //todo 应该不是soCode，需要进行客户代码匹配？
        SettlementObject so = getSoInfo(bill.getEawbSoCode(),soList);
        if (so != null){
            orderPool.setStandardCustName(so.getSoName());  //--客户名称
            orderPool.setOriginalCustId(so.getCdhNote());  //原业务系统客户ID  主数据MDM编码
            orderPool.setExtend2(so.getCustCode());
        }
        orderPool.setSenderCode(OrderPoolConstant.SENDER_CODE);  //业务系统代码 主数据系统代码
        orderPool.setBusinessVolume(bill.getEawbChargeableweight()); //业务量 （计费重）
        orderPool.setBizType(OrderPoolConstant.BIZ_TYPE); //业务类别  电商物流
        orderPool.setImportExportMarks(OrderPoolConstant.IEM_E); //进出口标识符
        orderPool.setBlNo(bill.getEawbReference1()); //运单号
        //todo 承运商编码如何获取
        orderPool.setCarrierCode(OrderPoolConstant.CARRIER_CODE);   //承运商编码
        orderPool.setAttribute5(OrderPoolConstant.ATTRIBUTE_5);   //业务子类
        orderPool.setAttribute7(OrderPoolConstant.ATTRIBUTE_7);  //贸易监管代码 9610/1210

        orderPool.setBmPiece(bill.getEawbPieces() == null?1:bill.getEawbPieces().intValue());
        orderPool.setBmChargeableweight(bill.getEawbChargeableweight());
        if (StringUtil.isEmpty(bill.getEawbDeparture())){
            orderPool.setTaNcCodeDeparture(CheckConstant.NC_3CODE_CN); //启运港国家州
        }else{
            NationCountry nc = getNcCode(bill.getEawbDeparture(),ncList);
            if (nc != null){
                orderPool.setTaNcCodeDeparture(nc.getNc3code()); //启运港国家州
            }
        }

        if (StringUtil.isNotEmpty(bill.getEawbDestination())){
            NationCountry nc = getNcCode(bill.getEawbDestination(),ncList);
            if (nc != null){
                orderPool.setTaNcCodeDestination(nc.getNc3code()); //目的港国家州
            }
        }


    }

    @Override
    public int countEawbPrintcodeByDmId(Long dmId){
        return orderPoolMapper.countEawbPrintcodeByDmId(dmId);
    }

    @Override
    public List<String> selectEawbPrintcodeByDmId(Long dmId){
        return orderPoolMapper.selectEawbPrintcodeByDmId(dmId);
    }


    @Override
    public void insertEawbByDmId(Long dmId){
        debitEawbMapper.insertByDmId(dmId);
    }

    @Override
    public void insertEawbBymId(Long mId){
        List<Long> dmIds = billManifestMapper.selectDmByMId(mId);
        ManifestList manifestList = manifestListMapper.selectByPrimaryKey(mId);
        for (Long dmId : dmIds){
            manifestEawbMapper.insertByDmId(dmId,mId,manifestList.getmConfirmtime());
        }

    }

    @Override
    public  List<ManifestEawb> selectManifestEawb(int threadNum,int pageSize){
        PageHelper.startPage(threadNum, pageSize,false);
        List<ManifestEawb> resultList = manifestEawbMapper.selectManifestEawb();
        return resultList;
    }

    @Override
    public void insertEawbBymIdAndDmId(Long mId,Long dmId){
//        List<Long> dmIds = billManifestMapper.selectDmByMId(mId);
//        ManifestList manifestList = manifestListMapper.selectByPrimaryKey(mId);
        DebitManifest debitManifest = debitManifestMapper.selectByPrimaryKey(dmId.intValue());
        //账单来源于临时账单汇总
        if ("T".equals(debitManifest.getDmDirty())){
            manifestEawbMapper.insertByTemporaryDmId(dmId,mId,debitManifest.getDmHandleTime());
        }else{
            manifestEawbMapper.insertByDmId(dmId,mId,debitManifest.getDmHandleTime());
        }


    }

    @Override
    public void insertBalanceByTempId(Long mId,Long dmId,Long tempId){
        DebitManifest debitManifest = debitManifestMapper.selectByPrimaryKey(dmId.intValue());
        //临时账单ID
        manifestEawbMapper.insertByDmId(tempId,mId,debitManifest.getDmHandleTime());

        DebitManifestTemporary manifestTemporary = new DebitManifestTemporary();
        manifestTemporary.setIsBalance("Y");
        manifestTemporary.setDmTempId(tempId.intValue());
        manifestTemporaryMapper.updateByPrimaryKeySelective(manifestTemporary);
    }

    @Override
    public     List<Long> selectIdByDmId(Long dmId){
        return manifestTemporaryMapper.selectIdByDmId(dmId);
    }

    @Override
    public     List<DebitManifestTemporary> selectTemporaryByDmId(Long dmId){
        return manifestTemporaryMapper.selectTemporaryByDmId(dmId);
    }

    private NationCountry getNcCode(String code,List<NationCountry> ncList){
        Optional<NationCountry> optional = ncList.stream().filter(property -> property.getNc2code().equals(code)).findFirst();
        if (optional.isPresent()) {
            // 存在
            NationCountry nc =  optional.get();
            return nc;
        } else {
            // 不存在
        }
        return null;
    }

    private String getOrgCode(String code,List<SinotransOriginalOrg> orgList){
        Optional<SinotransOriginalOrg> optional = orgList.stream().filter(property -> property.getSacId().equals(code)).findFirst();
        if (optional.isPresent()) {
            // 存在
            SinotransOriginalOrg org =  optional.get();
            return org.getMainOrgCode();
        } else {
            // 不存在
        }
        return null;
    }

}
