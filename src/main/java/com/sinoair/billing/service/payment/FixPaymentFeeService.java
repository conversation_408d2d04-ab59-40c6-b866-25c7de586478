package com.sinoair.billing.service.payment;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.sinoair.billing.core.util.SpringContextUtil;
import com.sinoair.billing.dao.billing.ExpressBusinessActivityMapper;
import com.sinoair.billing.dao.billing.GeneratePaymentFeeLogMapper;
import com.sinoair.billing.dao.billing.ReceiptRecordMapper;
import com.sinoair.billing.domain.model.billing.ExpressBusinessActivity;
import com.sinoair.billing.domain.model.billing.GeneratePaymentFeeLog;
import com.sinoair.billing.domain.model.billing.PaymentRecord;
import com.sinoair.billing.domain.model.billing.ReceiptRecord;
import com.sinoair.billing.domain.vo.price.GenerateFee;
import com.sinoair.billing.domain.vo.price.GenerateReceiptFee;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Service("FixPaymentFeeService")
public class FixPaymentFeeService {

    @Autowired
    private ExpressBusinessActivityMapper expressBusinessActivityMapper;

    @Autowired
    private ReceiptRecordMapper receiptRecordMapper;

    @Autowired
    private GeneratePaymentFeeService generatePaymentFeeService;

    @Autowired
    private GeneratePaymentFeeLogMapper generatePaymentFeeLogMapper;

    private ReceiptRecord convertGenerateReceiptFeeToReceiptRecord(GenerateReceiptFee generateFee, BigDecimal exchangeRate, Integer feeStatus) {
        ReceiptRecord rp = new ReceiptRecord();
        if (exchangeRate != null) {
            rp.setRrTotalRmb(generateFee.getPlanAmount().multiply(exchangeRate).setScale(2, RoundingMode.HALF_UP));
        }
        if (generateFee.getCtCode().equals("142")) {
            exchangeRate = new BigDecimal(1);
        }
        rp.setEawbPrintcode(generateFee.getEawbPrintcode());
        rp.setSoCode(generateFee.getSoCode());
        rp.setPrId(generateFee.getRpId().toString());
        rp.setCtCode(generateFee.getCtCode());
        rp.setCompanyId(generateFee.getcId());
        rp.setRrName(generateFee.getPdName());
        rp.setPdSyscode(generateFee.getPdSyscode());
        rp.setEawbSoCode(generateFee.getEawbSoCode());
        rp.setPartitionCode(generateFee.getPartitionCode());
        rp.setRrType(generateFee.getPtType());
        rp.setRrPlanAmount(generateFee.getPlanAmount());
        rp.setRrActualAmount(generateFee.getPlanAmount());
        rp.setRrRemark(generateFee.getFormula());
        rp.setEpKey(generateFee.getEawbServicetype());
        rp.setChargeableweight(generateFee.getEawbChargeableweight());
        rp.setEawbChargeableweight(generateFee.getEawbChargeableweight());
        rp.setOutboundCompanyId(generateFee.getEawbOutboundSacId());
        rp.setEawbIeType("E");
        rp.setCtRate(exchangeRate);
        rp.setRrStatus("ON");
        rp.setRrHandletime(new Date());
        rp.setRrUserId(1);
        rp.setRrOccurtime(generateFee.getEbaOccurtime());
        rp.setEawbReference1(generateFee.getEawbTrackingNo());
        rp.setEawbReference2(generateFee.getEawbReference2());
        rp.setMawbCode(generateFee.getMawbCode());
        rp.setFeeStatus(feeStatus);
        rp.setEawbSoCode(generateFee.getEawbSoCode());
        //新增字段
        rp.setSoMode(generateFee.getSoMode());
        rp.setEawbDestcountry(generateFee.getEawbDestcountry());
        rp.setEawbDestination(generateFee.getEawbDestination());
        rp.setEawbDepartcountry(generateFee.getEawbDepartcountry());
        rp.setEawbDeparture(generateFee.getEawbDeparture());
        rp.setRrTotalfc(generateFee.getPlanAmount());
        rp.setPtType(generateFee.getPtType());
        rp.setCcCode(generateFee.getcCode());
        return rp;
    }

    private PaymentRecord convertGenerateFeeToPaymentRecord(GenerateFee generateFee, BigDecimal exchangeRate, Integer feeStatus) {
        PaymentRecord pr = new PaymentRecord();
        if (exchangeRate != null) {
            pr.setPrTotalRmb(generateFee.getPlanAmount().multiply(exchangeRate).setScale(2, RoundingMode.HALF_UP));
        }
        if (generateFee.getCtCode().equals("142")) {
            exchangeRate = new BigDecimal(1);
        }
        pr.setEawbPrintcode(generateFee.getEawbPrintcode());
        pr.setSoCode(generateFee.getSoCode());
        pr.setPpId(generateFee.getPpId());
        pr.setCtCode(generateFee.getCtCode());
        pr.setCompanyId(generateFee.getcId());
        pr.setPrName(generateFee.getPdName());
        pr.setPdSyscode(generateFee.getPdSyscode());
        pr.setEawbSoCode(generateFee.getEawbSoCode());
        pr.setPartitionCode(generateFee.getPartitionCode());
        pr.setPrType(generateFee.getPpCate());
        pr.setServiceId(generateFee.getServiceId());
        pr.setPrPlanAmount(generateFee.getPlanAmount());
        pr.setPrActualAmount(generateFee.getPlanAmount());
        pr.setPrRemark(generateFee.getFormula());
        pr.setEpKey(generateFee.getEawbServicetype());
        pr.setChargeableweight(generateFee.getEawbChargeableweight());
        pr.setEawbChargeableweight(generateFee.getEawbChargeableweight());
        pr.setOutboundCompanyId(generateFee.getEawbOutboundSacId());
        pr.setEawbIetype("E");
        pr.setPrCate("OUTER");
        pr.setCtRate(exchangeRate);
        pr.setPrStatus("ON");
        pr.setPrHandletime(new Date());
        pr.setPrUserId(1);
        pr.setEawbTrackingNo(generateFee.getEawbTrackingNo());
        pr.setPrOccurtime(generateFee.getEbaOccurtime());
        pr.setServiceDetailId(generateFee.getPsdId());
        pr.setEawbReference1(generateFee.getEawbTrackingNo());
        pr.setEawbReference2(generateFee.getEawbReference2());
        //pr.setMawbCode(generateFee.getMawbCode());
        pr.setFeeStatus(feeStatus);
        return pr;
    }

    private PaymentRecord convertReceiptToPayment(ReceiptRecord receiptRecord) {
        PaymentRecord paymentRecord = new PaymentRecord();
        paymentRecord.setEawbPrintcode(receiptRecord.getEawbPrintcode());
        //paymentRecord.setPpId();
        //应收转应付soCode、companyId特殊处理
        paymentRecord.setSoCode(receiptRecord.getCompanyId());
        paymentRecord.setCompanyId(receiptRecord.getCcCode());

        paymentRecord.setCtCode(receiptRecord.getCtCode());
        paymentRecord.setPrType("A");
        paymentRecord.setPrPlanAmount(receiptRecord.getRrPlanAmount());
        paymentRecord.setPrActualAmount(receiptRecord.getRrActualAmount());
        paymentRecord.setPrStatus("ON");
        paymentRecord.setPrUserId(1);
        paymentRecord.setPrHandletime(new Date());
        paymentRecord.setEawbReference1(receiptRecord.getEawbReference1());
        paymentRecord.setEawbReference2(receiptRecord.getEawbReference2());
        paymentRecord.setEawbChargeableweight(receiptRecord.getEawbChargeableweight());
        paymentRecord.setPrOccurtime(receiptRecord.getRrOccurtime());
        paymentRecord.setEawbTrackingNo(receiptRecord.getEawbReference1());
        paymentRecord.setEpKey(receiptRecord.getEpKey());
        paymentRecord.setChargeableweight(receiptRecord.getEawbChargeableweight());
        paymentRecord.setPdSyscode(receiptRecord.getPdSyscode());
        paymentRecord.setOutboundCompanyId(receiptRecord.getOutboundCompanyId());
        paymentRecord.setEawbIetype(receiptRecord.getEawbIeType());
        paymentRecord.setPrCate("INNER");
        paymentRecord.setCtRate(receiptRecord.getCtRate());
        paymentRecord.setPrTotalRmb(receiptRecord.getRrTotalRmb());
        paymentRecord.setPrTotalFc(receiptRecord.getRrTotalfc());
        paymentRecord.setRrId(receiptRecord.getRrId());
        paymentRecord.setEawbSoCode(receiptRecord.getEawbSoCode());
        paymentRecord.setFeeStatus(receiptRecord.getFeeStatus());
        paymentRecord.setPrRemark(receiptRecord.getRrRemark());
        paymentRecord.setPrName(receiptRecord.getRrName());
        paymentRecord.setMawbCode(receiptRecord.getMawbCode());
        return paymentRecord;
    }

    @Transactional
    public void fixPayment(GeneratePaymentFeeLog feeLog) {
        List<GenerateFee> generateFeeList = new ArrayList();
        List<GenerateFee> jiaohangList = new ArrayList<>();
        List<GenerateReceiptFee> receiptFeeList = new ArrayList<>();
        List<GenerateReceiptFee> jiaohangReceiptFeeList = new ArrayList<>();
        Map<String, Object> params = new HashMap<String, Object>();
        List<ExpressBusinessActivity> eabList = expressBusinessActivityMapper.selectFixEba(feeLog.getBeginNum().longValue(), feeLog.getEndNum().longValue());
        for (ExpressBusinessActivity eba : eabList) {
            String ebaSyscode = eba.getEbaSyscode().toPlainString();
            String eawbPrintCode = eba.getEawbPrintcode();
            if (StringUtils.isEmpty(eba.getpEadcode())) {
                eba.setpEadcode("INTERNATIONAL");
                eba.setpEastcode("ASS");
            }
            //产品结审环节
            if (eba.getpEadcode().equals(eba.getEadCode().toUpperCase()) && eba.getpEastcode().equals(eba.getEastCode().toUpperCase())) {
                //应付
                List<GenerateFee> jiaohangList1 = generatePaymentFeeService.generateFeeByEba(eba);
                jiaohangList1.forEach(e -> e.setEbaOccurtime(eba.getEbaOccurtime()));
                jiaohangList.addAll(jiaohangList1.stream().filter(e -> e.getPlanAmount() != null && e.getPlanAmount().compareTo(new BigDecimal(0)) > 0).collect(Collectors.toList()));
                //应收
                List<GenerateReceiptFee> rfl = generatePaymentFeeService.generateReceiptFeeByEba(eba);
                rfl.forEach(e -> {
                    e.setEbaOccurtime(eba.getEbaOccurtime());
                    params.put(e.getEawbPrintcode(), eba.getEbaOccurtime());
                });
                jiaohangReceiptFeeList.addAll(rfl.stream().filter(e -> e.getPlanAmount() != null && e.getPlanAmount().compareTo(new BigDecimal(0)) > 0).collect(Collectors.toList()));
            }
            List<GenerateFee> generateFeeList1 = generatePaymentFeeService.generateFeeList(ebaSyscode, eawbPrintCode);
            if (generateFeeList1 != null && generateFeeList1.size() > 0) {
                generateFeeList.addAll(generateFeeList1.stream().filter(e -> e.getPlanAmount() != null && e.getPlanAmount().compareTo(new BigDecimal(0)) > 0).collect(Collectors.toList()));
            }
            List<GenerateReceiptFee> generateReceiptFees = generatePaymentFeeService.generateReceiptFeeList(ebaSyscode, eawbPrintCode);
            receiptFeeList.addAll(generateReceiptFees.stream().filter(e -> e.getPlanAmount() != null && e.getPlanAmount().compareTo(new BigDecimal(0)) > 0).collect(Collectors.toList()));
        }
        //应收
        if (receiptFeeList.size() > 0) {
            List<GenerateReceiptFee> filterFeeList = new ArrayList<>();
            List<ReceiptRecord> receiptRecordList = new ArrayList<>();
            List<PaymentRecord> receiptToPayRecord = new ArrayList<>();
            //费用项去重
            for (int i = 0; i < receiptFeeList.size(); i++) {
                GenerateReceiptFee pp = receiptFeeList.get(i);
                boolean result = true;
                for (GenerateReceiptFee generateFee : filterFeeList) {
                    if (generateFee.getEawbPrintcode().equals(pp.getEawbPrintcode()) &&
                            generateFee.getSoCode().equals(pp.getSoCode()) &&
                            generateFee.getPdSyscode().equals(pp.getPdSyscode()) &&
                            generateFee.getRpId().equals(pp.getRpId())) {
                        result = false;
                    }
                }
                if (result) {
                    filterFeeList.add(pp);
                }
            }
            for (GenerateReceiptFee generateFee : filterFeeList) {
                BigDecimal exchangeRate = generatePaymentFeeService.selectExchangeRate(generateFee.getCtCode());
                ReceiptRecord rp = convertGenerateReceiptFeeToReceiptRecord(generateFee, exchangeRate, 1);
                List<ReceiptRecord> ppkList = generatePaymentFeeService.queryReceiptByCondition(generateFee.getEawbPrintcode(), generateFee.getRpId(), generateFee.getPdSyscode());
                if (ppkList.size() > 0) {
                    ReceiptRecord ppk = ppkList.get(0);
                    ppk.setRrOccurtime(generateFee.getEbaOccurtime());
                    generatePaymentFeeService.updateTimeAndStatusByRrId(ppk);
                } else {
                    List<ReceiptRecord> ppk = generatePaymentFeeService.queryReceiptByCondition(generateFee.getEawbPrintcode(), generateFee.getRpId(), null);
                    if (ppk.size() == 0) {
                        rp.setRrId(generatePaymentFeeService.selectSequence());
                        receiptRecordList.add(rp);
                    }
                }
                if (receiptRecordList.size() >= 1000) {
                    generatePaymentFeeService.batchInsertRceiptRecord(receiptRecordList);
                    System.out.println("receiptRecordList===" + JSONObject.toJSON(receiptRecordList));
                    for (ReceiptRecord r : receiptRecordList) {
                        if ("INNER".equals(r.getPtType())) {
                            PaymentRecord convertPR = convertReceiptToPayment(r);
                            receiptToPayRecord.add(convertPR);
                        }
                    }
                    generatePaymentFeeService.insertPaymentRecord(receiptToPayRecord);
                    receiptToPayRecord.clear();
                    receiptRecordList.clear();
                }
            }
            if (receiptRecordList.size() > 0) {
                generatePaymentFeeService.batchInsertRceiptRecord(receiptRecordList);
                System.out.println("receiptRecordList===" + JSONObject.toJSON(receiptRecordList));
                for (ReceiptRecord r : receiptRecordList) {
                    if ("INNER".equals(r.getPtType())) {
                        PaymentRecord convertPR = convertReceiptToPayment(r);
                        receiptToPayRecord.add(convertPR);
                    }
                }
                if (receiptToPayRecord.size() > 0) {
                    generatePaymentFeeService.insertPaymentRecord(receiptToPayRecord);
                }
            }

        }
        if (jiaohangReceiptFeeList.size() > 0) {
            List<ReceiptRecord> jhList = new ArrayList<>();
            List<GenerateReceiptFee> filterFeeList = new ArrayList<>();
            List<PaymentRecord> receiptToPayRecord = new ArrayList<>();
            //费用项去重
            for (int i = 0; i < jiaohangReceiptFeeList.size(); i++) {
                GenerateReceiptFee pp = jiaohangReceiptFeeList.get(i);
                boolean result = true;
                for (GenerateReceiptFee generateFee : filterFeeList) {
                    if (generateFee.getEawbPrintcode().equals(pp.getEawbPrintcode()) &&
                            generateFee.getSoCode().equals(pp.getSoCode()) &&
                            generateFee.getPdSyscode().equals(pp.getPdSyscode()) &&
                            generateFee.getRpId().equals(pp.getRpId())) {
                        result = false;
                    }
                }
                if (result) {
                    filterFeeList.add(pp);
                }
            }
            for (GenerateReceiptFee generateFee : filterFeeList) {
                int actualCou = generatePaymentFeeService.existReceiptRecordList(generateFee.getEawbPrintcode(), generateFee.getRpId(), generateFee.getPdSyscode(), generateFee.getSoCode());
                if (actualCou > 0) {
                    continue;
                }
                BigDecimal exchangeRate = generatePaymentFeeService.selectExchangeRate(generateFee.getCtCode());
                ReceiptRecord rp = convertGenerateReceiptFeeToReceiptRecord(generateFee, exchangeRate, 0);
                rp.setRrId(generatePaymentFeeService.selectSequence());
                jhList.add(rp);
                if (jhList.size() >= 1000) {
                    generatePaymentFeeService.batchInsertRceiptRecord(jhList);
                    for (ReceiptRecord r : jhList) {
                        if ("INNER".equals(r.getPtType())) {
                            PaymentRecord convertPR = convertReceiptToPayment(r);
                            receiptToPayRecord.add(convertPR);
                        }
                    }
                    generatePaymentFeeService.insertPaymentRecord(receiptToPayRecord);
                    receiptToPayRecord.clear();
                    jhList.clear();
                }
            }
            if (jhList.size() > 0) {
                generatePaymentFeeService.batchInsertRceiptRecord(jhList);
                for (ReceiptRecord r : jhList) {
                    if ("INNER".equals(r.getPtType())) {
                        PaymentRecord convertPR = convertReceiptToPayment(r);
                        receiptToPayRecord.add(convertPR);
                    }
                }
                generatePaymentFeeService.insertPaymentRecord(receiptToPayRecord);
            }
        }

        //应付
        if (generateFeeList.size() > 0) {
            List<PaymentRecord> paymentRecordList = new ArrayList<>();
            List<GenerateFee> filterFeeList = new ArrayList<>();
            //费用项去重
            for (int i = 0; i < generateFeeList.size(); i++) {
                GenerateFee pp = generateFeeList.get(i);
                boolean result = true;
                for (GenerateFee generateFee : filterFeeList) {
                    if (generateFee.getEawbPrintcode().equals(pp.getEawbPrintcode()) &&
                            generateFee.getSoCode().equals(pp.getSoCode()) &&
                            generateFee.getPdSyscode().equals(pp.getPdSyscode()) &&
                            generateFee.getServiceId().equals(pp.getServiceId())) {
                        result = false;
                    }
                }
                if (result) {
                    filterFeeList.add(pp);
                }
            }
            for (GenerateFee generateFee : filterFeeList) {
                BigDecimal exchangeRate = generatePaymentFeeService.selectExchangeRate(generateFee.getCtCode());
                PaymentRecord pr = convertGenerateFeeToPaymentRecord(generateFee, exchangeRate, 1);
                PaymentRecord ppk = generatePaymentFeeService.selectRecordIdByDetailIdAndFeeId(generateFee.getEawbPrintcode(), generateFee.getPsdId(), generateFee.getPdSyscode());
                if (ppk != null) {
                    ppk.setPrOccurtime(generateFee.getEbaOccurtime());
                    generatePaymentFeeService.updateTimeAndStatusById(ppk);
                } else {
                    if ("A".equals(generateFee.getPpCate())) {//实际
                        int actualCou = generatePaymentFeeService.existPaymentRecordActualityList(generateFee.getEawbPrintcode(), generateFee.getServiceId(), generateFee.getPdSyscode(), generateFee.getSoCode());
                        if (actualCou == 0) {
                            paymentRecordList.add(pr);
                        }
                    } else {//计划
                        int planCou = generatePaymentFeeService.existPaymentRecordPlanList(generateFee.getEawbPrintcode(), generateFee.getServiceId(), generateFee.getPdSyscode());
                        if (planCou == 0) {
                            paymentRecordList.add(pr);
                        }
                    }
                    if (paymentRecordList.size() >= 1000) {
                        generatePaymentFeeService.insertPaymentRecord(paymentRecordList);
                        paymentRecordList.clear();
                    }
                }
            }
            if (paymentRecordList.size() > 0) {
                generatePaymentFeeService.insertPaymentRecord(paymentRecordList);
            }
        }
        if (jiaohangList.size() > 0) {
            List<PaymentRecord> jLRecordList = new ArrayList<>();
            List<GenerateFee> jhList = new ArrayList<>();
            //费用项去重
            for (int i = 0; i < jiaohangList.size(); i++) {
                GenerateFee pp = jiaohangList.get(i);
                boolean result = true;
                for (GenerateFee generateFee : jhList) {
                    if (generateFee.getEawbPrintcode().equals(pp.getEawbPrintcode()) &&
                            generateFee.getSoCode().equals(pp.getSoCode()) &&
                            generateFee.getPdSyscode().equals(pp.getPdSyscode()) &&
                            generateFee.getServiceId().equals(pp.getServiceId())) {
                        result = false;
                    }
                }
                if (result) {
                    jhList.add(pp);
                }
            }
            for (GenerateFee generateFee : jhList) {
                int actualCou = generatePaymentFeeService.existPaymentRecordActualityList(generateFee.getEawbPrintcode(), generateFee.getServiceId(), generateFee.getPdSyscode(), generateFee.getSoCode());
                if (actualCou > 0) {
                    continue;
                }
                BigDecimal exchangeRate = generatePaymentFeeService.selectExchangeRate(generateFee.getCtCode());
                PaymentRecord pr = convertGenerateFeeToPaymentRecord(generateFee, exchangeRate, 0);
                jLRecordList.add(pr);
                if (jLRecordList.size() >= 1000) {
                    generatePaymentFeeService.insertPaymentRecord(jLRecordList);
                    jLRecordList.clear();
                }
            }
            if (jLRecordList.size() > 0) {
                generatePaymentFeeService.insertPaymentRecord(jLRecordList);
            }
        }

        GeneratePaymentFeeLog updateG=new GeneratePaymentFeeLog();
        updateG.setId(feeLog.getId());
        updateG.setStatus("Y");
        generatePaymentFeeLogMapper.updateByPrimaryKeySelective(updateG);
    }


    public List<GeneratePaymentFeeLog> queryGpfList(){
        return generatePaymentFeeLogMapper.selectDealStatusIsN();
    }

    public List<ExpressBusinessActivity>  selectFixEba(Long beginNo, Long endNo){
        return expressBusinessActivityMapper.selectFixEba(beginNo,endNo);
    }

    public void updateGeneratePaymentFeeLog(GeneratePaymentFeeLog generatePaymentFeeLog){
        generatePaymentFeeLogMapper.updateByPrimaryKeySelective(generatePaymentFeeLog);
    }




}
