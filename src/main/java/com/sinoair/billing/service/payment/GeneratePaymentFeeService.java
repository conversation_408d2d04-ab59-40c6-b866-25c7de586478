package com.sinoair.billing.service.payment;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.sinoair.billing.dao.billing.ExpressBusinessActivityMapper;
import com.sinoair.billing.dao.billing.GeneratePaymentFeeLogMapper;
import com.sinoair.billing.dao.billing.PaymentRecordMapper;
import com.sinoair.billing.dao.billing.ReceiptRecordMapper;
import com.sinoair.billing.domain.model.billing.ExpressBusinessActivity;
import com.sinoair.billing.domain.model.billing.GeneratePaymentFeeLog;
import com.sinoair.billing.domain.model.billing.PaymentRecord;
import com.sinoair.billing.domain.model.billing.ReceiptRecord;
import com.sinoair.billing.domain.vo.price.GenerateFee;
import com.sinoair.billing.domain.vo.price.GenerateReceiptFee;
import com.sinoair.billing.domain.vo.price.RecordTem;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Iterator;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Service("GeneratePaymentFeeService")
public class GeneratePaymentFeeService {

    @Autowired
    private ExpressBusinessActivityMapper expressBusinessActivityMapper;

    @Autowired
    private PaymentRecordMapper paymentRecordMapper;

    @Autowired
    private GeneratePaymentFeeLogMapper generatePaymentFeeLogMapper;

    @Autowired
    private ReceiptRecordMapper receiptRecordMapper;

    @Autowired
    private RedisTemplate<String,String> redisTemplate;

    public List<ExpressBusinessActivity> selectsysCodeList(Long beginNo){
        return expressBusinessActivityMapper.selectsysCodeList(beginNo);
    }

    public List<RecordTem> selectRecordCountByServiceId(Set<Integer> serviceList){
        return paymentRecordMapper.selectRecordCountByServiceId(serviceList);
    }

    public List<GenerateFee> generateFeeList(String ebaSyscode, String eawbPrintCode){
        return expressBusinessActivityMapper.generateFeeList(ebaSyscode,eawbPrintCode);
    }

    public void insertPaymentRecord(List<PaymentRecord> paymentRecordList){
        Iterator<PaymentRecord> iterator=paymentRecordList.iterator();
        while (iterator.hasNext()){
            PaymentRecord paymentRecord=iterator.next();
            String value=redisTemplate.opsForValue().get(paymentRecord.getEawbPrintcode()+"|"+paymentRecord.getSoCode()+"|"+paymentRecord.getPdSyscode()+"|"+paymentRecord.getServiceId()+"|PR");
            if(StringUtils.isEmpty(value)){
                redisTemplate.opsForValue().set(paymentRecord.getEawbPrintcode()+"|"+paymentRecord.getSoCode()+"|"+paymentRecord.getPdSyscode()+"|"+paymentRecord.getServiceId()+"|PR",paymentRecord.getEawbPrintcode(),50,TimeUnit.MINUTES);
            }else{
                iterator.remove();
            }

        }
        if(paymentRecordList.size()>0) {
            paymentRecordMapper.insertBatch(paymentRecordList);
        }
    }

    public int existPaymentRecordActualityList(String eawbPrintcode,Integer serviceId,Integer pdSyscode,String soCode){
        return paymentRecordMapper.existPaymentRecordActualityList(eawbPrintcode,serviceId,pdSyscode,soCode);
    }
    public int existPaymentRecordPlanList(String eawbPrintcode,Integer serviceId,Integer pdSyscode){
        return paymentRecordMapper.existPaymentRecordPlanList(eawbPrintcode,serviceId,pdSyscode);
    }
    public List<GenerateFee> generateFeeByEba(ExpressBusinessActivity expressBusinessActivity){
        return expressBusinessActivityMapper.generateFeeByEba(expressBusinessActivity);
    }

    public PaymentRecord selectRecordIdByDetailIdAndFeeId(String eawbPrintcode,Integer detailId,Integer pdSyscode){
        return paymentRecordMapper.selectRecordIdByDetailIdAndFeeId(eawbPrintcode,detailId,pdSyscode);
    }

    public void updateTimeAndStatusById(PaymentRecord record){
        paymentRecordMapper.updateTimeAndStatusById(record);
    }


    public BigDecimal selectExchangeRate(String ctCode){
        return expressBusinessActivityMapper.selectExchangeRate(ctCode);
    }

    public BigDecimal selectMaxBillingSyscodeByBeginNo(Long beginNo){
        return expressBusinessActivityMapper.selectMaxBillingSyscodeByBeginNo(beginNo);
    }

    public void insertPaymentFeeLog(GeneratePaymentFeeLog feeLog){
        generatePaymentFeeLogMapper.insertSelective(feeLog);
    }

    public List<GenerateReceiptFee> generateReceiptFeeList(String ebaSyscode, String eawbPrintCode){
        return expressBusinessActivityMapper.generateReceiptFeeList(ebaSyscode,eawbPrintCode);
    }

    public List<GenerateReceiptFee> generateReceiptFeeByEba(ExpressBusinessActivity expressBusinessActivity){
        return expressBusinessActivityMapper.generateReceiptFeeByEba(expressBusinessActivity);
    }

    public List<ReceiptRecord> queryReceiptByCondition(String eawbPrintCode,Integer prId,Integer pdSyscode){
        return receiptRecordMapper.queryByCondition(eawbPrintCode,prId,pdSyscode);
    }

    public void updateTimeAndStatusByRrId(ReceiptRecord rr){
        receiptRecordMapper.updateTimeAndStatusByRrId(rr.getRrId(),rr.getRrOccurtime());
    }

    /**
     * 根据运单号列表查询轨迹数据
     * @param eawbPrintcodes 运单号列表
     * @return 轨迹数据列表
     */
    public List<ExpressBusinessActivity> selectByEawbPrintcodes(List<String> eawbPrintcodes){
        return expressBusinessActivityMapper.selectByEawbPrintcodes(eawbPrintcodes);
    }

    public BigDecimal selectSequence(){
        return receiptRecordMapper.selectSequence();
    }
    public void batchInsertRceiptRecord(List<ReceiptRecord> recordList){
        Iterator<ReceiptRecord> iterator=recordList.iterator();
        while (iterator.hasNext()){
            ReceiptRecord paymentRecord=iterator.next();
            String value=redisTemplate.opsForValue().get(paymentRecord.getEawbPrintcode()+"|"+paymentRecord.getSoCode()+"|"+paymentRecord.getPdSyscode()+"|"+paymentRecord.getPrId()+"|RR");
            if(StringUtils.isEmpty(value)){
                redisTemplate.opsForValue().set(paymentRecord.getEawbPrintcode()+"|"+paymentRecord.getSoCode()+"|"+paymentRecord.getPdSyscode()+"|"+paymentRecord.getPrId()+"|RR",paymentRecord.getEawbPrintcode(),50,TimeUnit.MINUTES);
            }else{
                iterator.remove();
            }
        }
        receiptRecordMapper.receiptRecordBatch(recordList);
    }

    public int existReceiptRecordList(String eawbPrintcode,Integer rpId,Integer pdsyscode,String soCode){
        return receiptRecordMapper.existReceiptRecordList(eawbPrintcode,rpId,pdsyscode,soCode);
    }


}
