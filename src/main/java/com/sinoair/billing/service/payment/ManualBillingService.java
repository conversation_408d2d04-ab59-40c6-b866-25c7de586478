package com.sinoair.billing.service.payment;

import com.alibaba.druid.util.StringUtils;
import com.sinoair.billing.core.util.SpringContextUtil;
import com.sinoair.billing.domain.model.billing.ExpressBusinessActivity;
import com.sinoair.billing.domain.model.billing.GeneratePaymentFeeLog;
import com.sinoair.billing.domain.vo.price.GenerateFee;
import com.sinoair.billing.domain.vo.price.GenerateReceiptFee;
import com.sinoair.billing.thread.GeneratePaymentFeeThread;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * 手动计费服务
 * 用于处理遗漏的计费数据
 */
@Service("ManualBillingService")
public class ManualBillingService {

    private Logger logger = LoggerFactory.getLogger(ManualBillingService.class);

    @Autowired
    private GeneratePaymentFeeService generatePaymentFeeService;

    @Autowired
    @Qualifier("DruidDataSource")
    private DataSource dataSource;

    /**
     * 根据运单号列表进行手动计费
     * @param eawbPrintcodes 运单号列表
     * @param batchSize 批处理大小，默认500
     * @return 处理结果
     */
    public String manualBillingByEawbPrintcodes(List<String> eawbPrintcodes, Integer batchSize) {
        if (eawbPrintcodes == null || eawbPrintcodes.isEmpty()) {
            logger.warn("运单号列表为空，无需处理");
            return "运单号列表为空，无需处理";
        }

        if (batchSize == null || batchSize <= 0) {
            batchSize = 500; // 默认批处理大小
        }

        long startTime = System.currentTimeMillis();
        logger.info("开始手动计费，总运单数：{}", eawbPrintcodes.size());

        // 打印数据库连接信息
        printDatabaseInfo();

        try {
            // 分批处理运单号列表
            List<List<String>> batches = splitList(eawbPrintcodes, batchSize);
            int totalProcessed = 0;
            int totalSuccess = 0;
            int totalFailed = 0;

            for (int i = 0; i < batches.size(); i++) {
                List<String> batch = batches.get(i);
                logger.info("处理第{}批，运单数：{}", i + 1, batch.size());

                try {
                    // 查询轨迹数据
                    List<ExpressBusinessActivity> ebaList = generatePaymentFeeService.selectByEawbPrintcodes(batch);
                    logger.info("第{}批查询到轨迹数据：{}条", i + 1, ebaList.size());

                    if (ebaList.isEmpty()) {
                        logger.warn("第{}批未查询到轨迹数据", i + 1);
                        continue;
                    }

                    // 执行计费
                    int batchResult = processBillingBatch(ebaList, i + 1);
                    totalProcessed += ebaList.size();
                    totalSuccess += batchResult;
                    totalFailed += (ebaList.size() - batchResult);

                    // 记录日志
                    GeneratePaymentFeeLog feeLog = new GeneratePaymentFeeLog();
                    feeLog.setBeginNum(new BigDecimal(i * batchSize));
                    feeLog.setEndNum(new BigDecimal((i + 1) * batchSize));
                    feeLog.setCreateTime(new Date());
                    feeLog.setRemark("手动计费批次" + (i + 1) + "，处理运单数：" + ebaList.size());
                    feeLog.setStatus("COMPLETED");
                    generatePaymentFeeService.insertPaymentFeeLog(feeLog);

                } catch (Exception e) {
                    logger.error("第{}批处理异常：{}", i + 1, e.getMessage(), e);
                    totalFailed += batch.size();
                }
            }

            long endTime = System.currentTimeMillis();
            String result = String.format("手动计费完成，总运单数：%d，处理成功：%d，处理失败：%d，耗时：%d毫秒",
                    eawbPrintcodes.size(), totalSuccess, totalFailed, (endTime - startTime));
            logger.info(result);
            return result;

        } catch (Exception e) {
            logger.error("手动计费异常：{}", e.getMessage(), e);
            return "手动计费异常：" + e.getMessage();
        }
    }

    /**
     * 处理单个批次的计费
     * @param ebaList 轨迹数据列表
     * @param batchNum 批次号
     * @return 成功处理的数量
     */
    private int processBillingBatch(List<ExpressBusinessActivity> ebaList, int batchNum) {
        try {
            // 设置默认的产品环节代码
            for (ExpressBusinessActivity eba : ebaList) {
                if (StringUtils.isEmpty(eba.getpEadcode())) {
                    eba.setpEadcode("INTERNATIONAL");
                    eba.setpEastcode("ASS");
                }
            }

            // 使用多线程处理，每个线程处理100条数据
            int threadSize = 100;
            int threadNum = ebaList.size() / threadSize;
            if (ebaList.size() % threadSize != 0) {
                threadNum = threadNum + 1;
            }

            CountDownLatch latch = new CountDownLatch(threadNum);
            
            for (int i = 0; i < threadNum; i++) {
                int startIndex = i * threadSize;
                int endIndex = Math.min((i + 1) * threadSize, ebaList.size());
                List<ExpressBusinessActivity> subList = ebaList.subList(startIndex, endIndex);
                
                new GeneratePaymentFeeThread(i, threadSize, latch, subList).start();
            }

            // 等待所有线程完成
            latch.await();
            logger.info("批次{}计费完成，处理轨迹数：{}", batchNum, ebaList.size());
            return ebaList.size();

        } catch (Exception e) {
            logger.error("批次{}计费异常：{}", batchNum, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 将列表分割成指定大小的批次
     * @param list 原始列表
     * @param batchSize 批次大小
     * @return 分割后的批次列表
     */
    private <T> List<List<T>> splitList(List<T> list, int batchSize) {
        List<List<T>> batches = new ArrayList<>();
        for (int i = 0; i < list.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, list.size());
            batches.add(new ArrayList<>(list.subList(i, endIndex)));
        }
        return batches;
    }

    /**
     * 从Excel文件读取运单号列表（简化版本，实际使用时需要根据具体的Excel格式调整）
     * @param filePath Excel文件路径
     * @return 运单号列表
     */
    public List<String> readEawbPrintcodesFromExcel(String filePath) {
        // 这里是一个简化的实现，实际使用时需要使用POI等库来读取Excel
        // 假设Excel第一列是运单号
        List<String> eawbPrintcodes = new ArrayList<>();
        
        // TODO: 实现Excel读取逻辑
        // 可以使用Apache POI库来读取Excel文件
        
        logger.info("从Excel文件读取运单号：{}", filePath);
        return eawbPrintcodes;
    }

    /**
     * 验证运单号是否存在轨迹数据
     * @param eawbPrintcodes 运单号列表
     * @return 验证结果
     */
    public Map<String, Object> validateEawbPrintcodes(List<String> eawbPrintcodes) {
        Map<String, Object> result = new HashMap<>();
        
        if (eawbPrintcodes == null || eawbPrintcodes.isEmpty()) {
            result.put("valid", false);
            result.put("message", "运单号列表为空");
            return result;
        }

        try {
            // 打印数据库连接信息
            printDatabaseInfo();

            logger.info("开始验证运单号，输入运单数量: {}", eawbPrintcodes.size());
            logger.info("前5个运单号示例: {}", eawbPrintcodes.subList(0, Math.min(5, eawbPrintcodes.size())));

            List<ExpressBusinessActivity> ebaList = generatePaymentFeeService.selectByEawbPrintcodes(eawbPrintcodes);

            logger.info("查询到的轨迹数据数量: {}", ebaList.size());
            if (!ebaList.isEmpty()) {
                logger.info("前5个查询结果示例: {}",
                    ebaList.subList(0, Math.min(5, ebaList.size())).stream()
                        .map(eba -> eba.getEawbPrintcode() + "(" + eba.getEbaSyscode() + ")")
                        .collect(Collectors.toList()));
            }

            Set<String> foundEawbCodes = ebaList.stream()
                    .map(ExpressBusinessActivity::getEawbPrintcode)
                    .collect(Collectors.toSet());

            List<String> notFoundEawbCodes = eawbPrintcodes.stream()
                    .filter(code -> !foundEawbCodes.contains(code))
                    .collect(Collectors.toList());
            
            result.put("valid", true);
            result.put("totalInput", eawbPrintcodes.size());
            result.put("foundCount", foundEawbCodes.size());
            result.put("notFoundCount", notFoundEawbCodes.size());
            result.put("notFoundEawbCodes", notFoundEawbCodes);
            result.put("message", String.format("验证完成，输入%d个运单号，找到%d个有轨迹数据，%d个未找到", 
                    eawbPrintcodes.size(), foundEawbCodes.size(), notFoundEawbCodes.size()));
            
        } catch (Exception e) {
            logger.error("验证运单号异常：{}", e.getMessage(), e);
            result.put("valid", false);
            result.put("message", "验证异常：" + e.getMessage());
        }

        return result;
    }

    /**
     * 打印数据库连接信息
     */
    private void printDatabaseInfo() {
        try {
            // 尝试通过Spring上下文获取DataSource
            DataSource ds = null;
            try {
                ds = dataSource;
            } catch (Exception e) {
                // 如果注入失败，尝试从Spring上下文获取
                try {
                    ds = SpringContextUtil.getBean("DruidDataSource");
                } catch (Exception ex) {
                    logger.warn("无法获取DruidDataSource，尝试获取其他DataSource");
                    System.out.println("警告：无法获取DruidDataSource");
                }
            }

            if (ds != null) {
                try (Connection connection = ds.getConnection()) {
                    DatabaseMetaData metaData = connection.getMetaData();

                    String url = metaData.getURL();
                    String userName = metaData.getUserName();
                    String productName = metaData.getDatabaseProductName();

                    logger.info("=== 数据库连接信息 ===");
                    logger.info("数据库URL: {}", url);
                    logger.info("数据库用户名: {}", userName);
                    logger.info("数据库产品名称: {}", productName);
                    logger.info("===================");

                    // 同时输出到控制台
                    System.out.println("=== 数据库连接信息 ===");
                    System.out.println("数据库URL: " + url);
                    System.out.println("数据库用户名: " + userName);
                    System.out.println("数据库产品名称: " + productName);
                    System.out.println("===================");

                    // 从URL中提取更多信息
                    if (url != null) {
                        if (url.contains("localhost") || url.contains("127.0.0.1")) {
                            System.out.println("注意：连接的是本地数据库！");
                        } else if (url.contains("test") || url.contains("dev")) {
                            System.out.println("注意：连接的可能是测试数据库！");
                        } else {
                            System.out.println("连接的数据库URL看起来是生产环境");
                        }
                    }

                } catch (Exception e) {
                    logger.error("获取数据库元数据失败: {}", e.getMessage());
                    System.out.println("获取数据库元数据失败: " + e.getMessage());
                }
            } else {
                System.out.println("无法获取DataSource，跳过数据库连接信息打印");
            }

        } catch (Exception e) {
            logger.error("获取数据库连接信息失败: {}", e.getMessage(), e);
            System.out.println("获取数据库连接信息失败: " + e.getMessage());
        }
    }
}
