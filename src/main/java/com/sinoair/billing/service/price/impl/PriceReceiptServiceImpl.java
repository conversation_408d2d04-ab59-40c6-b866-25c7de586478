package com.sinoair.billing.service.price.impl;


import com.sinoair.billing.dao.billing.PriceGradeMapper;
import com.sinoair.billing.dao.billing.PriceReceiptMapper;
import com.sinoair.billing.domain.vo.price.PriceGradeVO;
import com.sinoair.billing.domain.vo.price.PriceReceiptVO;
import com.sinoair.billing.service.price.PriceReceiptService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: 大雄
 * @date: 2018/1/30
 * @time: 9:59
 * @description: To change this template use File | Settings | File Templates.
 */
@Service("PriceReceiptService")
public class PriceReceiptServiceImpl implements PriceReceiptService {

    @Autowired
    private PriceReceiptMapper priceReceiptMapper;

    @Autowired
    private PriceGradeMapper gradeMapper;

    @Override
    public List<PriceReceiptVO> selectPriceReceipt(){
        List<PriceReceiptVO> priceReceiptList = priceReceiptMapper.selectPriceReceipt();
        if (priceReceiptList != null && priceReceiptList.size() > 0){
            for (PriceReceiptVO priceReceiptVO : priceReceiptList){
                if ("GRADE".equals(priceReceiptVO.getPrType())){
                    List<PriceGradeVO> priceGradeList = gradeMapper.selectPriceGradeByPrId(priceReceiptVO.getPrId());
                    priceReceiptVO.setPriceGradeList(priceGradeList);
                }
            }
        }
        return priceReceiptList;
    }

}
