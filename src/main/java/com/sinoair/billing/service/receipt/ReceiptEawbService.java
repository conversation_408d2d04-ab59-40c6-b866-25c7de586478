package com.sinoair.billing.service.receipt;

import com.sinoair.billing.domain.model.billing.ReceiptEawb;
import com.sinoair.billing.domain.model.billing.RecordTask;
import com.sinoair.billing.domain.vo.check.EawbCheckVO;
import com.sinoair.billing.domain.vo.query.InsRecordQuery;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: 大雄
 * @date: 2018/1/30
 * @time: 9:52
 * @description: To change this template use File | Settings | File Templates.
 */
public interface ReceiptEawbService {


    Long selectMaxReId();

    List<ReceiptEawb> listReceiptEawb(EawbCheckVO param);


    Long selectMaxEbaCode();

    List<ReceiptEawb> listEba(EawbCheckVO param);


}
