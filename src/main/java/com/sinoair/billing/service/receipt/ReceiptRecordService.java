package com.sinoair.billing.service.receipt;

import com.sinoair.billing.domain.model.billing.DebitManifestTemporary;
import com.sinoair.billing.domain.model.billing.ReceiptEawb;
import com.sinoair.billing.domain.model.billing.ReceiptRecord;
import com.sinoair.billing.domain.model.billing.ReceiptRecordTmp;
import com.sinoair.billing.domain.vo.check.EawbCheckVO;
import com.sinoair.billing.domain.vo.query.CeosQuery;
import com.sinoair.billing.domain.vo.query.CommonQuery;
import com.sinoair.billing.domain.vo.query.InsRecordQuery;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: 大雄
 * @date: 2018/1/30
 * @time: 9:52
 * @description: To change this template use File | Settings | File Templates.
 */
public interface ReceiptRecordService {

    /**
     * 查询序列
     * @return
     */
    Integer selectSeq();

    Long selectMaxRrId();

    List<ReceiptRecord> selectListByRrId(CeosQuery ceosQuery);

    List<ReceiptRecord> selectListByDate(CommonQuery commonQuery);

    void updateBatchAppend(@Param(value = "list") List list);

    void updateBatchDmTempId(@Param(value = "list") List list);

    BigDecimal selectTotalWeightByDmId(@Param("dmId") Integer dmId);
    BigDecimal selectSumActualAmount(@Param("dmId") Integer dmId);

    DebitManifestTemporary selectCollect(@Param("dmId") Integer dmId);

    //非菜鸟应收
    List<ReceiptRecordTmp> getRRNonList(String eawbPrintcode,String prName);
    //非菜鸟——非环节计费
    List<ReceiptRecordTmp> getRRNonList_NC(String eawbPrintcode,String prName);

    List<ReceiptEawb> selectBillListByRrId(EawbCheckVO param);
    List<ReceiptEawb> selectBillListInnerByRrId(EawbCheckVO param);

    List<ReceiptRecordTmp> getRRInnerList(String eawbPrintcode,String soCode,String pCode);

    List<ReceiptRecordTmp> getRRInnerListNew(String eawbPrintcode,String soCode,String pCode);

    void saveInnerReceipts(List<ReceiptRecordTmp> receiptRecords);

    void saveInnerReceiptsNew(List<ReceiptRecordTmp> receiptRecords);




}
