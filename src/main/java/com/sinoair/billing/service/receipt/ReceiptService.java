package com.sinoair.billing.service.receipt;

import com.sinoair.billing.domain.model.billing.DebitManifest;
import com.sinoair.billing.domain.model.billing.ReceiptEawb;
import com.sinoair.billing.domain.model.billing.SettlementObject;
import com.sinoair.billing.domain.vo.check.EawbCheckVO;

import java.util.List;
import java.util.Map;

/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2018/1/30
 * @time: 9:52
 * @description: To change this template use File | Settings | File Templates.
 */
public interface ReceiptService {

    Integer insertDebitManifest(DebitManifest debitManifest);

    void generateMonthLyDebitManifest();

    void generateWeekDebitManifest();


    void syncAppRecord();



}
