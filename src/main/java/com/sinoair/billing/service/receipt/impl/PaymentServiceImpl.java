package com.sinoair.billing.service.receipt.impl;


import com.alibaba.fastjson.JSON;
import com.sinoair.billing.core.util.DateUtil;
import com.sinoair.billing.dao.billing.*;
import com.sinoair.billing.domain.model.billing.CreditManifest;
import com.sinoair.billing.domain.model.billing.DebitManifest;
import com.sinoair.billing.domain.model.billing.SerialNO;
import com.sinoair.billing.domain.vo.receipt.ReceiptVo;
import com.sinoair.billing.service.receipt.PaymentService;
import com.sinoair.billing.service.receipt.ReceiptService;
import com.sinoair.billing.service.receipt.SerialNOService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2018/1/30
 * @time: 9:59
 * @description: To change this template use File | Settings | File Templates.
 */
@Service("PaymentService")
public class PaymentServiceImpl implements PaymentService {

    private Logger log = LoggerFactory.getLogger(PaymentServiceImpl.class);

    private static final String EAWBIETYPE_E="E";//出口

    @Resource(name = "CreditManifestMapper")
    private CreditManifestMapper creditManifestMapper;

    @Resource(name = "PaymentRecordMapper")
    private PaymentRecordMapper paymentRecordMapper;

    @Autowired
    private SerialNOService serialNOService;

    /**
     * 1 insert credit_manifest  cm_code规则
     * 2 update payment_record set cm_id
     * @param creditManifest
     * @return cm_id
     */
    @Override
    public Integer insertCreditManifest(CreditManifest creditManifest) {

        Map map = new HashMap();
//        map.put("sacId", creditManifest.getCompanyId());
        map.put("sacId", "ZSU");
        map.put("soCode", creditManifest.getSoCode());
        map.put("rrUserId", creditManifest.getCmUserId());
        map.put("rrHandletime", DateUtil.getNowDateTime());
        map.put("startOccurtime", DateUtil.date2str(creditManifest.getCmStartTime(),DateUtil.YYYY_MM_DD));
        map.put("endOccurtime", DateUtil.date2str(creditManifest.getCmEndTime(),DateUtil.YYYY_MM_DD));
        map.put("ieType", "E");
        map.put("ctCode", creditManifest.getCtCode());
        try {
            int sizeId = paymentRecordMapper.getCMByOccurtime(map);

            if (sizeId != 0) {
                SerialNO serialNO = new SerialNO();
                serialNO.setSnPurpose(SerialNO.SNPURPOSE_CM);
                BigDecimal serialNumber = serialNOService.selectCurrentNo(serialNO,1);
                String currentNO = DateUtil.getYearMonthDay() + String.format("%04d",serialNumber.intValue());
                String cmCode= creditManifest.getCompanyId() + currentNO + "BC"+EAWBIETYPE_E;

                creditManifest.setCmCode(cmCode);
                log.info("应付账单管理-统计所需生成账单的个数 sizeId:"+sizeId);
                log.info("应付账单管理-执行添加账单 账单号 dmCode: "+creditManifest.getCmCode());
                creditManifestMapper.insertSelective(creditManifest);
                log.info("应付账单管理-添加账单 "+creditManifest.getCmCode()+" 完成,对应dmId:"+creditManifest.getCmId());
                map.put("cmId", creditManifest.getCmId());

                log.info("应付账单管理-批量更新执行完成");

                //2.建立关系
                log.info("应付账单管理-执行批量更新 dmId:"+creditManifest.getCmId());
                paymentRecordMapper.updateCM(map);


                //3.统计应收详细表总金额，总票数，总重量
                log.info("应付账单管理-执行统计费用总票数，总金额，总重量");
                Map<String, Object> numMap = paymentRecordMapper.countByCM(creditManifest.getCmId());
                creditManifest.setCmPlanAmount(new BigDecimal(nullTozero(String.valueOf(numMap.get("PR_PLAN_AMOUNT_COUNT")))));
                creditManifest.setCmTotalfc(new BigDecimal(nullTozero(String.valueOf(numMap.get("PR_ACTUAL_AMOUNT_COUNT")))));
                creditManifest.setCmTotalrmb(new BigDecimal(nullTozero(String.valueOf(numMap.get("PR_ACTUAL_AMOUNT_COUNT")))));
                creditManifest.setCmTotalPieces(Integer.parseInt(nullTozero(String.valueOf(numMap.get("EAWB_HAWB_QTY_COUNT")))));
                creditManifest.setCmTotalWeight(new BigDecimal(nullTozero(String.valueOf(numMap.get("EAWB_CHARGEABLEWEIGHT_COUNT")))));

                //4.更新账单表 总金额，总票数，总重量
                creditManifestMapper.updateByPrimaryKeySelective(creditManifest);
                log.info("应付账单管理-更新统计费用总票数，总金额，总重量结束");


                log.info("应付账单管理-生成账单成功");




            } else {
                log.info("应付账单管理-没有找到生成账单的数据的");

            }
        } catch (Exception e) {
            log.error("应付账单管理-生成账单异常",e);
            log.info("应付账单管理-异常的账单id:"+creditManifest.getCmId()+" dmCode:"+creditManifest.getCmCode());
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return null;

        }
        return creditManifest.getCmId();

    }


    private String nullTozero(String str) {
        if (str == null || "".equals(str) || "null".equals(str)) {
            return "0";
        } else {
            return str;
        }
    }

}
