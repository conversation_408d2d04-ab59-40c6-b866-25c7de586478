package com.sinoair.billing.service.receipt.impl;


import com.sinoair.billing.dao.billing.ExpressBusinessActivityMapper;
import com.sinoair.billing.dao.billing.PaymentRecordMapper;
import com.sinoair.billing.dao.billing.ReceiptEawbMapper;
import com.sinoair.billing.domain.model.billing.ReceiptEawb;
import com.sinoair.billing.domain.vo.check.EawbCheckVO;
import com.sinoair.billing.service.receipt.ReceiptEawbService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2018/1/30
 * @time: 9:59
 * @description: To change this template use File | Settings | File Templates.
 */
@Service("ReceiptEawbService")
public class ReceiptEawbServiceImpl implements ReceiptEawbService {

    private Logger logger = LoggerFactory.getLogger(ReceiptEawbServiceImpl.class);

    @Autowired
    private ReceiptEawbMapper receiptEawbMapper;

    @Autowired
    private ExpressBusinessActivityMapper activityMapper;


    @Override
    public Long selectMaxReId(){
        return receiptEawbMapper.selectMaxRrId();
    }

    @Override
    public List<ReceiptEawb> listReceiptEawb(EawbCheckVO param){
        return receiptEawbMapper.listReceiptEawb_rr(param);
    }

    @Override
    public Long selectMaxEbaCode() {
        return activityMapper.selectMaxEbaCode();
    }

    @Override
    public List<ReceiptEawb> listEba(EawbCheckVO param) {
        return activityMapper.selectActivityList(param);
    }
}
