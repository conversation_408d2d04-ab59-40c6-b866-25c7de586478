package com.sinoair.billing.service.receipt.impl;


import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.sinoair.billing.core.util.CheckUtil;
import com.sinoair.billing.core.util.DateUtil;
import com.sinoair.billing.core.util.MailUtil;
import com.sinoair.billing.core.util.StringUtil;
import com.sinoair.billing.dao.billing.*;
import com.sinoair.billing.domain.constant.EastEnum;
import com.sinoair.billing.domain.constant.EbaConstant;
import com.sinoair.billing.domain.constant.MailConstant;
import com.sinoair.billing.domain.constant.ReceiptRecordConstant;
import com.sinoair.billing.domain.model.billing.*;
import com.sinoair.billing.domain.vo.check.EawbCheckVO;
import com.sinoair.billing.domain.vo.query.CeosQuery;
import com.sinoair.billing.domain.vo.query.CommonQuery;
import com.sinoair.billing.domain.vo.query.InsRecordQuery;
import com.sinoair.billing.service.receipt.ReceiptRecordService;
import org.apache.ibatis.annotations.Param;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.Comparator.comparing;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;

/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2018/1/30
 * @time: 9:59
 * @description: To change this template use File | Settings | File Templates.
 */
@Service("ReceiptRecordService")
public class ReceiptRecordServiceImpl implements ReceiptRecordService {

    private Logger logger = LoggerFactory.getLogger(ReceiptRecordServiceImpl.class);

    @Autowired
    private ReceiptRecordTmpMapper receiptRecordTmpMapper;

    @Autowired
    private InsReceiptRecordMapper insReceiptRecordMapper;

    @Autowired
    private RecordLogMapper recordLogMapper;

    @Autowired
    private ReceiptRecordMapper receiptRecordMapper;

    @Autowired
    private PaymentRecordMapper paymentRecordMapper;

    @Autowired
    private TempMapper tempMapper;

    @Resource(name = "transactionManager")
    private PlatformTransactionManager platformTransactionManager;

    private BigDecimal zero = new BigDecimal(0);

    private int length = 1000;

    @Override
    public Integer selectSeq(){
        return recordLogMapper.selectSeq();
    }

    @Override
    public Long selectMaxRrId(){
        return receiptRecordMapper.selectMaxRrId();
    }

    @Override
    public List<ReceiptRecord> selectListByRrId(CeosQuery ceosQuery){
        return receiptRecordMapper.selectListByRrId(ceosQuery);
    }

    @Override
    public List<ReceiptRecord> selectListByDate(CommonQuery commonQuery){
        return receiptRecordMapper.selectListByDate(commonQuery);
    }

    @Override
    public     void updateBatchAppend(@Param(value = "list") List list){
        receiptRecordMapper.updateBatchAppend(list);
    }

    @Override
    public void updateBatchDmTempId(@Param(value = "list") List list){
        receiptRecordMapper.updateBatchDmTempId(list);
    }

    @Override
    public     BigDecimal selectTotalWeightByDmId(@Param("dmId") Integer dmId){
        return receiptRecordMapper.selectTotalWeightByDmId(dmId);
    }
    @Override
    public     BigDecimal selectSumActualAmount(@Param("dmId") Integer dmId){
        return receiptRecordMapper.selectSumActualAmount(dmId);
    }

    @Override
    public DebitManifestTemporary selectCollect(@Param("dmId") Integer dmId){
        return receiptRecordMapper.selectCollect(dmId);
    }

    @Override
    public List<ReceiptRecordTmp> getRRNonList(String eawbPrintcode, String prName) {
        List<ReceiptRecordTmp> tmpMdList =  receiptRecordTmpMapper.listNonPriceReceipt_other(eawbPrintcode,prName);
        List<ReceiptRecordTmp> mdList = tmpMdList.stream().filter(s->(!CheckUtil.checkNotCnServiceType(s.getEpKey())) && s.getRrPlanAmount() != null && s.getRrPlanAmount().compareTo(BigDecimal.ZERO) >0).collect(Collectors.toList());
        return mdList;
    }

    @Override
    public List<ReceiptRecordTmp> getRRNonList_NC(String eawbPrintcode, String prName) {
        List<ReceiptRecordTmp> tmpMdList =  receiptRecordTmpMapper.listNonPriceReceipt_other_nc(eawbPrintcode,prName);
        List<ReceiptRecordTmp> mdList = tmpMdList.stream().filter(s->(!CheckUtil.checkNotCnServiceType(s.getEpKey())) && s.getRrPlanAmount() != null && s.getRrPlanAmount().compareTo(BigDecimal.ZERO) >0).collect(Collectors.toList());
        return mdList;
    }

    @Override
    public List<ReceiptEawb> selectBillListByRrId(EawbCheckVO param){
        return receiptRecordMapper.selectBillListByRrId(param);
    }

    @Override
    public List<ReceiptEawb> selectBillListInnerByRrId(EawbCheckVO param) {
        return receiptRecordMapper.selectBillListInnerByRrId(param);
    }

    @Override
    public List<ReceiptRecordTmp> getRRInnerList(String eawbPrintcode,String soCode,String pCode) {
        List<ReceiptRecordTmp> tmpMdList =  receiptRecordTmpMapper.listInnerPriceReceipt(eawbPrintcode,soCode,pCode);
        List<ReceiptRecordTmp> mdList = tmpMdList.stream().filter(s-> s.getRrPlanAmount() != null && s.getRrPlanAmount().compareTo(BigDecimal.ZERO) >0).collect(Collectors.toList());
        return mdList;
    }

    @Override
    public List<ReceiptRecordTmp> getRRInnerListNew(String eawbPrintcode, String soCode, String pCode) {
        List<ReceiptRecordTmp> tmpMdList =  receiptRecordTmpMapper.listInnerPriceReceiptNew(eawbPrintcode,soCode,pCode);
        List<ReceiptRecordTmp> mdList = tmpMdList.stream().filter(s-> s.getRrPlanAmount() != null && s.getRrPlanAmount().compareTo(BigDecimal.ZERO) >0).collect(Collectors.toList());
        return mdList;
    }

    @Transactional
    @Override
    public void saveInnerReceipts(List<ReceiptRecordTmp> receiptRecords) {
        List<ReceiptRecordTmp> addRrList = new ArrayList<>();
        List<PaymentRecord> addPrList = new ArrayList<>();



        for (ReceiptRecordTmp receipt : receiptRecords){
            if (receiptRecordMapper.countByPdSyscode(receipt)==0) {
                receiptRecordMapper.insertSelectiveTmp(receipt);
//            if (receiptRecordMapper.countByPdSyscode(receipt) == 0){
//                addRrList.add(receipt);
//            }
            }
        }
        List<PaymentRecord> paymentRecords = convertPayments(receiptRecords);
        for (PaymentRecord tmp : paymentRecords){
            if (paymentRecordMapper.countPaymentRecord(tmp) == 0){
                addPrList.add(tmp);
            }
        }

//        if (addRrList.size() > 0){
//            logger.info("新增rr数据："+addRrList.size());
//            int size = addRrList.size();
//            int count = size % length != 0 ? size / length + 1 : size / length;
//            for (int i = 0; i < count; i++) {
//                List<ReceiptRecordTmp> subList = addRrList.subList(i * length, ((length + i * length) < size ? (length + i * length) : size));
//                if (subList.size() > 0){
//                    receiptRecordMapper.insertBatch(subList);
//                }
//            }
//        }

        if (addPrList.size() > 0){
            logger.info("新增pr数据："+addPrList.size());
            int size = addPrList.size();
            int count = size % length != 0 ? size / length + 1 : size / length;
            for (int i = 0; i < count; i++) {
                List<PaymentRecord> subList = addPrList.subList(i * length, ((length + i * length) < size ? (length + i * length) : size));
                if (subList.size() > 0){
                    paymentRecordMapper.insertBatch(subList);
                }
            }
        }

    }

    @Override
    public void saveInnerReceiptsNew(List<ReceiptRecordTmp> receiptRecords) {
        List<PaymentRecord> addPrList = new ArrayList<>();
        for (ReceiptRecordTmp receipt : receiptRecords){
            receiptRecordMapper.insertSelectiveTmp(receipt);
        }
        List<PaymentRecord> paymentRecords = convertPaymentsNew(receiptRecords);
        for (PaymentRecord tmp : paymentRecords){
            if (paymentRecordMapper.countPaymentRecord(tmp) == 0){
                addPrList.add(tmp);
            }
        }

        if (addPrList.size() > 0){
            logger.info("新增pr数据："+addPrList.size());
            int size = addPrList.size();
            int count = size % length != 0 ? size / length + 1 : size / length;
            for (int i = 0; i < count; i++) {
                List<PaymentRecord> subList = addPrList.subList(i * length, ((length + i * length) < size ? (length + i * length) : size));
                if (subList.size() > 0){
                    paymentRecordMapper.insertBatch(subList);
                }
            }
        }
    }

    private List<PaymentRecord> convertPayments(List<ReceiptRecordTmp> receiptRecords){
        List<PaymentRecord> paymentRecords = new ArrayList<>();
        for (ReceiptRecordTmp receipt : receiptRecords){
            PaymentRecord paymentRecord = convertPayment(receipt);
            paymentRecords.add(paymentRecord);
        }
        return paymentRecords;
    }

    private List<PaymentRecord> convertPaymentsNew(List<ReceiptRecordTmp> receiptRecords){
        List<PaymentRecord> paymentRecords = new ArrayList<>();
        for (ReceiptRecordTmp receipt : receiptRecords){
            PaymentRecord paymentRecord = convertPaymentNew(receipt);
            paymentRecords.add(paymentRecord);
        }
        return paymentRecords;
    }

    private PaymentRecord convertPayment(ReceiptRecordTmp receipt){

        PaymentRecord paymentRecord = new PaymentRecord();
        paymentRecord.setEawbPrintcode(receipt.getEawbPrintcode());
        paymentRecord.setMawbCode(receipt.getMawbCode());
        paymentRecord.setSoCode(receipt.getCompanyId());
        paymentRecord.setCtCode(receipt.getCtCode());
        paymentRecord.setCompanyId(receipt.getSpCompanyId());
        paymentRecord.setPrName(receipt.getRrName());
        paymentRecord.setPrPlanAmount(receipt.getRrPlanAmount());
        paymentRecord.setPrActualAmount(receipt.getRrActualAmount());
        paymentRecord.setPrStatus(receipt.getRrStatus());
        paymentRecord.setPrUserId(receipt.getRrUserId().intValue());
        paymentRecord.setPrHandletime(receipt.getRrHandletime());
        paymentRecord.setEawbReference1(receipt.getEawbReference1());
        paymentRecord.setEawbReference2(receipt.getEawbReference2());
        paymentRecord.setEawbChargeableweight(receipt.getEawbChargeableweight());
        paymentRecord.setPrOccurtime(receipt.getRrOccurtime());
        paymentRecord.setEawbTrackingNo(receipt.getEawbTrackingNo());
        paymentRecord.setEpKey(receipt.getEpKey());
        paymentRecord.setChargeableweight(receipt.getChargeableweight());
        paymentRecord.setPdSyscode(receipt.getPdSyscode());
        paymentRecord.setOutboundCompanyId(receipt.getOutboundCompanyId());
        paymentRecord.setPrCate(EbaConstant.R_TYPE_I);
        paymentRecord.setPrType(EbaConstant.PR_CATE_A);
        paymentRecord.setRrId(receipt.getRrId());
        return paymentRecord;
    }

    private PaymentRecord convertPaymentNew(ReceiptRecordTmp receipt){

        PaymentRecord paymentRecord = new PaymentRecord();
        paymentRecord.setEawbPrintcode(receipt.getEawbPrintcode());
        paymentRecord.setMawbCode(receipt.getMawbCode());
        paymentRecord.setSoCode(receipt.getCompanyId());
        paymentRecord.setCtCode(receipt.getCtCode());
        paymentRecord.setCompanyId(receipt.getSoCode());
        paymentRecord.setPrName(receipt.getRrName());
        paymentRecord.setPrPlanAmount(receipt.getRrPlanAmount());
        paymentRecord.setPrActualAmount(receipt.getRrActualAmount());
        paymentRecord.setPrStatus(receipt.getRrStatus());
        paymentRecord.setPrUserId(receipt.getRrUserId().intValue());
        paymentRecord.setPrHandletime(receipt.getRrHandletime());
        paymentRecord.setEawbReference1(receipt.getEawbReference1());
        paymentRecord.setEawbReference2(receipt.getEawbReference2());
        paymentRecord.setEawbChargeableweight(receipt.getEawbChargeableweight());
        paymentRecord.setPrOccurtime(receipt.getRrOccurtime());
        paymentRecord.setEawbTrackingNo(receipt.getEawbTrackingNo());
        paymentRecord.setEpKey(receipt.getEpKey());
        paymentRecord.setChargeableweight(receipt.getChargeableweight());
        paymentRecord.setPdSyscode(receipt.getPdSyscode());
        paymentRecord.setOutboundCompanyId(receipt.getOutboundCompanyId());
        paymentRecord.setPrCate(EbaConstant.R_TYPE_I);
        paymentRecord.setPrType(EbaConstant.PR_CATE_A);
        paymentRecord.setRrId(receipt.getRrId());
        return paymentRecord;
    }

}
