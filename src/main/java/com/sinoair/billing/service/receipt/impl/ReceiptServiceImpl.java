package com.sinoair.billing.service.receipt.impl;


import com.alibaba.fastjson.JSON;
import com.sinoair.billing.core.util.DateUtil;
import com.sinoair.billing.dao.billing.*;
import com.sinoair.billing.domain.model.billing.*;
import com.sinoair.billing.domain.vo.check.EawbCheckVO;
import com.sinoair.billing.domain.vo.receipt.ReceiptVo;
import com.sinoair.billing.service.receipt.ReceiptEawbService;
import com.sinoair.billing.service.receipt.ReceiptService;
import com.sinoair.billing.service.receipt.SerialNOService;
import org.apache.commons.collections.map.HashedMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2018/1/30
 * @time: 9:59
 * @description: To change this template use File | Settings | File Templates.
 */
@Service("ReceiptService")
public class ReceiptServiceImpl implements ReceiptService {

    private Logger log = LoggerFactory.getLogger(ReceiptServiceImpl.class);

    private static final String EAWBIETYPE_E = "E";//出口

    @Autowired
    private ReceiptEawbMapper receiptEawbMapper;

    @Autowired
    private DebitManifestMapper debitManifestMapper;

    @Resource(name = "ReceiptRecordMapper")
    private ReceiptRecordMapper receiptRecordMapper;
    @Autowired
    private SerialNOService serialNOService;
    @Resource(name = "SettlementObjectMapper")
    private SettlementObjectMapper settlementObjectMapper;

    @Autowired
    private AppReceiptRecordMapper appReceiptRecordMapper;

    @Autowired
    private AppOrderMapper appOrderMapper;

    @Autowired
    private AppDebitManifestMapper appDebitManifestMapper;

    @Autowired
    private ExpressAirWayBillMapper expressAirWayBillMapper;

    @Autowired
    private ProductMapper productMapper;

    @Autowired
    private AppOrderEawbMapper appOrderEawbMapper;



    /**
     * 1 dm_code生成规则，插入debit_manifest
     * 2 update receipt_record set dm_id
     * 3 按dm_id汇总receipt_record  update debit_manifest
     *
     * @param debitManifest
     * @return dm_id
     */
    @Override
    public Integer insertDebitManifest(DebitManifest debitManifest) {

        Map map = new HashMap();
//        map.put("sacId", debitManifest.getCompanyId());
        map.put("sacId", "ZSU");
        map.put("soCode", debitManifest.getSoCode());
        map.put("rrUserId", debitManifest.getDmUserId());
        map.put("rrHandletime", DateUtil.getNowDateTime());
        map.put("startRrOccurtime", DateUtil.date2str(debitManifest.getDmStartTime(), DateUtil.YYYY_MM_DD));
        map.put("endRrOccurtime", DateUtil.date2str(debitManifest.getDmEndTime(), DateUtil.YYYY_MM_DD));
        map.put("ieType", "E");
        map.put("ctCode", debitManifest.getCtCode());
        try {
            //dm_code规则
            SerialNO serialNO = new SerialNO();
            serialNO.setSnPurpose(SerialNO.SNPURPOSE_DM);
            BigDecimal serialNumber = serialNOService.selectCurrentNo(serialNO, 1);
            String currentNO = DateUtil.getYearMonthDay() + String.format("%04d", serialNumber.intValue());
            String dmCode = debitManifest.getCompanyId() + currentNO + "BD" + EAWBIETYPE_E;

            debitManifest.setDmCode(dmCode);
            log.info("应收账单管理-执行添加账单 账单号 dmCode: " + debitManifest.getDmCode());
            //插入 debit_manifest
            debitManifestMapper.insertSelective(debitManifest);
            log.info("应收账单管理-添加账单 " + debitManifest.getDmCode() + " 完成,对应dmId:" + debitManifest.getDmId());
            map.put("dmId", debitManifest.getDmId());

            log.info("应收账单管理-批量更新执行完成");

            ReceiptVo receiptVo = null;
            //2.建立关系
            log.info("应收账单管理-执行批量更新 dmId:" + debitManifest.getDmId());
            //更新receipt_record dm_id
            receiptRecordMapper.updateDmIdByRrId(map);


            //3.统计应收详细表总金额，总票数，总重量
            log.info("应收账单管理-执行统计费用总票数，总金额，总重量");
            receiptVo = debitManifestMapper.getToTalDebitManifestByDmId(debitManifest.getDmId());

            log.info("应收账单管理-查询统计费用总票数，总金额，总重量结束：" + JSON.toJSONString(receiptVo));
            //4.更新账单表 总金额，总票数，总重量
            updateDebitManifestByDmId(receiptVo, debitManifest.getDmUserId(), debitManifest.getCompanyId(), debitManifest.getDmCode());
            log.info("应收账单管理-更新统计费用总票数，总金额，总重量结束");
            log.info("应收账单管理-生成账单成功");

        } catch (Exception e) {
            log.error("应收账单管理-生成账单异常", e);
            log.info("应收账单管理-异常的账单id:" + debitManifest.getDmId() + " dmCode:" + debitManifest.getDmCode());
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return null;

        }
        return debitManifest.getDmId();

    }

    @Transactional
    @Override
    public void generateMonthLyDebitManifest() {
        List<DebitManifest> monthlyDebitManifest = receiptRecordMapper.getMonthlyDebitManifest();
        for (DebitManifest debitManifest : monthlyDebitManifest) {
            SettlementObject settlementObject = settlementObjectMapper.selectBySoCode(debitManifest.getSoCode());
            String sacId = "";
            if (settlementObject != null) {
                sacId = settlementObject.getSacId();
            }
            String dmCode = "";
            SerialNO serialNO = new SerialNO();
            serialNO.setSnPurpose(SerialNO.SNPURPOSE_DM);
            BigDecimal serialNumber = serialNOService.selectCurrentNo(serialNO, 1);
            String currentNO = DateUtil.getYearMonthDay() + String.format("%04d", serialNumber.intValue());
            dmCode = sacId + currentNO + "BD" + "E";
            debitManifest.setDmStatus("DRAFT");
            debitManifest.setDmCode(dmCode);
            debitManifest.setCtCode("142");
            debitManifest.setDmType("BASIC");
            debitManifest.setDmCreateTime(DateUtil.getNowDateTime());
            debitManifest.setDmHandleTime(debitManifest.getDmCreateTime());
            debitManifest.setDmDivideStatus("N");
            debitManifest.setSoMode(settlementObject.getSoMode());
            Map map = new HashedMap();
            map.put("soCode", debitManifest.getSoCode());
            map.put("companyId", debitManifest.getCompanyId());
            map.put("eawbServicetypeOriginal", debitManifest.getEawbServicetypeOriginal());
            BigDecimal totalWeight = receiptRecordMapper.getMonthlyTotalWeight(map);
            debitManifest.setDmTotalWeight(totalWeight);
            debitManifest.setDmRemark("定时任务生成月度账单");
            debitManifestMapper.insertSelective(debitManifest);
            receiptRecordMapper.updateDmIdMonthly(debitManifest);
        }
    }

    @Transactional
    @Override
    public void generateWeekDebitManifest() {
        List<DebitManifest> weekDebitManifest = receiptRecordMapper.getWeekDebitManifest();
        for (DebitManifest debitManifest : weekDebitManifest) {
            SettlementObject settlementObject = settlementObjectMapper.selectBySoCode(debitManifest.getSoCode());
            String sacId = "";
            if (settlementObject != null) {
                sacId = settlementObject.getSacId();
            }
            String dmCode = "";
            SerialNO serialNO = new SerialNO();
            serialNO.setSnPurpose(SerialNO.SNPURPOSE_DM);
            BigDecimal serialNumber = serialNOService.selectCurrentNo(serialNO, 1);
            String currentNO = DateUtil.getYearMonthDay() + String.format("%04d", serialNumber.intValue());
            dmCode = sacId + currentNO + "BD" + "E";
            debitManifest.setDmStatus("DRAFT");
            debitManifest.setDmCode(dmCode);
            debitManifest.setCtCode("142");
            debitManifest.setDmType("BASIC");
            debitManifest.setDmCreateTime(DateUtil.getNowDateTime());
            debitManifest.setDmHandleTime(debitManifest.getDmCreateTime());
            debitManifest.setDmDivideStatus("N");
            debitManifest.setSoMode(settlementObject.getSoMode());
            Map map = new HashedMap();
            map.put("soCode", debitManifest.getSoCode());
            map.put("companyId", debitManifest.getCompanyId());
            map.put("eawbServicetypeOriginal", debitManifest.getEawbServicetypeOriginal());
            BigDecimal totalWeight = receiptRecordMapper.getWeekTotalWeight(map);
            debitManifest.setDmTotalWeight(totalWeight);
            debitManifest.setDmRemark("定时任务生成周度账单");
            debitManifestMapper.insertSelective(debitManifest);
            receiptRecordMapper.updateDmIdWeek(debitManifest);
        }
    }


    public void updateDebitManifestByDmId(ReceiptVo receiptVo, int dmUserId, String companyId, String dmCode) throws Exception {
        DebitManifest debitManifest = new DebitManifest();
        if (receiptVo != null) {
            log.info("应收账单管理-费用统计更新 应收金额:" + receiptVo.getSumRrPlanAmount() +
                    ",实收金额:" + receiptVo.getSumRactualAmount() +
                    ",总票数:" + receiptVo.getCountNum() +
                    ",总重量:" + receiptVo.getSumEawbChargeableweight() +
                    ",DMID:" + debitManifest.getDmId());
            debitManifest.setDmId(receiptVo.getDmId());
            debitManifest.setDmUserId(dmUserId);
            debitManifest.setCompanyId(String.valueOf(companyId));
            debitManifest.setDmPlanAmount(receiptVo.getSumRrPlanAmount());//应收金额
            debitManifest.setDmTotalfc(receiptVo.getSumRactualAmount());//实收金额
            debitManifest.setDmTotalPieces(receiptVo.getCountNum());
            debitManifest.setDmTotalWeight(receiptVo.getSumEawbChargeableweight());
            debitManifest.setDmHandleTime(DateUtil.getNowDateTime());
            debitManifestMapper.updateTotaDebitManifestBydmId(debitManifest);

        } else {
            log.info("应收账单管理-费用统计更新 应收金额:" + debitManifest.getDmPlanAmount() +
                    ",实收金额:" + debitManifest.getDmTotalfc() +
                    ",总票数:" + debitManifest.getDmTotalPieces() +
                    ",总重量:" + debitManifest.getDmTotalWeight() +
                    ",DMID:" + debitManifest.getDmId());
            DebitManifest debitManifest1 = debitManifestMapper.getDmIdByDmCode(dmCode);
            debitManifest.setDmId(debitManifest1.getDmId());
            debitManifest.setDmUserId(dmUserId);
            debitManifest.setCompanyId(String.valueOf(companyId));
            debitManifest.setDmHandleTime(DateUtil.getNowDateTime());
            debitManifestMapper.updateNullTotaDebitManifestBydmId(debitManifest);

        }
    }

    @Transactional
    @Override
    public void syncAppRecord() {
        //order
        List<AppOrder> appOrderList = appOrderMapper.selectNotExistInEawb();
        List<ExpressAirWayBill> expressAirWayBills = new ArrayList<>();
        appOrderList.forEach(e -> {
            ExpressAirWayBill ebaaa = expressAirWayBillMapper.selectByEawbPrintCode(e.getEawbPrintcode());
            if (ebaaa == null) {
                ExpressAirWayBill eawb = new ExpressAirWayBill();
                eawb.setEawbPrintcode(e.getEawbPrintcode());
                eawb.setEawbDepartcountry(e.getSenderContry());
                eawb.setEawbDestcountry(e.getReceiveContry());
                eawb.setEawbStatus("ON");
                eawb.setEawbPieces(new BigDecimal(e.getPieces()));
                eawb.setEawbVolume(e.getActualTotalVolume());
                eawb.setEawbGrossweight(e.getActualTotalWeight());
                List<AppOrderEawb> eawbList=appOrderEawbMapper.selectListByOrderId(e.getId());
                if(eawbList.size()>0){
                    eawb.setEawbChargeableweight(eawbList.get(0).getChargeableWeight());
                    eawb.setEawbReference1(eawbList.get(0).getEawbReference1());
                }
                eawb.setCtCode("142");
                eawb.setEawbOutboundSacId(e.getOutboundCompanyId());
                eawb.setEawbSoCode("ZSU1000001058");
                eawb.setEawbCustprodenname(e.getFbaProductName());
                eawb.setEawbShipperAccountname(e.getSenderName());
                eawb.setEawbConsigneeAccountname(e.getReceiveName());
                eawb.setEawbBtCode(e.getOrderType());
                eawb.setEawbIetype("E");
                eawb.setEawbDeliverPostcode(e.getReceivePostcode());
                eawb.setEawbPickupPostcode(e.getSenderPostcode());
                eawb.setEawbCustdeclval(e.getDeclareValue());
                eawb.setEawbQuantity(e.getItemNum());
                eawb.setOrderCodeIn(e.getOrderCode());
                eawb.setEawbDeststate(e.getReceiveProvic());
                eawb.setEawbDestcity(e.getReceiveCity());
                eawb.setEawbDeclarevalue(e.getDeclareValue());
                eawb.setEawbCodcurrency("142");
                eawb.setEawbDeliverAddress(e.getReceiveAdress());
                eawb.setEawbDeliverContact(e.getReceiveName());
                eawb.setEawbDeliverMobile(e.getReceiveMobile());
                eawb.setEawbDeliverEmail(e.getReceiveEmail());
                eawb.setEawbHandletime(new Date());
                eawb.setEawbUpdatetime(e.getHandleTime());
                eawb.setWeightValue(e.getChargeableTotalWeight());
                eawb.setSacId("ZSU");
                eawb.setEawbReference2(e.getOrderCode());
                eawb.setEawbKeyentrytime(e.getCreateTime());
                Product p=productMapper.selectByPrimaryKey(e.getProductid());
                if(p!=null) {
                    eawb.setEawbServicetype(p.getpServicetype());
                    eawb.setEawbTransmodeid(p.getpTransmodeid());
                    eawb.setEawbServiceTypeOriginal(p.getpServicetypeOriginal());
                    eawb.setEawbTransmodeIdOriginal(p.getpTransmodeIdOriginal());
                }
                eawb.setEawbBillingStatus("CONSUMED");
                eawb.setEawbInner("app_order");
                eawb.setEawbDeclaregrossweight(e.getPreTotalWeight());
                eawb.setEawbDeclarevolume(e.getPreTotalVolume());
                expressAirWayBills.add(eawb);
            }
        });

        //mainfest
        List<AppDebitManifest> appDebitManifests = appDebitManifestMapper.selectStatusIsN();
        List<Integer> dmList = new ArrayList<>();
        List<DebitManifest> debitManifestList = new ArrayList<>();
        List<ReceiptRecord> records = new ArrayList<>();
        appDebitManifests.forEach(e -> {
            dmList.add(e.getDmId());
            DebitManifest dm = new DebitManifest();
            ExpressAirWayBill eawb=expressAirWayBillMapper.selectByOrderId(e.getOrderId());
            if(eawb!=null) {
                dm.setEawbPrintcode(eawb.getEawbPrintcode());
                dm.setEawbReference1(eawb.getEawbReference1());
            }
            BeanUtils.copyProperties(e, dm);
            dm.setDmStatus("PAYED");
            dm.setSoMode("CASH");
            dm.setInvoiceCode(e.getInvoiceId());
            debitManifestMapper.insert(dm);
            debitManifestList.add(dm);
            //receipt
            List<AppReceiptRecord> recordList = appReceiptRecordMapper.selectListByDmId(e.getDmId());
            recordList.forEach(ss -> {
                ReceiptRecord rr = new ReceiptRecord();
                BeanUtils.copyProperties(ss, rr);
                rr.setRrHandletime(new Date());
                rr.setDmId(dm.getDmId());
                rr.setRrType("1");
                AppOrder ao=appOrderMapper.selectByPrimaryKey(ss.getOrderId());
                if(ao!=null) {
                    Product p = productMapper.selectByPrimaryKey(ao.getProductid());
                    List<AppOrderEawb> eawbList = appOrderEawbMapper.selectListByOrderId(ss.getOrderId());
                    if (eawbList.size() > 0) {
                        rr.setEawbChargeableweight(eawbList.get(0).getChargeableWeight());
                        rr.setEawbReference1(eawbList.get(0).getEawbReference1());
                        rr.setEawbReference2(eawbList.get(0).getEawbReference2());
                    }
                    rr.setEpKey(p.getpServicetypeOriginal());
                    rr.setOutboundCompanyId(ao.getOutboundCompanyId());
                }
                rr.setSoMode("RECHARGE");
                rr.setEawbIeType("E");
                records.add(rr);
            });
        });
        if (expressAirWayBills.size() > 0) {
            expressAirWayBillMapper.insertBatch(expressAirWayBills);
        }
        if (dmList.size() > 0) {
            appDebitManifestMapper.batchUpdateById(dmList);
            //debitManifestMapper.insertBatch(debitManifestList);
        }
        if (records.size() > 0) {
            receiptRecordMapper.timerInsertBatch(records);
        }
    }

}
