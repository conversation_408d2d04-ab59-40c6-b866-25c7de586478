package com.sinoair.billing.service.receipt.impl;

import com.sinoair.billing.dao.billing.SerialNOMapper;
import com.sinoair.billing.domain.model.billing.SerialNO;
import com.sinoair.billing.service.receipt.SerialNOService;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * Created by WangXX4 on 2016/7/11.
 */
@Service("SerialNOService")
public class SerialNOServiceImpl implements SerialNOService {
    private Log log = LogFactory.getLog(SerialNOServiceImpl.class);

    @Resource(name = "SerialNOMapper")
    SerialNOMapper serialNOMapper;

    @Override
    public synchronized BigDecimal selectCurrentNo(SerialNO serialNO, int count){

        BigDecimal increaseNo = BigDecimal.valueOf(count);
        BigDecimal currentNo = BigDecimal.valueOf(-1);
        try{
            serialNO.setSnValid(SerialNO.SNVALID_Y);
            final List<SerialNO> serialNOList = serialNOMapper.select(serialNO);
            if (serialNOList == null || serialNOList.size() == 0) {
                throw new Exception("not exist SerialNO");
            } else {
                serialNO = serialNOList.get(0);
                currentNo = BigDecimal.valueOf(serialNO.getSnCurrentNo());
                log.info("currentNO==="+currentNo);
                if (SerialNO.SNTYPE_INCREASE.equals(serialNO.getSnType())) {//递增
                    serialNO.setSnCurrentNo(currentNo.add(increaseNo).longValue());
                    log.info("SNTYPE_INCREASE==="+serialNO.getSnCurrentNo());
                } else if (SerialNO.SNTYPE_INITDAILY.equals(serialNO.getSnType())) {//每日初始化
                    if (isToday(serialNO.getSnDate())) {//当天
                        serialNO.setSnCurrentNo(currentNo.add(increaseNo).longValue());
                        log.info("isToday==="+serialNO.getSnCurrentNo());
                    } else {//过了一天
                        serialNO.setSnCurrentNo(new BigDecimal(1).longValue());
                        serialNO.setSnDate(getCurDate());
                        //currentNo = new BigDecimal(1);
                        log.info("nextToday==="+serialNO.getSnCurrentNo());
                    }
                }
                serialNOMapper.updateByPrimaryKey(serialNO);
            }
        }catch (Exception e){
            e.printStackTrace();
        }

        return BigDecimal.valueOf(serialNO.getSnCurrentNo());
    }

    private boolean isToday(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date today = null;
        try {
            today = sdf.parse(sdf.format(new Date()));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return today.equals(date);
    }

    private Date getCurDate(){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date curDate = null;
        try {
            curDate = sdf.parse(sdf.format(new Date()));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return curDate;
    }

    @Override
    public int insert(SerialNO record) {
        return serialNOMapper.insert(record);
    }

    @Override
    public int update(SerialNO record) {
        return serialNOMapper.updateByPrimaryKey(record);
    }
}
