package com.sinoair.billing.service.syn;

import com.sinoair.billing.domain.model.billing.ExpressAirWayBill;
import com.sinoair.billing.domain.model.ceosbi.EawbEadFlat;
import com.sinoair.billing.domain.vo.ceos.CeosEawb;
import com.sinoair.billing.domain.vo.query.CeosQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2018/1/30
 * @time: 9:52
 * @description: To change this template use File | Settings | File Templates.
 */
public interface SynCeosEadFlatService {


    String synCeosEadFlat(int pageNum,List<EawbEadFlat> eadFlatList);

}
