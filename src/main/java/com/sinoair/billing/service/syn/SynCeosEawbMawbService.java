package com.sinoair.billing.service.syn;

import com.sinoair.billing.domain.model.billing.ExpressAssignment;
import com.sinoair.billing.domain.model.billing.ExpressAssignmentActual;
import com.sinoair.billing.domain.vo.ceos.CeosEawbEaVO;
import com.sinoair.billing.domain.vo.query.CeosQuery;
import com.sinoair.billing.domain.vo.query.CommonQuery;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2018/1/30
 * @time: 9:52
 * @description: To change this template use File | Settings | File Templates.
 */
public interface SynCeosEawbMawbService {



    String synCeosEawbMawb(CeosQuery queryParam);

    int countCeosMawbEawb(CeosQuery ceosQuery);

    int countCeosMawbEawbByEaCode(String eaCode);

    List<CeosEawbEaVO> selectCeosMawbEawbByEaCode(String eaCode);

    List<ExpressAssignmentActual> selectBillingEa(CommonQuery commonQuery);

    String synCeosMawb(CeosQuery queryParam);
}
