package com.sinoair.billing.service.syn;

import com.sinoair.billing.domain.model.billing.EawbPre;
import com.sinoair.billing.domain.model.billing.ExpressAirWayBill;
import com.sinoair.billing.domain.model.billing.ExpressAirWayBillCeos;
import com.sinoair.billing.domain.model.billing.ReceiptRecordTmp;
import com.sinoair.billing.domain.vo.ceos.CeosEawb;
import com.sinoair.billing.domain.vo.query.CeosQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: 大雄
 * @date: 2018/1/30
 * @time: 9:52
 * @description: To change this template use File | Settings | File Templates.
 */
public interface SynCeosEawbService {

    List<CeosEawb> selectEawbBySysCode(CeosQuery ceosQuery);

    List<CeosEawb> selectCeosEawbByTime(CeosQuery ceosQuery);

    String synCeosEawbBySysCode(int pageNum,List<ExpressAirWayBill> billCeosList);
    String synCeosEawbOtherBySysCode(int pageNum,List<ExpressAirWayBill> billCeosList);

    String synCeosEawb(CeosQuery queryParam);

    Long selectMaxCeosSysCode();

    int countCeosEawbBySysCode(CeosQuery query);

    List<ExpressAirWayBill> selectCeosEawbBySysCode(CeosQuery ceosQuery);

    int countEawbByHandleTime(CeosQuery ceosQuery);

    void deleteBySoCodeEmpty();

    String MonitorSynCeosEawb(CeosQuery queryParam, List<String> tmpList);

    ExpressAirWayBill selectCeosEawbCNBycode(@Param("eawbPrintcode") String eawbPrintcode);

    ExpressAirWayBill selectCeosEawbOtherByCode(@Param("eawbPrintcode") String eawbPrintcode);

    String monitorSynCeosEawb(int pageNum,List<ExpressAirWayBill> billCeosList);

    ExpressAirWayBill selectOtherByEawbPrintCode(String eawbPrintCode);

    ExpressAirWayBill selectByEawbPrintCode(String eawbPrintCode);

    ExpressAirWayBillCeos selectCeosEawbByEawbPrintcode(String eawbPrintcode);

    ExpressAirWayBill selectCeosFlatPre(String eawbPrintcode);

    EawbPre selectCeosPre(String eawbPrintcode);

    int countPreByHandleTime(CeosQuery ceosQuery);

    List<EawbPre> selectPreByHandleTime(CeosQuery ceosQuery);

    String synCeosPre(int pageNum,List<EawbPre> preList);

    EawbPre selectByPrimaryKey(Long eawbSyscode);
}
