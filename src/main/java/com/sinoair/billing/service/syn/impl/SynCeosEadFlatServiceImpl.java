package com.sinoair.billing.service.syn.impl;


import com.sinoair.billing.dao.billing.*;
import com.sinoair.billing.dao.ceos.CeosEawbMapper;
import com.sinoair.billing.domain.constant.EadFlatConstant;
import com.sinoair.billing.domain.constant.FlatStatusEnum;
import com.sinoair.billing.domain.model.billing.*;
import com.sinoair.billing.domain.model.ceosbi.EawbEadFlat;
import com.sinoair.billing.service.syn.SynCeosEadFlatService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;


/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2018/1/30
 * @time: 9:59
 * @description: To change this template use File | Settings | File Templates.
 */
@Service("SynCeosEadFlatService")
public class SynCeosEadFlatServiceImpl implements SynCeosEadFlatService {

    private Logger logger = LoggerFactory.getLogger(SynCeosEadFlatServiceImpl.class);

    private int length = 1000;


    @Autowired
    private BillingEawbEadFlatMapper flatMapper;

    @Autowired
    private ExpressPropertyMapper expressPropertyMapper;

    @Autowired
    private EawbPreMapper eawbPreMapper;

    @Autowired
    private CeosEawbMapper ceosEawbMapper;



    @Override
    public String synCeosEadFlat(int pageNum,List<EawbEadFlat> eadFlatList) {
//        logger.info("线程"+pageNum+"开始===="+new Date());
        List<EawbEadFlat> addFlatList = new ArrayList<>();
        List<EawbEadFlat> updateFlatList = new ArrayList<>();
        List<EawbPre> preList = new ArrayList<>();
        //状态信息
        List<ExpressProperty> epList = expressPropertyMapper.selectEpListFlatByGroup(EadFlatConstant.GROUP_ORDER_STATUS_NO);
        Date curDate = new Date();
        for (EawbEadFlat eadFlat : eadFlatList){
            BigDecimal eawbSyscode = eadFlat.getEawbSyscode();
            if (eadFlat.getDeclare() != null){
//                EawbPre eawbPre = eawbPreMapper.selectByPrimaryKey(eawbSyscode.longValue());
                EawbPre eawbPre = eawbPreMapper.selectByPrintcode(eadFlat.getEawbPrintcode());
                if (eawbPre == null){
                    eawbPre = ceosEawbMapper.selectCeosPre(eadFlat.getEawbPrintcode());
                    if (eawbPre != null){
                        preList.add(eawbPre);
                    }
                }
            }

//            EawbEadFlat eawbEadFlat = flatMapper.selectByPrimaryKey(eawbSyscode);
            EawbEadFlat eawbEadFlat = flatMapper.selectByPrintcode(eadFlat.getEawbPrintcode());
            if (eawbEadFlat == null){
                eawbEadFlat = flatMapper.selectByPrimaryKey(eadFlat.getEawbSyscode());
            }
            if (eawbEadFlat == null){
                eadFlat.setEefUpdatetime(curDate);
                addFlatList.add(eadFlat);
            }else{
                comparison(eawbEadFlat,eadFlat);
                comparisonStatus(eawbEadFlat,eadFlat,epList);
                eawbEadFlat.setEefUpdatetime(curDate);
                updateFlatList.add(eawbEadFlat);
            }

        }
//        logger.info("线程"+pageNum+"新增===="+addEawbList.size());
        if (addFlatList.size() > 0){
            logger.info("线程"+pageNum+"addFlatList===="+addFlatList.size());
            flatMapper.insertBatch(addFlatList);

        }
//        logger.info("线程"+pageNum+"修改===="+updateEawbList.size());
        if (updateFlatList.size() > 0){
            logger.info("线程"+pageNum+"updateFlatList===="+updateFlatList.size());
            flatMapper.updateBatch(updateFlatList);
        }

        if (preList.size() > 0){
            logger.info("线程"+pageNum+"preList===="+preList.size());
            eawbPreMapper.insertBatch(preList);
        }

        return null;
    }

    private void comparisonStatus(EawbEadFlat eawbEadFlat,EawbEadFlat param,List<ExpressProperty> epList){
        if (eawbEadFlat.getStatus() == null && null != param.getStatus()){
            eawbEadFlat.setStatus(param.getStatus());
        }else{
            if (param.getStatus() == null) {
                return;
            }
//            Integer statusInt = CheckUtil.getEpValue(eawbEadFlat.getStatus(),epList);
            Integer statusInt = FlatStatusEnum.getValue(eawbEadFlat.getStatus());
            if (statusInt != null && param.getStatusValueInt() != null){
                if (param.getStatusValueInt() > statusInt){
                    eawbEadFlat.setStatus(param.getStatus());
                }
            }
        }

    }

    private void comparison(EawbEadFlat eawbEadFlat,EawbEadFlat param){
        if (eawbEadFlat.getFcInbound() == null && param.getFcInbound() != null){
            eawbEadFlat.setFcInbound(param.getFcInbound());
        }
        if (eawbEadFlat.getFcOutbound() == null && param.getFcOutbound() != null){
            eawbEadFlat.setFcOutbound(param.getFcOutbound());
        }
        if (eawbEadFlat.getAdc() == null && param.getAdc() != null){
            eawbEadFlat.setAdc(param.getAdc());
        }
        if (eawbEadFlat.getCpt() == null && param.getCpt() != null){
            eawbEadFlat.setCpt(param.getCpt());
        }
        if (eawbEadFlat.getAss() == null && param.getAss() != null){
            eawbEadFlat.setAss(param.getAss());
        }
        if (eawbEadFlat.getRoe() == null && param.getRoe() != null){
            eawbEadFlat.setRoe(param.getRoe());
        }
        if (eawbEadFlat.getDelivery() == null && param.getDelivery() != null){
            eawbEadFlat.setDelivery(param.getDelivery());
        }
        if (eawbEadFlat.getUnass() == null && param.getUnass() != null){
            eawbEadFlat.setUnass(param.getUnass());
        }
        if (eawbEadFlat.getUndelivery() == null && param.getUndelivery() != null){
            eawbEadFlat.setUndelivery(param.getUndelivery());
        }
        if (eawbEadFlat.getDeclare() == null && param.getDeclare() != null){
            eawbEadFlat.setDeclare(param.getDeclare());
        }
        if (eawbEadFlat.getClrdt() == null && param.getClrdt() != null){
            eawbEadFlat.setClrdt(param.getClrdt());
        }
        if (null == eawbEadFlat.getAtd() && null != param.getAtd()) {
            eawbEadFlat.setAtd(param.getAtd());
        }
    }

}
