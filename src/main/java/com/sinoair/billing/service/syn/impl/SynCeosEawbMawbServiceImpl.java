package com.sinoair.billing.service.syn.impl;


import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.sinoair.billing.core.util.CheckUtil;
import com.sinoair.billing.core.util.MailUtil;
import com.sinoair.billing.core.util.StringUtil;
import com.sinoair.billing.dao.billing.*;
import com.sinoair.billing.dao.ceos.CeosEawbEaMapper;
import com.sinoair.billing.dao.ceos.CeosEawbMapper;
import com.sinoair.billing.domain.constant.CheckConstant;
import com.sinoair.billing.domain.constant.MailConstant;
import com.sinoair.billing.domain.model.billing.*;
import com.sinoair.billing.domain.vo.ceos.CeosEawbEaVO;
import com.sinoair.billing.domain.vo.check.EawbCheckVO;
import com.sinoair.billing.domain.vo.query.CeosQuery;
import com.sinoair.billing.domain.vo.query.CommonQuery;
import com.sinoair.billing.service.syn.SynCeosEawbMawbService;
import com.sinoair.billing.service.syn.SynCeosEawbService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2018/1/30
 * @time: 9:59
 * @description: To change this template use File | Settings | File Templates.
 */
@Service("SynCeosEawbMawbService")
public class SynCeosEawbMawbServiceImpl implements SynCeosEawbMawbService {

    private Logger logger = LoggerFactory.getLogger(SynCeosEawbMawbServiceImpl.class);

    private int length = 200;

    @Resource(name = "ExpressAirWayBillMapper")
    private ExpressAirWayBillMapper airWayBillMapper;

    @Resource(name = "CeosEawbEaMapper")
    private CeosEawbEaMapper ceosEawbEaMapper;

    @Resource(name = "ReceiptRecordMapper")
    private ReceiptRecordMapper receiptRecordMapper;

    @Resource(name = "ExpressAssignmentMapper")
    private ExpressAssignmentMapper expressAssignmentMapper;

    @Resource(name = "ExpressAssignmentActualMapper")
    private ExpressAssignmentActualMapper expressAssignmentActualMapper;

    @Autowired
    private CheckEawbResultDetailMapper checkEawbResultDetailMapper;


    @Override
    public String synCeosEawbMawb(CeosQuery queryParam){
        logger.info("线程"+queryParam.getPageNum()+"开始查询===="+new Date());

        int pageNum = queryParam.getPageNum();
        long startstream = System.currentTimeMillis();

        try {
            List<CeosEawbEaVO> eawbEaList = null;
            PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize(),false);
            if (StringUtil.isNotEmpty(queryParam.getEaCode())){
                eawbEaList = ceosEawbEaMapper.selectCeosMawbEawbByEaCode(queryParam.getEaCode());
                logger.info("线程:"+pageNum+" 查询参数:"+queryParam.getEaCode());
            }else{
                eawbEaList = ceosEawbEaMapper.selectCeosMawbEawb(queryParam);
                logger.info("线程:"+pageNum+" 查询参数:"+queryParam.getStartEbaHandletime());
            }
            logger.info("线程:"+pageNum+" 查询数据耗时:"+(System.currentTimeMillis()-startstream)+" ms");
            List<CeosEawbEaVO> updateList = new ArrayList<>();
            List<ReceiptRecord> updateRRList = new ArrayList<>();
            List<String> lackEawbList = new ArrayList<>();
            List<CheckEawbResultDetail> updateDetailList = new ArrayList<>();
            for (CeosEawbEaVO vo :eawbEaList){
                if (vo.getMawbCode() != null){
                    if (CheckConstant.CN_SO_CODE.equals(vo.getEawbSoCode())){
                        continue;
                    }
                    ExpressAirWayBill airWayBill = airWayBillMapper.selectByEawbPrintCode(vo.getEawbPrintcode());
                    if (airWayBill == null){
                        lackEawbList.add(vo.getEawbPrintcode());
                        continue;
                    }
                    int countEawb = 0;

                    countEawb = airWayBillMapper.countEawbByMawbCode(vo.getEawbPrintcode(),vo.getMawbCode());
                    if (countEawb == 0){
                        updateList.add(vo);
                    }

//                    List<ReceiptRecord> rrList = receiptRecordMapper.selectByEawbPrintcode(vo.getEawbPrintcode());
//                    if (rrList != null){
//                        for (ReceiptRecord receiptRecord : rrList){
//                            if (StringUtil.isEmpty(receiptRecord.getMawbCode())){
//                                receiptRecord.setMawbCode(vo.getMawbCode());
//                                updateRRList.add(receiptRecord);
//                            }
//                        }
//                    }
                }
            }

            if (updateList.size() > 0){
                logger.info("线程:"+pageNum+" 更新Ceos-eawb-mawb数据："+updateList.size());
                int size = updateList.size();
                int count = size % length != 0 ? size / length + 1 : size / length;
                for (int i = 0; i < count; i++) {
                    List<CeosEawbEaVO> subList = updateList.subList(i * length, ((length + i * length) < size ? (length + i * length) : size));
                    if (subList.size() > 0){
                        airWayBillMapper.batchUpdatMawb(subList);
                    }
                }
            }

//            if (updateRRList.size() > 0){
//                logger.info("线程:"+pageNum+" mawb 更新rr菜鸟数据："+updateRRList.size());
//                int size = updateRRList.size();
//                int count = size % length != 0 ? size / length + 1 : size / length;
//                for (int i = 0; i < count; i++) {
//                    List<ReceiptRecord> subList = updateRRList.subList(i * length, ((length + i * length) < size ? (length + i * length) : size));
//                    if (subList.size() > 0){
//                        receiptRecordMapper.updateBatchMawb(subList);
//                    }
//                }
//            }
            if (lackEawbList.size() > 0){
                logger.info("线程:"+pageNum+" 新增缺失的eawbprintcode数据："+lackEawbList.size());
                int size = lackEawbList.size();
                int count = size % length != 0 ? size / length + 1 : size / length;
                for (int i = 0; i < count; i++) {
                    List<String> subList = lackEawbList.subList(i * length, ((length + i * length) < size ? (length + i * length) : size));
                    if (subList.size() > 0){
                        checkEawbResultDetailMapper.insertBatchLackEawb(subList);
                    }
                }
            }

//            if (updateDetailList.size() > 0){
//                logger.info("修改eawb-check数据："+updateDetailList.size());
//                int size = updateDetailList.size();
//                int count = size % length != 0 ? size / length + 1 : size / length;
//                for (int i = 0; i < count; i++) {
//                    List<CheckEawbResultDetail> subList = updateDetailList.subList(i * length, ((1000 + i * length) < size ? (1000 + i * length) : size));
//                    if (subList.size() > 0){
//                        checkEawbResultDetailMapper.updateBatch(subList);
//                    }
//                }
//            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("线程:"+pageNum+" ec-billing 更新Ceos-eawb-mawb数据异常:"+e.getMessage());
            MailUtil.postMail(MailConstant.MAIL_TO_ONE,MailConstant.MAIL_SUBJECT_CEOS_EAWB,e.getMessage());
        }



        return "SUCCESS";
    }


    @Override
    public int countCeosMawbEawb(CeosQuery ceosQuery){
        return ceosEawbEaMapper.countCeosMawbEawb(ceosQuery);
    }

    @Override
    public     int countCeosMawbEawbByEaCode(String eaCode){
        return ceosEawbEaMapper.countCeosMawbEawbByEaCode(eaCode);
    }

    @Override
    public List<CeosEawbEaVO> selectCeosMawbEawbByEaCode(String eaCode){
        return ceosEawbEaMapper.selectCeosMawbEawbByEaCode(eaCode);
    }

    @Override
    public List<ExpressAssignmentActual> selectBillingEa(CommonQuery commonQuery){
        return expressAssignmentActualMapper.selectByDate(commonQuery);
    }

    @Override
    public String synCeosMawb(CeosQuery queryParam){
        logger.info("线程"+queryParam.getPageNum()+"开始查询===="+new Date());

        int pageNum = queryParam.getPageNum();
        long startstream = System.currentTimeMillis();

        try {
            List<ExpressAssignmentActual> ceosEaaList =ceosEawbEaMapper.selectCeosEaaList(queryParam);

            logger.info("线程:"+pageNum+" 查询数据耗时:"+(System.currentTimeMillis()-startstream)+" ms,大小："+ceosEaaList.size());
            List<ExpressAssignmentActual> updateList = new ArrayList<>();
            List<ExpressAssignmentActual> addList = new ArrayList<>();
            for (ExpressAssignmentActual vo :ceosEaaList){
                String eaCode = vo.getEaaCode();
                Long eaaSyscode = vo.getEaaSyscode();

                int eaCnCount = ceosEawbEaMapper.countCeosEaaCn(eaCode);
                if (eaCnCount > 0){
                    continue;
                }

                ExpressAssignmentActual eaa = expressAssignmentActualMapper.selectByPrimaryKey(eaaSyscode);
                if (eaa == null){
                    addList.add(vo);
                }else {
                    updateList.add(vo);
                }

            }

            if (updateList.size() > 0){
                logger.info("线程:"+pageNum+" 更新update-mawb数据："+updateList.size());
                int size = updateList.size();
                int count = size % length != 0 ? size / length + 1 : size / length;
                for (int i = 0; i < count; i++) {
                    List<ExpressAssignmentActual> subList = updateList.subList(i * length, ((length + i * length) < size ? (length + i * length) : size));
                    if (subList.size() > 0){
                        expressAssignmentActualMapper.updateBatch(subList);
                    }
                }
            }
            if (addList.size() > 0){
                logger.info("线程:"+pageNum+" 新增add-mawb数据："+addList.size());
                int size = addList.size();
                int count = size % length != 0 ? size / length + 1 : size / length;
                for (int i = 0; i < count; i++) {
                    List<ExpressAssignmentActual> subList = addList.subList(i * length, ((length + i * length) < size ? (length + i * length) : size));
                    if (subList.size() > 0){
//                        logger.info("subList:"+ JSONObject.toJSONString(subList));
                        expressAssignmentActualMapper.insertBatch(subList);
                    }
                }
            }


        } catch (Exception e) {
            e.printStackTrace();
            logger.error("线程:"+pageNum+" 同步主单数据异常:"+e.getMessage());
            MailUtil.postMail(MailConstant.MAIL_TO_ONE,MailConstant.MAIL_SUBJECT_CEOS_EAWB,e.getMessage());
        }



        return "SUCCESS";
    }
}
