package com.sinoair.billing.service.syn.impl;


import com.github.pagehelper.PageHelper;
import com.sinoair.billing.core.util.CheckUtil;
import com.sinoair.billing.core.util.MailUtil;
import com.sinoair.billing.core.util.StringUtil;
import com.sinoair.billing.dao.billing.*;
import com.sinoair.billing.dao.ceos.CeosEawbMapper;
import com.sinoair.billing.domain.constant.CheckConstant;
import com.sinoair.billing.domain.constant.MailConstant;
import com.sinoair.billing.domain.model.billing.*;
import com.sinoair.billing.domain.vo.ceos.CeosEawb;
import com.sinoair.billing.domain.vo.query.CeosQuery;
import com.sinoair.billing.service.syn.SynCeosEawbService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2018/1/30
 * @time: 9:59
 * @description: To change this template use File | Settings | File Templates.
 */
@Service("SynCeosEawbService")
public class SynCeosEawbServiceImpl implements SynCeosEawbService {

    private Logger logger = LoggerFactory.getLogger(SynCeosEawbServiceImpl.class);

    private int length = 1000;

    @Resource(name = "ExpressAirWayBillCeosMapper")
    private ExpressAirWayBillCeosMapper airWayBillCeosMapper;

    @Resource(name = "ExpressAirWayBillMapper")
    private ExpressAirWayBillMapper expressAirWayBillMapper;

    @Resource(name = "CeosEawbMapper")
    private CeosEawbMapper ceosEawbMapper;

    @Autowired
    private CheckEawbResultDetailMapper detailMapper;

    @Resource(name = "SettlementObjectMapper")
    private SettlementObjectMapper soMapper;

    @Autowired
    private ExpressPropertyMapper expressPropertyMapper;

    @Autowired
    private EawbPreMapper eawbPreMapper;

    @Override
    public List<CeosEawb> selectEawbBySysCode(CeosQuery ceosQuery){
        return ceosEawbMapper.selectEawbBySysCode(ceosQuery);
    }

    @Override
    public List<CeosEawb> selectCeosEawbByTime(CeosQuery ceosQuery) {
        PageHelper.startPage(ceosQuery.getPageNum(), ceosQuery.getPageSize(),false);
        List<CeosEawb> eawbCeosList = ceosEawbMapper.selectCeosEawbByTime(ceosQuery);
        return eawbCeosList;
    }

    @Override
    public String synCeosEawbBySysCode(int pageNum,List<ExpressAirWayBill> billCeosList) {
//        logger.info("线程"+pageNum+"开始===="+new Date());
        List<ExpressAirWayBill> addEawbList = new ArrayList<>();
        List<ExpressAirWayBill> updateEawbList = new ArrayList<>();

        List<ExpressAirWayBill> addEawbOtherList = new ArrayList<>();
        List<ExpressAirWayBill> updateEawbOtherList = new ArrayList<>();
        for (ExpressAirWayBill billCeos : billCeosList){
            try {
                String eawbPrintcode = billCeos.getEawbPrintcode();
                String soCode = billCeos.getEawbSoCode();

                ExpressAirWayBill expressAirWayBill = null;
                if (CheckConstant.CN_SO_CODE.equals(soCode)) {
                    continue;
                }
                String address = billCeos.getEawbDeliverAddress();
                if (StringUtil.isNotEmpty(address)) {
                    String deliverAddress = address.replaceAll("&", " ");
                    billCeos.setEawbDeliverAddress(deliverAddress);
                }
                String contact = billCeos.getEawbDeliverContact();
                if (StringUtil.isNotEmpty(contact)) {
                    String deliverContact = contact.replaceAll("&", " ");
                    billCeos.setEawbDeliverContact(deliverContact);
                }

                expressAirWayBill = expressAirWayBillMapper.selectByEawbPrintCode(eawbPrintcode);
                if (expressAirWayBill == null) {
                    addEawbList.add(billCeos);

                } else {
                    updateEawbList.add(billCeos);
                }
            }catch (Exception e){
                e.printStackTrace();
                logger.info("线程"+pageNum+"异常===="+billCeos.getEawbPrintcode()+"异常信息："+e.getMessage());
            }
        }
        logger.info("线程"+pageNum+"新增===="+addEawbList.size());
        if (addEawbList.size() > 0){
            expressAirWayBillMapper.insertBatch(addEawbList);
        }
//        logger.info("线程"+pageNum+"修改===="+updateEawbList.size());
//        if (updateEawbList.size() > 0){
//            expressAirWayBillMapper.batchUpdatInfo(updateEawbList);
//        }

        return null;
    }

    @Override
    public String synCeosEawbOtherBySysCode(int pageNum,List<ExpressAirWayBill> billCeosList) {
//        logger.info("线程"+pageNum+"开始===="+new Date());
        List<ExpressAirWayBill> addEawbList = new ArrayList<>();
        List<ExpressAirWayBill> updateEawbList = new ArrayList<>();
        for (ExpressAirWayBill billCeos : billCeosList){
            String eawbPrintcode = billCeos.getEawbPrintcode();

            ExpressAirWayBill expressAirWayBill = expressAirWayBillMapper.selectOtherByEawbPrintCode(eawbPrintcode);
            if (expressAirWayBill == null){
                addEawbList.add(billCeos);

            }else{
                updateEawbList.add(billCeos);
            }

        }
//        logger.info("线程"+pageNum+"新增===="+addEawbList.size());
        if (addEawbList.size() > 0){
//            expressAirWayBillMapper.insertBatchOther(addEawbList);
        }
//        logger.info("线程"+pageNum+"修改===="+updateEawbList.size());
        if (updateEawbList.size() > 0){
//            expressAirWayBillMapper.batchUpdatInfoOther(updateEawbList);
        }

        return null;
    }

    @Override
    public String synCeosEawb(CeosQuery queryParam){
        logger.info("线程"+queryParam.getPageNum()+"开始查询===="+new Date());

        int pageNum = queryParam.getPageNum();
        long startstream = System.currentTimeMillis();

        try {
            PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize(),false);
            List<CeosEawb> eawbCeosList = ceosEawbMapper.selectCeosEawbByTime(queryParam);
//            List<ExpressAirWayBillCeos> eawbCeosList = new ArrayList<>();
            logger.info("线程:"+pageNum+" 查询数据耗时:"+(System.currentTimeMillis()-startstream)+" ms");
            if (eawbCeosList.size() > 0){
                logger.info("线程:"+pageNum+" 新增Ceos-eawb数据："+eawbCeosList.size());
                int size = eawbCeosList.size();
                int count = size % length != 0 ? size / length + 1 : size / length;
                for (int i = 0; i < count; i++) {
//                    List<ExpressAirWayBill> subList = eawbCeosList.subList(i * length, ((length + i * length) < size ? (length + i * length) : size));
//                    if (subList.size() > 0){
////                        airWayBillCeosMapper.insertBatch(subList);
//                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("线程:"+pageNum+" 同步Ceos-eawb数据异常:"+e.getMessage());
            MailUtil.postMail(MailConstant.MAIL_TO_ONE,MailConstant.MAIL_SUBJECT_CEOS_EAWB,e.getMessage());
        }

        return "SUCCESS";
    }


    @Override
    public Long selectMaxCeosSysCode(){
        return ceosEawbMapper.selectMaxEawbSysCode(null);
    }

    @Override
    public int countCeosEawbBySysCode(CeosQuery query){
        return ceosEawbMapper.countCeosEawbBySysCode(query);
    }

    @Override
    public List<ExpressAirWayBill> selectCeosEawbBySysCode(CeosQuery ceosQuery) {
        return ceosEawbMapper.selectCeosEawbBySysCode(ceosQuery);
    }

    @Override
    public int countEawbByHandleTime(CeosQuery ceosQuery){
        return ceosEawbMapper.countEawbByHandleTime(ceosQuery);
    }

    @Override
    public void deleteBySoCodeEmpty(){
        airWayBillCeosMapper.deleteBySoCodeEmpty();
    }

    @Override
    public String MonitorSynCeosEawb(CeosQuery queryParam, List<String> tmpList) {
        logger.info("线程"+queryParam.getPageNum()+"大小===="+tmpList.size());
        List<String> mxxbKeyList = expressPropertyMapper.selectMxxbKeyList();
        int pageNum = queryParam.getPageNum();
        long startstream = System.currentTimeMillis();
        List<SettlementObject> soList = soMapper.selectSettlementList();
        try {
            List<ExpressAirWayBillCeos> eawbCeosList = new ArrayList<>();
            List<ExpressAirWayBill> addEawbList = new ArrayList<>();
            List<ExpressAirWayBill> addEawbOtherList = new ArrayList<>();
            List<String> deleteEawbList = new ArrayList<>();
            for (String eawbPrintcode : tmpList){
                ExpressAirWayBill eawbCeos = null;
                ExpressAirWayBill expressAirWayBill = expressAirWayBillMapper.selectByEawbPrintCode(eawbPrintcode);
//                if (expressAirWayBill == null){
//                    expressAirWayBill = expressAirWayBillMapper.selectOtherByEawbPrintCode(eawbPrintcode);
//                }
                if (expressAirWayBill == null){
                    ExpressAirWayBillCeos  billCeos = null;
                    try {
                        billCeos = ceosEawbMapper.selectCeosEawbByEawbPrintcode(eawbPrintcode);
                    } catch (Exception e) {
                        logger.info("eawbprintcode重复："+eawbPrintcode);
                        e.printStackTrace();
                    }
                    if (billCeos != null){
                        if (CheckConstant.CN_SO_CODE.equals(billCeos.getEawbSoCode())){
                            eawbCeos = ceosEawbMapper.selectCeosEawbCNBycode(eawbPrintcode);
                            eawbCeos.setSacId(CheckUtil.getSoSacId(billCeos.getEawbSoCode(),soList));
                            if (CheckConstant.L_AE_STANDARD_SINOJP_RM.equals(eawbCeos.getEawbServiceTypeOriginal())){
                                eawbCeos.setWeightValue(eawbCeos.getEawbDeclaregrossweight());
                                eawbCeos.setEawbChargeableweight(eawbCeos.getEawbDeclaregrossweight());
                            }
                            if (CheckConstant.L_AE_PREMIUM_SINOFEDEX.equals(eawbCeos.getEawbServiceTypeOriginal())){
                                eawbCeos.setWeightValue(CheckUtil.getFedexCompareWeight(eawbCeos));
                            }
                            addEawbOtherList.add(eawbCeos);
                        }else{
                            eawbCeos = ceosEawbMapper.selectCeosEawbOtherByCode(eawbPrintcode);
                            if (StringUtil.isEmpty(eawbCeos.getSacId())){
                                eawbCeos.setSacId(CheckUtil.getSoSacId(billCeos.getEawbSoCode(),soList));
                            }
                            if (mxxbKeyList.contains(eawbCeos.getEawbServiceTypeOriginal())){
                                eawbCeos.setEawbTrackingNo(eawbCeos.getEawbReference3());
                            }

                            addEawbList.add(eawbCeos);
                        }

                    }
                    deleteEawbList.add(eawbPrintcode);
                }else{
                    deleteEawbList.add(eawbPrintcode);
                }


            }

//            logger.info("线程:"+pageNum+" 查询数据耗时:"+(System.currentTimeMillis()-startstream)+" ms");
            if (eawbCeosList.size() > 0){
//                airWayBillCeosMapper.insertBatch(eawbCeosList);
//                detailMapper.deleteBatchLackEawb(deleteEawbList);
                logger.info("线程:"+pageNum+" 新增Ceos-eawb数据："+eawbCeosList.size());
                int size = eawbCeosList.size();
                int count = size % length != 0 ? size / length + 1 : size / length;
                for (int i = 0; i < count; i++) {
                    List<ExpressAirWayBillCeos> subList = eawbCeosList.subList(i * length, ((length + i * length) < size ? (length + i * length) : size));
                    if (subList.size() > 0){
                        airWayBillCeosMapper.insertBatch(subList);
                    }
                }
            }
            if (deleteEawbList.size() > 0){

                logger.info("线程:"+pageNum+"  删除LackEawb："+deleteEawbList.size());
//                int size = deleteEawbList.size();
//                int count = size % length != 0 ? size / length + 1 : size / length;
//                for (int i = 0; i < count; i++) {
//                    List<String> delsubList = deleteEawbList.subList(i * length, ((length + i * length) < size ? (length + i * length) : size));
//                    if (delsubList.size() > 0){
//                        detailMapper.deleteBatchLackEawb(delsubList);
//                    }
//
//                }
                detailMapper.deleteBatchLackEawb(deleteEawbList);
            }

            if (addEawbList.size() > 0){
                logger.info("线程:"+pageNum+"  新增billing-eawb："+addEawbList.size());
//                int size = addEawbList.size();
//                int count = size % length != 0 ? size / length + 1 : size / length;
//                for (int i = 0; i < count; i++) {
//                    List<ExpressAirWayBill> addsubList = addEawbList.subList(i * length, ((length + i * length) < size ? (length + i * length) : size));
//                    if (addsubList.size() > 0){
//                        expressAirWayBillMapper.insertBatch(addEawbList);
//                    }
//                }
                expressAirWayBillMapper.insertBatch(addEawbList);
            }
            if (addEawbOtherList.size() > 0){
                logger.info("线程:"+pageNum+"  新增billing-eawb-other："+addEawbOtherList.size());

//                expressAirWayBillMapper.insertBatchOther(addEawbOtherList);
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("线程:"+pageNum+" 监控同步Ceos-eawb数据异常:"+e.getMessage());
            MailUtil.postMail(MailConstant.MAIL_TO_ONE,MailConstant.MAIL_SUBJECT_CEOS_EAWB,e.getMessage());
        }



        return "SUCCESS";
    }

    @Override
    public ExpressAirWayBill selectCeosEawbCNBycode(String eawbPrintcode) {
        try {
            ExpressAirWayBill expressAirWayBill =  ceosEawbMapper.selectCeosEawbCNBycode(eawbPrintcode);
            return expressAirWayBill;
        }catch (Exception e){
            e.printStackTrace();
            MailUtil.postMail(MailConstant.MAIL_TO_ONE,MailConstant.MAIL_SUBJECT_CEOS_EAWB_ERR+":"+eawbPrintcode,e.getMessage());
            return null;
        }


    }

    @Override
    public ExpressAirWayBill selectCeosEawbOtherByCode(String eawbPrintcode) {
        try {
            ExpressAirWayBill expressAirWayBill =  ceosEawbMapper.selectCeosEawbOtherByCode(eawbPrintcode);
            return expressAirWayBill;
        }catch (Exception e){
            e.printStackTrace();
            logger.info("eawb表重复："+eawbPrintcode);
            MailUtil.postMail(MailConstant.MAIL_TO_ONE,MailConstant.MAIL_SUBJECT_CEOS_EAWB_ERR+":"+eawbPrintcode,e.getMessage());
            return null;
        }
    }

    @Override
    public String monitorSynCeosEawb(int pageNum,List<ExpressAirWayBill> billCeosList) {
//        logger.info("线程"+pageNum+"开始===="+new Date());
        List<String> addEawbList = new ArrayList<>();
        for (ExpressAirWayBill billCeos : billCeosList){
            String eawbPrintcode = billCeos.getEawbPrintcode();
            String soCode =  billCeos.getEawbSoCode();

            ExpressAirWayBill expressAirWayBill = null;
            if (CheckConstant.CN_SO_CODE.equals(soCode)){
                expressAirWayBill = expressAirWayBillMapper.selectByEawbPrintCode(eawbPrintcode);
                if (expressAirWayBill == null){
                    addEawbList.add(billCeos.getEawbPrintcode());

                }
            }else{
                expressAirWayBill = expressAirWayBillMapper.selectOtherByEawbPrintCode(eawbPrintcode);
                if (expressAirWayBill == null){
                    addEawbList.add(billCeos.getEawbPrintcode());
                }
            }
        }
//        logger.info("线程"+pageNum+"新增===="+addEawbList.size());
        if (addEawbList.size() > 0){
            detailMapper.insertBatchLackEawbMo(addEawbList);
        }


        return null;
    }

    @Override
    public ExpressAirWayBill selectOtherByEawbPrintCode(String eawbPrintCode) {
        return expressAirWayBillMapper.selectOtherByEawbPrintCode(eawbPrintCode);
    }

    @Override
    public ExpressAirWayBill selectByEawbPrintCode(String eawbPrintCode) {
        return expressAirWayBillMapper.selectByEawbPrintCode(eawbPrintCode);
    }

    @Override
    public ExpressAirWayBillCeos selectCeosEawbByEawbPrintcode(String eawbPrintcode) {
        return ceosEawbMapper.selectCeosEawbByEawbPrintcode(eawbPrintcode);
    }

    @Override
    public ExpressAirWayBill selectCeosFlatPre(String eawbPrintcode) {
        return ceosEawbMapper.selectCeosFlatPre(eawbPrintcode);
    }

    @Override
    public EawbPre selectCeosPre(String eawbPrintcode) {
        return ceosEawbMapper.selectCeosPre(eawbPrintcode);
    }

    @Override
    public int countPreByHandleTime(CeosQuery ceosQuery) {
        return ceosEawbMapper.countPreByHandleTime(ceosQuery);
    }

    @Override
    public List<EawbPre> selectPreByHandleTime(CeosQuery ceosQuery) {
        PageHelper.startPage(ceosQuery.getPageNum(), ceosQuery.getPageSize(),false);
        return ceosEawbMapper.selectPreByHandleTime(ceosQuery);
    }

    @Override
    public String synCeosPre(int pageNum, List<EawbPre> preList) {
        eawbPreMapper.insertBatch(preList);
        return "";
    }

    @Override
    public EawbPre selectByPrimaryKey(Long eawbSyscode) {
        return eawbPreMapper.selectByPrimaryKey(eawbSyscode);
    }
}
