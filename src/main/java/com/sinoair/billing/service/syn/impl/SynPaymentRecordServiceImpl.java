package com.sinoair.billing.service.syn.impl;


import com.sinoair.billing.core.util.DateUtil;
import com.sinoair.billing.core.util.MailUtil;
import com.sinoair.billing.core.util.StringUtil;
import com.sinoair.billing.dao.billing.*;
import com.sinoair.billing.domain.constant.MailConstant;
import com.sinoair.billing.domain.model.billing.CheckLackRRMawb;
import com.sinoair.billing.domain.model.billing.PaymentRecord;
import com.sinoair.billing.domain.model.billing.ReceiptRecordTmp;
import com.sinoair.billing.domain.vo.query.CeosQuery;
import com.sinoair.billing.service.syn.SynPaymentRecordService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.TreeSet;

import static java.util.Comparator.comparing;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;

/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2018/1/30
 * @time: 9:59
 * @description: To change this template use File | Settings | File Templates.
 */
@Service("SynPaymentRecordService")
public class SynPaymentRecordServiceImpl implements SynPaymentRecordService {

    private Logger logger = LoggerFactory.getLogger(SynPaymentRecordServiceImpl.class);

    private final static String STATUS_Y="Y";
    private final static String STATUS_N="N";
    private int length = 1000;

    private static int GHFW = 100101; //挂号服务费
    private static int PSFW = 100118; //配送服务费



    @Autowired
    private ReceiptEawbMapper receiptEawbMapper;

    @Autowired
    private PaymentRecordMapper paymentRecordMapper;


    @Override
    public String synPaymentRecord(CeosQuery queryParam,List<PaymentRecord> tmpList){
        logger.info("线程"+queryParam.getPageNum()+" 序号："+queryParam.getSerialNumber() +"开始===="+new Date());

        int handleSize = 0;
        String isCn = "Y";
        try {

            logger.info("正在同步应付计费数据！！！");

            if (tmpList != null){
                logger.info("Thread线程"+queryParam.getPageNum()+"==tmpList.size=="+tmpList.size());
                List<PaymentRecord> recordList = tmpList.stream().collect(
                        collectingAndThen(
                                toCollection(() -> new TreeSet<>(comparing(n->n.getEawbPrintcode()+";"+n.getCompanyId()+";"+n.getPdSyscode()+";"+n.getSoCode()))),ArrayList::new)
                );
//
                List<PaymentRecord> prList = new ArrayList<>();
                List<Long> reIdList = new ArrayList<>();

                logger.info("Thread线程"+queryParam.getPageNum()+"==去重后.size=="+recordList.size());
                long checkstart= System.currentTimeMillis();
                for (PaymentRecord tmp : recordList){

                    long beforestart= System.currentTimeMillis();
                    if (paymentRecordMapper.countPaymentRecord(tmp) == 0){
                        prList.add(tmp);
                    }
//                    reIdList.add(tmp.getReId());

                }
                if (prList.size() > 0){
                    logger.info("新增pr数据："+prList.size());
                    int size = prList.size();
                    int count = size % length != 0 ? size / length + 1 : size / length;
                    for (int i = 0; i < count; i++) {
                        List<PaymentRecord> subList = prList.subList(i * length, ((length + i * length) < size ? (length + i * length) : size));
                        if (subList.size() > 0){
                            paymentRecordMapper.insertBatch(subList);
                        }
                    }
                }

//                if (reIdList.size() > 0){
//                    logger.info("修改rreawb状态数据："+reIdList.size());
//                    int size = reIdList.size();
//                    int count = size % length != 0 ? size / length + 1 : size / length;
//                    for (int i = 0; i < count; i++) {
//                        List<Long> subList = reIdList.subList(i * length, ((length + i * length) < size ? (length + i * length) : size));
//                        if (subList.size() > 0){
//                            receiptEawbMapper.updateBatchRrEawbN(subList);
//                        }
//                    }
//                }

            }

        } catch (Exception e){
            logger.error("RR信息同步异常"+e.getMessage());
            e.printStackTrace();
            MailUtil.postMail(MailConstant.MAIL_TO_ONE,MailConstant.MAIL_SUBJECT_INS_RR,e.getMessage());
        }
        logger.info("线程"+queryParam.getPageNum()+"总用时===");


        return handleSize+"";
    }

}
