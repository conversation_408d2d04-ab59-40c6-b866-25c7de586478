package com.sinoair.billing.service.syn.impl;


import com.github.pagehelper.PageHelper;
import com.sinoair.billing.core.util.BeanConvertUtil;
import com.sinoair.billing.core.util.CheckUtil;
import com.sinoair.billing.core.util.DateUtil;
import com.sinoair.billing.core.util.MailUtil;
import com.sinoair.billing.dao.billing.*;
import com.sinoair.billing.dao.ceos.CeosActivityMapper;
import com.sinoair.billing.domain.constant.CheckConstant;
import com.sinoair.billing.domain.constant.MailConstant;
import com.sinoair.billing.domain.model.billing.*;
import com.sinoair.billing.domain.vo.check.EawbCheckVO;
import com.sinoair.billing.domain.vo.query.ActivityQuery;
import com.sinoair.billing.domain.vo.query.CeosQuery;
import com.sinoair.billing.service.check.CheckInsEbaService;
import com.sinoair.billing.service.syn.SynReceiptRecordService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.TreeSet;

import static java.util.Comparator.comparing;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;

/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2018/1/30
 * @time: 9:59
 * @description: To change this template use File | Settings | File Templates.
 */
@Service("SynReceiptRecordService")
public class SynReceiptRecordServiceImpl implements SynReceiptRecordService {

    private Logger logger = LoggerFactory.getLogger(SynReceiptRecordServiceImpl.class);

    private final static String STATUS_Y="Y";
    private final static String STATUS_N="N";
    private int length = 1000;
    private static int GHFW = 100101; //挂号服务费
    private static int PSFW = 100118; //配送服务费
    private static int YF = 100130; //运费
    private static int JCF = 100197; //基础费
    private static int CZF = 100096; //操作费


    @Autowired
    private ReceiptRecordTmpOtherMapper otherMapper;

    @Autowired
    private ReceiptRecordTmpMapper cnMapper;

    @Autowired
    private ReceiptRecordMapper receiptRecordMapper;

    @Autowired
    private ReceiptEawbMapper receiptEawbMapper;




    @Override
    public String synReceiptRecord(CeosQuery queryParam){
        logger.info("线程"+queryParam.getPageNum()+"开始查询===="+new Date());

        int handleSize = 0;
        String isCn = "N";
        try {
            List<ReceiptRecordTmp> tmpList = null;

            PageHelper.startPage(1, queryParam.getPageSize(),false);
            tmpList = otherMapper.list();
            logger.info("线程"+queryParam.getPageNum()+"查询结束===="+new Date());

//            if (tmpList == null || tmpList.size() == 0 ){
//                PageHelper.startPage(1, queryParam.getPageSize(),false);
//                tmpList = cnMapper.list();
//                isCn = "Y";
//            }
            if (isCn.equals("Y")){
                logger.info("正在同步菜鸟计费数据！！！");
            }else{
                logger.info("正在同步其它客户计费数据！！！");
            }

            if (tmpList != null){
                logger.info("线程"+queryParam.getPageNum()+"==tmpList.size=="+tmpList.size());
                List<ReceiptRecordTmp> recordList = tmpList.stream().collect(
                        collectingAndThen(
                                toCollection(() -> new TreeSet<>(comparing(n->n.getEawbPrintcode()+";"+n.getCompanyId()+";"+n.getPdSyscode()+";"+n.getSoCode()))),ArrayList::new)
                );
//
                List<ReceiptRecordTmp> rrList = new ArrayList<>();
                List<ReceiptRecordTmp> rrOtherList = new ArrayList<>();
                List<ReceiptEawb> rrEawbList = new ArrayList<>();
                List<ReceiptEawb> rreawbNList = new ArrayList<>();
                logger.info("线程"+queryParam.getPageNum()+"==去重后.size=="+recordList.size());
                long checkstart= System.currentTimeMillis();
                for (ReceiptRecordTmp tmp : recordList){
                    tmp.setChargeableweight(BigDecimal.ZERO);
                    String rrOccurtime = DateUtil.date2str(tmp.getRrOccurtime(),DateUtil.YYYYMMDD);
                    tmp.setStartOccurtime(DateUtil.getFirstDayOfGivenMonth(rrOccurtime));
                    tmp.setEndOccurtime(DateUtil.getFirstDayOfNextMonth(rrOccurtime));
                    long beforestart= System.currentTimeMillis();
                    String rrOccurtimeStr = DateUtil.date2str(tmp.getRrOccurtime(),DateUtil.YYYY_MM_DD);
                    if (receiptRecordMapper.countOtherByPdSyscode(tmp) == 0){
                        //rrOccurtimeStr 大于 2021-01-01 同步至新的应收记录表
//                        if (DateUtil.compareDate(rrOccurtimeStr,CheckConstant.RR_OTHER_DATE,DateUtil.YYYY_MM_DD) >=0){
//                            rrOtherList.add(tmp);
//                        }else{
//                            rrList.add(tmp);
//                        }
                        rrOtherList.add(tmp);
                    }
//                    if (receiptRecordMapper.countReceiptEawb(tmp.getEawbPrintcode()) == 0){
//                        rrEawbList.add(tmp);
//                    }
                    if (tmp.getPdSyscode() == GHFW || tmp.getPdSyscode() == PSFW ||
                        tmp.getPdSyscode() == YF || tmp.getPdSyscode() == JCF ||
                        tmp.getPdSyscode() == CZF){
                        if (receiptEawbMapper.countReceiptEawb(tmp.getEawbPrintcode()) == 0){
                            rrEawbList.add(BeanConvertUtil.record2eawb(tmp));
                        }
                        if (receiptEawbMapper.countReceiptEawbN(tmp.getEawbPrintcode()) == 0){
                            rreawbNList.add(BeanConvertUtil.record2eawb(tmp));
                        }
                    }
//                    logger.info("check end校验...end时效："+(System.currentTimeMillis()-beforestart));
                }
                logger.info("线程"+queryParam.getPageNum()+"批量校验...end时效："+(System.currentTimeMillis()-checkstart));
                long delstart= System.currentTimeMillis();
                if (tmpList.size() > 0){
                    logger.info("删除临时rr数据："+tmpList.size());
                    int size = tmpList.size();
                    int count = size % length != 0 ? size / length + 1 : size / length;
                    for (int i = 0; i < count; i++) {
                        List<ReceiptRecordTmp> subList = tmpList.subList(i * length, ((length + i * length) < size ? (length + i * length) : size));
                        if (subList.size() > 0){
                            if ("Y".equals(isCn)){
                                cnMapper.deleteBatch(subList);
                            }else{
                                otherMapper.deleteBatch(subList);
                            }
                        }
                    }
                }
                logger.info("线程"+queryParam.getPageNum()+"批量删除...end时效："+(System.currentTimeMillis()-delstart));
                long batchstart= System.currentTimeMillis();
                if (rrList.size() > 0){
                    logger.info("新增rr数据："+rrList.size());
                    int size = rrList.size();
                    int count = size % length != 0 ? size / length + 1 : size / length;
                    for (int i = 0; i < count; i++) {
                        List<ReceiptRecordTmp> subList = rrList.subList(i * length, ((length + i * length) < size ? (length + i * length) : size));
                        if (subList.size() > 0){
                            receiptRecordMapper.insertBatch(subList);
                        }
                    }
                }
                if (rrOtherList.size() > 0){
                    logger.info("新增rr-other数据："+rrOtherList.size());
                    int size = rrOtherList.size();
                    int count = size % length != 0 ? size / length + 1 : size / length;
                    for (int i = 0; i < count; i++) {
                        List<ReceiptRecordTmp> subList = rrOtherList.subList(i * length, ((length + i * length) < size ? (length + i * length) : size));
                        if (subList.size() > 0){
                            receiptRecordMapper.insertOtherBatch(subList);
                        }
                    }
                }

                if (rrEawbList.size() > 0){
                    logger.info("新增rreawb数据："+rrList.size());
                    int size = rrEawbList.size();
                    int count = size % length != 0 ? size / length + 1 : size / length;
                    for (int i = 0; i < count; i++) {
                        List<ReceiptEawb> subList = rrEawbList.subList(i * length, ((length + i * length) < size ? (length + i * length) : size));
                        if (subList.size() > 0){
                            receiptEawbMapper.insertBatchRrEawb(subList);
                        }
                    }
                }
                List<ReceiptEawb> resEawbNList = rreawbNList.stream().collect(
                        collectingAndThen(
                                toCollection(() -> new TreeSet<>(comparing(n->n.getEawbPrintcode()))),ArrayList::new)
                );
                if (resEawbNList.size() > 0){
                    logger.info("新增rreawb_N数据："+resEawbNList.size());
                    int size = resEawbNList.size();
                    int count = size % length != 0 ? size / length + 1 : size / length;
                    for (int i = 0; i < count; i++) {
                        List<ReceiptEawb> subList = resEawbNList.subList(i * length, ((length + i * length) < size ? (length + i * length) : size));
                        if (subList.size() > 0){
                            receiptEawbMapper.insertBatchRrEawbN(subList);
                        }
                    }
                }
                logger.info("线程"+queryParam.getPageNum()+"批量插入...end时效："+(System.currentTimeMillis()-batchstart));

            }

        } catch (Exception e){
            logger.error("RR信息同步异常"+e.getMessage());
            e.printStackTrace();
            MailUtil.postMail(MailConstant.MAIL_TO_ONE,MailConstant.MAIL_SUBJECT_INS_RR,e.getMessage());
        }
        logger.info("线程"+queryParam.getPageNum()+"总用时===");


        return handleSize+"";
    }



}
