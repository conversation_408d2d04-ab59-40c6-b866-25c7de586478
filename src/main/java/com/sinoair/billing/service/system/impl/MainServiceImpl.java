package com.sinoair.billing.service.system.impl;


import com.sinoair.billing.dao.billing.HomeMapper;
import com.sinoair.billing.dao.billing.MainMapper;
import com.sinoair.billing.dao.billing.UserMapper;
import com.sinoair.billing.dao.ceos.CeosMainMapper;
import com.sinoair.billing.dao.ceosbi.CeosBiMainMapper;
import com.sinoair.billing.domain.model.billing.Home;
import com.sinoair.billing.service.system.MainService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: 大雄
 * @date: 2018/1/30
 * @time: 9:59
 * @description: To change this template use File | Settings | File Templates.
 */
@Service("MainService")
public class MainServiceImpl implements MainService {

    @Autowired
    private HomeMapper homeMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private MainMapper mainMapper;

    @Autowired
    private CeosMainMapper ceosMainMapper;

    @Autowired
    private CeosBiMainMapper ceosBiMainMapper;

    @Override
    public String checkDb() {
        return mainMapper.checkDb();
    }

    /**
     * ceos只读库测试
     *
     * @return
     */
    @Override
    public List selectCeosUser() {
        System.out.println(userMapper.getUserListByUserName("admin").size());
        return ceosMainMapper.selectCesoUser();
    }

    @Override
    public List<Map> selectEawbEadFlat() {
        return ceosBiMainMapper.selectEawbEadFlat();
    }
}
