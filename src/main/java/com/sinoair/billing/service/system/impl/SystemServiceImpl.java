package com.sinoair.billing.service.system.impl;

import com.sinoair.billing.core.mail.MailUtil;
import com.sinoair.billing.dao.billing.ExpressPropertyMapper;
import com.sinoair.billing.dao.billing.MainMapper;
import com.sinoair.billing.domain.model.billing.ExpressProperty;
import com.sinoair.billing.domain.vo.system.DBTableSpaceVO;
import com.sinoair.billing.service.system.SystemService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.mail.MessagingException;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;

/**
 * Created with JiaZF
 * author:
 * Date:2019-6-24
 * Time:11:18
 */
@Service("SystemService")
public class SystemServiceImpl implements SystemService {


    @Autowired
    private MainMapper mainMapper;


    /**
     * 查询表空间
     *
     * @return
     */
    @Override
    public DBTableSpaceVO selectTableSpace() {
        return this.mainMapper.selectDbTableSpace();
    }

}
