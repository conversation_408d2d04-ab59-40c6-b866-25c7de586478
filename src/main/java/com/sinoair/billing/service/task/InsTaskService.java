package com.sinoair.billing.service.task;

import com.sinoair.billing.domain.model.billing.InsTask;
import com.sinoair.billing.domain.model.billing.RecordTask;
import com.sinoair.billing.domain.vo.query.InsRecordQuery;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: nhb
 * @date: 2018/1/30
 * @time: 9:52
 * @description: To change this template use File | Settings | File Templates.
 */
public interface InsTaskService {

    List<InsTask> selectByCondition(InsRecordQuery param);

    InsTask selectByTaskType(String taskType);

    int updateByPrimaryKeySelective(InsTask record);



}
