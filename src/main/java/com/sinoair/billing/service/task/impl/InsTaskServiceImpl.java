package com.sinoair.billing.service.task.impl;


import com.sinoair.billing.dao.billing.InsTaskMapper;
import com.sinoair.billing.domain.model.billing.InsTask;
import com.sinoair.billing.domain.vo.query.InsRecordQuery;
import com.sinoair.billing.service.task.InsTaskService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2018/1/30
 * @time: 9:59
 * @description: To change this template use File | Settings | File Templates.
 */
@Service("InsTaskService")
public class InsTaskServiceImpl implements InsTaskService {

    private Logger logger = LoggerFactory.getLogger(InsTaskServiceImpl.class);

    @Autowired
    private InsTaskMapper insTaskMapper;


    @Override
    public List<InsTask> selectByCondition(InsRecordQuery param){
        return insTaskMapper.selectByCondition(param);
    }

    @Override
    public InsTask selectByTaskType(String taskType){
        return insTaskMapper.selectByTaskType(taskType);
    }

    @Override
    public int updateByPrimaryKeySelective(InsTask record){
        return insTaskMapper.updateByPrimaryKeySelective(record);
    }




}
