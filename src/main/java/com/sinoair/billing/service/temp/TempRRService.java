package com.sinoair.billing.service.temp;

import com.sinoair.billing.domain.model.billing.TrapsRRConfig;
import com.sinoair.billing.domain.vo.query.CeosQuery;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: 大雄
 * @date: 2018/1/30
 * @time: 9:52
 * @description: To change this template use File | Settings | File Templates.
 */
public interface TempRRService {

    List<String> selectTempNhb();
    int deleteTempNhb(String eawb);

    String handleTemp_rr(CeosQuery queryParam);

    TrapsRRConfig selectById(Integer trapsId);

    void updateTrapsRRConfig(TrapsRRConfig record);

    int countTemp();

    String handleMakeUpRR(String eawbPrintcode);

    String handleSupplementaryBill(String eawbPrintcode);
}
