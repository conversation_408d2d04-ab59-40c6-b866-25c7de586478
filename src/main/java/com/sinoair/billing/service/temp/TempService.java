package com.sinoair.billing.service.temp;

import com.sinoair.billing.domain.model.billing.ExpressAirWayBill;
import com.sinoair.billing.domain.model.billing.SettlementObject;
import com.sinoair.billing.domain.vo.query.CeosQuery;

import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: 大雄
 * @date: 2018/1/30
 * @time: 9:52
 * @description: To change this template use File | Settings | File Templates.
 */
public interface TempService {





    List<String> selectTempEa();

    int deleteBatchTempEa(List<String> list);


    int insertBatchTempEa(List<String> list);

    List<String> selectTempAsendia();





}
