package com.sinoair.billing.service.temp.impl;


import com.github.pagehelper.PageHelper;
import com.sinoair.billing.core.util.CheckUtil;
import com.sinoair.billing.core.util.MailUtil;
import com.sinoair.billing.core.util.StringUtil;
import com.sinoair.billing.dao.billing.*;
import com.sinoair.billing.dao.ceos.CeosActivityMapper;
import com.sinoair.billing.domain.constant.BillingConstant;
import com.sinoair.billing.domain.constant.CheckConstant;
import com.sinoair.billing.domain.constant.MailConstant;
import com.sinoair.billing.domain.constant.ReceiptRecordConstant;
import com.sinoair.billing.domain.model.billing.*;
import com.sinoair.billing.domain.vo.check.EawbCheckVO;
import com.sinoair.billing.domain.vo.query.ActivityQuery;
import com.sinoair.billing.domain.vo.query.CeosQuery;
import com.sinoair.billing.service.common.CommonService;
import com.sinoair.billing.service.receipt.ReceiptRecordService;
import com.sinoair.billing.service.temp.TempRRService;
import com.sinoair.billing.service.temp.TempService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.TreeSet;

import static java.util.Comparator.comparing;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;

/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2018/1/30
 * @time: 9:59
 * @description: To change this template use File | Settings | File Templates.
 */
@Service("TempRRService")
public class TempRRServiceImpl implements TempRRService {

    private Logger logger = LoggerFactory.getLogger(TempRRServiceImpl.class);

    private final static String STATUS_Y="Y";
    private final static String STATUS_N="N";
    private int length = 1000;

    @Autowired
    private TempMapper tempMapper;

    @Autowired
    private CheckEawbResultDetailMapper eawbResultDetailMapper;

    @Autowired
    private ExpressAirWayBillMapper billMapper;

    @Autowired
    private CommonService commonService;

    @Autowired
    private PriceReceiptMapper priceReceiptMapper;

    @Autowired
    private ReceiptRecordTmpMapper recordTmpMapper;

    @Autowired
    private ReceiptRecordTmpOtherMapper recordTmpOtherMapper;

    @Autowired
    private TrapsRRConfigMapper trapsRRConfigMapper;

    @Autowired
    private ReceiptRecordService receiptRecordService;

    @Autowired
    private CheckEawbResultDetailMapper checkEawbResultDetailMapper;

    @Autowired
    private ReceiptRecordMapper receiptRecordMapper;

    @Autowired
    private SettlementObjectBalanceMapper soBalanceMapper;

    @Autowired
    private SettlementObjectMapper settlementObjectMapper;

    @Autowired
    private ExpressAirWayBillMapper expressAirWayBillMapper;


    @Override
    public List<String> selectTempNhb() {
        return tempMapper.selectTemp();
    }

    @Override
    public int deleteTempNhb(String eawb) {
        return tempMapper.deleteTempRr(eawb);
    }

    @Override
    public String handleTemp_rr(CeosQuery queryParam){
        logger.info("线程"+queryParam.getPageNum()+"开始查询===="+new Date());

        int handleSize = 0;

        try {

            PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize(),false);
            List<String> recordTmpList = tempMapper.selectTemp();
//            List<String> recordTmpList = new ArrayList<>();
//            recordTmpList.add("92152934716");
            logger.info("线程"+queryParam.getPageNum()+"查询结束===="+new Date());

            if (recordTmpList != null){
                List<CheckEawbResultDetail> updatelList = new ArrayList<>();
                List<String> deleteList = new ArrayList<>();
                List<CheckEawbResultDetail> addDetailList = new ArrayList<>();

                List<ReceiptRecordTmp> rrTmpList = new ArrayList<>();
                List<ReceiptRecordTmp> rrTmpOtherList = new ArrayList<>();
                List<String> lackEawbList = new ArrayList<>();

                logger.info("线程"+queryParam.getPageNum()+"==recordTmpList.size=="+recordTmpList.size());

                String eadCode = queryParam.getEadCode();//"INTERNATIONAL";
                String eastCode = queryParam.getEastCode(); //"ASS";
                String prName = queryParam.getRrName();  //"出口报关费";  //国内调拨费,出口报关费,货运保险费

                for (String tmpeawbPrintcode : recordTmpList){
                    ExpressAirWayBill bill = null;
                    String eawbPrintcode = null;
                    if ("2".equals(queryParam.getPrintcodeType())){
                        bill = billMapper.selectByRef2(tmpeawbPrintcode);
                        if (bill == null){
                            bill = billMapper.selectOtherByRef2(tmpeawbPrintcode);
                            if (bill == null){
                                logger.info("没有找到单号："+tmpeawbPrintcode);
                                continue;
                            }
                        }
                        eawbPrintcode = bill.getEawbPrintcode();

                    }else {
                        eawbPrintcode = tmpeawbPrintcode;
                        bill = billMapper.selectByEawbPrintCode(eawbPrintcode);
                        if (bill == null){
                            bill = billMapper.selectOtherByEawbPrintCode(eawbPrintcode);
                            if (bill == null){
                                logger.info("没有找到单号："+tmpeawbPrintcode);
                                lackEawbList.add(tmpeawbPrintcode);
                                continue;
                            }
                        }
                    }

                    EawbCheckVO param = new EawbCheckVO();
                    param.setEawbPrintcode(eawbPrintcode);
                    param.setEadCode(eadCode);
                    param.setEastCode(eastCode);
                    if (StringUtil.isNotEmpty(prName)){
                        param.setPrName(prName);
                    }

//                    List<CheckEawbResultDetail> details = eawbResultDetailMapper.selectResultDetailList(param);
//                    //不是计费的环节直接删掉
//                    if ((details == null || details.size() ==0)){
                    List<ReceiptRecordTmp> tmpList = null;
                    List<CheckEawbResultDetail> resultDetails = new ArrayList<>();
                    //指定客户类型，不通过soCode判断
                    if ("NC".equals(queryParam.getSoType())){
                        //非环节计费
                        tmpList = receiptRecordService.getRRNonList_NC(eawbPrintcode,prName);
                    }else if ("OTHER".equals(queryParam.getSoType())){
                        tmpList = receiptRecordService.getRRNonList(eawbPrintcode,prName);
                    }else{
                        tmpList = receiptRecordService.getRRNonList(eawbPrintcode,prName);
                    }


                    if (tmpList != null && !tmpList.isEmpty()){
                        for (ReceiptRecordTmp rrTmp : tmpList){
                            rrTmp.setRrType("1");
                            rrTmp.setRrActualAmount(rrTmp.getRrPlanAmount());
                            rrTmp.setRrStatus("ON");
                            rrTmp.setRrUserId(Short.valueOf("1"));
                            rrTmp.setRrHandletime(new Date());
                            rrTmp.setRrAwbType("H");
                            rrTmp.setRrRemark("");
                            rrTmp.setRrOccurtime2(rrTmp.getRrOccurtime());
                            rrTmp.setChargeableweight(BigDecimal.ZERO);
                        }
                        deleteList.add(tmpeawbPrintcode);
                        if ("NC".equals(queryParam.getSoType())){
                            //非环节计费
                            rrTmpOtherList.addAll(tmpList);
                        }else if ("OTHER".equals(queryParam.getSoType())){
                            rrTmpOtherList.addAll(tmpList);
                        }else{
                            if (ReceiptRecordConstant.CAINIAO_SO_CODE.equals(bill.getEawbSoCode())){
                                rrTmpList.addAll(tmpList);

                            }else {
                                rrTmpOtherList.addAll(tmpList);
                            }
                        }

                    }

                }


                long startstream = System.currentTimeMillis();
                List<ReceiptRecordTmp> rrList = rrTmpList.stream().collect(
                        collectingAndThen(
                                toCollection(() -> new TreeSet<>(comparing(n->n.getEawbPrintcode()+";"+n.getRrName()+";"+n.getPrId()))),ArrayList::new)
                );
                logger.info(" 去重耗时:"+(System.currentTimeMillis()-startstream)+" ms");
                if (rrList.size() > 0){
                    logger.info("新增菜鸟计费数据："+rrList.size());
                    int size = rrList.size();
                    int count = size % length != 0 ? size / length + 1 : size / length;
                    for (int i = 0; i < count; i++) {
                        List<ReceiptRecordTmp> subList = rrList.subList(i * length, ((1000 + i * length) < size ? (1000 + i * length) : size));
                        if (subList.size() > 0){
                            recordTmpMapper.insertBatch(subList);
                        }
                    }
                }

                List<ReceiptRecordTmp> rrOtherList = rrTmpOtherList.stream().collect(
                        collectingAndThen(
                                toCollection(() -> new TreeSet<>(comparing(n->n.getEawbPrintcode()+";"+n.getRrName()+";"+n.getPrId()))),ArrayList::new)
                );
                if (rrOtherList.size() > 0){
                    logger.info("新增非菜鸟计费数据："+rrOtherList.size());
                    int size = rrOtherList.size();
                    int count = size % length != 0 ? size / length + 1 : size / length;
                    for (int i = 0; i < count; i++) {
                        List<ReceiptRecordTmp> subList = rrOtherList.subList(i * length, ((1000 + i * length) < size ? (1000 + i * length) : size));
                        if (subList.size() > 0){
                            recordTmpOtherMapper.insertBatch(subList);
                        }
                    }
                }
                if (deleteList.size() > 0){
                    logger.info("删除TEMP数据："+deleteList.size());
                    int size = deleteList.size();
                    int count = size % length != 0 ? size / length + 1 : size / length;
                    for (int i = 0; i < count; i++) {
                        List<String> subList = deleteList.subList(i * length, ((1000 + i * length) < size ? (1000 + i * length) : size));
                        if (subList.size() > 0){
                            tempMapper.deleteBatchTemp(subList);
                        }
                    }
                }

                if (lackEawbList.size() > 0){
                    logger.info("线程:temprr 新增缺失的eawbprintcode数据："+lackEawbList.size());
                    int size = lackEawbList.size();
                    int count = size % length != 0 ? size / length + 1 : size / length;
                    for (int i = 0; i < count; i++) {
                        List<String> subList = lackEawbList.subList(i * length, ((length + i * length) < size ? (length + i * length) : size));
                        if (subList.size() > 0){
                            checkEawbResultDetailMapper.insertBatchLackEawb(subList);
                        }
                    }
                }

            }

        } catch (Exception e){
            e.printStackTrace();
//            MailUtil.postMail(MailConstant.MAIL_TO,MailConstant.MAIL_SUBJECT_INS_EBA,e.getMessage());
        }
        logger.info("线程"+queryParam.getPageNum()+"总用时===");


        return handleSize+"";
    }

    @Override
    public TrapsRRConfig selectById(Integer trapsId){
        return trapsRRConfigMapper.selectById(trapsId);
    }

    @Override
    public void updateTrapsRRConfig(TrapsRRConfig record){
        trapsRRConfigMapper.updateTrapsRRConfig(record);
    }

    @Override
    public int countTemp(){
        return tempMapper.countTemp();
    }

    @Override
    @Transactional
    public String handleMakeUpRR(String eawbPrintcode) {
        List<ReceiptRecord> receiptRecordList = receiptRecordMapper.selectOuterRRByEawbPrintcode(eawbPrintcode);
        ReceiptRecord receiptRecordInfo = null;
        if (receiptRecordList.size() > 0){
            receiptRecordInfo = receiptRecordList.get(0);
        }
        SettlementObject settlementObject = settlementObjectMapper.selectBySoCode(receiptRecordInfo.getSoCode());
        BigDecimal yue = settlementObject.getSoBalance();

        for (ReceiptRecord receiptRecord:receiptRecordList){

            BigDecimal rrAmount = receiptRecord.getRrPlanAmount();
            receiptRecord.setRrId(null);
            receiptRecord.setRrPlanAmount(rrAmount.negate());
            receiptRecord.setRrActualAmount(receiptRecord.getRrPlanAmount());
            receiptRecord.setRrHandletime(new Date());
            receiptRecord.setRrOccurtime(new Date());
            receiptRecord.setDmId(null);
//            receiptRecord.setRrName("服务赔付");



            boolean result = false;
            int re=settlementObjectMapper.addSoBanlance(receiptRecord.getSoCode(),rrAmount);
            logger.info("更新余额结果=="+re);
            result=true;
//        if(re==1){
//            result=true;
//        }else if(re>1||re<0){
//            logger.info("setRollbackOnly==");
//            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
//        }

            if (result){
                receiptRecordMapper.insertSelective(receiptRecord);
                yue=yue.add(rrAmount);
                SettlementObjectBalance soBalance=new SettlementObjectBalance();
                soBalance.setSoCode(receiptRecord.getSoCode());
                soBalance.setSbAmount(receiptRecord.getRrPlanAmount());
                soBalance.setSbExplain("取消");
                soBalance.setSbFlowno(receiptRecord.getEawbReference2());
                soBalance.setSbType("ADJUCT");
                soBalance.setAmountType("R");
                soBalance.setEawbChargeableweight(receiptRecord.getEawbChargeableweight());
                soBalance.setEawbPrintcode(receiptRecord.getEawbPrintcode());
                soBalance.setEawbReference1(receiptRecord.getEawbReference1());
                soBalance.setEawbReference2(receiptRecord.getEawbReference2());
                soBalance.setPrName(receiptRecord.getRrName());
//            soBalance.setRrId(receiptRecord.getRrId());
                soBalance.setRrOccurtime(new Date());
                soBalance.setSbHandletime(new Date());
                soBalance.setSoBalance(yue);
                soBalanceMapper.insertSelective(soBalance);
            }
        }



        return null;
    }


    @Override
    @Transactional
    public String handleSupplementaryBill(String eawbPrintcode) {
        ExpressAirWayBill expressAirWayBill = expressAirWayBillMapper.selectByEawbPrintCode(eawbPrintcode);
        if (expressAirWayBill == null){
            List<ExpressAirWayBill> billList = expressAirWayBillMapper.selectByRef1(eawbPrintcode);
            if (billList.size() > 0){
                expressAirWayBill = billList.get(0);
            }
        }
        if (expressAirWayBill == null){
            logger.info("单号不存在："+eawbPrintcode);
            return null;
        }
        eawbPrintcode = expressAirWayBill.getEawbPrintcode();

        String rrName = "操作费";
        ReceiptRecord receiptParam = new ReceiptRecord();
        receiptParam.setEawbPrintcode(eawbPrintcode);
        receiptParam.setRrName(rrName);
        int rrCount = receiptRecordMapper.countReceiptRecord(receiptParam);
        if (rrCount > 0){
            logger.info("费用已存在："+eawbPrintcode);
            return null;
        }


        List<ReceiptRecord> receiptRecordList = receiptRecordMapper.listPriceReceipt_nc(eawbPrintcode,rrName);
        ReceiptRecord receiptRecordInfo = null;
        if (receiptRecordList.size() == 0){
            logger.info("未计算到费用："+eawbPrintcode);
            return null;
        }
        receiptRecordInfo = receiptRecordList.get(0);

        SettlementObject settlementObject = settlementObjectMapper.selectBySoCode(receiptRecordInfo.getSoCode());
        BigDecimal yue = settlementObject.getSoBalance();

        for (ReceiptRecord receiptRecord:receiptRecordList){

            BigDecimal rrAmount = receiptRecord.getRrPlanAmount();
//            receiptRecord.setRrId(null);
//            receiptRecord.setRrPlanAmount(rrAmount.negate());
            receiptRecord.setRrActualAmount(receiptRecord.getRrPlanAmount());
            receiptRecord.setRrHandletime(new Date());
            receiptRecord.setRrOccurtime(new Date());
            receiptRecord.setDmId(null);
//            receiptRecord.setRrName("服务赔付");



            boolean result = false;
            int re=settlementObjectMapper.cutSoBanlance(receiptRecord.getSoCode(),rrAmount.doubleValue());
            logger.info("更新余额结果=="+re);
            result=false;
            if(re==1){
                result=true;
            }else if(re>1||re<0){
                logger.info("setRollbackOnly==");
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            }

            if (result){
                receiptRecordMapper.insertSelective(receiptRecord);
                yue=yue.subtract(rrAmount);
                SettlementObjectBalance soBalance=new SettlementObjectBalance();
                soBalance.setSoCode(receiptRecord.getSoCode());
                soBalance.setSbAmount(receiptRecord.getRrPlanAmount());
                soBalance.setSbExplain("扣款");
                soBalance.setSbFlowno(receiptRecord.getEawbReference2());
                soBalance.setSbType("DEDUCT");
                soBalance.setAmountType("P");
                soBalance.setEawbChargeableweight(receiptRecord.getEawbChargeableweight());
                soBalance.setEawbPrintcode(receiptRecord.getEawbPrintcode());
                soBalance.setEawbReference1(receiptRecord.getEawbReference1());
                soBalance.setEawbReference2(receiptRecord.getEawbReference2());
                soBalance.setPrName(receiptRecord.getRrName());
//            soBalance.setRrId(receiptRecord.getRrId());
                soBalance.setRrOccurtime(new Date());
                soBalance.setSbHandletime(new Date());
                soBalance.setSoBalance(yue);
                soBalanceMapper.insertSelective(soBalance);
            }
        }

        return "success";
    }
}
