package com.sinoair.billing.service.temp.impl;


import com.github.pagehelper.PageHelper;
import com.sinoair.billing.core.util.CheckUtil;
import com.sinoair.billing.core.util.MailUtil;
import com.sinoair.billing.core.util.StringUtil;
import com.sinoair.billing.dao.billing.*;
import com.sinoair.billing.dao.ceos.CeosActivityMapper;
import com.sinoair.billing.domain.constant.CheckConstant;
import com.sinoair.billing.domain.constant.MailConstant;
import com.sinoair.billing.domain.model.billing.*;
import com.sinoair.billing.domain.vo.check.EawbCheckVO;
import com.sinoair.billing.domain.vo.query.ActivityQuery;
import com.sinoair.billing.domain.vo.query.CeosQuery;
import com.sinoair.billing.service.temp.TempService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2018/1/30
 * @time: 9:59
 * @description: To change this template use File | Settings | File Templates.
 */
@Service("TempService")
public class TempServiceImpl implements TempService {

    private Logger logger = LoggerFactory.getLogger(TempServiceImpl.class);


    @Autowired
    private TempMapper tempMapper;


    @Override
    public List<String> selectTempEa(){
        return tempMapper.selectTempEa();
    }
    @Override
    public int deleteBatchTempEa(List<String> list){
        return tempMapper.deleteBatchTempEa(list);
    }

    @Override
    public     int insertBatchTempEa(List<String> list){
        return tempMapper.insertBatchTempEa(list);
    }

    @Override
    public List<String> selectTempAsendia() {
        return tempMapper.selectTempAsendia();
    }
}
