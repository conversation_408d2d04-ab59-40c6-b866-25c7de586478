package com.sinoair.billing.thread;

import com.sinoair.billing.core.util.SpringContextUtil;
import com.sinoair.billing.dao.billing.SettlementObjectMapper;
import com.sinoair.billing.domain.constant.EbaConstant;
import com.sinoair.billing.domain.model.billing.PaymentRecord;
import com.sinoair.billing.domain.model.billing.ReceiptEawb;
import com.sinoair.billing.domain.model.billing.ReceiptRecordTmp;
import com.sinoair.billing.domain.model.billing.SettlementObject;
import com.sinoair.billing.domain.vo.query.CeosQuery;
import com.sinoair.billing.service.common.CommonService;
import com.sinoair.billing.service.receipt.ReceiptRecordService;
import com.sinoair.billing.service.syn.SynPaymentRecordService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.TreeSet;
import java.util.concurrent.CountDownLatch;

import static java.util.Comparator.comparing;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;

/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2019-02-25
 * @time: 15:31
 * @description:
 * To change this template use File | Settings | File Templates.
 */

public class CheckInnerBillNewThread extends Thread {

    private Logger logger = LoggerFactory.getLogger(CheckInnerBillNewThread.class);

    public int threadNum;

    public int pageSize;

    CountDownLatch latch;

    public List<ReceiptEawb> receiptEawbList;


    public CheckInnerBillNewThread(int threadNum, int pageSize, CountDownLatch latch, List<ReceiptEawb> receiptEawbList) {

        this.threadNum = threadNum;
        this.pageSize = pageSize;
        this.latch = latch;
        this.receiptEawbList = receiptEawbList;
    }

    /**
     * If this thread was constructed using a separate
     * <code>Runnable</code> run object, then that
     * <code>Runnable</code> object's <code>run</code> method is called;
     * otherwise, this method does nothing and returns.
     * <p>
     * Subclasses of <code>Thread</code> should override this method.
     *
     * @see #
     * @see #
     */
    @Override
    public void run() {
        SynPaymentRecordService synPaymentRecordService = SpringContextUtil.getBean("SynPaymentRecordService");
        ReceiptRecordService receiptRecordService = SpringContextUtil.getBean("ReceiptRecordService");
        CommonService commonService = SpringContextUtil.getBean("CommonService");
        SettlementObjectMapper settlementObjectMapper = SpringContextUtil.getBean("SettlementObjectMapper");
        logger.info("【内部交易费计费】多线程同步PR开始=="+threadNum);
        try {
            int insertLength = 1000;

            List<ReceiptRecordTmp> receiptRecordList = new ArrayList<>();
            for (ReceiptEawb receiptEawb : receiptEawbList){
                List<ReceiptRecordTmp> tempRecordList = receiptRecordService.getRRInnerListNew(receiptEawb.getEawbPrintcode(),receiptEawb.getSoCode(),receiptEawb.getpCode());
                if (tempRecordList != null){
                    tempRecordList.stream().forEach(rr -> {
                        rr.setCompanyId(receiptEawb.getCompanyId());
                        rr.setSoCode(receiptEawb.getSoCode());
                        SettlementObject settlementObject = settlementObjectMapper.selectBySoCode(receiptEawb.getSoCode());
                        rr.setSoMode(settlementObject.getSoMode());
                        rr.setRrType(EbaConstant.R_TYPE_I);
                        rr.setRrActualAmount(rr.getRrPlanAmount());
                        rr.setRrStatus("ON");
                        rr.setRrUserId(Short.valueOf("1"));
                        rr.setRrHandletime(new Date());
                        rr.setRrAwbType("H");
                        rr.setRrRemark("");
                        rr.setRrOccurtime2(rr.getRrOccurtime());
                        rr.setChargeableweight(BigDecimal.ZERO);
                    });
                    receiptRecordList.addAll(tempRecordList);
                }
            }

            List<ReceiptRecordTmp> receiptList = receiptRecordList.stream().collect(
                    collectingAndThen(
                            toCollection(() -> new TreeSet<>(comparing(n->n.getEawbPrintcode()+";"+n.getCompanyId()+";"+n.getSoCode()+";"+n.getRrName()))), ArrayList::new)
            );
            logger.info("【内部交易费计费】过滤去重后数量："+receiptList.size());
            int size = receiptList.size();
            int count = size % insertLength != 0 ? size / insertLength + 1 : size / insertLength;
            for (int i = 0; i < count; i++) {
                List<ReceiptRecordTmp> subList = receiptList.subList(i * insertLength, ((1000 + i * insertLength) < size ? (1000 + i * insertLength) : size));
                if (subList.size() > 0){
                    CeosQuery param = new CeosQuery();
                    param.setPageNum(threadNum);
                    param.setPageSize(pageSize);
                    param.setSerialNumber(i);
                    receiptRecordService.saveInnerReceiptsNew(subList);
                }
            }

        } catch (Exception e){
            e.printStackTrace();
        } finally {
            //让latch中的值减1
            latch.countDown();
        }
        //执行推送
        logger.info("多线程同步【内部交易费计费】PR "+threadNum+"结束");
    }

}
