package com.sinoair.billing.thread;

import com.sinoair.billing.core.util.SpringContextUtil;
import com.sinoair.billing.domain.vo.query.CeosQuery;
import com.sinoair.billing.service.check.CheckInsEbaService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.CountDownLatch;

/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2019-02-25
 * @time: 15:31
 * @description:
 * To change this template use File | Settings | File Templates.
 */

public class CheckInsEbaTmpThread extends Thread {

    private Logger logger = LoggerFactory.getLogger(CheckInsEbaTmpThread.class);

    public int threadNum;
    public int rowNum;//

    public int allSize;
    public String soCode;
    public String startEbaHandletime;
    public String endEbaHandletime;
    CountDownLatch latch;

    public CheckInsEbaTmpThread(int threadNum, int rowNum, CountDownLatch latch) {

        this.threadNum = threadNum;
        this.rowNum = rowNum;
        this.latch = latch;


    }

    /**
     * If this thread was constructed using a separate
     * <code>Runnable</code> run object, then that
     * <code>Runnable</code> object's <code>run</code> method is called;
     * otherwise, this method does nothing and returns.
     * <p>
     * Subclasses of <code>Thread</code> should override this method.
     *
     * @see #
     * @see #
     */
    @Override
    public void run() {
        CheckInsEbaService checkInsEbaService = SpringContextUtil.getBean("CheckInsEbaService");
        logger.info("CheckInsEbaTmpThread 线程池开始=="+threadNum);
        try {
            CeosQuery ceosQuery = new CeosQuery();

            ceosQuery.setPageNum(threadNum);
            ceosQuery.setPageSize(rowNum);


            checkInsEbaService.handleEbaTmp(ceosQuery);

        } catch (Exception e){
            e.printStackTrace();
        } finally {
            //让latch中的值减1
            latch.countDown();
        }

        //执行推送
        logger.info("线程"+threadNum+"结束");
    }

}
