package com.sinoair.billing.thread;

import com.sinoair.billing.core.util.SpringContextUtil;
import com.sinoair.billing.core.util.StringUtil;
import com.sinoair.billing.domain.model.billing.PaymentRecord;
import com.sinoair.billing.domain.model.billing.ReceiptEawb;
import com.sinoair.billing.domain.model.billing.ReceiptRecordTmp;
import com.sinoair.billing.domain.vo.check.EawbCheckVO;
import com.sinoair.billing.domain.vo.query.CeosQuery;
import com.sinoair.billing.service.check.EawbCheckDetailService;
import com.sinoair.billing.service.common.CommonService;
import com.sinoair.billing.service.syn.SynPaymentRecordService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.TreeSet;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

import static java.util.Comparator.comparing;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;

/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2019-02-25
 * @time: 15:31
 * @description:
 * To change this template use File | Settings | File Templates.
 */

public class InsCheckPRThread extends Thread {

    private Logger logger = LoggerFactory.getLogger(InsCheckPRThread.class);

    public int threadNum;

    public int pageSize;


    CountDownLatch latch;

    public List<ReceiptEawb> receiptEawbList;


    public InsCheckPRThread(int threadNum, int pageSize, CountDownLatch latch, List<ReceiptEawb> receiptEawbList) {

        this.threadNum = threadNum;
        this.pageSize = pageSize;
        this.latch = latch;
        this.receiptEawbList = receiptEawbList;
    }

    /**
     * If this thread was constructed using a separate
     * <code>Runnable</code> run object, then that
     * <code>Runnable</code> object's <code>run</code> method is called;
     * otherwise, this method does nothing and returns.
     * <p>
     * Subclasses of <code>Thread</code> should override this method.
     *
     * @see #
     * @see #
     */
    @Override
    public void run() {
        SynPaymentRecordService synPaymentRecordService = SpringContextUtil.getBean("SynPaymentRecordService");
        EawbCheckDetailService detailService = SpringContextUtil.getBean("EawbCheckDetailService");
        CommonService commonService = SpringContextUtil.getBean("CommonService");
        logger.info("多线程同步PR开始=="+threadNum);
        try {
            int insertLength = 1000;

            List<PaymentRecord> paymentRecordList = new ArrayList<>();
            for (ReceiptEawb receiptEawb : receiptEawbList){
//                if ("Y".equals(receiptEawb.getpStatus())){
//                    continue;
//                }
                EawbCheckVO temp = new EawbCheckVO();
                temp.setEawbPrintcode(receiptEawb.getEawbPrintcode());
                temp.setEawbSoCode(receiptEawb.getSoCode());
                List<PaymentRecord> tempRecordList = detailService.selectPrByCondition(temp);
                if (tempRecordList != null){
                    List<PaymentRecord> filterList = tempRecordList.stream().filter(s->(s.getPpPrice() != null && s.getPpPrice().compareTo(BigDecimal.ZERO) >0)).collect(Collectors.toList());

                    if (filterList.size() > 0){
                        paymentRecordList.addAll(filterList);
                    }
                }
            }
            List<PaymentRecord> addList = new ArrayList<>();
            for (PaymentRecord paymentRecord : paymentRecordList){
                if (paymentRecord.getPpPrice() != null && paymentRecord.getPpPrice().compareTo(BigDecimal.ZERO) > 0){
                    paymentRecord.setPrActualAmount(paymentRecord.getPpPrice());
                    paymentRecord.setPrPlanAmount(paymentRecord.getPpPrice());
                    addList.add(paymentRecord);
                    continue;
                }

            }

            List<PaymentRecord> payResList = addList.stream().collect(
                    collectingAndThen(
                            toCollection(() -> new TreeSet<>(comparing(n->n.getEawbPrintcode()+";"+n.getCompanyId()+";"+n.getSoCode()+";"+n.getPpId()))), ArrayList::new)
            );
            logger.info("过滤去重后数量："+payResList.size());
            int size = payResList.size();
            int count = size % insertLength != 0 ? size / insertLength + 1 : size / insertLength;
            for (int i = 0; i < count; i++) {
                List<PaymentRecord> subList = payResList.subList(i * insertLength, ((1000 + i * insertLength) < size ? (1000 + i * insertLength) : size));
                if (subList.size() > 0){
                    CeosQuery param = new CeosQuery();

                    param.setPageNum(threadNum);
                    param.setPageSize(pageSize);
                    param.setSerialNumber(i);

                    synPaymentRecordService.synPaymentRecord(param,subList);
                }
            }

        } catch (Exception e){
            e.printStackTrace();
        } finally {
            //让latch中的值减1
            latch.countDown();
        }

        //执行推送
        logger.info("多线程同步PR "+threadNum+"结束");
    }

}
