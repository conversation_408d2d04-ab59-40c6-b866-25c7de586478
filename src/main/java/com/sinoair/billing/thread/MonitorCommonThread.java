package com.sinoair.billing.thread;

import com.sinoair.billing.core.util.SpringContextUtil;
import com.sinoair.billing.domain.vo.query.CeosQuery;
import com.sinoair.billing.service.syn.SynCeosEawbService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2019-02-25
 * @time: 15:31
 * @description:
 * To change this template use File | Settings | File Templates.
 */

public class MonitorCommonThread extends Thread {

    private Logger logger = LoggerFactory.getLogger(MonitorCommonThread.class);

    public int threadNum;

    public String threadName;

    public int rowNum;//

    CountDownLatch latch;

    public List<String> resultList;



    public MonitorCommonThread(int threadNum, int rowNum, String threadName, CountDownLatch latch, List<String> resultList) {

        this.threadNum = threadNum;
        this.rowNum = rowNum;
        this.threadName = threadName;
        this.latch = latch;
        this.resultList = resultList;

    }

    /**
     * If this thread was constructed using a separate
     * <code>Runnable</code> run object, then that
     * <code>Runnable</code> object's <code>run</code> method is called;
     * otherwise, this method does nothing and returns.
     * <p>
     * Subclasses of <code>Thread</code> should override this method.
     *
     * @see #
     * @see #
     */
    @Override
    public void run() {
        SynCeosEawbService synCeosEawbService = SpringContextUtil.getBean("SynCeosEawbService");
        logger.info("监控"+threadName+"线程池开始=="+threadNum+" 大小："+resultList.size());
        try {
            int insertLength = 1000;

            int size = resultList.size();
            int count = size % insertLength != 0 ? size / insertLength + 1 : size / insertLength;
            for (int i = 0; i < count; i++) {
                List<String> subList = resultList.subList(i * insertLength, ((1000 + i * insertLength) < size ? (1000 + i * insertLength) : size));
                if (subList.size() > 0){
                    CeosQuery param = new CeosQuery();

                    param.setPageNum(threadNum);
                    param.setPageSize(rowNum);
                    param.setSerialNumber(i);

                    synCeosEawbService.MonitorSynCeosEawb(param,subList);
                }
            }


            //synCeosEawbService.;
        } catch (Exception e){
            e.printStackTrace();
        } finally {
            //让latch中的值减1
            latch.countDown();
        }

        //执行推送
        logger.info("监控"+threadName+"线程"+threadNum+"结束");
    }

}
