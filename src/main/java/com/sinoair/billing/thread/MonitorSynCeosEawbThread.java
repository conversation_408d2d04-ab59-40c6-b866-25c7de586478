package com.sinoair.billing.thread;

import com.sinoair.billing.core.util.SpringContextUtil;
import com.sinoair.billing.domain.model.billing.ExpressAirWayBill;
import com.sinoair.billing.service.syn.SynCeosEawbService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2019-02-25
 * @time: 15:31
 * @description:
 * To change this template use File | Settings | File Templates.
 */

public class MonitorSynCeosEawbThread extends Thread {

    private Logger logger = LoggerFactory.getLogger(MonitorSynCeosEawbThread.class);

    public int threadNum;

    public int pageSize;

    public String startEbaHandletime;

    public String endEbaHandletime;

    CountDownLatch latch;

    public List<ExpressAirWayBill> billCeosList;


    public MonitorSynCeosEawbThread(int threadNum, int pageSize, String startEbaHandletime, String endEbaHandletime,
                                    CountDownLatch latch, List<ExpressAirWayBill> billCeosList) {

        this.threadNum = threadNum;
        this.pageSize = pageSize;
        this.startEbaHandletime = startEbaHandletime;
        this.endEbaHandletime = endEbaHandletime;
        this.latch = latch;
        this.billCeosList = billCeosList;

    }

    /**
     * If this thread was constructed using a separate
     * <code>Runnable</code> run object, then that
     * <code>Runnable</code> object's <code>run</code> method is called;
     * otherwise, this method does nothing and returns.
     * <p>
     * Subclasses of <code>Thread</code> should override this method.
     *
     * @see #
     * @see #
     */
    @Override
    public void run() {
        SynCeosEawbService synCeosEawbService = SpringContextUtil.getBean("SynCeosEawbService");
        logger.info("同步ceos-eawb线程池开始=="+threadNum);
        try {
//            CeosQuery param = new CeosQuery();
//            param.setStartEbaHandletime(startEbaHandletime);
//            param.setEndEbaHandletime(endEbaHandletime);
//            param.setPageNum(threadNum);
//            param.setPageSize(pageSize);
//
//            synCeosEawbService.synCeosEawb(param);

            int insertLength = 1000;

            int size = billCeosList.size();
            int count = size % insertLength != 0 ? size / insertLength + 1 : size / insertLength;
            for (int i = 0; i < count; i++) {
                List<ExpressAirWayBill> subList = billCeosList.subList(i * insertLength, ((1000 + i * insertLength) < size ? (1000 + i * insertLength) : size));
                if (subList.size() > 0){
                    synCeosEawbService.monitorSynCeosEawb(i,subList);
                }
            }
        } catch (Exception e){
            e.printStackTrace();
        } finally {
            //让latch中的值减1
            latch.countDown();
        }

        //执行推送
        logger.info("同步ceos-eawb线程"+threadNum+"结束");
    }

}
