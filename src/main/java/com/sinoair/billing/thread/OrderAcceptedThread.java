package com.sinoair.billing.thread;

import com.sinoair.billing.core.util.SpringContextUtil;
import com.sinoair.billing.domain.model.billing.SettlementObject;
import com.sinoair.billing.domain.vo.check.EawbCheckVO;
import com.sinoair.billing.service.order.OrderPoolService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2019-02-25
 * @time: 15:31
 * @description:
 * To change this template use File | Settings | File Templates.
 */

public class OrderAcceptedThread extends Thread {

    private Logger logger = LoggerFactory.getLogger(OrderAcceptedThread.class);

    public int threadNum;

    public int pageSize;

    public Long beginNo;

    public Long endNo;

    public List<SettlementObject> soList;

    CountDownLatch latch;


    public OrderAcceptedThread(int threadNum, int pageSize,Long beginNo,Long endNo,List<SettlementObject> soList,
                               CountDownLatch latch) {

        this.threadNum = threadNum;
        this.pageSize = pageSize;
        this.beginNo = beginNo;
        this.endNo = endNo;
        this.soList = soList;
        this.latch = latch;

    }

    /**
     * If this thread was constructed using a separate
     * <code>Runnable</code> run object, then that
     * <code>Runnable</code> object's <code>run</code> method is called;
     * otherwise, this method does nothing and returns.
     * <p>
     * Subclasses of <code>Thread</code> should override this method.
     *
     * @see #
     * @see #
     */
    @Override
    public void run() {
        OrderPoolService orderPoolService = SpringContextUtil.getBean("OrderPoolService");
        logger.info("监控环节线程池开始=="+threadNum);
        try {
            EawbCheckVO param = new EawbCheckVO();
            param.setBeginNo(beginNo);
            param.setEndNo(endNo);
            param.setPageNum(threadNum);
            param.setPageSize(pageSize);

            orderPoolService.handleAcceptedOrder(param,soList);
        } catch (Exception e){
            e.printStackTrace();
        } finally {
            //让latch中的值减1
            latch.countDown();
        }

        //执行推送
        logger.info("监控环节线程"+threadNum+"结束");
    }

}
