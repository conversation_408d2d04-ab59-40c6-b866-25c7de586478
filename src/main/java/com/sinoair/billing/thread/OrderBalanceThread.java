package com.sinoair.billing.thread;

import com.sinoair.billing.core.util.SpringContextUtil;
import com.sinoair.billing.domain.model.billing.ManifestEawb;
import com.sinoair.billing.domain.model.billing.SettlementObject;
import com.sinoair.billing.domain.model.billing.SinotransOrderPool;
import com.sinoair.billing.domain.vo.check.EawbCheckVO;
import com.sinoair.billing.service.order.OrderPoolService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2019-02-25
 * @time: 15:31
 * @description:
 * To change this template use File | Settings | File Templates.
 */

public class OrderBalanceThread extends Thread {

    private Logger logger = LoggerFactory.getLogger(OrderBalanceThread.class);

    public int threadNum;

    public int pageSize;

    public Long dmId;

    CountDownLatch latch;

    public List<ManifestEawb> resultList;


    public OrderBalanceThread(int threadNum, int pageSize, CountDownLatch latch,List<ManifestEawb> resultList) {

        this.threadNum = threadNum;
        this.pageSize = pageSize;
//        this.dmId = dmId;
        this.latch = latch;
        this.resultList = resultList;

    }

    /**
     * If this thread was constructed using a separate
     * <code>Runnable</code> run object, then that
     * <code>Runnable</code> object's <code>run</code> method is called;
     * otherwise, this method does nothing and returns.
     * <p>
     * Subclasses of <code>Thread</code> should override this method.
     *
     * @see #
     * @see #
     */
    @Override
    public void run() {
        OrderPoolService orderPoolService = SpringContextUtil.getBean("OrderPoolService");
        logger.info("监控环节线程池开始=="+threadNum);
        try {
            int insertLength = 1000;

            int size = resultList.size();
            int count = size % insertLength != 0 ? size / insertLength + 1 : size / insertLength;
            for (int i = 0; i < count; i++) {
                List<ManifestEawb> subList = resultList.subList(i * insertLength, ((1000 + i * insertLength) < size ? (1000 + i * insertLength) : size));
                if (subList.size() > 0){
//                    logger.info("监控环节线程池开始=="+threadNum+" 序号："+i);
                    orderPoolService.handleBalanceOrder(threadNum,pageSize,subList);
                }
            }
        } catch (Exception e){
            e.printStackTrace();
        } finally {
            //让latch中的值减1
            latch.countDown();
        }

        //执行推送
        logger.info("监控环节线程"+threadNum+"结束");
    }

}
