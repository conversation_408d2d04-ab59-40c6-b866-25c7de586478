package com.sinoair.billing.thread;

import com.sinoair.billing.core.util.SpringContextUtil;
import com.sinoair.billing.domain.model.billing.ExpressAirWayBill;
import com.sinoair.billing.domain.model.ceosbi.EawbEadFlat;
import com.sinoair.billing.service.syn.SynCeosEadFlatService;
import com.sinoair.billing.service.syn.SynCeosEawbService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;

/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2019-02-25
 * @time: 15:31
 * @description:
 * To change this template use File | Settings | File Templates.
 */

public class SynCeosEadFlatThread extends Thread {

    private Logger logger = LoggerFactory.getLogger(SynCeosEadFlatThread.class);

    public int threadNum;

    public int pageSize;


    CountDownLatch latch;

    public List<EawbEadFlat> eadFlatList;


    public SynCeosEadFlatThread(int threadNum, int pageSize,
                                CountDownLatch latch, List<EawbEadFlat> eadFlatList) {

        this.threadNum = threadNum;
        this.pageSize = pageSize;

        this.latch = latch;
        this.eadFlatList = eadFlatList;

    }

    /**
     * If this thread was constructed using a separate
     * <code>Runnable</code> run object, then that
     * <code>Runnable</code> object's <code>run</code> method is called;
     * otherwise, this method does nothing and returns.
     * <p>
     * Subclasses of <code>Thread</code> should override this method.
     *
     * @see #
     * @see #
     */
    @Override
    public void run() {
        SynCeosEadFlatService synCeosEadFlatService = SpringContextUtil.getBean("SynCeosEadFlatService");
        logger.info("同步ceos-ead-flat线程池开始=="+threadNum);
        try {


            int insertLength = 1000;

            int size = eadFlatList.size();
            int count = size % insertLength != 0 ? size / insertLength + 1 : size / insertLength;
            logger.info("线程池=="+threadNum+" count:"+count);
            for (int i = 0; i < count; i++) {
                List<EawbEadFlat> subList = eadFlatList.subList(i * insertLength, ((1000 + i * insertLength) < size ? (1000 + i * insertLength) : size));
                if (subList.size() > 0){
                    synCeosEadFlatService.synCeosEadFlat(i,subList);
                }
            }
        } catch (Exception e){
            e.printStackTrace();
        } finally {
            //让latch中的值减1
            latch.countDown();
        }

        //执行推送
        logger.info("同步ceos-ead-flat线程池结束=="+threadNum+"结束");
    }

}
