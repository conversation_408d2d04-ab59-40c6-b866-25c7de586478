package com.sinoair.billing.thread;

import com.sinoair.billing.core.util.SpringContextUtil;
import com.sinoair.billing.domain.model.billing.CheckSynEbaConfig;
import com.sinoair.billing.domain.model.billing.ExpressBusinessActivity;
import com.sinoair.billing.domain.vo.query.CeosQuery;
import com.sinoair.billing.service.check.CheckInsEbaService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2019-02-25
 * @time: 15:31
 * @description:
 * To change this template use File | Settings | File Templates.
 */

public class SynNonActivityThread extends Thread {

    private Logger logger = LoggerFactory.getLogger(SynNonActivityThread.class);

    public int threadNum;

    public int pageSize;

    public List<ExpressBusinessActivity> activities;

    public List<CheckSynEbaConfig> configList;


    CountDownLatch latch;


    public SynNonActivityThread(int threadNum, int pageSize, CountDownLatch latch, List<ExpressBusinessActivity> activities,
                                List<CheckSynEbaConfig> configList) {

        this.threadNum = threadNum;
        this.pageSize = pageSize;
        this.latch = latch;
        this.activities = activities;
        this.configList = configList;
    }

    /**
     * If this thread was constructed using a separate
     * <code>Runnable</code> run object, then that
     * <code>Runnable</code> object's <code>run</code> method is called;
     * otherwise, this method does nothing and returns.
     * <p>
     * Subclasses of <code>Thread</code> should override this method.
     *
     * @see #
     * @see #
     */
    @Override
    public void run() {
        CheckInsEbaService insEbaService = SpringContextUtil.getBean("CheckInsEbaService");
        logger.info("多线程同步non_activity开始=="+threadNum);
        try {
            CeosQuery param = new CeosQuery();

            param.setPageNum(threadNum);
            param.setPageSize(pageSize);

            insEbaService.handleSynEba(threadNum,activities,configList);
        } catch (Exception e){
            e.printStackTrace();
        } finally {
            //让latch中的值减1
            latch.countDown();
        }

        //执行推送
        logger.info("多线程同步non_eba "+threadNum+"结束");
    }

}
