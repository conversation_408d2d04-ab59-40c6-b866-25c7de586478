package com.sinoair.billing.thread;

import com.sinoair.billing.core.util.SpringContextUtil;
import com.sinoair.billing.domain.vo.query.CeosQuery;
import com.sinoair.billing.service.temp.TempRRService;
import com.sinoair.billing.service.temp.TempService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.CountDownLatch;

/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2019-02-25
 * @time: 15:31
 * @description:
 * To change this template use File | Settings | File Templates.
 */

public class TempRRThread extends Thread {

    private Logger logger = LoggerFactory.getLogger(TempRRThread.class);

    public int threadNum;
    public int rowNum;//

    public String eadCode;
    public String eastCode;
    public String rrName;
    public String printcodeType;
    public String soType;

    CountDownLatch latch;


    public TempRRThread(int threadNum, int rowNum,String eadCode,String eastCode,String rrName,String printcodeType,String soType,CountDownLatch latch) {

        this.threadNum = threadNum;
        this.rowNum = rowNum;

        this.eadCode = eadCode;
        this.eastCode = eastCode;
        this.rrName = rrName;
        this.printcodeType = printcodeType;
        this.soType = soType;

        this.latch = latch;

    }

    /**
     * If this thread was constructed using a separate
     * <code>Runnable</code> run object, then that
     * <code>Runnable</code> object's <code>run</code> method is called;
     * otherwise, this method does nothing and returns.
     * <p>
     * Subclasses of <code>Thread</code> should override this method.
     *
     * @see #
     * @see #
     */
    @Override
    public void run() {
        TempRRService tempService = SpringContextUtil.getBean("TempRRService");
        logger.info("TempRRThread 线程池开始=="+threadNum);
        try {
            CeosQuery ceosQuery = new CeosQuery();

            ceosQuery.setPageNum(threadNum);
            ceosQuery.setPageSize(rowNum);

            ceosQuery.setEadCode(eadCode);
            ceosQuery.setEastCode(eastCode);
            ceosQuery.setRrName(rrName);
            ceosQuery.setPrintcodeType(printcodeType);
            ceosQuery.setSoType(soType);


            tempService.handleTemp_rr(ceosQuery);

        } catch (Exception e){
            e.printStackTrace();
        } finally {
            //让latch中的值减1
            latch.countDown();
        }

        //执行推送
        logger.info("线程"+threadNum+"结束");
    }

}
