package com.sinoair.billing.thread;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: 大雄
 * @date: 2019-02-25
 * @time: 16:21
 * @description: 线程池控制器
 * To change this template use File | Settings | File Templates.
 */
public class ThreadPoolCenter {

    /**
     * 初始化线程池
     */
    private static final ExecutorService RECEIPT_THREAD_POOL = Executors.newCachedThreadPool();
/*
    private static final ThreadPoolExecutor threadPool = new ThreadPoolExecutor(
            1,1,0, TimeUnit.SECONDS,
            new ArrayBlockingQueue<Runnable>(1),
            new ThreadPoolExecutor.DiscardOldestPolicy());
*/

    /**
     * 将线程推到线程池中执行
     * @param thread
     */
    public static void execRecordThread(Thread thread){
        RECEIPT_THREAD_POOL.execute(thread);
    }
}
