package com.sinoair.billing.thread;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: 大雄
 * @date: 2019-06-19
 * @time: 10:42
 * @description: To change this template use File | Settings | File Templates.
 */
public class ThreadTest extends Thread {

    private Logger logger = LoggerFactory.getLogger(ThreadTest.class);

    private Long threadId;

    private ThreadTest(){}

    public ThreadTest(Long threadId) {
        this.threadId = threadId;
    }

    /**
     * When an object implementing interface <code>Runnable</code> is used
     * to create a thread, starting the thread causes the object's
     * <code>run</code> method to be called in that separately executing
     * thread.
     * <p>
     * The general contract of the method <code>run</code> is that it may
     * take any action whatsoever.
     *
     * @see Thread#run()
     */
    @Override
    public void run() {
        logger.info("定时任务【"+threadId+"】开始执行");
        try {
            sleep(1000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        logger.info("定时任务【"+threadId+"】开始结束");
    }
}
