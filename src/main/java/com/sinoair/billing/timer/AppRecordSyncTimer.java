package com.sinoair.billing.timer;

import code.sinoair.core.support.cron.SinoairCron;
import com.sinoair.billing.core.util.SpringContextUtil;
import com.sinoair.billing.service.receipt.ReceiptService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 定时同步小程序记录
 */
public class AppRecordSyncTimer extends SinoairCron {

    private Logger logger = LoggerFactory.getLogger(AppRecordSyncTimer.class);


    public AppRecordSyncTimer(String cronName, long timeout, boolean isExit) {
        super(cronName, timeout, isExit);
    }


    @Override
    public void cronThreadRun() throws Exception {
        logger.info("定时同步小程序费用：");
        long start = System.currentTimeMillis();
        ReceiptService receiptService=SpringContextUtil.getBean("ReceiptService");
        receiptService.syncAppRecord();
        logger.info("定时同步小程序费用结束："+(System.currentTimeMillis()-start));
    }
}
