package com.sinoair.billing.timer;

import code.sinoair.core.support.cron.SinoairCron;
import com.sinoair.billing.core.util.SpringContextUtil;
import com.sinoair.billing.service.email.NoticeService;
import com.sinoair.billing.service.manifest.BmsManifestService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: nhb
 * @date: 2020/3/17
 * @time: 10:39
 * @description: 清单管理-BMS清单汇总 【暂不使用】
 */
public class BmsManifestCollectTimer extends SinoairCron {

    private Logger logger = LoggerFactory.getLogger(BmsManifestCollectTimer.class);

    public BmsManifestCollectTimer(String cronName, long timeout, boolean isExit) {
        super(cronName, timeout, isExit);
    }

    @Override
    public void cronThreadRun() throws Exception {
        testValidate();
        logger.info("测试结束");
    }

    private void testValidate(){

        logger.info("BMS清单汇总开始：");
        long start = System.currentTimeMillis();
        BmsManifestService bmsManifestService = SpringContextUtil.getBean("BmsManifestService");
        bmsManifestService.handleBmsManifest();

        logger.info("BMS清单汇总结束："+(System.currentTimeMillis()-start));
    }
}
