package com.sinoair.billing.timer;

import code.sinoair.core.support.cron.SinoairCron;
import com.sinoair.billing.core.util.DateUtil;
import com.sinoair.billing.core.util.SpringContextUtil;
import com.sinoair.billing.core.util.StringUtil;
import com.sinoair.billing.domain.constant.CheckConstant;
import com.sinoair.billing.domain.constant.EbaConstant;
import com.sinoair.billing.domain.model.billing.*;
import com.sinoair.billing.domain.vo.check.EawbCheckVO;
import com.sinoair.billing.service.check.CheckEawbNoService;
import com.sinoair.billing.service.check.EawbCheckDetailService;
import com.sinoair.billing.service.common.CommonService;
import com.sinoair.billing.service.receipt.ReceiptEawbService;
import com.sinoair.billing.service.receipt.ReceiptRecordService;
import com.sinoair.billing.service.task.InsTaskService;
import com.sinoair.billing.thread.CheckInnerBillNewThread;
import com.sinoair.billing.thread.CheckInnerBillThread;
import com.sinoair.billing.thread.InsCheckPRThread;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.TreeSet;
import java.util.concurrent.CountDownLatch;

import static java.util.Comparator.comparing;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;

/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2018/7/4
 * @time: 10:39
 * @description: 内部公司结算 生成应收应付
 */
public class CheckInnerBillTimer extends SinoairCron {

    private Logger logger = LoggerFactory.getLogger(CheckInnerBillTimer.class);

    public CheckInnerBillTimer(String cronName, long timeout, boolean isExit) {
        super(cronName, timeout, isExit);
    }

    @Override
    public void cronThreadRun() throws Exception {
        testValidate();
        logger.info("测试结束");
    }

    private void testValidate(){
        ReceiptRecordService receiptRecordService = SpringContextUtil.getBean("ReceiptRecordService");
        ReceiptEawbService receiptEawbService = SpringContextUtil.getBean("ReceiptEawbService");
        CheckEawbNoService eawbNoService = SpringContextUtil.getBean("CheckEawbNoService");
        CommonService commonService = SpringContextUtil.getBean("CommonService");
        InsTaskService insTaskService = SpringContextUtil.getBean("InsTaskService");

        EawbCheckVO param = new EawbCheckVO();

        InsTask insTask = insTaskService.selectByTaskType(CheckConstant.HANDLE_INNER_BILL);
        String startDate = insTask.getTaskStartDate();
        String endDate = insTask.getTaskEndDate();
        Date curDate = DateUtil.getNowDateTime();
        if (StringUtil.isEmpty(startDate)){
            startDate = DateUtil.getCurDate();
            try {
                endDate = DateUtil.getPastDayByDate(DateUtil.str2date(startDate,DateUtil.YYYYMMDD),1);
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
        param.setStartDate(startDate);
        param.setEndDate(endDate);

        logger.info("开始查询 startDate："+param.getStartDate() +" endDate:"+param.getEndDate());

//
//        CheckEawbNo checkNo = eawbNoService.selectCheckNo(EbaConstant.INNER_BILL);
//
//        Long maxCode = receiptEawbService.selectMaxReId();
        Long handleNum = Long.valueOf(CheckConstant.PAGE_SIZE_1W);
//        Long handleNum = checkNo.getHandleSize();
//
//        logger.info("查询MaxRrId : "+maxCode);
//        maxCode = maxCode == null?0:maxCode;
//
//        param.setBeginNo(checkNo.getBeginNo().longValue());
//        if (maxCode - checkNo.getBeginNo().longValue() > handleNum){
//            maxCode = checkNo.getBeginNo().longValue() + handleNum;
//        }
//        if (checkNo.getEndNo() == null){
//            param.setEndNo(maxCode);
//        }else{
//            param.setEndNo(checkNo.getEndNo().longValue());
//        }
//
//        logger.info("开始查询 BeginNo："+param.getBeginNo() +" endNo:"+param.getEndNo());
//
//        long allCount = param.getEndNo() - param.getBeginNo();
//        if (allCount == 0){
//            logger.info("查询数量为0");
//            logger.info("下次编号为："+param.getEndNo());
//            checkNo.setBeginNo(new BigDecimal(param.getEndNo()));
//            checkNo.setHandleDate(new Date());
//            eawbNoService.updateEawbNo(checkNo);
//            return;
//        }
//
//        Date curDate = new Date();
        List<ReceiptEawb> tmpList =receiptRecordService.selectBillListByRrId(param);
        List<ReceiptEawb> tmpInnerExchangeList =receiptRecordService.selectBillListInnerByRrId(param);
        addInnerExchange(tmpInnerExchangeList);
        List<ReceiptEawb> receiptEawbList = tmpList.stream().collect(
                collectingAndThen(
                        toCollection(() -> new TreeSet<>(comparing(n->n.getEawbPrintcode()))), ArrayList::new)
        );
        logger.info("==去重结束.size=="+receiptEawbList.size());

        int pageSize = handleNum.intValue()/5;//
        int length = pageSize;
        int handleCount = receiptEawbList.size();
        int size = handleCount;
        if (handleCount == 0){
            logger.info("要处理的数量为0");
        }

        int threadNum = handleCount / pageSize;
        if (handleCount % pageSize != 0) {
            threadNum = threadNum + 1;
        }

        CountDownLatch latch = new CountDownLatch(threadNum);
        for (int i=0;i< threadNum;i++){
            List<ReceiptEawb> subList = receiptEawbList.subList(i * length, ((length + i * length) < size ? (length + i * length) : size));
            new CheckInnerBillThread(i,pageSize,latch,subList).start();
        }
        try {
            latch.await();  //阻塞当前线程，直到latch的值为0
            logger.info("多个线程inner bill计费执行结束，可以做自己的事情了");

//            logger.info("下次开始编号："+param.getEndNo());
            logger.info("多个线程都执行结束，可以做自己的事情了");
//            checkNo.setBeginNo(new BigDecimal(param.getEndNo()));
//            checkNo.setHandleDate(new Date());
//            eawbNoService.updateEawbNo(checkNo);

            insTask.setTaskStartDate(endDate);
            try {
                insTask.setTaskEndDate(DateUtil.getPastDayByDate(DateUtil.str2date(endDate,DateUtil.YYYYMMDD),1));
            } catch (ParseException e) {
                e.printStackTrace();
            }
            insTask.setTaskHandletime(new Date());
            insTaskService.updateByPrimaryKeySelective(insTask);

        } catch (InterruptedException e) {
            e.printStackTrace();
            logger.info("多线程执行中出错了，凉凉了！！！");
        }
        logger.info("监控内部结算计费结束：");
    }


    /***
    *@description 内部交易费计费生成应收应付
    *@params [tmpList]
    *@return void
    *<AUTHOR>
    *date 2023/2/28 16:01
    */
    @Async
    public void addInnerExchange(List<ReceiptEawb> tmpList) {
        List<ReceiptEawb> receiptEawbList = tmpList.stream().collect(
                collectingAndThen(
                        toCollection(() -> new TreeSet<>(comparing(n->n.getEawbPrintcode()))), ArrayList::new)
        );
        logger.info("==【内部交易费计费】去重结束.size=="+receiptEawbList.size());
        InsTaskService insTaskService = SpringContextUtil.getBean("InsTaskService");
        InsTask insTask = insTaskService.selectByTaskType(CheckConstant.HANDLE_INNER_BILL);
        String endDate = insTask.getTaskEndDate();
        Long handleNum = Long.valueOf(CheckConstant.PAGE_SIZE_1W);
        int pageSize = handleNum.intValue()/5;//
        int length = pageSize;
        int handleCount = receiptEawbList.size();
        int size = handleCount;
        if (handleCount == 0){
            logger.info("【内部交易费计费】要处理的数量为0");
        }
        int threadNum = handleCount / pageSize;
        if (handleCount % pageSize != 0) {
            threadNum = threadNum + 1;
        }
        CountDownLatch latch = new CountDownLatch(threadNum);
        for (int i=0;i< threadNum;i++){
            List<ReceiptEawb> subList = receiptEawbList.subList(i * length, ((length + i * length) < size ? (length + i * length) : size));
            new CheckInnerBillNewThread(i,pageSize,latch,subList).start();
        }
        try {
            latch.await();  //阻塞当前线程，直到latch的值为0
            logger.info("多个线程【内部交易费计费】执行结束，可以做自己的事情了........");
            logger.info("多个线程【内部交易费计费】都执行结束，可以做自己的事情了");
            insTask.setTaskStartDate(endDate);
            try {
                insTask.setTaskEndDate(DateUtil.getPastDayByDate(DateUtil.str2date(endDate,DateUtil.YYYYMMDD),1));
            } catch (ParseException e) {
                e.printStackTrace();
            }
            insTask.setTaskHandletime(new Date());
            insTaskService.updateByPrimaryKeySelective(insTask);
        } catch (InterruptedException e) {
            e.printStackTrace();
            logger.info("多线程【内部交易费计费】执行中出错了，凉凉了！！！");
        }
        logger.info("监控【内部交易费计费】结束：");
    }
}
