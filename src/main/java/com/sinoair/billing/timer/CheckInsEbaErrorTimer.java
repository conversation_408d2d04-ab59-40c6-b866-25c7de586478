package com.sinoair.billing.timer;

import code.sinoair.core.support.cron.SinoairCron;
import com.sinoair.billing.core.util.SpringContextUtil;
import com.sinoair.billing.service.check.CheckInsEbaService;
import com.sinoair.billing.thread.CheckInsEbaErrorThread;
import com.sinoair.billing.thread.CheckInsEbaTmpThread;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.CountDownLatch;

/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2018/7/4
 * @time: 10:39
 * @description: 环节同步-计费有异常的环节重新计费
 */
public class CheckInsEbaErrorTimer extends SinoairCron {

    private Logger logger = LoggerFactory.getLogger(CheckInsEbaErrorTimer.class);

    public CheckInsEbaErrorTimer(String cronName, long timeout, boolean isExit) {
        super(cronName, timeout, isExit);
    }

    @Override
    public void cronThreadRun() throws Exception {
        testValidate();
        logger.info("测试结束");
    }

    private void testValidate(){

        CheckInsEbaService checkInsEbaService = SpringContextUtil.getBean("CheckInsEbaService");

        int pageSize = 50000;//CheckConstant.PAGE_SIZE;

        int allCount = 500000;//checkInsEbaService.countEbaTmp();
        if (allCount == 0){
            logger.info("查询数量为0");
        }
        int threadNum = allCount / pageSize;
        if (allCount % pageSize != 0) {
            threadNum = threadNum + 1;
        }
        CountDownLatch latch = new CountDownLatch(10);
        for (int i=1;i<= 10;i++){

            new CheckInsEbaErrorThread(i,pageSize,latch).start();

        }
        //多个线程都执行结束

        try {
            latch.await();  //阻塞当前线程，直到latch的值为0
            logger.info("多个线程都执行结束，可以做自己的事情了");


        } catch (InterruptedException e) {
            e.printStackTrace();
            logger.info("多线程执行中出错了，凉凉了！！！");
        }


        logger.info("监控CEOS环节结束：");
    }
}
