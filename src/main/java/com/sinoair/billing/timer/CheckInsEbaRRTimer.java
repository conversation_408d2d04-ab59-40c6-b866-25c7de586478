package com.sinoair.billing.timer;

import code.sinoair.core.support.cron.SinoairCron;
import com.sinoair.billing.core.util.SpringContextUtil;
import com.sinoair.billing.domain.model.billing.TrapsRRConfig;
import com.sinoair.billing.service.check.CheckInsEbaService;
import com.sinoair.billing.service.temp.TempRRService;
import com.sinoair.billing.thread.CheckInsEbaTmpThread;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.CountDownLatch;

/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2018/7/4
 * @time: 10:39
 * @description: 同步的环节临时数据进行计费
 */
public class CheckInsEbaRRTimer extends SinoairCron {

    private Logger logger = LoggerFactory.getLogger(CheckInsEbaRRTimer.class);

    public CheckInsEbaRRTimer(String cronName, long timeout, boolean isExit) {
        super(cronName, timeout, isExit);
    }

    @Override
    public void cronThreadRun() throws Exception {
        testValidate();
        logger.info("测试结束");
    }

    private void testValidate(){

        CheckInsEbaService checkInsEbaService = SpringContextUtil.getBean("CheckInsEbaService");
        TempRRService tempRRService = SpringContextUtil.getBean("TempRRService");
        TrapsRRConfig trapsRRConfig = tempRRService.selectById(6);
        int pageSize = trapsRRConfig.getPageSize();//
        int startPage = trapsRRConfig.getStartPage();
        int threadNum = trapsRRConfig.getEndPage();

        int allCount = ((threadNum - startPage)+1)*pageSize;
        if (allCount == 0){
            logger.info("查询数量为0");
        }
        threadNum = allCount / pageSize;
        if (allCount % pageSize != 0) {
            threadNum = threadNum + 1;
        }
        CountDownLatch latch = new CountDownLatch(threadNum);
        for (int i=1;i<= threadNum;i++){

            new CheckInsEbaTmpThread(i,pageSize,latch).start();

        }
        //多个线程都执行结束

        try {
            latch.await();  //阻塞当前线程，直到latch的值为0
            logger.info("多个线程都执行结束，可以做自己的事情了");


        } catch (InterruptedException e) {
            e.printStackTrace();
            logger.info("多线程执行中出错了，凉凉了！！！");
        }


        logger.info("监控CEOS环节结束：");
    }
}
