package com.sinoair.billing.timer;

import code.sinoair.core.support.cron.SinoairCron;
import com.sinoair.billing.core.util.CheckUtil;
import com.sinoair.billing.core.util.DateUtil;
import com.sinoair.billing.core.util.SpringContextUtil;
import com.sinoair.billing.core.util.StringUtil;
import com.sinoair.billing.domain.constant.BillingConstant;
import com.sinoair.billing.domain.constant.CheckConstant;
import com.sinoair.billing.domain.constant.EbaConstant;
import com.sinoair.billing.domain.constant.ReceiptRecordConstant;
import com.sinoair.billing.domain.model.billing.*;
import com.sinoair.billing.domain.vo.query.CeosQuery;
import com.sinoair.billing.service.activity.EbaRecordService;
import com.sinoair.billing.service.check.CheckEawbNoService;
import com.sinoair.billing.service.check.CheckInsEbaService;
import com.sinoair.billing.service.common.CommonService;
import com.sinoair.billing.service.task.InsTaskService;
import com.sinoair.billing.thread.SynNonActivityThread;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.TreeSet;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

import static java.util.Comparator.comparing;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;

/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2018/7/4
 * @time: 10:39
 * @description: 按CODE同步CEOS-EBA表数据放到环节表中-非菜鸟数据
 */
public class CheckInsEbaTimer extends SinoairCron {

    private Logger logger = LoggerFactory.getLogger(CheckInsEbaTimer.class);

    public CheckInsEbaTimer(String cronName, long timeout, boolean isExit) {
        super(cronName, timeout, isExit);
    }

    @Override
    public void cronThreadRun() throws Exception {
        testValidate();
        logger.info("测试结束");
    }

    private void testValidate(){
        if (true){
            synByCode();
            return;
        }

    }

    private void synByCode(){

        InsTaskService insTaskService = SpringContextUtil.getBean("InsTaskService");
        CheckInsEbaService checkInsEbaService = SpringContextUtil.getBean("CheckInsEbaService");
        CheckEawbNoService eawbNoService = SpringContextUtil.getBean("CheckEawbNoService");
        EbaRecordService ebaRecordService = SpringContextUtil.getBean("EbaRecordService");
        CommonService commonService = SpringContextUtil.getBean("CommonService");

        CheckSynEbaConfig configParam = new CheckSynEbaConfig();
        configParam.setSoType(EbaConstant.SO_TYPE_NON); //非菜鸟
        List<CheckSynEbaConfig> configList = commonService.listSynEbaConfig(configParam);


        CheckEawbNo checkNo = eawbNoService.selectCheckNo(EbaConstant.SYN_EBA_NON);

        Long maxCode = ebaRecordService.selectMaxEbaCode();
        logger.info("查询MaxEbaSysCode : "+maxCode);

        int handleSize = checkNo.getHandleSize().intValue();
        CeosQuery param = new CeosQuery();
        param.setBeginNo(checkNo.getBeginNo());
        if (maxCode - checkNo.getBeginNo().longValue() > handleSize){
            maxCode = checkNo.getBeginNo().longValue() + handleSize;
        }
        if (checkNo.getEndNo() == null){
            param.setEndNo(new BigDecimal(maxCode));
        }else{
            param.setEndNo(checkNo.getEndNo());
        }
//        param.setBeginNo(new BigDecimal("2155010599"));
//        param.setEndNo(new BigDecimal("2156010599"));
        logger.info("开始查询 BeginNo："+param.getBeginNo() +" endNo:"+param.getEndNo());

        int allCount = param.getEndNo().subtract(param.getBeginNo()).intValue();
        if (allCount == 0){
            logger.info("查询数量为0");
            logger.info("下次编号为："+param.getEndNo());
            checkNo.setBeginNo(param.getEndNo());
            checkNo.setHandleDate(new Date());
            eawbNoService.updateEawbNo(checkNo);
            return;
        }

        Date curDate = new Date();

        List<ExpressBusinessActivity> recordTmpList =ebaRecordService.selectCeosActivityBySysCode(param);
        List<ExpressBusinessActivity> eadList = recordTmpList.stream().collect(
                collectingAndThen(
                        toCollection(() -> new TreeSet<>(comparing(n->n.getEawbPrintcode()+";"+n.getEadCode()+";"+n.getEastCode()))),ArrayList::new)
        );

        List<ExpressBusinessActivity> recordList = eadList.stream().filter(eba -> !CheckConstant.CN_SO_CODE.equals(eba.getSoCode())).collect(Collectors.toList());

        int length = EbaConstant.PAGE_SIZE_1W;
        int size = recordList.size();
        int count = size % length != 0 ? size / length + 1 : size / length;
        CountDownLatch latch = new CountDownLatch(count);

        for (int t = 0; t < count; t++) {
            List<ExpressBusinessActivity> subList = recordList.subList(t * length, ((length + t * length) < size ? (length + t * length) : size));

            if (subList.size() > 0){
                new SynNonActivityThread(t,length,latch,subList,configList).start();
            }
        }
        try {
            latch.await();  //阻塞当前线程，直到latch的值为0
            //多个线程都执行结束
            logger.info("循环同步完成，可以做自己的事情了");
            logger.info("下次开始编号："+param.getEndNo());
            logger.info("多个线程都执行结束，可以做自己的事情了");
            checkNo.setBeginNo(param.getEndNo());
            checkNo.setHandleDate(new Date());
            eawbNoService.updateEawbNo(checkNo);
            logger.info("监控CEOS环节结束：");


        } catch (InterruptedException e) {
            e.printStackTrace();
            logger.info("多线程执行中出错了，凉凉了！！！");
        }



    }
}
