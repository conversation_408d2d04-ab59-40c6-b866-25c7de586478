package com.sinoair.billing.timer;

import code.sinoair.core.support.cron.SinoairCron;
import com.sinoair.billing.core.util.DateUtil;
import com.sinoair.billing.core.util.SpringContextUtil;
import com.sinoair.billing.core.util.XiaoTongMsgUtil;
import com.sinoair.billing.domain.vo.system.DBTableSpaceVO;
import com.sinoair.billing.service.system.SystemService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: 大雄
 * @date: 2019-11-05
 * @time: 14:14
 * @description: To change this template use File | Settings | File Templates.
 */
public class DBTableSpacePreWarningTimer extends SinoairCron {

    private Logger logger = LoggerFactory.getLogger(DBTableSpacePreWarningTimer.class);

    public DBTableSpacePreWarningTimer(String cronName, long timeout, boolean isExit) {
        super(cronName, timeout, isExit);
    }

    @Override
    public void cronThreadRun() throws Exception {
        try {
            Date now = new Date();
            String date = DateUtil.date2str(now,DateUtil.YYYY_MM_DD_HH_MM_SS);
            SystemService service = SpringContextUtil.getBean("SystemService");
            DBTableSpaceVO tableSpace = service.selectTableSpace();
            BigDecimal b = new BigDecimal(tableSpace.getFreeG());
            Double free =  b.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
            boolean warning = false;
            if (free < 20D ){
                warning = true;
            }
            StringBuilder messageBuilder = new StringBuilder("小程序、官网billing生产库(10.28.168.14)表空间情况：剩余"+free+"G,使用率"+tableSpace.getRound()).append("%;");
            if (warning){
                messageBuilder.append("剩余表空间已不足20G,请及时处理!");
            }
            //发送钉钉、邮件
            try {
                XiaoTongMsgUtil.ceosNotify("小程序、官网billing生产库(10.28.168.14)表空间情况("+date+")",messageBuilder.toString());
            }catch (Exception e){
                e.printStackTrace();
            }
            try {
                XiaoTongMsgUtil.notifyCeosWarning("小程序、官网billing生产库(10.28.168.14)表空间情况("+date+")",messageBuilder.toString());
            }catch (Exception e){
                e.printStackTrace();
            }

        }catch (Exception e){
            e.printStackTrace();
        }

    }
}
