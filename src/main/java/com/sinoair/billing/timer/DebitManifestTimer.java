package com.sinoair.billing.timer;

import code.sinoair.core.support.cron.SinoairCron;
import com.sinoair.billing.core.util.DateUtil;
import com.sinoair.billing.core.util.SpringContextUtil;
import com.sinoair.billing.core.util.StringUtil;
import com.sinoair.billing.domain.constant.CheckConstant;
import com.sinoair.billing.domain.constant.ReceiptRecordConstant;
import com.sinoair.billing.domain.model.billing.DebitManifest;
import com.sinoair.billing.domain.model.billing.InsTask;
import com.sinoair.billing.domain.model.billing.SerialNO;
import com.sinoair.billing.domain.model.billing.SettlementObject;
import com.sinoair.billing.domain.vo.query.CeosQuery;
import com.sinoair.billing.service.common.CommonService;
import com.sinoair.billing.service.manifest.BmsManifestService;
import com.sinoair.billing.service.receipt.ReceiptService;
import com.sinoair.billing.service.receipt.SerialNOService;
import com.sinoair.billing.service.syn.SynReceiptRecordService;
import com.sinoair.billing.service.task.InsTaskService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2018/7/4
 * @time: 10:39
 * @description: 自动生成账单
 */
public class DebitManifestTimer extends SinoairCron {

    private Logger logger = LoggerFactory.getLogger(DebitManifestTimer.class);

    private static final String EAWBIETYPE_E="E";//出口

    public DebitManifestTimer(String cronName, long timeout, boolean isExit) {
        super(cronName, timeout, isExit);
    }

    @Override
    public void cronThreadRun() throws Exception {
        testValidate();
        logger.info("测试结束");
    }

    private void testValidate(){
        CommonService commonService = SpringContextUtil.getBean("CommonService");
        ReceiptService receiptService = SpringContextUtil.getBean("ReceiptService");
        SerialNOService serialNOService = SpringContextUtil.getBean("SerialNOService");
        BmsManifestService bmsManifestService = SpringContextUtil.getBean("BmsManifestService");
        InsTaskService insTaskService = SpringContextUtil.getBean("InsTaskService");

        bmsManifestService.handleDebitManifest();

        logger.info("生成账单结束：");
    }
}
