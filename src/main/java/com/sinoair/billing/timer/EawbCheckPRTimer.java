package com.sinoair.billing.timer;

import code.sinoair.core.support.cron.SinoairCron;
import com.sinoair.billing.core.util.SpringContextUtil;
import com.sinoair.billing.domain.constant.EbaConstant;
import com.sinoair.billing.domain.model.billing.*;
import com.sinoair.billing.domain.vo.check.EawbCheckVO;
import com.sinoair.billing.service.check.CheckEawbNoService;
import com.sinoair.billing.service.check.EawbCheckDetailService;
import com.sinoair.billing.service.common.CommonService;
import com.sinoair.billing.service.receipt.ReceiptEawbService;
import com.sinoair.billing.service.receipt.ReceiptRecordService;
import com.sinoair.billing.thread.InsCheckPRThread;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.TreeSet;
import java.util.concurrent.CountDownLatch;

import static java.util.Comparator.comparing;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;

/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2018/7/4
 * @time: 10:39
 * @description: 生成应付数据
 */
public class EawbCheckPRTimer extends SinoairCron {

    private Logger logger = LoggerFactory.getLogger(EawbCheckPRTimer.class);

    public EawbCheckPRTimer(String cronName, long timeout, boolean isExit) {
        super(cronName, timeout, isExit);
    }

    @Override
    public void cronThreadRun() throws Exception {
        testValidate();
        logger.info("测试结束");
    }

    private void testValidate(){
        ReceiptEawbService receiptEawbService = SpringContextUtil.getBean("ReceiptEawbService");
//        TrapsRRConfig trapsRRConfig = tempRRService.selectById(3);
        CheckEawbNoService eawbNoService = SpringContextUtil.getBean("CheckEawbNoService");
        CommonService commonService = SpringContextUtil.getBean("CommonService");
        ReceiptRecordService receiptRecordService = SpringContextUtil.getBean("ReceiptRecordService");

        CheckEawbNo checkNo = eawbNoService.selectCheckNo(EbaConstant.BILLING_PR);
        //按rrId
//        Long maxCode = receiptEawbService.selectMaxReId();
        //按ebaCode
        Long maxCode = receiptEawbService.selectMaxEbaCode();
        Long handleNum = checkNo.getHandleSize();

        logger.info("查询MaxReId : "+maxCode);
        maxCode = maxCode == null?0:maxCode;
        EawbCheckVO param = new EawbCheckVO();
        param.setBeginNo(checkNo.getBeginNo().longValue());
        if (maxCode - checkNo.getBeginNo().longValue() > handleNum){
            maxCode = checkNo.getBeginNo().longValue() + handleNum;
        }
        if (checkNo.getEndNo() == null){
            param.setEndNo(maxCode);
        }else{
            param.setEndNo(checkNo.getEndNo().longValue());
        }
//        param.setBeginNo(Long.valueOf(1));
//        param.setEndNo(Long.valueOf(2));
        logger.info("开始查询 BeginNo："+param.getBeginNo() +" endNo:"+param.getEndNo());

        long allCount = param.getEndNo() - param.getBeginNo();
        if (allCount == 0){
            logger.info("查询数量为0");
            logger.info("下次编号为："+param.getEndNo());
            checkNo.setBeginNo(new BigDecimal(param.getEndNo()));
            checkNo.setHandleDate(new Date());
            eawbNoService.updateEawbNo(checkNo);
            return;
        }

        Date curDate = new Date();
        List<PaymentRecord> paymentRecordList = new ArrayList<>();
//        List<ReceiptEawb> tmpList =receiptEawbService.listReceiptEawb(param);
        //按eba计费
        List<ReceiptEawb> tmpList =receiptEawbService.listEba(param);
        //按rr应收记录计费
//        List<ReceiptEawb> tmpList =receiptRecordService.selectBillListByRrId(param);
        List<ReceiptEawb> receiptEawbList = tmpList.stream().collect(
                collectingAndThen(
                        toCollection(() -> new TreeSet<>(comparing(n->n.getEawbPrintcode()))), ArrayList::new)
        );
        logger.info("==去重结束.size=="+receiptEawbList.size());


        List<PaymentRecord> recordList = null;

        int pageSize = handleNum.intValue()/5;//
        int length = pageSize;
        int handleCount = receiptEawbList.size();
        int size = handleCount;
        if (handleCount == 0){
            logger.info("要处理的数量为0");
        }

        int threadNum = handleCount / pageSize;
        if (handleCount % pageSize != 0) {
            threadNum = threadNum + 1;
        }

        CountDownLatch latch = new CountDownLatch(threadNum);
        for (int i=0;i< threadNum;i++){
            List<ReceiptEawb> subList = receiptEawbList.subList(i * length, ((length + i * length) < size ? (length + i * length) : size));
            new InsCheckPRThread(i,pageSize,latch,subList).start();
        }
        try {
            latch.await();  //阻塞当前线程，直到latch的值为0
            logger.info("多个线程PR计费执行结束，可以做自己的事情了");

            logger.info("下次开始编号："+param.getEndNo());
            logger.info("多个线程都执行结束，可以做自己的事情了");
            checkNo.setBeginNo(new BigDecimal(param.getEndNo()));
            checkNo.setHandleDate(new Date());
            eawbNoService.updateEawbNo(checkNo);

        } catch (InterruptedException e) {
            e.printStackTrace();
            logger.info("多线程执行中出错了，凉凉了！！！");
        }
        logger.info("监控应付计费结束：");
    }
}
