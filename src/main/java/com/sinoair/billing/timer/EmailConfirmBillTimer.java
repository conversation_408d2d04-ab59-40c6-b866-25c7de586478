package com.sinoair.billing.timer;

import code.sinoair.core.support.cron.SinoairCron;
import com.sinoair.billing.core.util.SpringContextUtil;
import com.sinoair.billing.service.email.NoticeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: nhb
 * @date: 2020/3/17
 * @time: 10:39
 * @description: 监控菜鸟账单确认并发邮件
 */
public class EmailConfirmBillTimer extends SinoairCron {

    private Logger logger = LoggerFactory.getLogger(EmailConfirmBillTimer.class);

    public EmailConfirmBillTimer(String cronName, long timeout, boolean isExit) {
        super(cronName, timeout, isExit);
    }

    @Override
    public void cronThreadRun() throws Exception {
        testValidate();
        logger.info("测试结束");
    }

    private void testValidate(){
        NoticeService noticeService = SpringContextUtil.getBean("NoticeService");
        logger.info("监控账单确认通知开始：");
        long start = System.currentTimeMillis();
        noticeService.confirmBill();
        long end = System.currentTimeMillis();
        logger.info("监控账单确认通知结束："+(end-start));

        logger.info("监控账单汇总明细生成开始：");
        noticeService.monitorManifestDetail();
        logger.info("监控账单汇总明细生成开始："+(System.currentTimeMillis() - end));
    }
}
