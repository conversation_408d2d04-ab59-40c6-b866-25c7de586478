package com.sinoair.billing.timer;

import code.sinoair.core.support.cron.SinoairCron;
import com.sinoair.billing.core.util.SpringContextUtil;
import com.sinoair.billing.domain.model.billing.ExpressBusinessActivity;
import com.sinoair.billing.domain.model.billing.GeneratePaymentFeeLog;
import com.sinoair.billing.service.payment.FixPaymentFeeService;
import com.sinoair.billing.service.payment.GeneratePaymentFeeService;
import com.sinoair.billing.thread.FixPaymentFeeThread;
import com.sinoair.billing.thread.GeneratePaymentFeeThread;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * 应收应付未计费轨迹查漏补缺
 */
public class FixPaymentFeeTimer  extends SinoairCron {

    private Logger logger = LoggerFactory.getLogger(FixPaymentFeeTimer.class);

    public FixPaymentFeeTimer(String cronName, long timeout, boolean isExit) {
        super(cronName, timeout, isExit);
    }

    @Override
    public void cronThreadRun() throws Exception {
        FixPaymentFeeService fixPaymentFeeService = SpringContextUtil.getBean("FixPaymentFeeService");
        List<GeneratePaymentFeeLog> logList=fixPaymentFeeService.queryGpfList();
        long startTime=System.currentTimeMillis();
        CountDownLatch latch = new CountDownLatch(logList.size());
        for(int i=0;i<logList.size();i++){
            List<ExpressBusinessActivity> expressBusinessActivityList=fixPaymentFeeService.selectFixEba(logList.get(i).getBeginNum().longValue(),logList.get(i).getEndNum().longValue());
            long searchTime=System.currentTimeMillis();
            logger.info("查询轨迹耗时:"+(searchTime-startTime)+"毫秒,"+"beginNum:"+logList.get(i).getBeginNum()+"endNum:"+logList.get(i).getEndNum());
            new FixPaymentFeeThread(logList.get(i),latch,expressBusinessActivityList).start();
        }
        try {
            latch.await();
            long endTime=System.currentTimeMillis();
            logger.info("应收应付未计费轨迹生成结束,共处理"+logList.size()+"条轨迹,用时"+(endTime-startTime)+"毫秒");
        }catch(Exception e){
            e.printStackTrace();
            logger.info("FixPaymentFeeTimer：应收应付未计费轨迹生成费用异常!!!!");
        }
    }


}
