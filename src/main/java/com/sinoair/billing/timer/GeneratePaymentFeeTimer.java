package com.sinoair.billing.timer;

import code.sinoair.core.support.cron.SinoairCron;
import com.sinoair.billing.core.util.SpringContextUtil;
import com.sinoair.billing.domain.constant.EbaConstant;
import com.sinoair.billing.domain.model.billing.CheckEawbNo;
import com.sinoair.billing.domain.model.billing.ExpressBusinessActivity;
import com.sinoair.billing.service.check.CheckEawbNoService;
import com.sinoair.billing.service.payment.GeneratePaymentFeeService;
import com.sinoair.billing.service.receipt.ReceiptEawbService;
import com.sinoair.billing.thread.GeneratePaymentFeeThread;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * 轨迹生成应付费用
 */

public class GeneratePaymentFeeTimer extends SinoairCron {

    private Logger logger = LoggerFactory.getLogger(GeneratePaymentFeeTimer.class);

    public GeneratePaymentFeeTimer(String cronName, long timeout, boolean isExit) {
        super(cronName, timeout, isExit);
    }


    @Override
    public void cronThreadRun() throws Exception {
        long startTime=System.currentTimeMillis();
        //ReceiptEawbService receiptEawbService = SpringContextUtil.getBean("ReceiptEawbService");
        CheckEawbNoService eawbNoService = SpringContextUtil.getBean("CheckEawbNoService");
        GeneratePaymentFeeService generatePaymentFeeService = SpringContextUtil.getBean("GeneratePaymentFeeService");
        CheckEawbNo checkNo = eawbNoService.selectCheckNo("GENERATE_PAYMENT");
        //Long maxCode = receiptEawbService.selectMaxEbaCode();
        BigDecimal beginNo=generatePaymentFeeService.selectMaxBillingSyscodeByBeginNo(checkNo.getBeginNo().longValue());
        List<ExpressBusinessActivity> easCodeList=generatePaymentFeeService.selectsysCodeList(checkNo.getBeginNo().longValue());
        int pageSize =checkNo.getHandleSize().intValue();//
        if(easCodeList.size()==0){
            logger.info("要处理的数量为0");
        }
        int threadNum = easCodeList.size() / pageSize;
        if(easCodeList.size()%pageSize!=0){
            threadNum = threadNum + 1;
        }
        CountDownLatch latch = new CountDownLatch(threadNum);
        for(int i=0;i<threadNum;i++){
            List<ExpressBusinessActivity> subList=easCodeList.subList(i * pageSize,((pageSize + i * pageSize) < easCodeList.size() ? (pageSize + i * pageSize) : easCodeList.size()));
            new GeneratePaymentFeeThread(i,pageSize,latch,subList).start();
        }
        try{
            latch.await();  //阻塞当前线程，直到latch的值为0
            if(easCodeList.size()>0){
                logger.info("处理开始beginNo:"+checkNo.getBeginNo()+"结束endNo:"+beginNo);
                checkNo.setBeginNo(beginNo);
                checkNo.setHandleDate(new Date());
                eawbNoService.updateEawbNo(checkNo);
            }
            logger.info("GeneratePaymentFeeTimer：轨迹生成应付费用结束");
        }catch(Exception e) {
            e.printStackTrace();
            logger.info("GeneratePaymentFeeTimer：轨迹生成应付费用异常!!!");
        }
        long endTime=System.currentTimeMillis();
        logger.info("轨迹生成结束,共处理"+easCodeList.size()+"条轨迹,用时"+(endTime-startTime)+"毫秒");
    }
}
