package com.sinoair.billing.timer;

import code.sinoair.core.support.cron.SinoairCron;
import com.sinoair.billing.core.util.SpringContextUtil;
import com.sinoair.billing.service.payment.ManualBillingService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.FileReader;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 手动计费定时任务
 * 用于处理遗漏的计费数据
 */
public class ManualBillingTimer extends SinoairCron {

    private Logger logger = LoggerFactory.getLogger(ManualBillingTimer.class);

    public ManualBillingTimer(String cronName, long timeout, boolean isExit) {
        super(cronName, timeout, isExit);
    }

    @Override
    public void cronThreadRun() throws Exception {
        long startTime = System.currentTimeMillis();
        logger.info("手动计费定时任务开始执行");

        try {
            // 确保使用正确的Spring Profile
            String activeProfile = System.getProperty("spring.profiles.active");
            logger.info("当前激活的Spring Profile: {}", activeProfile);

            ManualBillingService manualBillingService = SpringContextUtil.getBean("ManualBillingService");
            
            // 从文件读取运单号列表
            String filePath = "D:/work/workspace/app-billing-timer/missing_eawb_codes.txt";
            List<String> eawbPrintcodes = readEawbPrintcodesFromFile(filePath);
            
            if (eawbPrintcodes.isEmpty()) {
                logger.warn("未读取到运单号，请检查文件路径和格式：{}", filePath);
                return;
            }

            logger.info("从文件读取到运单号数量：{}", eawbPrintcodes.size());

            // 验证运单号
            logger.info("开始验证运单号...");
            Map<String, Object> validateResult = manualBillingService.validateEawbPrintcodes(eawbPrintcodes);
            logger.info("验证结果：{}", validateResult.get("message"));
            
            if (!(Boolean) validateResult.get("valid")) {
                logger.warn("验证失败，停止处理");
                return;
            }

            // 执行手动计费
            logger.info("开始执行手动计费...");
            String result = manualBillingService.manualBillingByEawbPrintcodes(eawbPrintcodes, 500);
            logger.info("计费结果：{}", result);

        } catch (Exception e) {
            logger.error("手动计费定时任务执行异常：{}", e.getMessage(), e);
            throw e;
        }

        long endTime = System.currentTimeMillis();
        logger.info("手动计费定时任务执行完成，耗时：{}毫秒", (endTime - startTime));
    }

    /**
     * 从文本文件读取运单号列表
     * @param filePath 文件路径
     * @return 运单号列表
     */
    private List<String> readEawbPrintcodesFromFile(String filePath) {
        List<String> eawbPrintcodes = new ArrayList<>();
        
        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
            String line;
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                if (!line.isEmpty()) {
                    eawbPrintcodes.add(line);
                }
            }
        } catch (Exception e) {
            logger.error("读取运单号文件异常：{}", e.getMessage(), e);
        }
        
        return eawbPrintcodes;
    }
}
