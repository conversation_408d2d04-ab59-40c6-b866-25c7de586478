package com.sinoair.billing.timer;

import code.sinoair.core.support.cron.SinoairCron;
import com.sinoair.billing.core.util.SpringContextUtil;
import com.sinoair.billing.domain.model.billing.TrapsRRConfig;
import com.sinoair.billing.domain.vo.query.CeosQuery;
import com.sinoair.billing.service.check.EawbCheckDetailService;
import com.sinoair.billing.service.temp.TempRRService;
import com.sinoair.billing.thread.MonitorCommonThread;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

import static java.util.Comparator.comparing;

/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2018/7/4
 * @time: 10:39
 * @description: 监控缺失的eawb数据重新同步
 */
public class MonitorLackEawbTimer extends SinoairCron {

    private Logger logger = LoggerFactory.getLogger(MonitorLackEawbTimer.class);

    public MonitorLackEawbTimer(String cronName, long timeout, boolean isExit) {
        super(cronName, timeout, isExit);
    }

    @Override
    public void cronThreadRun() throws Exception {
        testValidate();
        logger.info("测试结束");
    }

    private void testValidate(){
        EawbCheckDetailService checkDetailService = SpringContextUtil.getBean("EawbCheckDetailService");

        TempRRService tempRRService = SpringContextUtil.getBean("TempRRService");
        TrapsRRConfig trapsRRConfig = tempRRService.selectById(5);
        int pageSize = trapsRRConfig.getPageSize();//
        int startPage = trapsRRConfig.getStartPage();
        int threadNum = trapsRRConfig.getEndPage();

        int allPageSize = ((threadNum - startPage)+1)*pageSize;
        CeosQuery queryParam = new CeosQuery();
        queryParam.setPageNum(1);
        queryParam.setPageSize(allPageSize);
//        queryParam.setPageSize(1);
        logger.info("准备查询=="+queryParam.getPageSize());
        List<String> eawbPrintcodes = checkDetailService.listLackEawb(queryParam);
        logger.info("查询结果=="+eawbPrintcodes.size());
        List<String> recordList = eawbPrintcodes.stream().distinct().collect(Collectors.toList());
        logger.info("去重结果=="+recordList.size());
//        logger.info("去重打印=="+recordList.toString());
        int length = pageSize;
        int size = recordList.size();
        int count = size % length != 0 ? size / length + 1 : size / length;

        logger.info("线程数count=="+count);

        //所有线程阻塞，然后统一开始
        CountDownLatch latch = new CountDownLatch(count);
        //每次10w条，循环处理，暂时先不用多线程
        for (int i=0;i< count;i++){
            List<String> subList = recordList.subList(i * length, ((length + i * length) < size ? (length + i * length) : size));

            new MonitorCommonThread(i,length,"MonitorLackEawb",latch,subList).start();
        }

        //多个线程都执行结束

        try {
            latch.await();  //阻塞当前线程，直到latch的值为0
            logger.info("监控eawb多线程同步结束，可以做自己的事情了");

        } catch (InterruptedException e) {
            e.printStackTrace();
            logger.info("监控eawb多线程同步执行中出错了，凉凉了！！！");
        }

        logger.info("监控eawb同步结束：");
    }
}
