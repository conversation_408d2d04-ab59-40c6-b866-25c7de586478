package com.sinoair.billing.timer;

import code.sinoair.core.support.cron.SinoairCron;
import com.sinoair.billing.core.util.DateUtil;
import com.sinoair.billing.core.util.MailUtil;
import com.sinoair.billing.core.util.SpringContextUtil;
import com.sinoair.billing.core.util.StringUtil;
import com.sinoair.billing.domain.constant.CheckConstant;
import com.sinoair.billing.domain.constant.MailConstant;
import com.sinoair.billing.domain.constant.ReceiptRecordConstant;
import com.sinoair.billing.domain.model.billing.ExpressAssignment;
import com.sinoair.billing.domain.model.billing.ExpressAssignmentActual;
import com.sinoair.billing.domain.model.billing.InsTask;
import com.sinoair.billing.domain.vo.query.CeosQuery;
import com.sinoair.billing.domain.vo.query.CommonQuery;
import com.sinoair.billing.service.common.CommonService;
import com.sinoair.billing.service.syn.SynCeosEawbMawbService;
import com.sinoair.billing.service.task.InsTaskService;
import com.sinoair.billing.service.temp.TempService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2018/7/4
 * @time: 10:39
 * @description: 监控已同步的主单eawb数量与ceos的eawb数量是否一致
 */
public class MonitorMawbEawbTimer extends SinoairCron {

    private Logger logger = LoggerFactory.getLogger(MonitorMawbEawbTimer.class);

    public MonitorMawbEawbTimer(String cronName, long timeout, boolean isExit) {
        super(cronName, timeout, isExit);
    }

    @Override
    public void cronThreadRun() throws Exception {
        testValidate();
        logger.info("测试结束");
    }

    private void testValidate(){
        InsTaskService insTaskService = SpringContextUtil.getBean("InsTaskService");
        SynCeosEawbMawbService synCeosEawbMawbService = SpringContextUtil.getBean("SynCeosEawbMawbService");
        CommonService commonService = SpringContextUtil.getBean("CommonService");
        TempService tempService = SpringContextUtil.getBean("TempService");

        InsTask insTask = insTaskService.selectByTaskType(CheckConstant.SYN_CEOS_EAWB_MAWB);

        String taskFiled = "";

        String curDate = DateUtil.getCurDate();
        int curDay = DateUtil.getDay(new Date());
        String startDate = null;
        String endDate = null;
        try {
            //1号
            if (curDay == 1){
                //开始日期为上月1号
                startDate = DateUtil.date2str(DateUtil.getLastMonthFirstDay(),DateUtil.YYYYMMDD);
            }else{
                //开始日期为本月1号
                startDate = DateUtil.date2str(DateUtil.getThisMonthFirstDay(),DateUtil.YYYYMMDD);
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        endDate =curDate;
        logger.info("时间段为："+startDate +" 至 "+endDate);

        CommonQuery commonQuery = new CommonQuery();
        commonQuery.setStrStartDate(startDate);
        commonQuery.setStrEndDate(endDate);
        List<ExpressAssignmentActual> eaList = synCeosEawbMawbService.selectBillingEa(commonQuery);
        if (eaList.size() == 0){
            logger.info("MonitorMawbEawbTimer 没有产生新主单【"+startDate+"】");
            MailUtil.postMail(MailConstant.MAIL_TO_ONE,"没有产生新主单【"+startDate+"】","");
            return;
        }
        logger.info(startDate+" 至 "+endDate +" 主单数："+eaList.size());
        int seq = 0;
        List<String> addList = new ArrayList<>();
        for (ExpressAssignmentActual expressAssignment : eaList) {
            String eaCode = expressAssignment.getEaaCode();
            int ceosCount = synCeosEawbMawbService.countCeosMawbEawbByEaCode(eaCode);
            int billingCount = commonService.countByMawbCode(eaCode);
            if (billingCount == 0){
                billingCount = commonService.countOtherByMawbCode(eaCode);
            }
            seq++;
            if (ceosCount > billingCount) {
                logger.info("序号：" + seq + " 主单：" + eaCode + " ceos订单总数：" + ceosCount + " billing订单总数：" + billingCount);
//                tempService.insertTempEa(eaCode);
                addList.add(eaCode);
            }

        }
        if (addList.size() > 0){
            tempService.insertBatchTempEa(addList);
            MailUtil.postMail(MailConstant.MAIL_TO_ONE,"双方系统主单下的订单数不一致,主单数量："+addList.size(),"");
        }


        logger.info("监控Ceos-eawb-mawb结束：");
    }
}
