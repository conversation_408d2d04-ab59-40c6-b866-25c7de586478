package com.sinoair.billing.timer;

import code.sinoair.core.support.cron.SinoairCron;
import com.sinoair.billing.core.util.SpringContextUtil;
import com.sinoair.billing.domain.constant.CheckConstant;
import com.sinoair.billing.domain.model.billing.*;
import com.sinoair.billing.domain.vo.check.EawbCheckVO;
import com.sinoair.billing.service.check.CheckEawbNoService;
import com.sinoair.billing.service.order.OrderPoolService;
import com.sinoair.billing.thread.OrderAcceptedThread;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * Created by IntelliJ IDEA. 订单池受理委托任务
 *
 * @author:
 * @date: 2020/3/11
 * @time: 10:39
 * @description: 订单池-订单受理
 */
public class OrderAcceptedTimer extends SinoairCron {

    private Logger logger = LoggerFactory.getLogger(OrderAcceptedTimer.class);

    public OrderAcceptedTimer(String cronName, long timeout, boolean isExit) {
        super(cronName, timeout, isExit);
    }

    @Override
    public void cronThreadRun() throws Exception {
        testValidate();
        logger.info("测试结束");
    }

    private void testValidate(){

        CheckEawbNoService eawbNoService = SpringContextUtil.getBean("CheckEawbNoService");
        OrderPoolService orderPoolService = SpringContextUtil.getBean("OrderPoolService");

        CheckEawbNo checkNo = eawbNoService.selectCheckNo(CheckConstant.ORDER_POOL);
        Long maxCode = orderPoolService.selectMaxEawbSysCode();
        List<SettlementObject> soList = orderPoolService.selectSettlementList();

        EawbCheckVO param = new EawbCheckVO();
        param.setBeginNo(checkNo.getBeginNo().longValue());
        //临时代码 todo 上线时关闭
//        maxCode = checkNo.getBeginNo().longValue()+10;
        if (maxCode - checkNo.getBeginNo().longValue() > 1000000){
            maxCode = checkNo.getBeginNo().longValue() + 1000000;
        }
        if (checkNo.getEndNo() == null){
            param.setEndNo(maxCode);
        }else{
            param.setEndNo(checkNo.getEndNo().longValue());
        }
        logger.info("开始查询 BeginNo："+checkNo.getBeginNo() +" endNo:"+param.getEndNo());
//        List<SinotransOrderPool> resultList = orderPoolService.selectOrderAccept(param);
        int allCount = orderPoolService.countOrderAccept(param);
        if (allCount == 0){
            logger.info("查询数量为0");
            logger.info("下次编号为："+maxCode);
            checkNo.setBeginNo(new BigDecimal(maxCode));
            checkNo.setHandleDate(new Date());
            eawbNoService.updateEawbNo(checkNo);
            return;
        }
        logger.info("今日订单总数："+allCount);
        int pageSize = CheckConstant.PAGE_SIZE_5W;
        int threadNum = allCount / pageSize;
        if (allCount % pageSize != 0) {
            threadNum = threadNum + 1;
        }
        //所有线程阻塞，然后统一开始
        CountDownLatch latch = new CountDownLatch(threadNum);

        //主线程阻塞，直到所有分线程执行完毕
//        CountDownLatch end = new CountDownLatch(threadNum);
        //开始多线程
//        begin.countDown();
        for (int i = 1; i <= threadNum; i++) {

            new OrderAcceptedThread(i,pageSize,param.getBeginNo(),param.getEndNo(),soList,latch).start();

        }
        //多个线程都执行结束

        try {
            latch.await();  //阻塞当前线程，直到latch的值为0
            logger.info("下次开始编号："+maxCode);
            logger.info("多个线程都执行结束，可以做自己的事情了");
            checkNo.setBeginNo(new BigDecimal(maxCode));
            checkNo.setHandleDate(new Date());
            eawbNoService.updateEawbNo(checkNo);

        } catch (InterruptedException e) {
            e.printStackTrace();
            logger.info("多线程执行中出错了，凉凉了！！！");
        }

        logger.info("受理订单同步结束：");
    }
}
