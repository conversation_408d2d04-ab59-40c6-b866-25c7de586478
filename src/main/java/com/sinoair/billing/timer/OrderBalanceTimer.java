package com.sinoair.billing.timer;

import code.sinoair.core.support.cron.SinoairCron;
import com.sinoair.billing.core.util.SpringContextUtil;
import com.sinoair.billing.domain.constant.CheckConstant;
import com.sinoair.billing.domain.constant.OrderPoolConstant;
import com.sinoair.billing.domain.model.billing.*;
import com.sinoair.billing.domain.vo.check.EawbCheckVO;
import com.sinoair.billing.service.check.CheckEawbNoService;
import com.sinoair.billing.service.order.DebitOrderService;
import com.sinoair.billing.service.order.OrderPoolService;
import com.sinoair.billing.thread.OrderAcceptedThread;
import com.sinoair.billing.thread.OrderBalanceThread;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.TreeSet;
import java.util.concurrent.CountDownLatch;

import static java.util.Comparator.comparing;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;

/**
 * Created by IntelliJ IDEA. 订单池结审任务
 *
 * @author:
 * @date: 2020/3/11
 * @time: 10:39
 * @description: 订单池-订单结审
 */
public class OrderBalanceTimer extends SinoairCron {

    private Logger logger = LoggerFactory.getLogger(OrderBalanceTimer.class);

    public OrderBalanceTimer(String cronName, long timeout, boolean isExit) {
        super(cronName, timeout, isExit);
    }

    @Override
    public void cronThreadRun() throws Exception {
        testValidate();
        logger.info("测试结束");
    }

    private void testValidate(){

        OrderPoolService orderPoolService = SpringContextUtil.getBean("OrderPoolService");
//        DebitOrderService debitOrderService = SpringContextUtil.getBean("DebitOrderService");

        List<ManifestEawb> manifestEawbs = orderPoolService.selectManifestEawb(1,500000);
        if (manifestEawbs.size() == 0){
            logger.info("没有要处理的结审数据");
            return;
        }
        logger.info("==去重开始.size=="+manifestEawbs.size());
        List<ManifestEawb> recordList = manifestEawbs.stream().collect(
                collectingAndThen(
                        toCollection(() -> new TreeSet<>(comparing(n->n.getEawbPrintcode()))), ArrayList::new)
        );
        logger.info("==去重结束.size=="+recordList.size());

        int length = CheckConstant.PAGE_SIZE_5W;
        int size = recordList.size();
        int count = size % length != 0 ? size / length + 1 : size / length;
        logger.info("线程数count=="+count);

        //所有线程阻塞，然后统一开始
        CountDownLatch latch = new CountDownLatch(count);
        //每次10w条，循环处理，暂时先不用多线程
        for (int i=0;i< count;i++){
            List<ManifestEawb> subList = recordList.subList(i * length, ((length + i * length) < size ? (length + i * length) : size));

            new OrderBalanceThread(i,length,latch,subList).start();
        }

        //多个线程都执行结束

        try {
            latch.await();  //阻塞当前线程，直到latch的值为0
            logger.info("多个线程都执行结束，可以做自己的事情了");


        } catch (InterruptedException e) {
            e.printStackTrace();
            logger.info("多线程执行中出错了，凉凉了！！！");
        }



        logger.info("结审订单同步结束：");
    }
}
