package com.sinoair.billing.timer;

import code.sinoair.core.support.cron.SinoairCron;
import com.sinoair.billing.core.util.SpringContextUtil;
import com.sinoair.billing.domain.constant.OrderPoolConstant;
import com.sinoair.billing.domain.model.billing.DebitOrder;
import com.sinoair.billing.service.manifest.BmsManifestService;
import com.sinoair.billing.service.manifest.DebitEawbService;
import com.sinoair.billing.service.order.DebitOrderService;
import com.sinoair.billing.service.order.OrderPoolService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;

/**
 * Created by IntelliJ IDEA. 同步账单下的eawb任务  暂时废弃
 *
 * @author:
 * @date: 2020/3/11
 * @time: 10:39
 * @description:
 */
public class OrderCollectWeightTimer extends SinoairCron {

    private Logger logger = LoggerFactory.getLogger(OrderCollectWeightTimer.class);

    public OrderCollectWeightTimer(String cronName, long timeout, boolean isExit) {
        super(cronName, timeout, isExit);
    }

    @Override
    public void cronThreadRun() throws Exception {
        testValidate();
        logger.info("测试结束");
    }

    private void testValidate(){

        BmsManifestService bmsManifestService = SpringContextUtil.getBean("BmsManifestService");
        logger.info("检查eawb UpdateTime");
        //补充expressairwaybill eawb_updatetime null=sysdate 入库时间
        bmsManifestService.handleEawbUpdateTime();
//        //获取要处理的账单id
//        logger.info("准备汇总本月eawb重量");
        // 汇总 》 insert bms_rec_heaed
        bmsManifestService.handleBmsCollectWeight();
//        logger.info("生成应收账单及应收行");
        bmsManifestService.handleDebitManifest();
//        logger.info("生成应付账单及应付行");
        bmsManifestService.handleCreditManifest();

//        logger.info("准备汇总本月eawb重量-FBA");
//        bmsManifestService.handleBmsFbaCollectWeight();
 //       logger.info("生成应收账单及应收行-FBA");
//        bmsManifestService.handleDebitManifestFba();
//        logger.info("生成应付账单及应付行-FBA");
//        bmsManifestService.handleCreditManifestFba();
//
//        logger.info("汇总本月eawb重量结束：");
    }

}
