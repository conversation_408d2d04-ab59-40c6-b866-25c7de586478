package com.sinoair.billing.timer;

import code.sinoair.core.support.cron.SinoairCron;
import com.sinoair.billing.core.util.SpringContextUtil;
import com.sinoair.billing.domain.constant.CheckConstant;
import com.sinoair.billing.domain.constant.OrderPoolConstant;
import com.sinoair.billing.domain.constant.ReceiptRecordConstant;
import com.sinoair.billing.domain.model.billing.DebitManifest;
import com.sinoair.billing.domain.model.billing.DebitManifestTemporary;
import com.sinoair.billing.domain.model.billing.DebitOrder;
import com.sinoair.billing.service.manifest.DebitEawbService;
import com.sinoair.billing.service.order.DebitOrderService;
import com.sinoair.billing.service.order.OrderPoolService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;
import java.util.List;

/**
 * Created by IntelliJ IDEA. 同步账单下的eawb任务  暂时废弃
 *
 * @author:
 * @date: 2020/3/11
 * @time: 10:39
 * @description: To change this template use File | Settings | File Templates.
 */
public class OrderDebitEawbTimer extends SinoairCron {

    private Logger logger = LoggerFactory.getLogger(OrderDebitEawbTimer.class);

    public OrderDebitEawbTimer(String cronName, long timeout, boolean isExit) {
        super(cronName, timeout, isExit);
    }

    @Override
    public void cronThreadRun() throws Exception {
        testValidate();
        logger.info("测试结束");
    }

    private void testValidate(){

        OrderPoolService orderPoolService = SpringContextUtil.getBean("OrderPoolService");
        DebitOrderService debitOrderService = SpringContextUtil.getBean("DebitOrderService");
        DebitEawbService debitEawbService = SpringContextUtil.getBean("DebitEawbService");
        DebitOrder debitOrder = null;
        logger.info("查询是否有正在处理的账单");
        int processCount = debitOrderService.countDebitOrder(OrderPoolConstant.DM_STATUS_PROCESSING);
        if (processCount > 0){
            logger.info("当前有正在处理的账单同步订单操作");
            debitOrder = debitOrderService.getDebitOrder(OrderPoolConstant.DM_STATUS_PROCESSING);
//            int count = debitEawbService.countDebitEawb();
//            if (count > 0){
//                logger.info("按线路、单票号汇总明细");
//                debitEawbService.insertBmsManifestAmount(debitOrderProcess.getDmId());
//                logger.info("删除数据");
//                debitEawbService.deleteAll();
//                debitOrderProcess.setSysStatus(OrderPoolConstant.DM_STATUS_FINISHED);
//                debitOrderProcess.setSysHandletime(new Date());
//                debitOrderService.updateByPrimaryKeySelective(debitOrderProcess);
//            }
//            return;
        }else{
            logger.info("查询准备要处理的账单");
            debitOrder = debitOrderService.getDebitOrder(OrderPoolConstant.DM_STATUS_PENDING);
            if (debitOrder == null){
                logger.info("没有要处理的账单");
                return;
            }
        }

        Long dmId = debitOrder.getDmId();

        debitOrder.setSysStatus(OrderPoolConstant.DM_STATUS_PROCESSING);
        debitOrder.setSysHandletime(new Date());
        debitOrderService.updateByPrimaryKeySelective(debitOrder);
        String sysStatus = OrderPoolConstant.DM_STATUS_FINISHED;


        try {
            logger.info("插入账单和eawb信息并按线路、单票号汇总明细");
            DebitManifest debitManifest = debitEawbService.selectDmById(dmId);
            if (ReceiptRecordConstant.CAINIAO_SO_CODE.equals(debitManifest.getSoCode())
                    && "T".equals(debitManifest.getDmDirty())){
                List<DebitManifestTemporary> dmTemporaryList = orderPoolService.selectTemporaryByDmId(dmId);
                for (DebitManifestTemporary temporary : dmTemporaryList){
                    if (CheckConstant.STATUS_N.equals(temporary.getIsDebiteawb())){
                        debitEawbService.insertByDmTempId(temporary.getDmTempId().longValue());
                    }
                }

            }else{
                debitEawbService.insertByDmId(dmId);
            }


            debitEawbService.insertBmsManifestAmount(dmId);

            debitEawbService.deleteAll();

//            debitEawbService.handleBmsManifestAmount(dmId);
//            orderPoolService.insertEawbByDmId(dmId);
            logger.info("同步eawb执行结束，更新同步状态");
            debitOrder.setSysStatus(sysStatus);
            debitOrder.setSysHandletime(new Date());
            debitOrderService.updateByPrimaryKeySelective(debitOrder);

        } catch (Exception e) {
            e.printStackTrace();
            logger.info("同步eawb执行中出错了，凉凉了！！！");
        }



        logger.info("同步eawb结束：");
    }
}
