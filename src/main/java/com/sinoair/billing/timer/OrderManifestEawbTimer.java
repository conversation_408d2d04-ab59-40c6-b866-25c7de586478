package com.sinoair.billing.timer;

import code.sinoair.core.support.cron.SinoairCron;
import com.sinoair.billing.core.util.MailUtil;
import com.sinoair.billing.core.util.SpringContextUtil;
import com.sinoair.billing.domain.constant.CheckConstant;
import com.sinoair.billing.domain.constant.MailConstant;
import com.sinoair.billing.domain.constant.OrderPoolConstant;
import com.sinoair.billing.domain.model.billing.DebitManifestTemporary;
import com.sinoair.billing.domain.model.billing.DebitOrder;
import com.sinoair.billing.domain.model.billing.ManifestOrder;
import com.sinoair.billing.service.order.DebitOrderService;
import com.sinoair.billing.service.order.ManifestOrderService;
import com.sinoair.billing.service.order.OrderPoolService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;
import java.util.List;

/**
 * Created by IntelliJ IDEA. 同步清单下的eawb任务
 *
 * @author:
 * @date: 2020/3/11
 * @time: 10:39
 * @description: To change this template use File | Settings | File Templates.
 */
public class OrderManifestEawbTimer extends SinoairCron {

    private Logger logger = LoggerFactory.getLogger(OrderManifestEawbTimer.class);

    public OrderManifestEawbTimer(String cronName, long timeout, boolean isExit) {
        super(cronName, timeout, isExit);
    }

    @Override
    public void cronThreadRun() throws Exception {
        testValidate();
        logger.info("测试结束");
    }

    private void testValidate(){

        OrderPoolService orderPoolService = SpringContextUtil.getBean("OrderPoolService");
        ManifestOrderService manifestOrderService = SpringContextUtil.getBean("ManifestOrderService");
//        List<Long> dmTemporaryIds = null;
        List<DebitManifestTemporary> dmTemporaryList = null;
        ManifestOrder manifestOrder = null;
        logger.info("查询是否有正在处理的清单和账单");
        int processCount = manifestOrderService.countManifestOrder(OrderPoolConstant.DM_STATUS_PROCESSING);
        if (processCount > 0){
            manifestOrder = manifestOrderService.getManifestOrder(OrderPoolConstant.DM_STATUS_PROCESSING);
            if (manifestOrder == null){
                logger.info("当前有正在处理的清单同步订单操作");
                return;
            }

        }else{
            //获取要处理的清单id
            logger.info("查询准备要处理的清单");
            manifestOrder = manifestOrderService.getManifestOrder(OrderPoolConstant.DM_STATUS_PENDING);
            if (manifestOrder == null){
                logger.info("没有要处理的清单");
                return;
            }

        }

        Long mId = manifestOrder.getmId();
        Long dmId = manifestOrder.getThreadNum();
//        dmTemporaryIds = orderPoolService.selectIdByDmId(dmId);
        dmTemporaryList = orderPoolService.selectTemporaryByDmId(dmId);

        manifestOrder.setSysStatus(OrderPoolConstant.DM_STATUS_PROCESSING);
        manifestOrder.setSysHandletime(new Date());
        manifestOrderService.updateByPrimaryKeySelective(manifestOrder);
        String sysStatus = OrderPoolConstant.DM_STATUS_FINISHED;


        try {

            logger.info("插入清单、账单和eawb信息 清单："+mId+ " 账单："+dmId+" 临时账单数："+dmTemporaryList.size());
            for (DebitManifestTemporary temporary : dmTemporaryList){
                if (CheckConstant.STATUS_N.equals(temporary.getIsBalance())){
                    orderPoolService.insertBalanceByTempId(mId,dmId,temporary.getDmTempId().longValue());
                }
            }

            logger.info("同步eawb执行结束，更新同步状态");
            manifestOrder.setSysStatus(sysStatus);
            manifestOrder.setSysHandletime(new Date());
            manifestOrderService.updateByPrimaryKeySelective(manifestOrder);

        } catch (Exception e) {
            e.printStackTrace();
            logger.info("同步eawb执行中出错了，凉凉了！！！");
            MailUtil.postMail(MailConstant.MAIL_TO_ONE,MailConstant.MAIL_SUBJECT_M_EAWB,e.getMessage());
        }



        logger.info("同步eawb结束：");
    }
}
