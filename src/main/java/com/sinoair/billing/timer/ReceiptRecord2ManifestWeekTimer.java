package com.sinoair.billing.timer;

import code.sinoair.core.support.cron.SinoairCron;
import com.sinoair.billing.core.util.SpringContextUtil;
import com.sinoair.billing.service.receipt.ReceiptService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

public class ReceiptRecord2ManifestWeekTimer extends SinoairCron {

    private Logger logger = LoggerFactory.getLogger(ReceiptRecord2ManifestMonthlyTimer.class);


    public ReceiptRecord2ManifestWeekTimer(String cronName, long timeout, boolean isExit) {
        super(cronName, timeout, isExit);
    }

    @Override
    public void cronThreadRun() throws Exception {
        testValidate();
    }

    private void testValidate(){
        logger.info("开始生成周结账单......");
        ReceiptService receiptService= SpringContextUtil.getBean("ReceiptService");
        receiptService.generateWeekDebitManifest();
        logger.info("生成周结账单任务结束......");
    }
}
