package com.sinoair.billing.timer;

import code.sinoair.core.support.cron.SinoairCron;
import com.sinoair.billing.core.util.*;
import com.sinoair.billing.domain.constant.CheckConstant;
import com.sinoair.billing.domain.constant.MailConstant;
import com.sinoair.billing.domain.model.billing.*;
import com.sinoair.billing.domain.vo.ceos.CeosEawb;
import com.sinoair.billing.domain.vo.query.CeosQuery;
import com.sinoair.billing.service.check.CheckEawbNoService;
import com.sinoair.billing.service.common.CommonService;
import com.sinoair.billing.service.order.OrderPoolService;
import com.sinoair.billing.service.syn.SynCeosEawbService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.*;

import static java.util.Comparator.comparing;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;

/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2018/7/4
 * @time: 10:39
 * @description: 按Code同步eawb数据
 */
public class SynCeosEawbByCodeTimer extends SinoairCron {

    private Logger logger = LoggerFactory.getLogger(SynCeosEawbByCodeTimer.class);

    public SynCeosEawbByCodeTimer(String cronName, long timeout, boolean isExit) {
        super(cronName, timeout, isExit);
    }

    @Override
    public void cronThreadRun() throws Exception {
        testValidate();
        logger.info("测试结束");
    }

    private void testValidate(){
        CheckEawbNoService eawbNoService = SpringContextUtil.getBean("CheckEawbNoService");
        SynCeosEawbService synCeosEawbService = SpringContextUtil.getBean("SynCeosEawbService");
        CommonService commonService = SpringContextUtil.getBean("CommonService");
        OrderPoolService orderPoolService = SpringContextUtil.getBean("OrderPoolService");

        CheckEawbNo checkNo = eawbNoService.selectCheckNo(CheckConstant.CEOS_EAWB);
        List<SettlementObject> soList = orderPoolService.selectSettlementList();
        try {
            List<String> mxxbKeyList = commonService.selectMxxbKeyList();

            Long maxCode = synCeosEawbService.selectMaxCeosSysCode();
            logger.info("查询MaxEbapSysCode : "+maxCode);
            CeosQuery param = new CeosQuery();
            param.setBeginNo(checkNo.getBeginNo());
            if (maxCode - checkNo.getBeginNo().longValue() > 100000){
                maxCode = checkNo.getBeginNo().longValue() + 100000;
            }
            if (checkNo.getEndNo() == null){
                param.setEndNo(new BigDecimal(maxCode));
            }else{
                param.setEndNo(checkNo.getEndNo());
            }
//            param.setBeginNo(new BigDecimal("2392128805"));
//            param.setEndNo(new BigDecimal("2392128807"));
            logger.info("开始查询 BeginNo："+param.getBeginNo() +" endNo:"+param.getEndNo());
//
            int allCount = param.getEndNo().subtract(param.getBeginNo()).intValue();
            if (allCount == 0){
                logger.info("查询数量为0");
                logger.info("下次编号为："+param.getEndNo());
                checkNo.setBeginNo(param.getEndNo());
                checkNo.setHandleDate(new Date());
                eawbNoService.updateEawbNo(checkNo);
                return;
            }
            List<CeosEawb> recordTmpList =synCeosEawbService.selectEawbBySysCode(param);

            int length = 1000;
            if (recordTmpList != null){
                int querySize = recordTmpList.size();
                logger.info("==recordTmpList.size=="+querySize);

                logger.info("==去重开始.size=="+recordTmpList.size());
                List<CeosEawb> recordList = recordTmpList.stream().collect(
                        collectingAndThen(
                                toCollection(() -> new TreeSet<>(comparing(n->n.getEawbPrintcode()))), ArrayList::new)
                );
                logger.info(" 线程"+param.getPageNum()+"==去重结束.size=="+recordList.size());

                List<ExpressAirWayBill> eawbList = new ArrayList<>();
                List<ExpressAirWayBill> eawbOtherList = new ArrayList<>();
                for (CeosEawb tmp : recordList) {
                    if (CheckConstant.CN_SO_CODE.equals(tmp.getEawbSoCode())){
                        continue;
                    }
                    else{
                        ExpressAirWayBill other = synCeosEawbService.selectCeosEawbOtherByCode(tmp.getEawbPrintcode());
                        if (other == null){
                            continue;
                        }

                        if (StringUtil.isEmpty(other.getSacId())){
                            other.setSacId(CheckUtil.getSoSacId(other.getEawbSoCode(),soList));
                        }
                        if (mxxbKeyList.contains(other.getEawbServiceTypeOriginal())){
                            other.setEawbTrackingNo(other.getEawbReference3());
                        }
                        if (CheckConstant.SPECIAL_SO.contains(other.getEawbSoCode())){
                            other.setWeightValue(other.getEawbGrossweight()==null?other.getEawbDeclaregrossweight():other.getEawbGrossweight());
                        }
                        if (CheckConstant.FEIYU_SO.contains(other.getEawbSoCode())
                                && CheckConstant.USPS_PM_SERVICETYPE.equals(other.getEawbServiceTypeOriginal())){
                            other.setWeightValue(CheckUtil.getCompareWeight(other));
                        }
                        if (CheckConstant.L_AE_STANDARD_SINOJP_RM.equals(other.getEawbServiceTypeOriginal())){
                            other.setWeightValue(other.getEawbDeclaregrossweight());
                            other.setEawbChargeableweight(other.getEawbDeclaregrossweight());
                        }

                        eawbList.add(other);
                    }
                }
//                logger.info("==菜鸟数.size=="+eawbList.size());
                logger.info("==非菜鸟数.size=="+eawbList.size());
                int size = eawbList.size();
                int count = size % length != 0 ? size / length + 1 : size / length;
                logger.info("==count=="+count);
                for (int t = 0; t < count; t++) {
                    List<ExpressAirWayBill> subList = eawbList.subList(t * length, ((length + t * length) < size ? (length + t * length) : size));

                    if (subList.size() > 0){
                        synCeosEawbService.synCeosEawbBySysCode(t,subList);
                    }
                }

//                int sizeOther = eawbOtherList.size();
//                int countCount = sizeOther % length != 0 ? sizeOther / length + 1 : sizeOther / length;
//                logger.info("==countCount=="+countCount);
//                for (int t = 0; t < countCount; t++) {
//                    List<ExpressAirWayBill> subList = eawbOtherList.subList(t * length, ((length + t * length) < sizeOther ? (length + t * length) : sizeOther));
//
//                    if (subList.size() > 0){
//                        synCeosEawbService.synCeosEawbOtherBySysCode(t,subList);
//                    }
//                }

            }

            //多个线程都执行结束
            logger.info("循环同步完成，可以做自己的事情了");
//        taskService.updateByTaskType(EbaConstant.CN_EBA_PUSH_1);
            logger.info("下次开始编号："+param.getEndNo());
            logger.info("多个线程都执行结束，可以做自己的事情了");
            checkNo.setBeginNo(param.getEndNo());
            checkNo.setHandleDate(new Date());
            eawbNoService.updateEawbNo(checkNo);
        } catch (Exception e){
            e.printStackTrace();
            MailUtil.postMail(MailConstant.MAIL_TO_ONE,MailConstant.MAIL_SUBJECT_CEOS_EAWB_CODE,e.getMessage());

        }

    }

}
