package com.sinoair.billing.timer;

import code.sinoair.core.support.cron.SinoairCron;
import com.sinoair.billing.core.util.DateUtil;
import com.sinoair.billing.core.util.MailUtil;
import com.sinoair.billing.core.util.SpringContextUtil;
import com.sinoair.billing.core.util.StringUtil;
import com.sinoair.billing.domain.constant.CheckConstant;
import com.sinoair.billing.domain.constant.MailConstant;
import com.sinoair.billing.domain.constant.ReceiptRecordConstant;
import com.sinoair.billing.domain.model.billing.ExpressAssignment;
import com.sinoair.billing.domain.model.billing.ExpressAssignmentActual;
import com.sinoair.billing.domain.model.billing.InsTask;
import com.sinoair.billing.domain.vo.query.CeosQuery;
import com.sinoair.billing.domain.vo.query.CommonQuery;
import com.sinoair.billing.service.syn.SynCeosEawbMawbService;
import com.sinoair.billing.service.syn.SynCeosEawbService;
import com.sinoair.billing.service.task.InsTaskService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2018/7/4
 * @time: 10:39
 * @description: 同步主单与eawb关联数据
 */
public class SynCeosEawbMawbTimer extends SinoairCron {

    private Logger logger = LoggerFactory.getLogger(SynCeosEawbMawbTimer.class);

    public SynCeosEawbMawbTimer(String cronName, long timeout, boolean isExit) {
        super(cronName, timeout, isExit);
    }

    @Override
    public void cronThreadRun() throws Exception {
        testValidate();
        logger.info("测试结束");
    }

    private void testValidate(){
        InsTaskService insTaskService = SpringContextUtil.getBean("InsTaskService");
        SynCeosEawbMawbService synCeosEawbMawbService = SpringContextUtil.getBean("SynCeosEawbMawbService");

        InsTask insTask = insTaskService.selectByTaskType(CheckConstant.SYN_CEOS_EAWB_MAWB);
        String taskStartDate = insTask.getTaskStartDate();
        String taskEndDate = insTask.getTaskEndDate();
        String taskFiled = "";
        //同步ceos主单表
        CeosQuery ceosQuery = new CeosQuery();
        ceosQuery.setStartEbaHandletime(taskStartDate);
        ceosQuery.setEndEbaHandletime(taskEndDate);
        synCeosEawbMawbService.synCeosMawb(ceosQuery);
        //绑定主单与eawb关系
        CommonQuery commonQuery = new CommonQuery();
        commonQuery.setStrStartDate(taskStartDate);
        commonQuery.setStrEndDate(taskEndDate);
        List<ExpressAssignmentActual> eaList = synCeosEawbMawbService.selectBillingEa(commonQuery);
        if (eaList.size() == 0){
            logger.info("ec-billing SynCeosEawbMawbTimer 没有产生新主单【"+taskStartDate+"】");
            MailUtil.postMail(MailConstant.MAIL_TO_ONE,"没有产生新主单【"+taskStartDate+"】","");
            return;
        }
        logger.info(taskStartDate+ " 主单数："+eaList.size());
        int seq = 0;
        for (ExpressAssignmentActual expressAssignment : eaList){
            String eaCode = expressAssignment.getEaaCode();
            int allCount = synCeosEawbMawbService.countCeosMawbEawbByEaCode(eaCode);
            seq++;
            logger.info("序号："+seq+" 主单："+eaCode +"订单总数："+allCount);
            if (allCount == 0){
                logger.info("查询数量为0");
                MailUtil.postMail(MailConstant.MAIL_TO_ONE,"主单【"+eaCode+"】 订单总数为0","");
//                break;
                continue;
            }

            int pageSize = CheckConstant.PAGE_SIZE;
            int threadNum = allCount / pageSize;
            if (allCount % pageSize != 0) {
                threadNum = threadNum + 1;
            }

            //所有线程阻塞，然后统一开始
//        CountDownLatch latch = new CountDownLatch(threadNum);

            for (int i = 1; i <= threadNum; i++) {

                CeosQuery param = new CeosQuery();

                param.setPageNum(i);
                param.setPageSize(pageSize);
                param.setEaCode(eaCode);

                synCeosEawbMawbService.synCeosEawbMawb(param);
            }
        }


        //多个线程都执行结束

        try {
//            latch.await();  //阻塞当前线程，直到latch的值为0
            int hour = DateUtil.getHours();
            String startDate = DateUtil.getCurDate();
//            String startDate = DateUtil.getPastDay(1);
            String endDate = DateUtil.getPastDay(1);

            logger.info("多个线程都执行结束，可以做自己的事情了");

            insTask.setTaskStatus(ReceiptRecordConstant.TASK_PENDING);
            if (StringUtil.isEmpty(taskFiled)){
                if (!startDate.equals(taskEndDate)){
                    startDate = taskEndDate;
                    endDate = DateUtil.getPastDayByDate(DateUtil.str2date(startDate,DateUtil.YYYYMMDD),1);
                }
                insTask.setTaskStartDate(startDate);
                insTask.setTaskEndDate(endDate);
            }


            insTask.setTaskRemark(DateUtil.getCurDate()+" "+hour+"点监控完成");
            insTask.setTaskHandletime(new Date());
            insTaskService.updateByPrimaryKeySelective(insTask);
            logger.info("下次开始编号："+startDate);

        } catch (Exception e) {
            e.printStackTrace();
            logger.info("多线程执行中出错了，凉凉了！！！");
        }

        logger.info("同步Ceos-eawb-mawb结束：");
    }
}
