package com.sinoair.billing.timer;

import code.sinoair.core.support.cron.SinoairCron;
import com.sinoair.billing.core.util.*;
import com.sinoair.billing.domain.constant.CheckConstant;
import com.sinoair.billing.domain.constant.MailConstant;
import com.sinoair.billing.domain.constant.ReceiptRecordConstant;
import com.sinoair.billing.domain.model.billing.ExpressAirWayBill;
import com.sinoair.billing.domain.model.billing.InsTask;
import com.sinoair.billing.domain.model.billing.SettlementObject;
import com.sinoair.billing.domain.vo.ceos.CeosEawb;
import com.sinoair.billing.domain.vo.query.CeosQuery;
import com.sinoair.billing.service.common.CommonService;
import com.sinoair.billing.service.order.OrderPoolService;
import com.sinoair.billing.service.syn.SynCeosEawbService;
import com.sinoair.billing.service.task.InsTaskService;
import com.sinoair.billing.thread.SynCeosEawbThread;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.TreeSet;
import java.util.concurrent.CountDownLatch;

import static java.util.Comparator.comparing;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;

/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2018/7/4
 * @time: 10:39
 * @description: 每天同步eawb数据
 */
public class SynCeosEawbTimer extends SinoairCron {

    private Logger logger = LoggerFactory.getLogger(SynCeosEawbTimer.class);

    public SynCeosEawbTimer(String cronName, long timeout, boolean isExit) {
        super(cronName, timeout, isExit);
    }

    @Override
    public void cronThreadRun() throws Exception {
        testValidate();
        logger.info("测试结束");
    }

    private void testValidate(){
//        CheckEawbNoService eawbNoService = SpringContextUtil.getBean("CheckEawbNoService");
        InsTaskService insTaskService = SpringContextUtil.getBean("InsTaskService");
        SynCeosEawbService synCeosEawbService = SpringContextUtil.getBean("SynCeosEawbService");
        OrderPoolService orderPoolService = SpringContextUtil.getBean("OrderPoolService");
        CommonService commonService = SpringContextUtil.getBean("CommonService");

        try {

            List<SettlementObject> soList = orderPoolService.selectSettlementList();
            List<String> mxxbKeyList = commonService.selectMxxbKeyList();

            InsTask insTask = insTaskService.selectByTaskType(CheckConstant.SYN_CEOS_EAWB);
            String taskStartDate = insTask.getTaskStartDate();
            String taskEndDate = insTask.getTaskEndDate();
            String taskFiled = insTask.getTaskFiled();
            if (StringUtil.isNotEmpty(taskFiled)){
                taskEndDate = DateUtil.getCurDate();
                taskStartDate = DateUtil.getPastDayByDate(DateUtil.str2date(taskEndDate,DateUtil.YYYYMMDD),Integer.parseInt(taskFiled));
            }

            CeosQuery query = new CeosQuery();
            query.setStartEbaHandletime(taskStartDate);
            query.setEndEbaHandletime(taskEndDate);
            query.setSoCode(insTask.getTaskSoCode());

            logger.info("开始查询 BeginDate："+query.getStartEbaHandletime() +" endDate:"+query.getEndEbaHandletime());

            int allCount = synCeosEawbService.countEawbByHandleTime(query);
            if (allCount == 0){
                logger.info("查询数量为0");
                return;
            }
            logger.info(query.getStartEbaHandletime() +"订单总数："+allCount);
            int pageSize = CheckConstant.PAGE_SIZE;
            int threadNum = allCount / pageSize;
            if (allCount % pageSize != 0) {
                threadNum = threadNum + 1;
            }
            //所有线程阻塞，然后统一开始
    //        CountDownLatch latch = new CountDownLatch(threadNum);
            //删除socode为空的数据，避免出现主键重复情况
//            synCeosEawbService.deleteBySoCodeEmpty();
            for (int i = 1; i <= threadNum; i++) {

                CeosQuery param = new CeosQuery();
                param.setStartEbaHandletime(query.getStartEbaHandletime());
                param.setEndEbaHandletime(query.getEndEbaHandletime());
                param.setSoCode(query.getSoCode());
                param.setPageNum(i);
                param.setPageSize(pageSize);

                List<CeosEawb> recordTmpList =synCeosEawbService.selectCeosEawbByTime(param);
                logger.info("==去重开始.size=="+recordTmpList.size());
                List<CeosEawb> recordList = recordTmpList.stream().collect(
                        collectingAndThen(
                                toCollection(() -> new TreeSet<>(comparing(n->n.getEawbPrintcode()))), ArrayList::new)
                );
                logger.info(" 线程"+param.getPageNum()+"==去重结束.size=="+recordList.size());
                List<ExpressAirWayBill> eawbList = new ArrayList<>();
                List<ExpressAirWayBill> eawbOtherList = new ArrayList<>();
                for (CeosEawb tmp : recordList) {
                    if (CheckConstant.CN_SO_CODE.equals(tmp.getEawbSoCode())){
                        continue;
                    }
                    ExpressAirWayBill other = synCeosEawbService.selectCeosEawbOtherByCode(tmp.getEawbPrintcode());
                    if (other == null){
                        continue;
                    }
                    if (other != null){
                        if (StringUtil.isEmpty(other.getSacId())){
                            other.setSacId(CheckUtil.getSoSacId(other.getEawbSoCode(),soList));
                        }
                        if (mxxbKeyList.contains(other.getEawbServiceTypeOriginal())){
                            other.setEawbTrackingNo(other.getEawbReference3());
                        }
                        if (CheckConstant.SPECIAL_SO.contains(other.getEawbSoCode())){
                            other.setWeightValue(other.getEawbGrossweight()==null?other.getEawbDeclaregrossweight():other.getEawbGrossweight());
                        }
                        if (CheckConstant.FEIYU_SO.contains(other.getEawbSoCode())
                                && CheckConstant.USPS_PM_SERVICETYPE.equals(other.getEawbServiceTypeOriginal())){
                            other.setWeightValue(CheckUtil.getCompareWeight(other));
                        }
                        if (CheckConstant.L_AE_STANDARD_SINOJP_RM.equals(other.getEawbServiceTypeOriginal())){
                            other.setWeightValue(other.getEawbDeclaregrossweight());
                            other.setEawbChargeableweight(other.getEawbDeclaregrossweight());
                        }

                        eawbList.add(other);
//                            eawbOtherList.add(other);

                    }

                }
                int length = 500;
                logger.info("==符合运单条件.size=="+eawbList.size());
                int size = eawbList.size();
                int count = size % length != 0 ? size / length + 1 : size / length;
                logger.info("==count=="+count);
//                CountDownLatch latch = new CountDownLatch(count);
                for (int t = 0; t < count; t++) {
                    List<ExpressAirWayBill> subList = eawbList.subList(t * length, ((length + t * length) < size ? (length + t * length) : size));
//                    new SynCeosEawbThread(t,length,null,null
//                            ,latch,subList).start();
                    synCeosEawbService.synCeosEawbBySysCode(t,subList);

                }

//                latch.await();  //阻塞当前线程，直到latch的值为0
            }
        //多个线程都执行结束

//
            int hour = DateUtil.getHours();
            String startDate = DateUtil.getCurDate();
            String endDate = DateUtil.getPastDay(1);

            logger.info("多个线程都执行结束，可以做自己的事情了");

            insTask.setTaskStatus(ReceiptRecordConstant.TASK_PENDING);

//            insTask.setTaskStartDate(startDate);
//            insTask.setTaskEndDate(endDate);
            insTask.setTaskStartDate(taskEndDate);
            insTask.setTaskEndDate(DateUtil.getPastDayByDate(DateUtil.str2date(taskEndDate,DateUtil.YYYYMMDD),1));
            logger.info("下次开始编号："+insTask.getTaskStartDate());

            insTask.setTaskRemark(startDate+" "+hour+"点监控完成");
            insTask.setTaskHandletime(new Date());
            insTaskService.updateByPrimaryKeySelective(insTask);

        } catch (Exception e) {
            e.printStackTrace();
            logger.info("多线程执行中出错了，凉凉了！！！");
            MailUtil.postMail(MailConstant.MAIL_TO_ONE,MailConstant.MAIL_SUBJECT_CEOS_EAWB,e.getMessage());

        }

        logger.info("同步ceos-eawb结束：");
    }
}
