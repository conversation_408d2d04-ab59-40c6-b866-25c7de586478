package com.sinoair.billing.timer;

import code.sinoair.core.support.cron.SinoairCron;
import com.sinoair.billing.core.util.DateUtil;
import com.sinoair.billing.core.util.MailUtil;
import com.sinoair.billing.core.util.SpringContextUtil;
import com.sinoair.billing.core.util.StringUtil;
import com.sinoair.billing.domain.constant.*;
import com.sinoair.billing.domain.model.billing.ExpressAirWayBill;
import com.sinoair.billing.domain.model.billing.ExpressBusinessActivity;
import com.sinoair.billing.domain.model.billing.InsTask;
import com.sinoair.billing.domain.model.ceosbi.EawbEadFlat;
import com.sinoair.billing.domain.vo.query.CeosQuery;
import com.sinoair.billing.service.common.CommonService;
import com.sinoair.billing.service.order.OrderPoolService;
import com.sinoair.billing.service.syn.SynCeosEadFlatService;
import com.sinoair.billing.service.syn.SynCeosEawbService;
import com.sinoair.billing.service.task.InsTaskService;
import com.sinoair.billing.service.temp.TempService;
import com.sinoair.billing.thread.SynCeosEadFlatThread;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

import static java.util.Comparator.comparing;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;

/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2018/7/4
 * @time: 10:39
 * @description: 同步环节并拉平
 */
public class SynCeosEbaFlatBySoCodeTimer extends SinoairCron {

    private Logger logger = LoggerFactory.getLogger(SynCeosEbaFlatBySoCodeTimer.class);

    public SynCeosEbaFlatBySoCodeTimer(String cronName, long timeout, boolean isExit) {
        super(cronName, timeout, isExit);
    }

    @Override
    public void cronThreadRun() throws Exception {
//        testValidate();
        testValidateBySocode();
//        testValidateByEpKey();
        logger.info("测试结束");
    }

    private void testValidateBySocode(){

        SynCeosEawbService synCeosEawbService = SpringContextUtil.getBean("SynCeosEawbService");
        CommonService commonService = SpringContextUtil.getBean("CommonService");

        try {

            String preDateStr = DateUtil.getPastDay(-2);
            Date preDate = DateUtil.str2date(preDateStr,DateUtil.YYYYMMDD);
//            Date preDate = DateUtil.str2date("2022-06-01",DateUtil.YYYY_MM_DD);
            logger.info("SynCeosEbaFlatBySoCodeTimer=查询日期=="+DateUtil.date2str(preDate,DateUtil.YYYY_MM_DD));

            List<String> soList = commonService.selectFlatSoList();
            logger.info("soList结果："+soList.size());
            //所有线程阻塞，然后统一开始
            for (int i = 0; i < soList.size(); i++) {
                String soCode = soList.get(i);
                logger.info((i+1)+" 开始查询 soCode："+soCode);
                CeosQuery param = new CeosQuery();

                param.setSoCode(soCode);
                param.setStartEbaHandletime(preDateStr);


                int allCount = commonService.countCeosActivitySoCode(param);
                logger.info("soCode："+soCode+" 环节数量："+allCount);
                int pageSize = CheckConstant.PAGE_SIZE;
                int threadNum = allCount / pageSize;
                if (allCount % pageSize != 0) {
                    threadNum = threadNum + 1;
                }

                for (int p = 1; p <= threadNum; p++) {
                    param.setPageNum(p);
                    param.setPageSize(CheckConstant.PAGE_SIZE);
                    List<ExpressBusinessActivity> recordTmpList =commonService.selectCeosActivitySoCode(param);

                    logger.info("线程"+param.getPageNum()+"==去重开始.size=="+recordTmpList.size());
                    List<ExpressBusinessActivity> recordList = recordTmpList.stream().collect(
                            collectingAndThen(
                                    toCollection(() -> new TreeSet<>(comparing(n->n.getEawbPrintcode()+";"+n.getEadCode()+";"+n.getEastCode()))),ArrayList::new)
                    );
                    logger.info("线程"+param.getPageNum()+"==去重结束.size=="+recordList.size());

                    Map<Long, List<ExpressBusinessActivity>> activityMap = recordList.stream().filter(item-> (item.getEawbSyscode() != null && !CheckConstant.CN_SO_CODE.equals(item.getSoCode())
                            && item.getEbaHandletime().compareTo(preDate) > -1)).collect(
                            Collectors.groupingBy(a -> a.getEawbSyscode()));
                    logger.info(" 线程"+param.getPageNum()+"==过滤后.size=="+activityMap.size());
                    Date curDate = new Date();
                    List<EawbEadFlat> flatList = new ArrayList<>();
                    for (Map.Entry<Long, List<ExpressBusinessActivity>> m : activityMap.entrySet()) {
                        Long eawbSyscode = m.getKey();
                        List<ExpressBusinessActivity> ebaList = m.getValue();
                        ExpressBusinessActivity activity = ebaList.get(0);
                        if (CheckConstant.CN_SO_CODE.equals(activity.getSoCode())){
                            continue;
                        }

                        EawbEadFlat eadFlat = setEadFlat(ebaList);

                        if (StringUtil.isNotEmpty(eadFlat.getEawbPrintcode())){
                            ExpressAirWayBill airWayBill = synCeosEawbService.selectCeosFlatPre(eadFlat.getEawbPrintcode());
                            if (airWayBill == null){
                                airWayBill = synCeosEawbService.selectByEawbPrintCode(eadFlat.getEawbPrintcode());
                            }

                            eadFlat.setEawbSyscode(new BigDecimal(eawbSyscode));
                            eadFlat.setEefUpdatetime(curDate);

                            if (airWayBill != null){
                                eadFlat.setEawbDestcountry(airWayBill.getEawbDestcountry());
                                eadFlat.setEawbServicetype(airWayBill.getEawbServicetype());
                                eadFlat.setEawbSoCode(airWayBill.getEawbSoCode());
                                eadFlat.setEawbPostcode(airWayBill.getEawbDeliverPostcode());
                                if (StringUtil.isNotEmpty(airWayBill.getEawbDeliverPostcode())){
                                    eadFlat.setPostcodeFirst(airWayBill.getEawbDeliverPostcode().substring(0,1));
                                }
                            }

                            flatList.add(eadFlat);
                        }
                    }

                    int length = 10000;
                    logger.info("==符合环节节点条件.size=="+flatList.size());
                    int size = flatList.size();
                    int count = size % length != 0 ? size / length + 1 : size / length;
                    logger.info("==count=="+count);
                    CountDownLatch latch = new CountDownLatch(count);
                    for (int t = 0; t < count; t++) {
                        List<EawbEadFlat> subList = flatList.subList(t * length, ((length + t * length) < size ? (length + t * length) : size));
                        new SynCeosEadFlatThread(t,length,latch,subList).start();
                    }

                    latch.await();  //阻塞当前线程，直到latch的值为0
                }

            }
            //多个线程都执行结束

            logger.info("多个线程都执行结束，可以做自己的事情了");


        } catch (Exception e) {
            e.printStackTrace();
            logger.info("SynCeosEbaFlatTimer 多线程执行中出错了，凉凉了！！！");
            MailUtil.postMail(MailConstant.MAIL_TO_ONE,MailConstant.MAIL_SUBJECT_CEOS_EAD_FLAT,e.getMessage());

        }

        logger.info("同步CEOS_EAD_FLAT结束：");
    }

    private EawbEadFlat setEadFlat(List<ExpressBusinessActivity> activityList){
        EawbEadFlat eadFlat = new EawbEadFlat();
        for (ExpressBusinessActivity activity : activityList){

            setEadFlat(eadFlat,activity);
        }
        return eadFlat;
    }

    private void setEadFlat(EawbEadFlat eadFlat,ExpressBusinessActivity activity){
        String eadCode = activity.getEadCode();
        String eawbPrintcode = activity.getEawbPrintcode();

        switch (eadCode) {
            case EadFlatConstant.FC_INBOUND:
                eadFlat.setFcInbound(setOccurtime(activity));
                eadFlat.setEawbPrintcode(eawbPrintcode);
                if (eadFlat.getStatusValueInt() == null || FlatStatusEnum.getValue(FlatStatusEnum.INBOUND.getKey()) > eadFlat.getStatusValueInt()){
                    eadFlat.setStatus(FlatStatusEnum.INBOUND.getKey());
                    eadFlat.setStatusValueInt(FlatStatusEnum.getValue(eadFlat.getStatus()));
                }
                break;
            case EadFlatConstant.FC_OUTBOUND:
                eadFlat.setFcOutbound(setOccurtime(activity));
                eadFlat.setEawbPrintcode(eawbPrintcode);
                if (eadFlat.getStatusValueInt() == null || FlatStatusEnum.getValue(FlatStatusEnum.OUTBOUND.getKey()) > eadFlat.getStatusValueInt()) {
                    eadFlat.setStatus(FlatStatusEnum.OUTBOUND.getKey());
                    eadFlat.setStatusValueInt(FlatStatusEnum.getValue(eadFlat.getStatus()));
                }
                break;
            case EadFlatConstant.UNASS:
                eadFlat.setUnass(setOccurtime(activity));
                eadFlat.setEawbPrintcode(eawbPrintcode);
                if (eadFlat.getStatusValueInt() == null || FlatStatusEnum.getValue(FlatStatusEnum.EXCEPTION.getKey()) > eadFlat.getStatusValueInt()) {
                    eadFlat.setStatus(FlatStatusEnum.EXCEPTION.getKey());
                    eadFlat.setStatusValueInt(FlatStatusEnum.getValue(eadFlat.getStatus()));
                }
                break;
            case EadFlatConstant.DELIVERY:
                String d_eastCode = activity.getEastCode();
                if (EadFlatConstant.OK.equals(d_eastCode)){
                    eadFlat.setDelivery(setOccurtime(activity));
                    eadFlat.setEawbPrintcode(eawbPrintcode);
                    if (eadFlat.getStatusValueInt() == null || FlatStatusEnum.getValue(FlatStatusEnum.DELIVERY.getKey()) > eadFlat.getStatusValueInt()) {
                        eadFlat.setStatus(FlatStatusEnum.DELIVERY.getKey());
                        eadFlat.setStatusValueInt(FlatStatusEnum.getValue(eadFlat.getStatus()));
                    }
                }
                if (EadFlatConstant.D_DEF.equals(d_eastCode)){
                    eadFlat.setUndelivery(setOccurtime(activity));
                    eadFlat.setEawbPrintcode(eawbPrintcode);
                    if (eadFlat.getStatusValueInt() == null || FlatStatusEnum.getValue(FlatStatusEnum.UNDELIVERY.getKey()) > eadFlat.getStatusValueInt()) {
                        eadFlat.setStatus(FlatStatusEnum.UNDELIVERY.getKey());
                        eadFlat.setStatusValueInt(FlatStatusEnum.getValue(eadFlat.getStatus()));
                    }
                }
                break;
            case EadFlatConstant.INFOR:
                String i_eastCode = activity.getEastCode();
                if (EadFlatConstant.I_DECLARE.equals(i_eastCode)){
                    eadFlat.setDeclare(setOccurtime(activity));
                    eadFlat.setEawbPrintcode(eawbPrintcode);
                    if (eadFlat.getStatusValueInt() == null || FlatStatusEnum.getValue(FlatStatusEnum.DECLARE.getKey()) > eadFlat.getStatusValueInt()) {
                        eadFlat.setStatus(FlatStatusEnum.DECLARE.getKey());
                        eadFlat.setStatusValueInt(FlatStatusEnum.getValue(eadFlat.getStatus()));
                    }
                }
                break;
            case EadFlatConstant.INTERNATIONAL:
                String eastCode = activity.getEastCode();
                if (EadFlatConstant.S_ADC.equals(eastCode)){
                    eadFlat.setAdc(setOccurtime(activity));
                    eadFlat.setEawbPrintcode(eawbPrintcode);
                }
                if (EadFlatConstant.S_ASS.equals(eastCode)){
                    eadFlat.setAss(setOccurtime(activity));
                    eadFlat.setEawbPrintcode(eawbPrintcode);
                }
                if (EadFlatConstant.S_CPT.equals(eastCode)){
                    eadFlat.setCpt(setOccurtime(activity));
                    eadFlat.setEawbPrintcode(eawbPrintcode);
                }
                if (EadFlatConstant.S_ROE.equals(eastCode)){
                    eadFlat.setRoe(setOccurtime(activity));
                    eadFlat.setEawbPrintcode(eawbPrintcode);
                }
                if (EadFlatConstant.S_CLRDT.equals(eastCode)){
                    eadFlat.setRoe(setOccurtime(activity));
                    eadFlat.setEawbPrintcode(eawbPrintcode);
                }
                if (eadFlat.getStatusValueInt() == null || FlatStatusEnum.getValue(FlatStatusEnum.SENDING.getKey()) > eadFlat.getStatusValueInt()) {
                    eadFlat.setStatus(FlatStatusEnum.SENDING.getKey());
                    eadFlat.setStatusValueInt(FlatStatusEnum.getValue(eadFlat.getStatus()));
                }
                break;

        }

    }

    private Date setOccurtime(ExpressBusinessActivity activity){
        return activity.getEbaOccurtime() == null?activity.getEbaHandletime():activity.getEbaOccurtime();
    }
}
