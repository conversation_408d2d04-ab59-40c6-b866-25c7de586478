package com.sinoair.billing.timer;

import code.sinoair.core.support.cron.SinoairCron;
import com.sinoair.billing.core.util.*;
import com.sinoair.billing.domain.constant.*;
import com.sinoair.billing.domain.model.billing.*;
import com.sinoair.billing.domain.model.ceosbi.EawbEadFlat;
import com.sinoair.billing.domain.vo.ceos.CeosEawb;
import com.sinoair.billing.domain.vo.query.CeosQuery;
import com.sinoair.billing.service.common.CommonService;
import com.sinoair.billing.service.order.OrderPoolService;
import com.sinoair.billing.service.syn.SynCeosEadFlatService;
import com.sinoair.billing.service.syn.SynCeosEawbService;
import com.sinoair.billing.service.task.InsTaskService;
import com.sinoair.billing.service.temp.TempService;
import com.sinoair.billing.thread.SynCeosEadFlatThread;
import com.sinoair.billing.thread.SynCeosEawbThread;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

import static java.util.Comparator.comparing;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;

/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2018/7/4
 * @time: 10:39
 * @description: 同步环节并拉平
 */
public class SynCeosEbaFlatTimer extends SinoairCron {

    private final BigDecimal incrementNum = new BigDecimal(1000000);

    private Logger logger = LoggerFactory.getLogger(SynCeosEbaFlatTimer.class);

    public SynCeosEbaFlatTimer(String cronName, long timeout, boolean isExit) {
        super(cronName, timeout, isExit);
    }

    @Override
    public void cronThreadRun() throws Exception {
        testValidateV2();
//        testValidateByEpKey();
        logger.info("测试结束");
    }

    private void testValidateV2() {
        InsTaskService insTaskService = SpringContextUtil.getBean("InsTaskService");
        CommonService commonService = SpringContextUtil.getBean("CommonService");
        try {
            InsTask insTask = insTaskService.selectByTaskType(CheckConstant.SYN_CEOS_EBA_FLAT_V2);
            String taskStartSysCode = insTask.getTaskStartDate();
            String taskEndSysCode = insTask.getTaskEndDate();
            CeosQuery param = new CeosQuery();
            param.setBeginNo(new BigDecimal(taskStartSysCode));
            param.setEndNo(new BigDecimal(taskEndSysCode));
            param.setSoCode(CheckConstant.CN_SO_CODE);
            CeosQuery query = new CeosQuery();
            query.setBeginNo(new BigDecimal(taskStartSysCode));
            query.setEndNo(new BigDecimal(taskEndSysCode));
            BigDecimal maxNum = commonService.selectCeosActivityMax(query);
            List<ExpressBusinessActivity> recordTmpList = commonService.selectCeosActivity(param);
            logger.info("==去重开始.size==" + recordTmpList.size());
            List<ExpressBusinessActivity> recordList = recordTmpList.stream().collect(
                    collectingAndThen(
                            toCollection(() -> new TreeSet<>(comparing(n -> n.getEawbPrintcode() + ";" + n.getEadCode() + ";" + n.getEastCode()))), ArrayList::new)
            );
            logger.info(" 线程" + param.getPageNum() + "==去重结束.size==" + recordList.size());

            Map<Long, List<ExpressBusinessActivity>> activityMap = recordList.stream().collect(
                    Collectors.groupingBy(a -> a.getEawbSyscode()));
            logger.info(" 线程" + param.getPageNum() + "==过滤后.size==" + activityMap.size());
            List<EawbEadFlat> flatList = new ArrayList<>();
            for (Map.Entry<Long, List<ExpressBusinessActivity>> m : activityMap.entrySet()) {
                List<ExpressBusinessActivity> ebaList = m.getValue();
                EawbEadFlat eadFlat = setEadFlat(ebaList);
                if (StringUtil.isNotEmpty(eadFlat.getEawbPrintcode())) {
                    flatList.add(eadFlat);
                }
            }

            int length = 10000;
            logger.info("==符合环节节点条件.size==" + flatList.size());
            int size = flatList.size();
            int count = size % length != 0 ? size / length + 1 : size / length;
            logger.info("==count==" + count);
            CountDownLatch latch = new CountDownLatch(count);
            for (int t = 0; t < count; t++) {
                List<EawbEadFlat> subList = flatList.subList(t * length, ((length + t * length) < size ? (length + t * length) : size));
                new SynCeosEadFlatThread(t, length, latch, subList).start();
            }
            latch.await();
            //多个线程都执行结束
            int hour = DateUtil.getHours();
            String startDate = DateUtil.getCurDate();
            logger.info("多个线程都执行结束，可以做自己的事情了");
            insTask.setTaskStatus(ReceiptRecordConstant.TASK_PENDING);
            //每次查询一百万条
            //下次起始值为本次查询结果的ebaSysCode最大值, 下次结束值为下次起始值加增量单位
            BigDecimal nextStartNum;
//            Optional<ExpressBusinessActivity> max = recordList.stream().max(comparing(ExpressBusinessActivity::getCeosEbaSyscode));
            if (null != maxNum && maxNum.doubleValue() > 0) {
                nextStartNum = maxNum.add(new BigDecimal(1));
            } else {
                nextStartNum = new BigDecimal(taskStartSysCode).add(new BigDecimal(recordTmpList.size()));
            }
            BigDecimal nextEndNum = nextStartNum.add(incrementNum);
            insTask.setTaskStartDate(nextStartNum.toString());
            insTask.setTaskEndDate(nextEndNum.toString());
            logger.info("下次开始编号：" + insTask.getTaskStartDate());
            insTask.setTaskRemark(startDate + " " + hour + "点监控完成");
            insTask.setTaskHandletime(new Date());
            insTaskService.updateByPrimaryKeySelective(insTask);

        } catch (Exception e) {
            e.printStackTrace();
            logger.info("SynCeosEbaFlatTimer 多线程执行中出错了，凉凉了！！！");
            MailUtil.postMail(MailConstant.MAIL_TO_ONE, MailConstant.MAIL_SUBJECT_CEOS_EAD_FLAT, e.getMessage());

        }
        logger.info("同步CEOS_EAD_FLAT结束：");
    }

    private void testValidate(){
//        CheckEawbNoService eawbNoService = SpringContextUtil.getBean("CheckEawbNoService");
        InsTaskService insTaskService = SpringContextUtil.getBean("InsTaskService");
        SynCeosEawbService synCeosEawbService = SpringContextUtil.getBean("SynCeosEawbService");
        OrderPoolService orderPoolService = SpringContextUtil.getBean("OrderPoolService");
        CommonService commonService = SpringContextUtil.getBean("CommonService");
        SynCeosEadFlatService synCeosEadFlatService = SpringContextUtil.getBean("SynCeosEadFlatService");

        try {

            InsTask insTask = insTaskService.selectByTaskType(CheckConstant.SYN_CEOS_EBA_FLAT);
            String taskStartDate = insTask.getTaskStartDate();
            String taskEndDate = insTask.getTaskEndDate();
            String taskFiled = insTask.getTaskFiled();
            if (StringUtil.isNotEmpty(taskFiled)){
                taskEndDate = DateUtil.getCurDate();
                taskStartDate = DateUtil.getPastDayByDate(DateUtil.str2date(taskEndDate,DateUtil.YYYYMMDD),Integer.parseInt(taskFiled));
            }
//            taskStartDate = "20220512";
//            taskEndDate = "20220513";

            CeosQuery query = new CeosQuery();
            query.setStartEbaHandletime(taskStartDate);
            query.setEndEbaHandletime(taskEndDate);

            logger.info("开始查询 BeginDate："+query.getStartEbaHandletime() +" endDate:"+query.getEndEbaHandletime());

            int allCount = commonService.selectCeosActivityCount(query);
            if (allCount == 0){
                logger.info("查询数量为0");
                return;
            }
            logger.info(query.getStartEbaHandletime() +"订单总数："+allCount);
            int pageSize = CheckConstant.PAGE_SIZE;
            int threadNum = allCount / pageSize;
            if (allCount % pageSize != 0) {
                threadNum = threadNum + 1;
            }
            //所有线程阻塞，然后统一开始
            for (int i = 1; i <= threadNum; i++) {

                CeosQuery param = new CeosQuery();
                param.setStartEbaHandletime(query.getStartEbaHandletime());
                param.setEndEbaHandletime(query.getEndEbaHandletime());
                param.setSoCode(query.getSoCode());
                param.setPageNum(i);
                param.setPageSize(CheckConstant.PAGE_SIZE);

//                List<CeosEawb> recordTmpList =synCeosEawbService.selectCeosEawbByTime(param);
                List<ExpressBusinessActivity> recordTmpList =commonService.selectCeosActivity(param);
//                List<ExpressBusinessActivity> recordTmpList =commonService.selectCeosActivitySoCode(param);
                logger.info("==去重开始.size=="+recordTmpList.size());
                List<ExpressBusinessActivity> recordList = recordTmpList.stream().collect(
                        collectingAndThen(
                                toCollection(() -> new TreeSet<>(comparing(n->n.getEawbPrintcode()+";"+n.getEadCode()+";"+n.getEastCode()))),ArrayList::new)
                );
                logger.info(" 线程"+param.getPageNum()+"==去重结束.size=="+recordList.size());

                Map<Long, List<ExpressBusinessActivity>> activityMap = recordList.stream().filter(item-> (item.getEawbSyscode() != null && !CheckConstant.CN_SO_CODE.equals(item.getSoCode()))).collect(
                        Collectors.groupingBy(a -> a.getEawbSyscode()));
                logger.info(" 线程"+param.getPageNum()+"==过滤后.size=="+activityMap.size());
                Date curDate = new Date();
                List<EawbEadFlat> flatList = new ArrayList<>();
                for (Map.Entry<Long, List<ExpressBusinessActivity>> m : activityMap.entrySet()) {
                    Long eawbSyscode = m.getKey();
                    List<ExpressBusinessActivity> ebaList = m.getValue();
                    ExpressBusinessActivity activity = ebaList.get(0);
                    if (CheckConstant.CN_SO_CODE.equals(activity.getSoCode())){
                        continue;
                    }

                    EawbEadFlat eadFlat = setEadFlat(ebaList);

                    if (StringUtil.isNotEmpty(eadFlat.getEawbPrintcode())){
                        ExpressAirWayBill airWayBill = synCeosEawbService.selectCeosFlatPre(eadFlat.getEawbPrintcode());
                        if (airWayBill == null){
                            airWayBill = synCeosEawbService.selectByEawbPrintCode(eadFlat.getEawbPrintcode());
                        }
                        if (airWayBill != null && CheckConstant.CN_SO_CODE.equals(airWayBill.getEawbSoCode())){
                            continue;
                        }
//                        ExpressAirWayBillCeos ceosAirWayBill = synCeosEawbService.selectCeosEawbByEawbPrintcode(eadFlat.getEawbPrintcode());
//                        if (ceosAirWayBill == null || CheckConstant.CN_SO_CODE.equals(ceosAirWayBill.getEawbSoCode())){
//                            continue;
//                        }

                        eadFlat.setEawbSyscode(new BigDecimal(eawbSyscode));
                        eadFlat.setEefUpdatetime(curDate);

                        if (airWayBill != null){
                            eadFlat.setEawbDestcountry(airWayBill.getEawbDestcountry());
                            eadFlat.setEawbServicetype(airWayBill.getEawbServicetype());
                            eadFlat.setEawbSoCode(airWayBill.getEawbSoCode());
                            eadFlat.setEawbPostcode(airWayBill.getEawbDeliverPostcode());
                            if (StringUtil.isNotEmpty(airWayBill.getEawbDeliverPostcode())){
                                eadFlat.setPostcodeFirst(airWayBill.getEawbDeliverPostcode().substring(0,1));
                            }
                        }

                        flatList.add(eadFlat);
                    }
                }

                int length = 1000;
                logger.info("==符合环节节点条件.size=="+flatList.size());
                int size = flatList.size();
                int count = size % length != 0 ? size / length + 1 : size / length;
                logger.info("==count=="+count);

                for (int t = 0; t < count; t++) {

                    List<EawbEadFlat> subList = flatList.subList(t * length, ((length + t * length) < size ? (length + t * length) : size));
                    synCeosEadFlatService.synCeosEadFlat(i,subList);
//                    int insertLength = 1000;
//                    int subSize = subList.size();
//                    int subCount = subSize % insertLength != 0 ? subSize / insertLength + 1 : subSize / insertLength;
//                    CountDownLatch latch = new CountDownLatch(subCount);
//                    for (int n = 0; n < subCount; n++) {
//                        List<EawbEadFlat> eadList = subList.subList(n * insertLength, ((1000 + n * insertLength) < subSize ? (1000 + n * insertLength) : subSize));
//                        new SynCeosEadFlatThread(n,insertLength,latch,eadList).start();
//                    }
//
//                    latch.await();  //阻塞当前线程，直到latch的值为0
                }


            }
            //多个线程都执行结束

//
            int hour = DateUtil.getHours();
            String startDate = DateUtil.getCurDate();
            String endDate = DateUtil.getPastDay(1);

            logger.info("多个线程都执行结束，可以做自己的事情了");

            insTask.setTaskStatus(ReceiptRecordConstant.TASK_PENDING);
            if(DateUtil.getCurDate().equals(taskStartDate)){
                insTask.setTaskStartDate(taskStartDate);
                insTask.setTaskEndDate(taskEndDate);
            }else{
                insTask.setTaskStartDate(taskEndDate);
                insTask.setTaskEndDate(DateUtil.getPastDayByDate(DateUtil.str2date(taskEndDate,DateUtil.YYYYMMDD),1));
            }

            logger.info("下次开始编号："+insTask.getTaskStartDate());

            insTask.setTaskRemark(startDate+" "+hour+"点监控完成");
            insTask.setTaskHandletime(new Date());
//            insTaskService.updateByPrimaryKeySelective(insTask);

        } catch (Exception e) {
            e.printStackTrace();
            logger.info("SynCeosEbaFlatTimer 多线程执行中出错了，凉凉了！！！");
            MailUtil.postMail(MailConstant.MAIL_TO_ONE,MailConstant.MAIL_SUBJECT_CEOS_EAD_FLAT,e.getMessage());

        }

        logger.info("同步CEOS_EAD_FLAT结束：");
    }

    private void testValidateByEpKey(){
//        CheckEawbNoService eawbNoService = SpringContextUtil.getBean("CheckEawbNoService");
        InsTaskService insTaskService = SpringContextUtil.getBean("InsTaskService");
        SynCeosEawbService synCeosEawbService = SpringContextUtil.getBean("SynCeosEawbService");
        OrderPoolService orderPoolService = SpringContextUtil.getBean("OrderPoolService");
        CommonService commonService = SpringContextUtil.getBean("CommonService");
        TempService tempService = SpringContextUtil.getBean("TempService");
        SynCeosEadFlatService synCeosEadFlatService = SpringContextUtil.getBean("SynCeosEadFlatService");

        try {

            List<String> eawbList = tempService.selectTempAsendia();

            int num = 0;
            List<EawbEadFlat> flatList = new ArrayList<>();
            for (String printCode : eawbList){
                num++;
                CeosQuery param = new CeosQuery();

                param.setPageNum(1);
                param.setPageSize(1000);
                param.setEawbPrintcode(printCode);

                ExpressAirWayBill airWayBill = synCeosEawbService.selectOtherByEawbPrintCode(printCode);

//                List<CeosEawb> recordTmpList =synCeosEawbService.selectCeosEawbByTime(param);
                List<ExpressBusinessActivity> recordTmpList =commonService.selectCeosActivity(param);
                logger.info("==去重开始.size=="+recordTmpList.size());
                List<ExpressBusinessActivity> recordList = recordTmpList.stream().collect(
                        collectingAndThen(
                                toCollection(() -> new TreeSet<>(comparing(n->n.getEawbPrintcode()+";"+n.getEadCode()+";"+n.getEastCode()))),ArrayList::new)
                );
                logger.info(" 线程"+num+" code: "+printCode+"==去重结束.size=="+recordList.size());

                Map<Long, List<ExpressBusinessActivity>> activityMap = recordList.stream().filter(item-> item.getEawbSyscode() != null).collect(
                        Collectors.groupingBy(a -> a.getEawbSyscode()));
                Date curDate = new Date();

//                List<Map<Long,List<EawbEadFlat>>> mapList = new ArrayList<>();
                for (Map.Entry<Long, List<ExpressBusinessActivity>> m : activityMap.entrySet()) {
                    Long eawbSyscode = m.getKey();
                    List<ExpressBusinessActivity> ebaList = m.getValue();
                    EawbEadFlat eadFlat = setEadFlat(ebaList);

                    if (StringUtil.isNotEmpty(eadFlat.getEawbPrintcode())){
                        eadFlat.setEawbSyscode(new BigDecimal(eawbSyscode));
                        eadFlat.setEefUpdatetime(curDate);
                        if (airWayBill != null){
                            eadFlat.setEawbDestcountry(airWayBill.getEawbDestcountry());
                            eadFlat.setEawbServicetype(airWayBill.getEawbServicetype());
                            eadFlat.setEawbSoCode(airWayBill.getEawbSoCode());
                            eadFlat.setEawbPostcode(airWayBill.getEawbDeliverPostcode());
                            if (StringUtil.isNotEmpty(airWayBill.getEawbDeliverPostcode())){
                                eadFlat.setPostcodeFirst(airWayBill.getEawbDeliverPostcode().substring(0,1));
                            }
                        }

                        flatList.add(eadFlat);
                    }
                }
//                synCeosEadFlatService.synCeosEadFlat(1,flatList);



//                latch.await();  //阻塞当前线程，直到latch的值为0
            }
            //多个线程都执行结束
            int length = 10000;
            logger.info("==符合环节节点条件.size=="+flatList.size());
            int size = flatList.size();
            int count = size % length != 0 ? size / length + 1 : size / length;
            logger.info("==count=="+count);
            CountDownLatch latch = new CountDownLatch(count);
            for (int t = 0; t < count; t++) {
                List<EawbEadFlat> subList = flatList.subList(t * length, ((length + t * length) < size ? (length + t * length) : size));
                new SynCeosEadFlatThread(t,length,latch,subList).start();
            }

//
            int hour = DateUtil.getHours();
            String startDate = DateUtil.getCurDate();
            String endDate = DateUtil.getPastDay(1);
            logger.info("下次开始编号："+startDate);
            logger.info("多个线程都执行结束，可以做自己的事情了");

//            insTask.setTaskStatus(ReceiptRecordConstant.TASK_PENDING);
//
////            insTask.setTaskStartDate(startDate);
////            insTask.setTaskEndDate(endDate);
//            insTask.setTaskStartDate(taskEndDate);
//            insTask.setTaskEndDate(DateUtil.getPastDayByDate(DateUtil.str2date(taskEndDate,DateUtil.YYYYMMDD),1));
//
//            insTask.setTaskRemark(startDate+" "+hour+"点监控完成");
//            insTask.setTaskHandletime(new Date());
//            insTaskService.updateByPrimaryKeySelective(insTask);

        } catch (Exception e) {
            e.printStackTrace();
            logger.info("SynCeosEbaFlatTimer 多线程执行中出错了，凉凉了！！！");
            MailUtil.postMail(MailConstant.MAIL_TO_ONE,MailConstant.MAIL_SUBJECT_CEOS_EAD_FLAT,e.getMessage());

        }

        logger.info("同步CEOS_EAD_FLAT结束：");
    }

    private EawbEadFlat setEadFlat(List<ExpressBusinessActivity> activityList){
        EawbEadFlat eadFlat = new EawbEadFlat();
        for (ExpressBusinessActivity activity : activityList){

            setEadFlat(eadFlat,activity);
        }
        return eadFlat;
    }

    private void setEadFlat(EawbEadFlat eadFlat,ExpressBusinessActivity activity){
        String eadCode = activity.getEadCode();
        String eawbPrintcode = activity.getEawbPrintcode();
        eadFlat.setEawbSyscode(new BigDecimal(activity.getEawbSyscode()));
        eadFlat.setEefUpdatetime(new Date());
        eadFlat.setEawbDestcountry(activity.getEawbDestcountry());
        eadFlat.setEawbServicetype(activity.getEawbServicetype());
        eadFlat.setEawbSoCode(activity.getEawbSoCode());
        eadFlat.setEawbPostcode(activity.getEawbDeliverPostcode());
        if (StringUtil.isNotEmpty(activity.getEawbDeliverPostcode())) {
            eadFlat.setPostcodeFirst(activity.getEawbDeliverPostcode().substring(0, 1));
        }

        switch (eadCode) {
            case EadFlatConstant.FC_INBOUND:
                eadFlat.setFcInbound(setOccurtime(activity));
                eadFlat.setEawbPrintcode(eawbPrintcode);
                if (eadFlat.getStatusValueInt() == null || FlatStatusEnum.getValue(FlatStatusEnum.INBOUND.getKey()) > eadFlat.getStatusValueInt()){
                    eadFlat.setStatus(FlatStatusEnum.INBOUND.getKey());
                    eadFlat.setStatusValueInt(FlatStatusEnum.getValue(eadFlat.getStatus()));
                }
                break;
            case EadFlatConstant.FC_OUTBOUND:
                eadFlat.setFcOutbound(setOccurtime(activity));
                eadFlat.setEawbPrintcode(eawbPrintcode);
                if (eadFlat.getStatusValueInt() == null || FlatStatusEnum.getValue(FlatStatusEnum.OUTBOUND.getKey()) > eadFlat.getStatusValueInt()) {
                    eadFlat.setStatus(FlatStatusEnum.OUTBOUND.getKey());
                    eadFlat.setStatusValueInt(FlatStatusEnum.getValue(eadFlat.getStatus()));
                }
                break;
            case EadFlatConstant.UNASS:
                eadFlat.setUnass(setOccurtime(activity));
                eadFlat.setEawbPrintcode(eawbPrintcode);
                if (eadFlat.getStatusValueInt() == null || FlatStatusEnum.getValue(FlatStatusEnum.EXCEPTION.getKey()) > eadFlat.getStatusValueInt()) {
                    eadFlat.setStatus(FlatStatusEnum.EXCEPTION.getKey());
                    eadFlat.setStatusValueInt(FlatStatusEnum.getValue(eadFlat.getStatus()));
                }
                break;
            case EadFlatConstant.DELIVERY:
                String d_eastCode = activity.getEastCode();
                if (EadFlatConstant.OK.equals(d_eastCode)){
                    eadFlat.setDelivery(setOccurtime(activity));
                    eadFlat.setEawbPrintcode(eawbPrintcode);
                    if (eadFlat.getStatusValueInt() == null || FlatStatusEnum.getValue(FlatStatusEnum.DELIVERY.getKey()) > eadFlat.getStatusValueInt()) {
                        eadFlat.setStatus(FlatStatusEnum.DELIVERY.getKey());
                        eadFlat.setStatusValueInt(FlatStatusEnum.getValue(eadFlat.getStatus()));
                    }
                }
                if (EadFlatConstant.D_DEF.equals(d_eastCode)){
                    eadFlat.setUndelivery(setOccurtime(activity));
                    eadFlat.setEawbPrintcode(eawbPrintcode);
                    if (eadFlat.getStatusValueInt() == null || FlatStatusEnum.getValue(FlatStatusEnum.UNDELIVERY.getKey()) > eadFlat.getStatusValueInt()) {
                        eadFlat.setStatus(FlatStatusEnum.UNDELIVERY.getKey());
                        eadFlat.setStatusValueInt(FlatStatusEnum.getValue(eadFlat.getStatus()));
                    }
                }
                break;
            case EadFlatConstant.INFOR:
                String i_eastCode = activity.getEastCode();
                if (EadFlatConstant.I_DECLARE.equals(i_eastCode)){
                    eadFlat.setDeclare(setOccurtime(activity));
                    eadFlat.setEawbPrintcode(eawbPrintcode);
                    if (eadFlat.getStatusValueInt() == null || FlatStatusEnum.getValue(FlatStatusEnum.DECLARE.getKey()) > eadFlat.getStatusValueInt()) {
                        eadFlat.setStatus(FlatStatusEnum.DECLARE.getKey());
                        eadFlat.setStatusValueInt(FlatStatusEnum.getValue(eadFlat.getStatus()));
                    }
                }
                break;
            case EadFlatConstant.INTERNATIONAL:
                String eastCode = activity.getEastCode();
                if (EadFlatConstant.S_ADC.equals(eastCode)){
                    eadFlat.setAdc(setOccurtime(activity));
                    eadFlat.setEawbPrintcode(eawbPrintcode);
                }
                if (EadFlatConstant.S_ASS.equals(eastCode)){
                    eadFlat.setAss(setOccurtime(activity));
                    eadFlat.setEawbPrintcode(eawbPrintcode);
                }
                if (EadFlatConstant.S_CPT.equals(eastCode)){
                    eadFlat.setCpt(setOccurtime(activity));
                    eadFlat.setEawbPrintcode(eawbPrintcode);
                }
                if (EadFlatConstant.S_ROE.equals(eastCode)){
                    eadFlat.setRoe(setOccurtime(activity));
                    eadFlat.setEawbPrintcode(eawbPrintcode);
                }
                if (EadFlatConstant.S_CLRDT.equals(eastCode)){
                    eadFlat.setClrdt(setOccurtime(activity));
                    eadFlat.setEawbPrintcode(eawbPrintcode);
                }
                if (EadFlatConstant.S_ATD.equals(eastCode)){
                    //环节时间同步到eawb_ead_flat_n表中的ATD字段
                    eadFlat.setAtd(setOccurtime(activity));
                    eadFlat.setEawbPrintcode(eawbPrintcode);
                }
                //ATD节点不需要改订单状态
               if (eadFlat.getStatusValueInt() == null || FlatStatusEnum.getValue(FlatStatusEnum.SENDING.getKey()) > eadFlat.getStatusValueInt()) {
                   eadFlat.setStatus(FlatStatusEnum.SENDING.getKey());
                   eadFlat.setStatusValueInt(FlatStatusEnum.getValue(eadFlat.getStatus()));
               }
                break;

        }

    }

    private Date setOccurtime(ExpressBusinessActivity activity){
        return activity.getEbaOccurtime() == null?activity.getEbaHandletime():activity.getEbaOccurtime();
    }
}
