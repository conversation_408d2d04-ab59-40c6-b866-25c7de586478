package com.sinoair.billing.timer;

import code.sinoair.core.support.cron.SinoairCron;
import com.sinoair.billing.core.util.*;
import com.sinoair.billing.domain.constant.CheckConstant;
import com.sinoair.billing.domain.constant.MailConstant;
import com.sinoair.billing.domain.constant.ReceiptRecordConstant;
import com.sinoair.billing.domain.model.billing.*;
import com.sinoair.billing.domain.vo.ceos.CeosEawb;
import com.sinoair.billing.domain.vo.query.CeosQuery;
import com.sinoair.billing.service.common.CommonService;
import com.sinoair.billing.service.order.OrderPoolService;
import com.sinoair.billing.service.syn.SynCeosEawbService;
import com.sinoair.billing.service.task.InsTaskService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

import static java.util.Comparator.comparing;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;

/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2018/7/4
 * @time: 10:39
 * @description: 每天同步Pre数据
 */
public class SynCeosPreTimer extends SinoairCron {

    private Logger logger = LoggerFactory.getLogger(SynCeosPreTimer.class);

    public SynCeosPreTimer(String cronName, long timeout, boolean isExit) {
        super(cronName, timeout, isExit);
    }

    @Override
    public void cronThreadRun() throws Exception {
        testValidate();
        logger.info("测试结束");
    }

    private void testValidate(){
//        CheckEawbNoService eawbNoService = SpringContextUtil.getBean("CheckEawbNoService");
        InsTaskService insTaskService = SpringContextUtil.getBean("InsTaskService");
        SynCeosEawbService synCeosEawbService = SpringContextUtil.getBean("SynCeosEawbService");
        OrderPoolService orderPoolService = SpringContextUtil.getBean("OrderPoolService");
        CommonService commonService = SpringContextUtil.getBean("CommonService");

        try {

            List<SettlementObject> soList = orderPoolService.selectSettlementList();
            List<String> mxxbKeyList = commonService.selectMxxbKeyList();

            InsTask insTask = insTaskService.selectByTaskType(CheckConstant.SYN_CEOS_EAWB_PRE);
            String taskStartDate = insTask.getTaskStartDate();
            String taskEndDate = insTask.getTaskEndDate();
            String taskFiled = insTask.getTaskFiled();
            if (StringUtil.isNotEmpty(taskFiled)){
                taskEndDate = DateUtil.getCurDate();
                taskStartDate = DateUtil.getPastDayByDate(DateUtil.str2date(taskEndDate,DateUtil.YYYYMMDD),Integer.parseInt(taskFiled));
            }

            CeosQuery query = new CeosQuery();
            query.setStartEbaHandletime(taskStartDate);
            query.setEndEbaHandletime(taskEndDate);
            query.setSoCode(insTask.getTaskSoCode());

            logger.info("开始查询 BeginDate："+query.getStartEbaHandletime() +" endDate:"+query.getEndEbaHandletime());

            int allCount = synCeosEawbService.countPreByHandleTime(query);
            if (allCount == 0){
                logger.info("查询数量为0");
                return;
            }
            logger.info(query.getStartEbaHandletime() +"订单总数："+allCount);
            int pageSize = CheckConstant.PAGE_SIZE;
            int threadNum = allCount / pageSize;
            if (allCount % pageSize != 0) {
                threadNum = threadNum + 1;
            }
            //所有线程阻塞，然后统一开始
    //        CountDownLatch latch = new CountDownLatch(threadNum);
            //删除socode为空的数据，避免出现主键重复情况
//            synCeosEawbService.deleteBySoCodeEmpty();
            for (int i = 1; i <= threadNum; i++) {

                CeosQuery param = new CeosQuery();
                param.setStartEbaHandletime(query.getStartEbaHandletime());
                param.setEndEbaHandletime(query.getEndEbaHandletime());
                param.setSoCode(query.getSoCode());
                param.setPageNum(i);
                param.setPageSize(pageSize);

                List<EawbPre> recordTmpList =synCeosEawbService.selectPreByHandleTime(param);
                logger.info("==去重开始.size=="+recordTmpList.size());
                List<EawbPre> recordList = recordTmpList.stream().collect(
                        collectingAndThen(
                                toCollection(() -> new TreeSet<>(comparing(n->n.getEawbPrintcode()))), ArrayList::new)
                );

                List<EawbPre> preTmpList = recordList.stream().filter(item-> (item.getEawbSyscode() != null && !CheckConstant.CN_SO_CODE.equals(item.getEawbSoCode()))).collect(Collectors.toList());

                logger.info(" 线程"+param.getPageNum()+"==去重结束.size=="+recordList.size());
                List<EawbPre> preList = new ArrayList<>();
                for (EawbPre tmp : preTmpList) {
                    if (CheckConstant.CN_SO_CODE.equals(tmp.getEawbSoCode())){
                        continue;
                    }
                    EawbPre pre = synCeosEawbService.selectByPrimaryKey(tmp.getEawbSyscode());
                    if (pre != null){
                        continue;
                    }
                    preList.add(tmp);
                }
                int length = 1000;
                logger.info(i+"==符合运单条件.size=="+preList.size());
                int size = preList.size();
                int count = size % length != 0 ? size / length + 1 : size / length;
                logger.info(i+"==count=="+count);
//                CountDownLatch latch = new CountDownLatch(count);
                for (int t = 0; t < count; t++) {
                    List<EawbPre> subList = preList.subList(t * length, ((length + t * length) < size ? (length + t * length) : size));

                    synCeosEawbService.synCeosPre(t,subList);
                }
//                latch.await();  //阻塞当前线程，直到latch的值为0
            }
        //多个线程都执行结束

//
            int hour = DateUtil.getHours();
            String startDate = DateUtil.getCurDate();
            String endDate = DateUtil.getPastDay(1);

            logger.info("多个线程都执行结束，可以做自己的事情了");

            insTask.setTaskStatus(ReceiptRecordConstant.TASK_PENDING);

//            insTask.setTaskStartDate(startDate);
//            insTask.setTaskEndDate(endDate);
            insTask.setTaskStartDate(taskEndDate);
            insTask.setTaskEndDate(DateUtil.getPastDayByDate(DateUtil.str2date(taskEndDate,DateUtil.YYYYMMDD),1));
            logger.info("下次开始编号："+insTask.getTaskStartDate());

            insTask.setTaskRemark(startDate+" "+hour+"点监控完成");
            insTask.setTaskHandletime(new Date());
            insTaskService.updateByPrimaryKeySelective(insTask);

        } catch (Exception e) {
            e.printStackTrace();
            logger.info("多线程执行中出错了，凉凉了！！！");
            MailUtil.postMail(MailConstant.MAIL_TO_ONE,MailConstant.MAIL_SUBJECT_CEOS_EAWB,e.getMessage());

        }

        logger.info("同步ceos-eawb结束：");
    }
}
