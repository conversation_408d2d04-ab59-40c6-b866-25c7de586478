package com.sinoair.billing.timer;

import code.sinoair.core.support.cron.SinoairCron;
import com.sinoair.billing.core.util.DateUtil;
import com.sinoair.billing.core.util.SpringContextUtil;
import com.sinoair.billing.domain.constant.CheckConstant;
import com.sinoair.billing.domain.constant.ReceiptRecordConstant;
import com.sinoair.billing.domain.model.billing.InsTask;
import com.sinoair.billing.domain.vo.query.CeosQuery;
import com.sinoair.billing.service.check.CheckInsEbaService;
import com.sinoair.billing.service.syn.SynReceiptRecordService;
import com.sinoair.billing.service.task.InsTaskService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;

/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2018/7/4
 * @time: 10:39
 * @description: 费用记录同步至正式表（非菜鸟）
 */
public class SynReceiptRecordTimer extends SinoairCron {

    private Logger logger = LoggerFactory.getLogger(SynReceiptRecordTimer.class);

    public SynReceiptRecordTimer(String cronName, long timeout, boolean isExit) {
        super(cronName, timeout, isExit);
    }

    @Override
    public void cronThreadRun() throws Exception {
        testValidate();
        logger.info("测试结束");
    }

    private void testValidate(){
        SynReceiptRecordService synReceiptRecordService = SpringContextUtil.getBean("SynReceiptRecordService");

        CeosQuery vo = new CeosQuery();
        int pageSize = CheckConstant.PAGE_SIZE_5W;
//        pageSize = 1000;
        for (int i=1;i<= 10;i++){
            vo.setPageNum(i);
            vo.setPageSize(pageSize);
            synReceiptRecordService.synReceiptRecord(vo);
        }


        logger.info("计费信息同步结束：");
    }
}
