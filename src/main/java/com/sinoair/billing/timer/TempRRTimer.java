package com.sinoair.billing.timer;

import code.sinoair.core.support.cron.SinoairCron;
import com.sinoair.billing.core.util.SpringContextUtil;
import com.sinoair.billing.domain.model.billing.TrapsRRConfig;
import com.sinoair.billing.service.temp.TempRRService;
import com.sinoair.billing.thread.TempRRThread;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.CountDownLatch;

/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2018/7/4
 * @time: 10:39
 * @description: 临时生成费用记录
 */
public class TempRRTimer extends SinoairCron {

    private Logger logger = LoggerFactory.getLogger(TempRRTimer.class);

    public TempRRTimer(String cronName, long timeout, boolean isExit) {
        super(cronName, timeout, isExit);
    }

    @Override
    public void cronThreadRun() throws Exception {
        testValidate();
        logger.info("测试结束");
    }

    private void testValidate(){

        TempRRService tempRRService = SpringContextUtil.getBean("TempRRService");
        TrapsRRConfig trapsRRConfig = tempRRService.selectById(1);
        int pageSize = trapsRRConfig.getPageSize();//

        int allCount = 100000;//
        if (allCount == 0){
            logger.info("查询数量为0");
        }
        int threadNum = allCount / pageSize;
        if (allCount % pageSize != 0) {
            threadNum = threadNum + 1;
        }
        logger.info("要生成的费用："+trapsRRConfig.getRrName());
//        tempRRService
        CountDownLatch latch = new CountDownLatch(trapsRRConfig.getEndPage() - trapsRRConfig.getStartPage() + 1);
        for (int i=trapsRRConfig.getStartPage();i<= trapsRRConfig.getEndPage();i++){

            new TempRRThread(i,pageSize,trapsRRConfig.getEadCode(),trapsRRConfig.getEastCode(),
                    trapsRRConfig.getRrName(),trapsRRConfig.getPrintcodeType(),trapsRRConfig.getSoType(),latch).start();

        }
        //多个线程都执行结束

        try {
            latch.await();  //阻塞当前线程，直到latch的值为0
            logger.info("多个线程都执行结束，可以做自己的事情了");

            int tempCount = tempRRService.countTemp();
            int startCount = trapsRRConfig.getStartPage() * trapsRRConfig.getPageSize();
            int endCount = trapsRRConfig.getEndPage() * trapsRRConfig.getPageSize();
            int startPage = trapsRRConfig.getStartPage();
            int endPage = trapsRRConfig.getEndPage();
            logger.info("tempCount:"+tempCount);
            logger.info("startCount:"+startCount);
            logger.info("endCount:"+endCount);
            logger.info("(tempCount - endCount):"+(tempCount - endCount));
//            logger.info("(endCount - startCount):"+((endPage - startPage)*trapsRRConfig.getPageSize()));
//            logger.info("endCount:"+endCount);
//            logger.info("计算结果:"+((tempCount - endCount) > (endPage - startPage)*trapsRRConfig.getPageSize()));
            if ((tempCount - endCount) > 0){
//            if ((tempCount - endCount) > (endPage - startPage)*trapsRRConfig.getPageSize()){
                Integer oldStartPage = trapsRRConfig.getStartPage();
                Integer oldEndPage = trapsRRConfig.getEndPage();
                trapsRRConfig.setStartPage(trapsRRConfig.getEndPage());
                trapsRRConfig.setEndPage(oldEndPage+(oldEndPage - oldStartPage));

            }else{
                trapsRRConfig.setStartPage(1);
                trapsRRConfig.setEndPage(20);
            }
            logger.info("getStartPage:"+trapsRRConfig.getStartPage());
            logger.info("getEndPage:"+trapsRRConfig.getEndPage());
            tempRRService.updateTrapsRRConfig(trapsRRConfig);


        } catch (InterruptedException e) {
            e.printStackTrace();
            logger.info("多线程执行中出错了，凉凉了！！！");
        }

        logger.info("监控CEOS环节结束：");
    }
}
