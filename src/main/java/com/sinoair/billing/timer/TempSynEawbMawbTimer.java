package com.sinoair.billing.timer;

import code.sinoair.core.support.cron.SinoairCron;
import com.sinoair.billing.core.util.DateUtil;
import com.sinoair.billing.core.util.MailUtil;
import com.sinoair.billing.core.util.SpringContextUtil;
import com.sinoair.billing.core.util.StringUtil;
import com.sinoair.billing.domain.constant.CheckConstant;
import com.sinoair.billing.domain.constant.MailConstant;
import com.sinoair.billing.domain.constant.ReceiptRecordConstant;
import com.sinoair.billing.domain.model.billing.ExpressAssignment;
import com.sinoair.billing.domain.model.billing.InsTask;
import com.sinoair.billing.domain.vo.query.CeosQuery;
import com.sinoair.billing.domain.vo.query.CommonQuery;
import com.sinoair.billing.service.syn.SynCeosEawbMawbService;
import com.sinoair.billing.service.task.InsTaskService;
import com.sinoair.billing.service.temp.TempService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author:
 * @date: 2018/7/4
 * @time: 10:39
 * @description: 临时处理主单eawb缺失数据
 */
public class TempSynEawbMawbTimer extends SinoairCron {

    private Logger logger = LoggerFactory.getLogger(TempSynEawbMawbTimer.class);

    public TempSynEawbMawbTimer(String cronName, long timeout, boolean isExit) {
        super(cronName, timeout, isExit);
    }

    @Override
    public void cronThreadRun() throws Exception {
        testValidate();
        logger.info("测试结束");
    }

    private void testValidate(){
        TempService tempService = SpringContextUtil.getBean("TempService");
        SynCeosEawbMawbService synCeosEawbMawbService = SpringContextUtil.getBean("SynCeosEawbMawbService");

        List<String> eaList = tempService.selectTempEa();
        if (eaList.size() == 0){
            logger.info("TempSynEawbMawbTimer 没有临时主单");
            return;
        }
        List<String> deleteList = new ArrayList<>();
        logger.info( " 主单数："+eaList.size());
        int seq = 0;
        for (String eaCode : eaList){

            int allCount = synCeosEawbMawbService.countCeosMawbEawbByEaCode(eaCode);
            seq++;
            logger.info("序号："+seq+" 主单："+eaCode +"订单总数："+allCount);
            if (allCount == 0){
                logger.info("查询数量为0");
                MailUtil.postMail(MailConstant.MAIL_TO_ONE,"主单【"+eaCode+"】 订单总数为0","");
                continue;
            }

            int pageSize = 200000;
            int threadNum = allCount / pageSize;
            if (allCount % pageSize != 0) {
                threadNum = threadNum + 1;
            }

            //所有线程阻塞，然后统一开始
//        CountDownLatch latch = new CountDownLatch(threadNum);

            for (int i = 1; i <= threadNum; i++) {

                CeosQuery param = new CeosQuery();

                param.setPageNum(i);
                param.setPageSize(pageSize);
                param.setEaCode(eaCode);

                synCeosEawbMawbService.synCeosEawbMawb(param);
                List<String> eadList = new ArrayList<>();
                eadList.add(eaCode);
                tempService.deleteBatchTempEa(eadList);
            }
            //deleteList.add(eaCode);

        }


        try {

            //tempService.deleteBatchTempEa(deleteList);

        } catch (Exception e) {
            e.printStackTrace();
            logger.info("多线程执行中出错了，凉凉了！！！");
        }

        logger.info("同步Ceos-eawb-mawb结束：");
    }
}
