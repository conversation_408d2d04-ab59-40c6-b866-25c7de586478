package com.sinoair.billing.timer;

import code.sinoair.core.support.cron.SinoairCron;
import com.sinoair.billing.core.util.SpringContextUtil;
import com.sinoair.billing.service.system.MainService;
import com.sinoair.billing.service.temp.TempRRService;
import com.sinoair.billing.service.temp.TempService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: 大雄
 * @date: 2018/7/4
 * @time: 10:39
 * @description: To change this template use File | Settings | File Templates.
 */
public class TestTimer extends SinoairCron {

    private Logger logger = LoggerFactory.getLogger(TestTimer.class);

    public TestTimer(String cronName, long timeout, boolean isExit) {
        super(cronName, timeout, isExit);
    }

    @Override
    public void cronThreadRun() throws Exception {
        testValidate();
        logger.info("测试结束");
    }

    private void testValidate(){
//        MainService mainService = SpringContextUtil.getBean("MainService");
//        String s = mainService.checkDb();

        TempRRService mainService = SpringContextUtil.getBean("TempRRService");

        List<String> eawbs = mainService.selectTempNhb();
        for (String s : eawbs){
            String res = mainService.handleSupplementaryBill(s);
            if ("success".equals(res)){
                mainService.deleteTempNhb(s);
            }
        }


//        List<String> eawbPrintcodes = new ArrayList<>();
//        eawbPrintcodes.add("SE101073471555");
//
//        for (String s : eawbPrintcodes){
//            mainService.handleMakeUpRR(s);
//        }
//        logger.info("数据库测试："+s);
    }
}
