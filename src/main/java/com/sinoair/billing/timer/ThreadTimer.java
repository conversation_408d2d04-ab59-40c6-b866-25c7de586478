package com.sinoair.billing.timer;

import code.sinoair.core.support.cron.SinoairCron;
import com.sinoair.billing.thread.ThreadTest;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: 大雄
 * @date: 2019-06-19
 * @time: 10:49
 * @description: To change this template use File | Settings | File Templates.
 */
public class ThreadTimer extends SinoairCron {

    public ThreadTimer(String cronName, long timeout, boolean isExit) {
        super(cronName, timeout, isExit);
    }

    @Override
    public void cronThreadRun() throws Exception {
        for (long i = 0; i < 10 ; i++) {
            new ThreadTest(i).start();
        }
    }
}
