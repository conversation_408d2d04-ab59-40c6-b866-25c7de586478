<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:context="http://www.springframework.org/schema/context"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:tx="http://www.springframework.org/schema/tx" xmlns:p="http://www.springframework.org/schema/p"
       xmlns:cache="http://www.springframework.org/schema/cache"
       xmlns:task="http://www.springframework.org/schema/task"
       xsi:schemaLocation="http://www.springframework.org/schema/context
    http://www.springframework.org/schema/context/spring-context.xsd
    http://www.springframework.org/schema/beans
    http://www.springframework.org/schema/beans/spring-beans.xsd
    http://www.springframework.org/schema/tx
    http://www.springframework.org/schema/tx/spring-tx.xsd
    http://www.springframework.org/schema/cache
    http://www.springframework.org/schema/cache/spring-cache.xsd
    http://www.springframework.org/schema/aop
    http://www.springframework.org/schema/aop/spring-aop.xsd
    http://www.springframework.org/schema/task
     http://www.springframework.org/schema/task/spring-task.xsd"
>

    <!-- 自动扫描包 ,将带有注解的类 纳入spring容器管理 -->
    <context:component-scan base-package="com.sinoair.billing"></context:component-scan>

    <!-- 引入配置文件 -->
    <bean id="propertyConfigurer" class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
        <property name="locations">
            <list>
                <value>classpath*:conf/jdbc.properties</value>
                <value>classpath*:conf/redis.properties</value>
            </list>
        </property>
    </bean>

    <!-- dataSource 配置 -->
    <bean id="DruidDataSource" class="com.alibaba.druid.pool.DruidDataSource" init-method="init" >
        <!-- 基本属性 url、user、password -->
        <property name="url" value="${jdbc.url}"/>
        <property name="username" value="${jdbc.username}"/>
        <property name="password" value="${jdbc.password}"/>

        <!-- 配置初始化大小、最小、最大 -->
        <property name="initialSize" value="${ds.initialSize}"/>
        <property name="minIdle" value="${ds.minIdle}"/>
        <property name="maxActive" value="${ds.maxActive}"/>

        <!-- 配置获取连接等待超时的时间 -->
        <property name="maxWait" value="${ds.maxWait}"/>

        <!-- 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒 -->
        <property name="timeBetweenEvictionRunsMillis" value="${ds.timeBetweenEvictionRunsMillis}"/>

        <!-- 配置一个连接在池中最小生存的时间，单位是毫秒 -->
        <property name="minEvictableIdleTimeMillis" value="${ds.minEvictableIdleTimeMillis}"/>

        <property name="validationQuery" value="SELECT 1 from dual"/>
        <property name="testWhileIdle" value="true"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="true"/>

        <!-- 打开PSCache，并且指定每个连接上PSCache的大小 -->
        <property name="poolPreparedStatements" value="false"/>
        <property name="maxPoolPreparedStatementPerConnectionSize" value="20"/>

        <!-- 配置监控统计拦截的filters -->
        <property name="filters" value="stat"/>
    </bean>

    <!-- mybatis文件配置，扫描所有mapper文件 -->
    <bean id="sqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean" p:dataSource-ref="DruidDataSource"
          p:configLocation="classpath:conf/mybatis-config.xml"
          p:mapperLocations="classpath:mapper/billing/*.xml"/>

    <bean id="sqlSessionTemplate" class="org.mybatis.spring.SqlSessionTemplate" scope="prototype">
        <constructor-arg index="0" ref="sqlSessionFactory" />
    </bean>

    <!-- spring与mybatis整合配置，扫描所有dao -->
    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer" p:basePackage="com.sinoair.billing.dao.billing"
          p:sqlSessionFactoryBeanName="sqlSessionFactory"/>

    <!-- 对dataSource 数据源进行事务管理 -->
    <bean id="transactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager"
          p:dataSource-ref="DruidDataSource"/>

    <!-- 事务管理 通知 -->
    <tx:advice id="txAdvice" transaction-manager="transactionManager">
        <tx:attributes>
            <!-- 对insert,update,delete 开头的方法进行事务管理,只要有异常就回滚 -->
            <tx:method name="insert*" propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
            <tx:method name="update*" propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
            <tx:method name="delete*" propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
            <tx:method name="*Transaction" propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
            <!-- select,count开头的方法,开启只读,提高数据库访问性能 -->
            <tx:method name="select*" read-only="true"/>
            <tx:method name="count*" read-only="true"/>
            <tx:method name="get*" read-only="true"/>
            <tx:method name="list*" read-only="true"/>
            <!-- 对其他方法 使用默认的事务管理 -->
            <tx:method name="*"/>
        </tx:attributes>
    </tx:advice>

    <!-- 事务 aop 配置 -->
    <aop:config>
        <aop:pointcut id="serviceMethods" expression="execution(* com.sinoair.billing.service..*.*(..))"/>
        <aop:advisor advice-ref="txAdvice" pointcut-ref="serviceMethods"/>
        <!-- 只读库只允许查询 -->
        <aop:advisor advice-ref="txAdvice2" pointcut-ref="serviceMethods"/>
    </aop:config>

    <!-- 配置使Spring采用CGLIB代理 -->
    <aop:aspectj-autoproxy proxy-target-class="true"/>

    <!-- 启用对事务注解的支持 -->
    <tx:annotation-driven transaction-manager="transactionManager"/>

    <!-- Cache配置 -->
    <cache:annotation-driven cache-manager="cacheManager"/>
    <bean id="ehCacheManagerFactory" class="org.springframework.cache.ehcache.EhCacheManagerFactoryBean"
          p:configLocation="classpath:conf/ehcache.xml"/>
    <bean id="cacheManager" class="org.springframework.cache.ehcache.EhCacheCacheManager"
          p:cacheManager-ref="ehCacheManagerFactory"/>

    <!-- 定时任务控制 -->
    <task:executor id="executor" pool-size="5"/>
    <task:scheduler id="scheduler" pool-size="10"/>
    <task:annotation-driven executor="executor" scheduler="scheduler"/>

    <!--=============================================-->
    <!-- 配置数据源 ceos  非主从   -->
    <bean id="dataSource2" class="com.alibaba.druid.pool.DruidDataSource" init-method="init">
    <!-- 基本属性 url、user、password -->
    <property name="url" value="${jdbc.url2}"/>
    <property name="username" value="${jdbc.username2}"/>
    <property name="password" value="${jdbc.password2}"/>

    <!-- 配置初始化大小、最小、最大 -->
    <property name="initialSize" value="${ds.initialSize}"/>
    <property name="minIdle" value="${ds.minIdle}"/>
    <property name="maxActive" value="${ds.maxActive}"/>

    <!-- 配置获取连接等待超时的时间 -->
    <property name="maxWait" value="${ds.maxWait}"/>

    <!-- 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒 -->
    <property name="timeBetweenEvictionRunsMillis" value="${ds.timeBetweenEvictionRunsMillis}"/>

    <!-- 配置一个连接在池中最小生存的时间，单位是毫秒 -->
    <property name="minEvictableIdleTimeMillis" value="${ds.minEvictableIdleTimeMillis}"/>

    <property name="validationQuery" value="SELECT 1 from dual"/>
    <property name="testWhileIdle" value="true"/>
    <property name="testOnBorrow" value="false"/>
    <property name="testOnReturn" value="false"/>

    <!-- 打开PSCache，并且指定每个连接上PSCache的大小 -->
    <property name="poolPreparedStatements" value="false"/>
    <property name="maxPoolPreparedStatementPerConnectionSize" value="20"/>

    <!-- 配置监控统计拦截的filters -->
    <property name="filters" value="stat"/>
    </bean>
    <!-- mybatis文件配置，扫描所有mapper文件 -->
    <bean id="sqlSessionFactory2" class="org.mybatis.spring.SqlSessionFactoryBean" p:dataSource-ref="dataSource2"
    p:configLocation="classpath:conf/mybatis-config.xml"
    p:mapperLocations="classpath:mapper/ceos/*.xml"/>
    <!-- spring与mybatis整合配置，扫描所有dao -->
    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer" p:basePackage="com.sinoair.billing.dao.ceos"
    p:sqlSessionFactoryBeanName="sqlSessionFactory2"/>
    <!-- 对dataSource 数据源进行事务管理 -->
    <bean id="transactionManager2" class="org.springframework.jdbc.datasource.DataSourceTransactionManager"
    p:dataSource-ref="dataSource2"/>
    <!-- 事务管理 通知 -->
    <tx:advice id="txAdvice2" transaction-manager="transactionManager2">
        <tx:attributes>
            <!-- 只读库只允许查询 -->
            <tx:method name="*" read-only="true"/>
        </tx:attributes>
    </tx:advice>

    <!--=============================================-->
    <!-- 配置数据源 ceosBI  非主从   -->
    <bean id="dataSource3" class="com.alibaba.druid.pool.DruidDataSource" init-method="init">
        <!-- 基本属性 url、user、password -->
        <property name="url" value="${jdbc.url3}"/>
        <property name="username" value="${jdbc.username3}"/>
        <property name="password" value="${jdbc.password3}"/>

        <!-- 配置初始化大小、最小、最大 -->
        <property name="initialSize" value="${ds.initialSize}"/>
        <property name="minIdle" value="${ds.minIdle}"/>
        <property name="maxActive" value="${ds.maxActive}"/>

        <!-- 配置获取连接等待超时的时间 -->
        <property name="maxWait" value="${ds.maxWait}"/>

        <!-- 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒 -->
        <property name="timeBetweenEvictionRunsMillis" value="${ds.timeBetweenEvictionRunsMillis}"/>

        <!-- 配置一个连接在池中最小生存的时间，单位是毫秒 -->
        <property name="minEvictableIdleTimeMillis" value="${ds.minEvictableIdleTimeMillis}"/>

        <property name="validationQuery" value="SELECT 1 from dual"/>
        <property name="testWhileIdle" value="true"/>
        <property name="testOnBorrow" value="false"/>
        <property name="testOnReturn" value="false"/>

        <!-- 打开PSCache，并且指定每个连接上PSCache的大小 -->
        <property name="poolPreparedStatements" value="false"/>
        <property name="maxPoolPreparedStatementPerConnectionSize" value="20"/>

        <!-- 配置监控统计拦截的filters -->
        <property name="filters" value="stat"/>
    </bean>
    <!-- mybatis文件配置，扫描所有mapper文件 -->
    <bean id="sqlSessionFactory3" class="org.mybatis.spring.SqlSessionFactoryBean" p:dataSource-ref="dataSource3"
          p:configLocation="classpath:conf/mybatis-config.xml"
          p:mapperLocations="classpath:mapper/ceosbi/*.xml"/>
    <!-- spring与mybatis整合配置，扫描所有dao -->
    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer" p:basePackage="com.sinoair.billing.dao.ceosbi"
          p:sqlSessionFactoryBeanName="sqlSessionFactory3"/>
    <!-- 对dataSource 数据源进行事务管理 -->
    <bean id="transactionManager3" class="org.springframework.jdbc.datasource.DataSourceTransactionManager"
          p:dataSource-ref="dataSource3"/>


    <!-- redis连接池的配置 -->
    <bean id="jedisPoolConfig" class="redis.clients.jedis.JedisPoolConfig">
        <property name="maxTotal" value="500" />
        <property name="maxIdle" value="50"/>
        <property name="minIdle" value="20"/>
        <property name="maxWaitMillis" value="5000" />
        <property name="testWhileIdle" value="true"/>
        <property name="minEvictableIdleTimeMillis" value="240000"/>
        <property name="numTestsPerEvictionRun" value="3"/>
        <property name="timeBetweenEvictionRunsMillis" value="30000"/>
    </bean>

    <!-- 工厂类配置 -->
    <bean id="jedisConnectionFactory"
          class="org.springframework.data.redis.connection.jedis.JedisConnectionFactory">
        <property name="hostName" value="${redis.ip}" />
        <property name="port" value="${redis.port}" />
        <property name="password" value="${redis.password}" />
        <property name="database" value="${redis.database}" />
        <property name="poolConfig" ref="jedisPoolConfig" />
        <property name="timeout" value="15000"/>
        <property name="usePool" value="true"/>
    </bean>

    <!-- redisTemplate配置 -->
    <bean id="redisTemplate" class="org.springframework.data.redis.core.RedisTemplate">
        <property name="connectionFactory"   ref="jedisConnectionFactory" />
        <property name="keySerializer">
            <bean class="org.springframework.data.redis.serializer.StringRedisSerializer" />
        </property>
        <property name="valueSerializer">
            <!--<bean class="org.springframework.data.redis.serializer.JdkSerializationRedisSerializer"/>-->
            <bean class="com.sinoair.billing.core.config.serializer.JsonRedisSerializer">
                <constructor-arg index="0" value="java.lang.Object"></constructor-arg>
            </bean>
        </property>
        <property name="hashKeySerializer">
            <bean class="org.springframework.data.redis.serializer.StringRedisSerializer"/>
        </property>
        <property name="hashValueSerializer">
            <bean class="com.sinoair.billing.core.config.serializer.JsonRedisSerializer">
                <constructor-arg index="0" value="java.lang.Object"></constructor-arg>
            </bean>
        </property>
    </bean>
</beans>