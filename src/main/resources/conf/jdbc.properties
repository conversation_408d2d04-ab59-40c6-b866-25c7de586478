jdbc.driver=oracle.jdbc.driver.OracleDriver

#\u6570\u636E\u6E901 billing
jdbc.url=${jdbc.url}
jdbc.username=${jdbc.username}
jdbc.password=${jdbc.password}

#\u6570\u636E\u6E902 ceos
jdbc.url2=${jdbc2.url2}
jdbc.username2=${jdbc2.username2}
jdbc.password2=${jdbc2.password2}

jdbc.url3=${jdbc3.url3}
jdbc.username3=${jdbc3.username3}
jdbc.password3=${jdbc3.password3}

##DataSource Global Setting

#\u914D\u7F6E\u521D\u59CB\u5316\u5927\u5C0F\u3001\u6700\u5C0F\u3001\u6700\u5927
ds.initialSize=1
ds.minIdle=1
ds.maxActive=200

#\u914D\u7F6E\u83B7\u53D6\u8FDE\u63A5\u7B49\u5F85\u8D85\u65F6\u7684\u65F6\u95F4
ds.maxWait=60000

#\u914D\u7F6E\u95F4\u9694\u591A\u4E45\u624D\u8FDB\u884C\u4E00\u6B21\u68C0\u6D4B\uFF0C\u68C0\u6D4B\u9700\u8981\u5173\u95ED\u7684\u7A7A\u95F2\u8FDE\u63A5\uFF0C\u5355\u4F4D\u662F\u6BEB\u79D2
ds.timeBetweenEvictionRunsMillis=60000

#\u914D\u7F6E\u4E00\u4E2A\u8FDE\u63A5\u5728\u6C60\u4E2D\u6700\u5C0F\u751F\u5B58\u7684\u65F6\u95F4\uFF0C\u5355\u4F4D\u662F\u6BEB\u79D2
ds.minEvictableIdleTimeMillis=300000
