<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.billing.dao.billing.AppDebitManifestMapper">
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.AppDebitManifest">
    <id column="DM_ID" jdbcType="DECIMAL" property="dmId" />
    <result column="COMPANY_ID" jdbcType="VARCHAR" property="companyId" />
    <result column="SO_CODE" jdbcType="VARCHAR" property="soCode" />
    <result column="CT_CODE" jdbcType="VARCHAR" property="ctCode" />
    <result column="DM_CURRENCYRATE" jdbcType="DECIMAL" property="dmCurrencyrate" />
    <result column="DM_TOTALRMB" jdbcType="DECIMAL" property="dmTotalrmb" />
    <result column="DM_TOTALFC" jdbcType="DECIMAL" property="dmTotalfc" />
    <result column="SO_TAX" jdbcType="DECIMAL" property="soTax" />
    <result column="TAX_AMOUNT" jdbcType="DECIMAL" property="taxAmount" />
    <result column="NOTAX_AMOUNT" jdbcType="DECIMAL" property="notaxAmount" />
    <result column="TAX_AMOUNT_FC" jdbcType="DECIMAL" property="taxAmountFc" />
    <result column="NOTAX_AMOUNT_FC" jdbcType="DECIMAL" property="notaxAmountFc" />
    <result column="INVOICE_ID" jdbcType="VARCHAR" property="invoiceId" />
    <result column="DM_USER_ID" jdbcType="DECIMAL" property="dmUserId" />
    <result column="DM_STATUS" jdbcType="VARCHAR" property="dmStatus" />
    <result column="DM_CREATE_TIME" jdbcType="TIMESTAMP" property="dmCreateTime" />
    <result column="DM_HANDLE_TIME" jdbcType="TIMESTAMP" property="dmHandleTime" />
    <result column="DM_DIRTY" jdbcType="VARCHAR" property="dmDirty" />
    <result column="DM_DIRTY_TIME" jdbcType="TIMESTAMP" property="dmDirtyTime" />
    <result column="DM_CODE" jdbcType="VARCHAR" property="dmCode" />
    <result column="DM_PLAN_AMOUNT" jdbcType="DECIMAL" property="dmPlanAmount" />
    <result column="DM_START_TIME" jdbcType="TIMESTAMP" property="dmStartTime" />
    <result column="DM_END_TIME" jdbcType="TIMESTAMP" property="dmEndTime" />
    <result column="DM_TOTAL_PIECES" jdbcType="DECIMAL" property="dmTotalPieces" />
    <result column="DM_TOTAL_WEIGHT" jdbcType="DECIMAL" property="dmTotalWeight" />
    <result column="DM_REMARK" jdbcType="VARCHAR" property="dmRemark" />
    <result column="DM_TYPE" jdbcType="VARCHAR" property="dmType" />
    <result column="DM_DIVIDE_STATUS" jdbcType="VARCHAR" property="dmDivideStatus" />
    <result column="DM_EMAIL_STATUS" jdbcType="VARCHAR" property="dmEmailStatus" />
    <result column="ORDER_ID" jdbcType="DECIMAL" property="orderId" />
    <result column="UNIONID" jdbcType="VARCHAR" property="unionid" />
    <result column="COUPON_CODE" jdbcType="VARCHAR" property="couponCode" />
    <result column="COUPON_AMOUNT" jdbcType="DECIMAL" property="couponAmount" />
    <result column="DM_TRANSACTION_ID" jdbcType="VARCHAR" property="dmTransactionId" />
    <result column="OUT_TRANSACTION_ID" jdbcType="VARCHAR" property="outTransactionId" />
    <result column="SYN_STATUS" jdbcType="VARCHAR" property="synStatus" />
  </resultMap>
  <sql id="Base_Column_List">
    DM_ID, COMPANY_ID, SO_CODE, CT_CODE, DM_CURRENCYRATE, DM_TOTALRMB, DM_TOTALFC, SO_TAX, 
    TAX_AMOUNT, NOTAX_AMOUNT, TAX_AMOUNT_FC, NOTAX_AMOUNT_FC, INVOICE_ID, DM_USER_ID, 
    DM_STATUS, DM_CREATE_TIME, DM_HANDLE_TIME, DM_DIRTY, DM_DIRTY_TIME, DM_CODE, DM_PLAN_AMOUNT, 
    DM_START_TIME, DM_END_TIME, DM_TOTAL_PIECES, DM_TOTAL_WEIGHT, DM_REMARK, DM_TYPE, 
    DM_DIVIDE_STATUS, DM_EMAIL_STATUS, ORDER_ID, UNIONID, COUPON_CODE, COUPON_AMOUNT, 
    DM_TRANSACTION_ID, OUT_TRANSACTION_ID, SYN_STATUS
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from APP_DEBIT_MANIFEST
    where DM_ID = #{dmId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from APP_DEBIT_MANIFEST
    where DM_ID = #{dmId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.AppDebitManifest">
    insert into APP_DEBIT_MANIFEST (DM_ID, COMPANY_ID, SO_CODE, 
      CT_CODE, DM_CURRENCYRATE, DM_TOTALRMB, 
      DM_TOTALFC, SO_TAX, TAX_AMOUNT, 
      NOTAX_AMOUNT, TAX_AMOUNT_FC, NOTAX_AMOUNT_FC, 
      INVOICE_ID, DM_USER_ID, DM_STATUS, 
      DM_CREATE_TIME, DM_HANDLE_TIME, DM_DIRTY, 
      DM_DIRTY_TIME, DM_CODE, DM_PLAN_AMOUNT, 
      DM_START_TIME, DM_END_TIME, DM_TOTAL_PIECES, 
      DM_TOTAL_WEIGHT, DM_REMARK, DM_TYPE, 
      DM_DIVIDE_STATUS, DM_EMAIL_STATUS, ORDER_ID, 
      UNIONID, COUPON_CODE, COUPON_AMOUNT, 
      DM_TRANSACTION_ID, OUT_TRANSACTION_ID, SYN_STATUS
      )
    values (#{dmId,jdbcType=DECIMAL}, #{companyId,jdbcType=VARCHAR}, #{soCode,jdbcType=VARCHAR}, 
      #{ctCode,jdbcType=VARCHAR}, #{dmCurrencyrate,jdbcType=DECIMAL}, #{dmTotalrmb,jdbcType=DECIMAL}, 
      #{dmTotalfc,jdbcType=DECIMAL}, #{soTax,jdbcType=DECIMAL}, #{taxAmount,jdbcType=DECIMAL}, 
      #{notaxAmount,jdbcType=DECIMAL}, #{taxAmountFc,jdbcType=DECIMAL}, #{notaxAmountFc,jdbcType=DECIMAL}, 
      #{invoiceId,jdbcType=VARCHAR}, #{dmUserId,jdbcType=DECIMAL}, #{dmStatus,jdbcType=VARCHAR}, 
      #{dmCreateTime,jdbcType=TIMESTAMP}, #{dmHandleTime,jdbcType=TIMESTAMP}, #{dmDirty,jdbcType=VARCHAR}, 
      #{dmDirtyTime,jdbcType=TIMESTAMP}, #{dmCode,jdbcType=VARCHAR}, #{dmPlanAmount,jdbcType=DECIMAL}, 
      #{dmStartTime,jdbcType=TIMESTAMP}, #{dmEndTime,jdbcType=TIMESTAMP}, #{dmTotalPieces,jdbcType=DECIMAL}, 
      #{dmTotalWeight,jdbcType=DECIMAL}, #{dmRemark,jdbcType=VARCHAR}, #{dmType,jdbcType=VARCHAR}, 
      #{dmDivideStatus,jdbcType=VARCHAR}, #{dmEmailStatus,jdbcType=VARCHAR}, #{orderId,jdbcType=DECIMAL}, 
      #{unionid,jdbcType=VARCHAR}, #{couponCode,jdbcType=VARCHAR}, #{couponAmount,jdbcType=DECIMAL}, 
      #{dmTransactionId,jdbcType=VARCHAR}, #{outTransactionId,jdbcType=VARCHAR}, #{synStatus,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.AppDebitManifest">
    insert into APP_DEBIT_MANIFEST
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="dmId != null">
        DM_ID,
      </if>
      <if test="companyId != null">
        COMPANY_ID,
      </if>
      <if test="soCode != null">
        SO_CODE,
      </if>
      <if test="ctCode != null">
        CT_CODE,
      </if>
      <if test="dmCurrencyrate != null">
        DM_CURRENCYRATE,
      </if>
      <if test="dmTotalrmb != null">
        DM_TOTALRMB,
      </if>
      <if test="dmTotalfc != null">
        DM_TOTALFC,
      </if>
      <if test="soTax != null">
        SO_TAX,
      </if>
      <if test="taxAmount != null">
        TAX_AMOUNT,
      </if>
      <if test="notaxAmount != null">
        NOTAX_AMOUNT,
      </if>
      <if test="taxAmountFc != null">
        TAX_AMOUNT_FC,
      </if>
      <if test="notaxAmountFc != null">
        NOTAX_AMOUNT_FC,
      </if>
      <if test="invoiceId != null">
        INVOICE_ID,
      </if>
      <if test="dmUserId != null">
        DM_USER_ID,
      </if>
      <if test="dmStatus != null">
        DM_STATUS,
      </if>
      <if test="dmCreateTime != null">
        DM_CREATE_TIME,
      </if>
      <if test="dmHandleTime != null">
        DM_HANDLE_TIME,
      </if>
      <if test="dmDirty != null">
        DM_DIRTY,
      </if>
      <if test="dmDirtyTime != null">
        DM_DIRTY_TIME,
      </if>
      <if test="dmCode != null">
        DM_CODE,
      </if>
      <if test="dmPlanAmount != null">
        DM_PLAN_AMOUNT,
      </if>
      <if test="dmStartTime != null">
        DM_START_TIME,
      </if>
      <if test="dmEndTime != null">
        DM_END_TIME,
      </if>
      <if test="dmTotalPieces != null">
        DM_TOTAL_PIECES,
      </if>
      <if test="dmTotalWeight != null">
        DM_TOTAL_WEIGHT,
      </if>
      <if test="dmRemark != null">
        DM_REMARK,
      </if>
      <if test="dmType != null">
        DM_TYPE,
      </if>
      <if test="dmDivideStatus != null">
        DM_DIVIDE_STATUS,
      </if>
      <if test="dmEmailStatus != null">
        DM_EMAIL_STATUS,
      </if>
      <if test="orderId != null">
        ORDER_ID,
      </if>
      <if test="unionid != null">
        UNIONID,
      </if>
      <if test="couponCode != null">
        COUPON_CODE,
      </if>
      <if test="couponAmount != null">
        COUPON_AMOUNT,
      </if>
      <if test="dmTransactionId != null">
        DM_TRANSACTION_ID,
      </if>
      <if test="outTransactionId != null">
        OUT_TRANSACTION_ID,
      </if>
      <if test="synStatus != null">
        SYN_STATUS,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="dmId != null">
        #{dmId,jdbcType=DECIMAL},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="soCode != null">
        #{soCode,jdbcType=VARCHAR},
      </if>
      <if test="ctCode != null">
        #{ctCode,jdbcType=VARCHAR},
      </if>
      <if test="dmCurrencyrate != null">
        #{dmCurrencyrate,jdbcType=DECIMAL},
      </if>
      <if test="dmTotalrmb != null">
        #{dmTotalrmb,jdbcType=DECIMAL},
      </if>
      <if test="dmTotalfc != null">
        #{dmTotalfc,jdbcType=DECIMAL},
      </if>
      <if test="soTax != null">
        #{soTax,jdbcType=DECIMAL},
      </if>
      <if test="taxAmount != null">
        #{taxAmount,jdbcType=DECIMAL},
      </if>
      <if test="notaxAmount != null">
        #{notaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="taxAmountFc != null">
        #{taxAmountFc,jdbcType=DECIMAL},
      </if>
      <if test="notaxAmountFc != null">
        #{notaxAmountFc,jdbcType=DECIMAL},
      </if>
      <if test="invoiceId != null">
        #{invoiceId,jdbcType=VARCHAR},
      </if>
      <if test="dmUserId != null">
        #{dmUserId,jdbcType=DECIMAL},
      </if>
      <if test="dmStatus != null">
        #{dmStatus,jdbcType=VARCHAR},
      </if>
      <if test="dmCreateTime != null">
        #{dmCreateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dmHandleTime != null">
        #{dmHandleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dmDirty != null">
        #{dmDirty,jdbcType=VARCHAR},
      </if>
      <if test="dmDirtyTime != null">
        #{dmDirtyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dmCode != null">
        #{dmCode,jdbcType=VARCHAR},
      </if>
      <if test="dmPlanAmount != null">
        #{dmPlanAmount,jdbcType=DECIMAL},
      </if>
      <if test="dmStartTime != null">
        #{dmStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dmEndTime != null">
        #{dmEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dmTotalPieces != null">
        #{dmTotalPieces,jdbcType=DECIMAL},
      </if>
      <if test="dmTotalWeight != null">
        #{dmTotalWeight,jdbcType=DECIMAL},
      </if>
      <if test="dmRemark != null">
        #{dmRemark,jdbcType=VARCHAR},
      </if>
      <if test="dmType != null">
        #{dmType,jdbcType=VARCHAR},
      </if>
      <if test="dmDivideStatus != null">
        #{dmDivideStatus,jdbcType=VARCHAR},
      </if>
      <if test="dmEmailStatus != null">
        #{dmEmailStatus,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=DECIMAL},
      </if>
      <if test="unionid != null">
        #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="couponCode != null">
        #{couponCode,jdbcType=VARCHAR},
      </if>
      <if test="couponAmount != null">
        #{couponAmount,jdbcType=DECIMAL},
      </if>
      <if test="dmTransactionId != null">
        #{dmTransactionId,jdbcType=VARCHAR},
      </if>
      <if test="outTransactionId != null">
        #{outTransactionId,jdbcType=VARCHAR},
      </if>
      <if test="synStatus != null">
        #{synStatus,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.AppDebitManifest">
    update APP_DEBIT_MANIFEST
    <set>
      <if test="companyId != null">
        COMPANY_ID = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="soCode != null">
        SO_CODE = #{soCode,jdbcType=VARCHAR},
      </if>
      <if test="ctCode != null">
        CT_CODE = #{ctCode,jdbcType=VARCHAR},
      </if>
      <if test="dmCurrencyrate != null">
        DM_CURRENCYRATE = #{dmCurrencyrate,jdbcType=DECIMAL},
      </if>
      <if test="dmTotalrmb != null">
        DM_TOTALRMB = #{dmTotalrmb,jdbcType=DECIMAL},
      </if>
      <if test="dmTotalfc != null">
        DM_TOTALFC = #{dmTotalfc,jdbcType=DECIMAL},
      </if>
      <if test="soTax != null">
        SO_TAX = #{soTax,jdbcType=DECIMAL},
      </if>
      <if test="taxAmount != null">
        TAX_AMOUNT = #{taxAmount,jdbcType=DECIMAL},
      </if>
      <if test="notaxAmount != null">
        NOTAX_AMOUNT = #{notaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="taxAmountFc != null">
        TAX_AMOUNT_FC = #{taxAmountFc,jdbcType=DECIMAL},
      </if>
      <if test="notaxAmountFc != null">
        NOTAX_AMOUNT_FC = #{notaxAmountFc,jdbcType=DECIMAL},
      </if>
      <if test="invoiceId != null">
        INVOICE_ID = #{invoiceId,jdbcType=VARCHAR},
      </if>
      <if test="dmUserId != null">
        DM_USER_ID = #{dmUserId,jdbcType=DECIMAL},
      </if>
      <if test="dmStatus != null">
        DM_STATUS = #{dmStatus,jdbcType=VARCHAR},
      </if>
      <if test="dmCreateTime != null">
        DM_CREATE_TIME = #{dmCreateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dmHandleTime != null">
        DM_HANDLE_TIME = #{dmHandleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dmDirty != null">
        DM_DIRTY = #{dmDirty,jdbcType=VARCHAR},
      </if>
      <if test="dmDirtyTime != null">
        DM_DIRTY_TIME = #{dmDirtyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dmCode != null">
        DM_CODE = #{dmCode,jdbcType=VARCHAR},
      </if>
      <if test="dmPlanAmount != null">
        DM_PLAN_AMOUNT = #{dmPlanAmount,jdbcType=DECIMAL},
      </if>
      <if test="dmStartTime != null">
        DM_START_TIME = #{dmStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dmEndTime != null">
        DM_END_TIME = #{dmEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dmTotalPieces != null">
        DM_TOTAL_PIECES = #{dmTotalPieces,jdbcType=DECIMAL},
      </if>
      <if test="dmTotalWeight != null">
        DM_TOTAL_WEIGHT = #{dmTotalWeight,jdbcType=DECIMAL},
      </if>
      <if test="dmRemark != null">
        DM_REMARK = #{dmRemark,jdbcType=VARCHAR},
      </if>
      <if test="dmType != null">
        DM_TYPE = #{dmType,jdbcType=VARCHAR},
      </if>
      <if test="dmDivideStatus != null">
        DM_DIVIDE_STATUS = #{dmDivideStatus,jdbcType=VARCHAR},
      </if>
      <if test="dmEmailStatus != null">
        DM_EMAIL_STATUS = #{dmEmailStatus,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        ORDER_ID = #{orderId,jdbcType=DECIMAL},
      </if>
      <if test="unionid != null">
        UNIONID = #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="couponCode != null">
        COUPON_CODE = #{couponCode,jdbcType=VARCHAR},
      </if>
      <if test="couponAmount != null">
        COUPON_AMOUNT = #{couponAmount,jdbcType=DECIMAL},
      </if>
      <if test="dmTransactionId != null">
        DM_TRANSACTION_ID = #{dmTransactionId,jdbcType=VARCHAR},
      </if>
      <if test="outTransactionId != null">
        OUT_TRANSACTION_ID = #{outTransactionId,jdbcType=VARCHAR},
      </if>
      <if test="synStatus != null">
        SYN_STATUS = #{synStatus,jdbcType=VARCHAR},
      </if>
    </set>
    where DM_ID = #{dmId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.AppDebitManifest">
    update APP_DEBIT_MANIFEST
    set COMPANY_ID = #{companyId,jdbcType=VARCHAR},
      SO_CODE = #{soCode,jdbcType=VARCHAR},
      CT_CODE = #{ctCode,jdbcType=VARCHAR},
      DM_CURRENCYRATE = #{dmCurrencyrate,jdbcType=DECIMAL},
      DM_TOTALRMB = #{dmTotalrmb,jdbcType=DECIMAL},
      DM_TOTALFC = #{dmTotalfc,jdbcType=DECIMAL},
      SO_TAX = #{soTax,jdbcType=DECIMAL},
      TAX_AMOUNT = #{taxAmount,jdbcType=DECIMAL},
      NOTAX_AMOUNT = #{notaxAmount,jdbcType=DECIMAL},
      TAX_AMOUNT_FC = #{taxAmountFc,jdbcType=DECIMAL},
      NOTAX_AMOUNT_FC = #{notaxAmountFc,jdbcType=DECIMAL},
      INVOICE_ID = #{invoiceId,jdbcType=VARCHAR},
      DM_USER_ID = #{dmUserId,jdbcType=DECIMAL},
      DM_STATUS = #{dmStatus,jdbcType=VARCHAR},
      DM_CREATE_TIME = #{dmCreateTime,jdbcType=TIMESTAMP},
      DM_HANDLE_TIME = #{dmHandleTime,jdbcType=TIMESTAMP},
      DM_DIRTY = #{dmDirty,jdbcType=VARCHAR},
      DM_DIRTY_TIME = #{dmDirtyTime,jdbcType=TIMESTAMP},
      DM_CODE = #{dmCode,jdbcType=VARCHAR},
      DM_PLAN_AMOUNT = #{dmPlanAmount,jdbcType=DECIMAL},
      DM_START_TIME = #{dmStartTime,jdbcType=TIMESTAMP},
      DM_END_TIME = #{dmEndTime,jdbcType=TIMESTAMP},
      DM_TOTAL_PIECES = #{dmTotalPieces,jdbcType=DECIMAL},
      DM_TOTAL_WEIGHT = #{dmTotalWeight,jdbcType=DECIMAL},
      DM_REMARK = #{dmRemark,jdbcType=VARCHAR},
      DM_TYPE = #{dmType,jdbcType=VARCHAR},
      DM_DIVIDE_STATUS = #{dmDivideStatus,jdbcType=VARCHAR},
      DM_EMAIL_STATUS = #{dmEmailStatus,jdbcType=VARCHAR},
      ORDER_ID = #{orderId,jdbcType=DECIMAL},
      UNIONID = #{unionid,jdbcType=VARCHAR},
      COUPON_CODE = #{couponCode,jdbcType=VARCHAR},
      COUPON_AMOUNT = #{couponAmount,jdbcType=DECIMAL},
      DM_TRANSACTION_ID = #{dmTransactionId,jdbcType=VARCHAR},
      OUT_TRANSACTION_ID = #{outTransactionId,jdbcType=VARCHAR},
      SYN_STATUS = #{synStatus,jdbcType=VARCHAR}
    where DM_ID = #{dmId,jdbcType=DECIMAL}
  </update>
  <select id="selectStatusIsN" resultMap="BaseResultMap">
    select * from APP_DEBIT_MANIFEST where SYN_STATUS='N'
  </select>
  <update id="batchUpdateById">
     update APP_DEBIT_MANIFEST set SYN_STATUS='Y'
     where DM_ID in
    <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
       #{item,jdbcType=DECIMAL}
    </foreach>
  </update>
</mapper>