<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.billing.dao.billing.AppOrderEawbMapper">
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.AppOrderEawb">
    <id column="ID" jdbcType="DECIMAL" property="id" />
    <result column="ORDER_ID" jdbcType="DECIMAL" property="orderId" />
    <result column="EAWB_PRINTCODE" jdbcType="VARCHAR" property="eawbPrintcode" />
    <result column="EAWB_REFERENCE2" jdbcType="VARCHAR" property="eawbReference2" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="EAWB_REFERENCE1" jdbcType="VARCHAR" property="eawbReference1" />
    <result column="LABEL_URL" jdbcType="VARCHAR" property="labelUrl" />
    <result column="STATUS" jdbcType="VARCHAR" property="status" />
    <result column="ACTUAL_WEIGHT" jdbcType="DECIMAL" property="actualWeight" />
    <result column="ACTUAL_VOLUME" jdbcType="DECIMAL" property="actualVolume" />
    <result column="CHARGEABLE_WEIGHT" jdbcType="DECIMAL" property="chargeableWeight" />
    <result column="REPORT_WEIGHT" jdbcType="DECIMAL" property="reportWeight" />
    <result column="REPORT_LENGTH" jdbcType="DECIMAL" property="reportLength" />
    <result column="REPORT_WIDTH" jdbcType="DECIMAL" property="reportWidth" />
    <result column="REPORT_HIGHT" jdbcType="DECIMAL" property="reportHight" />
    <result column="IS_ELECTRICI" jdbcType="VARCHAR" property="isElectrici" />
    <result column="IS_MAGNETISM" jdbcType="VARCHAR" property="isMagnetism" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, ORDER_ID, EAWB_PRINTCODE, EAWB_REFERENCE2, REMARK, EAWB_REFERENCE1, LABEL_URL, 
    STATUS, ACTUAL_WEIGHT, ACTUAL_VOLUME, CHARGEABLE_WEIGHT, REPORT_WEIGHT, REPORT_LENGTH, 
    REPORT_WIDTH, REPORT_HIGHT, IS_ELECTRICI, IS_MAGNETISM
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from APP_ORDER_EAWB
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from APP_ORDER_EAWB
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.AppOrderEawb">
    insert into APP_ORDER_EAWB (ID, ORDER_ID, EAWB_PRINTCODE, 
      EAWB_REFERENCE2, REMARK, EAWB_REFERENCE1, 
      LABEL_URL, STATUS, ACTUAL_WEIGHT, 
      ACTUAL_VOLUME, CHARGEABLE_WEIGHT, REPORT_WEIGHT, 
      REPORT_LENGTH, REPORT_WIDTH, REPORT_HIGHT, 
      IS_ELECTRICI, IS_MAGNETISM)
    values (#{id,jdbcType=DECIMAL}, #{orderId,jdbcType=DECIMAL}, #{eawbPrintcode,jdbcType=VARCHAR}, 
      #{eawbReference2,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{eawbReference1,jdbcType=VARCHAR}, 
      #{labelUrl,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{actualWeight,jdbcType=DECIMAL}, 
      #{actualVolume,jdbcType=DECIMAL}, #{chargeableWeight,jdbcType=DECIMAL}, #{reportWeight,jdbcType=DECIMAL}, 
      #{reportLength,jdbcType=DECIMAL}, #{reportWidth,jdbcType=DECIMAL}, #{reportHight,jdbcType=DECIMAL}, 
      #{isElectrici,jdbcType=VARCHAR}, #{isMagnetism,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.AppOrderEawb">
    insert into APP_ORDER_EAWB
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="orderId != null">
        ORDER_ID,
      </if>
      <if test="eawbPrintcode != null">
        EAWB_PRINTCODE,
      </if>
      <if test="eawbReference2 != null">
        EAWB_REFERENCE2,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="eawbReference1 != null">
        EAWB_REFERENCE1,
      </if>
      <if test="labelUrl != null">
        LABEL_URL,
      </if>
      <if test="status != null">
        STATUS,
      </if>
      <if test="actualWeight != null">
        ACTUAL_WEIGHT,
      </if>
      <if test="actualVolume != null">
        ACTUAL_VOLUME,
      </if>
      <if test="chargeableWeight != null">
        CHARGEABLE_WEIGHT,
      </if>
      <if test="reportWeight != null">
        REPORT_WEIGHT,
      </if>
      <if test="reportLength != null">
        REPORT_LENGTH,
      </if>
      <if test="reportWidth != null">
        REPORT_WIDTH,
      </if>
      <if test="reportHight != null">
        REPORT_HIGHT,
      </if>
      <if test="isElectrici != null">
        IS_ELECTRICI,
      </if>
      <if test="isMagnetism != null">
        IS_MAGNETISM,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=DECIMAL},
      </if>
      <if test="eawbPrintcode != null">
        #{eawbPrintcode,jdbcType=VARCHAR},
      </if>
      <if test="eawbReference2 != null">
        #{eawbReference2,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="eawbReference1 != null">
        #{eawbReference1,jdbcType=VARCHAR},
      </if>
      <if test="labelUrl != null">
        #{labelUrl,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="actualWeight != null">
        #{actualWeight,jdbcType=DECIMAL},
      </if>
      <if test="actualVolume != null">
        #{actualVolume,jdbcType=DECIMAL},
      </if>
      <if test="chargeableWeight != null">
        #{chargeableWeight,jdbcType=DECIMAL},
      </if>
      <if test="reportWeight != null">
        #{reportWeight,jdbcType=DECIMAL},
      </if>
      <if test="reportLength != null">
        #{reportLength,jdbcType=DECIMAL},
      </if>
      <if test="reportWidth != null">
        #{reportWidth,jdbcType=DECIMAL},
      </if>
      <if test="reportHight != null">
        #{reportHight,jdbcType=DECIMAL},
      </if>
      <if test="isElectrici != null">
        #{isElectrici,jdbcType=VARCHAR},
      </if>
      <if test="isMagnetism != null">
        #{isMagnetism,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.AppOrderEawb">
    update APP_ORDER_EAWB
    <set>
      <if test="orderId != null">
        ORDER_ID = #{orderId,jdbcType=DECIMAL},
      </if>
      <if test="eawbPrintcode != null">
        EAWB_PRINTCODE = #{eawbPrintcode,jdbcType=VARCHAR},
      </if>
      <if test="eawbReference2 != null">
        EAWB_REFERENCE2 = #{eawbReference2,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="eawbReference1 != null">
        EAWB_REFERENCE1 = #{eawbReference1,jdbcType=VARCHAR},
      </if>
      <if test="labelUrl != null">
        LABEL_URL = #{labelUrl,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        STATUS = #{status,jdbcType=VARCHAR},
      </if>
      <if test="actualWeight != null">
        ACTUAL_WEIGHT = #{actualWeight,jdbcType=DECIMAL},
      </if>
      <if test="actualVolume != null">
        ACTUAL_VOLUME = #{actualVolume,jdbcType=DECIMAL},
      </if>
      <if test="chargeableWeight != null">
        CHARGEABLE_WEIGHT = #{chargeableWeight,jdbcType=DECIMAL},
      </if>
      <if test="reportWeight != null">
        REPORT_WEIGHT = #{reportWeight,jdbcType=DECIMAL},
      </if>
      <if test="reportLength != null">
        REPORT_LENGTH = #{reportLength,jdbcType=DECIMAL},
      </if>
      <if test="reportWidth != null">
        REPORT_WIDTH = #{reportWidth,jdbcType=DECIMAL},
      </if>
      <if test="reportHight != null">
        REPORT_HIGHT = #{reportHight,jdbcType=DECIMAL},
      </if>
      <if test="isElectrici != null">
        IS_ELECTRICI = #{isElectrici,jdbcType=VARCHAR},
      </if>
      <if test="isMagnetism != null">
        IS_MAGNETISM = #{isMagnetism,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.AppOrderEawb">
    update APP_ORDER_EAWB
    set ORDER_ID = #{orderId,jdbcType=DECIMAL},
      EAWB_PRINTCODE = #{eawbPrintcode,jdbcType=VARCHAR},
      EAWB_REFERENCE2 = #{eawbReference2,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR},
      EAWB_REFERENCE1 = #{eawbReference1,jdbcType=VARCHAR},
      LABEL_URL = #{labelUrl,jdbcType=VARCHAR},
      STATUS = #{status,jdbcType=VARCHAR},
      ACTUAL_WEIGHT = #{actualWeight,jdbcType=DECIMAL},
      ACTUAL_VOLUME = #{actualVolume,jdbcType=DECIMAL},
      CHARGEABLE_WEIGHT = #{chargeableWeight,jdbcType=DECIMAL},
      REPORT_WEIGHT = #{reportWeight,jdbcType=DECIMAL},
      REPORT_LENGTH = #{reportLength,jdbcType=DECIMAL},
      REPORT_WIDTH = #{reportWidth,jdbcType=DECIMAL},
      REPORT_HIGHT = #{reportHight,jdbcType=DECIMAL},
      IS_ELECTRICI = #{isElectrici,jdbcType=VARCHAR},
      IS_MAGNETISM = #{isMagnetism,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>

  <select id="selectListByOrderId" resultMap="BaseResultMap">
      select * from app_order_eawb where order_id=#{orderId}
  </select>
</mapper>