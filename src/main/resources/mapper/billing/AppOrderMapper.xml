<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.billing.dao.billing.AppOrderMapper">
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.AppOrder">
    <id column="ID" jdbcType="DECIMAL" property="id" />
    <result column="OUT_PRODUCTID" jdbcType="DECIMAL" property="outProductid" />
    <result column="PRODUCTID" jdbcType="DECIMAL" property="productid" />
    <result column="UNIONID" jdbcType="VARCHAR" property="unionid" />
    <result column="EAWB_PRINTCODE" jdbcType="VARCHAR" property="eawbPrintcode" />
    <result column="ORDER_CODE" jdbcType="VARCHAR" property="orderCode" />
    <result column="VAT" jdbcType="DECIMAL" property="vat" />
    <result column="TAX" jdbcType="DECIMAL" property="tax" />
    <result column="PREMIUMS" jdbcType="DECIMAL" property="premiums" />
    <result column="PICKUP_VALUE" jdbcType="DECIMAL" property="pickupValue" />
    <result column="FARE" jdbcType="DECIMAL" property="fare" />
    <result column="OUTBOUND_COMPANY_ID" jdbcType="VARCHAR" property="outboundCompanyId" />
    <result column="PIECES" jdbcType="DECIMAL" property="pieces" />
    <result column="PRE_PRICE" jdbcType="DECIMAL" property="prePrice" />
    <result column="PRE_TOTAL_WEIGHT" jdbcType="DECIMAL" property="preTotalWeight" />
    <result column="PRE_TOTAL_VOLUME" jdbcType="DECIMAL" property="preTotalVolume" />
    <result column="ACTUAL_PRICE" jdbcType="DECIMAL" property="actualPrice" />
    <result column="ACTUAL_TOTAL_WEIGHT" jdbcType="DECIMAL" property="actualTotalWeight" />
    <result column="ACTUAL_TOTAL_VOLUME" jdbcType="DECIMAL" property="actualTotalVolume" />
    <result column="CREATE_ID" jdbcType="DECIMAL" property="createId" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="HANDLER" jdbcType="DECIMAL" property="handler" />
    <result column="HANDLE_TIME" jdbcType="TIMESTAMP" property="handleTime" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="STATUS" jdbcType="VARCHAR" property="status" />
    <result column="PAYMENT_MODE" jdbcType="VARCHAR" property="paymentMode" />
    <result column="INSURANCE_SERVICE" jdbcType="VARCHAR" property="insuranceService" />
    <result column="PICKUP_TYPE" jdbcType="VARCHAR" property="pickupType" />
    <result column="SENDER_NAME" jdbcType="VARCHAR" property="senderName" />
    <result column="SENDER_CONTRY" jdbcType="VARCHAR" property="senderContry" />
    <result column="SENDER_PROVIC" jdbcType="VARCHAR" property="senderProvic" />
    <result column="SENDER_CITY" jdbcType="VARCHAR" property="senderCity" />
    <result column="SENDER_AREA" jdbcType="VARCHAR" property="senderArea" />
    <result column="SENDER_ADRESS" jdbcType="VARCHAR" property="senderAdress" />
    <result column="SENDER_POSTCODE" jdbcType="VARCHAR" property="senderPostcode" />
    <result column="SENDER_PHONE" jdbcType="VARCHAR" property="senderPhone" />
    <result column="RECEIVE_NAME" jdbcType="VARCHAR" property="receiveName" />
    <result column="RECEIVE_CONTRY" jdbcType="VARCHAR" property="receiveContry" />
    <result column="RECEIVE_PROVIC" jdbcType="VARCHAR" property="receiveProvic" />
    <result column="RECEIVE_CITY" jdbcType="VARCHAR" property="receiveCity" />
    <result column="RECEIVE_AREA" jdbcType="VARCHAR" property="receiveArea" />
    <result column="RECEIVE_ADRESS" jdbcType="VARCHAR" property="receiveAdress" />
    <result column="RECEIVE_POSTCODE" jdbcType="VARCHAR" property="receivePostcode" />
    <result column="RECEIVE_PHONE" jdbcType="VARCHAR" property="receivePhone" />
    <result column="RECEIVE_MOBILE" jdbcType="VARCHAR" property="receiveMobile" />
    <result column="RECEIVE_EMAIL" jdbcType="VARCHAR" property="receiveEmail" />
    <result column="PLAN_PRICE" jdbcType="DECIMAL" property="planPrice" />
    <result column="ORDER_TYPE" jdbcType="VARCHAR" property="orderType" />
    <result column="DECLARE_VALUE" jdbcType="DECIMAL" property="declareValue" />
    <result column="SINGLE_CLEARANCE" jdbcType="VARCHAR" property="singleClearance" />
    <result column="IS_WAREHOUSE" jdbcType="VARCHAR" property="isWarehouse" />
    <result column="ITEM_NUM" jdbcType="DECIMAL" property="itemNum" />
    <result column="CHARGEABLE_TOTAL_WEIGHT" jdbcType="DECIMAL" property="chargeableTotalWeight" />
    <result column="IS_INSURED" jdbcType="VARCHAR" property="isInsured" />
    <result column="INSURED_PRICE" jdbcType="DECIMAL" property="insuredPrice" />
    <result column="IS_CLEAR" jdbcType="VARCHAR" property="isClear" />
    <result column="IS_DEFER" jdbcType="VARCHAR" property="isDefer" />
    <result column="IS_ELECTRICI" jdbcType="VARCHAR" property="isElectrici" />
    <result column="IS_MAGNETISM" jdbcType="VARCHAR" property="isMagnetism" />
    <result column="REFERENCE_ID" jdbcType="VARCHAR" property="referenceId" />
    <result column="DECLARE_CURRENCY" jdbcType="VARCHAR" property="declareCurrency" />
    <result column="FBA_PRODUCT_NAME" jdbcType="VARCHAR" property="fbaProductName" />
    <result column="SENDER_COMPANY" jdbcType="VARCHAR" property="senderCompany" />
    <result column="RECEIVE_COMPANY" jdbcType="VARCHAR" property="receiveCompany" />
    <result column="CLIENT_CODE" jdbcType="VARCHAR" property="clientCode" />
    <result column="RECEIVE_WAREHOUSE" jdbcType="VARCHAR" property="receiveWarehouse" />
    <result column="ARRIVAL_WAREHOUSE_DATE" jdbcType="TIMESTAMP" property="arrivalWarehouseDate" />
    <result column="RECEIVE_AMZ_WAREHOUSE" jdbcType="VARCHAR" property="receiveAmzWarehouse" />
    <result column="RECEIVE_AMZ_WAREHOUSE_NAME" jdbcType="VARCHAR" property="receiveAmzWarehouseName" />
    <result column="EORI" jdbcType="VARCHAR" property="eori" />
    <result column="SO_CODE" jdbcType="VARCHAR" property="soCode" />
    <result column="VAT_NUMBER" jdbcType="VARCHAR" property="vatNumber" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, OUT_PRODUCTID, PRODUCTID, UNIONID, EAWB_PRINTCODE, ORDER_CODE, VAT, TAX, PREMIUMS, 
    PICKUP_VALUE, FARE, OUTBOUND_COMPANY_ID, PIECES, PRE_PRICE, PRE_TOTAL_WEIGHT, PRE_TOTAL_VOLUME, 
    ACTUAL_PRICE, ACTUAL_TOTAL_WEIGHT, ACTUAL_TOTAL_VOLUME, CREATE_ID, CREATE_TIME, HANDLER, 
    HANDLE_TIME, REMARK, STATUS, PAYMENT_MODE, INSURANCE_SERVICE, PICKUP_TYPE, SENDER_NAME, 
    SENDER_CONTRY, SENDER_PROVIC, SENDER_CITY, SENDER_AREA, SENDER_ADRESS, SENDER_POSTCODE, 
    SENDER_PHONE, RECEIVE_NAME, RECEIVE_CONTRY, RECEIVE_PROVIC, RECEIVE_CITY, RECEIVE_AREA, 
    RECEIVE_ADRESS, RECEIVE_POSTCODE, RECEIVE_PHONE, RECEIVE_MOBILE, RECEIVE_EMAIL, PLAN_PRICE, 
    ORDER_TYPE, DECLARE_VALUE, SINGLE_CLEARANCE, IS_WAREHOUSE, ITEM_NUM, CHARGEABLE_TOTAL_WEIGHT, 
    IS_INSURED, INSURED_PRICE, IS_CLEAR, IS_DEFER, IS_ELECTRICI, IS_MAGNETISM, REFERENCE_ID, 
    DECLARE_CURRENCY, FBA_PRODUCT_NAME, SENDER_COMPANY, RECEIVE_COMPANY, CLIENT_CODE, 
    RECEIVE_WAREHOUSE, ARRIVAL_WAREHOUSE_DATE, RECEIVE_AMZ_WAREHOUSE, RECEIVE_AMZ_WAREHOUSE_NAME, 
    EORI, SO_CODE, VAT_NUMBER
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from APP_ORDER
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from APP_ORDER
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.AppOrder">
    insert into APP_ORDER (ID, OUT_PRODUCTID, PRODUCTID, 
      UNIONID, EAWB_PRINTCODE, ORDER_CODE, 
      VAT, TAX, PREMIUMS, 
      PICKUP_VALUE, FARE, OUTBOUND_COMPANY_ID, 
      PIECES, PRE_PRICE, PRE_TOTAL_WEIGHT, 
      PRE_TOTAL_VOLUME, ACTUAL_PRICE, ACTUAL_TOTAL_WEIGHT, 
      ACTUAL_TOTAL_VOLUME, CREATE_ID, CREATE_TIME, 
      HANDLER, HANDLE_TIME, REMARK, 
      STATUS, PAYMENT_MODE, INSURANCE_SERVICE, 
      PICKUP_TYPE, SENDER_NAME, SENDER_CONTRY, 
      SENDER_PROVIC, SENDER_CITY, SENDER_AREA, 
      SENDER_ADRESS, SENDER_POSTCODE, SENDER_PHONE, 
      RECEIVE_NAME, RECEIVE_CONTRY, RECEIVE_PROVIC, 
      RECEIVE_CITY, RECEIVE_AREA, RECEIVE_ADRESS, 
      RECEIVE_POSTCODE, RECEIVE_PHONE, RECEIVE_MOBILE, 
      RECEIVE_EMAIL, PLAN_PRICE, ORDER_TYPE, 
      DECLARE_VALUE, SINGLE_CLEARANCE, IS_WAREHOUSE, 
      ITEM_NUM, CHARGEABLE_TOTAL_WEIGHT, IS_INSURED, 
      INSURED_PRICE, IS_CLEAR, IS_DEFER, 
      IS_ELECTRICI, IS_MAGNETISM, REFERENCE_ID, 
      DECLARE_CURRENCY, FBA_PRODUCT_NAME, SENDER_COMPANY, 
      RECEIVE_COMPANY, CLIENT_CODE, RECEIVE_WAREHOUSE, 
      ARRIVAL_WAREHOUSE_DATE, RECEIVE_AMZ_WAREHOUSE, 
      RECEIVE_AMZ_WAREHOUSE_NAME, EORI, SO_CODE, 
      VAT_NUMBER)
    values (#{id,jdbcType=DECIMAL}, #{outProductid,jdbcType=DECIMAL}, #{productid,jdbcType=DECIMAL}, 
      #{unionid,jdbcType=VARCHAR}, #{eawbPrintcode,jdbcType=VARCHAR}, #{orderCode,jdbcType=VARCHAR}, 
      #{vat,jdbcType=DECIMAL}, #{tax,jdbcType=DECIMAL}, #{premiums,jdbcType=DECIMAL}, 
      #{pickupValue,jdbcType=DECIMAL}, #{fare,jdbcType=DECIMAL}, #{outboundCompanyId,jdbcType=VARCHAR}, 
      #{pieces,jdbcType=DECIMAL}, #{prePrice,jdbcType=DECIMAL}, #{preTotalWeight,jdbcType=DECIMAL}, 
      #{preTotalVolume,jdbcType=DECIMAL}, #{actualPrice,jdbcType=DECIMAL}, #{actualTotalWeight,jdbcType=DECIMAL}, 
      #{actualTotalVolume,jdbcType=DECIMAL}, #{createId,jdbcType=DECIMAL}, #{createTime,jdbcType=TIMESTAMP}, 
      #{handler,jdbcType=DECIMAL}, #{handleTime,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR}, 
      #{status,jdbcType=VARCHAR}, #{paymentMode,jdbcType=VARCHAR}, #{insuranceService,jdbcType=VARCHAR}, 
      #{pickupType,jdbcType=VARCHAR}, #{senderName,jdbcType=VARCHAR}, #{senderContry,jdbcType=VARCHAR}, 
      #{senderProvic,jdbcType=VARCHAR}, #{senderCity,jdbcType=VARCHAR}, #{senderArea,jdbcType=VARCHAR}, 
      #{senderAdress,jdbcType=VARCHAR}, #{senderPostcode,jdbcType=VARCHAR}, #{senderPhone,jdbcType=VARCHAR}, 
      #{receiveName,jdbcType=VARCHAR}, #{receiveContry,jdbcType=VARCHAR}, #{receiveProvic,jdbcType=VARCHAR}, 
      #{receiveCity,jdbcType=VARCHAR}, #{receiveArea,jdbcType=VARCHAR}, #{receiveAdress,jdbcType=VARCHAR}, 
      #{receivePostcode,jdbcType=VARCHAR}, #{receivePhone,jdbcType=VARCHAR}, #{receiveMobile,jdbcType=VARCHAR}, 
      #{receiveEmail,jdbcType=VARCHAR}, #{planPrice,jdbcType=DECIMAL}, #{orderType,jdbcType=VARCHAR}, 
      #{declareValue,jdbcType=DECIMAL}, #{singleClearance,jdbcType=VARCHAR}, #{isWarehouse,jdbcType=VARCHAR}, 
      #{itemNum,jdbcType=DECIMAL}, #{chargeableTotalWeight,jdbcType=DECIMAL}, #{isInsured,jdbcType=VARCHAR}, 
      #{insuredPrice,jdbcType=DECIMAL}, #{isClear,jdbcType=VARCHAR}, #{isDefer,jdbcType=VARCHAR}, 
      #{isElectrici,jdbcType=VARCHAR}, #{isMagnetism,jdbcType=VARCHAR}, #{referenceId,jdbcType=VARCHAR}, 
      #{declareCurrency,jdbcType=VARCHAR}, #{fbaProductName,jdbcType=VARCHAR}, #{senderCompany,jdbcType=VARCHAR}, 
      #{receiveCompany,jdbcType=VARCHAR}, #{clientCode,jdbcType=VARCHAR}, #{receiveWarehouse,jdbcType=VARCHAR}, 
      #{arrivalWarehouseDate,jdbcType=TIMESTAMP}, #{receiveAmzWarehouse,jdbcType=VARCHAR}, 
      #{receiveAmzWarehouseName,jdbcType=VARCHAR}, #{eori,jdbcType=VARCHAR}, #{soCode,jdbcType=VARCHAR}, 
      #{vatNumber,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.AppOrder">
    insert into APP_ORDER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="outProductid != null">
        OUT_PRODUCTID,
      </if>
      <if test="productid != null">
        PRODUCTID,
      </if>
      <if test="unionid != null">
        UNIONID,
      </if>
      <if test="eawbPrintcode != null">
        EAWB_PRINTCODE,
      </if>
      <if test="orderCode != null">
        ORDER_CODE,
      </if>
      <if test="vat != null">
        VAT,
      </if>
      <if test="tax != null">
        TAX,
      </if>
      <if test="premiums != null">
        PREMIUMS,
      </if>
      <if test="pickupValue != null">
        PICKUP_VALUE,
      </if>
      <if test="fare != null">
        FARE,
      </if>
      <if test="outboundCompanyId != null">
        OUTBOUND_COMPANY_ID,
      </if>
      <if test="pieces != null">
        PIECES,
      </if>
      <if test="prePrice != null">
        PRE_PRICE,
      </if>
      <if test="preTotalWeight != null">
        PRE_TOTAL_WEIGHT,
      </if>
      <if test="preTotalVolume != null">
        PRE_TOTAL_VOLUME,
      </if>
      <if test="actualPrice != null">
        ACTUAL_PRICE,
      </if>
      <if test="actualTotalWeight != null">
        ACTUAL_TOTAL_WEIGHT,
      </if>
      <if test="actualTotalVolume != null">
        ACTUAL_TOTAL_VOLUME,
      </if>
      <if test="createId != null">
        CREATE_ID,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="handler != null">
        HANDLER,
      </if>
      <if test="handleTime != null">
        HANDLE_TIME,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="status != null">
        STATUS,
      </if>
      <if test="paymentMode != null">
        PAYMENT_MODE,
      </if>
      <if test="insuranceService != null">
        INSURANCE_SERVICE,
      </if>
      <if test="pickupType != null">
        PICKUP_TYPE,
      </if>
      <if test="senderName != null">
        SENDER_NAME,
      </if>
      <if test="senderContry != null">
        SENDER_CONTRY,
      </if>
      <if test="senderProvic != null">
        SENDER_PROVIC,
      </if>
      <if test="senderCity != null">
        SENDER_CITY,
      </if>
      <if test="senderArea != null">
        SENDER_AREA,
      </if>
      <if test="senderAdress != null">
        SENDER_ADRESS,
      </if>
      <if test="senderPostcode != null">
        SENDER_POSTCODE,
      </if>
      <if test="senderPhone != null">
        SENDER_PHONE,
      </if>
      <if test="receiveName != null">
        RECEIVE_NAME,
      </if>
      <if test="receiveContry != null">
        RECEIVE_CONTRY,
      </if>
      <if test="receiveProvic != null">
        RECEIVE_PROVIC,
      </if>
      <if test="receiveCity != null">
        RECEIVE_CITY,
      </if>
      <if test="receiveArea != null">
        RECEIVE_AREA,
      </if>
      <if test="receiveAdress != null">
        RECEIVE_ADRESS,
      </if>
      <if test="receivePostcode != null">
        RECEIVE_POSTCODE,
      </if>
      <if test="receivePhone != null">
        RECEIVE_PHONE,
      </if>
      <if test="receiveMobile != null">
        RECEIVE_MOBILE,
      </if>
      <if test="receiveEmail != null">
        RECEIVE_EMAIL,
      </if>
      <if test="planPrice != null">
        PLAN_PRICE,
      </if>
      <if test="orderType != null">
        ORDER_TYPE,
      </if>
      <if test="declareValue != null">
        DECLARE_VALUE,
      </if>
      <if test="singleClearance != null">
        SINGLE_CLEARANCE,
      </if>
      <if test="isWarehouse != null">
        IS_WAREHOUSE,
      </if>
      <if test="itemNum != null">
        ITEM_NUM,
      </if>
      <if test="chargeableTotalWeight != null">
        CHARGEABLE_TOTAL_WEIGHT,
      </if>
      <if test="isInsured != null">
        IS_INSURED,
      </if>
      <if test="insuredPrice != null">
        INSURED_PRICE,
      </if>
      <if test="isClear != null">
        IS_CLEAR,
      </if>
      <if test="isDefer != null">
        IS_DEFER,
      </if>
      <if test="isElectrici != null">
        IS_ELECTRICI,
      </if>
      <if test="isMagnetism != null">
        IS_MAGNETISM,
      </if>
      <if test="referenceId != null">
        REFERENCE_ID,
      </if>
      <if test="declareCurrency != null">
        DECLARE_CURRENCY,
      </if>
      <if test="fbaProductName != null">
        FBA_PRODUCT_NAME,
      </if>
      <if test="senderCompany != null">
        SENDER_COMPANY,
      </if>
      <if test="receiveCompany != null">
        RECEIVE_COMPANY,
      </if>
      <if test="clientCode != null">
        CLIENT_CODE,
      </if>
      <if test="receiveWarehouse != null">
        RECEIVE_WAREHOUSE,
      </if>
      <if test="arrivalWarehouseDate != null">
        ARRIVAL_WAREHOUSE_DATE,
      </if>
      <if test="receiveAmzWarehouse != null">
        RECEIVE_AMZ_WAREHOUSE,
      </if>
      <if test="receiveAmzWarehouseName != null">
        RECEIVE_AMZ_WAREHOUSE_NAME,
      </if>
      <if test="eori != null">
        EORI,
      </if>
      <if test="soCode != null">
        SO_CODE,
      </if>
      <if test="vatNumber != null">
        VAT_NUMBER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="outProductid != null">
        #{outProductid,jdbcType=DECIMAL},
      </if>
      <if test="productid != null">
        #{productid,jdbcType=DECIMAL},
      </if>
      <if test="unionid != null">
        #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="eawbPrintcode != null">
        #{eawbPrintcode,jdbcType=VARCHAR},
      </if>
      <if test="orderCode != null">
        #{orderCode,jdbcType=VARCHAR},
      </if>
      <if test="vat != null">
        #{vat,jdbcType=DECIMAL},
      </if>
      <if test="tax != null">
        #{tax,jdbcType=DECIMAL},
      </if>
      <if test="premiums != null">
        #{premiums,jdbcType=DECIMAL},
      </if>
      <if test="pickupValue != null">
        #{pickupValue,jdbcType=DECIMAL},
      </if>
      <if test="fare != null">
        #{fare,jdbcType=DECIMAL},
      </if>
      <if test="outboundCompanyId != null">
        #{outboundCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="pieces != null">
        #{pieces,jdbcType=DECIMAL},
      </if>
      <if test="prePrice != null">
        #{prePrice,jdbcType=DECIMAL},
      </if>
      <if test="preTotalWeight != null">
        #{preTotalWeight,jdbcType=DECIMAL},
      </if>
      <if test="preTotalVolume != null">
        #{preTotalVolume,jdbcType=DECIMAL},
      </if>
      <if test="actualPrice != null">
        #{actualPrice,jdbcType=DECIMAL},
      </if>
      <if test="actualTotalWeight != null">
        #{actualTotalWeight,jdbcType=DECIMAL},
      </if>
      <if test="actualTotalVolume != null">
        #{actualTotalVolume,jdbcType=DECIMAL},
      </if>
      <if test="createId != null">
        #{createId,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="handler != null">
        #{handler,jdbcType=DECIMAL},
      </if>
      <if test="handleTime != null">
        #{handleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="paymentMode != null">
        #{paymentMode,jdbcType=VARCHAR},
      </if>
      <if test="insuranceService != null">
        #{insuranceService,jdbcType=VARCHAR},
      </if>
      <if test="pickupType != null">
        #{pickupType,jdbcType=VARCHAR},
      </if>
      <if test="senderName != null">
        #{senderName,jdbcType=VARCHAR},
      </if>
      <if test="senderContry != null">
        #{senderContry,jdbcType=VARCHAR},
      </if>
      <if test="senderProvic != null">
        #{senderProvic,jdbcType=VARCHAR},
      </if>
      <if test="senderCity != null">
        #{senderCity,jdbcType=VARCHAR},
      </if>
      <if test="senderArea != null">
        #{senderArea,jdbcType=VARCHAR},
      </if>
      <if test="senderAdress != null">
        #{senderAdress,jdbcType=VARCHAR},
      </if>
      <if test="senderPostcode != null">
        #{senderPostcode,jdbcType=VARCHAR},
      </if>
      <if test="senderPhone != null">
        #{senderPhone,jdbcType=VARCHAR},
      </if>
      <if test="receiveName != null">
        #{receiveName,jdbcType=VARCHAR},
      </if>
      <if test="receiveContry != null">
        #{receiveContry,jdbcType=VARCHAR},
      </if>
      <if test="receiveProvic != null">
        #{receiveProvic,jdbcType=VARCHAR},
      </if>
      <if test="receiveCity != null">
        #{receiveCity,jdbcType=VARCHAR},
      </if>
      <if test="receiveArea != null">
        #{receiveArea,jdbcType=VARCHAR},
      </if>
      <if test="receiveAdress != null">
        #{receiveAdress,jdbcType=VARCHAR},
      </if>
      <if test="receivePostcode != null">
        #{receivePostcode,jdbcType=VARCHAR},
      </if>
      <if test="receivePhone != null">
        #{receivePhone,jdbcType=VARCHAR},
      </if>
      <if test="receiveMobile != null">
        #{receiveMobile,jdbcType=VARCHAR},
      </if>
      <if test="receiveEmail != null">
        #{receiveEmail,jdbcType=VARCHAR},
      </if>
      <if test="planPrice != null">
        #{planPrice,jdbcType=DECIMAL},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="declareValue != null">
        #{declareValue,jdbcType=DECIMAL},
      </if>
      <if test="singleClearance != null">
        #{singleClearance,jdbcType=VARCHAR},
      </if>
      <if test="isWarehouse != null">
        #{isWarehouse,jdbcType=VARCHAR},
      </if>
      <if test="itemNum != null">
        #{itemNum,jdbcType=DECIMAL},
      </if>
      <if test="chargeableTotalWeight != null">
        #{chargeableTotalWeight,jdbcType=DECIMAL},
      </if>
      <if test="isInsured != null">
        #{isInsured,jdbcType=VARCHAR},
      </if>
      <if test="insuredPrice != null">
        #{insuredPrice,jdbcType=DECIMAL},
      </if>
      <if test="isClear != null">
        #{isClear,jdbcType=VARCHAR},
      </if>
      <if test="isDefer != null">
        #{isDefer,jdbcType=VARCHAR},
      </if>
      <if test="isElectrici != null">
        #{isElectrici,jdbcType=VARCHAR},
      </if>
      <if test="isMagnetism != null">
        #{isMagnetism,jdbcType=VARCHAR},
      </if>
      <if test="referenceId != null">
        #{referenceId,jdbcType=VARCHAR},
      </if>
      <if test="declareCurrency != null">
        #{declareCurrency,jdbcType=VARCHAR},
      </if>
      <if test="fbaProductName != null">
        #{fbaProductName,jdbcType=VARCHAR},
      </if>
      <if test="senderCompany != null">
        #{senderCompany,jdbcType=VARCHAR},
      </if>
      <if test="receiveCompany != null">
        #{receiveCompany,jdbcType=VARCHAR},
      </if>
      <if test="clientCode != null">
        #{clientCode,jdbcType=VARCHAR},
      </if>
      <if test="receiveWarehouse != null">
        #{receiveWarehouse,jdbcType=VARCHAR},
      </if>
      <if test="arrivalWarehouseDate != null">
        #{arrivalWarehouseDate,jdbcType=TIMESTAMP},
      </if>
      <if test="receiveAmzWarehouse != null">
        #{receiveAmzWarehouse,jdbcType=VARCHAR},
      </if>
      <if test="receiveAmzWarehouseName != null">
        #{receiveAmzWarehouseName,jdbcType=VARCHAR},
      </if>
      <if test="eori != null">
        #{eori,jdbcType=VARCHAR},
      </if>
      <if test="soCode != null">
        #{soCode,jdbcType=VARCHAR},
      </if>
      <if test="vatNumber != null">
        #{vatNumber,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.AppOrder">
    update APP_ORDER
    <set>
      <if test="outProductid != null">
        OUT_PRODUCTID = #{outProductid,jdbcType=DECIMAL},
      </if>
      <if test="productid != null">
        PRODUCTID = #{productid,jdbcType=DECIMAL},
      </if>
      <if test="unionid != null">
        UNIONID = #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="eawbPrintcode != null">
        EAWB_PRINTCODE = #{eawbPrintcode,jdbcType=VARCHAR},
      </if>
      <if test="orderCode != null">
        ORDER_CODE = #{orderCode,jdbcType=VARCHAR},
      </if>
      <if test="vat != null">
        VAT = #{vat,jdbcType=DECIMAL},
      </if>
      <if test="tax != null">
        TAX = #{tax,jdbcType=DECIMAL},
      </if>
      <if test="premiums != null">
        PREMIUMS = #{premiums,jdbcType=DECIMAL},
      </if>
      <if test="pickupValue != null">
        PICKUP_VALUE = #{pickupValue,jdbcType=DECIMAL},
      </if>
      <if test="fare != null">
        FARE = #{fare,jdbcType=DECIMAL},
      </if>
      <if test="outboundCompanyId != null">
        OUTBOUND_COMPANY_ID = #{outboundCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="pieces != null">
        PIECES = #{pieces,jdbcType=DECIMAL},
      </if>
      <if test="prePrice != null">
        PRE_PRICE = #{prePrice,jdbcType=DECIMAL},
      </if>
      <if test="preTotalWeight != null">
        PRE_TOTAL_WEIGHT = #{preTotalWeight,jdbcType=DECIMAL},
      </if>
      <if test="preTotalVolume != null">
        PRE_TOTAL_VOLUME = #{preTotalVolume,jdbcType=DECIMAL},
      </if>
      <if test="actualPrice != null">
        ACTUAL_PRICE = #{actualPrice,jdbcType=DECIMAL},
      </if>
      <if test="actualTotalWeight != null">
        ACTUAL_TOTAL_WEIGHT = #{actualTotalWeight,jdbcType=DECIMAL},
      </if>
      <if test="actualTotalVolume != null">
        ACTUAL_TOTAL_VOLUME = #{actualTotalVolume,jdbcType=DECIMAL},
      </if>
      <if test="createId != null">
        CREATE_ID = #{createId,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="handler != null">
        HANDLER = #{handler,jdbcType=DECIMAL},
      </if>
      <if test="handleTime != null">
        HANDLE_TIME = #{handleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        STATUS = #{status,jdbcType=VARCHAR},
      </if>
      <if test="paymentMode != null">
        PAYMENT_MODE = #{paymentMode,jdbcType=VARCHAR},
      </if>
      <if test="insuranceService != null">
        INSURANCE_SERVICE = #{insuranceService,jdbcType=VARCHAR},
      </if>
      <if test="pickupType != null">
        PICKUP_TYPE = #{pickupType,jdbcType=VARCHAR},
      </if>
      <if test="senderName != null">
        SENDER_NAME = #{senderName,jdbcType=VARCHAR},
      </if>
      <if test="senderContry != null">
        SENDER_CONTRY = #{senderContry,jdbcType=VARCHAR},
      </if>
      <if test="senderProvic != null">
        SENDER_PROVIC = #{senderProvic,jdbcType=VARCHAR},
      </if>
      <if test="senderCity != null">
        SENDER_CITY = #{senderCity,jdbcType=VARCHAR},
      </if>
      <if test="senderArea != null">
        SENDER_AREA = #{senderArea,jdbcType=VARCHAR},
      </if>
      <if test="senderAdress != null">
        SENDER_ADRESS = #{senderAdress,jdbcType=VARCHAR},
      </if>
      <if test="senderPostcode != null">
        SENDER_POSTCODE = #{senderPostcode,jdbcType=VARCHAR},
      </if>
      <if test="senderPhone != null">
        SENDER_PHONE = #{senderPhone,jdbcType=VARCHAR},
      </if>
      <if test="receiveName != null">
        RECEIVE_NAME = #{receiveName,jdbcType=VARCHAR},
      </if>
      <if test="receiveContry != null">
        RECEIVE_CONTRY = #{receiveContry,jdbcType=VARCHAR},
      </if>
      <if test="receiveProvic != null">
        RECEIVE_PROVIC = #{receiveProvic,jdbcType=VARCHAR},
      </if>
      <if test="receiveCity != null">
        RECEIVE_CITY = #{receiveCity,jdbcType=VARCHAR},
      </if>
      <if test="receiveArea != null">
        RECEIVE_AREA = #{receiveArea,jdbcType=VARCHAR},
      </if>
      <if test="receiveAdress != null">
        RECEIVE_ADRESS = #{receiveAdress,jdbcType=VARCHAR},
      </if>
      <if test="receivePostcode != null">
        RECEIVE_POSTCODE = #{receivePostcode,jdbcType=VARCHAR},
      </if>
      <if test="receivePhone != null">
        RECEIVE_PHONE = #{receivePhone,jdbcType=VARCHAR},
      </if>
      <if test="receiveMobile != null">
        RECEIVE_MOBILE = #{receiveMobile,jdbcType=VARCHAR},
      </if>
      <if test="receiveEmail != null">
        RECEIVE_EMAIL = #{receiveEmail,jdbcType=VARCHAR},
      </if>
      <if test="planPrice != null">
        PLAN_PRICE = #{planPrice,jdbcType=DECIMAL},
      </if>
      <if test="orderType != null">
        ORDER_TYPE = #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="declareValue != null">
        DECLARE_VALUE = #{declareValue,jdbcType=DECIMAL},
      </if>
      <if test="singleClearance != null">
        SINGLE_CLEARANCE = #{singleClearance,jdbcType=VARCHAR},
      </if>
      <if test="isWarehouse != null">
        IS_WAREHOUSE = #{isWarehouse,jdbcType=VARCHAR},
      </if>
      <if test="itemNum != null">
        ITEM_NUM = #{itemNum,jdbcType=DECIMAL},
      </if>
      <if test="chargeableTotalWeight != null">
        CHARGEABLE_TOTAL_WEIGHT = #{chargeableTotalWeight,jdbcType=DECIMAL},
      </if>
      <if test="isInsured != null">
        IS_INSURED = #{isInsured,jdbcType=VARCHAR},
      </if>
      <if test="insuredPrice != null">
        INSURED_PRICE = #{insuredPrice,jdbcType=DECIMAL},
      </if>
      <if test="isClear != null">
        IS_CLEAR = #{isClear,jdbcType=VARCHAR},
      </if>
      <if test="isDefer != null">
        IS_DEFER = #{isDefer,jdbcType=VARCHAR},
      </if>
      <if test="isElectrici != null">
        IS_ELECTRICI = #{isElectrici,jdbcType=VARCHAR},
      </if>
      <if test="isMagnetism != null">
        IS_MAGNETISM = #{isMagnetism,jdbcType=VARCHAR},
      </if>
      <if test="referenceId != null">
        REFERENCE_ID = #{referenceId,jdbcType=VARCHAR},
      </if>
      <if test="declareCurrency != null">
        DECLARE_CURRENCY = #{declareCurrency,jdbcType=VARCHAR},
      </if>
      <if test="fbaProductName != null">
        FBA_PRODUCT_NAME = #{fbaProductName,jdbcType=VARCHAR},
      </if>
      <if test="senderCompany != null">
        SENDER_COMPANY = #{senderCompany,jdbcType=VARCHAR},
      </if>
      <if test="receiveCompany != null">
        RECEIVE_COMPANY = #{receiveCompany,jdbcType=VARCHAR},
      </if>
      <if test="clientCode != null">
        CLIENT_CODE = #{clientCode,jdbcType=VARCHAR},
      </if>
      <if test="receiveWarehouse != null">
        RECEIVE_WAREHOUSE = #{receiveWarehouse,jdbcType=VARCHAR},
      </if>
      <if test="arrivalWarehouseDate != null">
        ARRIVAL_WAREHOUSE_DATE = #{arrivalWarehouseDate,jdbcType=TIMESTAMP},
      </if>
      <if test="receiveAmzWarehouse != null">
        RECEIVE_AMZ_WAREHOUSE = #{receiveAmzWarehouse,jdbcType=VARCHAR},
      </if>
      <if test="receiveAmzWarehouseName != null">
        RECEIVE_AMZ_WAREHOUSE_NAME = #{receiveAmzWarehouseName,jdbcType=VARCHAR},
      </if>
      <if test="eori != null">
        EORI = #{eori,jdbcType=VARCHAR},
      </if>
      <if test="soCode != null">
        SO_CODE = #{soCode,jdbcType=VARCHAR},
      </if>
      <if test="vatNumber != null">
        VAT_NUMBER = #{vatNumber,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.AppOrder">
    update APP_ORDER
    set OUT_PRODUCTID = #{outProductid,jdbcType=DECIMAL},
      PRODUCTID = #{productid,jdbcType=DECIMAL},
      UNIONID = #{unionid,jdbcType=VARCHAR},
      EAWB_PRINTCODE = #{eawbPrintcode,jdbcType=VARCHAR},
      ORDER_CODE = #{orderCode,jdbcType=VARCHAR},
      VAT = #{vat,jdbcType=DECIMAL},
      TAX = #{tax,jdbcType=DECIMAL},
      PREMIUMS = #{premiums,jdbcType=DECIMAL},
      PICKUP_VALUE = #{pickupValue,jdbcType=DECIMAL},
      FARE = #{fare,jdbcType=DECIMAL},
      OUTBOUND_COMPANY_ID = #{outboundCompanyId,jdbcType=VARCHAR},
      PIECES = #{pieces,jdbcType=DECIMAL},
      PRE_PRICE = #{prePrice,jdbcType=DECIMAL},
      PRE_TOTAL_WEIGHT = #{preTotalWeight,jdbcType=DECIMAL},
      PRE_TOTAL_VOLUME = #{preTotalVolume,jdbcType=DECIMAL},
      ACTUAL_PRICE = #{actualPrice,jdbcType=DECIMAL},
      ACTUAL_TOTAL_WEIGHT = #{actualTotalWeight,jdbcType=DECIMAL},
      ACTUAL_TOTAL_VOLUME = #{actualTotalVolume,jdbcType=DECIMAL},
      CREATE_ID = #{createId,jdbcType=DECIMAL},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      HANDLER = #{handler,jdbcType=DECIMAL},
      HANDLE_TIME = #{handleTime,jdbcType=TIMESTAMP},
      REMARK = #{remark,jdbcType=VARCHAR},
      STATUS = #{status,jdbcType=VARCHAR},
      PAYMENT_MODE = #{paymentMode,jdbcType=VARCHAR},
      INSURANCE_SERVICE = #{insuranceService,jdbcType=VARCHAR},
      PICKUP_TYPE = #{pickupType,jdbcType=VARCHAR},
      SENDER_NAME = #{senderName,jdbcType=VARCHAR},
      SENDER_CONTRY = #{senderContry,jdbcType=VARCHAR},
      SENDER_PROVIC = #{senderProvic,jdbcType=VARCHAR},
      SENDER_CITY = #{senderCity,jdbcType=VARCHAR},
      SENDER_AREA = #{senderArea,jdbcType=VARCHAR},
      SENDER_ADRESS = #{senderAdress,jdbcType=VARCHAR},
      SENDER_POSTCODE = #{senderPostcode,jdbcType=VARCHAR},
      SENDER_PHONE = #{senderPhone,jdbcType=VARCHAR},
      RECEIVE_NAME = #{receiveName,jdbcType=VARCHAR},
      RECEIVE_CONTRY = #{receiveContry,jdbcType=VARCHAR},
      RECEIVE_PROVIC = #{receiveProvic,jdbcType=VARCHAR},
      RECEIVE_CITY = #{receiveCity,jdbcType=VARCHAR},
      RECEIVE_AREA = #{receiveArea,jdbcType=VARCHAR},
      RECEIVE_ADRESS = #{receiveAdress,jdbcType=VARCHAR},
      RECEIVE_POSTCODE = #{receivePostcode,jdbcType=VARCHAR},
      RECEIVE_PHONE = #{receivePhone,jdbcType=VARCHAR},
      RECEIVE_MOBILE = #{receiveMobile,jdbcType=VARCHAR},
      RECEIVE_EMAIL = #{receiveEmail,jdbcType=VARCHAR},
      PLAN_PRICE = #{planPrice,jdbcType=DECIMAL},
      ORDER_TYPE = #{orderType,jdbcType=VARCHAR},
      DECLARE_VALUE = #{declareValue,jdbcType=DECIMAL},
      SINGLE_CLEARANCE = #{singleClearance,jdbcType=VARCHAR},
      IS_WAREHOUSE = #{isWarehouse,jdbcType=VARCHAR},
      ITEM_NUM = #{itemNum,jdbcType=DECIMAL},
      CHARGEABLE_TOTAL_WEIGHT = #{chargeableTotalWeight,jdbcType=DECIMAL},
      IS_INSURED = #{isInsured,jdbcType=VARCHAR},
      INSURED_PRICE = #{insuredPrice,jdbcType=DECIMAL},
      IS_CLEAR = #{isClear,jdbcType=VARCHAR},
      IS_DEFER = #{isDefer,jdbcType=VARCHAR},
      IS_ELECTRICI = #{isElectrici,jdbcType=VARCHAR},
      IS_MAGNETISM = #{isMagnetism,jdbcType=VARCHAR},
      REFERENCE_ID = #{referenceId,jdbcType=VARCHAR},
      DECLARE_CURRENCY = #{declareCurrency,jdbcType=VARCHAR},
      FBA_PRODUCT_NAME = #{fbaProductName,jdbcType=VARCHAR},
      SENDER_COMPANY = #{senderCompany,jdbcType=VARCHAR},
      RECEIVE_COMPANY = #{receiveCompany,jdbcType=VARCHAR},
      CLIENT_CODE = #{clientCode,jdbcType=VARCHAR},
      RECEIVE_WAREHOUSE = #{receiveWarehouse,jdbcType=VARCHAR},
      ARRIVAL_WAREHOUSE_DATE = #{arrivalWarehouseDate,jdbcType=TIMESTAMP},
      RECEIVE_AMZ_WAREHOUSE = #{receiveAmzWarehouse,jdbcType=VARCHAR},
      RECEIVE_AMZ_WAREHOUSE_NAME = #{receiveAmzWarehouseName,jdbcType=VARCHAR},
      EORI = #{eori,jdbcType=VARCHAR},
      SO_CODE = #{soCode,jdbcType=VARCHAR},
      VAT_NUMBER = #{vatNumber,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>

  <select id="selectNotExistInEawb" resultMap="BaseResultMap">
    select * from app_order where id in(
      select ORDER_ID from APP_DEBIT_MANIFEST where syn_status='N')
  </select>
</mapper>