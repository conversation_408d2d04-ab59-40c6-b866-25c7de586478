<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.billing.dao.billing.AppReceiptRecordMapper">
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.AppReceiptRecord">
    <id column="RR_ID" jdbcType="DECIMAL" property="rrId" />
    <result column="EAWB_PRINTCODE" jdbcType="VARCHAR" property="eawbPrintcode" />
    <result column="MAWB_CODE" jdbcType="VARCHAR" property="mawbCode" />
    <result column="PR_ID" jdbcType="DECIMAL" property="prId" />
    <result column="DM_ID" jdbcType="DECIMAL" property="dmId" />
    <result column="SO_CODE" jdbcType="VARCHAR" property="soCode" />
    <result column="CT_CODE" jdbcType="VARCHAR" property="ctCode" />
    <result column="COMPANY_ID" jdbcType="VARCHAR" property="companyId" />
    <result column="RR_NAME" jdbcType="VARCHAR" property="rrName" />
    <result column="RR_TYPE" jdbcType="VARCHAR" property="rrType" />
    <result column="RR_PLAN_AMOUNT" jdbcType="DECIMAL" property="rrPlanAmount" />
    <result column="RR_ACTUAL_AMOUNT" jdbcType="DECIMAL" property="rrActualAmount" />
    <result column="RR_STATUS" jdbcType="VARCHAR" property="rrStatus" />
    <result column="RR_USER_ID" jdbcType="DECIMAL" property="rrUserId" />
    <result column="RR_HANDLETIME" jdbcType="TIMESTAMP" property="rrHandletime" />
    <result column="RR_AWB_TYPE" jdbcType="VARCHAR" property="rrAwbType" />
    <result column="RR_REMARK" jdbcType="VARCHAR" property="rrRemark" />
    <result column="EAWB_REFERENCE1" jdbcType="VARCHAR" property="eawbReference1" />
    <result column="EAWB_REFERENCE2" jdbcType="VARCHAR" property="eawbReference2" />
    <result column="EAWB_CHARGEABLEWEIGHT" jdbcType="DECIMAL" property="eawbChargeableweight" />
    <result column="EAWB_HAWB_QTY" jdbcType="DECIMAL" property="eawbHawbQty" />
    <result column="RR_OCCURTIME" jdbcType="TIMESTAMP" property="rrOccurtime" />
    <result column="EP_KEY" jdbcType="OTHER" property="epKey" />
    <result column="CHARGEABLEWEIGHT" jdbcType="DECIMAL" property="chargeableweight" />
    <result column="PD_SYSCODE" jdbcType="DECIMAL" property="pdSyscode" />
    <result column="OUTBOUND_COMPANY_ID" jdbcType="VARCHAR" property="outboundCompanyId" />
    <result column="EAWB_IETYPE" jdbcType="VARCHAR" property="eawbIetype" />
    <result column="EAWB_DESTCOUNTRY" jdbcType="VARCHAR" property="eawbDestcountry" />
    <result column="EAWB_DESTINATION" jdbcType="VARCHAR" property="eawbDestination" />
    <result column="EAWB_DEPARTCOUNTRY" jdbcType="VARCHAR" property="eawbDepartcountry" />
    <result column="EAWB_DEPARTURE" jdbcType="VARCHAR" property="eawbDeparture" />
    <result column="BMS_NUM" jdbcType="VARCHAR" property="bmsNum" />
    <result column="BMS_DIRTY" jdbcType="VARCHAR" property="bmsDirty" />
    <result column="RR_OCCURTIME2" jdbcType="TIMESTAMP" property="rrOccurtime2" />
    <result column="ESTIMATE_STATUS" jdbcType="VARCHAR" property="estimateStatus" />
    <result column="UNIONID" jdbcType="VARCHAR" property="unionid" />
    <result column="COUPON_CODE" jdbcType="VARCHAR" property="couponCode" />
    <result column="COUPON_AMOUNT" jdbcType="DECIMAL" property="couponAmount" />
    <result column="ORDER_ID" jdbcType="DECIMAL" property="orderId" />
    <result column="PAY_STATUS" jdbcType="DECIMAL" property="payStatus" />
    <result column="RR_AMOUNT_ORIGINAL" jdbcType="DECIMAL" property="rrAmountOriginal" />
  </resultMap>
  <sql id="Base_Column_List">
    RR_ID, EAWB_PRINTCODE, MAWB_CODE, PR_ID, DM_ID, SO_CODE, CT_CODE, COMPANY_ID, RR_NAME, 
    RR_TYPE, RR_PLAN_AMOUNT, RR_ACTUAL_AMOUNT, RR_STATUS, RR_USER_ID, RR_HANDLETIME, 
    RR_AWB_TYPE, RR_REMARK, EAWB_REFERENCE1, EAWB_REFERENCE2, EAWB_CHARGEABLEWEIGHT, 
    EAWB_HAWB_QTY, RR_OCCURTIME, EP_KEY, CHARGEABLEWEIGHT, PD_SYSCODE, OUTBOUND_COMPANY_ID, 
    EAWB_IETYPE, EAWB_DESTCOUNTRY, EAWB_DESTINATION, EAWB_DEPARTCOUNTRY, EAWB_DEPARTURE, 
    BMS_NUM, BMS_DIRTY, RR_OCCURTIME2, ESTIMATE_STATUS, UNIONID, COUPON_CODE, COUPON_AMOUNT, 
    ORDER_ID, PAY_STATUS, RR_AMOUNT_ORIGINAL
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from APP_RECEIPT_RECORD
    where RR_ID = #{rrId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from APP_RECEIPT_RECORD
    where RR_ID = #{rrId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.AppReceiptRecord">
    insert into APP_RECEIPT_RECORD (RR_ID, EAWB_PRINTCODE, MAWB_CODE, 
      PR_ID, DM_ID, SO_CODE, 
      CT_CODE, COMPANY_ID, RR_NAME, 
      RR_TYPE, RR_PLAN_AMOUNT, RR_ACTUAL_AMOUNT, 
      RR_STATUS, RR_USER_ID, RR_HANDLETIME, 
      RR_AWB_TYPE, RR_REMARK, EAWB_REFERENCE1, 
      EAWB_REFERENCE2, EAWB_CHARGEABLEWEIGHT, EAWB_HAWB_QTY, 
      RR_OCCURTIME, EP_KEY, CHARGEABLEWEIGHT, 
      PD_SYSCODE, OUTBOUND_COMPANY_ID, EAWB_IETYPE, 
      EAWB_DESTCOUNTRY, EAWB_DESTINATION, EAWB_DEPARTCOUNTRY, 
      EAWB_DEPARTURE, BMS_NUM, BMS_DIRTY, 
      RR_OCCURTIME2, ESTIMATE_STATUS, UNIONID, 
      COUPON_CODE, COUPON_AMOUNT, ORDER_ID, 
      PAY_STATUS, RR_AMOUNT_ORIGINAL)
    values (#{rrId,jdbcType=DECIMAL}, #{eawbPrintcode,jdbcType=VARCHAR}, #{mawbCode,jdbcType=VARCHAR}, 
      #{prId,jdbcType=DECIMAL}, #{dmId,jdbcType=DECIMAL}, #{soCode,jdbcType=VARCHAR}, 
      #{ctCode,jdbcType=VARCHAR}, #{companyId,jdbcType=VARCHAR}, #{rrName,jdbcType=VARCHAR}, 
      #{rrType,jdbcType=VARCHAR}, #{rrPlanAmount,jdbcType=DECIMAL}, #{rrActualAmount,jdbcType=DECIMAL}, 
      #{rrStatus,jdbcType=VARCHAR}, #{rrUserId,jdbcType=DECIMAL}, #{rrHandletime,jdbcType=TIMESTAMP}, 
      #{rrAwbType,jdbcType=VARCHAR}, #{rrRemark,jdbcType=VARCHAR}, #{eawbReference1,jdbcType=VARCHAR}, 
      #{eawbReference2,jdbcType=VARCHAR}, #{eawbChargeableweight,jdbcType=DECIMAL}, #{eawbHawbQty,jdbcType=DECIMAL}, 
      #{rrOccurtime,jdbcType=TIMESTAMP}, #{epKey,jdbcType=OTHER}, #{chargeableweight,jdbcType=DECIMAL}, 
      #{pdSyscode,jdbcType=DECIMAL}, #{outboundCompanyId,jdbcType=VARCHAR}, #{eawbIetype,jdbcType=VARCHAR}, 
      #{eawbDestcountry,jdbcType=VARCHAR}, #{eawbDestination,jdbcType=VARCHAR}, #{eawbDepartcountry,jdbcType=VARCHAR}, 
      #{eawbDeparture,jdbcType=VARCHAR}, #{bmsNum,jdbcType=VARCHAR}, #{bmsDirty,jdbcType=VARCHAR}, 
      #{rrOccurtime2,jdbcType=TIMESTAMP}, #{estimateStatus,jdbcType=VARCHAR}, #{unionid,jdbcType=VARCHAR}, 
      #{couponCode,jdbcType=VARCHAR}, #{couponAmount,jdbcType=DECIMAL}, #{orderId,jdbcType=DECIMAL}, 
      #{payStatus,jdbcType=DECIMAL}, #{rrAmountOriginal,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.AppReceiptRecord">
    insert into APP_RECEIPT_RECORD
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="rrId != null">
        RR_ID,
      </if>
      <if test="eawbPrintcode != null">
        EAWB_PRINTCODE,
      </if>
      <if test="mawbCode != null">
        MAWB_CODE,
      </if>
      <if test="prId != null">
        PR_ID,
      </if>
      <if test="dmId != null">
        DM_ID,
      </if>
      <if test="soCode != null">
        SO_CODE,
      </if>
      <if test="ctCode != null">
        CT_CODE,
      </if>
      <if test="companyId != null">
        COMPANY_ID,
      </if>
      <if test="rrName != null">
        RR_NAME,
      </if>
      <if test="rrType != null">
        RR_TYPE,
      </if>
      <if test="rrPlanAmount != null">
        RR_PLAN_AMOUNT,
      </if>
      <if test="rrActualAmount != null">
        RR_ACTUAL_AMOUNT,
      </if>
      <if test="rrStatus != null">
        RR_STATUS,
      </if>
      <if test="rrUserId != null">
        RR_USER_ID,
      </if>
      <if test="rrHandletime != null">
        RR_HANDLETIME,
      </if>
      <if test="rrAwbType != null">
        RR_AWB_TYPE,
      </if>
      <if test="rrRemark != null">
        RR_REMARK,
      </if>
      <if test="eawbReference1 != null">
        EAWB_REFERENCE1,
      </if>
      <if test="eawbReference2 != null">
        EAWB_REFERENCE2,
      </if>
      <if test="eawbChargeableweight != null">
        EAWB_CHARGEABLEWEIGHT,
      </if>
      <if test="eawbHawbQty != null">
        EAWB_HAWB_QTY,
      </if>
      <if test="rrOccurtime != null">
        RR_OCCURTIME,
      </if>
      <if test="epKey != null">
        EP_KEY,
      </if>
      <if test="chargeableweight != null">
        CHARGEABLEWEIGHT,
      </if>
      <if test="pdSyscode != null">
        PD_SYSCODE,
      </if>
      <if test="outboundCompanyId != null">
        OUTBOUND_COMPANY_ID,
      </if>
      <if test="eawbIetype != null">
        EAWB_IETYPE,
      </if>
      <if test="eawbDestcountry != null">
        EAWB_DESTCOUNTRY,
      </if>
      <if test="eawbDestination != null">
        EAWB_DESTINATION,
      </if>
      <if test="eawbDepartcountry != null">
        EAWB_DEPARTCOUNTRY,
      </if>
      <if test="eawbDeparture != null">
        EAWB_DEPARTURE,
      </if>
      <if test="bmsNum != null">
        BMS_NUM,
      </if>
      <if test="bmsDirty != null">
        BMS_DIRTY,
      </if>
      <if test="rrOccurtime2 != null">
        RR_OCCURTIME2,
      </if>
      <if test="estimateStatus != null">
        ESTIMATE_STATUS,
      </if>
      <if test="unionid != null">
        UNIONID,
      </if>
      <if test="couponCode != null">
        COUPON_CODE,
      </if>
      <if test="couponAmount != null">
        COUPON_AMOUNT,
      </if>
      <if test="orderId != null">
        ORDER_ID,
      </if>
      <if test="payStatus != null">
        PAY_STATUS,
      </if>
      <if test="rrAmountOriginal != null">
        RR_AMOUNT_ORIGINAL,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="rrId != null">
        #{rrId,jdbcType=DECIMAL},
      </if>
      <if test="eawbPrintcode != null">
        #{eawbPrintcode,jdbcType=VARCHAR},
      </if>
      <if test="mawbCode != null">
        #{mawbCode,jdbcType=VARCHAR},
      </if>
      <if test="prId != null">
        #{prId,jdbcType=DECIMAL},
      </if>
      <if test="dmId != null">
        #{dmId,jdbcType=DECIMAL},
      </if>
      <if test="soCode != null">
        #{soCode,jdbcType=VARCHAR},
      </if>
      <if test="ctCode != null">
        #{ctCode,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="rrName != null">
        #{rrName,jdbcType=VARCHAR},
      </if>
      <if test="rrType != null">
        #{rrType,jdbcType=VARCHAR},
      </if>
      <if test="rrPlanAmount != null">
        #{rrPlanAmount,jdbcType=DECIMAL},
      </if>
      <if test="rrActualAmount != null">
        #{rrActualAmount,jdbcType=DECIMAL},
      </if>
      <if test="rrStatus != null">
        #{rrStatus,jdbcType=VARCHAR},
      </if>
      <if test="rrUserId != null">
        #{rrUserId,jdbcType=DECIMAL},
      </if>
      <if test="rrHandletime != null">
        #{rrHandletime,jdbcType=TIMESTAMP},
      </if>
      <if test="rrAwbType != null">
        #{rrAwbType,jdbcType=VARCHAR},
      </if>
      <if test="rrRemark != null">
        #{rrRemark,jdbcType=VARCHAR},
      </if>
      <if test="eawbReference1 != null">
        #{eawbReference1,jdbcType=VARCHAR},
      </if>
      <if test="eawbReference2 != null">
        #{eawbReference2,jdbcType=VARCHAR},
      </if>
      <if test="eawbChargeableweight != null">
        #{eawbChargeableweight,jdbcType=DECIMAL},
      </if>
      <if test="eawbHawbQty != null">
        #{eawbHawbQty,jdbcType=DECIMAL},
      </if>
      <if test="rrOccurtime != null">
        #{rrOccurtime,jdbcType=TIMESTAMP},
      </if>
      <if test="epKey != null">
        #{epKey,jdbcType=OTHER},
      </if>
      <if test="chargeableweight != null">
        #{chargeableweight,jdbcType=DECIMAL},
      </if>
      <if test="pdSyscode != null">
        #{pdSyscode,jdbcType=DECIMAL},
      </if>
      <if test="outboundCompanyId != null">
        #{outboundCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="eawbIetype != null">
        #{eawbIetype,jdbcType=VARCHAR},
      </if>
      <if test="eawbDestcountry != null">
        #{eawbDestcountry,jdbcType=VARCHAR},
      </if>
      <if test="eawbDestination != null">
        #{eawbDestination,jdbcType=VARCHAR},
      </if>
      <if test="eawbDepartcountry != null">
        #{eawbDepartcountry,jdbcType=VARCHAR},
      </if>
      <if test="eawbDeparture != null">
        #{eawbDeparture,jdbcType=VARCHAR},
      </if>
      <if test="bmsNum != null">
        #{bmsNum,jdbcType=VARCHAR},
      </if>
      <if test="bmsDirty != null">
        #{bmsDirty,jdbcType=VARCHAR},
      </if>
      <if test="rrOccurtime2 != null">
        #{rrOccurtime2,jdbcType=TIMESTAMP},
      </if>
      <if test="estimateStatus != null">
        #{estimateStatus,jdbcType=VARCHAR},
      </if>
      <if test="unionid != null">
        #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="couponCode != null">
        #{couponCode,jdbcType=VARCHAR},
      </if>
      <if test="couponAmount != null">
        #{couponAmount,jdbcType=DECIMAL},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=DECIMAL},
      </if>
      <if test="payStatus != null">
        #{payStatus,jdbcType=DECIMAL},
      </if>
      <if test="rrAmountOriginal != null">
        #{rrAmountOriginal,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.AppReceiptRecord">
    update APP_RECEIPT_RECORD
    <set>
      <if test="eawbPrintcode != null">
        EAWB_PRINTCODE = #{eawbPrintcode,jdbcType=VARCHAR},
      </if>
      <if test="mawbCode != null">
        MAWB_CODE = #{mawbCode,jdbcType=VARCHAR},
      </if>
      <if test="prId != null">
        PR_ID = #{prId,jdbcType=DECIMAL},
      </if>
      <if test="dmId != null">
        DM_ID = #{dmId,jdbcType=DECIMAL},
      </if>
      <if test="soCode != null">
        SO_CODE = #{soCode,jdbcType=VARCHAR},
      </if>
      <if test="ctCode != null">
        CT_CODE = #{ctCode,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        COMPANY_ID = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="rrName != null">
        RR_NAME = #{rrName,jdbcType=VARCHAR},
      </if>
      <if test="rrType != null">
        RR_TYPE = #{rrType,jdbcType=VARCHAR},
      </if>
      <if test="rrPlanAmount != null">
        RR_PLAN_AMOUNT = #{rrPlanAmount,jdbcType=DECIMAL},
      </if>
      <if test="rrActualAmount != null">
        RR_ACTUAL_AMOUNT = #{rrActualAmount,jdbcType=DECIMAL},
      </if>
      <if test="rrStatus != null">
        RR_STATUS = #{rrStatus,jdbcType=VARCHAR},
      </if>
      <if test="rrUserId != null">
        RR_USER_ID = #{rrUserId,jdbcType=DECIMAL},
      </if>
      <if test="rrHandletime != null">
        RR_HANDLETIME = #{rrHandletime,jdbcType=TIMESTAMP},
      </if>
      <if test="rrAwbType != null">
        RR_AWB_TYPE = #{rrAwbType,jdbcType=VARCHAR},
      </if>
      <if test="rrRemark != null">
        RR_REMARK = #{rrRemark,jdbcType=VARCHAR},
      </if>
      <if test="eawbReference1 != null">
        EAWB_REFERENCE1 = #{eawbReference1,jdbcType=VARCHAR},
      </if>
      <if test="eawbReference2 != null">
        EAWB_REFERENCE2 = #{eawbReference2,jdbcType=VARCHAR},
      </if>
      <if test="eawbChargeableweight != null">
        EAWB_CHARGEABLEWEIGHT = #{eawbChargeableweight,jdbcType=DECIMAL},
      </if>
      <if test="eawbHawbQty != null">
        EAWB_HAWB_QTY = #{eawbHawbQty,jdbcType=DECIMAL},
      </if>
      <if test="rrOccurtime != null">
        RR_OCCURTIME = #{rrOccurtime,jdbcType=TIMESTAMP},
      </if>
      <if test="epKey != null">
        EP_KEY = #{epKey,jdbcType=OTHER},
      </if>
      <if test="chargeableweight != null">
        CHARGEABLEWEIGHT = #{chargeableweight,jdbcType=DECIMAL},
      </if>
      <if test="pdSyscode != null">
        PD_SYSCODE = #{pdSyscode,jdbcType=DECIMAL},
      </if>
      <if test="outboundCompanyId != null">
        OUTBOUND_COMPANY_ID = #{outboundCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="eawbIetype != null">
        EAWB_IETYPE = #{eawbIetype,jdbcType=VARCHAR},
      </if>
      <if test="eawbDestcountry != null">
        EAWB_DESTCOUNTRY = #{eawbDestcountry,jdbcType=VARCHAR},
      </if>
      <if test="eawbDestination != null">
        EAWB_DESTINATION = #{eawbDestination,jdbcType=VARCHAR},
      </if>
      <if test="eawbDepartcountry != null">
        EAWB_DEPARTCOUNTRY = #{eawbDepartcountry,jdbcType=VARCHAR},
      </if>
      <if test="eawbDeparture != null">
        EAWB_DEPARTURE = #{eawbDeparture,jdbcType=VARCHAR},
      </if>
      <if test="bmsNum != null">
        BMS_NUM = #{bmsNum,jdbcType=VARCHAR},
      </if>
      <if test="bmsDirty != null">
        BMS_DIRTY = #{bmsDirty,jdbcType=VARCHAR},
      </if>
      <if test="rrOccurtime2 != null">
        RR_OCCURTIME2 = #{rrOccurtime2,jdbcType=TIMESTAMP},
      </if>
      <if test="estimateStatus != null">
        ESTIMATE_STATUS = #{estimateStatus,jdbcType=VARCHAR},
      </if>
      <if test="unionid != null">
        UNIONID = #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="couponCode != null">
        COUPON_CODE = #{couponCode,jdbcType=VARCHAR},
      </if>
      <if test="couponAmount != null">
        COUPON_AMOUNT = #{couponAmount,jdbcType=DECIMAL},
      </if>
      <if test="orderId != null">
        ORDER_ID = #{orderId,jdbcType=DECIMAL},
      </if>
      <if test="payStatus != null">
        PAY_STATUS = #{payStatus,jdbcType=DECIMAL},
      </if>
      <if test="rrAmountOriginal != null">
        RR_AMOUNT_ORIGINAL = #{rrAmountOriginal,jdbcType=DECIMAL},
      </if>
    </set>
    where RR_ID = #{rrId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.AppReceiptRecord">
    update APP_RECEIPT_RECORD
    set EAWB_PRINTCODE = #{eawbPrintcode,jdbcType=VARCHAR},
      MAWB_CODE = #{mawbCode,jdbcType=VARCHAR},
      PR_ID = #{prId,jdbcType=DECIMAL},
      DM_ID = #{dmId,jdbcType=DECIMAL},
      SO_CODE = #{soCode,jdbcType=VARCHAR},
      CT_CODE = #{ctCode,jdbcType=VARCHAR},
      COMPANY_ID = #{companyId,jdbcType=VARCHAR},
      RR_NAME = #{rrName,jdbcType=VARCHAR},
      RR_TYPE = #{rrType,jdbcType=VARCHAR},
      RR_PLAN_AMOUNT = #{rrPlanAmount,jdbcType=DECIMAL},
      RR_ACTUAL_AMOUNT = #{rrActualAmount,jdbcType=DECIMAL},
      RR_STATUS = #{rrStatus,jdbcType=VARCHAR},
      RR_USER_ID = #{rrUserId,jdbcType=DECIMAL},
      RR_HANDLETIME = #{rrHandletime,jdbcType=TIMESTAMP},
      RR_AWB_TYPE = #{rrAwbType,jdbcType=VARCHAR},
      RR_REMARK = #{rrRemark,jdbcType=VARCHAR},
      EAWB_REFERENCE1 = #{eawbReference1,jdbcType=VARCHAR},
      EAWB_REFERENCE2 = #{eawbReference2,jdbcType=VARCHAR},
      EAWB_CHARGEABLEWEIGHT = #{eawbChargeableweight,jdbcType=DECIMAL},
      EAWB_HAWB_QTY = #{eawbHawbQty,jdbcType=DECIMAL},
      RR_OCCURTIME = #{rrOccurtime,jdbcType=TIMESTAMP},
      EP_KEY = #{epKey,jdbcType=OTHER},
      CHARGEABLEWEIGHT = #{chargeableweight,jdbcType=DECIMAL},
      PD_SYSCODE = #{pdSyscode,jdbcType=DECIMAL},
      OUTBOUND_COMPANY_ID = #{outboundCompanyId,jdbcType=VARCHAR},
      EAWB_IETYPE = #{eawbIetype,jdbcType=VARCHAR},
      EAWB_DESTCOUNTRY = #{eawbDestcountry,jdbcType=VARCHAR},
      EAWB_DESTINATION = #{eawbDestination,jdbcType=VARCHAR},
      EAWB_DEPARTCOUNTRY = #{eawbDepartcountry,jdbcType=VARCHAR},
      EAWB_DEPARTURE = #{eawbDeparture,jdbcType=VARCHAR},
      BMS_NUM = #{bmsNum,jdbcType=VARCHAR},
      BMS_DIRTY = #{bmsDirty,jdbcType=VARCHAR},
      RR_OCCURTIME2 = #{rrOccurtime2,jdbcType=TIMESTAMP},
      ESTIMATE_STATUS = #{estimateStatus,jdbcType=VARCHAR},
      UNIONID = #{unionid,jdbcType=VARCHAR},
      COUPON_CODE = #{couponCode,jdbcType=VARCHAR},
      COUPON_AMOUNT = #{couponAmount,jdbcType=DECIMAL},
      ORDER_ID = #{orderId,jdbcType=DECIMAL},
      PAY_STATUS = #{payStatus,jdbcType=DECIMAL},
      RR_AMOUNT_ORIGINAL = #{rrAmountOriginal,jdbcType=DECIMAL}
    where RR_ID = #{rrId,jdbcType=DECIMAL}
  </update>

  <select id="selectListByDmId" resultMap="BaseResultMap">
    select arr.* from app_receipt_record arr where arr.dm_id=#{dmId}
  </select>
</mapper>