<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.BillManifestMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.BillManifest" >
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="M_ID" property="mId" jdbcType="DECIMAL" />
    <result column="DM_ID" property="dmId" jdbcType="DECIMAL" />
    <result column="CM_ID" property="cmId" jdbcType="DECIMAL" />
    <result column="BM_HANDLETIME" property="bmHandletime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, M_ID, DM_ID, CM_ID, BM_HANDLETIME
  </sql>
  <sql id='TABLE_SEQUENCE'>SEQ_MANIFEST_LIST.NEXTVAL</sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from BILL_MANIFEST
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from BILL_MANIFEST
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.BillManifest" >
    <selectKey keyProperty="id" resultType="long" order="BEFORE">
      select
      <include refid="TABLE_SEQUENCE"/>
      from dual
    </selectKey>
    insert into BILL_MANIFEST (ID, M_ID, DM_ID, 
      CM_ID, BM_HANDLETIME)
    values (#{id,jdbcType=DECIMAL}, #{mId,jdbcType=DECIMAL}, #{dmId,jdbcType=DECIMAL}, 
      #{cmId,jdbcType=DECIMAL}, #{bmHandletime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.BillManifest" >
    <selectKey keyProperty="id" resultType="long" order="BEFORE">
      select
      <include refid="TABLE_SEQUENCE"/>
      from dual
    </selectKey>
    insert into BILL_MANIFEST
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="mId != null" >
        M_ID,
      </if>
      <if test="dmId != null" >
        DM_ID,
      </if>
      <if test="cmId != null" >
        CM_ID,
      </if>
      <if test="bmHandletime != null" >
        BM_HANDLETIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="mId != null" >
        #{mId,jdbcType=DECIMAL},
      </if>
      <if test="dmId != null" >
        #{dmId,jdbcType=DECIMAL},
      </if>
      <if test="cmId != null" >
        #{cmId,jdbcType=DECIMAL},
      </if>
      <if test="bmHandletime != null" >
        #{bmHandletime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.BillManifest" >
    update BILL_MANIFEST
    <set >
      <if test="mId != null" >
        M_ID = #{mId,jdbcType=DECIMAL},
      </if>
      <if test="dmId != null" >
        DM_ID = #{dmId,jdbcType=DECIMAL},
      </if>
      <if test="cmId != null" >
        CM_ID = #{cmId,jdbcType=DECIMAL},
      </if>
      <if test="bmHandletime != null" >
        BM_HANDLETIME = #{bmHandletime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.BillManifest" >
    update BILL_MANIFEST
    set M_ID = #{mId,jdbcType=DECIMAL},
      DM_ID = #{dmId,jdbcType=DECIMAL},
      CM_ID = #{cmId,jdbcType=DECIMAL},
      BM_HANDLETIME = #{bmHandletime,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=DECIMAL}
  </update>

  <delete id="deleteByManifestId" parameterType="java.lang.Long" >
    delete from BILL_MANIFEST
    where M_ID = #{mId,jdbcType=DECIMAL}
  </delete>

  <select id="listBillManifestByMId" resultType="com.sinoair.billing.domain.vo.manifest.RelationDebitVO" parameterType="java.lang.Long" >
    select
    bm.m_id,dm.*,stt.SO_NAME,ct.CT_NAME
    from BILL_MANIFEST bm,debit_manifest dm,currencytype ct, settlementobject stt
    where bm.dm_id = dm.dm_id
      and dm.so_code = stt.so_code
      and dm.ct_code = ct.ct_code
      and bm.m_id = #{mId,jdbcType=DECIMAL}
  </select>

  <select id="listBillCreditManifestByMId" resultType="com.sinoair.billing.domain.vo.manifest.RelationCreditVO" parameterType="java.lang.Long" >
    select
    bm.m_id,cm.*,s.SP_NAME as soName,ct.CT_NAME
    from BILL_MANIFEST bm,credit_manifest cm,currencytype ct, supplier s
    where bm.cm_id = cm.cm_id
    and cm.so_code = s.sp_code
    and cm.ct_code = ct.ct_code
    and bm.m_id = #{mId,jdbcType=DECIMAL}
  </select>

    <select id="listDebitManifest" resultType="com.sinoair.billing.domain.vo.manifest.RelationDebitVO" parameterType="com.sinoair.billing.domain.model.billing.DebitManifest" >
      select
        dm.*,stt.SO_NAME,ct.CT_NAME
      from debit_manifest dm,currencytype ct, settlementobject stt
      where dm.so_code = stt.so_code
      and dm.ct_code = ct.ct_code
      and dm.dm_status != 'OFF'
      and dm.company_id = #{companyId}
      and not exists (select * from bill_manifest bm,manifest_list ml where bm.m_id = ml.m_id and bm.dm_id = dm.dm_id)
      order by dm.dm_start_time desc,dm.dm_id
    </select>

    <select id="listCreditManifest" resultType="com.sinoair.billing.domain.vo.manifest.RelationCreditVO" parameterType="com.sinoair.billing.domain.model.billing.CreditManifest" >
        select
        cm.*,s.SP_NAME as soName,ct.CT_NAME
        from credit_manifest cm,currencytype ct, SUPPLIER s
        where cm.so_code = s.SP_CODE
        and cm.ct_code = ct.ct_code
        and cm.cm_status != 'OFF'
        and cm.CM_TYPE!='BALANCE'
        and cm.company_id = #{companyId}
        and not exists (select * from bill_manifest bm,manifest_list ml where bm.m_id = ml.m_id and bm.cm_id = cm.cm_id)
        order by cm.CM_START_TIME desc
    </select>

  <delete id="deleteByDmId" parameterType="java.util.HashMap" >
    delete from BILL_MANIFEST
    where m_id = #{mId,jdbcType=DECIMAL}
    and dm_id in
    <foreach collection="dmIdList" item="dmId" open="(" separator="," close=")">
      #{dmId}
    </foreach>
  </delete>

  <delete id="deleteByCmId" parameterType="java.util.HashMap" >
    delete from BILL_MANIFEST
    where m_id = #{mId,jdbcType=DECIMAL}
    and cm_id in
    <foreach collection="cmIdList" item="cmId" open="(" separator="," close=")">
      #{cmId}
    </foreach>
  </delete>

  <select id="selectDmByMId" resultType="java.lang.Long" parameterType="java.lang.Long" >
    select
    DM_ID
    from BILL_MANIFEST
    where M_ID = #{mId,jdbcType=DECIMAL}
  </select>
</mapper>