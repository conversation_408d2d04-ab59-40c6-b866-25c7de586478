<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.BillingDetailsMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.BillingDetails" >
    <id column="B_SYSCODE" property="bSyscode" jdbcType="DECIMAL" />
    <result column="B_ID" property="bId" jdbcType="DECIMAL" />
    <result column="B_TYPE" property="bType" jdbcType="VARCHAR" />
    <result column="DIFF_TYPE" property="diffType" jdbcType="VARCHAR" />
    <result column="DIFF_WEIGHT" property="diffWeight" jdbcType="DECIMAL" />
    <result column="DIFF_QUANTITY" property="diffQuantity" jdbcType="DECIMAL" />
    <result column="DIFF_AMOUNT" property="diffAmount" jdbcType="DECIMAL" />
    <result column="DIFF_REASON" property="diffReason" jdbcType="VARCHAR" />
    <result column="REPLY_STATUS" property="replyStatus" jdbcType="VARCHAR" />
    <result column="REPLY_REMARKS" property="replyRemarks" jdbcType="VARCHAR" />
    <result column="REPLY_PERSON" property="replyPerson" jdbcType="VARCHAR" />
    <result column="REPLY_TIME" property="replyTime" jdbcType="TIMESTAMP" />
    <result column="DIFF_HANDLETIME" property="diffHandletime" jdbcType="TIMESTAMP" />
    <result column="DIFF_USER_ID" property="diffUserId" jdbcType="DECIMAL" />
    <result column="CONFIRM_AMOUNT" property="confirmAmount" jdbcType="DECIMAL" />
    <result column="IS_CLOSE" property="isClose" jdbcType="VARCHAR" />
    <result column="B_CODE" property="bCode" jdbcType="VARCHAR" />
    <result column="CT_CODE" property="ctCode" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    B_SYSCODE, B_ID, B_TYPE, DIFF_TYPE, DIFF_WEIGHT, DIFF_QUANTITY, DIFF_AMOUNT, DIFF_REASON,
    REPLY_STATUS, REPLY_REMARKS, REPLY_PERSON, REPLY_TIME, DIFF_HANDLETIME, DIFF_USER_ID,
    CONFIRM_AMOUNT, IS_CLOSE, B_CODE, CT_CODE
  </sql>
  <!-- 序列 -->
  <sql id='TABLE_SEQUENCE'>SEQ_BILLING_DETAILS.NEXTVAL</sql>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Short" >
    select
    <include refid="Base_Column_List" />
    from BILLING_DETAILS
    where B_SYSCODE = #{bSyscode,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Short" >
    delete from BILLING_DETAILS
    where B_SYSCODE = #{bSyscode,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.BillingDetails" >
    <selectKey keyProperty="bSyscode" resultType="int" order="BEFORE">
      select <include refid="TABLE_SEQUENCE" /> from dual
    </selectKey>
    from dual
    insert into BILLING_DETAILS (B_SYSCODE, B_ID, B_TYPE,
    DIFF_TYPE, DIFF_WEIGHT, DIFF_QUANTITY,
    DIFF_AMOUNT, DIFF_REASON, REPLY_STATUS,
    REPLY_REMARKS, REPLY_PERSON, REPLY_TIME,
    DIFF_HANDLETIME, DIFF_USER_ID, CONFIRM_AMOUNT,
    IS_CLOSE, B_CODE, CT_CODE
    )
    values (#{bSyscode,jdbcType=DECIMAL}, #{bId,jdbcType=DECIMAL}, #{bType,jdbcType=VARCHAR},
    #{diffType,jdbcType=VARCHAR}, #{diffWeight,jdbcType=DECIMAL}, #{diffQuantity,jdbcType=DECIMAL},
    #{diffAmount,jdbcType=DECIMAL}, #{diffReason,jdbcType=VARCHAR}, #{replyStatus,jdbcType=VARCHAR},
    #{replyRemarks,jdbcType=VARCHAR}, #{replyPerson,jdbcType=VARCHAR}, #{replyTime,jdbcType=TIMESTAMP},
    #{diffHandletime,jdbcType=TIMESTAMP}, #{diffUserId,jdbcType=DECIMAL}, #{confirmAmount,jdbcType=DECIMAL},
    #{isClose,jdbcType=VARCHAR}, #{bCode,jdbcType=VARCHAR}, #{ctCode,jdbcType=VARCHAR}
    )
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.BillingDetails" >
    <selectKey keyProperty="bSyscode" resultType="int" order="BEFORE">
      select <include refid="TABLE_SEQUENCE" /> from dual
    </selectKey>

    insert into BILLING_DETAILS
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="bSyscode != null" >
        B_SYSCODE,
      </if>
      <if test="bId != null" >
        B_ID,
      </if>
      <if test="bType != null" >
        B_TYPE,
      </if>
      <if test="diffType != null" >
        DIFF_TYPE,
      </if>
      <if test="diffWeight != null" >
        DIFF_WEIGHT,
      </if>
      <if test="diffQuantity != null" >
        DIFF_QUANTITY,
      </if>
      <if test="diffAmount != null" >
        DIFF_AMOUNT,
      </if>
      <if test="diffReason != null" >
        DIFF_REASON,
      </if>
      <if test="replyStatus != null" >
        REPLY_STATUS,
      </if>
      <if test="replyRemarks != null" >
        REPLY_REMARKS,
      </if>
      <if test="replyPerson != null" >
        REPLY_PERSON,
      </if>
      <if test="replyTime != null" >
        REPLY_TIME,
      </if>
      <if test="diffHandletime != null" >
        DIFF_HANDLETIME,
      </if>
      <if test="diffUserId != null" >
        DIFF_USER_ID,
      </if>
      <if test="confirmAmount != null" >
        CONFIRM_AMOUNT,
      </if>
      <if test="isClose != null" >
        IS_CLOSE,
      </if>
      <if test="bCode != null" >
        B_CODE,
      </if>
      <if test="ctCode != null" >
        CT_CODE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="bSyscode != null" >
        #{bSyscode,jdbcType=DECIMAL},
      </if>
      <if test="bId != null" >
        #{bId,jdbcType=DECIMAL},
      </if>
      <if test="bType != null" >
        #{bType,jdbcType=VARCHAR},
      </if>
      <if test="diffType != null" >
        #{diffType,jdbcType=VARCHAR},
      </if>
      <if test="diffWeight != null" >
        #{diffWeight,jdbcType=DECIMAL},
      </if>
      <if test="diffQuantity != null" >
        #{diffQuantity,jdbcType=DECIMAL},
      </if>
      <if test="diffAmount != null" >
        #{diffAmount,jdbcType=DECIMAL},
      </if>
      <if test="diffReason != null" >
        #{diffReason,jdbcType=VARCHAR},
      </if>
      <if test="replyStatus != null" >
        #{replyStatus,jdbcType=VARCHAR},
      </if>
      <if test="replyRemarks != null" >
        #{replyRemarks,jdbcType=VARCHAR},
      </if>
      <if test="replyPerson != null" >
        #{replyPerson,jdbcType=VARCHAR},
      </if>
      <if test="replyTime != null" >
        #{replyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="diffHandletime != null" >
        #{diffHandletime,jdbcType=TIMESTAMP},
      </if>
      <if test="diffUserId != null" >
        #{diffUserId,jdbcType=DECIMAL},
      </if>
      <if test="confirmAmount != null" >
        #{confirmAmount,jdbcType=DECIMAL},
      </if>
      <if test="isClose != null" >
        #{isClose,jdbcType=VARCHAR},
      </if>
      <if test="bCode != null" >
        #{bCode,jdbcType=VARCHAR},
      </if>
      <if test="ctCode != null" >
        #{ctCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.BillingDetails" >
    update BILLING_DETAILS
    <set >
      <if test="bId != null" >
        B_ID = #{bId,jdbcType=DECIMAL},
      </if>
      <if test="bType != null" >
        B_TYPE = #{bType,jdbcType=VARCHAR},
      </if>
      <if test="diffType != null" >
        DIFF_TYPE = #{diffType,jdbcType=VARCHAR},
      </if>
      <if test="diffWeight != null" >
        DIFF_WEIGHT = #{diffWeight,jdbcType=DECIMAL},
      </if>
      <if test="diffQuantity != null" >
        DIFF_QUANTITY = #{diffQuantity,jdbcType=DECIMAL},
      </if>
      <if test="diffAmount != null" >
        DIFF_AMOUNT = #{diffAmount,jdbcType=DECIMAL},
      </if>
      <if test="diffReason != null" >
        DIFF_REASON = #{diffReason,jdbcType=VARCHAR},
      </if>
      <if test="replyStatus != null" >
        REPLY_STATUS = #{replyStatus,jdbcType=VARCHAR},
      </if>
      <if test="replyRemarks != null" >
        REPLY_REMARKS = #{replyRemarks,jdbcType=VARCHAR},
      </if>
      <if test="replyPerson != null" >
        REPLY_PERSON = #{replyPerson,jdbcType=VARCHAR},
      </if>
      <if test="replyTime != null" >
        REPLY_TIME = #{replyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="diffHandletime != null" >
        DIFF_HANDLETIME = #{diffHandletime,jdbcType=TIMESTAMP},
      </if>
      <if test="diffUserId != null" >
        DIFF_USER_ID = #{diffUserId,jdbcType=DECIMAL},
      </if>
      <if test="confirmAmount != null" >
        CONFIRM_AMOUNT = #{confirmAmount,jdbcType=DECIMAL},
      </if>
      <if test="isClose != null" >
        IS_CLOSE = #{isClose,jdbcType=VARCHAR},
      </if>
      <if test="bCode != null" >
        B_CODE = #{bCode,jdbcType=VARCHAR},
      </if>
      <if test="ctCode != null" >
        CT_CODE = #{ctCode,jdbcType=VARCHAR},
      </if>
    </set>
    where B_SYSCODE = #{bSyscode,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.BillingDetails" >
    update BILLING_DETAILS
    set B_ID = #{bId,jdbcType=DECIMAL},
      B_TYPE = #{bType,jdbcType=VARCHAR},
      DIFF_TYPE = #{diffType,jdbcType=VARCHAR},
      DIFF_WEIGHT = #{diffWeight,jdbcType=DECIMAL},
      DIFF_QUANTITY = #{diffQuantity,jdbcType=DECIMAL},
      DIFF_AMOUNT = #{diffAmount,jdbcType=DECIMAL},
      DIFF_REASON = #{diffReason,jdbcType=VARCHAR},
      REPLY_STATUS = #{replyStatus,jdbcType=VARCHAR},
      REPLY_REMARKS = #{replyRemarks,jdbcType=VARCHAR},
      REPLY_PERSON = #{replyPerson,jdbcType=VARCHAR},
      REPLY_TIME = #{replyTime,jdbcType=TIMESTAMP},
      DIFF_HANDLETIME = #{diffHandletime,jdbcType=TIMESTAMP},
      DIFF_USER_ID = #{diffUserId,jdbcType=DECIMAL},
      CONFIRM_AMOUNT = #{confirmAmount,jdbcType=DECIMAL},
      IS_CLOSE = #{isClose,jdbcType=VARCHAR},
      B_CODE = #{bCode,jdbcType=VARCHAR},
      CT_CODE = #{ctCode,jdbcType=VARCHAR}
    where B_SYSCODE = #{bSyscode,jdbcType=DECIMAL}
  </update>

  <select id="querydiff" resultType="java.util.Map">
    select bd.b_syscode as b_syscode,
    bd.b_id as b_id,
    bd.b_code as b_code,
      case
        when bd.b_type = '应收' then '应收'
        when bd.b_type = '应付' then '应付'
      end as b_type,
      case
        when substr(bd.diff_type, 0, 2) = '其它' then
           substr(bd.diff_type,4)
        when substr(bd.diff_type, 0, 2) != '其它' then
           bd.diff_type
      end as diff_type,
      bd.diff_weight as diff_weight,
      bd.diff_quantity as diff_quantity,
      bd.diff_amount as diff_amount,
      bd.diff_reason as diff_reason,
      bd.reply_status as reply_status,
      bd.reply_person as reply_person,
      to_char(bd.reply_time, 'yyyy/mm/dd') as reply_time,
      bd.confirm_amount as confirm_amount,
      to_char(bd.diff_handletime, 'yyyy/mm/dd') as diff_handletime,
      (select u.username from users u where u.id = bd.diff_user_id) username,
      case
        when bd.is_close = 'Y' then '已关闭'
        when bd.is_close = 'N' then '未关闭'
      end as is_close,
      (select ct.ct_name from currencytype ct where ct.ct_code = bd.ct_code) ct_name
      from billing_details bd
       where 1=1
       <if test="b_code!=null and b_code!=''">
         and bd.b_id in (select dm.dm_id
         from debit_manifest dm
         where dm.dm_code like '%'|| #{b_code} ||'%')
       </if>
       <if test="is_close!=null and is_close!=''">
          and bd.is_close=#{is_close}
       </if>
     order by bd.B_SYSCODE  desc
  </select>


  <select id="selectByBId" resultType="java.util.Map">
      select bd.B_SYSCODE,
         bd.B_ID,
         bd.B_TYPE,
         bd.DIFF_TYPE,
         bd.DIFF_WEIGHT,
         bd.DIFF_QUANTITY,
         bd.DIFF_AMOUNT,
         bd.DIFF_REASON,
         bd.REPLY_STATUS,
         bd.REPLY_REMARKS,
         bd.REPLY_PERSON,
         bd.REPLY_TIME,
         bd.DIFF_HANDLETIME,
         (select u.username from users u where u.id = bd.diff_user_id) username,
         bd.CONFIRM_AMOUNT,
         bd.IS_CLOSE,
         bd.CT_CODE
    from billing_details bd
     where bd.b_syscode =#{bSyscode}
  </select>
</mapper>