<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.BmsManifestDetailMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.BmsManifestDetail" >
    <id column="BMD_SYSCODE" property="bmdSyscode" jdbcType="DECIMAL" />
    <result column="DM_ID" property="dmId" jdbcType="DECIMAL" />
    <result column="EP_KEY" property="epKey" jdbcType="VARCHAR" />
    <result column="SINOTRANS_ID" property="sinotransId" jdbcType="VARCHAR" />
    <result column="RECEIPT_AMOUNT" property="receiptAmount" jdbcType="DECIMAL" />
    <result column="SAC_ID" property="sacId" jdbcType="VARCHAR" />
    <result column="SO_CODE" property="soCode" jdbcType="VARCHAR" />
    <result column="BMD_HANDLETIME" property="bmdHandletime" jdbcType="TIMESTAMP" />
    <result column="CT_SIGN" property="ctSign" jdbcType="VARCHAR" />
    <result column="DM_MONTH" property="dmMonth" jdbcType="DECIMAL" />
  </resultMap>
  <sql id="Base_Column_List" >
    BMD_SYSCODE, DM_ID, EP_KEY, SINOTRANS_ID, RECEIPT_AMOUNT, SAC_ID, SO_CODE, BMD_HANDLETIME,
    CT_SIGN, DM_MONTH
  </sql>
  <sql id='TABLE_SEQUENCE'>SEQ_MANIFEST_LIST.NEXTVAL</sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from BMS_MANIFEST_DETAIL
    where BMD_SYSCODE = #{bmdSyscode,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from BMS_MANIFEST_DETAIL
    where BMD_SYSCODE = #{bmdSyscode,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.BmsManifestDetail" >
    <selectKey keyProperty="bmdSyscode" resultType="long" order="BEFORE">
      select
      <include refid="TABLE_SEQUENCE"/>
      from dual
    </selectKey>
    insert into BMS_MANIFEST_DETAIL (BMD_SYSCODE, DM_ID, EP_KEY,
    SINOTRANS_ID, RECEIPT_AMOUNT, SAC_ID,
    SO_CODE, BMD_HANDLETIME, CT_SIGN,
    DM_MONTH)
    values (#{bmdSyscode,jdbcType=DECIMAL}, #{dmId,jdbcType=DECIMAL}, #{epKey,jdbcType=VARCHAR},
    #{sinotransId,jdbcType=VARCHAR}, #{receiptAmount,jdbcType=DECIMAL}, #{sacId,jdbcType=VARCHAR},
    #{soCode,jdbcType=VARCHAR}, #{bmdHandletime,jdbcType=TIMESTAMP}, #{ctSign,jdbcType=VARCHAR},
    #{dmMonth,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.BmsManifestDetail" >
    <selectKey keyProperty="bmdSyscode" resultType="long" order="BEFORE">
      select
      <include refid="TABLE_SEQUENCE"/>
      from dual
    </selectKey>
    insert into BMS_MANIFEST_DETAIL
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="bmdSyscode != null" >
        BMD_SYSCODE,
      </if>
      <if test="dmId != null" >
        DM_ID,
      </if>
      <if test="epKey != null" >
        EP_KEY,
      </if>
      <if test="sinotransId != null" >
        SINOTRANS_ID,
      </if>
      <if test="receiptAmount != null" >
        RECEIPT_AMOUNT,
      </if>
      <if test="sacId != null" >
        SAC_ID,
      </if>
      <if test="soCode != null" >
        SO_CODE,
      </if>
      <if test="bmdHandletime != null" >
        BMD_HANDLETIME,
      </if>
      <if test="ctSign != null" >
        CT_SIGN,
      </if>
      <if test="dmMonth != null" >
        DM_MONTH,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="bmdSyscode != null" >
        #{bmdSyscode,jdbcType=DECIMAL},
      </if>
      <if test="dmId != null" >
        #{dmId,jdbcType=DECIMAL},
      </if>
      <if test="epKey != null" >
        #{epKey,jdbcType=VARCHAR},
      </if>
      <if test="sinotransId != null" >
        #{sinotransId,jdbcType=VARCHAR},
      </if>
      <if test="receiptAmount != null" >
        #{receiptAmount,jdbcType=DECIMAL},
      </if>
      <if test="sacId != null" >
        #{sacId,jdbcType=VARCHAR},
      </if>
      <if test="soCode != null" >
        #{soCode,jdbcType=VARCHAR},
      </if>
      <if test="bmdHandletime != null" >
        #{bmdHandletime,jdbcType=TIMESTAMP},
      </if>
      <if test="ctSign != null" >
        #{ctSign,jdbcType=VARCHAR},
      </if>
      <if test="dmMonth != null" >
        #{dmMonth,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.BmsManifestDetail" >
    update BMS_MANIFEST_DETAIL
    <set >
      <if test="dmId != null" >
        DM_ID = #{dmId,jdbcType=DECIMAL},
      </if>
      <if test="epKey != null" >
        EP_KEY = #{epKey,jdbcType=VARCHAR},
      </if>
      <if test="sinotransId != null" >
        SINOTRANS_ID = #{sinotransId,jdbcType=VARCHAR},
      </if>
      <if test="receiptAmount != null" >
        RECEIPT_AMOUNT = #{receiptAmount,jdbcType=DECIMAL},
      </if>
      <if test="sacId != null" >
        SAC_ID = #{sacId,jdbcType=VARCHAR},
      </if>
      <if test="soCode != null" >
        SO_CODE = #{soCode,jdbcType=VARCHAR},
      </if>
      <if test="bmdHandletime != null" >
        BMD_HANDLETIME = #{bmdHandletime,jdbcType=TIMESTAMP},
      </if>
      <if test="ctSign != null" >
        CT_SIGN = #{ctSign,jdbcType=VARCHAR},
      </if>
      <if test="dmMonth != null" >
        DM_MONTH = #{dmMonth,jdbcType=DECIMAL},
      </if>
    </set>
    where BMD_SYSCODE = #{bmdSyscode,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.BmsManifestDetail" >
    update BMS_MANIFEST_DETAIL
    set DM_ID = #{dmId,jdbcType=DECIMAL},
      EP_KEY = #{epKey,jdbcType=VARCHAR},
      SINOTRANS_ID = #{sinotransId,jdbcType=VARCHAR},
      RECEIPT_AMOUNT = #{receiptAmount,jdbcType=DECIMAL},
      SAC_ID = #{sacId,jdbcType=VARCHAR},
      SO_CODE = #{soCode,jdbcType=VARCHAR},
      BMD_HANDLETIME = #{bmdHandletime,jdbcType=TIMESTAMP},
      CT_SIGN = #{ctSign,jdbcType=VARCHAR},
      DM_MONTH = #{dmMonth,jdbcType=DECIMAL}
    where BMD_SYSCODE = #{bmdSyscode,jdbcType=DECIMAL}
  </update>
</mapper>