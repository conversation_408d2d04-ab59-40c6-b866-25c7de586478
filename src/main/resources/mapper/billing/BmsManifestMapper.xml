<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.BmsManifestMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.BmsManifest" >
    <id column="BM_SYSCODE" property="bmSyscode" jdbcType="DECIMAL" />
    <result column="COMPANY_ID" property="companyId" jdbcType="VARCHAR" />
    <result column="SO_CODE" property="soCode" jdbcType="VARCHAR" />
    <result column="SINOTRANS_ID" property="sinotransId" jdbcType="VARCHAR" />
    <result column="IK_NAME" property="ikName" jdbcType="VARCHAR" />
    <result column="CT_SIGN" property="ctSign" jdbcType="VARCHAR" />
    <result column="CT_RATE" property="ctRate" jdbcType="DECIMAL" />
    <result column="BM_TOTALRMB" property="bmTotalrmb" jdbcType="DECIMAL" />
    <result column="BM_TOTALFC" property="bmTotalfc" jdbcType="DECIMAL" />
    <result column="TAX_AMOUNT" property="taxAmount" jdbcType="DECIMAL" />
    <result column="NOTAX_AMOUNT" property="notaxAmount" jdbcType="DECIMAL" />
    <result column="TAX_AMOUNT_FC" property="taxAmountFc" jdbcType="DECIMAL" />
    <result column="NOTAX_AMOUNT_FC" property="notaxAmountFc" jdbcType="DECIMAL" />
    <result column="BM_PIECE" property="bmPiece" jdbcType="DECIMAL" />
    <result column="BM_CHARGEABLEWEIGHT" property="bmChargeableweight" jdbcType="DECIMAL" />
    <result column="COMPLETION_DATE" property="completionDate" jdbcType="TIMESTAMP" />
    <result column="INDENTIFIER_RC" property="indentifierRc" jdbcType="VARCHAR" />
    <result column="INVOICE_TYPE" property="invoiceType" jdbcType="VARCHAR" />
    <result column="INVOICE_NUM" property="invoiceNum" jdbcType="VARCHAR" />
    <result column="TAX_RATE" property="taxRate" jdbcType="DECIMAL" />
    <result column="SO_NAME" property="soName" jdbcType="VARCHAR" />
    <result column="BM_HANDLE_TIME" property="bmHandleTime" jdbcType="TIMESTAMP" />
    <result column="M_ID" property="mId" jdbcType="DECIMAL" />
    <result column="M_CODE" property="mCode" jdbcType="VARCHAR" />
    <result column="REMARK" property="remark" jdbcType="VARCHAR" />
    <result column="IE_TYPE" property="ieType" jdbcType="VARCHAR" />
    <result column="RECEIPT_AMOUNT" property="receiptAmount" jdbcType="DECIMAL" />
    <result column="BUSINESS_TYPE" property="businessType" jdbcType="VARCHAR" />
    <result column="BM_TYPE" property="bmType" jdbcType="VARCHAR" />
    <result column="SALESMAN" property="salesman" jdbcType="VARCHAR" />
    <result column="SALESMAN_CODE" property="salesmanCode" jdbcType="VARCHAR" />
    <result column="EP_KEY" property="epKey" jdbcType="VARCHAR" />
    <result column="EP_VALUE" property="epValue" jdbcType="VARCHAR" />
    <result column="DEPARTURE_CODE" property="departureCode" jdbcType="VARCHAR" />
    <result column="DEST_CODE" property="destCode" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    BM_SYSCODE, COMPANY_ID, SO_CODE, SINOTRANS_ID, IK_NAME, CT_SIGN, CT_RATE, BM_TOTALRMB, 
    BM_TOTALFC, TAX_AMOUNT, NOTAX_AMOUNT, TAX_AMOUNT_FC, NOTAX_AMOUNT_FC, BM_PIECE, BM_CHARGEABLEWEIGHT, 
    COMPLETION_DATE, INDENTIFIER_RC, INVOICE_TYPE, INVOICE_NUM, TAX_RATE, SO_NAME, BM_HANDLE_TIME, 
    M_ID, M_CODE, REMARK, IE_TYPE, RECEIPT_AMOUNT, BUSINESS_TYPE, BM_TYPE, SALESMAN, 
    SALESMAN_CODE,EP_KEY,EP_VALUE,DEPARTURE_CODE,DEST_CODE
  </sql>
  <sql id='TABLE_SEQUENCE'>SEQ_MANIFEST_LIST.NEXTVAL</sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from BMS_MANIFEST
    where BM_SYSCODE = #{bmSyscode,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from BMS_MANIFEST
    where BM_SYSCODE = #{bmSyscode,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.BmsManifest" >
    <selectKey keyProperty="bmSyscode" resultType="long" order="BEFORE">
      select
      <include refid="TABLE_SEQUENCE"/>
      from dual
    </selectKey>
    insert into BMS_MANIFEST (BM_SYSCODE, COMPANY_ID, SO_CODE, 
      SINOTRANS_ID, IK_NAME, CT_SIGN, 
      CT_RATE, BM_TOTALRMB, BM_TOTALFC, 
      TAX_AMOUNT, NOTAX_AMOUNT, TAX_AMOUNT_FC, 
      NOTAX_AMOUNT_FC, BM_PIECE, BM_CHARGEABLEWEIGHT, 
      COMPLETION_DATE, INDENTIFIER_RC, INVOICE_TYPE, 
      INVOICE_NUM, TAX_RATE, SO_NAME, 
      BM_HANDLE_TIME, M_ID, M_CODE, 
      REMARK, IE_TYPE, RECEIPT_AMOUNT, 
      BUSINESS_TYPE, BM_TYPE, SALESMAN, 
      SALESMAN_CODE,EP_KEY,EP_VALUE,DEPARTURE_CODE,DEST_CODE)
    values (#{bmSyscode,jdbcType=DECIMAL}, #{companyId,jdbcType=VARCHAR}, #{soCode,jdbcType=VARCHAR}, 
      #{sinotransId,jdbcType=VARCHAR}, #{ikName,jdbcType=VARCHAR}, #{ctSign,jdbcType=VARCHAR}, 
      #{ctRate,jdbcType=DECIMAL}, #{bmTotalrmb,jdbcType=DECIMAL}, #{bmTotalfc,jdbcType=DECIMAL}, 
      #{taxAmount,jdbcType=DECIMAL}, #{notaxAmount,jdbcType=DECIMAL}, #{taxAmountFc,jdbcType=DECIMAL}, 
      #{notaxAmountFc,jdbcType=DECIMAL}, #{bmPiece,jdbcType=DECIMAL}, #{bmChargeableweight,jdbcType=DECIMAL}, 
      #{completionDate,jdbcType=TIMESTAMP}, #{indentifierRc,jdbcType=VARCHAR}, #{invoiceType,jdbcType=VARCHAR}, 
      #{invoiceNum,jdbcType=VARCHAR}, #{taxRate,jdbcType=DECIMAL}, #{soName,jdbcType=VARCHAR}, 
      #{bmHandleTime,jdbcType=TIMESTAMP}, #{mId,jdbcType=DECIMAL}, #{mCode,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{ieType,jdbcType=VARCHAR}, #{receiptAmount,jdbcType=DECIMAL}, 
      #{businessType,jdbcType=VARCHAR}, #{bmType,jdbcType=VARCHAR}, #{salesman,jdbcType=VARCHAR}, 
      #{salesmanCode,jdbcType=VARCHAR},#{epKey,jdbcType=VARCHAR},#{epValue,jdbcType=VARCHAR},
      #{departureCode,jdbcType=VARCHAR},#{destCode,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.BmsManifest" >
    <selectKey keyProperty="bmSyscode" resultType="long" order="BEFORE">
      select
      <include refid="TABLE_SEQUENCE"/>
      from dual
    </selectKey>
    insert into BMS_MANIFEST
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="bmSyscode != null" >
        BM_SYSCODE,
      </if>
      <if test="companyId != null" >
        COMPANY_ID,
      </if>
      <if test="soCode != null" >
        SO_CODE,
      </if>
      <if test="sinotransId != null" >
        SINOTRANS_ID,
      </if>
      <if test="ikName != null" >
        IK_NAME,
      </if>
      <if test="ctSign != null" >
        CT_SIGN,
      </if>
      <if test="ctRate != null" >
        CT_RATE,
      </if>
      <if test="bmTotalrmb != null" >
        BM_TOTALRMB,
      </if>
      <if test="bmTotalfc != null" >
        BM_TOTALFC,
      </if>
      <if test="taxAmount != null" >
        TAX_AMOUNT,
      </if>
      <if test="notaxAmount != null" >
        NOTAX_AMOUNT,
      </if>
      <if test="taxAmountFc != null" >
        TAX_AMOUNT_FC,
      </if>
      <if test="notaxAmountFc != null" >
        NOTAX_AMOUNT_FC,
      </if>
      <if test="bmPiece != null" >
        BM_PIECE,
      </if>
      <if test="bmChargeableweight != null" >
        BM_CHARGEABLEWEIGHT,
      </if>
      <if test="completionDate != null" >
        COMPLETION_DATE,
      </if>
      <if test="indentifierRc != null" >
        INDENTIFIER_RC,
      </if>
      <if test="invoiceType != null" >
        INVOICE_TYPE,
      </if>
      <if test="invoiceNum != null" >
        INVOICE_NUM,
      </if>
      <if test="taxRate != null" >
        TAX_RATE,
      </if>
      <if test="soName != null" >
        SO_NAME,
      </if>
      <if test="bmHandleTime != null" >
        BM_HANDLE_TIME,
      </if>
      <if test="mId != null" >
        M_ID,
      </if>
      <if test="mCode != null" >
        M_CODE,
      </if>
      <if test="remark != null" >
        REMARK,
      </if>
      <if test="ieType != null" >
        IE_TYPE,
      </if>
      <if test="receiptAmount != null" >
        RECEIPT_AMOUNT,
      </if>
      <if test="businessType != null" >
        BUSINESS_TYPE,
      </if>
      <if test="bmType != null" >
        BM_TYPE,
      </if>
      <if test="salesman != null" >
        SALESMAN,
      </if>
      <if test="salesmanCode != null" >
        SALESMAN_CODE,
      </if>
      <if test="epKey != null" >
        EP_KEY,
      </if>
      <if test="epValue != null" >
        EP_VALUE,
      </if>
      <if test="departureCode != null" >
        DEPARTURE_CODE,
      </if>
      <if test="destCode != null" >
        DEST_CODE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="bmSyscode != null" >
        #{bmSyscode,jdbcType=DECIMAL},
      </if>
      <if test="companyId != null" >
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="soCode != null" >
        #{soCode,jdbcType=VARCHAR},
      </if>
      <if test="sinotransId != null" >
        #{sinotransId,jdbcType=VARCHAR},
      </if>
      <if test="ikName != null" >
        #{ikName,jdbcType=VARCHAR},
      </if>
      <if test="ctSign != null" >
        #{ctSign,jdbcType=VARCHAR},
      </if>
      <if test="ctRate != null" >
        #{ctRate,jdbcType=DECIMAL},
      </if>
      <if test="bmTotalrmb != null" >
        #{bmTotalrmb,jdbcType=DECIMAL},
      </if>
      <if test="bmTotalfc != null" >
        #{bmTotalfc,jdbcType=DECIMAL},
      </if>
      <if test="taxAmount != null" >
        #{taxAmount,jdbcType=DECIMAL},
      </if>
      <if test="notaxAmount != null" >
        #{notaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="taxAmountFc != null" >
        #{taxAmountFc,jdbcType=DECIMAL},
      </if>
      <if test="notaxAmountFc != null" >
        #{notaxAmountFc,jdbcType=DECIMAL},
      </if>
      <if test="bmPiece != null" >
        #{bmPiece,jdbcType=DECIMAL},
      </if>
      <if test="bmChargeableweight != null" >
        #{bmChargeableweight,jdbcType=DECIMAL},
      </if>
      <if test="completionDate != null" >
        #{completionDate,jdbcType=TIMESTAMP},
      </if>
      <if test="indentifierRc != null" >
        #{indentifierRc,jdbcType=VARCHAR},
      </if>
      <if test="invoiceType != null" >
        #{invoiceType,jdbcType=VARCHAR},
      </if>
      <if test="invoiceNum != null" >
        #{invoiceNum,jdbcType=VARCHAR},
      </if>
      <if test="taxRate != null" >
        #{taxRate,jdbcType=DECIMAL},
      </if>
      <if test="soName != null" >
        #{soName,jdbcType=VARCHAR},
      </if>
      <if test="bmHandleTime != null" >
        #{bmHandleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="mId != null" >
        #{mId,jdbcType=DECIMAL},
      </if>
      <if test="mCode != null" >
        #{mCode,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="ieType != null" >
        #{ieType,jdbcType=VARCHAR},
      </if>
      <if test="receiptAmount != null" >
        #{receiptAmount,jdbcType=DECIMAL},
      </if>
      <if test="businessType != null" >
        #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="bmType != null" >
        #{bmType,jdbcType=VARCHAR},
      </if>
      <if test="salesman != null" >
        #{salesman,jdbcType=VARCHAR},
      </if>
      <if test="salesmanCode != null" >
        #{salesmanCode,jdbcType=VARCHAR},
      </if>
      <if test="epKey != null" >
        #{epKey,jdbcType=VARCHAR},
      </if>
      <if test="epValue != null" >
        #{epValue,jdbcType=VARCHAR},
      </if>
      <if test="departureCode != null" >
        #{departureCode,jdbcType=VARCHAR},
      </if>
      <if test="destCode != null" >
        #{destCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.BmsManifest" >
    update BMS_MANIFEST
    <set >
      <if test="companyId != null" >
        COMPANY_ID = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="soCode != null" >
        SO_CODE = #{soCode,jdbcType=VARCHAR},
      </if>
      <if test="sinotransId != null" >
        SINOTRANS_ID = #{sinotransId,jdbcType=VARCHAR},
      </if>
      <if test="ikName != null" >
        IK_NAME = #{ikName,jdbcType=VARCHAR},
      </if>
      <if test="ctSign != null" >
        CT_SIGN = #{ctSign,jdbcType=VARCHAR},
      </if>
      <if test="ctRate != null" >
        CT_RATE = #{ctRate,jdbcType=DECIMAL},
      </if>
      <if test="bmTotalrmb != null" >
        BM_TOTALRMB = #{bmTotalrmb,jdbcType=DECIMAL},
      </if>
      <if test="bmTotalfc != null" >
        BM_TOTALFC = #{bmTotalfc,jdbcType=DECIMAL},
      </if>
      <if test="taxAmount != null" >
        TAX_AMOUNT = #{taxAmount,jdbcType=DECIMAL},
      </if>
      <if test="notaxAmount != null" >
        NOTAX_AMOUNT = #{notaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="taxAmountFc != null" >
        TAX_AMOUNT_FC = #{taxAmountFc,jdbcType=DECIMAL},
      </if>
      <if test="notaxAmountFc != null" >
        NOTAX_AMOUNT_FC = #{notaxAmountFc,jdbcType=DECIMAL},
      </if>
      <if test="bmPiece != null" >
        BM_PIECE = #{bmPiece,jdbcType=DECIMAL},
      </if>
      <if test="bmChargeableweight != null" >
        BM_CHARGEABLEWEIGHT = #{bmChargeableweight,jdbcType=DECIMAL},
      </if>
      <if test="completionDate != null" >
        COMPLETION_DATE = #{completionDate,jdbcType=TIMESTAMP},
      </if>
      <if test="indentifierRc != null" >
        INDENTIFIER_RC = #{indentifierRc,jdbcType=VARCHAR},
      </if>
      <if test="invoiceType != null" >
        INVOICE_TYPE = #{invoiceType,jdbcType=VARCHAR},
      </if>
      <if test="invoiceNum != null" >
        INVOICE_NUM = #{invoiceNum,jdbcType=VARCHAR},
      </if>
      <if test="taxRate != null" >
        TAX_RATE = #{taxRate,jdbcType=DECIMAL},
      </if>
      <if test="soName != null" >
        SO_NAME = #{soName,jdbcType=VARCHAR},
      </if>
      <if test="bmHandleTime != null" >
        BM_HANDLE_TIME = #{bmHandleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="mId != null" >
        M_ID = #{mId,jdbcType=DECIMAL},
      </if>
      <if test="mCode != null" >
        M_CODE = #{mCode,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="ieType != null" >
        IE_TYPE = #{ieType,jdbcType=VARCHAR},
      </if>
      <if test="receiptAmount != null" >
        RECEIPT_AMOUNT = #{receiptAmount,jdbcType=DECIMAL},
      </if>
      <if test="businessType != null" >
        BUSINESS_TYPE = #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="bmType != null" >
        BM_TYPE = #{bmType,jdbcType=VARCHAR},
      </if>
      <if test="salesman != null" >
        SALESMAN = #{salesman,jdbcType=VARCHAR},
      </if>
      <if test="salesmanCode != null" >
        SALESMAN_CODE = #{salesmanCode,jdbcType=VARCHAR},
      </if>
      <if test="epKey != null" >
        EP_KEY = #{epKey,jdbcType=VARCHAR},
      </if>
      <if test="epValue != null" >
        EP_VALUE = #{epValue,jdbcType=VARCHAR},
      </if>
      <if test="departureCode != null" >
        DEPARTURE_CODE = #{departureCode,jdbcType=VARCHAR},
      </if>
      <if test="destCode != null" >
        DEST_CODE = #{destCode,jdbcType=VARCHAR},
      </if>
    </set>
    where BM_SYSCODE = #{bmSyscode,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.BmsManifest" >
    update BMS_MANIFEST
    set COMPANY_ID = #{companyId,jdbcType=VARCHAR},
      SO_CODE = #{soCode,jdbcType=VARCHAR},
      SINOTRANS_ID = #{sinotransId,jdbcType=VARCHAR},
      IK_NAME = #{ikName,jdbcType=VARCHAR},
      CT_SIGN = #{ctSign,jdbcType=VARCHAR},
      CT_RATE = #{ctRate,jdbcType=DECIMAL},
      BM_TOTALRMB = #{bmTotalrmb,jdbcType=DECIMAL},
      BM_TOTALFC = #{bmTotalfc,jdbcType=DECIMAL},
      TAX_AMOUNT = #{taxAmount,jdbcType=DECIMAL},
      NOTAX_AMOUNT = #{notaxAmount,jdbcType=DECIMAL},
      TAX_AMOUNT_FC = #{taxAmountFc,jdbcType=DECIMAL},
      NOTAX_AMOUNT_FC = #{notaxAmountFc,jdbcType=DECIMAL},
      BM_PIECE = #{bmPiece,jdbcType=DECIMAL},
      BM_CHARGEABLEWEIGHT = #{bmChargeableweight,jdbcType=DECIMAL},
      COMPLETION_DATE = #{completionDate,jdbcType=TIMESTAMP},
      INDENTIFIER_RC = #{indentifierRc,jdbcType=VARCHAR},
      INVOICE_TYPE = #{invoiceType,jdbcType=VARCHAR},
      INVOICE_NUM = #{invoiceNum,jdbcType=VARCHAR},
      TAX_RATE = #{taxRate,jdbcType=DECIMAL},
      SO_NAME = #{soName,jdbcType=VARCHAR},
      BM_HANDLE_TIME = #{bmHandleTime,jdbcType=TIMESTAMP},
      M_ID = #{mId,jdbcType=DECIMAL},
      M_CODE = #{mCode,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR},
      IE_TYPE = #{ieType,jdbcType=VARCHAR},
      RECEIPT_AMOUNT = #{receiptAmount,jdbcType=DECIMAL},
      BUSINESS_TYPE = #{businessType,jdbcType=VARCHAR},
      BM_TYPE = #{bmType,jdbcType=VARCHAR},
      SALESMAN = #{salesman,jdbcType=VARCHAR},
      SALESMAN_CODE = #{salesmanCode,jdbcType=VARCHAR},
      EP_KEY = #{epKey,jdbcType=VARCHAR},
      EP_VALUE = #{epValue,jdbcType=VARCHAR},
      DEPARTURE_CODE = #{departureCode,jdbcType=VARCHAR},
      DEST_CODE = #{destCode,jdbcType=VARCHAR}
    where BM_SYSCODE = #{bmSyscode,jdbcType=DECIMAL}
  </update>

  <select id="selectByMId" resultType="com.sinoair.billing.domain.model.billing.BmsManifest" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from BMS_MANIFEST
    where M_ID = #{mId,jdbcType=DECIMAL}
  </select>

  <select id="selectBmsBussnissSeq" resultType="java.lang.String">
    SELECT SEQ_BMS_BUSINESSNO.NEXTVAL
    FROM dual
  </select>
</mapper>