<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.BmsPayDetailMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.BmsPayDetail" >
    <id column="BPD_SYSCODE" property="bpdSyscode" jdbcType="DECIMAL" />
    <result column="CM_ID" property="cmId" jdbcType="DECIMAL" />
    <result column="EP_KEY" property="epKey" jdbcType="VARCHAR" />
    <result column="SINOTRANS_ID" property="sinotransId" jdbcType="VARCHAR" />
    <result column="PAY_AMOUNT" property="payAmount" jdbcType="DECIMAL" />
    <result column="SAC_ID" property="sacId" jdbcType="VARCHAR" />
    <result column="SO_CODE" property="soCode" jdbcType="VARCHAR" />
    <result column="BPD_HANDLETIME" property="bpdHandletime" jdbcType="TIMESTAMP" />
    <result column="CT_SIGN" property="ctSign" jdbcType="VARCHAR" />
    <result column="CM_MONTH" property="cmMonth" jdbcType="DECIMAL" />
    <result column="PR_TYPE" property="prType" jdbcType="VARCHAR" />
    <result column="D_DIRTY" property="dDirty" jdbcType="VARCHAR" />
    <result column="CREATETIME" property="createtime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    BPD_SYSCODE, CM_ID, EP_KEY, SINOTRANS_ID, PAY_AMOUNT, SAC_ID, SO_CODE, BPD_HANDLETIME, 
    CT_SIGN, CM_MONTH, PR_TYPE, D_DIRTY, CREATETIME
  </sql>
  <sql id='TABLE_SEQUENCE'>SEQ_MANIFEST_LIST.NEXTVAL</sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from BMS_PAY_DETAIL
    where BPD_SYSCODE = #{bpdSyscode,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from BMS_PAY_DETAIL
    where BPD_SYSCODE = #{bpdSyscode,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.BmsPayDetail" >
    <selectKey keyProperty="bpdSyscode" resultType="long" order="BEFORE">
      select
      <include refid="TABLE_SEQUENCE"/>
      from dual
    </selectKey>
    insert into BMS_PAY_DETAIL (BPD_SYSCODE, CM_ID, EP_KEY, 
      SINOTRANS_ID, PAY_AMOUNT, SAC_ID, 
      SO_CODE, BPD_HANDLETIME, CT_SIGN, 
      CM_MONTH, PR_TYPE, D_DIRTY, 
      CREATETIME)
    values (#{bpdSyscode,jdbcType=DECIMAL}, #{cmId,jdbcType=DECIMAL}, #{epKey,jdbcType=VARCHAR}, 
      #{sinotransId,jdbcType=VARCHAR}, #{payAmount,jdbcType=DECIMAL}, #{sacId,jdbcType=VARCHAR}, 
      #{soCode,jdbcType=VARCHAR}, #{bpdHandletime,jdbcType=TIMESTAMP}, #{ctSign,jdbcType=VARCHAR}, 
      #{cmMonth,jdbcType=DECIMAL}, #{prType,jdbcType=VARCHAR}, #{dDirty,jdbcType=VARCHAR}, 
      #{createtime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.BmsPayDetail" >
    <selectKey keyProperty="bpdSyscode" resultType="long" order="BEFORE">
      select
      <include refid="TABLE_SEQUENCE"/>
      from dual
    </selectKey>
    insert into BMS_PAY_DETAIL
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="bpdSyscode != null" >
        BPD_SYSCODE,
      </if>
      <if test="cmId != null" >
        CM_ID,
      </if>
      <if test="epKey != null" >
        EP_KEY,
      </if>
      <if test="sinotransId != null" >
        SINOTRANS_ID,
      </if>
      <if test="payAmount != null" >
        PAY_AMOUNT,
      </if>
      <if test="sacId != null" >
        SAC_ID,
      </if>
      <if test="soCode != null" >
        SO_CODE,
      </if>
      <if test="bpdHandletime != null" >
        BPD_HANDLETIME,
      </if>
      <if test="ctSign != null" >
        CT_SIGN,
      </if>
      <if test="cmMonth != null" >
        CM_MONTH,
      </if>
      <if test="prType != null" >
        PR_TYPE,
      </if>
      <if test="dDirty != null" >
        D_DIRTY,
      </if>
      <if test="createtime != null" >
        CREATETIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="bpdSyscode != null" >
        #{bpdSyscode,jdbcType=DECIMAL},
      </if>
      <if test="cmId != null" >
        #{cmId,jdbcType=DECIMAL},
      </if>
      <if test="epKey != null" >
        #{epKey,jdbcType=VARCHAR},
      </if>
      <if test="sinotransId != null" >
        #{sinotransId,jdbcType=VARCHAR},
      </if>
      <if test="payAmount != null" >
        #{payAmount,jdbcType=DECIMAL},
      </if>
      <if test="sacId != null" >
        #{sacId,jdbcType=VARCHAR},
      </if>
      <if test="soCode != null" >
        #{soCode,jdbcType=VARCHAR},
      </if>
      <if test="bpdHandletime != null" >
        #{bpdHandletime,jdbcType=TIMESTAMP},
      </if>
      <if test="ctSign != null" >
        #{ctSign,jdbcType=VARCHAR},
      </if>
      <if test="cmMonth != null" >
        #{cmMonth,jdbcType=DECIMAL},
      </if>
      <if test="prType != null" >
        #{prType,jdbcType=VARCHAR},
      </if>
      <if test="dDirty != null" >
        #{dDirty,jdbcType=VARCHAR},
      </if>
      <if test="createtime != null" >
        #{createtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.BmsPayDetail" >
    update BMS_PAY_DETAIL
    <set >
      <if test="cmId != null" >
        CM_ID = #{cmId,jdbcType=DECIMAL},
      </if>
      <if test="epKey != null" >
        EP_KEY = #{epKey,jdbcType=VARCHAR},
      </if>
      <if test="sinotransId != null" >
        SINOTRANS_ID = #{sinotransId,jdbcType=VARCHAR},
      </if>
      <if test="payAmount != null" >
        PAY_AMOUNT = #{payAmount,jdbcType=DECIMAL},
      </if>
      <if test="sacId != null" >
        SAC_ID = #{sacId,jdbcType=VARCHAR},
      </if>
      <if test="soCode != null" >
        SO_CODE = #{soCode,jdbcType=VARCHAR},
      </if>
      <if test="bpdHandletime != null" >
        BPD_HANDLETIME = #{bpdHandletime,jdbcType=TIMESTAMP},
      </if>
      <if test="ctSign != null" >
        CT_SIGN = #{ctSign,jdbcType=VARCHAR},
      </if>
      <if test="cmMonth != null" >
        CM_MONTH = #{cmMonth,jdbcType=DECIMAL},
      </if>
      <if test="prType != null" >
        PR_TYPE = #{prType,jdbcType=VARCHAR},
      </if>
      <if test="dDirty != null" >
        D_DIRTY = #{dDirty,jdbcType=VARCHAR},
      </if>
      <if test="createtime != null" >
        CREATETIME = #{createtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where BPD_SYSCODE = #{bpdSyscode,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.BmsPayDetail" >
    update BMS_PAY_DETAIL
    set CM_ID = #{cmId,jdbcType=DECIMAL},
      EP_KEY = #{epKey,jdbcType=VARCHAR},
      SINOTRANS_ID = #{sinotransId,jdbcType=VARCHAR},
      PAY_AMOUNT = #{payAmount,jdbcType=DECIMAL},
      SAC_ID = #{sacId,jdbcType=VARCHAR},
      SO_CODE = #{soCode,jdbcType=VARCHAR},
      BPD_HANDLETIME = #{bpdHandletime,jdbcType=TIMESTAMP},
      CT_SIGN = #{ctSign,jdbcType=VARCHAR},
      CM_MONTH = #{cmMonth,jdbcType=DECIMAL},
      PR_TYPE = #{prType,jdbcType=VARCHAR},
      D_DIRTY = #{dDirty,jdbcType=VARCHAR},
      CREATETIME = #{createtime,jdbcType=TIMESTAMP}
    where BPD_SYSCODE = #{bpdSyscode,jdbcType=DECIMAL}
  </update>

  <update id="updateAmount" parameterType="java.lang.Long" >
    update bms_pay_detail
    set
     PAY_AMOUNT  = (select sum(pr.pr_actual_amount) from payment_record pr where pr.bpd_syscode=#{bpdSyscode,jdbcType=DECIMAL})
    where BPD_SYSCODE = #{bpdSyscode,jdbcType=DECIMAL}
  </update>
</mapper>