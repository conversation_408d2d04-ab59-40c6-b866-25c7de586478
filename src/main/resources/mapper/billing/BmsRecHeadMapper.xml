<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.BmsRecHeadMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.BmsRecHead" >
    <result column="COLLECT_MONTH" property="collectMonth" jdbcType="DECIMAL" />
    <result column="EP_KEY" property="epKey" jdbcType="VARCHAR" />
    <result column="SINOTRANS_ID" property="sinotransId" jdbcType="VARCHAR" />
    <result column="BM_PIECE" property="bmPiece" jdbcType="DECIMAL" />
    <result column="BM_CHARGEABLEWEIGHT" property="bmChargeableweight" jdbcType="DECIMAL" />
    <result column="DEPARTURE_CODE" property="departureCode" jdbcType="VARCHAR" />
    <result column="DEST_CODE" property="destCode" jdbcType="VARCHAR" />
    <result column="SAC_ID" property="sacId" jdbcType="VARCHAR" />
    <result column="SO_CODE" property="soCode" jdbcType="VARCHAR" />
  </resultMap>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.BmsRecHead" >
    insert into BMS_REC_HEAD (COLLECT_MONTH, EP_KEY, SINOTRANS_ID, 
      BM_PIECE, BM_CHARGEABLEWEIGHT, DEPARTURE_CODE, 
      DEST_CODE, SAC_ID, SO_CODE)
    values (#{collectMonth,jdbcType=DECIMAL}, #{epKey,jdbcType=VARCHAR}, #{sinotransId,jdbcType=VARCHAR}, 
      #{bmPiece,jdbcType=DECIMAL}, #{bmChargeableweight,jdbcType=DECIMAL}, #{departureCode,jdbcType=VARCHAR}, 
      #{destCode,jdbcType=VARCHAR}, #{sacId,jdbcType=VARCHAR}, #{soCode,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.BmsRecHead" >
    insert into BMS_REC_HEAD
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="collectMonth != null" >
        COLLECT_MONTH,
      </if>
      <if test="epKey != null" >
        EP_KEY,
      </if>
      <if test="sinotransId != null" >
        SINOTRANS_ID,
      </if>
      <if test="bmPiece != null" >
        BM_PIECE,
      </if>
      <if test="bmChargeableweight != null" >
        BM_CHARGEABLEWEIGHT,
      </if>
      <if test="departureCode != null" >
        DEPARTURE_CODE,
      </if>
      <if test="destCode != null" >
        DEST_CODE,
      </if>
      <if test="sacId != null" >
        SAC_ID,
      </if>
      <if test="soCode != null" >
        SO_CODE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="collectMonth != null" >
        #{collectMonth,jdbcType=DECIMAL},
      </if>
      <if test="epKey != null" >
        #{epKey,jdbcType=VARCHAR},
      </if>
      <if test="sinotransId != null" >
        #{sinotransId,jdbcType=VARCHAR},
      </if>
      <if test="bmPiece != null" >
        #{bmPiece,jdbcType=DECIMAL},
      </if>
      <if test="bmChargeableweight != null" >
        #{bmChargeableweight,jdbcType=DECIMAL},
      </if>
      <if test="departureCode != null" >
        #{departureCode,jdbcType=VARCHAR},
      </if>
      <if test="destCode != null" >
        #{destCode,jdbcType=VARCHAR},
      </if>
      <if test="sacId != null" >
        #{sacId,jdbcType=VARCHAR},
      </if>
      <if test="soCode != null" >
        #{soCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <delete id="deleteByMonth" parameterType="java.lang.Integer" >
    delete from BMS_REC_HEAD
    where COLLECT_MONTH = #{collectMonth,jdbcType=DECIMAL}
  </delete>

  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.BmsRecHead" >
    update BMS_REC_HEAD
    <set >
      <if test="bmPiece != null" >
        BM_PIECE = #{bmPiece,jdbcType=DECIMAL},
      </if>
      <if test="bmChargeableweight != null" >
        BM_CHARGEABLEWEIGHT = #{bmChargeableweight,jdbcType=DECIMAL},
      </if>
      <if test="departureCode != null" >
        DEPARTURE_CODE = #{departureCode,jdbcType=VARCHAR},
      </if>
      <if test="destCode != null" >
        DEST_CODE = #{destCode,jdbcType=VARCHAR},
      </if>
      <if test="sacId != null" >
        SAC_ID = #{sacId,jdbcType=VARCHAR},
      </if>
      <if test="soCode != null" >
        SO_CODE = #{soCode,jdbcType=VARCHAR},
      </if>
    </set>
    where COLLECT_MONTH = #{collectMonth,jdbcType=DECIMAL}
      and EP_KEY = #{epKey,jdbcType=VARCHAR}
      and SINOTRANS_ID = #{sinotransId,jdbcType=VARCHAR}

  </update>

</mapper>