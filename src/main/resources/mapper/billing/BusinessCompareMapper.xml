<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.BusinessCompareMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.BusinessCompare" >
    <id column="BUSINESS_CODE" property="businessCode" jdbcType="VARCHAR" />
    <id column="DSBMS_CODE" property="dsbmsCode" jdbcType="VARCHAR" />
    <result column="EAWB_SERVICETYPE" property="eawbServicetype" jdbcType="VARCHAR" />
    <result column="PBD_DATE" property="pbdDate" jdbcType="TIMESTAMP" />
    <result column="FIELD_TYPE" property="fieldType" jdbcType="VARCHAR" />
    <result column="BC_HANDLETIME" property="bcHandletime" jdbcType="TIMESTAMP" />
    <result column="BC_USER_ID" property="bcUserId" jdbcType="DECIMAL" />
  </resultMap>
  <sql id="Base_Column_List" >
    BUSINESS_CODE, DSBMS_CODE, EAWB_SERVICETYPE, PBD_DATE, FIELD_TYPE, BC_HANDLETIME, 
    BC_USER_ID
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.sinoair.billing.domain.model.billing.BusinessCompareKey" >
    select 
    <include refid="Base_Column_List" />
    from BUSINESS_COMPARE
    where BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR}
      and DSBMS_CODE = #{dsbmsCode,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.BusinessCompareKey" >
    delete from BUSINESS_COMPARE
    where BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR}
      and DSBMS_CODE = #{dsbmsCode,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.BusinessCompare" >
    insert into BUSINESS_COMPARE (BUSINESS_CODE, DSBMS_CODE, EAWB_SERVICETYPE, 
      PBD_DATE, FIELD_TYPE, BC_HANDLETIME, 
      BC_USER_ID)
    values (#{businessCode,jdbcType=VARCHAR}, #{dsbmsCode,jdbcType=VARCHAR}, #{eawbServicetype,jdbcType=VARCHAR}, 
      #{pbdDate,jdbcType=TIMESTAMP}, #{fieldType,jdbcType=VARCHAR}, #{bcHandletime,jdbcType=TIMESTAMP}, 
      #{bcUserId,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.BusinessCompare" >
    insert into BUSINESS_COMPARE
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="businessCode != null" >
        BUSINESS_CODE,
      </if>
      <if test="dsbmsCode != null" >
        DSBMS_CODE,
      </if>
      <if test="eawbServicetype != null" >
        EAWB_SERVICETYPE,
      </if>
      <if test="pbdDate != null" >
        PBD_DATE,
      </if>
      <if test="fieldType != null" >
        FIELD_TYPE,
      </if>
      <if test="bcHandletime != null" >
        BC_HANDLETIME,
      </if>
      <if test="bcUserId != null" >
        BC_USER_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="businessCode != null" >
        #{businessCode,jdbcType=VARCHAR},
      </if>
      <if test="dsbmsCode != null" >
        #{dsbmsCode,jdbcType=VARCHAR},
      </if>
      <if test="eawbServicetype != null" >
        #{eawbServicetype,jdbcType=VARCHAR},
      </if>
      <if test="pbdDate != null" >
        #{pbdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="fieldType != null" >
        #{fieldType,jdbcType=VARCHAR},
      </if>
      <if test="bcHandletime != null" >
        #{bcHandletime,jdbcType=TIMESTAMP},
      </if>
      <if test="bcUserId != null" >
        #{bcUserId,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.BusinessCompare" >
    update BUSINESS_COMPARE
    <set >
      <if test="eawbServicetype != null" >
        EAWB_SERVICETYPE = #{eawbServicetype,jdbcType=VARCHAR},
      </if>
      <if test="pbdDate != null" >
        PBD_DATE = #{pbdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="fieldType != null" >
        FIELD_TYPE = #{fieldType,jdbcType=VARCHAR},
      </if>
      <if test="bcHandletime != null" >
        BC_HANDLETIME = #{bcHandletime,jdbcType=TIMESTAMP},
      </if>
      <if test="bcUserId != null" >
        BC_USER_ID = #{bcUserId,jdbcType=DECIMAL},
      </if>
    </set>
    where BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR}
      and DSBMS_CODE = #{dsbmsCode,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.BusinessCompare" >
    update BUSINESS_COMPARE
    set EAWB_SERVICETYPE = #{eawbServicetype,jdbcType=VARCHAR},
      PBD_DATE = #{pbdDate,jdbcType=TIMESTAMP},
      FIELD_TYPE = #{fieldType,jdbcType=VARCHAR},
      BC_HANDLETIME = #{bcHandletime,jdbcType=TIMESTAMP},
      BC_USER_ID = #{bcUserId,jdbcType=DECIMAL}
    where BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR}
      and DSBMS_CODE = #{dsbmsCode,jdbcType=VARCHAR}
  </update>
  <insert id="insertBatch" parameterType="java.util.List" >
    insert into BUSINESS_COMPARE (BUSINESS_CODE, DSBMS_CODE, EAWB_SERVICETYPE,
    PBD_DATE, FIELD_TYPE, BC_HANDLETIME,
    BC_USER_ID)
    <foreach collection="list" item="item" index="index" separator="UNION" >
    (SELECT
      #{item.businessCode,jdbcType=VARCHAR}, #{item.dsbmsCode,jdbcType=VARCHAR}, #{item.eawbServicetype,jdbcType=VARCHAR},
    #{item.pbdDate,jdbcType=TIMESTAMP}, #{item.fieldType,jdbcType=VARCHAR}, #{item.bcHandletime,jdbcType=TIMESTAMP},
    #{item.bcUserId,jdbcType=DECIMAL}
      FROM dual)
    </foreach>
  </insert>
</mapper>