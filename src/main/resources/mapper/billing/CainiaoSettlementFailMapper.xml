<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.CainiaoSettlementFailMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.CainiaoSettlementFail" >
    <id column="CNSFAIL_ID" property="cnsfailId" jdbcType="DECIMAL" />
    <result column="EAWB_REFERENCE2" property="eawbReference2" jdbcType="VARCHAR" />
    <result column="RR_NAME" property="rrName" jdbcType="VARCHAR" />
    <result column="CN_DM_ID" property="cnDmId" jdbcType="VARCHAR" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="PAYMENT_ORDER_ID" property="paymentOrderId" jdbcType="VARCHAR" />
    <result column="FILE_NAME" property="fileName" jdbcType="VARCHAR" />
    <result column="REMARKS" property="remarks" jdbcType="VARCHAR" />
    <result column="FAIL_TYPE" property="failType" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    CNSFAIL_ID, EAWB_REFERENCE2, RR_NAME, CN_DM_ID, CREATE_DATE, PAYMENT_ORDER_ID, FILE_NAME, 
    REMARKS, FAIL_TYPE
  </sql>
  <sql id='TABLE_SEQUENCE'>SEQ_SETTLEMENT_SUC.NEXTVAL</sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from CAINIAO_SETTLEMENT_FAIL
    where CNSFAIL_ID = #{cnsfailId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from CAINIAO_SETTLEMENT_FAIL
    where CNSFAIL_ID = #{cnsfailId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.CainiaoSettlementFail" >
    <selectKey keyProperty="cnsfailId" resultType="int" order="BEFORE">
      select
      <include refid="TABLE_SEQUENCE"/>
      from dual
    </selectKey>
    insert into CAINIAO_SETTLEMENT_FAIL (CNSFAIL_ID, EAWB_REFERENCE2, RR_NAME, 
      CN_DM_ID, CREATE_DATE, PAYMENT_ORDER_ID, 
      FILE_NAME, REMARKS, FAIL_TYPE
      )
    values (#{cnsfailId,jdbcType=DECIMAL}, #{eawbReference2,jdbcType=VARCHAR}, #{rrName,jdbcType=VARCHAR}, 
      #{cnDmId,jdbcType=VARCHAR}, #{createDate,jdbcType=TIMESTAMP}, #{paymentOrderId,jdbcType=VARCHAR}, 
      #{fileName,jdbcType=VARCHAR}, #{remarks,jdbcType=VARCHAR}, #{failType,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.CainiaoSettlementFail" >
    <selectKey keyProperty="cnsfailId" resultType="int" order="BEFORE">
      select
      <include refid="TABLE_SEQUENCE"/>
      from dual
    </selectKey>
    insert into CAINIAO_SETTLEMENT_FAIL
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="cnsfailId != null" >
        CNSFAIL_ID,
      </if>
      <if test="eawbReference2 != null" >
        EAWB_REFERENCE2,
      </if>
      <if test="rrName != null" >
        RR_NAME,
      </if>
      <if test="cnDmId != null" >
        CN_DM_ID,
      </if>
      <if test="createDate != null" >
        CREATE_DATE,
      </if>
      <if test="paymentOrderId != null" >
        PAYMENT_ORDER_ID,
      </if>
      <if test="fileName != null" >
        FILE_NAME,
      </if>
      <if test="remarks != null" >
        REMARKS,
      </if>
      <if test="failType != null" >
        FAIL_TYPE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="cnsfailId != null" >
        #{cnsfailId,jdbcType=DECIMAL},
      </if>
      <if test="eawbReference2 != null" >
        #{eawbReference2,jdbcType=VARCHAR},
      </if>
      <if test="rrName != null" >
        #{rrName,jdbcType=VARCHAR},
      </if>
      <if test="cnDmId != null" >
        #{cnDmId,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="paymentOrderId != null" >
        #{paymentOrderId,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null" >
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="remarks != null" >
        #{remarks,jdbcType=VARCHAR},
      </if>
      <if test="failType != null" >
        #{failType,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.CainiaoSettlementFail" >
    update CAINIAO_SETTLEMENT_FAIL
    <set >
      <if test="eawbReference2 != null" >
        EAWB_REFERENCE2 = #{eawbReference2,jdbcType=VARCHAR},
      </if>
      <if test="rrName != null" >
        RR_NAME = #{rrName,jdbcType=VARCHAR},
      </if>
      <if test="cnDmId != null" >
        CN_DM_ID = #{cnDmId,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="paymentOrderId != null" >
        PAYMENT_ORDER_ID = #{paymentOrderId,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null" >
        FILE_NAME = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="remarks != null" >
        REMARKS = #{remarks,jdbcType=VARCHAR},
      </if>
      <if test="failType != null" >
        FAIL_TYPE = #{failType,jdbcType=VARCHAR},
      </if>
    </set>
    where CNSFAIL_ID = #{cnsfailId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.CainiaoSettlementFail" >
    update CAINIAO_SETTLEMENT_FAIL
    set EAWB_REFERENCE2 = #{eawbReference2,jdbcType=VARCHAR},
      RR_NAME = #{rrName,jdbcType=VARCHAR},
      CN_DM_ID = #{cnDmId,jdbcType=VARCHAR},
      CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
      PAYMENT_ORDER_ID = #{paymentOrderId,jdbcType=VARCHAR},
      FILE_NAME = #{fileName,jdbcType=VARCHAR},
      REMARKS = #{remarks,jdbcType=VARCHAR},
      FAIL_TYPE = #{failType,jdbcType=VARCHAR}
    where CNSFAIL_ID = #{cnsfailId,jdbcType=DECIMAL}
  </update>

  <insert id="insertBatch" parameterType="java.util.List" >
    insert into CAINIAO_SETTLEMENT_FAIL (CNSFAIL_ID, EAWB_REFERENCE2, RR_NAME,
    CN_DM_ID, CREATE_DATE, PAYMENT_ORDER_ID,
    FILE_NAME, REMARKS, FAIL_TYPE
    )
    select SEQ_SETTLEMENT_SUC.NEXTVAL,pbd.* from (
    <foreach collection="list" item="item" index="index" separator="UNION ALL">
      select #{item.eawbReference2,jdbcType=VARCHAR}, #{item.rrName,jdbcType=VARCHAR},
      #{item.cnDmId,jdbcType=VARCHAR}, #{item.createDate,jdbcType=TIMESTAMP}, #{item.paymentOrderId,jdbcType=VARCHAR},
      #{item.fileName,jdbcType=VARCHAR}, #{item.remarks,jdbcType=VARCHAR}, #{item.failType,jdbcType=VARCHAR} from dual
    </foreach>
    ) pbd
  </insert>
</mapper>