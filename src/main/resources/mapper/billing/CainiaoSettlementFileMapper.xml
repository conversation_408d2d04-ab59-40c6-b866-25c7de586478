<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.CainiaoSettlementFileMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.CainiaoSettlementFile" >
    <id column="CNSF_ID" property="cnsfId" jdbcType="DECIMAL" />
    <result column="CNSU_ID" property="cnsuId" jdbcType="DECIMAL" />
    <result column="FILE_NAME" property="fileName" jdbcType="VARCHAR" />
    <result column="FILE_PATH" property="filePath" jdbcType="VARCHAR" />
    <result column="HANDLE_STATUS" property="handleStatus" jdbcType="VARCHAR" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="REMARKS" property="remarks" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    CNSF_ID, CNSU_ID, FILE_NAME, FILE_PATH, HANDLE_STATUS, CREATE_TIME,REMARKS
  </sql>

  <sql id='TABLE_SEQUENCE'>SEQ_SETTLEMENT_SUC.NEXTVAL</sql>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from CAINIAO_SETTLEMENT_FILE
    where CNSF_ID = #{cnsfId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from CAINIAO_SETTLEMENT_FILE
    where CNSF_ID = #{cnsfId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.CainiaoSettlementFile" >
    <selectKey keyProperty="cnsfId" resultType="int" order="BEFORE">
      select
      <include refid="TABLE_SEQUENCE"/>
      from dual
    </selectKey>
    insert into CAINIAO_SETTLEMENT_FILE (CNSF_ID, CNSU_ID, FILE_NAME, 
      FILE_PATH, HANDLE_STATUS, CREATE_TIME,REMARKS
      )
    values (#{cnsfId,jdbcType=DECIMAL}, #{cnsuId,jdbcType=DECIMAL}, #{fileName,jdbcType=VARCHAR}, 
      #{filePath,jdbcType=VARCHAR}, #{handleStatus,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
      #{remarks,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.CainiaoSettlementFile" >
    <selectKey keyProperty="cnsfId" resultType="int" order="BEFORE">
      select
      <include refid="TABLE_SEQUENCE"/>
      from dual
    </selectKey>

    insert into CAINIAO_SETTLEMENT_FILE
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="cnsfId != null" >
        CNSF_ID,
      </if>
      <if test="cnsuId != null" >
        CNSU_ID,
      </if>
      <if test="fileName != null" >
        FILE_NAME,
      </if>
      <if test="filePath != null" >
        FILE_PATH,
      </if>
      <if test="handleStatus != null" >
        HANDLE_STATUS,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
      <if test="remarks != null" >
        REMARKS
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="cnsfId != null" >
        #{cnsfId,jdbcType=DECIMAL},
      </if>
      <if test="cnsuId != null" >
        #{cnsuId,jdbcType=DECIMAL},
      </if>
      <if test="fileName != null" >
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="filePath != null" >
        #{filePath,jdbcType=VARCHAR},
      </if>
      <if test="handleStatus != null" >
        #{handleStatus,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remarks != null" >
        #{remarks,jdbcType=VARCHAR}
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.CainiaoSettlementFile" >
    update CAINIAO_SETTLEMENT_FILE
    <set >
      <if test="cnsuId != null" >
        CNSU_ID = #{cnsuId,jdbcType=DECIMAL},
      </if>
      <if test="fileName != null" >
        FILE_NAME = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="filePath != null" >
        FILE_PATH = #{filePath,jdbcType=VARCHAR},
      </if>
      <if test="handleStatus != null" >
        HANDLE_STATUS = #{handleStatus,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remarks != null" >
        REMARKS = #{remarks,jdbcType=VARCHAR}
      </if>
    </set>
    where CNSF_ID = #{cnsfId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.CainiaoSettlementFile" >
    update CAINIAO_SETTLEMENT_FILE
    set CNSU_ID = #{cnsuId,jdbcType=DECIMAL},
      FILE_NAME = #{fileName,jdbcType=VARCHAR},
      FILE_PATH = #{filePath,jdbcType=VARCHAR},
      HANDLE_STATUS = #{handleStatus,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
        REMARKS = #{remarks,jdbcType=VARCHAR}
    where CNSF_ID = #{cnsfId,jdbcType=DECIMAL}
  </update>
</mapper>