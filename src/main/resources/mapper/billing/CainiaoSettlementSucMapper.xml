<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.CainiaoSettlementSucMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.CainiaoSettlementSuc" >
    <result column="EAWB_REFERENCE2" property="eawbReference2" jdbcType="VARCHAR" />
    <result column="MAWB_CODE" property="mawbCode" jdbcType="VARCHAR" />
    <result column="EAWB_KEYENTRYTIME" property="eawbKeyentrytime" jdbcType="VARCHAR" />
    <result column="RR_NAME" property="rrName" jdbcType="VARCHAR" />
    <result column="RR_OCCURTIME" property="rrOccurtime" jdbcType="VARCHAR" />
    <result column="CN_PRODUCT" property="cnProduct" jdbcType="VARCHAR" />
    <result column="RR_AMOUNT" property="rrAmount" jdbcType="DECIMAL" />
    <result column="CT_SIGN" property="ctSign" jdbcType="VARCHAR" />
    <result column="CN_DM_ID" property="cnDmId" jdbcType="VARCHAR" />
  </resultMap>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.CainiaoSettlementSuc" >
    insert into CAINIAO_SETTLEMENT_SUC (EAWB_REFERENCE2, MAWB_CODE, EAWB_KEYENTRYTIME, 
      RR_NAME, RR_OCCURTIME, CN_PRODUCT, 
      RR_AMOUNT, CT_SIGN, CN_DM_ID,PAYMENT_ORDER_ID, FILE_NAME
      )
    values (#{eawbReference2,jdbcType=VARCHAR}, #{mawbCode,jdbcType=VARCHAR}, #{eawbKeyentrytime,jdbcType=VARCHAR}, 
      #{rrName,jdbcType=VARCHAR}, #{rrOccurtime,jdbcType=VARCHAR}, #{cnProduct,jdbcType=VARCHAR}, 
      #{rrAmount,jdbcType=DECIMAL}, #{ctSign,jdbcType=VARCHAR}, #{cnDmId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.CainiaoSettlementSuc" >
    insert into CAINIAO_SETTLEMENT_SUC
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="eawbReference2 != null" >
        EAWB_REFERENCE2,
      </if>
      <if test="mawbCode != null" >
        MAWB_CODE,
      </if>
      <if test="eawbKeyentrytime != null" >
        EAWB_KEYENTRYTIME,
      </if>
      <if test="rrName != null" >
        RR_NAME,
      </if>
      <if test="rrOccurtime != null" >
        RR_OCCURTIME,
      </if>
      <if test="cnProduct != null" >
        CN_PRODUCT,
      </if>
      <if test="rrAmount != null" >
        RR_AMOUNT,
      </if>
      <if test="ctSign != null" >
        CT_SIGN,
      </if>
      <if test="cnDmId != null" >
        CN_DM_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="eawbReference2 != null" >
        #{eawbReference2,jdbcType=VARCHAR},
      </if>
      <if test="mawbCode != null" >
        #{mawbCode,jdbcType=VARCHAR},
      </if>
      <if test="eawbKeyentrytime != null" >
        #{eawbKeyentrytime,jdbcType=VARCHAR},
      </if>
      <if test="rrName != null" >
        #{rrName,jdbcType=VARCHAR},
      </if>
      <if test="rrOccurtime != null" >
        #{rrOccurtime,jdbcType=VARCHAR},
      </if>
      <if test="cnProduct != null" >
        #{cnProduct,jdbcType=VARCHAR},
      </if>
      <if test="rrAmount != null" >
        #{rrAmount,jdbcType=DECIMAL},
      </if>
      <if test="ctSign != null" >
        #{ctSign,jdbcType=VARCHAR},
      </if>
      <if test="cnDmId != null" >
        #{cnDmId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <insert id="insertBatch" parameterType="java.util.List" >
    insert into CAINIAO_SETTLEMENT_SUC (EAWB_REFERENCE2, MAWB_CODE, EAWB_KEYENTRYTIME,
    RR_NAME, RR_OCCURTIME, CN_PRODUCT,
    RR_AMOUNT, CT_SIGN, CN_DM_ID,PAYMENT_ORDER_ID, FILE_NAME)
    <foreach collection="list" item="item" index="index" separator="UNION" >
      (SELECT
      #{item.eawbReference2,jdbcType=VARCHAR}, #{item.mawbCode,jdbcType=VARCHAR}, #{item.eawbKeyentrytime,jdbcType=VARCHAR},
      #{item.rrName,jdbcType=VARCHAR}, #{item.rrOccurtime,jdbcType=VARCHAR}, #{item.cnProduct,jdbcType=VARCHAR},
      #{item.rrAmount,jdbcType=DECIMAL}, #{item.ctSign,jdbcType=VARCHAR}, #{item.cnDmId,jdbcType=VARCHAR},
      #{item.paymentOrderId,jdbcType=VARCHAR}, #{item.fileName,jdbcType=VARCHAR}
      FROM dual)
    </foreach>
  </insert>

  <select id="countCainiaoSettlement" parameterType="com.sinoair.billing.domain.model.billing.CainiaoSettlementSuc" resultType="java.lang.Integer">
    select
    count(0)
    from CAINIAO_SETTLEMENT_SUC
    where EAWB_REFERENCE2 = #{eawbReference2,jdbcType=VARCHAR}
     and RR_NAME = #{rrName,jdbcType=VARCHAR}
     and CN_DM_ID = #{cnDmId,jdbcType=VARCHAR}
  </select>
</mapper>