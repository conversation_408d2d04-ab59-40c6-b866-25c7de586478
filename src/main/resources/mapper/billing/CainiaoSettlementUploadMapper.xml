<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.CainiaoSettlementUploadMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.CainiaoSettlementUpload" >
    <id column="CNSU_ID" property="cnsuId" jdbcType="DECIMAL" />
    <result column="PAYMENT_ORDER_ID" property="paymentOrderId" jdbcType="VARCHAR" />
    <result column="FILE_NAME" property="fileName" jdbcType="VARCHAR" />
    <result column="FILE_PATH" property="filePath" jdbcType="VARCHAR" />
    <result column="FILE_SIZE" property="fileSize" jdbcType="DECIMAL" />
    <result column="FILE_COUNT" property="fileCount" jdbcType="DECIMAL" />
    <result column="HANDLE_COUNT" property="handleCount" jdbcType="DECIMAL" />
    <result column="HANDLE_STATUS" property="handleStatus" jdbcType="VARCHAR" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="HANDLE_TIME" property="handleTime" jdbcType="TIMESTAMP" />
    <result column="CREATE_USER_ID" property="createUserId" jdbcType="DECIMAL" />
    <result column="DATA_STATUS" property="dataStatus" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    CNSU_ID, PAYMENT_ORDER_ID, FILE_NAME, FILE_PATH, FILE_SIZE, FILE_COUNT, HANDLE_COUNT,
    HANDLE_STATUS, CREATE_TIME, HANDLE_TIME, CREATE_USER_ID,DATA_STATUS
  </sql>
  <sql id='TABLE_SEQUENCE'>SEQ_SETTLEMENT_SUC.NEXTVAL</sql>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from CAINIAO_SETTLEMENT_UPLOAD
    where CNSU_ID = #{cnsuId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from CAINIAO_SETTLEMENT_UPLOAD
    where CNSU_ID = #{cnsuId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.CainiaoSettlementUpload" >
    <selectKey keyProperty="cnsuId" resultType="int" order="BEFORE">
      select
      <include refid="TABLE_SEQUENCE"/>
      from dual
    </selectKey>
    insert into CAINIAO_SETTLEMENT_UPLOAD (CNSU_ID, PAYMENT_ORDER_ID, FILE_NAME,
    FILE_PATH, FILE_SIZE, FILE_COUNT,
    HANDLE_COUNT, HANDLE_STATUS, CREATE_TIME,
    HANDLE_TIME, CREATE_USER_ID, DATA_STATUS
    )
    values (#{cnsuId,jdbcType=DECIMAL}, #{paymentOrderId,jdbcType=VARCHAR}, #{fileName,jdbcType=VARCHAR},
    #{filePath,jdbcType=VARCHAR}, #{fileSize,jdbcType=DECIMAL}, #{fileCount,jdbcType=DECIMAL},
    #{handleCount,jdbcType=DECIMAL}, #{handleStatus,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
    #{handleTime,jdbcType=TIMESTAMP}, #{createUserId,jdbcType=DECIMAL}, #{dataStatus,jdbcType=VARCHAR}
    )
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.CainiaoSettlementUpload" >
    <selectKey keyProperty="cnsuId" resultType="int" order="BEFORE">
      select
      <include refid="TABLE_SEQUENCE"/>
      from dual
    </selectKey>
    insert into CAINIAO_SETTLEMENT_UPLOAD
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="cnsuId != null" >
        CNSU_ID,
      </if>
      <if test="paymentOrderId != null" >
        PAYMENT_ORDER_ID,
      </if>
      <if test="fileName != null" >
        FILE_NAME,
      </if>
      <if test="filePath != null" >
        FILE_PATH,
      </if>
      <if test="fileSize != null" >
        FILE_SIZE,
      </if>
      <if test="fileCount != null" >
        FILE_COUNT,
      </if>
      <if test="handleCount != null" >
        HANDLE_COUNT,
      </if>
      <if test="handleStatus != null" >
        HANDLE_STATUS,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
      <if test="handleTime != null" >
        HANDLE_TIME,
      </if>
      <if test="createUserId != null" >
        CREATE_USER_ID,
      </if>
      <if test="dataStatus != null" >
        DATA_STATUS,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="cnsuId != null" >
        #{cnsuId,jdbcType=DECIMAL},
      </if>
      <if test="paymentOrderId != null" >
        #{paymentOrderId,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null" >
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="filePath != null" >
        #{filePath,jdbcType=VARCHAR},
      </if>
      <if test="fileSize != null" >
        #{fileSize,jdbcType=DECIMAL},
      </if>
      <if test="fileCount != null" >
        #{fileCount,jdbcType=DECIMAL},
      </if>
      <if test="handleCount != null" >
        #{handleCount,jdbcType=DECIMAL},
      </if>
      <if test="handleStatus != null" >
        #{handleStatus,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="handleTime != null" >
        #{handleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null" >
        #{createUserId,jdbcType=DECIMAL},
      </if>
      <if test="dataStatus != null" >
        #{dataStatus,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.CainiaoSettlementUpload" >
    update CAINIAO_SETTLEMENT_UPLOAD
    <set >
      <if test="paymentOrderId != null" >
        PAYMENT_ORDER_ID = #{paymentOrderId,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null" >
        FILE_NAME = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="filePath != null" >
        FILE_PATH = #{filePath,jdbcType=VARCHAR},
      </if>
      <if test="fileSize != null" >
        FILE_SIZE = #{fileSize,jdbcType=DECIMAL},
      </if>
      <if test="fileCount != null" >
        FILE_COUNT = #{fileCount,jdbcType=DECIMAL},
      </if>
      <if test="handleCount != null" >
        HANDLE_COUNT = #{handleCount,jdbcType=DECIMAL},
      </if>
      <if test="handleStatus != null" >
        HANDLE_STATUS = #{handleStatus,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="handleTime != null" >
        HANDLE_TIME = #{handleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null" >
        CREATE_USER_ID = #{createUserId,jdbcType=DECIMAL},
      </if>
      <if test="dataStatus != null" >
        DATA_STATUS = #{dataStatus,jdbcType=VARCHAR},
      </if>
    </set>
    where CNSU_ID = #{cnsuId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.CainiaoSettlementUpload" >
    update CAINIAO_SETTLEMENT_UPLOAD
    set PAYMENT_ORDER_ID = #{paymentOrderId,jdbcType=VARCHAR},
        FILE_NAME = #{fileName,jdbcType=VARCHAR},
        FILE_PATH = #{filePath,jdbcType=VARCHAR},
        FILE_SIZE = #{fileSize,jdbcType=DECIMAL},
        FILE_COUNT = #{fileCount,jdbcType=DECIMAL},
        HANDLE_COUNT = #{handleCount,jdbcType=DECIMAL},
        HANDLE_STATUS = #{handleStatus,jdbcType=VARCHAR},
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
        HANDLE_TIME = #{handleTime,jdbcType=TIMESTAMP},
        CREATE_USER_ID = #{createUserId,jdbcType=DECIMAL},
        DATA_STATUS = #{dataStatus,jdbcType=VARCHAR}
    where CNSU_ID = #{cnsuId,jdbcType=DECIMAL}
  </update>

  <select id="selectList" resultMap="BaseResultMap" parameterType="com.sinoair.billing.domain.model.billing.CainiaoSettlementUpload" >
    select
    <include refid="Base_Column_List" />
    from CAINIAO_SETTLEMENT_UPLOAD
    where HANDLE_STATUS = #{handleStatus,jdbcType=VARCHAR}
  </select>
  <select id="selectPendingList" resultMap="BaseResultMap" parameterType="com.sinoair.billing.domain.model.billing.CainiaoSettlementUpload" >
    select
    <include refid="Base_Column_List" />
    from CAINIAO_SETTLEMENT_UPLOAD
    where HANDLE_STATUS in ('NEW','RUNING')
  </select>


</mapper>