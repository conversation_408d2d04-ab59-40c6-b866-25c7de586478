<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.CainiaowulishMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.Cainiaowulish" >
    <result column="ZWYPART" property="zwypart" jdbcType="VARCHAR" />
    <result column="LUJING" property="lujing" jdbcType="VARCHAR" />
    <result column="ZWYBIAOMING" property="zwybiaoming" jdbcType="VARCHAR" />
    <result column="ZWYFOLDERNAME" property="zwyfoldername" jdbcType="VARCHAR" />
    <result column="ZWYTABLENAME" property="zwytablename" jdbcType="VARCHAR" />
    <result column="ZWYBILLMONTH" property="zwybillmonth" jdbcType="VARCHAR" />
    <result column="ZWYLPNO" property="zwylpno" jdbcType="VARCHAR" />
    <result column="ZWYWEIGHTKG" property="zwyweightkg" jdbcType="DECIMAL" />
    <result column="ZWYFEENAME" property="zwyfeename" jdbcType="VARCHAR" />
    <result column="ZWYCURRENCY" property="zwycurrency" jdbcType="VARCHAR" />
    <result column="ZWYAMOUNT" property="zwyamount" jdbcType="DECIMAL" />
    <result column="ZWYCOMMENT" property="zwycomment" jdbcType="VARCHAR" />
    <result column="REMARK" property="remark" jdbcType="VARCHAR" />
    <result column="ENTITY_VALUE" property="entityValue" jdbcType="VARCHAR" />
    <result column="SERVICE_ITEM_CODE" property="serviceItemCode" jdbcType="VARCHAR" />
    <result column="SERVICE_ITEM_NAME" property="serviceItemName" jdbcType="VARCHAR" />
    <result column="USER_ID" property="userId" jdbcType="VARCHAR" />
    <result column="MAX_CREATE_TIME" property="maxCreateTime" jdbcType="VARCHAR" />
    <result column="MIN_CREATE_TIME" property="minCreateTime" jdbcType="VARCHAR" />
    <result column="USER_NICK" property="userNick" jdbcType="VARCHAR" />
    <result column="WEIGHT_SUM" property="weightSum" jdbcType="DECIMAL" />
    <result column="FEE_NAME" property="feeName" jdbcType="VARCHAR" />
    <result column="IS_PAY_SUCCESS" property="isPaySuccess" jdbcType="VARCHAR" />
    <result column="PAY_CURRENCY" property="payCurrency" jdbcType="VARCHAR" />
    <result column="CHARGE_AMOUNT" property="chargeAmount" jdbcType="DECIMAL" />
    <result column="ISTYPE" property="istype" jdbcType="VARCHAR" />
    <result column="TYPE" property="type" jdbcType="VARCHAR" />
    <result column="FILE_BATCH" property="fileBatch" jdbcType="VARCHAR" />
  </resultMap>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.Cainiaowulish" >
    insert into CAINIAO_CAINIAOWULISH (ZWYPART, LUJING, ZWYBIAOMING, 
      ZWYFOLDERNAME, ZWYTABLENAME, ZWYBILLMONTH, 
      ZWYLPNO, ZWYWEIGHTKG, ZWYFEENAME, 
      ZWYCURRENCY, ZWYAMOUNT, ZWYCOMMENT, 
      REMARK, ENTITY_VALUE, SERVICE_ITEM_CODE, 
      SERVICE_ITEM_NAME, USER_ID, MAX_CREATE_TIME, 
      MIN_CREATE_TIME, USER_NICK, WEIGHT_SUM, 
      FEE_NAME, IS_PAY_SUCCESS, PAY_CURRENCY, 
      CHARGE_AMOUNT, ISTYPE, TYPE, 
      FILE_BATCH)
    values (#{zwypart,jdbcType=VARCHAR}, #{lujing,jdbcType=VARCHAR}, #{zwybiaoming,jdbcType=VARCHAR}, 
      #{zwyfoldername,jdbcType=VARCHAR}, #{zwytablename,jdbcType=VARCHAR}, #{zwybillmonth,jdbcType=VARCHAR}, 
      #{zwylpno,jdbcType=VARCHAR}, #{zwyweightkg,jdbcType=DECIMAL}, #{zwyfeename,jdbcType=VARCHAR}, 
      #{zwycurrency,jdbcType=VARCHAR}, #{zwyamount,jdbcType=DECIMAL}, #{zwycomment,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{entityValue,jdbcType=VARCHAR}, #{serviceItemCode,jdbcType=VARCHAR}, 
      #{serviceItemName,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR}, #{maxCreateTime,jdbcType=VARCHAR}, 
      #{minCreateTime,jdbcType=VARCHAR}, #{userNick,jdbcType=VARCHAR}, #{weightSum,jdbcType=DECIMAL}, 
      #{feeName,jdbcType=VARCHAR}, #{isPaySuccess,jdbcType=VARCHAR}, #{payCurrency,jdbcType=VARCHAR}, 
      #{chargeAmount,jdbcType=DECIMAL}, #{istype,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, 
      #{fileBatch,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.Cainiaowulish" >
    insert into CAINIAO_CAINIAOWULISH
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="zwypart != null" >
        ZWYPART,
      </if>
      <if test="lujing != null" >
        LUJING,
      </if>
      <if test="zwybiaoming != null" >
        ZWYBIAOMING,
      </if>
      <if test="zwyfoldername != null" >
        ZWYFOLDERNAME,
      </if>
      <if test="zwytablename != null" >
        ZWYTABLENAME,
      </if>
      <if test="zwybillmonth != null" >
        ZWYBILLMONTH,
      </if>
      <if test="zwylpno != null" >
        ZWYLPNO,
      </if>
      <if test="zwyweightkg != null" >
        ZWYWEIGHTKG,
      </if>
      <if test="zwyfeename != null" >
        ZWYFEENAME,
      </if>
      <if test="zwycurrency != null" >
        ZWYCURRENCY,
      </if>
      <if test="zwyamount != null" >
        ZWYAMOUNT,
      </if>
      <if test="zwycomment != null" >
        ZWYCOMMENT,
      </if>
      <if test="remark != null" >
        REMARK,
      </if>
      <if test="entityValue != null" >
        ENTITY_VALUE,
      </if>
      <if test="serviceItemCode != null" >
        SERVICE_ITEM_CODE,
      </if>
      <if test="serviceItemName != null" >
        SERVICE_ITEM_NAME,
      </if>
      <if test="userId != null" >
        USER_ID,
      </if>
      <if test="maxCreateTime != null" >
        MAX_CREATE_TIME,
      </if>
      <if test="minCreateTime != null" >
        MIN_CREATE_TIME,
      </if>
      <if test="userNick != null" >
        USER_NICK,
      </if>
      <if test="weightSum != null" >
        WEIGHT_SUM,
      </if>
      <if test="feeName != null" >
        FEE_NAME,
      </if>
      <if test="isPaySuccess != null" >
        IS_PAY_SUCCESS,
      </if>
      <if test="payCurrency != null" >
        PAY_CURRENCY,
      </if>
      <if test="chargeAmount != null" >
        CHARGE_AMOUNT,
      </if>
      <if test="istype != null" >
        ISTYPE,
      </if>
      <if test="type != null" >
        TYPE,
      </if>
      <if test="fileBatch != null" >
        FILE_BATCH,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="zwypart != null" >
        #{zwypart,jdbcType=VARCHAR},
      </if>
      <if test="lujing != null" >
        #{lujing,jdbcType=VARCHAR},
      </if>
      <if test="zwybiaoming != null" >
        #{zwybiaoming,jdbcType=VARCHAR},
      </if>
      <if test="zwyfoldername != null" >
        #{zwyfoldername,jdbcType=VARCHAR},
      </if>
      <if test="zwytablename != null" >
        #{zwytablename,jdbcType=VARCHAR},
      </if>
      <if test="zwybillmonth != null" >
        #{zwybillmonth,jdbcType=VARCHAR},
      </if>
      <if test="zwylpno != null" >
        #{zwylpno,jdbcType=VARCHAR},
      </if>
      <if test="zwyweightkg != null" >
        #{zwyweightkg,jdbcType=DECIMAL},
      </if>
      <if test="zwyfeename != null" >
        #{zwyfeename,jdbcType=VARCHAR},
      </if>
      <if test="zwycurrency != null" >
        #{zwycurrency,jdbcType=VARCHAR},
      </if>
      <if test="zwyamount != null" >
        #{zwyamount,jdbcType=DECIMAL},
      </if>
      <if test="zwycomment != null" >
        #{zwycomment,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="entityValue != null" >
        #{entityValue,jdbcType=VARCHAR},
      </if>
      <if test="serviceItemCode != null" >
        #{serviceItemCode,jdbcType=VARCHAR},
      </if>
      <if test="serviceItemName != null" >
        #{serviceItemName,jdbcType=VARCHAR},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="maxCreateTime != null" >
        #{maxCreateTime,jdbcType=VARCHAR},
      </if>
      <if test="minCreateTime != null" >
        #{minCreateTime,jdbcType=VARCHAR},
      </if>
      <if test="userNick != null" >
        #{userNick,jdbcType=VARCHAR},
      </if>
      <if test="weightSum != null" >
        #{weightSum,jdbcType=DECIMAL},
      </if>
      <if test="feeName != null" >
        #{feeName,jdbcType=VARCHAR},
      </if>
      <if test="isPaySuccess != null" >
        #{isPaySuccess,jdbcType=VARCHAR},
      </if>
      <if test="payCurrency != null" >
        #{payCurrency,jdbcType=VARCHAR},
      </if>
      <if test="chargeAmount != null" >
        #{chargeAmount,jdbcType=DECIMAL},
      </if>
      <if test="istype != null" >
        #{istype,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="fileBatch != null" >
        #{fileBatch,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <insert id="insertBatch" parameterType="java.util.List" >
    insert into CAINIAO_CAINIAOWULISH (ZWYPART, LUJING, ZWYBIAOMING,
    ZWYFOLDERNAME, ZWYTABLENAME, ZWYBILLMONTH,
    ZWYLPNO, ZWYWEIGHTKG, ZWYFEENAME,
    ZWYCURRENCY, ZWYAMOUNT, ZWYCOMMENT,
    REMARK, ENTITY_VALUE, SERVICE_ITEM_CODE,
    SERVICE_ITEM_NAME, USER_ID, MAX_CREATE_TIME,
    MIN_CREATE_TIME, USER_NICK, WEIGHT_SUM,
    FEE_NAME, IS_PAY_SUCCESS, PAY_CURRENCY,
    CHARGE_AMOUNT, ISTYPE, TYPE,
    FILE_BATCH)
    <foreach collection="list" item="item" index="index" separator="UNION" >
      (SELECT
      #{item.zwypart,jdbcType=VARCHAR}, #{item.lujing,jdbcType=VARCHAR}, #{item.zwybiaoming,jdbcType=VARCHAR},
      #{item.zwyfoldername,jdbcType=VARCHAR}, #{item.zwytablename,jdbcType=VARCHAR}, #{item.zwybillmonth,jdbcType=VARCHAR},
      #{item.zwylpno,jdbcType=VARCHAR}, #{item.zwyweightkg,jdbcType=DECIMAL}, #{item.zwyfeename,jdbcType=VARCHAR},
      #{item.zwycurrency,jdbcType=VARCHAR}, #{item.zwyamount,jdbcType=DECIMAL}, #{item.zwycomment,jdbcType=VARCHAR},
      #{item.remark,jdbcType=VARCHAR}, #{item.entityValue,jdbcType=VARCHAR}, #{item.serviceItemCode,jdbcType=VARCHAR},
      #{item.serviceItemName,jdbcType=VARCHAR}, #{item.userId,jdbcType=VARCHAR}, #{item.maxCreateTime,jdbcType=VARCHAR},
      #{item.minCreateTime,jdbcType=VARCHAR}, #{item.userNick,jdbcType=VARCHAR}, #{item.weightSum,jdbcType=DECIMAL},
      #{item.feeName,jdbcType=VARCHAR}, #{item.isPaySuccess,jdbcType=VARCHAR}, #{item.payCurrency,jdbcType=VARCHAR},
      #{item.chargeAmount,jdbcType=DECIMAL}, #{item.istype,jdbcType=VARCHAR}, #{item.type,jdbcType=VARCHAR},
      #{item.fileBatch,jdbcType=VARCHAR}
      FROM dual)
    </foreach>
  </insert>
</mapper>