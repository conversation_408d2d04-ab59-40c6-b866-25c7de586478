<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.CheckCeosActivityResultMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.CheckCeosActivityResult" >
    <id column="CHECK_DATE" property="checkDate" jdbcType="DECIMAL" />
    <result column="ACTIVITY_NUM_CEOS" property="activityNumCeos" jdbcType="DECIMAL" />
    <result column="RESULT_TYPE" property="resultType" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    CHECK_DATE, ACTIVITY_NUM_CEOS,RESULT_TYPE
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from CHECK_CEOS_ACTIVITY_RESULT
    where CHECK_DATE = #{checkDate,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from CHECK_CEOS_ACTIVITY_RESULT
    where CHECK_DATE = #{checkDate,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.CheckCeosActivityResult" >
    insert into CHECK_CEOS_ACTIVITY_RESULT (CHECK_DATE, ACTIVITY_NUM_CEOS,RESULT_TYPE
    )
    values (#{checkDate,jdbcType=DECIMAL}, #{activityNumCeos,jdbcType=DECIMAL}, #{resultType,jdbcType=VARCHAR}
    )
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.CheckCeosActivityResult" >
    insert into CHECK_CEOS_ACTIVITY_RESULT
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="checkDate != null" >
        CHECK_DATE,
      </if>
      <if test="activityNumCeos != null" >
        ACTIVITY_NUM_CEOS,
      </if>
      <if test="resultType != null" >
        RESULT_TYPE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="checkDate != null" >
        #{checkDate,jdbcType=DECIMAL},
      </if>
      <if test="activityNumCeos != null" >
        #{activityNumCeos,jdbcType=DECIMAL},
      </if>
      <if test="resultType != null" >
        #{resultType,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.CheckCeosActivityResult" >
    update CHECK_CEOS_ACTIVITY_RESULT
    <set >
      <if test="activityNumCeos != null" >
        ACTIVITY_NUM_CEOS = #{activityNumCeos,jdbcType=DECIMAL},
      </if>
      <if test="resultType != null" >
        RESULT_TYPE = #{resultType,jdbcType=VARCHAR},
      </if>
    </set>
    where CHECK_DATE = #{checkDate,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.CheckCeosActivityResult" >
    update CHECK_CEOS_ACTIVITY_RESULT
    set ACTIVITY_NUM_CEOS = #{activityNumCeos,jdbcType=DECIMAL}
    where CHECK_DATE = #{checkDate,jdbcType=DECIMAL}
  </update>

  <insert id="insertBatch"  parameterType="java.util.List">
    insert into CHECK_CEOS_ACTIVITY_RESULT (CHECK_DATE, ACTIVITY_NUM_CEOS,RESULT_TYPE)
    <foreach collection="list" item="item" index="index" separator="UNION" >
      (select
      #{item.checkDate,jdbcType=DECIMAL},
      #{item.activityNumCeos,jdbcType=DECIMAL},
      #{item.resultType,jdbcType=VARCHAR}
      from dual)
    </foreach>
  </insert>

  <delete id="deleteBatch" parameterType="java.lang.String">
    delete from CHECK_CEOS_ACTIVITY_RESULT where RESULT_TYPE = #{resultType,jdbcType=VARCHAR}
  </delete>

  <select id="list" resultMap="BaseResultMap" parameterType="java.lang.String">
    select
    <include refid="Base_Column_List" />
    from CHECK_CEOS_ACTIVITY_RESULT
    where RESULT_TYPE = #{resultType,jdbcType=VARCHAR}
  </select>

  <select id="getCeosActivityResult" resultType="com.sinoair.billing.domain.model.billing.CheckCeosActivityResult">
      select d.check_date as checkDate,count(d.cerd_syscode) as activityNumCeos,'CEOS' as resultType
      from check_eawb_result_detail d
      where d.activity_status_ceos = 'N'
      group by d.check_date
  </select>

  <select id="getActivityResult" resultType="com.sinoair.billing.domain.model.billing.CheckCeosActivityResult">
    select check_Date as checkDate,count(0) as activityNumCeos,'ACTIVITY' as resultType
    from check_eawb_result_detail
    where activity_status = 'N'
    group by check_date
  </select>
  <select id="getRRResult" resultType="com.sinoair.billing.domain.model.billing.CheckCeosActivityResult">
    select check_Date as checkDate,count(0) as activityNumCeos,'RR' as resultType
    from check_eawb_result_detail
    where rr_status = 'N'
    group by check_date
  </select>

</mapper>