<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.CheckEawbNoMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.CheckEawbNo" >
    <id column="CHECK_ID" property="checkId" jdbcType="DECIMAL" />
    <result column="CHECK_TYPE" property="checkType" jdbcType="VARCHAR" />
    <result column="FROM_NO" property="fromNo" jdbcType="DECIMAL" />
    <result column="BEGIN_NO" property="beginNo" jdbcType="DECIMAL" />
    <result column="END_NO" property="endNo" jdbcType="DECIMAL" />
    <result column="HANDLE_DATE" property="handleDate" jdbcType="TIMESTAMP" />
    <result column="REMARKS" property="remarks" jdbcType="VARCHAR" />
    <result column="HANDLE_SIZE" property="handleSize" jdbcType="DECIMAL" />
  </resultMap>
  <sql id="Base_Column_List" >
    CHECK_ID, CHECK_TYPE, FROM_NO, BEGIN_NO, END_NO, HANDLE_DATE,REMARKS,HANDLE_SIZE
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Short" >
    select 
    <include refid="Base_Column_List" />
    from CHECK_EAWB_NO
    where CHECK_ID = #{checkId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Short" >
    delete from CHECK_EAWB_NO
    where CHECK_ID = #{checkId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.CheckEawbNo" >
    insert into CHECK_EAWB_NO (CHECK_ID, CHECK_TYPE, FROM_NO, 
      BEGIN_NO, END_NO, HANDLE_DATE
      )
    values (#{checkId,jdbcType=DECIMAL}, #{checkType,jdbcType=VARCHAR}, #{fromNo,jdbcType=DECIMAL}, 
      #{beginNo,jdbcType=DECIMAL}, #{endNo,jdbcType=DECIMAL}, #{handleDate,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.CheckEawbNo" >
    insert into CHECK_EAWB_NO
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="checkId != null" >
        CHECK_ID,
      </if>
      <if test="checkType != null" >
        CHECK_TYPE,
      </if>
      <if test="fromNo != null" >
        FROM_NO,
      </if>
      <if test="beginNo != null" >
        BEGIN_NO,
      </if>
      <if test="endNo != null" >
        END_NO,
      </if>
      <if test="handleDate != null" >
        HANDLE_DATE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="checkId != null" >
        #{checkId,jdbcType=DECIMAL},
      </if>
      <if test="checkType != null" >
        #{checkType,jdbcType=VARCHAR},
      </if>
      <if test="fromNo != null" >
        #{fromNo,jdbcType=DECIMAL},
      </if>
      <if test="beginNo != null" >
        #{beginNo,jdbcType=DECIMAL},
      </if>
      <if test="endNo != null" >
        #{endNo,jdbcType=DECIMAL},
      </if>
      <if test="handleDate != null" >
        #{handleDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.CheckEawbNo" >
    update CHECK_EAWB_NO
    <set >
      <if test="checkType != null" >
        CHECK_TYPE = #{checkType,jdbcType=VARCHAR},
      </if>
      <if test="fromNo != null" >
        FROM_NO = #{fromNo,jdbcType=DECIMAL},
      </if>
      <if test="beginNo != null" >
        BEGIN_NO = #{beginNo,jdbcType=DECIMAL},
      </if>
      <if test="endNo != null" >
        END_NO = #{endNo,jdbcType=DECIMAL},
      </if>
      <if test="handleDate != null" >
        HANDLE_DATE = #{handleDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where CHECK_ID = #{checkId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.CheckEawbNo" >
    update CHECK_EAWB_NO
    set CHECK_TYPE = #{checkType,jdbcType=VARCHAR},
      FROM_NO = #{fromNo,jdbcType=DECIMAL},
      BEGIN_NO = #{beginNo,jdbcType=DECIMAL},
      END_NO = #{endNo,jdbcType=DECIMAL},
      HANDLE_DATE = #{handleDate,jdbcType=TIMESTAMP}
    where CHECK_ID = #{checkId,jdbcType=DECIMAL}
  </update>

  <select id="selectByType" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select
    <include refid="Base_Column_List" />
    from CHECK_EAWB_NO
    where CHECK_TYPE = #{checkType,jdbcType=VARCHAR}
  </select>
</mapper>