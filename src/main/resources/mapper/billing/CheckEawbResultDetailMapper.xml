<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.CheckEawbResultDetailMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.CheckEawbResultDetail" >
    <id column="CERD_SYSCODE" property="cerdSyscode" jdbcType="DECIMAL" />
    <result column="EAWB_PRINTCODE" property="eawbPrintcode" jdbcType="VARCHAR" />
    <result column="SO_CODE" property="soCode" jdbcType="VARCHAR" />
    <result column="PR_NAME" property="prName" jdbcType="VARCHAR" />
    <result column="EAD_CODE" property="eadCode" jdbcType="VARCHAR" />
    <result column="EAST_CODE" property="eastCode" jdbcType="VARCHAR" />
    <result column="SERVICETYPE" property="servicetype" jdbcType="VARCHAR" />
    <result column="OCC_COMPANY_ID" property="occCompanyId" jdbcType="VARCHAR" />
    <result column="ACTIVITY_STATUS_CEOS" property="activityStatusCeos" jdbcType="VARCHAR" />
    <result column="ACTIVITY_STATUS" property="activityStatus" jdbcType="VARCHAR" />
    <result column="RR_STATUS" property="rrStatus" jdbcType="VARCHAR" />
    <result column="BILL_STATUS" property="billStatus" jdbcType="VARCHAR" />
    <result column="CHECK_DATE" property="checkDate" jdbcType="DECIMAL" />
    <result column="CHECK_MONTH" property="checkMonth" jdbcType="DECIMAL" />
    <result column="CHECK_YEAR" property="checkYear" jdbcType="DECIMAL" />
    <result column="CHECK_HANDLETIME" property="checkHandletime" jdbcType="TIMESTAMP" />
    <result column="PR_ID" property="prId" jdbcType="DECIMAL" />
    <result column="EBA_HANDLETIME" property="ebaHandletime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    CERD_SYSCODE, EAWB_PRINTCODE, SO_CODE, PR_NAME, EAD_CODE, EAST_CODE, SERVICETYPE, 
    OCC_COMPANY_ID, ACTIVITY_STATUS_CEOS, ACTIVITY_STATUS, RR_STATUS, BILL_STATUS, CHECK_DATE, 
    CHECK_MONTH, CHECK_YEAR, CHECK_HANDLETIME,PR_ID,EBA_HANDLETIME
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from CHECK_EAWB_RESULT_DETAIL
    where CERD_SYSCODE = #{cerdSyscode,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from CHECK_EAWB_RESULT_DETAIL
    where CERD_SYSCODE = #{cerdSyscode,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.CheckEawbResultDetail" >
    insert into CHECK_EAWB_RESULT_DETAIL (CERD_SYSCODE, EAWB_PRINTCODE, SO_CODE, 
      PR_NAME, EAD_CODE, EAST_CODE, 
      SERVICETYPE, OCC_COMPANY_ID, ACTIVITY_STATUS_CEOS, 
      ACTIVITY_STATUS, RR_STATUS, BILL_STATUS, 
      CHECK_DATE, CHECK_MONTH, CHECK_YEAR, 
      CHECK_HANDLETIME)
    values (#{cerdSyscode,jdbcType=DECIMAL}, #{eawbPrintcode,jdbcType=VARCHAR}, #{soCode,jdbcType=VARCHAR}, 
      #{prName,jdbcType=VARCHAR}, #{eadCode,jdbcType=VARCHAR}, #{eastCode,jdbcType=VARCHAR}, 
      #{servicetype,jdbcType=VARCHAR}, #{occCompanyId,jdbcType=VARCHAR}, #{activityStatusCeos,jdbcType=VARCHAR}, 
      #{activityStatus,jdbcType=VARCHAR}, #{rrStatus,jdbcType=VARCHAR}, #{billStatus,jdbcType=VARCHAR}, 
      #{checkDate,jdbcType=DECIMAL}, #{checkMonth,jdbcType=DECIMAL}, #{checkYear,jdbcType=DECIMAL}, 
      #{checkHandletime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.CheckEawbResultDetail" >
    insert into CHECK_EAWB_RESULT_DETAIL
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="cerdSyscode != null" >
        CERD_SYSCODE,
      </if>
      <if test="eawbPrintcode != null" >
        EAWB_PRINTCODE,
      </if>
      <if test="soCode != null" >
        SO_CODE,
      </if>
      <if test="prName != null" >
        PR_NAME,
      </if>
      <if test="eadCode != null" >
        EAD_CODE,
      </if>
      <if test="eastCode != null" >
        EAST_CODE,
      </if>
      <if test="servicetype != null" >
        SERVICETYPE,
      </if>
      <if test="occCompanyId != null" >
        OCC_COMPANY_ID,
      </if>
      <if test="activityStatusCeos != null" >
        ACTIVITY_STATUS_CEOS,
      </if>
      <if test="activityStatus != null" >
        ACTIVITY_STATUS,
      </if>
      <if test="rrStatus != null" >
        RR_STATUS,
      </if>
      <if test="billStatus != null" >
        BILL_STATUS,
      </if>
      <if test="checkDate != null" >
        CHECK_DATE,
      </if>
      <if test="checkMonth != null" >
        CHECK_MONTH,
      </if>
      <if test="checkYear != null" >
        CHECK_YEAR,
      </if>
      <if test="checkHandletime != null" >
        CHECK_HANDLETIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="cerdSyscode != null" >
        #{cerdSyscode,jdbcType=DECIMAL},
      </if>
      <if test="eawbPrintcode != null" >
        #{eawbPrintcode,jdbcType=VARCHAR},
      </if>
      <if test="soCode != null" >
        #{soCode,jdbcType=VARCHAR},
      </if>
      <if test="prName != null" >
        #{prName,jdbcType=VARCHAR},
      </if>
      <if test="eadCode != null" >
        #{eadCode,jdbcType=VARCHAR},
      </if>
      <if test="eastCode != null" >
        #{eastCode,jdbcType=VARCHAR},
      </if>
      <if test="servicetype != null" >
        #{servicetype,jdbcType=VARCHAR},
      </if>
      <if test="occCompanyId != null" >
        #{occCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="activityStatusCeos != null" >
        #{activityStatusCeos,jdbcType=VARCHAR},
      </if>
      <if test="activityStatus != null" >
        #{activityStatus,jdbcType=VARCHAR},
      </if>
      <if test="rrStatus != null" >
        #{rrStatus,jdbcType=VARCHAR},
      </if>
      <if test="billStatus != null" >
        #{billStatus,jdbcType=VARCHAR},
      </if>
      <if test="checkDate != null" >
        #{checkDate,jdbcType=DECIMAL},
      </if>
      <if test="checkMonth != null" >
        #{checkMonth,jdbcType=DECIMAL},
      </if>
      <if test="checkYear != null" >
        #{checkYear,jdbcType=DECIMAL},
      </if>
      <if test="checkHandletime != null" >
        #{checkHandletime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.CheckEawbResultDetail" >
    update CHECK_EAWB_RESULT_DETAIL
    <set >
      <if test="eawbPrintcode != null" >
        EAWB_PRINTCODE = #{eawbPrintcode,jdbcType=VARCHAR},
      </if>
      <if test="soCode != null" >
        SO_CODE = #{soCode,jdbcType=VARCHAR},
      </if>
      <if test="prName != null" >
        PR_NAME = #{prName,jdbcType=VARCHAR},
      </if>
      <if test="eadCode != null" >
        EAD_CODE = #{eadCode,jdbcType=VARCHAR},
      </if>
      <if test="eastCode != null" >
        EAST_CODE = #{eastCode,jdbcType=VARCHAR},
      </if>
      <if test="servicetype != null" >
        SERVICETYPE = #{servicetype,jdbcType=VARCHAR},
      </if>
      <if test="occCompanyId != null" >
        OCC_COMPANY_ID = #{occCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="activityStatusCeos != null" >
        ACTIVITY_STATUS_CEOS = #{activityStatusCeos,jdbcType=VARCHAR},
      </if>
      <if test="activityStatus != null" >
        ACTIVITY_STATUS = #{activityStatus,jdbcType=VARCHAR},
      </if>
      <if test="rrStatus != null" >
        RR_STATUS = #{rrStatus,jdbcType=VARCHAR},
      </if>
      <if test="billStatus != null" >
        BILL_STATUS = #{billStatus,jdbcType=VARCHAR},
      </if>
      <if test="checkDate != null" >
        CHECK_DATE = #{checkDate,jdbcType=DECIMAL},
      </if>
      <if test="checkMonth != null" >
        CHECK_MONTH = #{checkMonth,jdbcType=DECIMAL},
      </if>
      <if test="checkYear != null" >
        CHECK_YEAR = #{checkYear,jdbcType=DECIMAL},
      </if>
      <if test="checkHandletime != null" >
        CHECK_HANDLETIME = #{checkHandletime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where CERD_SYSCODE = #{cerdSyscode,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.CheckEawbResultDetail" >
    update CHECK_EAWB_RESULT_DETAIL
    set EAWB_PRINTCODE = #{eawbPrintcode,jdbcType=VARCHAR},
      SO_CODE = #{soCode,jdbcType=VARCHAR},
      PR_NAME = #{prName,jdbcType=VARCHAR},
      EAD_CODE = #{eadCode,jdbcType=VARCHAR},
      EAST_CODE = #{eastCode,jdbcType=VARCHAR},
      SERVICETYPE = #{servicetype,jdbcType=VARCHAR},
      OCC_COMPANY_ID = #{occCompanyId,jdbcType=VARCHAR},
      ACTIVITY_STATUS_CEOS = #{activityStatusCeos,jdbcType=VARCHAR},
      ACTIVITY_STATUS = #{activityStatus,jdbcType=VARCHAR},
      RR_STATUS = #{rrStatus,jdbcType=VARCHAR},
      BILL_STATUS = #{billStatus,jdbcType=VARCHAR},
      CHECK_DATE = #{checkDate,jdbcType=DECIMAL},
      CHECK_MONTH = #{checkMonth,jdbcType=DECIMAL},
      CHECK_YEAR = #{checkYear,jdbcType=DECIMAL},
      CHECK_HANDLETIME = #{checkHandletime,jdbcType=TIMESTAMP}
    where CERD_SYSCODE = #{cerdSyscode,jdbcType=DECIMAL}
  </update>

  <insert id="insertBatch"  parameterType="java.util.List">
    insert into CHECK_EAWB_RESULT_DETAIL (CERD_SYSCODE, EAWB_PRINTCODE, SO_CODE, PR_NAME, EAD_CODE, EAST_CODE, SERVICETYPE,
    OCC_COMPANY_ID, ACTIVITY_STATUS_CEOS, ACTIVITY_STATUS, RR_STATUS, BILL_STATUS, CHECK_DATE,
    CHECK_MONTH, CHECK_YEAR, CHECK_HANDLETIME,PR_ID,EBA_HANDLETIME)
    select SEQ_CHECK_DETAIL.NEXTVAL,pbd.* from (
    <foreach collection="list" item="item" index="index" separator="UNION ALL">
      select #{item.eawbPrintcode,jdbcType=VARCHAR}, #{item.soCode,jdbcType=VARCHAR},
      #{item.prName,jdbcType=VARCHAR}, #{item.eadCode,jdbcType=VARCHAR}, #{item.eastCode,jdbcType=VARCHAR},
      #{item.servicetype,jdbcType=VARCHAR}, #{item.occCompanyId,jdbcType=VARCHAR}, #{item.activityStatusCeos,jdbcType=VARCHAR},
      #{item.activityStatus,jdbcType=VARCHAR}, #{item.rrStatus,jdbcType=VARCHAR}, #{item.billStatus,jdbcType=VARCHAR},
      #{item.checkDate,jdbcType=DECIMAL}, #{item.checkMonth,jdbcType=DECIMAL}, #{item.checkYear,jdbcType=DECIMAL},
      #{item.checkHandletime,jdbcType=TIMESTAMP},#{item.prId,jdbcType=DECIMAL},#{item.ebaHandletime,jdbcType=TIMESTAMP} from dual
    </foreach>
    ) pbd
  </insert>

  <select id="selectDetailByPrintCode"  parameterType="java.lang.String" resultType="com.sinoair.billing.domain.model.billing.CheckEawbResultDetail">

        select  distinct rr.pr_id,e.eawb_printcode,e.eawb_so_code as so_code,rr.pr_name,rr.ead_code,rr.east_code,
        p.p_servicetype_original as servicetype,p.occ_company_id,
        to_char(e.eawb_keyentrytime, 'yyyymmdd') as checkDate,rr.pr_special_key,
        getSpecialKey_RR3(e.eawb_printcode,rr.pr_id) as resSpecialKey
        from
        expressairwaybill e,product p,PRICE_RECEIPT rr,product_customer pc
        where 1=1
        and e.eawb_outbound_sac_id = p.occ_company_id
        and e.eawb_servicetype_original = p.p_servicetype_original
        and e.eawb_so_code = pc.so_code
        and p.p_id = pc.p_id
        and pc.pc_id = rr.pc_id
        and rr.pr_status = 'ON'
        and pr_auto = 'Y'
        and rr.pr_expireddate >= trunc(e.eawb_keyentrytime, 'dd')
        <![CDATA[
        and rr.pr_effectivedate <= trunc(e.eawb_keyentrytime, 'dd')
        ]]>
        and e.eawb_printcode = #{eawbPrintcode,jdbcType=VARCHAR}

  </select>

    <select id="selectResultDetailList" resultMap="BaseResultMap" parameterType="com.sinoair.billing.domain.vo.check.EawbCheckVO" >
  select
  <include refid="Base_Column_List" />
  from CHECK_EAWB_RESULT_DETAIL
  where 1=1
  <if test="activityStatus != null" >
    and  ACTIVITY_STATUS = #{activityStatus,jdbcType=VARCHAR}
  </if>
  <if test="activityStatusCeos != null" >
    and ACTIVITY_STATUS_CEOS = #{activityStatusCeos,jdbcType=VARCHAR}
  </if>
  <if test="rrStatus != null" >
    and RR_STATUS = #{rrStatus,jdbcType=VARCHAR}
  </if>
  <if test="checkPr != null" >
    and  PR_ID is null
  </if>
  <if test="eawbPrintcode != null" >
    and EAWB_PRINTCODE = #{eawbPrintcode,jdbcType=VARCHAR}
  </if>
  <if test="eadCode != null" >
    and EAD_CODE = #{eadCode,jdbcType=VARCHAR}
  </if>
  <if test="eastCode != null" >
    and EAST_CODE = #{eastCode,jdbcType=VARCHAR}
  </if>
    <if test="prName != null" >
        and PR_NAME = #{prName,jdbcType=VARCHAR}
    </if>
    <if test="prId != null" >
        and PR_ID = #{prId,jdbcType=DECIMAL}
    </if>
      <if test="checkHandleTime != null" >
      <![CDATA[
      and check_handletime < trunc(sysdate,'dd')
      ]]>
      </if>
</select>

  <select id="selectRRDetailList" resultType="com.sinoair.billing.domain.model.billing.CheckEawbResultDetail" parameterType="com.sinoair.billing.domain.vo.check.EawbCheckVO" >
    select
      d.*,eawb.mawb_code,eawb.sac_id,eawb.eawb_reference1,
      eawb.eawb_reference2,eawb.eawb_chargeableweight,
      eawb.eawb_quantity,eawb.eawb_servicetype_original,
      eawb.eawb_outbound_sac_id,eawb.eawb_destcountry,
      eawb.eawb_destination,eawb.eawb_departcountry,
      eawb.eawb_departure
      from check_eawb_result_detail d,expressairwaybill eawb
      where d.eawb_printcode = eawb.eawb_printcode
    <if test="activityStatus != null" >
      and  ACTIVITY_STATUS = #{activityStatus,jdbcType=VARCHAR}
    </if>
    <if test="activityStatusCeos != null" >
      and ACTIVITY_STATUS_CEOS = #{activityStatusCeos,jdbcType=VARCHAR}
    </if>
    <if test="rrStatus != null" >
      and RR_STATUS = #{rrStatus,jdbcType=VARCHAR}
    </if>
    <if test="checkPr != null" >
      and  PR_ID is null
    </if>
    <if test="eawbPrintcode != null" >
      and EAWB_PRINTCODE = #{eawbPrintcode,jdbcType=VARCHAR}
    </if>
    <if test="eadCode != null" >
      and EAD_CODE = #{eadCode,jdbcType=VARCHAR}
    </if>
    <if test="eastCode != null" >
      and EAST_CODE = #{eastCode,jdbcType=VARCHAR}
    </if>
    <if test="prName != null" >
      and PR_NAME = #{prName,jdbcType=VARCHAR}
    </if>
    <if test="prId != null" >
      and PR_ID = #{prId,jdbcType=DECIMAL}
    </if>
    <if test="checkHandleTime != null" >
      <![CDATA[
      and check_handletime < trunc(sysdate,'dd')
      ]]>
    </if>
  </select>

  <select id="countResultDetailList" resultType="java.lang.Integer" parameterType="com.sinoair.billing.domain.vo.check.EawbCheckVO" >
    select
    count(0)
    from CHECK_EAWB_RESULT_DETAIL
    where 1=1
    <if test="activityStatus != null" >
      and  ACTIVITY_STATUS = #{activityStatus,jdbcType=VARCHAR}
    </if>
    <if test="activityStatusCeos != null" >
      and ACTIVITY_STATUS_CEOS = #{activityStatusCeos,jdbcType=VARCHAR}
    </if>
    <if test="rrStatus != null" >
      and RR_STATUS = #{rrStatus,jdbcType=VARCHAR}
    </if>
    <if test="checkPr != null" >
      and  PR_ID is null
    </if>

    <if test="eawbPrintcode != null" >
      and EAWB_PRINTCODE = #{eawbPrintcode,jdbcType=VARCHAR}
    </if>
    <if test="eadCode != null" >
      and EAD_CODE = #{eadCode,jdbcType=VARCHAR}
    </if>
    <if test="eastCode != null" >
      and EAST_CODE = #{eastCode,jdbcType=VARCHAR}
    </if>
      <if test="prName != null" >
          and PR_NAME = #{prName,jdbcType=VARCHAR}
      </if>
      <if test="prId != null" >
          and PR_ID = #{prId,jdbcType=DECIMAL}
      </if>
    <if test="checkHandleTime != null" >
      <![CDATA[
      and check_handletime < trunc(sysdate,'dd')
      ]]>
    </if>
  </select>

    <update id="updateBatch" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
            update CHECK_EAWB_RESULT_DETAIL
            <set>

                <if test="item.activityStatus != null" >
                    ACTIVITY_STATUS = #{item.activityStatus,jdbcType=VARCHAR},
                </if>
                <if test="item.rrStatus != null" >
                    RR_STATUS = #{item.rrStatus,jdbcType=VARCHAR},
                </if>
                <if test="item.activityStatusCeos != null" >
                    ACTIVITY_STATUS_CEOS = #{item.activityStatusCeos,jdbcType=VARCHAR},
                </if>
                <if test="item.ebaHandletime != null" >
                    EBA_HANDLETIME = #{item.ebaHandletime,jdbcType=TIMESTAMP},
                </if>
              <if test="item.billStatus != null" >
                BILL_STATUS = #{item.billStatus,jdbcType=VARCHAR},
              </if>
              <if test="item.prId != null" >
                PR_ID = #{item.prId,jdbcType=DECIMAL},
              </if>
                CHECK_HANDLETIME = sysdate
            </set>
            where CERD_SYSCODE = #{item.cerdSyscode,jdbcType=DECIMAL}
        </foreach>
    </update>

  <select id="selectMaxSysCode" resultType="java.lang.Long">
    select max(cerd_syscode) from check_eawb_result_detail
  </select>

  <select id="selectMinDetailSysCode" resultType="java.lang.Long">
    select cerd_syscode from check_eawb_result_detail
    where activity_status_ceos = 'N'
    <![CDATA[
        and rownum < 2
        ]]>
    order by cerd_syscode
  </select>

  <select id="selectEawbBySysCode" resultMap="BaseResultMap"
          parameterType="com.sinoair.billing.domain.vo.check.EawbCheckVO">

    SELECT
    <include refid="Base_Column_List" />
    FROM check_eawb_result_detail
    WHERE cerd_syscode >= #{beginNo}
    <![CDATA[

        ]]>
      and activity_status_ceos='N'
    <![CDATA[
      and rownum < 20001
    ]]>
    order by cerd_syscode
  </select>

  <insert id="insertBatchHis"  parameterType="java.util.List">
    insert into CHECK_EAWB_RESULT_DETAIL_HIS (CERD_SYSCODE, EAWB_PRINTCODE, SO_CODE, PR_NAME, EAD_CODE, EAST_CODE, SERVICETYPE,
    OCC_COMPANY_ID, ACTIVITY_STATUS_CEOS, ACTIVITY_STATUS, RR_STATUS, BILL_STATUS, CHECK_DATE,
    CHECK_MONTH, CHECK_YEAR, CHECK_HANDLETIME,PR_ID,EBA_HANDLETIME)

    <foreach collection="list" item="item" index="index" separator="UNION ALL">
      select #{item.cerdSyscode,jdbcType=DECIMAL},#{item.eawbPrintcode,jdbcType=VARCHAR}, #{item.soCode,jdbcType=VARCHAR},
      #{item.prName,jdbcType=VARCHAR}, #{item.eadCode,jdbcType=VARCHAR}, #{item.eastCode,jdbcType=VARCHAR},
      #{item.servicetype,jdbcType=VARCHAR}, #{item.occCompanyId,jdbcType=VARCHAR}, #{item.activityStatusCeos,jdbcType=VARCHAR},
      #{item.activityStatus,jdbcType=VARCHAR}, #{item.rrStatus,jdbcType=VARCHAR}, #{item.billStatus,jdbcType=VARCHAR},
      #{item.checkDate,jdbcType=DECIMAL}, #{item.checkMonth,jdbcType=DECIMAL}, #{item.checkYear,jdbcType=DECIMAL},
      #{item.checkHandletime,jdbcType=TIMESTAMP},#{item.prId,jdbcType=DECIMAL},#{item.ebaHandletime,jdbcType=TIMESTAMP} from dual
    </foreach>

  </insert>

    <delete id="deleteBatch" parameterType="java.util.List" >
        delete from CHECK_EAWB_RESULT_DETAIL
        where CERD_SYSCODE in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item.cerdSyscode,jdbcType=DECIMAL}
        </foreach>
    </delete>

  <update id="updateBatchByOther" parameterType="java.util.List">
    <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
      update CHECK_EAWB_RESULT_DETAIL
      <set>

        <if test="item.activityStatus != null" >
          ACTIVITY_STATUS = #{item.activityStatus,jdbcType=VARCHAR},
        </if>
        <if test="item.rrStatus != null" >
          RR_STATUS = #{item.rrStatus,jdbcType=VARCHAR},
        </if>
        <if test="item.activityStatusCeos != null" >
          ACTIVITY_STATUS_CEOS = #{item.activityStatusCeos,jdbcType=VARCHAR},
        </if>
        <if test="item.ebaHandletime != null" >
          EBA_HANDLETIME = #{item.ebaHandletime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.prId != null" >
          PR_ID = #{item.prId,jdbcType=DECIMAL},
        </if>
        CHECK_HANDLETIME = sysdate
      </set>
      where EAWB_PRINTCODE = #{item.eawbPrintcode,jdbcType=VARCHAR}
      and SO_CODE = #{item.soCode,jdbcType=VARCHAR}
      and PR_NAME = #{item.prName,jdbcType=VARCHAR}
      and EAD_CODE = #{item.eadCode,jdbcType=VARCHAR}
      and EAST_CODE = #{item.eastCode,jdbcType=VARCHAR}
      and SERVICETYPE = #{item.servicetype,jdbcType=VARCHAR}
    </foreach>
  </update>

  <select id="selectDetailEndNo" resultType="java.lang.Long" parameterType="com.sinoair.billing.domain.vo.check.EawbCheckVO">
    select max(a.cerd_syscode) from (
      select cerd_syscode from check_eawb_result_detail
      where activity_status_ceos = 'N'
      and cerd_syscode >= #{beginNo}
    <![CDATA[
      and rownum < 20001
    ]]>
    order by cerd_syscode
    ) a
  </select>

  <select id="countByPrintcode" resultType="java.lang.Integer" parameterType="java.lang.String" >
    select
    count(0)
    from check_eawb_result_detail
    where EAWB_PRINTCODE = #{eawbPrintcode,jdbcType=VARCHAR}
  </select>

  <select id="selectDetailByCondition"  parameterType="com.sinoair.billing.domain.vo.check.EawbCheckVO" resultType="com.sinoair.billing.domain.model.billing.CheckEawbResultDetail">
    select  distinct rr.pr_id,e.eawb_printcode,e.eawb_so_code as so_code,rr.pr_name,rr.ead_code,rr.east_code,
    p.p_servicetype_original as servicetype,p.occ_company_id,
    to_char(e.eawb_keyentrytime, 'yyyymmdd') as checkDate,rr.pr_special_key,
    getSpecialKey_RR3(e.eawb_printcode,rr.pr_id) as resSpecialKey,rr.dest_sac_id,e.mawb_code
    from
    expressairwaybill e,product p,PRICE_RECEIPT rr,product_customer pc
    where 1=1
    and e.eawb_outbound_sac_id = p.occ_company_id
    and e.eawb_servicetype_original = p.p_servicetype_original
    and e.eawb_so_code = pc.so_code
    and p.p_id = pc.p_id
    and pc.pc_id = rr.pc_id
    and rr.pr_status = 'ON'
    and pr_auto = 'Y'
    and rr.pr_expireddate >= trunc(e.eawb_keyentrytime, 'dd')
    <![CDATA[
      and rr.pr_effectivedate <= trunc(e.eawb_keyentrytime, 'dd')
      ]]>
    and e.eawb_printcode = #{eawbPrintcode,jdbcType=VARCHAR}
    and rr.ead_code = #{eadCode,jdbcType=VARCHAR}
    and rr.east_code = #{eastCode,jdbcType=VARCHAR}
    <if test="checkPr != null" >
      and rr.pr_special_key != 'N'
    </if>
    <if test="prName != null" >
      and rr.pr_name = #{prName,jdbcType=VARCHAR}
    </if>
  </select>

  <select id="selectNonDetailByCondition"  parameterType="com.sinoair.billing.domain.vo.check.EawbCheckVO" resultType="com.sinoair.billing.domain.model.billing.CheckEawbResultDetail">
    select  distinct rr.pr_id,e.eawb_printcode,e.eawb_so_code as so_code,rr.pr_name,rr.ead_code,rr.east_code,
    p.p_servicetype_original as servicetype,p.occ_company_id,
    to_char(e.eawb_keyentrytime, 'yyyymmdd') as checkDate,rr.pr_special_key,
    getSpecialKey_RR3(e.eawb_printcode,rr.pr_id) as resSpecialKey,rr.dest_sac_id,e.mawb_code
    from
    expressairwaybill e,
    expressbusinessactivity eba,
    product p,PRICE_RECEIPT rr,product_customer pc
    where 1=1
    and e.eawb_outbound_sac_id = p.occ_company_id
    and e.eawb_servicetype_original = p.p_servicetype_original
    and e.eawb_so_code = pc.so_code
    and p.p_id = pc.p_id
    and e.eawb_printcode=eba.eawb_printcode
    and eba.ead_code = rr.ead_code
    and eba.east_code = rr.east_code
    and pc.pc_id = rr.pc_id
    and rr.pr_status = 'ON'
    and pr_auto = 'Y'
    and rr.pr_expireddate >= trunc(eba.eba_handletime, 'dd')
    <![CDATA[
      and rr.pr_effectivedate <= trunc(eba.eba_handletime, 'dd')
      ]]>
    and e.eawb_printcode = #{eawbPrintcode,jdbcType=VARCHAR}
    and rr.ead_code = #{eadCode,jdbcType=VARCHAR}
    and rr.east_code = #{eastCode,jdbcType=VARCHAR}
    <if test="checkPr != null" >
      and rr.pr_special_key != 'N'
    </if>
    <if test="prName != null" >
      and rr.pr_name = #{prName,jdbcType=VARCHAR}
    </if>
  </select>

  <select id="selectActivityCeosList" resultMap="BaseResultMap" parameterType="com.sinoair.billing.domain.vo.check.EawbCheckVO" >
    select <include refid="Base_Column_List" />
    from check_eawb_result_detail
    where 1=1
    <if test="beginNo != null" >
      and cerd_syscode >= #{beginNo}
    </if>
    <if test="endNo != null" >
      <![CDATA[
      and cerd_syscode < #{endNo}
      ]]>
    </if>
    <if test="activityStatusCeos != null" >
      and activity_status_ceos = #{activityStatusCeos}
    </if>
    <if test="eastCode != null" >
      and EAST_CODE = #{eastCode,jdbcType=VARCHAR}
    </if>
    <if test="checkDate != null" >
      <![CDATA[
      and check_date = #{checkDate,jdbcType=DECIMAL}
      ]]>
    </if>
  </select>

  <select id="countActivityCeosList" resultType="java.lang.Integer" parameterType="com.sinoair.billing.domain.vo.check.EawbCheckVO" >
    select count(0)
    from check_eawb_result_detail
    where 1=1
    <if test="activityStatusCeos != null" >
      and activity_status_ceos = #{activityStatusCeos}
    </if>
    <if test="beginNo != null" >
      and cerd_syscode >= #{beginNo}
    </if>
    <if test="endNo != null" >
      <![CDATA[
      and cerd_syscode < #{endNo}
      ]]>
    </if>
    <if test="eastCode != null" >
      and EAST_CODE = #{eastCode,jdbcType=VARCHAR}
    </if>
    <if test="checkDate != null" >
      <![CDATA[
      and check_date = #{checkDate,jdbcType=DECIMAL}
      ]]>
    </if>
  </select>

  <insert id="insertBatchLackEawb"  parameterType="java.util.List">
    insert into check_lack_eawb (eawb_printcode)

    <foreach collection="list" item="item" index="index" separator="UNION ALL">
      select #{item,jdbcType=VARCHAR} from dual
    </foreach>

  </insert>

  <insert id="insertBatchLackEawbMo"  parameterType="java.util.List">
    insert into check_lack_eawb_monitor (eawb_printcode)

    <foreach collection="list" item="item" index="index" separator="UNION ALL">
      select #{item,jdbcType=VARCHAR} from dual
    </foreach>

  </insert>

  <select id="listLackEawb"  resultType="java.lang.String">
    select * from check_lack_eawb

  </select>

  <select id="countListLackEawb"  resultType="java.lang.Integer">
    select count(0) from check_lack_eawb

  </select>

  <delete id="deleteBatchLackEawb" parameterType="java.util.List" >
    delete from check_lack_eawb
    where eawb_printcode in
    <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
      #{item}
    </foreach>
  </delete>

  <insert id="insertBatchLackRRMawb"  parameterType="java.util.List">
    insert into CHECK_LACK_RR_MAWB (eawb_printcode,so_code)

    <foreach collection="list" item="item" index="index" separator="UNION ALL">
      select #{item.eawbPrintcode,jdbcType=VARCHAR},#{item.soCode,jdbcType=VARCHAR} from dual
    </foreach>

  </insert>

  <select id="listLackRRMawb"  resultType="com.sinoair.billing.domain.model.billing.CheckLackRRMawb">
    select * from CHECK_LACK_RR_MAWB

  </select>

  <select id="countListLackRRMawb"  resultType="java.lang.Integer">
    select count(0) from CHECK_LACK_RR_MAWB

  </select>

  <delete id="deleteBatchLackRRMawb" parameterType="java.util.List" >
    delete from CHECK_LACK_RR_MAWB
    where eawb_printcode in
    <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
      #{item}
    </foreach>
  </delete>

  <!-- and pp.pp_dest='N' -->
  <select id="selectPrByCondition"  parameterType="com.sinoair.billing.domain.vo.check.EawbCheckVO" resultType="com.sinoair.billing.domain.model.billing.PaymentRecord">
    select distinct e.eawb_printcode,
    e.mawb_code,
    pp.pp_id,
    s.so_code,
    pp.ct_code,
    nvl(p.occ_company_id,pp.company_id) as companyId,
    e.eawb_outbound_sac_id as outboundCompanyId,
    pp.pp_name as prName,
    round(getPaymentprice_other(e.eawb_printcode,pp.pp_id),2) as ppPrice,
    e.eawb_reference1,
    e.eawb_reference2,e.eawb_chargeableweight,e.eawb_chargeableweight as chargeableweight,
    r.rr_occurtime as prOccurtime,
    e.eawb_tracking_no,e.eawb_servicetype as epKey,
    pp.pp_dest,
    e.eawb_destcountry,
    e.eawb_so_code as eawbSoCode,
    pp.so_code as ppSoCode,
    'OUTER' as prCate,
    pp.pp_cate as prType,
    (select pd.pd_syscode from price_define pd where pd.pd_name=pp.pp_name) as pdSyscode
    from

    expressairwaybill       e,
    product                 p,
    service                 s,
    product_service         ps,
    price_payment           pp,
    receipt_record          r
    where  r.eawb_printcode=e.eawb_printcode

    and r.ep_key = p.p_servicetype_original

    and p.p_id = ps.p_id
    and s.s_id=ps.s_id
    and pp.s_id = s.s_id

    and pp.pp_special_key=getSpecialKey_PP_other(e.eawb_printcode)

    and pp.pp_expireddate >= trunc(r.rr_occurtime,'dd')
      <![CDATA[
      and pp.pp_effectivedate <= trunc(r.rr_occurtime,'dd')
      ]]>
    and s.s_awb_type='H'

    and pp.pp_auto='Y'
    and pp.pp_type!='N'
    and pp.pp_status='ON'
    <if test="eawbPrintcode != null" >
    and e.eawb_printcode = #{eawbPrintcode}
    </if>
    <if test="beginNo != null">
      and re_id >= #{beginNo}
      <![CDATA[
      and re_id < #{endNo}
      ]]>
    </if>

  </select>
</mapper>