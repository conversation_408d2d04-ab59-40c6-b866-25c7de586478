<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.CheckEawbResultMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.CheckEawbResult" >
    <id column="EAWB_PRINTCODE" property="eawbPrintcode" jdbcType="VARCHAR" />
    <result column="EAWB_SYSCODE_CEOS" property="eawbSyscodeCeos" jdbcType="DECIMAL" />
    <result column="EAWB_KEYENTRYTIME" property="eawbKeyentrytime" jdbcType="TIMESTAMP" />
    <result column="CODE_STATUS" property="codeStatus" jdbcType="VARCHAR" />
    <result column="ACTIVITY_STATUS" property="activityStatus" jdbcType="VARCHAR" />
    <result column="RR_STATUS" property="rrStatus" jdbcType="VARCHAR" />
    <result column="RESULT_STATUS" property="resultStatus" jdbcType="VARCHAR" />
    <result column="ACTIVITY_STATUS_CEOS" property="activityStatusCeos" jdbcType="VARCHAR" />
    <result column="CHECK_HANDLETIME" property="checkHandletime" jdbcType="TIMESTAMP" />
      <result column="EAWB_SO_TYPE" property="eawbSoType" jdbcType="DECIMAL" />
    <result column="CHECK_DATE" property="checkDate" jdbcType="DECIMAL" />
  </resultMap>
  <sql id="Base_Column_List" >
    EAWB_PRINTCODE, EAWB_SYSCODE_CEOS, EAWB_KEYENTRYTIME, CODE_STATUS, ACTIVITY_STATUS, 
    RR_STATUS, RESULT_STATUS,ACTIVITY_STATUS_CEOS,CHECK_HANDLETIME,EAWB_SO_TYPE,CHECK_DATE
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from CHECK_EAWB_RESULT
    where EAWB_PRINTCODE = #{eawbPrintcode,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from CHECK_EAWB_RESULT
    where EAWB_PRINTCODE = #{eawbPrintcode,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.CheckEawbResult" >
    insert into CHECK_EAWB_RESULT (EAWB_PRINTCODE, EAWB_SYSCODE_CEOS, EAWB_KEYENTRYTIME, 
      CODE_STATUS, ACTIVITY_STATUS, RR_STATUS, 
      RESULT_STATUS,ACTIVITY_STATUS_CEOS,CHECK_HANDLETIME)
    values (#{eawbPrintcode,jdbcType=VARCHAR}, #{eawbSyscodeCeos,jdbcType=DECIMAL}, #{eawbKeyentrytime,jdbcType=TIMESTAMP}, 
      #{codeStatus,jdbcType=VARCHAR}, #{activityStatus,jdbcType=VARCHAR}, #{rrStatus,jdbcType=VARCHAR}, 
      #{resultStatus,jdbcType=VARCHAR}, #{activityStatusCeos,jdbcType=VARCHAR}, #{checkHandletime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.CheckEawbResult" >
    insert into CHECK_EAWB_RESULT
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="eawbPrintcode != null" >
        EAWB_PRINTCODE,
      </if>
      <if test="eawbSyscodeCeos != null" >
        EAWB_SYSCODE_CEOS,
      </if>
      <if test="eawbKeyentrytime != null" >
        EAWB_KEYENTRYTIME,
      </if>
      <if test="codeStatus != null" >
        CODE_STATUS,
      </if>
      <if test="activityStatus != null" >
        ACTIVITY_STATUS,
      </if>
      <if test="rrStatus != null" >
        RR_STATUS,
      </if>
      <if test="resultStatus != null" >
        RESULT_STATUS,
      </if>
      <if test="activityStatusCeos != null" >
          ACTIVITY_STATUS_CEOS,
      </if>
      <if test="checkHandletime != null" >
          checkHandletime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="eawbPrintcode != null" >
        #{eawbPrintcode,jdbcType=VARCHAR},
      </if>
      <if test="eawbSyscodeCeos != null" >
        #{eawbSyscodeCeos,jdbcType=DECIMAL},
      </if>
      <if test="eawbKeyentrytime != null" >
        #{eawbKeyentrytime,jdbcType=TIMESTAMP},
      </if>
      <if test="codeStatus != null" >
        #{codeStatus,jdbcType=VARCHAR},
      </if>
      <if test="activityStatus != null" >
        #{activityStatus,jdbcType=VARCHAR},
      </if>
      <if test="rrStatus != null" >
        #{rrStatus,jdbcType=VARCHAR},
      </if>
      <if test="resultStatus != null" >
        #{resultStatus,jdbcType=VARCHAR},
      </if>
        <if test="activityStatusCeos != null" >
            #{activityStatusCeos,jdbcType=VARCHAR},
        </if>
        <if test="checkHandletime != null" >
            #{checkHandletime,jdbcType=TIMESTAMP},
        </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.CheckEawbResult" >
    update CHECK_EAWB_RESULT
    <set >
      <if test="eawbSyscodeCeos != null" >
        EAWB_SYSCODE_CEOS = #{eawbSyscodeCeos,jdbcType=DECIMAL},
      </if>
      <if test="eawbKeyentrytime != null" >
        EAWB_KEYENTRYTIME = #{eawbKeyentrytime,jdbcType=TIMESTAMP},
      </if>
      <if test="codeStatus != null" >
        CODE_STATUS = #{codeStatus,jdbcType=VARCHAR},
      </if>
      <if test="activityStatus != null" >
        ACTIVITY_STATUS = #{activityStatus,jdbcType=VARCHAR},
      </if>
      <if test="rrStatus != null" >
        RR_STATUS = #{rrStatus,jdbcType=VARCHAR},
      </if>
      <if test="resultStatus != null" >
        RESULT_STATUS = #{resultStatus,jdbcType=VARCHAR},
      </if>
        <if test="activityStatusCeos != null" >
            ACTIVITY_STATUS_CEOS = #{activityStatusCeos,jdbcType=VARCHAR},
        </if>
        <if test="checkHandletime != null" >
            CHECK_HANDLETIME = #{checkHandletime,jdbcType=TIMESTAMP},
        </if>
    </set>
    where EAWB_PRINTCODE = #{eawbPrintcode,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.CheckEawbResult" >
    update CHECK_EAWB_RESULT
    set EAWB_SYSCODE_CEOS = #{eawbSyscodeCeos,jdbcType=DECIMAL},
      EAWB_KEYENTRYTIME = #{eawbKeyentrytime,jdbcType=TIMESTAMP},
      CODE_STATUS = #{codeStatus,jdbcType=VARCHAR},
      ACTIVITY_STATUS = #{activityStatus,jdbcType=VARCHAR},
      RR_STATUS = #{rrStatus,jdbcType=VARCHAR},
      RESULT_STATUS = #{resultStatus,jdbcType=VARCHAR},
      ACTIVITY_STATUS_CEOS = #{activityStatusCeos,jdbcType=VARCHAR},
      CHECK_HANDLETIME = #{checkHandletime,jdbcType=TIMESTAMP}
    where EAWB_PRINTCODE = #{eawbPrintcode,jdbcType=VARCHAR}
  </update>

  <select id="countResult" resultType="java.lang.Integer" parameterType="java.lang.String" >
    select
    count(0)
    from CHECK_EAWB_RESULT
    where EAWB_PRINTCODE = #{eawbPrintcode,jdbcType=VARCHAR}
  </select>
  <insert id="insertBatch"  parameterType="java.util.List">
    insert into CHECK_EAWB_RESULT (EAWB_PRINTCODE, EAWB_SYSCODE_CEOS, EAWB_KEYENTRYTIME,
    CODE_STATUS, ACTIVITY_STATUS, RR_STATUS,
    RESULT_STATUS,ACTIVITY_STATUS_CEOS,CHECK_HANDLETIME,EAWB_SO_TYPE,CHECK_DATE)
    <foreach collection="list" item="item" index="index" separator="UNION" >
      (select
      #{item.eawbPrintcode,jdbcType=VARCHAR}, #{item.eawbSyscodeCeos,jdbcType=DECIMAL}, #{item.eawbKeyentrytime,jdbcType=TIMESTAMP},
      #{item.codeStatus,jdbcType=VARCHAR}, #{item.activityStatus,jdbcType=VARCHAR}, #{item.rrStatus,jdbcType=VARCHAR},
      #{item.resultStatus,jdbcType=VARCHAR},#{item.activityStatusCeos,jdbcType=VARCHAR},sysdate,#{item.eawbSoType,jdbcType=DECIMAL},
      #{item.checkDate,jdbcType=DECIMAL}
      from dual)
    </foreach>
  </insert>

  <update id="updateBatch" parameterType="java.util.List">
    <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
      update CHECK_EAWB_RESULT
      <set>
        <if test="item.eawbSyscodeCeos != null" >
          EAWB_SYSCODE_CEOS = #{item.eawbSyscodeCeos,jdbcType=DECIMAL},
        </if>
        <if test="item.eawbKeyentrytime != null" >
          EAWB_KEYENTRYTIME = #{item.eawbKeyentrytime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.codeStatus != null" >
          CODE_STATUS = #{item.codeStatus,jdbcType=VARCHAR},
        </if>
        <if test="item.activityStatus != null" >
          ACTIVITY_STATUS = #{item.activityStatus,jdbcType=VARCHAR},
        </if>
        <if test="item.rrStatus != null" >
          RR_STATUS = #{item.rrStatus,jdbcType=VARCHAR},
        </if>
        <if test="item.resultStatus != null" >
          RESULT_STATUS = #{item.resultStatus,jdbcType=VARCHAR},
        </if>
          <if test="item.activityStatusCeos != null" >
              ACTIVITY_STATUS_CEOS = #{item.activityStatusCeos,jdbcType=VARCHAR},
          </if>
          <if test="item.eawbSoType != null" >
              EAWB_SO_TYPE = #{item.eawbSoType,jdbcType=DECIMAL},
          </if>
        <if test="item.checkDate != null" >
          CHECK_DATE = #{item.checkDate,jdbcType=DECIMAL},
        </if>
          CHECK_HANDLETIME = sysdate
      </set>
      where EAWB_PRINTCODE = #{item.eawbPrintcode,jdbcType=VARCHAR}
    </foreach>
  </update>

  <select id="selectMinEawbSysCode" resultType="java.lang.Long">
    select eawb_syscode_ceos
    from check_eawb_result
    where result_status = 'N'
    <![CDATA[
        and rownum < 2
        ]]>
    order by eawb_syscode_ceos
  </select>

  <select id="selectSummaryResult" resultType="com.sinoair.billing.domain.model.billing.CheckSummaryResult">
      select checkDate ,allNum,
      (select count(0) from check_eawb_result other where other.eawb_so_type = 99 and  other.check_date = a.checkDate) as otherNum,
      (select count(0) from check_eawb_result c where c.code_status = 'N' and  c.check_date = a.checkDate) as codeNum
       from
      (select check_Date as checkDate,count(0) as allNum
      from check_eawb_result
      group by check_date) a
  </select>

  <select id="selectResultNumByDate" resultType="com.sinoair.billing.domain.model.billing.CheckSummaryResult" parameterType="java.lang.Long">
      select
      (select count(0) from check_eawb_result cn where cn.eawb_so_type = 1 and  cn.check_date = #{checkDate,jdbcType=DECIMAL}) as cnNum,
      (select count(0) from check_eawb_result other where other.eawb_so_type = 99 and  other.check_date = #{checkDate,jdbcType=DECIMAL}) as otherNum,
      (select count(0) from check_eawb_result c where c.code_status = 'N' and  c.check_date = #{checkDate,jdbcType=DECIMAL}) as codeNum,
      (select count(cerd_syscode) from check_eawb_result_detail ce where ce.activity_status_ceos = 'N' and  ce.check_date = #{checkDate,jdbcType=DECIMAL}) as activityNumCeos,
      (select count(cerd_syscode) from check_eawb_result_detail ac where ac.activity_status = 'N' and  ac.check_date = #{checkDate,jdbcType=DECIMAL}) as activityNum,
      (select count(cerd_syscode) from check_eawb_result_detail rr where rr.rr_status = 'N' and  rr.check_date = #{checkDate,jdbcType=DECIMAL}) as rrNum
      from dual
  </select>

  <select id="selectCheckDate" resultType="com.sinoair.billing.domain.model.billing.CheckSummaryResult">
    select check_Date as checkDate,count(0) as allNum
    from check_eawb_result
    group by check_date
  </select>

    <select id="selectCheckResultList" resultMap="BaseResultMap" parameterType="com.sinoair.billing.domain.vo.check.EawbCheckVO" >
        select
        <include refid="Base_Column_List" />
        from CHECK_EAWB_RESULT
        where 1=1
        <if test="codeStatus != null" >
           and  CODE_STATUS = #{codeStatus,jdbcType=VARCHAR}
        </if>
        <if test="activityStatus != null" >
           and  ACTIVITY_STATUS = #{activityStatus,jdbcType=VARCHAR}
        </if>
        <if test="activityStatusCeos != null" >
           and ACTIVITY_STATUS_CEOS = #{activityStatusCeos,jdbcType=VARCHAR}
        </if>
        <if test="rrStatus != null" >
           and  RR_STATUS = #{rrStatus,jdbcType=VARCHAR}
        </if>
      <if test="resultStatus != null" >
        and  RESULT_STATUS = #{resultStatus,jdbcType=VARCHAR}
      </if>

      <if test="startDate != null" >
        <!--<![CDATA[-->
      <!--
        and eawb_keyentrytime >= to_date(#{startDate,jdbcType=VARCHAR},'yyyymmdd')
        and eawb_keyentrytime < to_date(#{endDate,jdbcType=VARCHAR},'yyyymmdd') -->
     <!--  ]]> -->
      </if>
      <![CDATA[

      ]]>

    </select>

    <select id="selectCNNum" resultType="java.lang.Integer" parameterType="java.lang.Long">
      select count(0) from check_eawb_result cn where cn.eawb_so_type = 1 and  cn.check_date = #{checkDate,jdbcType=DECIMAL}
    </select>
  <select id="selectOtherNum" resultType="java.lang.Integer" parameterType="java.lang.Long">
    select count(0) from check_eawb_result cn where cn.eawb_so_type = 99 and  cn.check_date = #{checkDate,jdbcType=DECIMAL}
  </select>
  <select id="selectCodeNum" resultType="java.lang.Integer" parameterType="java.lang.Long">
    select count(0) from check_eawb_result c where c.code_status = 'N' and  c.check_date = #{checkDate,jdbcType=DECIMAL}
  </select>
  <select id="selectActivityNum" resultType="java.lang.Integer" parameterType="java.lang.Long">
    select count(0) from check_eawb_result_detail ac where ac.activity_status = 'N' and  ac.check_date = #{checkDate,jdbcType=DECIMAL}
  </select>
  <select id="selectRRNum" resultType="java.lang.Integer" parameterType="java.lang.Long">
    select count(0) from check_eawb_result_detail rr where rr.rr_status = 'N' and  rr.check_date = #{checkDate,jdbcType=DECIMAL}
  </select>

</mapper>