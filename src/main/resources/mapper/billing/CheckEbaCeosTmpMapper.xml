<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.CheckEbaCeosTmpMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.CheckEbaCeosTmp" >
    <id column="CECT_SYSCODE" property="cectSyscode" jdbcType="DECIMAL" />
    <result column="EAWB_PRINTCODE" property="eawbPrintcode" jdbcType="VARCHAR" />
    <result column="EAD_CODE" property="eadCode" jdbcType="VARCHAR" />
    <result column="EAST_CODE" property="eastCode" jdbcType="VARCHAR" />
    <result column="EBA_HANDLETIME" property="ebaHandletime" jdbcType="TIMESTAMP" />
    <result column="CECT_HANDLETIME" property="cectHandletime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    CECT_SYSCODE, EAWB_PRINTCODE, EAD_CODE, EAST_CODE, EBA_HANDLETIME, CECT_HANDLETIME
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from CHECK_EBA_CEOS_TMP
    where CECT_SYSCODE = #{cectSyscode,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from CHECK_EBA_CEOS_TMP
    where CECT_SYSCODE = #{cectSyscode,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.CheckEbaCeosTmp" >
    insert into CHECK_EBA_CEOS_TMP (CECT_SYSCODE, EAWB_PRINTCODE, EAD_CODE, 
      EAST_CODE, EBA_HANDLETIME, CECT_HANDLETIME
      )
    values (#{cectSyscode,jdbcType=DECIMAL}, #{eawbPrintcode,jdbcType=VARCHAR}, #{eadCode,jdbcType=VARCHAR}, 
      #{eastCode,jdbcType=VARCHAR}, #{ebaHandletime,jdbcType=TIMESTAMP}, #{cectHandletime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.CheckEbaCeosTmp" >
    insert into CHECK_EBA_CEOS_TMP
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="cectSyscode != null" >
        CECT_SYSCODE,
      </if>
      <if test="eawbPrintcode != null" >
        EAWB_PRINTCODE,
      </if>
      <if test="eadCode != null" >
        EAD_CODE,
      </if>
      <if test="eastCode != null" >
        EAST_CODE,
      </if>
      <if test="ebaHandletime != null" >
        EBA_HANDLETIME,
      </if>
      <if test="cectHandletime != null" >
        CECT_HANDLETIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="cectSyscode != null" >
        #{cectSyscode,jdbcType=DECIMAL},
      </if>
      <if test="eawbPrintcode != null" >
        #{eawbPrintcode,jdbcType=VARCHAR},
      </if>
      <if test="eadCode != null" >
        #{eadCode,jdbcType=VARCHAR},
      </if>
      <if test="eastCode != null" >
        #{eastCode,jdbcType=VARCHAR},
      </if>
      <if test="ebaHandletime != null" >
        #{ebaHandletime,jdbcType=TIMESTAMP},
      </if>
      <if test="cectHandletime != null" >
        #{cectHandletime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.CheckEbaCeosTmp" >
    update CHECK_EBA_CEOS_TMP
    <set >
      <if test="eawbPrintcode != null" >
        EAWB_PRINTCODE = #{eawbPrintcode,jdbcType=VARCHAR},
      </if>
      <if test="eadCode != null" >
        EAD_CODE = #{eadCode,jdbcType=VARCHAR},
      </if>
      <if test="eastCode != null" >
        EAST_CODE = #{eastCode,jdbcType=VARCHAR},
      </if>
      <if test="ebaHandletime != null" >
        EBA_HANDLETIME = #{ebaHandletime,jdbcType=TIMESTAMP},
      </if>
      <if test="cectHandletime != null" >
        CECT_HANDLETIME = #{cectHandletime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where CECT_SYSCODE = #{cectSyscode,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.CheckEbaCeosTmp" >
    update CHECK_EBA_CEOS_TMP
    set EAWB_PRINTCODE = #{eawbPrintcode,jdbcType=VARCHAR},
      EAD_CODE = #{eadCode,jdbcType=VARCHAR},
      EAST_CODE = #{eastCode,jdbcType=VARCHAR},
      EBA_HANDLETIME = #{ebaHandletime,jdbcType=TIMESTAMP},
      CECT_HANDLETIME = #{cectHandletime,jdbcType=TIMESTAMP}
    where CECT_SYSCODE = #{cectSyscode,jdbcType=DECIMAL}
  </update>

  <insert id="insertBatch"  parameterType="java.util.List">
    insert into CHECK_EBA_CEOS_TMP (CECT_SYSCODE, EAWB_PRINTCODE, EAD_CODE,
    EAST_CODE, EBA_HANDLETIME, CECT_HANDLETIME)
    select SEQ_CHECK_CEOS_TMP.NEXTVAL,pbd.* from (
    <foreach collection="list" item="item" index="index" separator="UNION ALL">
      select #{item.eawbPrintcode,jdbcType=VARCHAR}, #{item.eadCode,jdbcType=VARCHAR},
      #{item.eastCode,jdbcType=VARCHAR}, #{item.ebaHandletime,jdbcType=TIMESTAMP}, sysdate from dual
    </foreach>
    ) pbd
  </insert>

  <delete id="deleteBatch" parameterType="java.util.List" >
    delete from CHECK_EBA_CEOS_TMP
    where CECT_SYSCODE in
    <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
      #{item.cectSyscode,jdbcType=DECIMAL}
    </foreach>
  </delete>

  <select id="list" resultMap="BaseResultMap" >
    select
     CECT_SYSCODE, EAWB_PRINTCODE, EAD_CODE, EAST_CODE, EBA_HANDLETIME
    from CHECK_EBA_CEOS_TMP
 <![CDATA[
        where   CECT_SYSCODE > 1897000000
    ]]>
  </select>

  <select id="count" resultType="java.lang.Integer" >
    select
    count(0)
    from CHECK_EBA_CEOS_TMP
  </select>

  <insert id="insertBatchError"  parameterType="java.util.List">
    insert into CHECK_EBA_CEOS_ERROR (CECT_SYSCODE, EAWB_PRINTCODE, EAD_CODE,
    EAST_CODE, EBA_HANDLETIME, CECT_HANDLETIME)
    select SEQ_CHECK_CEOS_TMP.NEXTVAL,pbd.* from (
    <foreach collection="list" item="item" index="index" separator="UNION ALL">
      select #{item.eawbPrintcode,jdbcType=VARCHAR}, #{item.eadCode,jdbcType=VARCHAR},
      #{item.eastCode,jdbcType=VARCHAR}, #{item.ebaHandletime,jdbcType=TIMESTAMP}, sysdate from dual
    </foreach>
    ) pbd
  </insert>

  <delete id="deleteBatchError" parameterType="java.util.List" >
    delete from CHECK_EBA_CEOS_ERROR
    where CECT_SYSCODE in
    <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
      #{item.cectSyscode,jdbcType=DECIMAL}
    </foreach>
  </delete>

  <select id="listError" resultMap="BaseResultMap" >
    select
     CECT_SYSCODE, EAWB_PRINTCODE, EAD_CODE, EAST_CODE, EBA_HANDLETIME
    from CHECK_EBA_CEOS_ERROR
  </select>
</mapper>