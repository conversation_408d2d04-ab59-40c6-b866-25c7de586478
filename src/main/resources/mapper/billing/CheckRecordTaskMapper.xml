<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.CheckRecordTaskMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.CheckRecordTask" >
    <id column="CRT_ID" property="crtId" jdbcType="DECIMAL" />
    <result column="TASK_BATCH" property="taskBatch" jdbcType="VARCHAR" />
    <result column="TASK_MODULE" property="taskModule" jdbcType="VARCHAR" />
    <result column="TASK_TYPE" property="taskType" jdbcType="VARCHAR" />
    <result column="TASK_NAME" property="taskName" jdbcType="VARCHAR" />
    <result column="TASK_STATUS" property="taskStatus" jdbcType="VARCHAR" />
    <result column="QUERY_COUNT" property="queryCount" jdbcType="DECIMAL" />
    <result column="HANDLE_COUNT" property="handleCount" jdbcType="DECIMAL" />
    <result column="TASK_CREATETIME" property="taskCreatetime" jdbcType="TIMESTAMP" />
    <result column="TASK_HANDLETIME" property="taskHandletime" jdbcType="TIMESTAMP" />
    <result column="TASK_PARAM" property="taskParam" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    CRT_ID, TASK_BATCH, TASK_MODULE, TASK_TYPE, TASK_NAME, TASK_STATUS, QUERY_COUNT, 
    HANDLE_COUNT, TASK_CREATETIME, TASK_HANDLETIME,TASK_PARAM
  </sql>
  <sql id='TABLE_SEQUENCE'>SEQ_LOG.NEXTVAL</sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.math.BigDecimal" >
    select 
    <include refid="Base_Column_List" />
    from CHECK_RECORD_TASK
    where CRT_ID = #{crtId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.math.BigDecimal" >
    delete from CHECK_RECORD_TASK
    where CRT_ID = #{crtId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.CheckRecordTask" >
    insert into CHECK_RECORD_TASK (CRT_ID, TASK_BATCH, TASK_MODULE, 
      TASK_TYPE, TASK_NAME, TASK_STATUS, 
      QUERY_COUNT, HANDLE_COUNT, TASK_CREATETIME,
      TASK_HANDLETIME,TASK_PARAM)
    values (#{crtId,jdbcType=DECIMAL}, #{taskBatch,jdbcType=VARCHAR}, #{taskModule,jdbcType=VARCHAR}, 
      #{taskType,jdbcType=VARCHAR}, #{taskName,jdbcType=VARCHAR}, #{taskStatus,jdbcType=VARCHAR}, 
      #{queryCount,jdbcType=DECIMAL}, #{handleCount,jdbcType=DECIMAL}, #{taskCreatetime,jdbcType=TIMESTAMP}, 
      #{taskHandletime,jdbcType=TIMESTAMP}, #{taskParam,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.CheckRecordTask" >
    insert into CHECK_RECORD_TASK
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="crtId != null" >
        CRT_ID,
      </if>
      <if test="taskBatch != null" >
        TASK_BATCH,
      </if>
      <if test="taskModule != null" >
        TASK_MODULE,
      </if>
      <if test="taskType != null" >
        TASK_TYPE,
      </if>
      <if test="taskName != null" >
        TASK_NAME,
      </if>
      <if test="taskStatus != null" >
        TASK_STATUS,
      </if>
      <if test="queryCount != null" >
        QUERY_COUNT,
      </if>
      <if test="handleCount != null" >
        HANDLE_COUNT,
      </if>
      <if test="taskCreatetime != null" >
        TASK_CREATETIME,
      </if>
      <if test="taskHandletime != null" >
        TASK_HANDLETIME,
      </if>
      <if test="taskParam != null" >
        TASK_PARAM,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="crtId != null" >
        #{crtId,jdbcType=DECIMAL},
      </if>
      <if test="taskBatch != null" >
        #{taskBatch,jdbcType=VARCHAR},
      </if>
      <if test="taskModule != null" >
        #{taskModule,jdbcType=VARCHAR},
      </if>
      <if test="taskType != null" >
        #{taskType,jdbcType=VARCHAR},
      </if>
      <if test="taskName != null" >
        #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="taskStatus != null" >
        #{taskStatus,jdbcType=VARCHAR},
      </if>
      <if test="queryCount != null" >
        #{queryCount,jdbcType=DECIMAL},
      </if>
      <if test="handleCount != null" >
        #{handleCount,jdbcType=DECIMAL},
      </if>
      <if test="taskCreatetime != null" >
        #{taskCreatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="taskHandletime != null" >
        #{taskHandletime,jdbcType=TIMESTAMP},
      </if>
      <if test="taskParam != null" >
        #{taskParam,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.CheckRecordTask" >
    update CHECK_RECORD_TASK
    <set >
      <if test="taskBatch != null" >
        TASK_BATCH = #{taskBatch,jdbcType=VARCHAR},
      </if>
      <if test="taskModule != null" >
        TASK_MODULE = #{taskModule,jdbcType=VARCHAR},
      </if>
      <if test="taskType != null" >
        TASK_TYPE = #{taskType,jdbcType=VARCHAR},
      </if>
      <if test="taskName != null" >
        TASK_NAME = #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="taskStatus != null" >
        TASK_STATUS = #{taskStatus,jdbcType=VARCHAR},
      </if>
      <if test="queryCount != null" >
        QUERY_COUNT = #{queryCount,jdbcType=DECIMAL},
      </if>
      <if test="handleCount != null" >
        HANDLE_COUNT = #{handleCount,jdbcType=DECIMAL},
      </if>
      <if test="taskCreatetime != null" >
        TASK_CREATETIME = #{taskCreatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="taskHandletime != null" >
        TASK_HANDLETIME = #{taskHandletime,jdbcType=TIMESTAMP},
      </if>
      <if test="taskParam != null" >
        TASK_PARAM = #{taskParam,jdbcType=VARCHAR},
      </if>
    </set>
    where CRT_ID = #{crtId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.CheckRecordTask" >
    update CHECK_RECORD_TASK
    set TASK_BATCH = #{taskBatch,jdbcType=VARCHAR},
      TASK_MODULE = #{taskModule,jdbcType=VARCHAR},
      TASK_TYPE = #{taskType,jdbcType=VARCHAR},
      TASK_NAME = #{taskName,jdbcType=VARCHAR},
      TASK_STATUS = #{taskStatus,jdbcType=VARCHAR},
      QUERY_COUNT = #{queryCount,jdbcType=DECIMAL},
      HANDLE_COUNT = #{handleCount,jdbcType=DECIMAL},
      TASK_CREATETIME = #{taskCreatetime,jdbcType=TIMESTAMP},
      TASK_HANDLETIME = #{taskHandletime,jdbcType=TIMESTAMP},
      TASK_PARAM = #{taskParam,jdbcType=VARCHAR}
    where CRT_ID = #{crtId,jdbcType=DECIMAL}
  </update>

  <select id="selectSeq" resultType="java.lang.Integer">
    select
    <include refid="TABLE_SEQUENCE"/>
    from dual
  </select>

  <select id="selectByCondition" resultMap="BaseResultMap" parameterType="com.sinoair.billing.domain.vo.query.InsRecordQuery" >
    select
    <include refid="Base_Column_List" />
    from CHECK_RECORD_TASK
    where TASK_STATUS = #{taskStatus,jdbcType=VARCHAR}
    and TASK_TYPE = #{handleType,jdbcType=VARCHAR}
  </select>
</mapper>