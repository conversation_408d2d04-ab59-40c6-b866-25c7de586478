<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.CheckSummaryResultMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.CheckSummaryResult" >
    <id column="CHECK_DATE" property="checkDate" jdbcType="DECIMAL" />
    <result column="ALL_NUM" property="allNum" jdbcType="DECIMAL" />
    <result column="CODE_NUM" property="codeNum" jdbcType="DECIMAL" />
    <result column="ACTIVITY_NUM" property="activityNum" jdbcType="DECIMAL" />
    <result column="RR_NUM" property="rrNum" jdbcType="DECIMAL" />
    <result column="RESULT_NUM" property="resultNum" jdbcType="DECIMAL" />
    <result column="CHECK_MONTH" property="checkMonth" jdbcType="DECIMAL" />
    <result column="CHECK_YEAR" property="checkYear" jdbcType="DECIMAL" />
    <result column="KEY_DATE" property="keyDate" jdbcType="TIMESTAMP" />
    <result column="ACTIVITY_NUM_CEOS" property="activityNumCeos" jdbcType="DECIMAL" />
    <result column="CN_NUM" property="cnNum" jdbcType="DECIMAL" />
    <result column="OTHER_NUM" property="otherNum" jdbcType="DECIMAL" />
  </resultMap>
  <sql id="Base_Column_List" >
    CHECK_DATE, ALL_NUM, CODE_NUM, ACTIVITY_NUM, RR_NUM, RESULT_NUM, CHECK_MONTH, CHECK_YEAR,
    KEY_DATE
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from CHECK_SUMMARY_RESULT
    where CHECK_DATE = #{checkDate,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from CHECK_SUMMARY_RESULT
    where CHECK_DATE = #{checkDate,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.CheckSummaryResult" >
    insert into CHECK_SUMMARY_RESULT (CHECK_DATE, ALL_NUM, CODE_NUM,
    ACTIVITY_NUM, RR_NUM, RESULT_NUM,
    CHECK_MONTH, CHECK_YEAR, KEY_DATE
    )
    values (#{checkDate,jdbcType=DECIMAL}, #{allNum,jdbcType=DECIMAL}, #{codeNum,jdbcType=DECIMAL},
    #{activityNum,jdbcType=DECIMAL}, #{rrNum,jdbcType=DECIMAL}, #{resultNum,jdbcType=DECIMAL},
    #{checkMonth,jdbcType=DECIMAL}, #{checkYear,jdbcType=DECIMAL}, #{keyDate,jdbcType=TIMESTAMP}
    )
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.CheckSummaryResult" >
    insert into CHECK_SUMMARY_RESULT
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="checkDate != null" >
        CHECK_DATE,
      </if>
      <if test="allNum != null" >
        ALL_NUM,
      </if>
      <if test="codeNum != null" >
        CODE_NUM,
      </if>
      <if test="activityNum != null" >
        ACTIVITY_NUM,
      </if>
      <if test="rrNum != null" >
        RR_NUM,
      </if>
      <if test="resultNum != null" >
        RESULT_NUM,
      </if>
      <if test="checkMonth != null" >
        CHECK_MONTH,
      </if>
      <if test="checkYear != null" >
        CHECK_YEAR,
      </if>
      <if test="keyDate != null" >
        KEY_DATE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="checkDate != null" >
        #{checkDate,jdbcType=DECIMAL},
      </if>
      <if test="allNum != null" >
        #{allNum,jdbcType=DECIMAL},
      </if>
      <if test="codeNum != null" >
        #{codeNum,jdbcType=DECIMAL},
      </if>
      <if test="activityNum != null" >
        #{activityNum,jdbcType=DECIMAL},
      </if>
      <if test="rrNum != null" >
        #{rrNum,jdbcType=DECIMAL},
      </if>
      <if test="resultNum != null" >
        #{resultNum,jdbcType=DECIMAL},
      </if>
      <if test="checkMonth != null" >
        #{checkMonth,jdbcType=DECIMAL},
      </if>
      <if test="checkYear != null" >
        #{checkYear,jdbcType=DECIMAL},
      </if>
      <if test="keyDate != null" >
        #{keyDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.CheckSummaryResult" >
    update CHECK_SUMMARY_RESULT
    <set >
      <if test="allNum != null" >
        ALL_NUM = #{allNum,jdbcType=DECIMAL},
      </if>
      <if test="codeNum != null" >
        CODE_NUM = #{codeNum,jdbcType=DECIMAL},
      </if>
      <if test="activityNum != null" >
        ACTIVITY_NUM = #{activityNum,jdbcType=DECIMAL},
      </if>
      <if test="rrNum != null" >
        RR_NUM = #{rrNum,jdbcType=DECIMAL},
      </if>
      <if test="resultNum != null" >
        RESULT_NUM = #{resultNum,jdbcType=DECIMAL},
      </if>
      <if test="checkMonth != null" >
        CHECK_MONTH = #{checkMonth,jdbcType=DECIMAL},
      </if>
      <if test="checkYear != null" >
        CHECK_YEAR = #{checkYear,jdbcType=DECIMAL},
      </if>
      <if test="keyDate != null" >
        KEY_DATE = #{keyDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where CHECK_DATE = #{checkDate,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.CheckSummaryResult" >
    update CHECK_SUMMARY_RESULT
    set ALL_NUM = #{allNum,jdbcType=DECIMAL},
      CODE_NUM = #{codeNum,jdbcType=DECIMAL},
      ACTIVITY_NUM = #{activityNum,jdbcType=DECIMAL},
      RR_NUM = #{rrNum,jdbcType=DECIMAL},
      RESULT_NUM = #{resultNum,jdbcType=DECIMAL},
      CHECK_MONTH = #{checkMonth,jdbcType=DECIMAL},
      CHECK_YEAR = #{checkYear,jdbcType=DECIMAL},
      KEY_DATE = #{keyDate,jdbcType=TIMESTAMP}
    where CHECK_DATE = #{checkDate,jdbcType=DECIMAL}
  </update>

  <insert id="insertBatch"  parameterType="java.util.List">
    insert into CHECK_SUMMARY_RESULT (CHECK_DATE, ALL_NUM, CODE_NUM,
    ACTIVITY_NUM, RR_NUM, RESULT_NUM,
    CHECK_MONTH, CHECK_YEAR,KEY_DATE,ACTIVITY_NUM_CEOS,CN_NUM,OTHER_NUM)
    <foreach collection="list" item="item" index="index" separator="UNION" >
      (select
      #{item.checkDate,jdbcType=DECIMAL}, #{item.allNum,jdbcType=DECIMAL}, #{item.codeNum,jdbcType=DECIMAL},
      #{item.activityNum,jdbcType=DECIMAL}, #{item.rrNum,jdbcType=DECIMAL}, #{item.resultNum,jdbcType=DECIMAL},
      #{item.checkMonth,jdbcType=DECIMAL}, #{item.checkYear,jdbcType=DECIMAL}, #{item.keyDate,jdbcType=TIMESTAMP},
      #{item.activityNumCeos,jdbcType=DECIMAL}, #{item.cnNum,jdbcType=DECIMAL}, #{item.otherNum,jdbcType=DECIMAL}
      from dual)
    </foreach>
  </insert>

  <delete id="deleteBatch">
    delete from CHECK_SUMMARY_RESULT
  </delete>

</mapper>