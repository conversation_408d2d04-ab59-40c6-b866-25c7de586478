<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.CheckSynEbaConfigMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.CheckSynEbaConfig" >
    <result column="CSEC_ID" property="csecId" jdbcType="DECIMAL" />
    <result column="EBA_SYS" property="ebaSys" jdbcType="VARCHAR" />
    <result column="SO_TYPE" property="soType" jdbcType="DECIMAL" />
    <result column="SO_CODE" property="soCode" jdbcType="VARCHAR" />
    <result column="EBA_CODE" property="ebaCode" jdbcType="VARCHAR" />
    <result column="EBA_TYPE" property="ebaType" jdbcType="VARCHAR" />
    <result column="EBA_STATUS" property="ebaStatus" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    CSEC_ID, EBA_SYS, SO_TYPE,
      SO_CODE, EBA_CODE, EBA_TYPE,
      EBA_STATUS
  </sql>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.CheckSynEbaConfig" >
    insert into CHECK_SYN_EBA_CONFIG (CSEC_ID, EBA_SYS, SO_TYPE, 
      SO_CODE, EBA_CODE, EBA_TYPE, 
      EBA_STATUS)
    values (#{csecId,jdbcType=DECIMAL}, #{ebaSys,jdbcType=VARCHAR}, #{soType,jdbcType=DECIMAL}, 
      #{soCode,jdbcType=VARCHAR}, #{ebaCode,jdbcType=VARCHAR}, #{ebaType,jdbcType=VARCHAR}, 
      #{ebaStatus,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.CheckSynEbaConfig" >
    insert into CHECK_SYN_EBA_CONFIG
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="csecId != null" >
        CSEC_ID,
      </if>
      <if test="ebaSys != null" >
        EBA_SYS,
      </if>
      <if test="soType != null" >
        SO_TYPE,
      </if>
      <if test="soCode != null" >
        SO_CODE,
      </if>
      <if test="ebaCode != null" >
        EBA_CODE,
      </if>
      <if test="ebaType != null" >
        EBA_TYPE,
      </if>
      <if test="ebaStatus != null" >
        EBA_STATUS,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="csecId != null" >
        #{csecId,jdbcType=DECIMAL},
      </if>
      <if test="ebaSys != null" >
        #{ebaSys,jdbcType=VARCHAR},
      </if>
      <if test="soType != null" >
        #{soType,jdbcType=DECIMAL},
      </if>
      <if test="soCode != null" >
        #{soCode,jdbcType=VARCHAR},
      </if>
      <if test="ebaCode != null" >
        #{ebaCode,jdbcType=VARCHAR},
      </if>
      <if test="ebaType != null" >
        #{ebaType,jdbcType=VARCHAR},
      </if>
      <if test="ebaStatus != null" >
        #{ebaStatus,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <select id="listSynEbaConfig" resultMap="BaseResultMap" parameterType="com.sinoair.billing.domain.model.billing.CheckSynEbaConfig">
    select
    <include refid="Base_Column_List" />
    from CHECK_SYN_EBA_CONFIG
    where EBA_STATUS = 'ON'
      and (SO_TYPE = #{soType} or SO_TYPE=99)
    <if test="ebaType != null" >
      and EBA_TYPE = #{ebaType}
    </if>
    <if test="ebaSys != null" >
      and EBA_SYS = #{ebaSys}
    </if>

  </select>

  <select id="listFlatEbaConfig" resultMap="BaseResultMap" parameterType="com.sinoair.billing.domain.model.billing.CheckSynEbaConfig">
    select
    <include refid="Base_Column_List" />
    from CHECK_SYN_EBA_CONFIG
    where EBA_STATUS = 'ON'
    and (SO_TYPE = #{soType})
    <if test="ebaType != null" >
      and EBA_TYPE = #{ebaType}
    </if>
    <if test="ebaSys != null" >
      and EBA_SYS = #{ebaSys}
    </if>

  </select>

</mapper>