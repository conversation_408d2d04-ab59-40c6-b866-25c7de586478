<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.CompanyMapper">
    <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.Company">
        <id column="COMPANY_ID" property="companyId" jdbcType="VARCHAR"/>
        <result column="COMPANY_NAME" property="companyName" jdbcType="VARCHAR"/>
        <result column="COMPANY_CODE" property="companyCode" jdbcType="VARCHAR"/>
        <result column="COMPANY_TYPE" property="companyType" jdbcType="VARCHAR"/>
        <result column="TIME_INTERVAL" property="timeInterval" jdbcType="DECIMAL"/>
        <result column="ORG_ID" property="orgId" jdbcType="VARCHAR"/>
        <result column="COMPANY_STATUS" property="companyStatus" jdbcType="VARCHAR"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="CREATE_USER_ID" property="createUserId" jdbcType="DECIMAL"/>
        <result column="COMPANY_ADDRESS" property="companyAddress" jdbcType="VARCHAR"/>
        <result column="COMPANY_PHONE" property="companyPhone" jdbcType="VARCHAR"/>
        <result column="COMPANY_P_ID" property="companyPId" jdbcType="VARCHAR"/>
    </resultMap>
    <resultMap id="TreeResultMap" type="com.sinoair.billing.domain.model.billing.Company">
        <id column="COMPANY_ID" property="companyId" jdbcType="VARCHAR"/>
        <result column="COMPANY_NAME" property="companyName" jdbcType="VARCHAR"/>
        <result column="COMPANY_CODE" property="companyCode" jdbcType="VARCHAR"/>
        <result column="COMPANY_TYPE" property="companyType" jdbcType="VARCHAR"/>
        <result column="TIME_INTERVAL" property="timeInterval" jdbcType="DECIMAL"/>
        <result column="ORG_ID" property="orgId" jdbcType="VARCHAR"/>
        <result column="COMPANY_STATUS" property="companyStatus" jdbcType="VARCHAR"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="CREATE_USER_ID" property="createUserId" jdbcType="DECIMAL"/>
        <result column="COMPANY_ADDRESS" property="companyAddress" jdbcType="VARCHAR"/>
        <result column="COMPANY_PHONE" property="companyPhone" jdbcType="VARCHAR"/>
        <result column="COMPANY_P_ID" property="companyPId" jdbcType="VARCHAR"/>
        <collection property="childrenCompanyList" column="COMPANY_ID"
                    select="getChildrenCompanyList"/>
    </resultMap>
    <select id="selectTreeByPrimaryKey" resultMap="TreeResultMap" parameterType="java.lang.Integer">
        select
       *
        from COMPANY
        where COMPANY_ID = #{companyId,jdbcType=DECIMAL}
    </select>
    <select id="getChildrenCompanyList" parameterType="java.lang.Integer" resultMap="TreeResultMap">
           select * from COMPANY where COMPANY_P_ID = #{COMPANY_ID,jdbcType=DECIMAL}
    </select>

    <!-- 序列 -->
    <sql id='TABLE_SEQUENCE'>SEQ_COMPANY.NEXTVAL</sql>
    <sql id="Base_Column_List">
    COMPANY_ID, COMPANY_NAME, COMPANY_CODE, COMPANY_TYPE, TIME_INTERVAL, ORG_ID, COMPANY_STATUS,
    CREATE_TIME, CREATE_USER_ID, COMPANY_ADDRESS, COMPANY_PHONE, COMPANY_P_ID
  </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from COMPANY
        where COMPANY_ID = #{companyId}
    </select>
    <!--模糊查询供应商-->
    <select id="selectVonder" resultMap="BaseResultMap" parameterType="String">
        select * from COMPANY
        where 1=1
        <if test="companyName != null and companyName != ''">
            and company_name like '%'||#{companyName}||'%'
        </if>
    </select>
    <!--查询登录人的下级供应商-->
    <select id="selectNextVonder" resultMap="BaseResultMap" parameterType="java.lang.String">
    select * from COMPANY
    where COMPANY_P_ID = #{company_p_id}
  </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from COMPANY
    where COMPANY_ID = #{companyId}
  </delete>
    <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.Company">
       <!-- <selectKey keyProperty="companyId" resultType="int" order="BEFORE">
            select
            <include refid="TABLE_SEQUENCE"/>
            from dual
        </selectKey>-->
        insert into COMPANY (COMPANY_ID, COMPANY_NAME, COMPANY_CODE,
        COMPANY_TYPE, TIME_INTERVAL, ORG_ID,
        COMPANY_STATUS, CREATE_TIME, CREATE_USER_ID,
        COMPANY_ADDRESS, COMPANY_PHONE, COMPANY_P_ID
        )
        values (#{companyId,jdbcType=VARCHAR}, #{companyName,jdbcType=VARCHAR}, #{companyCode,jdbcType=VARCHAR},
        #{companyType,jdbcType=VARCHAR}, #{timeInterval,jdbcType=DECIMAL}, #{orgId,jdbcType=VARCHAR},
        #{companyStatus,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{createUserId,jdbcType=DECIMAL},
        #{companyAddress,jdbcType=VARCHAR}, #{companyPhone,jdbcType=VARCHAR}, #{companyPId,jdbcType=VARCHAR}
        )
    </insert>
    <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.Company">
        <!--<selectKey keyProperty="companyId" resultType="int" order="BEFORE">
            select
            <include refid="TABLE_SEQUENCE"/>
            from dual
        </selectKey>-->
        insert into COMPANY
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                COMPANY_ID,
            </if>
            <if test="companyName != null">
                COMPANY_NAME,
            </if>
            <if test="companyCode != null">
                COMPANY_CODE,
            </if>
            <if test="companyType != null">
                COMPANY_TYPE,
            </if>
            <if test="timeInterval != null">
                TIME_INTERVAL,
            </if>
            <if test="orgId != null">
                ORG_ID,
            </if>
            <if test="companyStatus != null">
                COMPANY_STATUS,
            </if>
            <if test="createTime != null">
                CREATE_TIME,
            </if>
            <if test="createUserId != null">
                CREATE_USER_ID,
            </if>
            <if test="companyAddress != null">
                COMPANY_ADDRESS,
            </if>
            <if test="companyPhone != null">
                COMPANY_PHONE,
            </if>
            <if test="companyPId != null">
                COMPANY_P_ID,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                #{companyId,jdbcType=VARCHAR},
            </if>
            <if test="companyName != null">
                #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="companyCode != null">
                #{companyCode,jdbcType=VARCHAR},
            </if>
            <if test="companyType != null">
                #{companyType,jdbcType=VARCHAR},
            </if>
            <if test="timeInterval != null">
                #{timeInterval,jdbcType=DECIMAL},
            </if>
            <if test="orgId != null">
                #{orgId,jdbcType=VARCHAR},
            </if>
            <if test="companyStatus != null">
                #{companyStatus,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUserId != null">
                #{createUserId,jdbcType=DECIMAL},
            </if>
            <if test="companyAddress != null">
                #{companyAddress,jdbcType=VARCHAR},
            </if>
            <if test="companyPhone != null">
                #{companyPhone,jdbcType=VARCHAR},
            </if>
            <if test="companyPId != null">
                #{companyPId,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.Company">
        update COMPANY
        <set>
            <if test="companyName != null">
                COMPANY_NAME = #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="companyCode != null">
                COMPANY_CODE = #{companyCode,jdbcType=VARCHAR},
            </if>
            <if test="companyType != null">
                COMPANY_TYPE = #{companyType,jdbcType=VARCHAR},
            </if>
            <if test="timeInterval != null">
                TIME_INTERVAL = #{timeInterval,jdbcType=DECIMAL},
            </if>
            <if test="orgId != null">
                ORG_ID = #{orgId,jdbcType=VARCHAR},
            </if>
            <if test="companyStatus != null">
                COMPANY_STATUS = #{companyStatus,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUserId != null">
                CREATE_USER_ID = #{createUserId,jdbcType=DECIMAL},
            </if>
            <if test="companyAddress != null">
                COMPANY_ADDRESS = #{companyAddress,jdbcType=VARCHAR},
            </if>
            <if test="companyPhone != null">
                COMPANY_PHONE = #{companyPhone,jdbcType=VARCHAR},
            </if>
            <if test="companyPId != null">
                COMPANY_P_ID = #{companyPId,jdbcType=VARCHAR},
            </if>
        </set>
        where COMPANY_ID = #{companyId,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.Company">
    update COMPANY
    set COMPANY_NAME = #{companyName,jdbcType=VARCHAR},
    COMPANY_CODE = #{companyCode,jdbcType=VARCHAR},
    COMPANY_TYPE = #{companyType,jdbcType=VARCHAR},
    TIME_INTERVAL = #{timeInterval,jdbcType=DECIMAL},
    ORG_ID = #{orgId,jdbcType=VARCHAR},
    COMPANY_STATUS = #{companyStatus,jdbcType=VARCHAR},
    CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
    CREATE_USER_ID = #{createUserId,jdbcType=DECIMAL},
    COMPANY_ADDRESS = #{companyAddress,jdbcType=VARCHAR},
    COMPANY_PHONE = #{companyPhone,jdbcType=VARCHAR},
    COMPANY_P_ID = #{companyPId,jdbcType=VARCHAR}
    where COMPANY_ID = #{companyId,jdbcType=VARCHAR}
  </update>

    <!-- 查询所有公司 -->
    <select id="selectListON" resultType="com.sinoair.billing.domain.model.billing.Company">
        select
        <include refid="Base_Column_List"/>
        from COMPANY
        where COMPANY_STATUS = 'ON'
    </select>
    <select id="getCompanyListMap" resultType="java.util.HashMap"
            parameterType="com.sinoair.billing.domain.vo.FilterParam">
        select c.company_id as id,c.company_name as name ,c.company_code as code ,c.company_type as type,
        c.org_id as orgid,c.TIME_INTERVAL as timeinterval ,c.company_address as address,c.company_phone as
        phone,c.company_p_id as p_id ,
        c.company_status as status,c.create_time as createtime ,u.realname as realname
        from COMPANY c left join users u on u.id = c.create_user_id
        where 1=1
        <if test="pid == '1' || pid == '1'">
            and UPPER (c.company_p_id) = '1'
        </if>
        <if test="pid == '2' || pid == 2">
            and UPPER (c.company_p_id) != '1' and UPPER (c.company_p_id) != '0'
        </if>
        <if test="pid2 != '0' and pid2 != null and pid2 != ''">
            and UPPER (c.company_p_id) = #{pid2}
        </if>
        <if test="code != null and code != ''">
            and UPPER(c.company_code) like '%'||UPPER(#{code})||'%'
        </if>
        <if test="name != null and name != ''">
            and UPPER(c.company_name) like '%'||UPPER(#{name})||'%'
        </if>
        <if test='sacId != null and sacId != "" and  sacId != "0" and sacId != "-1" '>
            and c.company_id = #{sacId,jdbcType=VARCHAR}
        </if>

    </select>
    <select id="getCompanyListByPid" resultType="com.sinoair.billing.domain.model.billing.Company"
            parameterType="com.sinoair.billing.domain.vo.FilterParam">
        select * from COMPANY
        where 1=1
        <if test='pid !=null and pid !="0"'>
            AND company_p_id = #{pid}
        </if>
        <if test='companyId != null and companyId != "0"'>
            AND company_id = #{companyId}
        </if>
    </select>
    <select id="getCompanyListByProjectCode" resultType="com.sinoair.billing.domain.model.billing.Company"
            parameterType="com.sinoair.billing.domain.vo.FilterParam">
        select * from COMPANY
        where 1=1
        <if test="pid !=null and pid !=0">
            AND UPPER (company_p_id) = #{pid}
        </if>
        <if test="companyId != 0">
            AND UPPER (company_id) = #{companyId}
        </if>
    </select>

    <select id="selectCountByCode" resultType="java.lang.Integer">
        select count(0) from COMPANY where company_code = #{sac_id}
    </select>
</mapper>