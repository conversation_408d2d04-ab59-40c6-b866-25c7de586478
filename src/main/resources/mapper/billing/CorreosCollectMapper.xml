<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.CorreosCollectMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.CorreosCollect" >
    <result column="DEBIT_NO" property="debitNo" jdbcType="VARCHAR" />
    <result column="DESCRIPTIONS" property="descriptions" jdbcType="VARCHAR" />
    <result column="UNITS" property="units" jdbcType="DECIMAL" />
    <result column="PRICE" property="price" jdbcType="DECIMAL" />
    <result column="GROSS_AM" property="grossAm" jdbcType="DECIMAL" />
    <result column="DISCS" property="discs" jdbcType="DECIMAL" />
    <result column="AMOUNT" property="amount" jdbcType="DECIMAL" />
    <result column="CORREOS_FILE" property="correosFile" jdbcType="VARCHAR" />
  </resultMap>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.CorreosCollect" >
    insert into CORREOS_COLLECT (DEBIT_NO, DESCRIPTIONS, UNITS, 
      PRICE, GROSS_AM, DISCS, 
      AMOUNT, CORREOS_FILE)
    values (#{debitNo,jdbcType=VARCHAR}, #{descriptions,jdbcType=VARCHAR}, #{units,jdbcType=DECIMAL}, 
      #{price,jdbcType=DECIMAL}, #{grossAm,jdbcType=DECIMAL}, #{discs,jdbcType=DECIMAL}, 
      #{amount,jdbcType=DECIMAL}, #{correosFile,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.CorreosCollect" >
    insert into CORREOS_COLLECT
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="debitNo != null" >
        DEBIT_NO,
      </if>
      <if test="descriptions != null" >
        DESCRIPTIONS,
      </if>
      <if test="units != null" >
        UNITS,
      </if>
      <if test="price != null" >
        PRICE,
      </if>
      <if test="grossAm != null" >
        GROSS_AM,
      </if>
      <if test="discs != null" >
        DISCS,
      </if>
      <if test="amount != null" >
        AMOUNT,
      </if>
      <if test="correosFile != null" >
        CORREOS_FILE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="debitNo != null" >
        #{debitNo,jdbcType=VARCHAR},
      </if>
      <if test="descriptions != null" >
        #{descriptions,jdbcType=VARCHAR},
      </if>
      <if test="units != null" >
        #{units,jdbcType=DECIMAL},
      </if>
      <if test="price != null" >
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="grossAm != null" >
        #{grossAm,jdbcType=DECIMAL},
      </if>
      <if test="discs != null" >
        #{discs,jdbcType=DECIMAL},
      </if>
      <if test="amount != null" >
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="correosFile != null" >
        #{correosFile,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <insert id="insertBatch"  parameterType="java.util.List">
    insert into CORREOS_COLLECT (DEBIT_NO, DESCRIPTIONS, UNITS,
    PRICE, GROSS_AM, DISCS,
    AMOUNT, CORREOS_FILE,FILE_MONTH,SERVICES)

    <foreach collection="list" item="item" index="index" separator="UNION ALL">
      select #{item.debitNo,jdbcType=VARCHAR},#{item.descriptions,jdbcType=VARCHAR},
      #{item.units,jdbcType=DECIMAL},#{item.price,jdbcType=DECIMAL},
      #{item.grossAm,jdbcType=DECIMAL},#{item.discs,jdbcType=DECIMAL},
      #{item.amount,jdbcType=DECIMAL},#{item.correosFile,jdbcType=VARCHAR},
      #{item.fileMonth,jdbcType=VARCHAR},#{item.services,jdbcType=VARCHAR} from dual
    </foreach>

  </insert>
</mapper>