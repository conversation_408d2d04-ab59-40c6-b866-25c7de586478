<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.CorreosDetailMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.CorreosDetail" >
    <result column="DEBIT_NO" property="debitNo" jdbcType="DECIMAL" />
    <result column="SHPT_NO" property="shptNo" jdbcType="VARCHAR" />
    <result column="SCTON" property="scton" jdbcType="VARCHAR" />
    <result column="GROSS_AMT" property="grossAmt" jdbcType="DECIMAL" />
    <result column="CD_NAME" property="cdName" jdbcType="VARCHAR" />
    <result column="CD_MONTH" property="cdMonth" jdbcType="VARCHAR" />
    <result column="CORREOS_ADM" property="correosAdm" jdbcType="VARCHAR" />
    <result column="CORREOS_FILE" property="correosFile" jdbcType="VARCHAR" />
    <result column="DESCRIPTIONS" property="descriptions" jdbcType="VARCHAR" />
  </resultMap>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.CorreosDetail" >
    insert into CORREOS_DETAIL (DEBIT_NO, SHPT_NO, SCTON, 
      GROSS_AMT, CD_NAME, CD_MONTH, 
      CORREOS_ADM, CORREOS_FILE,DESCRIPTIONS)
    values (#{debitNo,jdbcType=DECIMAL}, #{shptNo,jdbcType=VARCHAR}, #{scton,jdbcType=VARCHAR}, 
      #{grossAmt,jdbcType=DECIMAL}, #{cdName,jdbcType=VARCHAR}, #{cdMonth,jdbcType=VARCHAR}, 
      #{correosAdm,jdbcType=VARCHAR}, #{correosFile,jdbcType=VARCHAR},, #{descriptions,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.CorreosDetail" >
    insert into CORREOS_DETAIL
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="debitNo != null" >
        DEBIT_NO,
      </if>
      <if test="shptNo != null" >
        SHPT_NO,
      </if>
      <if test="scton != null" >
        SCTON,
      </if>
      <if test="grossAmt != null" >
        GROSS_AMT,
      </if>
      <if test="cdName != null" >
        CD_NAME,
      </if>
      <if test="cdMonth != null" >
        CD_MONTH,
      </if>
      <if test="correosAdm != null" >
        CORREOS_ADM,
      </if>
      <if test="correosFile != null" >
        CORREOS_FILE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="debitNo != null" >
        #{debitNo,jdbcType=DECIMAL},
      </if>
      <if test="shptNo != null" >
        #{shptNo,jdbcType=VARCHAR},
      </if>
      <if test="scton != null" >
        #{scton,jdbcType=VARCHAR},
      </if>
      <if test="grossAmt != null" >
        #{grossAmt,jdbcType=DECIMAL},
      </if>
      <if test="cdName != null" >
        #{cdName,jdbcType=VARCHAR},
      </if>
      <if test="cdMonth != null" >
        #{cdMonth,jdbcType=VARCHAR},
      </if>
      <if test="correosAdm != null" >
        #{correosAdm,jdbcType=VARCHAR},
      </if>
      <if test="correosFile != null" >
        #{correosFile,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <insert id="insertBatch"  parameterType="java.util.List">
    insert into CORREOS_DETAIL (DEBIT_NO, SHPT_NO, SCTON,
    GROSS_AMT, CD_NAME, CD_MONTH,
    CORREOS_ADM, CORREOS_FILE,DESCRIPTIONS,FILE_MONTH)

    <foreach collection="list" item="item" index="index" separator="UNION ALL">
      select #{item.debitNo,jdbcType=DECIMAL},#{item.shptNo,jdbcType=VARCHAR},
      #{item.scton,jdbcType=VARCHAR},#{item.grossAmt,jdbcType=DECIMAL},
             #{item.cdName,jdbcType=VARCHAR},#{item.cdMonth,jdbcType=VARCHAR},
             #{item.correosAdm,jdbcType=VARCHAR},#{item.correosFile,jdbcType=VARCHAR},#{item.descriptions,jdbcType=VARCHAR},
      #{item.fileMonth,jdbcType=VARCHAR} from dual
    </foreach>

  </insert>

  <select id="selectCorreosDetail" resultType="com.sinoair.billing.domain.model.billing.PaymentBillDetail"
          parameterType="com.sinoair.billing.domain.model.billing.CorreosDetail" >
    select cd.debit_no as invoiceCode,cd.shpt_no as businessCode,cn.detalle_bultos as dsbmsCode,
    cd.scton as chargeweightScope,cd.gross_amt as pbdAmount,
    to_date(to_char(cd.cd_month)||'01','yyyy-mm-dd') as pbdDate,
    cd.correos_file as pbdFilename,cd.gross_amt as pbdActualAmount
    from correos_detail cd,correos_no cn
    where cd.shpt_no = cn.shpt_no
    and cd.correos_file = cn.correos_file
    and cd.cd_month = cn.cn_month
    <if test="cdMonth != null" >
      and cd.cd_month = #{cdMonth,jdbcType=VARCHAR}
    </if>
    <if test="correosFile != null" >
      and cd.correos_file = #{correosFile,jdbcType=VARCHAR}
    </if>
  </select>
</mapper>