<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.CorreosLogMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.CorreosLog" >
    <result column="CORREOS_FILE" property="correosFile" jdbcType="VARCHAR" />
    <result column="DETAIL_COUNT" property="detailCount" jdbcType="DECIMAL" />
    <result column="NO_COUNT" property="noCount" jdbcType="DECIMAL" />
    <result column="COLLECT_COUNT" property="collectCount" jdbcType="DECIMAL" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="IS_MANIFEST" property="isManifest" jdbcType="VARCHAR" />
    <result column="MANIFEST_COUNT" property="manifestCount" jdbcType="DECIMAL" />
    <result column="MANIFEST_AMOUNT" property="manifestAmount" jdbcType="DECIMAL" />
    <result column="MANIFEST_WEIGHT" property="manifestWeight" jdbcType="DECIMAL" />
  </resultMap>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.CorreosLog" >
    insert into CORREOS_LOG (CORREOS_FILE, DETAIL_COUNT, NO_COUNT, 
      COLLECT_COUNT, CREATE_TIME)
    values (#{correosFile,jdbcType=VARCHAR}, #{detailCount,jdbcType=DECIMAL}, #{noCount,jdbcType=DECIMAL}, 
      #{collectCount,jdbcType=DECIMAL}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.CorreosLog" >
    insert into CORREOS_LOG
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="correosFile != null" >
        CORREOS_FILE,
      </if>
      <if test="detailCount != null" >
        DETAIL_COUNT,
      </if>
      <if test="noCount != null" >
        NO_COUNT,
      </if>
      <if test="collectCount != null" >
        COLLECT_COUNT,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
      <if test="fileMonth != null" >
        FILE_MONTH,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="correosFile != null" >
        #{correosFile,jdbcType=VARCHAR},
      </if>
      <if test="detailCount != null" >
        #{detailCount,jdbcType=DECIMAL},
      </if>
      <if test="noCount != null" >
        #{noCount,jdbcType=DECIMAL},
      </if>
      <if test="collectCount != null" >
        #{collectCount,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="fileMonth != null" >
        #{fileMonth,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <select id="listCorreosFile" resultType="java.lang.String" parameterType="java.lang.String" >
    select
      CORREOS_FILE
    from CORREOS_LOG
    where FILE_MONTH = #{fileMonth,jdbcType=VARCHAR}
        and IS_MANIFEST = 'N'
  </select>

  <update id="updateByMonthAndFile" parameterType="com.sinoair.billing.domain.model.billing.CorreosLog" >
    update CORREOS_LOG
    set IS_MANIFEST = 'Y',
        MANIFEST_COUNT =  #{manifestCount,jdbcType=DECIMAL},
        MANIFEST_AMOUNT = #{manifestAmount,jdbcType=DECIMAL},
        MANIFEST_WEIGHT = #{manifestWeight,jdbcType=DECIMAL}
    where FILE_MONTH = #{fileMonth,jdbcType=VARCHAR}
      and CORREOS_FILE = #{correosFile,jdbcType=VARCHAR}
  </update>

  <select id="countCorreosFile" resultType="java.lang.Integer" parameterType="java.lang.String" >
    select
      count(1)
    from CORREOS_LOG
    where FILE_MONTH = #{fileMonth,jdbcType=VARCHAR}
      and IS_MANIFEST = 'N'
  </select>

  <select id="selectSumCorreosFile" resultType="com.sinoair.billing.domain.model.billing.CorreosLog" parameterType="java.lang.String" >
    select
      sum(MANIFEST_COUNT) MANIFEST_COUNT,
      sum(MANIFEST_AMOUNT) MANIFEST_AMOUNT,
      sum(MANIFEST_WEIGHT) MANIFEST_COUNT
    from CORREOS_LOG
    where FILE_MONTH = #{fileMonth,jdbcType=VARCHAR}
  </select>
</mapper>