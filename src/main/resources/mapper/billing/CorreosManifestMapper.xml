<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.CorreosManifestMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.CorreosManifest" >
    <id column="FILE_MONTH" property="fileMonth" jdbcType="VARCHAR" />
    <result column="CM_ID" property="cmId" jdbcType="DECIMAL" />
    <result column="CORREOS_STATUS" property="correosStatus" jdbcType="VARCHAR" />
    <result column="CORREOS_ACCOUNT" property="correosAccount" jdbcType="DECIMAL" />
    <result column="HANDLE_ACCOUNT" property="handleAccount" jdbcType="DECIMAL" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="HANDLE_TIME" property="handleTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    FILE_MONTH, CM_ID, CORREOS_STATUS, CORREOS_ACCOUNT, HANDLE_ACCOUNT, CREATE_TIME, 
    HANDLE_TIME
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from CORREOS_MANIFEST
    where FILE_MONTH = #{fileMonth,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from CORREOS_MANIFEST
    where FILE_MONTH = #{fileMonth,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.CorreosManifest" >
    insert into CORREOS_MANIFEST (FILE_MONTH, CM_ID, CORREOS_STATUS, 
      CORREOS_ACCOUNT, HANDLE_ACCOUNT, CREATE_TIME, 
      HANDLE_TIME)
    values (#{fileMonth,jdbcType=VARCHAR}, #{cmId,jdbcType=DECIMAL}, #{correosStatus,jdbcType=VARCHAR}, 
      #{correosAccount,jdbcType=DECIMAL}, #{handleAccount,jdbcType=DECIMAL}, #{createTime,jdbcType=TIMESTAMP}, 
      #{handleTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.CorreosManifest" >
    insert into CORREOS_MANIFEST
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="fileMonth != null" >
        FILE_MONTH,
      </if>
      <if test="cmId != null" >
        CM_ID,
      </if>
      <if test="correosStatus != null" >
        CORREOS_STATUS,
      </if>
      <if test="correosAccount != null" >
        CORREOS_ACCOUNT,
      </if>
      <if test="handleAccount != null" >
        HANDLE_ACCOUNT,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
      <if test="handleTime != null" >
        HANDLE_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="fileMonth != null" >
        #{fileMonth,jdbcType=VARCHAR},
      </if>
      <if test="cmId != null" >
        #{cmId,jdbcType=DECIMAL},
      </if>
      <if test="correosStatus != null" >
        #{correosStatus,jdbcType=VARCHAR},
      </if>
      <if test="correosAccount != null" >
        #{correosAccount,jdbcType=DECIMAL},
      </if>
      <if test="handleAccount != null" >
        #{handleAccount,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="handleTime != null" >
        #{handleTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.CorreosManifest" >
    update CORREOS_MANIFEST
    <set >
      <if test="cmId != null" >
        CM_ID = #{cmId,jdbcType=DECIMAL},
      </if>
      <if test="correosStatus != null" >
        CORREOS_STATUS = #{correosStatus,jdbcType=VARCHAR},
      </if>
      <if test="correosAccount != null" >
        CORREOS_ACCOUNT = #{correosAccount,jdbcType=DECIMAL},
      </if>
      <if test="handleAccount != null" >
        HANDLE_ACCOUNT = #{handleAccount,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null" >
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="handleTime != null" >
        HANDLE_TIME = #{handleTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where FILE_MONTH = #{fileMonth,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.CorreosManifest" >
    update CORREOS_MANIFEST
    set CM_ID = #{cmId,jdbcType=DECIMAL},
      CORREOS_STATUS = #{correosStatus,jdbcType=VARCHAR},
      CORREOS_ACCOUNT = #{correosAccount,jdbcType=DECIMAL},
      HANDLE_ACCOUNT = #{handleAccount,jdbcType=DECIMAL},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      HANDLE_TIME = #{handleTime,jdbcType=TIMESTAMP}
    where FILE_MONTH = #{fileMonth,jdbcType=VARCHAR}
  </update>

  <select id="listCorreosManifest" resultMap="BaseResultMap" parameterType="com.sinoair.billing.domain.model.billing.CorreosManifest" >
    select
    <include refid="Base_Column_List" />
    from CORREOS_MANIFEST
    where
        CORREOS_STATUS = #{correosStatus,jdbcType=VARCHAR}
    <if test="fileMonth != null" >
      and FILE_MONTH = #{fileMonth,jdbcType=VARCHAR}
    </if>
  </select>

  <update id="updateHandleCount" parameterType="com.sinoair.billing.domain.model.billing.CorreosManifest" >
    update CORREOS_MANIFEST
    set
        HANDLE_ACCOUNT = NVL(HANDLE_ACCOUNT,0) + 1
    where FILE_MONTH = #{fileMonth,jdbcType=VARCHAR}
  </update>

</mapper>