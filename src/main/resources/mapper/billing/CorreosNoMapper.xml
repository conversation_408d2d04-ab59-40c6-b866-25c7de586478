<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.CorreosNoMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.CorreosNo" >
    <result column="DELIVS_NO" property="delivsNo" jdbcType="DECIMAL" />
    <result column="SHPT_NO" property="shptNo" jdbcType="VARCHAR" />
    <result column="DETALLE_BULTOS" property="detalleBultos" jdbcType="VARCHAR" />
    <result column="CN_NAME" property="cnName" jdbcType="VARCHAR" />
    <result column="CN_MONTH" property="cnMonth" jdbcType="VARCHAR" />
    <result column="DEBIT_NO" property="debitNo" jdbcType="VARCHAR" />
    <result column="CORREOS_FILE" property="correosFile" jdbcType="VARCHAR" />
    <result column="CORREOS_ADM" property="correosAdm" jdbcType="VARCHAR" />
    <result column="CORREOS_ADM" property="correosAdm" jdbcType="VARCHAR" />
  </resultMap>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.CorreosNo" >
    insert into CORREOS_NO (DELIVS_NO, SHPT_NO, DETALLE_BULTOS, 
      CN_NAME, CN_MONTH, DEBIT_NO, 
      CORREOS_FILE, CORREOS_ADM)
    values (#{delivsNo,jdbcType=DECIMAL}, #{shptNo,jdbcType=VARCHAR}, #{detalleBultos,jdbcType=VARCHAR}, 
      #{cnName,jdbcType=VARCHAR}, #{cnMonth,jdbcType=VARCHAR}, #{debitNo,jdbcType=VARCHAR}, 
      #{correosFile,jdbcType=VARCHAR}, #{correosAdm,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.CorreosNo" >
    insert into CORREOS_NO
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="delivsNo != null" >
        DELIVS_NO,
      </if>
      <if test="shptNo != null" >
        SHPT_NO,
      </if>
      <if test="detalleBultos != null" >
        DETALLE_BULTOS,
      </if>
      <if test="cnName != null" >
        CN_NAME,
      </if>
      <if test="cnMonth != null" >
        CN_MONTH,
      </if>
      <if test="debitNo != null" >
        DEBIT_NO,
      </if>
      <if test="correosFile != null" >
        CORREOS_FILE,
      </if>
      <if test="correosAdm != null" >
        CORREOS_ADM,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="delivsNo != null" >
        #{delivsNo,jdbcType=DECIMAL},
      </if>
      <if test="shptNo != null" >
        #{shptNo,jdbcType=VARCHAR},
      </if>
      <if test="detalleBultos != null" >
        #{detalleBultos,jdbcType=VARCHAR},
      </if>
      <if test="cnName != null" >
        #{cnName,jdbcType=VARCHAR},
      </if>
      <if test="cnMonth != null" >
        #{cnMonth,jdbcType=VARCHAR},
      </if>
      <if test="debitNo != null" >
        #{debitNo,jdbcType=VARCHAR},
      </if>
      <if test="correosFile != null" >
        #{correosFile,jdbcType=VARCHAR},
      </if>
      <if test="correosAdm != null" >
        #{correosAdm,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <insert id="insertBatch"  parameterType="java.util.List">
    insert into CORREOS_NO (DELIVS_NO, SHPT_NO, DETALLE_BULTOS,
    CN_NAME, CN_MONTH, DEBIT_NO,
    CORREOS_FILE, CORREOS_ADM,FILE_MONTH)

    <foreach collection="list" item="item" index="index" separator="UNION ALL">
      select #{item.delivsNo,jdbcType=DECIMAL},#{item.shptNo,jdbcType=VARCHAR},
      #{item.detalleBultos,jdbcType=VARCHAR},#{item.cnName,jdbcType=VARCHAR},
      #{item.cnMonth,jdbcType=VARCHAR},#{item.debitNo,jdbcType=VARCHAR},
      #{item.correosFile,jdbcType=VARCHAR},#{item.correosAdm,jdbcType=VARCHAR},
      #{item.fileMonth,jdbcType=VARCHAR}
    from dual
    </foreach>

  </insert>

</mapper>