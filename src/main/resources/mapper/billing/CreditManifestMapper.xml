<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.CreditManifestMapper">
    <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.CreditManifest">
        <id column="CM_ID" property="cmId" jdbcType="DECIMAL"/>
        <result column="COMPANY_ID" property="companyId" jdbcType="VARCHAR"/>
        <result column="SO_CODE" property="soCode" jdbcType="VARCHAR"/>
        <result column="CT_CODE" property="ctCode" jdbcType="VARCHAR"/>
        <result column="CM_CURRENCYRATE" property="cmCurrencyrate" jdbcType="DECIMAL"/>
        <result column="CM_TOTALRMB" property="cmTotalrmb" jdbcType="DECIMAL"/>
        <result column="CM_TOTALFC" property="cmTotalfc" jdbcType="DECIMAL"/>
        <result column="SO_TAX" property="soTax" jdbcType="DECIMAL"/>
        <result column="TAX_AMOUNT" property="taxAmount" jdbcType="DECIMAL"/>
        <result column="NOTAX_AMOUNT" property="notaxAmount" jdbcType="DECIMAL"/>
        <result column="TAX_AMOUNT_FC" property="taxAmountFc" jdbcType="DECIMAL"/>
        <result column="NOTAX_AMOUNT_FC" property="notaxAmountFc" jdbcType="DECIMAL"/>
        <result column="INVOICE_CODE" property="invoiceCode" jdbcType="VARCHAR"/>
        <result column="CM_USER_ID" property="cmUserId" jdbcType="DECIMAL"/>
        <result column="CM_CREATE_TIME" property="cmCreateTime" jdbcType="TIMESTAMP"/>
        <result column="CM_HANDLE_TIME" property="cmHandleTime" jdbcType="TIMESTAMP"/>
        <result column="CM_STATUS" property="cmStatus" jdbcType="VARCHAR"/>
        <result column="CM_DIRTY" property="cmDirty" jdbcType="VARCHAR"/>
        <result column="CM_DIRTY_TIME" property="cmDirtyTime" jdbcType="TIMESTAMP"/>
        <result column="CM_CODE" property="cmCode" jdbcType="VARCHAR"/>
        <result column="CM_PLAN_AMOUNT" property="cmPlanAmount" jdbcType="DECIMAL"/>
        <result column="CM_START_TIME" property="cmStartTime" jdbcType="TIMESTAMP"/>
        <result column="CM_END_TIME" property="cmEndTime" jdbcType="TIMESTAMP"/>
        <result column="CM_TOTAL_PIECES" property="cmTotalPieces" jdbcType="DECIMAL"/>
        <result column="CM_TOTAL_WEIGHT" property="cmTotalWeight" jdbcType="DECIMAL"/>
        <result column="CM_REMARK" property="cmRemark" jdbcType="VARCHAR"/>
        <result column="CM_TYPE" property="cmType" jdbcType="VARCHAR"/>
    </resultMap>
    <!-- 序列 -->
    <sql id='TABLE_SEQUENCE'>SEQ_CREDIT_MANIFEST.NEXTVAL</sql>

    <sql id="Base_Column_List">
        CM_ID, COMPANY_ID, SO_CODE, CT_CODE, CM_CURRENCYRATE, CM_TOTALRMB, CM_TOTALFC, SO_TAX,
        TAX_AMOUNT, NOTAX_AMOUNT, TAX_AMOUNT_FC, NOTAX_AMOUNT_FC, INVOICE_CODE, CM_USER_ID,
        CM_CREATE_TIME, CM_HANDLE_TIME, CM_STATUS, CM_DIRTY, CM_DIRTY_TIME, CM_CODE, CM_PLAN_AMOUNT,
        CM_START_TIME, CM_END_TIME, CM_TOTAL_PIECES, CM_TOTAL_WEIGHT, CM_REMARK, CM_TYPE
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Short">
        select
        <include refid="Base_Column_List"/>
        from CREDIT_MANIFEST
        where CM_ID = #{cmId,jdbcType=DECIMAL}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from CREDIT_MANIFEST
    where CM_ID = #{cmId,jdbcType=DECIMAL}
  </delete>
    <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.CreditManifest">
        <selectKey keyProperty="cmId" resultType="int" order="BEFORE">
            select
            <include refid="TABLE_SEQUENCE"/>
            from dual
        </selectKey>
        insert into CREDIT_MANIFEST (CM_ID, COMPANY_ID, SO_CODE,
        CT_CODE, CM_CURRENCYRATE, CM_TOTALRMB,
        CM_TOTALFC, SO_TAX, TAX_AMOUNT,
        NOTAX_AMOUNT, TAX_AMOUNT_FC, NOTAX_AMOUNT_FC,
        INVOICE_CODE, CM_USER_ID, CM_CREATE_TIME,
        CM_HANDLE_TIME, CM_STATUS, CM_DIRTY,
        CM_DIRTY_TIME, CM_CODE, CM_PLAN_AMOUNT,
        CM_START_TIME, CM_END_TIME, CM_TOTAL_PIECES,
        CM_TOTAL_WEIGHT, CM_REMARK, CM_TYPE
        )
        values (#{cmId,jdbcType=DECIMAL}, #{companyId,jdbcType=VARCHAR}, #{soCode,jdbcType=VARCHAR},
        #{ctCode,jdbcType=VARCHAR}, #{cmCurrencyrate,jdbcType=DECIMAL}, #{cmTotalrmb,jdbcType=DECIMAL},
        #{cmTotalfc,jdbcType=DECIMAL}, #{soTax,jdbcType=DECIMAL}, #{taxAmount,jdbcType=DECIMAL},
        #{notaxAmount,jdbcType=DECIMAL}, #{taxAmountFc,jdbcType=DECIMAL}, #{notaxAmountFc,jdbcType=DECIMAL},
        #{invoiceCode,jdbcType=VARCHAR}, #{cmUserId,jdbcType=DECIMAL}, #{cmCreateTime,jdbcType=TIMESTAMP},
        #{cmHandleTime,jdbcType=TIMESTAMP}, #{cmStatus,jdbcType=VARCHAR}, #{cmDirty,jdbcType=VARCHAR},
        #{cmDirtyTime,jdbcType=TIMESTAMP}, #{cmCode,jdbcType=VARCHAR}, #{cmPlanAmount,jdbcType=DECIMAL},
        #{cmStartTime,jdbcType=TIMESTAMP}, #{cmEndTime,jdbcType=TIMESTAMP}, #{cmTotalPieces,jdbcType=DECIMAL},
        #{cmTotalWeight,jdbcType=DECIMAL}, #{cmRemark,jdbcType=VARCHAR}, #{cmType,jdbcType=VARCHAR}
        )
    </insert>
    <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.CreditManifest">
        <selectKey keyProperty="cmId" resultType="int" order="BEFORE">
            select
            <include refid="TABLE_SEQUENCE"/>
            from dual
        </selectKey>
        insert into CREDIT_MANIFEST
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="cmId != null">
                CM_ID,
            </if>
            <if test="companyId != null">
                COMPANY_ID,
            </if>
            <if test="soCode != null">
                SO_CODE,
            </if>
            <if test="ctCode != null">
                CT_CODE,
            </if>
            <if test="cmCurrencyrate != null">
                CM_CURRENCYRATE,
            </if>
            <if test="cmTotalrmb != null">
                CM_TOTALRMB,
            </if>
            <if test="cmTotalfc != null">
                CM_TOTALFC,
            </if>
            <if test="soTax != null">
                SO_TAX,
            </if>
            <if test="taxAmount != null">
                TAX_AMOUNT,
            </if>
            <if test="notaxAmount != null">
                NOTAX_AMOUNT,
            </if>
            <if test="taxAmountFc != null">
                TAX_AMOUNT_FC,
            </if>
            <if test="notaxAmountFc != null">
                NOTAX_AMOUNT_FC,
            </if>
            <if test="invoiceCode != null">
                INVOICE_CODE,
            </if>
            <if test="cmUserId != null">
                CM_USER_ID,
            </if>
            <if test="cmCreateTime != null">
                CM_CREATE_TIME,
            </if>
            <if test="cmHandleTime != null">
                CM_HANDLE_TIME,
            </if>
            <if test="cmStatus != null">
                CM_STATUS,
            </if>
            <if test="cmDirty != null">
                CM_DIRTY,
            </if>
            <if test="cmDirtyTime != null">
                CM_DIRTY_TIME,
            </if>
            <if test="cmCode != null">
                CM_CODE,
            </if>
            <if test="cmPlanAmount != null">
                CM_PLAN_AMOUNT,
            </if>
            <if test="cmStartTime != null">
                CM_START_TIME,
            </if>
            <if test="cmEndTime != null">
                CM_END_TIME,
            </if>
            <if test="cmTotalPieces != null">
                CM_TOTAL_PIECES,
            </if>
            <if test="cmTotalWeight != null">
                CM_TOTAL_WEIGHT,
            </if>
            <if test="cmRemark != null">
                CM_REMARK,
            </if>
            <if test="cmType != null">
                CM_TYPE,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="cmId != null">
                #{cmId,jdbcType=DECIMAL},
            </if>
            <if test="companyId != null">
                #{companyId,jdbcType=VARCHAR},
            </if>
            <if test="soCode != null">
                #{soCode,jdbcType=VARCHAR},
            </if>
            <if test="ctCode != null">
                #{ctCode,jdbcType=VARCHAR},
            </if>
            <if test="cmCurrencyrate != null">
                #{cmCurrencyrate,jdbcType=DECIMAL},
            </if>
            <if test="cmTotalrmb != null">
                #{cmTotalrmb,jdbcType=DECIMAL},
            </if>
            <if test="cmTotalfc != null">
                #{cmTotalfc,jdbcType=DECIMAL},
            </if>
            <if test="soTax != null">
                #{soTax,jdbcType=DECIMAL},
            </if>
            <if test="taxAmount != null">
                #{taxAmount,jdbcType=DECIMAL},
            </if>
            <if test="notaxAmount != null">
                #{notaxAmount,jdbcType=DECIMAL},
            </if>
            <if test="taxAmountFc != null">
                #{taxAmountFc,jdbcType=DECIMAL},
            </if>
            <if test="notaxAmountFc != null">
                #{notaxAmountFc,jdbcType=DECIMAL},
            </if>
            <if test="invoiceCode != null">
                #{invoiceCode,jdbcType=VARCHAR},
            </if>
            <if test="cmUserId != null">
                #{cmUserId,jdbcType=DECIMAL},
            </if>
            <if test="cmCreateTime != null">
                #{cmCreateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="cmHandleTime != null">
                #{cmHandleTime,jdbcType=TIMESTAMP},
            </if>
            <if test="cmStatus != null">
                #{cmStatus,jdbcType=VARCHAR},
            </if>
            <if test="cmDirty != null">
                #{cmDirty,jdbcType=VARCHAR},
            </if>
            <if test="cmDirtyTime != null">
                #{cmDirtyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="cmCode != null">
                #{cmCode,jdbcType=VARCHAR},
            </if>
            <if test="cmPlanAmount != null">
                #{cmPlanAmount,jdbcType=DECIMAL},
            </if>
            <if test="cmStartTime != null">
                #{cmStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="cmEndTime != null">
                #{cmEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="cmTotalPieces != null">
                #{cmTotalPieces,jdbcType=DECIMAL},
            </if>
            <if test="cmTotalWeight != null">
                #{cmTotalWeight,jdbcType=DECIMAL},
            </if>
            <if test="cmRemark != null">
                #{cmRemark,jdbcType=VARCHAR},
            </if>
            <if test="cmType != null">
                #{cmType,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.CreditManifest">
        update CREDIT_MANIFEST
        <set>
            <if test="companyId != null">
                COMPANY_ID = #{companyId,jdbcType=VARCHAR},
            </if>
            <if test="soCode != null">
                SO_CODE = #{soCode,jdbcType=VARCHAR},
            </if>
            <if test="ctCode != null">
                CT_CODE = #{ctCode,jdbcType=VARCHAR},
            </if>
            <if test="cmCurrencyrate != null">
                CM_CURRENCYRATE = #{cmCurrencyrate,jdbcType=DECIMAL},
            </if>
            <if test="cmTotalrmb != null">
                CM_TOTALRMB = #{cmTotalrmb,jdbcType=DECIMAL},
            </if>
            <if test="cmTotalfc != null">
                CM_TOTALFC = #{cmTotalfc,jdbcType=DECIMAL},
            </if>
            <if test="soTax != null">
                SO_TAX = #{soTax,jdbcType=DECIMAL},
            </if>
            <if test="taxAmount != null">
                TAX_AMOUNT = #{taxAmount,jdbcType=DECIMAL},
            </if>
            <if test="notaxAmount != null">
                NOTAX_AMOUNT = #{notaxAmount,jdbcType=DECIMAL},
            </if>
            <if test="taxAmountFc != null">
                TAX_AMOUNT_FC = #{taxAmountFc,jdbcType=DECIMAL},
            </if>
            <if test="notaxAmountFc != null">
                NOTAX_AMOUNT_FC = #{notaxAmountFc,jdbcType=DECIMAL},
            </if>
            <if test="invoiceCode != null">
                INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR},
            </if>
            <if test="cmUserId != null">
                CM_USER_ID = #{cmUserId,jdbcType=DECIMAL},
            </if>
            <if test="cmCreateTime != null">
                CM_CREATE_TIME = #{cmCreateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="cmHandleTime != null">
                CM_HANDLE_TIME = #{cmHandleTime,jdbcType=TIMESTAMP},
            </if>
            <if test="cmStatus != null">
                CM_STATUS = #{cmStatus,jdbcType=VARCHAR},
            </if>
            <if test="cmDirty != null">
                CM_DIRTY = #{cmDirty,jdbcType=VARCHAR},
            </if>
            <if test="cmDirtyTime != null">
                CM_DIRTY_TIME = #{cmDirtyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="cmCode != null">
                CM_CODE = #{cmCode,jdbcType=VARCHAR},
            </if>
            <if test="cmPlanAmount != null">
                CM_PLAN_AMOUNT = #{cmPlanAmount,jdbcType=DECIMAL},
            </if>
            <if test="cmStartTime != null">
                CM_START_TIME = #{cmStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="cmEndTime != null">
                CM_END_TIME = #{cmEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="cmTotalPieces != null">
                CM_TOTAL_PIECES = #{cmTotalPieces,jdbcType=DECIMAL},
            </if>
            <if test="cmTotalWeight != null">
                CM_TOTAL_WEIGHT = #{cmTotalWeight,jdbcType=DECIMAL},
            </if>
            <if test="cmRemark != null">
                CM_REMARK = #{cmRemark,jdbcType=VARCHAR},
            </if>
            <if test="cmType != null">
                CM_TYPE = #{cmType,jdbcType=VARCHAR},
            </if>
        </set>
        where CM_ID = #{cmId,jdbcType=DECIMAL}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.CreditManifest">
    update CREDIT_MANIFEST
    set COMPANY_ID = #{companyId,jdbcType=VARCHAR},
      SO_CODE = #{soCode,jdbcType=VARCHAR},
      CT_CODE = #{ctCode,jdbcType=VARCHAR},
      CM_CURRENCYRATE = #{cmCurrencyrate,jdbcType=DECIMAL},
      CM_TOTALRMB = #{cmTotalrmb,jdbcType=DECIMAL},
      CM_TOTALFC = #{cmTotalfc,jdbcType=DECIMAL},
      SO_TAX = #{soTax,jdbcType=DECIMAL},
      TAX_AMOUNT = #{taxAmount,jdbcType=DECIMAL},
      NOTAX_AMOUNT = #{notaxAmount,jdbcType=DECIMAL},
      TAX_AMOUNT_FC = #{taxAmountFc,jdbcType=DECIMAL},
      NOTAX_AMOUNT_FC = #{notaxAmountFc,jdbcType=DECIMAL},
      INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR},
      CM_USER_ID = #{cmUserId,jdbcType=DECIMAL},
      CM_CREATE_TIME = #{cmCreateTime,jdbcType=TIMESTAMP},
      CM_HANDLE_TIME = #{cmHandleTime,jdbcType=TIMESTAMP},
      CM_STATUS = #{cmStatus,jdbcType=VARCHAR},
      CM_DIRTY = #{cmDirty,jdbcType=VARCHAR},
      CM_DIRTY_TIME = #{cmDirtyTime,jdbcType=TIMESTAMP},
      CM_CODE = #{cmCode,jdbcType=VARCHAR},
      CM_PLAN_AMOUNT = #{cmPlanAmount,jdbcType=DECIMAL},
      CM_START_TIME = #{cmStartTime,jdbcType=TIMESTAMP},
      CM_END_TIME = #{cmEndTime,jdbcType=TIMESTAMP},
      CM_TOTAL_PIECES = #{cmTotalPieces,jdbcType=DECIMAL},
      CM_TOTAL_WEIGHT = #{cmTotalWeight,jdbcType=DECIMAL},
      CM_REMARK = #{cmRemark,jdbcType=VARCHAR},
      CM_TYPE = #{cmType,jdbcType=VARCHAR}
    where CM_ID = #{cmId,jdbcType=DECIMAL}
  </update>
    <!--、、、、、、、、、、、、、、、、、、、、、、、、、、-->


    <!--根据条件检索-->
    <select id="selectInvoiceInfoForSearch" resultType="java.util.HashMap">
        select t1.*,t2.SP_NAME,t2.sp_type,t3.CT_NAME from CREDIT_MANIFEST t1, SUPPLIER t2,CURRENCYTYPE t3 where 1=1
        and t1.SO_CODE=t2.SP_CODE
        and t3.CT_CODE=t1.CT_CODE
        and t1.CM_STATUS!='OFF'
        and t1.CM_TYPE!='BALANCE'
        <if test="invoiceStatus != null and invoiceStatus !=''">
            and t1.CM_STATUS=#{invoiceStatus}
        </if>
        <if test="startTime != null and startTime !=''">
            and TO_CHAR(t1.CM_START_TIME,'yyyy-mm-dd')&gt;=#{startTime}
        </if>
        <if test="endTime != null and endTime !=''">
            and TO_CHAR(t1.CM_START_TIME,'yyyy-mm-dd')&lt;=#{endTime}
        </if>
        <if test="customer != null and customer !=''">
            and t1.SO_CODE in (select SP_CODE from SUPPLIER where SP_NAME like '%'||#{customer}||'%')
        </if>
        <if test="companyId != null and companyId !=''">
            and t1.COMPANY_ID=#{companyId}
        </if>
        <if test="seach_type != null and seach_type !=''">
            and t1.CM_TYPE=#{seach_type}
        </if>
        <if test="iList != null">
            and t1.CM_CODE in
            <foreach item="item" index="index" collection="iList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by t1.CM_START_TIME desc
    </select>
    <select id="selectBalanceManifestList" resultType="java.util.HashMap"
            parameterType="com.sinoair.billing.domain.vo.FilterParam">
        select t1.*,t2.SP_NAME,t3.CT_NAME from CREDIT_MANIFEST t1, SUPPLIER t2,CURRENCYTYPE t3 where 1=1
        and t1.SO_CODE=t2.SP_CODE
        and t3.CT_CODE=t1.CT_CODE
        AND t1.CM_TYPE='BALANCE'
        and t1.CM_STATUS!='OFF'
        <if test="status != null and status !=''">
            and t1.CM_STATUS=#{status}
        </if>
        <if test="type != null and type !=''">
            and t1.cm_type=#{type}
        </if>
        <if test="starttime != null and starttime !=''">
            and TO_CHAR(t1.CM_CREATE_TIME,'yyyy-mm-dd')&gt;=#{starttime}
        </if>
        <if test="endtime != null and endtime !=''">
            and TO_CHAR(t1.CM_CREATE_TIME,'yyyy-mm-dd')&lt;=#{endtime}
        </if>
        <if test="soCode != null and soCode !=''">
            and t1.SO_CODE = #{soCode}
        </if>
        <if test="companyId != null and companyId !=''">
            and t1.COMPANY_ID=#{companyId}
        </if>
        <if test="mawbs != null">
            and t1.CM_CODE in
            <foreach item="item" index="index" collection="mawbs" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by t1.CM_CREATE_TIME desc
    </select>

    <select id="selectInvoiceInfoById" resultType="java.util.HashMap">
        SELECT
	      t1.*, t2.CT_NAME,
	      t3.sp_name as SO_NAME
        FROM
	      CREDIT_MANIFEST t1,
	      CURRENCYTYPE t2,
	      SUPPLIER t3
        WHERE
            1 = 1
        AND T1.CM_ID = #{invoiceId}
        AND T1.CT_CODE = T2.CT_CODE
        AND T1.SO_CODE = T3.SP_CODE
    </select>
    <!--自动计算并更新账单金额，票数，重量-->
    <update id="updateCreditManifest" parameterType="java.lang.Integer">
        update credit_manifest ct
        <set>
            ct.cm_totalfc=
            (select sum(pbd.PBD_ACTUAL_AMOUNT)
            from payment_bill_detail pbd
            where pbd.cm_id=#{cmId}),
            ct.CM_TOTAL_PIECES=
            (select sum(pbd.PBD_ACTUAL_PIECES)
            from payment_bill_detail pbd
            where pbd.cm_id=#{cmId}),
            ct.CM_TOTAL_WEIGHT=
            (select sum(pbd.PBD_ACTUAL_CHARGEWEIGHT)
            from payment_bill_detail pbd
            where pbd.cm_id=#{cmId})
        </set>
        where ct.cm_id=#{cmId}
    </update>


    <select id="getByCmCode" resultType="com.sinoair.billing.domain.model.billing.CreditManifest">
        select * from credit_manifest cm where cm.cm_code=#{cmCode}
    </select>

    <delete id="deletePaymentMinusByCmId" parameterType="java.lang.Integer">
        delete from payment_estimate_minus
        where CM_ID = #{cmId,jdbcType=DECIMAL}
    </delete>

    <select id="getBmsPayAmount" resultType="com.sinoair.billing.domain.model.billing.BmsPayDetail">

        select e.sinotrans_id,pr.so_code as so_code,e.eawb_servicetype_original as ep_key,cm.cm_id,sum(pr.pr_plan_amount) as pay_amount,min(e.sac_id) as sac_id,
        ( select ct_sign from currencytype where ct_code = cm.ct_code) as ct_sign,to_char(min(PR_OCCURTIME),'yyyymmdd') as cm_month,pr.pr_type as prType
        from credit_manifest cm,payment_record pr,expressairwaybill e
        where cm.cm_id = pr.cm_id and pr.eawb_printcode = e.eawb_printcode
        and cm.cm_id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>

        group by e.sinotrans_id,pr.so_code,e.eawb_servicetype_original,cm.cm_id,cm.ct_code,pr.pr_type

    </select>

    <select id="getBmsPayBySo" resultType="com.sinoair.billing.domain.model.billing.BmsPayDetail">
         select e.sinotrans_id,pr.so_code as so_code,e.eawb_servicetype_original as ep_key,sum(pr.pr_actual_amount) as pay_amount,
        min(pr.company_id) as sac_id,
        ( select ct_sign from currencytype where ct_code = pr.ct_code) as ct_sign,to_char(pr.pr_occurtime,'yyyymmdd') as cm_month,pr.pr_type as prType
        from payment_record pr,expressairwaybill e
        where  pr.eawb_printcode = e.eawb_printcode
        and pr.bpd_syscode is null
        and pr.company_id='ZSU'
        and pr.pr_status='ON'
        and e.eawb_type='B2C'
        and pr.so_code=#{soCode}
        and pr.pr_occurtime &lt; to_date(#{endTime}, 'yyyymmdd')
        and pr.pr_occurtime &gt;= sysdate-30
        group by e.sinotrans_id,pr.so_code,e.eawb_servicetype_original,pr.ct_code,to_char(pr.pr_occurtime,'yyyymmdd'),pr.cm_id,pr.pr_type
    </select>

    <select id="getBmsPayAll" resultType="com.sinoair.billing.domain.model.billing.BmsPayDetail">
        select e.sinotrans_id,pr.so_code as so_code,e.eawb_servicetype_original as ep_key,sum(pr.pr_actual_amount) as pay_amount,
        min(pr.company_id) as sac_id,pr.cm_id as cm_id,pr.ct_code,
        ( select ct_sign from currencytype where ct_code = pr.ct_code) as ct_sign,to_char(pr.pr_occurtime,'yyyymmdd') as cm_month,pr.pr_type as prType
        from payment_record pr,expressairwaybill e
        where  pr.eawb_printcode = e.eawb_printcode
        and pr.bpd_syscode is null
        and e.SINOTRANS_ID is not null
        and pr.pr_status='ON'
        and e.eawb_type='B2C'
        and pr.pr_handletime &lt; TRUNC(SYSDATE)
        and pr.pr_handletime >= sysdate-30
        group by e.sinotrans_id,pr.so_code,e.eawb_servicetype_original,pr.ct_code,to_char(pr.pr_occurtime,'yyyymmdd'),pr.cm_id,pr.pr_type
    </select>
</mapper>