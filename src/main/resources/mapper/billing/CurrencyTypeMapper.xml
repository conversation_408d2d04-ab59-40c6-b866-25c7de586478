<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.billing.dao.billing.CurrencyTypeMapper">
    <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.CurrencyType">
        <id column="CT_CODE" jdbcType="VARCHAR" property="ctCode"/>
        <result column="CT_NAME" jdbcType="VARCHAR" property="ctName"/>
        <result column="CT_SIGN" jdbcType="VARCHAR" property="ctSign"/>
        <result column="CT_QUOTEPRICE" jdbcType="VARCHAR" property="ctQuoteprice"/>
        <result column="CT_STATUS" jdbcType="VARCHAR" property="ctStatus"/>
    </resultMap>
    <sql id="Base_Column_List">
    CT_CODE, CT_NAME, CT_SIGN, CT_QUOTEPRICE, CT_STATUS
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from CURRENCYTYPE
        where CT_CODE = #{ctCode,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from CURRENCYTYPE
    where CT_CODE = #{ctCode,jdbcType=VARCHAR}
  </delete>
    <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.CurrencyType">
    insert into CURRENCYTYPE (CT_CODE, CT_NAME, CT_SIGN, 
      CT_QUOTEPRICE, CT_STATUS)
    values (#{ctCode,jdbcType=VARCHAR}, #{ctName,jdbcType=VARCHAR}, #{ctSign,jdbcType=VARCHAR}, 
      #{ctQuoteprice,jdbcType=VARCHAR}, #{ctStatus,jdbcType=VARCHAR})
  </insert>
    <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.CurrencyType">
        insert into CURRENCYTYPE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ctCode != null">
                CT_CODE,
            </if>
            <if test="ctName != null">
                CT_NAME,
            </if>
            <if test="ctSign != null">
                CT_SIGN,
            </if>
            <if test="ctQuoteprice != null">
                CT_QUOTEPRICE,
            </if>
            <if test="ctStatus != null">
                CT_STATUS,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ctCode != null">
                #{ctCode,jdbcType=VARCHAR},
            </if>
            <if test="ctName != null">
                #{ctName,jdbcType=VARCHAR},
            </if>
            <if test="ctSign != null">
                #{ctSign,jdbcType=VARCHAR},
            </if>
            <if test="ctQuoteprice != null">
                #{ctQuoteprice,jdbcType=VARCHAR},
            </if>
            <if test="ctStatus != null">
                #{ctStatus,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.CurrencyType">
        update CURRENCYTYPE
        <set>
            <if test="ctName != null">
                CT_NAME = #{ctName,jdbcType=VARCHAR},
            </if>
            <if test="ctSign != null">
                CT_SIGN = #{ctSign,jdbcType=VARCHAR},
            </if>
            <if test="ctQuoteprice != null">
                CT_QUOTEPRICE = #{ctQuoteprice,jdbcType=VARCHAR},
            </if>
            <if test="ctStatus != null">
                CT_STATUS = #{ctStatus,jdbcType=VARCHAR},
            </if>
        </set>
        where CT_CODE = #{ctCode,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.CurrencyType">
    update CURRENCYTYPE
    set CT_NAME = #{ctName,jdbcType=VARCHAR},
      CT_SIGN = #{ctSign,jdbcType=VARCHAR},
      CT_QUOTEPRICE = #{ctQuoteprice,jdbcType=VARCHAR},
      CT_STATUS = #{ctStatus,jdbcType=VARCHAR}
    where CT_CODE = #{ctCode,jdbcType=VARCHAR}
  </update>

    <select id="getCurrencyType" resultMap="BaseResultMap">
       select * from currencytype ct order by ct.ct_code asc
  </select>

    <select id="getCurrencyTypeByctCode" parameterType="java.lang.String" resultType="java.lang.String">
     select ct.ct_name from currencytype ct where ct.ct_code=#{ctCode}
  </select>
    <select id="getCurrencyCodeByType" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from currencytype ct where ct.ct_name=#{ctCode}
    </select>
    <select id="selectAllCodeNameForSelector" resultType="java.util.HashMap">
     select
     ct.CT_NAME,ct.CT_CODE
      from currencytype ct where  1=1
  </select>

</mapper>