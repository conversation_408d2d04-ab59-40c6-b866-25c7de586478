<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.DebitEawbMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.DebitEawb" >
    <id column="DM_ID" property="dmId" jdbcType="DECIMAL" />
    <result column="EAWB_PRINTCODE" property="eawbPrintcode" jdbcType="VARCHAR" />
    <result column="RR_ACTUAL_AMOUNT" property="rrActualAmount" jdbcType="DECIMAL" />
    <result column="EP_KEY" property="epKey" jdbcType="VARCHAR" />
    <result column="SINOTRANS_ID" property="sinotransId" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    DM_ID, EAWB_PRINTCODE,RR_ACTUAL_AMOUNT,EP_KEY
  </sql>

  <select id="selectDebitEawb" resultMap="BaseResultMap"  >
    select
    <include refid="Base_Column_List" />
    from DEBIT_EAWB
  </select>

  <delete id="deleteBatchByEawb" parameterType="java.util.List" >
    delete from DEBIT_EAWB
    where EAWB_PRINTCODE in
    <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
      #{item.eawbPrintcode,jdbcType=DECIMAL}
    </foreach>
  </delete>

  <insert id="insertByDmId" >
   insert into DEBIT_EAWB (eawb_printcode,dm_id,RR_ACTUAL_AMOUNT,Ep_Key,Sinotrans_Id)
    select rr.eawb_printcode,rr.dm_id,rr.rr_actual_amount,
    rr.ep_key,rr.company_id||
    (select to_char(e.eawb_updatetime,'yyyymm')
    from  expressairwaybill e
    where e.eawb_printcode = rr.eawb_printcode) ||rr.so_code
    from receipt_record rr
    where  dm_id = #{dmId,jdbcType=DECIMAL}
    and rr.rr_occurtime >= (select dm_start_time from debit_manifest where dm_id = #{dmId,jdbcType=DECIMAL})
    and rr.rr_occurtime &lt;= (select dm_end_time from debit_manifest where dm_id = #{dmId,jdbcType=DECIMAL})

  </insert>

    <insert id="insertCNByDmId" >
        insert into DEBIT_EAWB (eawb_printcode,dm_id,RR_ACTUAL_AMOUNT,Ep_Key,Sinotrans_Id)
        select rr.eawb_printcode,rr.dm_id,rr.rr_actual_amount,
        rr.ep_key,rr.company_id||to_char(rr.rr_occurtime,'yyyymm')||rr.so_code
        from receipt_record rr
        where  dm_id = #{dmId,jdbcType=DECIMAL}
        and rr.rr_occurtime >= (select dm_start_time from debit_manifest where dm_id = #{dmId,jdbcType=DECIMAL})
        and rr.rr_occurtime &lt;= (select dm_end_time from debit_manifest where dm_id = #{dmId,jdbcType=DECIMAL})

    </insert>

    <insert id="insertCNTemporaryByDmId" >
        insert into DEBIT_EAWB (eawb_printcode,dm_id,RR_ACTUAL_AMOUNT,Ep_Key,Sinotrans_Id)
        select rr.eawb_printcode,dmt.dm_id,rr.rr_actual_amount,
            rr.ep_key,rr.company_id||to_char(rr.rr_occurtime,'yyyymm')||rr.so_code
            from receipt_record rr,debit_manifest_temporary dmt
            where rr.dm_id = dmt.dm_temp_id
              and dmt.dm_id = #{dmId,jdbcType=DECIMAL}

    </insert>

    <insert id="insertCNTemporaryByTempId" >
        insert into DEBIT_EAWB (eawb_printcode,dm_id,RR_ACTUAL_AMOUNT,Ep_Key,Sinotrans_Id)
        select rr.eawb_printcode,dmt.dm_id,rr.rr_actual_amount,
               rr.ep_key,rr.company_id||to_char(rr.rr_occurtime,'yyyymm')||rr.so_code
        from receipt_record rr,debit_manifest_temporary dmt
        where rr.dm_id = dmt.dm_temp_id
          and dmt.dm_temp_id = #{tempId,jdbcType=DECIMAL}

    </insert>

    <select id="countDebitEawb" resultType="java.lang.Integer"  >
        select
        count(0)
        from DEBIT_EAWB
    </select>

  <select id="selectBmsManifestAmount" resultType="com.sinoair.billing.domain.model.billing.BmsManifestDetail"
          parameterType="com.sinoair.billing.domain.model.billing.DebitEawb" >
    select
    de.sinotrans_id,  -- 外运号
    de.ep_key,
    sum(de.rr_actual_amount) as receiptAmount  --实收金额
    from debit_eawb de
    where de.dm_id = #{dmId,jdbcType=DECIMAL}
    <if test="epKey != null" >
      and de.ep_key = #{epKey,jdbcType=VARCHAR}
    </if>
    group by de.sinotrans_id,de.ep_key

  </select>

    <delete id="deleteAll">
        truncate table DEBIT_EAWB
    </delete>

    <!-- truncate table DEBIT_EAWB
    delete from DEBIT_EAWB
    -->
    <update id="truncateTable">
        truncate table DEBIT_EAWB
    </update>


</mapper>