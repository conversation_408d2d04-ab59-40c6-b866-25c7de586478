<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.DebitHandleTaskMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.DebitHandleTask" >
    <id column="DM_ID" property="dmId" jdbcType="DECIMAL" />
    <result column="SO_CODE" property="soCode" jdbcType="VARCHAR" />
    <result column="TASK_STATUS" property="taskStatus" jdbcType="VARCHAR" />
    <result column="EXECUTE_STATUS" property="executeStatus" jdbcType="VARCHAR" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="HANDLE_TIME" property="handleTime" jdbcType="TIMESTAMP" />
    <result column="EXECUTE_TIME" property="executeTime" jdbcType="TIMESTAMP" />
    <result column="TASK_REMARK" property="taskRemark" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    DM_ID, SO_CODE, TASK_STATUS, EXECUTE_STATUS, CREATE_TIME, HANDLE_TIME, EXECUTE_TIME, 
    TASK_REMARK
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from DEBIT_HANDLE_TASK
    where DM_ID = #{dmId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from DEBIT_HANDLE_TASK
    where DM_ID = #{dmId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.DebitHandleTask" >
    insert into DEBIT_HANDLE_TASK (DM_ID, SO_CODE, TASK_STATUS, 
      EXECUTE_STATUS, CREATE_TIME, HANDLE_TIME, 
      EXECUTE_TIME, TASK_REMARK)
    values (#{dmId,jdbcType=DECIMAL}, #{soCode,jdbcType=VARCHAR}, #{taskStatus,jdbcType=VARCHAR}, 
      #{executeStatus,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{handleTime,jdbcType=TIMESTAMP}, 
      #{executeTime,jdbcType=TIMESTAMP}, #{taskRemark,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.DebitHandleTask" >
    insert into DEBIT_HANDLE_TASK
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="dmId != null" >
        DM_ID,
      </if>
      <if test="soCode != null" >
        SO_CODE,
      </if>
      <if test="taskStatus != null" >
        TASK_STATUS,
      </if>
      <if test="executeStatus != null" >
        EXECUTE_STATUS,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
      <if test="handleTime != null" >
        HANDLE_TIME,
      </if>
      <if test="executeTime != null" >
        EXECUTE_TIME,
      </if>
      <if test="taskRemark != null" >
        TASK_REMARK,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="dmId != null" >
        #{dmId,jdbcType=DECIMAL},
      </if>
      <if test="soCode != null" >
        #{soCode,jdbcType=VARCHAR},
      </if>
      <if test="taskStatus != null" >
        #{taskStatus,jdbcType=VARCHAR},
      </if>
      <if test="executeStatus != null" >
        #{executeStatus,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="handleTime != null" >
        #{handleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="executeTime != null" >
        #{executeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taskRemark != null" >
        #{taskRemark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.DebitHandleTask" >
    update DEBIT_HANDLE_TASK
    <set >
      <if test="soCode != null" >
        SO_CODE = #{soCode,jdbcType=VARCHAR},
      </if>
      <if test="taskStatus != null" >
        TASK_STATUS = #{taskStatus,jdbcType=VARCHAR},
      </if>
      <if test="executeStatus != null" >
        EXECUTE_STATUS = #{executeStatus,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="handleTime != null" >
        HANDLE_TIME = #{handleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="executeTime != null" >
        EXECUTE_TIME = #{executeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taskRemark != null" >
        TASK_REMARK = #{taskRemark,jdbcType=VARCHAR},
      </if>
    </set>
    where DM_ID = #{dmId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.DebitHandleTask" >
    update DEBIT_HANDLE_TASK
    set SO_CODE = #{soCode,jdbcType=VARCHAR},
      TASK_STATUS = #{taskStatus,jdbcType=VARCHAR},
      EXECUTE_STATUS = #{executeStatus,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      HANDLE_TIME = #{handleTime,jdbcType=TIMESTAMP},
      EXECUTE_TIME = #{executeTime,jdbcType=TIMESTAMP},
      TASK_REMARK = #{taskRemark,jdbcType=VARCHAR}
    where DM_ID = #{dmId,jdbcType=DECIMAL}
  </update>

  <select id="listWaitRun" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from DEBIT_HANDLE_TASK
    where TASK_STATUS = 'NEW'
  </select>

  <select id="listWaitPending" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from DEBIT_HANDLE_TASK
    where TASK_STATUS = 'PENDING'
     and dm_id > 50000
  </select>

  <select id="countRun" resultType="java.lang.Integer">
    select
    count(0)
    from DEBIT_HANDLE_TASK
    where TASK_STATUS = 'RUNING'
  </select>

  <select id="countRelation" resultType="java.lang.Integer">
    select
    count(0)
    from DEBIT_HANDLE_TASK
    where TASK_STATUS = 'RELATIONING'
  </select>
</mapper>