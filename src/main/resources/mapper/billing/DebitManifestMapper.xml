<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.DebitManifestMapper">
    <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.DebitManifest">
        <id column="DM_ID" property="dmId" jdbcType="DECIMAL"/>
        <result column="COMPANY_ID" property="companyId" jdbcType="VARCHAR"/>
        <result column="SO_CODE" property="soCode" jdbcType="VARCHAR"/>
        <result column="CT_CODE" property="ctCode" jdbcType="VARCHAR"/>
        <result column="DM_CURRENCYRATE" property="dmCurrencyrate" jdbcType="DECIMAL"/>
        <result column="DM_TOTALRMB" property="dmTotalrmb" jdbcType="DECIMAL"/>
        <result column="DM_TOTALFC" property="dmTotalfc" jdbcType="DECIMAL"/>
        <result column="SO_TAX" property="soTax" jdbcType="DECIMAL"/>
        <result column="TAX_AMOUNT" property="taxAmount" jdbcType="DECIMAL"/>
        <result column="NOTAX_AMOUNT" property="notaxAmount" jdbcType="DECIMAL"/>
        <result column="TAX_AMOUNT_FC" property="taxAmountFc" jdbcType="DECIMAL"/>
        <result column="NOTAX_AMOUNT_FC" property="notaxAmountFc" jdbcType="DECIMAL"/>
        <result column="INVOICE_CODE" property="invoiceCode" jdbcType="VARCHAR"/>
        <result column="DM_USER_ID" property="dmUserId" jdbcType="DECIMAL"/>
        <result column="DM_STATUS" property="dmStatus" jdbcType="VARCHAR"/>
        <result column="DM_CREATE_TIME" property="dmCreateTime" jdbcType="TIMESTAMP"/>
        <result column="DM_HANDLE_TIME" property="dmHandleTime" jdbcType="TIMESTAMP"/>
        <result column="DM_DIRTY" property="dmDirty" jdbcType="VARCHAR"/>
        <result column="DM_DIRTY_TIME" property="dmDirtyTime" jdbcType="TIMESTAMP"/>
        <result column="DM_CODE" property="dmCode" jdbcType="VARCHAR"/>
        <result column="DM_PLAN_AMOUNT" property="dmPlanAmount" jdbcType="DECIMAL"/>
        <result column="DM_START_TIME" property="dmStartTime" jdbcType="TIMESTAMP"/>
        <result column="DM_END_TIME" property="dmEndTime" jdbcType="TIMESTAMP"/>
        <result column="DM_TOTAL_PIECES" property="dmTotalPieces" jdbcType="DECIMAL"/>
        <result column="DM_TOTAL_WEIGHT" property="dmTotalWeight" jdbcType="DECIMAL"/>
        <result column="DM_REMARK" property="dmRemark" jdbcType="VARCHAR"/>
        <result column="DM_TYPE" property="dmType" jdbcType="VARCHAR"/>
        <result column="DM_DIVIDE_STATUS" property="dmDivideStatus" jdbcType="VARCHAR" />
        <result column="DM_EMAIL_STATUS" property="dmEmailStatus" jdbcType="VARCHAR" />
        <result column="SO_MODE" property="soMode" jdbcType="VARCHAR" />
        <result column="EAWB_PRINTCODE" property="eawbPrintcode" jdbcType="VARCHAR" />
        <result column="EAWB_REFERENCE1" property="eawbReference1" jdbcType="VARCHAR" />
    </resultMap>
    <resultMap id="ResultReceiptVo" type="com.sinoair.billing.domain.vo.receipt.ReceiptVo">
        <result column="dmId" property="dmId"></result>
        <result column="dmCode" property="dmCode"></result>
        <result column="countNum" property="countNum"></result>
        <result column="sumRrPlanAmount" property="sumRrPlanAmount"></result>
        <result column="sumRactualAmount" property="sumRactualAmount"></result>
        <result column="sumEawbChargeableweight" property="sumEawbChargeableweight"></result>
    </resultMap>
    <sql id="Base_Column_List">
    DM_ID, COMPANY_ID, SO_CODE, CT_CODE, DM_CURRENCYRATE, DM_TOTALRMB, DM_TOTALFC, SO_TAX,
    TAX_AMOUNT, NOTAX_AMOUNT, TAX_AMOUNT_FC, NOTAX_AMOUNT_FC, INVOICE_CODE, DM_USER_ID,
    DM_STATUS, DM_CREATE_TIME, DM_HANDLE_TIME, DM_DIRTY, DM_DIRTY_TIME, DM_CODE, DM_PLAN_AMOUNT,
    DM_START_TIME, DM_END_TIME, DM_TOTAL_PIECES, DM_TOTAL_WEIGHT,DM_REMARK,DM_TYPE,
    DM_DIVIDE_STATUS,DM_EMAIL_STATUS
  </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select
        <include refid="Base_Column_List"/>
        from DEBIT_MANIFEST
        where DM_ID = #{dmId,jdbcType=DECIMAL}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from DEBIT_MANIFEST
    where DM_ID = #{dmId,jdbcType=DECIMAL}
  </delete>
    <!-- 序列 -->
    <sql id='TABLE_SEQUENCE'>SEQ_DEBIT_MANIFEST.NEXTVAL</sql>
    <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.DebitManifest">
        <selectKey keyProperty="dmId" resultType="int" order="BEFORE">
            select
            <include refid="TABLE_SEQUENCE"/>
            from dual
        </selectKey>
    insert into DEBIT_MANIFEST (DM_ID, COMPANY_ID, SO_CODE,
      CT_CODE, DM_CURRENCYRATE, DM_TOTALRMB,
      DM_TOTALFC, SO_TAX, TAX_AMOUNT,
      NOTAX_AMOUNT, TAX_AMOUNT_FC, NOTAX_AMOUNT_FC,
      INVOICE_CODE, DM_USER_ID, DM_STATUS,
      DM_CREATE_TIME, DM_HANDLE_TIME, DM_DIRTY,
      DM_DIRTY_TIME, DM_CODE, DM_PLAN_AMOUNT,
      DM_START_TIME, DM_END_TIME, DM_TOTAL_PIECES,
      DM_TOTAL_WEIGHT,DM_REMARK,DM_TYPE,
      DM_DIVIDE_STATUS,SO_MODE,EAWB_PRINTCODE,
      EAWB_REFERENCE1)
    values (#{dmId,jdbcType=DECIMAL}, #{companyId,jdbcType=VARCHAR}, #{soCode,jdbcType=VARCHAR},
      #{ctCode,jdbcType=VARCHAR}, #{dmCurrencyrate,jdbcType=DECIMAL}, #{dmTotalrmb,jdbcType=DECIMAL},
      #{dmTotalfc,jdbcType=DECIMAL}, #{soTax,jdbcType=DECIMAL}, #{taxAmount,jdbcType=DECIMAL},
      #{notaxAmount,jdbcType=DECIMAL}, #{taxAmountFc,jdbcType=DECIMAL}, #{notaxAmountFc,jdbcType=DECIMAL},
      #{invoiceCode,jdbcType=VARCHAR}, #{dmUserId,jdbcType=DECIMAL}, #{dmStatus,jdbcType=VARCHAR},
      #{dmCreateTime,jdbcType=TIMESTAMP}, #{dmHandleTime,jdbcType=TIMESTAMP}, #{dmDirty,jdbcType=VARCHAR},
      #{dmDirtyTime,jdbcType=TIMESTAMP}, #{dmCode,jdbcType=VARCHAR}, #{dmPlanAmount,jdbcType=DECIMAL},
      #{dmStartTime,jdbcType=TIMESTAMP}, #{dmEndTime,jdbcType=TIMESTAMP}, #{dmTotalPieces,jdbcType=DECIMAL},
      #{dmTotalWeight,jdbcType=DECIMAL},#{dmRemark,jdbcType=VARCHAR},#{dmType,jdbcType=VARCHAR},
      #{dmDivideStatus,jdbcType=VARCHAR},#{soMode,jdbcType=VARCHAR},#{eawbPrintcode,jdbcType=VARCHAR},
      #{eawbReference1,jdbcType=VARCHAR})
  </insert>
    <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.DebitManifest">
        <selectKey keyProperty="dmId" resultType="int" order="BEFORE">
            select
            <include refid="TABLE_SEQUENCE"/>
            from dual
        </selectKey>
        insert into DEBIT_MANIFEST
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dmId != null">
                DM_ID,
            </if>
            <if test="companyId != null">
                COMPANY_ID,
            </if>
            <if test="soCode != null">
                SO_CODE,
            </if>
            <if test="ctCode != null">
                CT_CODE,
            </if>
            <if test="dmCurrencyrate != null">
                DM_CURRENCYRATE,
            </if>
            <if test="dmTotalrmb != null">
                DM_TOTALRMB,
            </if>
            <if test="dmTotalfc != null">
                DM_TOTALFC,
            </if>
            <if test="soTax != null">
                SO_TAX,
            </if>
            <if test="taxAmount != null">
                TAX_AMOUNT,
            </if>
            <if test="notaxAmount != null">
                NOTAX_AMOUNT,
            </if>
            <if test="taxAmountFc != null">
                TAX_AMOUNT_FC,
            </if>
            <if test="notaxAmountFc != null">
                NOTAX_AMOUNT_FC,
            </if>
            <if test="invoiceCode != null">
                INVOICE_CODE,
            </if>
            <if test="dmUserId != null">
                DM_USER_ID,
            </if>
            <if test="dmStatus != null">
                DM_STATUS,
            </if>
            <if test="dmCreateTime != null">
                DM_CREATE_TIME,
            </if>
            <if test="dmHandleTime != null">
                DM_HANDLE_TIME,
            </if>
            <if test="dmDirty != null">
                DM_DIRTY,
            </if>
            <if test="dmDirtyTime != null">
                DM_DIRTY_TIME,
            </if>
            <if test="dmCode != null">
                DM_CODE,
            </if>
            <if test="dmPlanAmount != null">
                DM_PLAN_AMOUNT,
            </if>
            <if test="dmStartTime != null">
                DM_START_TIME,
            </if>
            <if test="dmEndTime != null">
                DM_END_TIME,
            </if>
            <if test="dmTotalPieces != null">
                DM_TOTAL_PIECES,
            </if>
            <if test="dmTotalWeight != null">
                DM_TOTAL_WEIGHT,
            </if>
            <if test="dmRemark != null">
                DM_REMARK,
            </if>
            <if test="dmType != null">
                DM_TYPE,
            </if>
            <if test="dmDivideStatus != null" >
                DM_DIVIDE_STATUS,
            </if>
            <if test="soMode != null" >
                SO_MODE,
            </if>
            <if test="eawbServicetypeOriginal != null" >
                EAWB_SERVICETYPE_ORIGINAL,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dmId != null">
                #{dmId,jdbcType=DECIMAL},
            </if>
            <if test="companyId != null">
                #{companyId,jdbcType=VARCHAR},
            </if>
            <if test="soCode != null">
                #{soCode,jdbcType=VARCHAR},
            </if>
            <if test="ctCode != null">
                #{ctCode,jdbcType=VARCHAR},
            </if>
            <if test="dmCurrencyrate != null">
                #{dmCurrencyrate,jdbcType=DECIMAL},
            </if>
            <if test="dmTotalrmb != null">
                #{dmTotalrmb,jdbcType=DECIMAL},
            </if>
            <if test="dmTotalfc != null">
                #{dmTotalfc,jdbcType=DECIMAL},
            </if>
            <if test="soTax != null">
                #{soTax,jdbcType=DECIMAL},
            </if>
            <if test="taxAmount != null">
                #{taxAmount,jdbcType=DECIMAL},
            </if>
            <if test="notaxAmount != null">
                #{notaxAmount,jdbcType=DECIMAL},
            </if>
            <if test="taxAmountFc != null">
                #{taxAmountFc,jdbcType=DECIMAL},
            </if>
            <if test="notaxAmountFc != null">
                #{notaxAmountFc,jdbcType=DECIMAL},
            </if>
            <if test="invoiceCode != null">
                #{invoiceCode,jdbcType=VARCHAR},
            </if>
            <if test="dmUserId != null">
                #{dmUserId,jdbcType=DECIMAL},
            </if>
            <if test="dmStatus != null">
                #{dmStatus,jdbcType=VARCHAR},
            </if>
            <if test="dmCreateTime != null">
                #{dmCreateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="dmHandleTime != null">
                #{dmHandleTime,jdbcType=TIMESTAMP},
            </if>
            <if test="dmDirty != null">
                #{dmDirty,jdbcType=VARCHAR},
            </if>
            <if test="dmDirtyTime != null">
                #{dmDirtyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="dmCode != null">
                #{dmCode,jdbcType=VARCHAR},
            </if>
            <if test="dmPlanAmount != null">
                #{dmPlanAmount,jdbcType=DECIMAL},
            </if>
            <if test="dmStartTime != null">
                #{dmStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="dmEndTime != null">
                #{dmEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="dmTotalPieces != null">
                #{dmTotalPieces,jdbcType=DECIMAL},
            </if>
            <if test="dmTotalWeight != null">
                #{dmTotalWeight,jdbcType=DECIMAL},
            </if>
            <if test="dmRemark != null">
                #{dmRemark,jdbcType=VARCHAR},
            </if>
            <if test="dmType != null">
                #{dmType,jdbcType=VARCHAR},
            </if>
            <if test="dmDivideStatus != null" >
                #{dmDivideStatus,jdbcType=VARCHAR},
            </if>
            <if test="soMode != null" >
                #{soMode,jdbcType=VARCHAR},
            </if>
            <if test="eawbServicetypeOriginal != null" >
                #{eawbServicetypeOriginal,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.DebitManifest">
        update DEBIT_MANIFEST
        <set>
            <if test="companyId != null">
                COMPANY_ID = #{companyId,jdbcType=VARCHAR},
            </if>
            <if test="soCode != null">
                SO_CODE = #{soCode,jdbcType=VARCHAR},
            </if>
            <if test="ctCode != null">
                CT_CODE = #{ctCode,jdbcType=VARCHAR},
            </if>
            <if test="dmCurrencyrate != null">
                DM_CURRENCYRATE = #{dmCurrencyrate,jdbcType=DECIMAL},
            </if>
            <if test="dmTotalrmb != null">
                DM_TOTALRMB = #{dmTotalrmb,jdbcType=DECIMAL},
            </if>
            <if test="dmTotalfc != null">
                DM_TOTALFC = #{dmTotalfc,jdbcType=DECIMAL},
            </if>
            <if test="soTax != null">
                SO_TAX = #{soTax,jdbcType=DECIMAL},
            </if>
            <if test="taxAmount != null">
                TAX_AMOUNT = #{taxAmount,jdbcType=DECIMAL},
            </if>
            <if test="notaxAmount != null">
                NOTAX_AMOUNT = #{notaxAmount,jdbcType=DECIMAL},
            </if>
            <if test="taxAmountFc != null">
                TAX_AMOUNT_FC = #{taxAmountFc,jdbcType=DECIMAL},
            </if>
            <if test="notaxAmountFc != null">
                NOTAX_AMOUNT_FC = #{notaxAmountFc,jdbcType=DECIMAL},
            </if>
            <if test="invoiceCode != null">
                INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR},
            </if>
            <if test="dmUserId != null">
                DM_USER_ID = #{dmUserId,jdbcType=DECIMAL},
            </if>
            <if test="dmStatus != null">
                DM_STATUS = #{dmStatus,jdbcType=VARCHAR},
            </if>
            <if test="dmCreateTime != null">
                DM_CREATE_TIME = #{dmCreateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="dmHandleTime != null">
                DM_HANDLE_TIME = #{dmHandleTime,jdbcType=TIMESTAMP},
            </if>
            <if test="dmDirty != null">
                DM_DIRTY = #{dmDirty,jdbcType=VARCHAR},
            </if>
            <if test="dmDirtyTime != null">
                DM_DIRTY_TIME = #{dmDirtyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="dmCode != null">
                DM_CODE = #{dmCode,jdbcType=VARCHAR},
            </if>
            <if test="dmPlanAmount != null">
                DM_PLAN_AMOUNT = #{dmPlanAmount,jdbcType=DECIMAL},
            </if>
            <if test="dmStartTime != null">
                DM_START_TIME = #{dmStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="dmEndTime != null">
                DM_END_TIME = #{dmEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="dmTotalPieces != null">
                DM_TOTAL_PIECES = #{dmTotalPieces,jdbcType=DECIMAL},
            </if>
            <if test="dmTotalWeight != null">
                DM_TOTAL_WEIGHT = #{dmTotalWeight,jdbcType=DECIMAL},
            </if>
            <if test="dmRemark != null">
                DM_REMARK = #{dmRemark,jdbcType=VARCHAR},
            </if>
            <if test="dmType != null">
                DM_TYPE = #{dmType,jdbcType=VARCHAR},
            </if>
            <if test="dmDivideStatus != null" >
                DM_DIVIDE_STATUS = #{dmDivideStatus,jdbcType=VARCHAR},
            </if>
            <if test="dmEmailStatus != null" >
                DM_EMAIL_STATUS = #{dmEmailStatus,jdbcType=VARCHAR},
            </if>
        </set>
        where DM_ID = #{dmId,jdbcType=DECIMAL}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.DebitManifest">
    update DEBIT_MANIFEST
    set COMPANY_ID = #{companyId,jdbcType=VARCHAR},
      SO_CODE = #{soCode,jdbcType=VARCHAR},
      CT_CODE = #{ctCode,jdbcType=VARCHAR},
      DM_CURRENCYRATE = #{dmCurrencyrate,jdbcType=DECIMAL},
      DM_TOTALRMB = #{dmTotalrmb,jdbcType=DECIMAL},
      DM_TOTALFC = #{dmTotalfc,jdbcType=DECIMAL},
      SO_TAX = #{soTax,jdbcType=DECIMAL},
      TAX_AMOUNT = #{taxAmount,jdbcType=DECIMAL},
      NOTAX_AMOUNT = #{notaxAmount,jdbcType=DECIMAL},
      TAX_AMOUNT_FC = #{taxAmountFc,jdbcType=DECIMAL},
      NOTAX_AMOUNT_FC = #{notaxAmountFc,jdbcType=DECIMAL},
      INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR},
      DM_USER_ID = #{dmUserId,jdbcType=DECIMAL},
      DM_STATUS = #{dmStatus,jdbcType=VARCHAR},
      DM_CREATE_TIME = #{dmCreateTime,jdbcType=TIMESTAMP},
      DM_HANDLE_TIME = #{dmHandleTime,jdbcType=TIMESTAMP},
      DM_DIRTY = #{dmDirty,jdbcType=VARCHAR},
      DM_DIRTY_TIME = #{dmDirtyTime,jdbcType=TIMESTAMP},
      DM_CODE = #{dmCode,jdbcType=VARCHAR},
      DM_PLAN_AMOUNT = #{dmPlanAmount,jdbcType=DECIMAL},
      DM_START_TIME = #{dmStartTime,jdbcType=TIMESTAMP},
      DM_END_TIME = #{dmEndTime,jdbcType=TIMESTAMP},
      DM_TOTAL_PIECES = #{dmTotalPieces,jdbcType=DECIMAL},
      DM_TOTAL_WEIGHT = #{dmTotalWeight,jdbcType=DECIMAL},
      DM_REMARK = #{dmRemark,jdbcType=VARCHAR},
      DM_TYPE = #{dmType,jdbcType=VARCHAR},
      DM_DIVIDE_STATUS = #{dmDivideStatus,jdbcType=VARCHAR}
    where DM_ID = #{dmId,jdbcType=DECIMAL}
  </update>


    <update id="updateTotaDebitManifestBydmId" parameterType="com.sinoair.billing.domain.model.billing.DebitManifest">
        update debit_manifest dm
        <set>
            <if test="dmUserId!=null">
                dm.dm_user_id = #{dmUserId},
            </if>
            <if test="companyId!=null">
                dm.company_id = #{companyId},
            </if>
            <if test="dmPlanAmount!=null">
                dm.dm_plan_amount = #{dmPlanAmount},
            </if>
            <if test="dmTotalfc!=null">
                dm.dm_totalfc = #{dmTotalfc},
            </if>
            <if test="dmTotalPieces!=null">
                dm.dm_total_pieces = #{dmTotalPieces},
            </if>
            <if test="dmTotalWeight!=null">
                dm.dm_total_weight = #{dmTotalWeight},
            </if>
            <if test="dmHandleTime!=null">
                dm.dm_handle_time = #{dmHandleTime}
            </if>
        </set>
        where dm.dm_id = #{dmId}
    </update>

    <update id="updateNullTotaDebitManifestBydmId" parameterType="com.sinoair.billing.domain.model.billing.DebitManifest">
        update debit_manifest dm
        set
        dm.dm_user_id = #{dmUserId},
        dm.company_id = #{companyId},
        dm.dm_plan_amount = null,
        dm.dm_totalfc =null,
        dm.dm_total_pieces = null,
        dm.dm_total_weight = null,
        dm.dm_handle_time = #{dmHandleTime}
        where dm.dm_id = #{dmId}
    </update>

    <select id="getSeqDebitManifest" resultType="java.lang.Integer">
    select seq_debit_manifest.nextval from dual
  </select>

    <select id="getDebitManifestListMap" resultType="java.util.HashMap"
            parameterType="com.sinoair.billing.domain.vo.FilterParam">
        select dm.dm_id as "dmId",
        dm.dm_code as "dmCode",
        dm.so_code as "soCode",
        stt.so_name as "soName",
        dm.dm_status as "dmStatus",
        dm.dm_total_pieces as "dmTotalPieces",
        dm.dm_total_weight as "dmTotalWeight",
        to_char(dm.dm_start_time,'yyyy/MM/dd') as "dmStartTime",
        to_char(dm.dm_end_time,'yyyy/MM/dd')   as "dmEndTime",
        ct.ct_name as "ctCode",
        dm.ct_code as "ctCodeEn",
        dm.dm_plan_amount as "dmPlanAmount",
        dm.dm_totalfc as "dmTotalfc",
        dm.dm_type as "dmType",
        dm.dm_remark as "dmRemark"
        from debit_manifest dm, currencytype ct, settlementobject stt
        where dm.so_code = stt.so_code
        and dm.ct_code = ct.ct_code
        and 1 = 1
        and dm.dm_status!='OFF'
        and dm.company_id = #{companyId}
        <if test="code!=null and code!=''">
            and dm.dm_code = #{code}
        </if>
        <if test="status!=null and status!='ALL'">
            and dm.dm_status = #{status}
        </if>
        <if test="soCode!=null and soCode!=''">
            and dm.so_code = #{soCode}
        </if>
        <if test="starttime!=null and endtime!=null and starttime!='' and endtime!=''">
            and (to_char(dm.dm_start_time, 'yyyy-mm-dd') >= #{starttime} and
            to_char(dm.dm_start_time, 'yyyy-mm-dd') &lt;= #{endtime})
        </if>
        <if test='soCate == "P" '>
            AND (stt.SO_CATE = #{soCate} or stt.SO_CATE IS NULL)
        </if>
        <if test='dmDivideStatus == "N" '>
            and (dm.DM_DIVIDE_STATUS = #{dmDivideStatus} or dm.DM_DIVIDE_STATUS IS NULL)
        </if>
        <if test='dmDivideStatus == "Y" '>
            and dm.DM_DIVIDE_STATUS = #{dmDivideStatus}
        </if>
        order by dm.dm_start_time desc,dm.dm_id
    </select>

    <select id="getDebitManifestBydmCode" parameterType="java.lang.String"
            resultType="com.sinoair.billing.domain.model.billing.DebitManifest">
      select dm.*,stt.so_name soName from debit_manifest dm,settlementobject stt
	  where dm.so_code=stt.so_code
	  and dm.dm_code=#{dmCode}
  </select>

    <update id="updatedmStatusdBydmCode" parameterType="com.sinoair.billing.domain.model.billing.DebitManifest">
        update debit_manifest dm
        <set>
            <if test="dmStatus!=null and dmStatus!=''">
                dm.dm_status=#{dmStatus},
            </if>
            <if test="dmUserId!=null and dmUserId!=''">
                dm.dm_user_id=#{dmUserId},
            </if>
            <if test="dmHandleTime!=null and dmHandleTime!=''">
                dm.dm_handle_time=#{dmHandleTime},
            </if>
            <if test="soTax!=null and soTax!=''">
                dm.so_tax=#{soTax},
            </if>
            <if test="dmCurrencyrate!=null and dmCurrencyrate!=''">
                dm.dm_currencyrate=#{dmCurrencyrate},
            </if>
            <if test="ctCode!=null and ctCode!=''">
                dm.ct_code=#{ctCode},
            </if>
            <if test="dmPlanAmount!=null and dmPlanAmount!=''">
                dm.dm_plan_amount=#{dmPlanAmount},
            </if>
            <if test="dmTotalrmb!=null and dmTotalrmb!=''">
                dm.dm_totalrmb=#{dmTotalrmb},
            </if>
            <if test="dmRemark!=null and dmRemark!=''">
                dm.dm_remark=#{dmRemark},
            </if>
            <if test="dmDivideStatus != null" >
                dm.DM_DIVIDE_STATUS = #{dmDivideStatus},
            </if>
        </set>
        where dm.dm_code=#{dmCode}
    </update>


    <update id="updateOffdBydmCode" parameterType="com.sinoair.billing.domain.model.billing.DebitManifest">
   update receipt_record rd
    set rd.dm_id = null,
         rd.rr_user_id    = #{dmUserId},
        rd.rr_handletime = #{dmHandleTime}
  where rd.dm_id in (select dm.dm_id
                       from debit_manifest dm
                      where dm.dm_code = #{dmCode})
  </update>

    <select id="getDmIdByDmCode" parameterType="java.lang.String"
            resultType="com.sinoair.billing.domain.model.billing.DebitManifest">
        select * from debit_manifest dm where dm.dm_code=#{dmCode}
    </select>

    <select id="getToTalDebitManifestByDmCode" parameterType="java.lang.String"
            resultMap="ResultReceiptVo">
            select dmId,
                   dmCode,
                   sum(eawb_printcode) countNum,
                   sum(chargeableweight) sumEawbChargeableweight,
                   sum(sumRrPlanAmount) sumRrPlanAmount,
                   sum(sumRactualAmount) sumRactualAmount
              from (select t.dm_id dmId,
                           t.dm_code dmCode,
                           count(distinct t.eawb_printcode) eawb_printcode,
                           sum(t.eawb_chargeableweight) chargeableweight,
                           0 sumRrPlanAmount,
                           0 sumRactualAmount
                      from (select distinct nvl(rd.eawb_printcode,rd.mawb_code) eawb_printcode,
                                            rd.eawb_chargeableweight,
                                            dm.dm_id,
                                            dm.dm_code
                              from receipt_record rd, debit_manifest dm
                             where rd.dm_id = dm.dm_id
                             group by rd.eawb_printcode,
                                      rd.mawb_code,
                                      rd.eawb_chargeableweight,
                                      dm.dm_id,
                                      dm.dm_code) t
                     group by t.dm_id, t.dm_code
                    union all
                    select dm.dm_id dmId,
                           dm.dm_code dmCode,
                           0,
                           0,
                           sum(rd.rr_plan_amount) sumRrPlanAmount,
                           sum(rr_actual_amount) sumRactualAmount
                      from receipt_record rd, debit_manifest dm
                     where rd.dm_id = dm.dm_id
                     group by dm.dm_id, dm.dm_code) tr
             where dmCode=#{dmCode}
             group by dmId, dmCode
    </select>

    <select id="selectCsvInfo" parameterType="java.util.HashMap"
            resultType="com.sinoair.billing.domain.vo.receipt.ReceiptCsvVo">
        select rd.eawb_reference1 as eawbReference1,
            rd.eawb_reference2 as eawbReference2,
            rd.rr_name as rrName,
            rd.rr_actual_amount as rrActualAmount,
            ep.ep_value as epValue,
            rd.eawb_chargeableweight as eawbChargeableweight,
            rd.eawb_printcode as eawbPrintcode,
            rd.pr_id,
            to_char(rd.rr_occurtime,'yyyy/mm/dd hh24:mi:ss') as rrOccurtime,
            rd.mawb_code as mawbCode,
            eawb.eawb_destcountry as eawbDestcountry,
            eawb.eawb_tracking_no as eawbTrackingNo,
            #{soName} as soName
            from debit_manifest dm
            inner join receipt_record rd on dm.dm_id = rd.dm_id
            left join expressairwaybill eawb on rd.eawb_printcode =
            eawb.eawb_printcode
            left join express_property ep on rd.ep_key = ep.ep_key
        where dm.dm_code = #{dmCode}
        <if test="pdSyscodes!=null and pdSyscodes!=''">
            and rd.pd_syscode in
            <foreach collection="pdSyscodes" item="pdSyscode" open="(" separator="," close=")">
                #{pdSyscode}
            </foreach>
        </if>
        <if test="soCode!=null and soCode!=''">
            and rd.so_code = #{soCode}
        </if>
        <if test="startRrOccurtime!=null and startRrOccurtime!='' and endRrOccurtime!=null and endRrOccurtime!=''">
            and
            (
                rd.rr_occurtime >=
                to_date(#{startRrOccurtime}, 'yyyy-mm-dd hh24:mi:ss') and
                rd.rr_occurtime &lt;=
                to_date(#{endRrOccurtime}, 'yyyy-mm-dd hh24:mi:ss')
            )
        </if>
        order by rd.rr_occurtime asc
    </select>
    <select id="selectSoTypeByDmid" parameterType="java.lang.Integer"
            resultType="com.sinoair.billing.domain.model.billing.SettlementObject">
        select so.so_type,so.so_syscode,so.so_code,so.tax_rate from SETTLEMENTOBJECT so where so.SO_CODE =
        (SELECT so_code from DEBIT_MANIFEST where DM_ID=#{dmId})
    </select>

    <delete id="deleteReceiptMinusByDmCode" parameterType="java.lang.String">
        delete from receipt_estimate_minus
        where DM_ID in (select dm.dm_id
                       from debit_manifest dm
                      where dm.dm_code = #{dmCode})
    </delete>

    <!---  -->
    <select id="getSumDmTotalfcByDmCodes" parameterType="java.util.HashMap"
            resultType="java.util.HashMap">
        select
          sum(t.dm_totalfc) as "dmTotalfc",
          sum(t.dm_total_pieces) as "dmTotalPieces",
          sum(t.dm_total_weight) as "dmTotalWeight"
        from DEBIT_MANIFEST t
        where t.dm_code in
        <foreach collection="dmCodes" item="dmCode" open="(" separator="," close=")">
            #{dmCode}
        </foreach>
    </select>

    <update id="updateDividedByDmCodes" parameterType="java.util.HashMap">
        update DEBIT_MANIFEST d
        set d.DM_DIVIDE_STATUS = 'Y'
        where d.dm_id in (select dm.dm_id
        from debit_manifest dm
        where dm.dm_code in
        <foreach collection="dmCodes" item="dmCode" open="(" separator="," close=")">
            #{dmCode}
        </foreach>
        )
    </update>

    <select id="getToTalDebitManifestByDmId"  resultMap="ResultReceiptVo" parameterType="java.lang.Integer">

        select d.dm_id as dmId,
        d.dm_code as dmCode,
        count(distinct r.eawb_printcode) as countNum,
        sum(r.eawb_chargeableweight) as sumEawbChargeableweight,
        sum(r.rr_plan_amount) as sumRrPlanAmount,
        sum(r.rr_actual_amount) as sumRactualAmount
        from receipt_record r, debit_manifest d
        where r.dm_id = d.dm_id
        and r.dm_id=#{dmId}
        and r.eawb_printcode is not null
        and (
        r.rr_occurtime >= (select dm_start_time from debit_manifest where dm_id = #{dmId}) and
        r.rr_occurtime &lt;= (select dm_end_time from debit_manifest where dm_id = #{dmId})
        )
        group by d.dm_id, d.dm_code
        union all
        select d.dm_id,
        d.dm_code,
        count(distinct r.mawb_code) as countNum,
        sum(r.eawb_chargeableweight) as sumEawbChargeableweight,
        sum(r.rr_plan_amount) as sumRrPlanAmount,
        sum(r.rr_actual_amount) as sumRactualAmount
        from receipt_record r, debit_manifest d
        where r.dm_id = d.dm_id
        and r.dm_id=#{dmId}
        and r.eawb_printcode is null
        and (
        r.rr_occurtime >= (select dm_start_time from debit_manifest where dm_id = #{dmId}) and
        r.rr_occurtime &lt;= (select dm_end_time from debit_manifest where dm_id = #{dmId})
        )
        group by d.dm_id, d.dm_code

    </select>

    <select id="getToTalEawbByDmId"  resultMap="ResultReceiptVo" parameterType="java.lang.Integer">
        select r.dm_id as dmId,
        count(distinct r.eawb_printcode) as countNum,
        sum(r.eawb_chargeableweight) as sumEawbChargeableweight,
        sum(r.rr_plan_amount) as sumRrPlanAmount,
        sum(r.rr_actual_amount) as sumRactualAmount
        from receipt_record r
        where r.dm_id=#{dmId}
        and eawb_printcode is not null
        group by r.dm_id
    </select>

    <select id="getToTalMawbByDmId"  resultMap="ResultReceiptVo" parameterType="java.lang.Integer">

        select r.dm_id as dmId,
        count(distinct r.mawb_code) as countNum,
        sum(r.eawb_chargeableweight) as sumEawbChargeableweight,
        sum(r.rr_plan_amount) as sumRrPlanAmount,
        sum(r.rr_actual_amount) as sumRactualAmount
        from receipt_record r
        where r.dm_id=#{dmId}
        and eawb_printcode is null
        group by r.dm_id

    </select>

    <update id="updateEmailStatusByDmIds" parameterType="java.util.List">
        update DEBIT_MANIFEST d
        set d.DM_EMAIL_STATUS = 'Y'
        where d.dm_id in
        <foreach collection="dmIds" item="dmId" open="(" separator="," close=")">
            #{dmId}
        </foreach>

    </update>

    <select id="selectBillEmail" parameterType="java.util.HashMap"
            resultType="java.util.HashMap">
       select tmp.*,to_char(tmp.dm_start_time,'yyyymm') as billMonth from
       (select
        t.dm_start_time ,t.so_code,
        (select count(0) from DEBIT_MANIFEST m1 where m1.dm_start_time = t.dm_start_time and m1.so_code = t.so_code
          and m1.dm_status != 'OFF') as billCount,
        (select count(0) from DEBIT_MANIFEST m2 where m2.dm_start_time = t.dm_start_time and m2.so_code = t.so_code
          and m2.dm_status = 'CONFIRM') as confirmCount
        from DEBIT_MANIFEST t
        where 1=1
          and t.so_code = #{soCode}
          and t.dm_id in
        <foreach collection="dmIds" item="dmId" open="(" separator="," close=")">
            #{dmId}
        </foreach>
          group by t.dm_start_time ,t.so_code) tmp

    </select>

    <select id="listEmailStatus" resultMap="BaseResultMap" parameterType="java.util.HashMap">
        select
        <include refid="Base_Column_List"/>
        from DEBIT_MANIFEST
        where DM_EMAIL_STATUS = 'N'
         and dm_status = 'CONFIRM'
         and so_code = #{soCode}
    </select>

    <select id="getBmsManifestAmount" resultType="com.sinoair.billing.domain.model.billing.BmsManifestDetail">
        select e.sinotrans_id,e.eawb_so_code as so_code,e.eawb_servicetype_original as ep_key,dm.dm_id,sum(rr.rr_actual_amount) as receipt_amount,min(e.sac_id) as sac_id,
        ( select ct_sign from currencytype where ct_code = dm.ct_code) as ct_sign,to_char(min(RR_OCCURTIME),'yyyymmdd') as dm_month
        from debit_manifest dm,receipt_record rr,expressairwaybill e
        where dm.dm_id = rr.dm_id and rr.eawb_printcode = e.eawb_printcode
        and dm.dm_id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by e.sinotrans_id,e.eawb_so_code,e.eawb_servicetype_original,dm.dm_id,dm.ct_code

    </select>

    <select id="getBmsDetailBySo" resultType="com.sinoair.billing.domain.model.billing.BmsManifestDetail">
        select e.sinotrans_id,e.eawb_so_code as so_code,e.eawb_servicetype_original as ep_key,sum(rr.rr_actual_amount) as receipt_amount,
        min(rr.company_id) as sac_id,
        ( select ct_sign from currencytype where ct_code = rr.ct_code) as ct_sign,to_char(rr.RR_OCCURTIME,'yyyymmdd') as dm_month
        from receipt_record rr,expressairwaybill e
        where  rr.eawb_printcode = e.eawb_printcode
        and rr.bmd_syscode is null
        and rr.rr_type = '1'
        and rr.rr_status='ON'
        and e.eawb_type='B2C'
        and rr.so_code=#{soCode}
        and rr.rr_occurtime &lt; TRUNC(SYSDATE)
        and rr.rr_occurtime &gt;= sysdate-30
        group by e.sinotrans_id,e.eawb_so_code,e.eawb_servicetype_original,rr.ct_code,to_char(rr.RR_OCCURTIME,'yyyymmdd'),rr.dm_id
    </select>
    <select id="getBmsDetailAll" resultType="com.sinoair.billing.domain.model.billing.BmsManifestDetail">
        select null as eawb_printcode,e.sinotrans_id,rr.so_code as so_code,e.eawb_servicetype_original as ep_key,sum(rr.rr_actual_amount) as receipt_amount,
        min(rr.company_id) as sac_id,rr.ct_code,
        ( select ct_sign from currencytype where ct_code = rr.ct_code) as ct_sign,to_char(rr.RR_OCCURTIME,'yyyymmdd') as dm_month
                ,rr.dm_id
        from receipt_record rr,expressairwaybill e
        where  rr.eawb_printcode = e.eawb_printcode
        and rr.bmd_syscode is null
        and rr.rr_status='ON'
        and e.eawb_type='B2C'
        and e.eawb_so_code not in ('ZSU1000001058','ZSU1000001059')
        and rr.rr_handletime &lt; TRUNC(SYSDATE)
        and rr.rr_handletime &gt;= sysdate-30
        and e.SINOTRANS_ID is not null
        group by e.sinotrans_id,rr.so_code,e.eawb_servicetype_original,rr.ct_code,to_char(rr.RR_OCCURTIME,'yyyymmdd'),rr.dm_id
        union all
        select e.eawb_printcode,e.sinotrans_id,rr.so_code as so_code,e.eawb_servicetype_original as ep_key,sum(rr.rr_actual_amount) as receipt_amount,
               min(rr.company_id) as sac_id,rr.ct_code,
               ( select ct_sign from currencytype where ct_code = rr.ct_code) as ct_sign,to_char(rr.RR_OCCURTIME,'yyyymmdd') as dm_month
                ,rr.dm_id
        from receipt_record rr,expressairwaybill e
        where  rr.eawb_printcode = e.eawb_printcode
          and rr.bmd_syscode is null
          and rr.rr_status='ON'
          and e.eawb_type='B2C'
          and e.eawb_so_code in ('ZSU1000001058','ZSU1000001059')
          and e.SINOTRANS_ID is not null
        and rr.rr_handletime &lt; TRUNC(SYSDATE)
        and rr.rr_handletime &gt;= sysdate-30
        group by e.sinotrans_id,rr.so_code,e.eawb_servicetype_original,rr.ct_code,to_char(rr.RR_OCCURTIME,'yyyymmdd'),e.eawb_printcode,rr.dm_id
    </select>

    <insert id="insertBatch" parameterType="java.util.ArrayList">
        insert into DEBIT_MANIFEST eawb (DM_ID,
        COMPANY_ID, SO_CODE, CT_CODE, DM_CURRENCYRATE, DM_TOTALRMB, DM_TOTALFC, SO_TAX,
        TAX_AMOUNT, NOTAX_AMOUNT, TAX_AMOUNT_FC, NOTAX_AMOUNT_FC, INVOICE_CODE, DM_USER_ID,
        DM_STATUS, DM_CREATE_TIME, DM_HANDLE_TIME, DM_DIRTY, DM_DIRTY_TIME, DM_CODE, DM_PLAN_AMOUNT,
        DM_START_TIME, DM_END_TIME, DM_TOTAL_PIECES, DM_TOTAL_WEIGHT,DM_REMARK,DM_TYPE,
        DM_DIVIDE_STATUS,DM_EMAIL_STATUS
        )
        select SEQ_DEBIT_MANIFEST.NEXTVAL,cd.* from(
        <foreach collection="list" item="item" index="index" separator="UNION ALL">
            (select
            #{item.companyId,jdbcType=VARCHAR},
            #{item.soCode,jdbcType=VARCHAR},
            #{item.ctCode,jdbcType=VARCHAR},
            #{item.dmCurrencyrate,jdbcType=DECIMAL},
            #{item.dmTotalrmb,jdbcType=DECIMAL},
            #{item.dmTotalfc,jdbcType=DECIMAL},
            #{item.soTax,jdbcType=DECIMAL},
            #{item.taxAmount,jdbcType=DECIMAL},
            #{item.notaxAmount,jdbcType=DECIMAL},
            #{item.taxAmountFc,jdbcType=DECIMAL},
            #{item.notaxAmountFc,jdbcType=DECIMAL},
            #{item.invoiceCode,jdbcType=VARCHAR},
            #{item.dmUserId,jdbcType=DECIMAL},
            #{item.dmStatus,jdbcType=VARCHAR},
            #{item.dmCreateTime,jdbcType=TIMESTAMP},
            #{item.dmHandleTime,jdbcType=TIMESTAMP},
            #{item.dmDirty,jdbcType=VARCHAR},
            #{item.dmDirtyTime,jdbcType=TIMESTAMP},
            #{item.dmCode,jdbcType=VARCHAR},
            #{item.dmPlanAmount,jdbcType=DECIMAL},
            #{item.dmStartTime,jdbcType=TIMESTAMP},
            #{item.dmEndTime,jdbcType=TIMESTAMP},
            #{item.dmTotalPieces,jdbcType=DECIMAL},
            #{item.dmTotalWeight,jdbcType=DECIMAL},
            #{item.dmRemark,jdbcType=VARCHAR},
            #{item.dmType,jdbcType=VARCHAR},
            #{item.dmDivideStatus,jdbcType=VARCHAR},
            #{item.dmEmailStatus,jdbcType=VARCHAR}
            from dual)
        </foreach>) cd
    </insert>
</mapper>
