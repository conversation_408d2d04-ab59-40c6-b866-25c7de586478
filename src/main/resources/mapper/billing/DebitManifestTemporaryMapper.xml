<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.DebitManifestTemporaryMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.DebitManifestTemporary" >
    <id column="DM_TEMP_ID" property="dmTempId" jdbcType="DECIMAL" />
    <result column="DM_ID" property="dmId" jdbcType="DECIMAL" />
    <result column="COMPANY_ID" property="companyId" jdbcType="VARCHAR" />
    <result column="SO_CODE" property="soCode" jdbcType="VARCHAR" />
    <result column="PD_SYSCODE" property="pdSyscode" jdbcType="DECIMAL" />
    <result column="PD_NAME" property="pdName" jdbcType="VARCHAR" />
    <result column="CT_CODE" property="ctCode" jdbcType="VARCHAR" />
    <result column="DM_AMOUNT" property="dmAmount" jdbcType="DECIMAL" />
    <result column="DM_STATUS" property="dmStatus" jdbcType="VARCHAR" />
    <result column="DM_CREATE_TIME" property="dmCreateTime" jdbcType="TIMESTAMP" />
    <result column="DM_HANDLE_TIME" property="dmHandleTime" jdbcType="TIMESTAMP" />
    <result column="DM_DAY" property="dmDay" jdbcType="DECIMAL" />
    <result column="DM_ORI_DAY" property="dmOriDay" jdbcType="DECIMAL" />
    <result column="DM_TOTAL_PIECES" property="dmTotalPieces" jdbcType="DECIMAL" />
    <result column="DM_TOTAL_WEIGHT" property="dmTotalWeight" jdbcType="DECIMAL" />
    <result column="DM_REMARK" property="dmRemark" jdbcType="VARCHAR" />
    <result column="DM_IETYPE" property="dmIetype" jdbcType="VARCHAR" />
    <result column="IS_BALANCE" property="isBalance" jdbcType="VARCHAR" />
    <result column="IS_DEBITEAWB" property="isDebiteawb" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    DM_TEMP_ID, DM_ID, COMPANY_ID, SO_CODE, PD_SYSCODE, PD_NAME, CT_CODE, DM_AMOUNT, 
    DM_STATUS, DM_CREATE_TIME, DM_HANDLE_TIME, DM_DAY, DM_ORI_DAY, DM_TOTAL_PIECES, DM_TOTAL_WEIGHT, 
    DM_REMARK, DM_IETYPE ,IS_BALANCE,IS_DEBITEAWB
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Short" >
    select 
    <include refid="Base_Column_List" />
    from DEBIT_MANIFEST_TEMPORARY
    where DM_TEMP_ID = #{dmTempId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Short" >
    delete from DEBIT_MANIFEST_TEMPORARY
    where DM_TEMP_ID = #{dmTempId,jdbcType=DECIMAL}
  </delete>
  <sql id='TABLE_SEQUENCE'>SEQ_DEBIT_MANIFEST.NEXTVAL</sql>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.DebitManifestTemporary" >
    <selectKey keyProperty="dmTempId" resultType="int" order="BEFORE">
      select
      <include refid="TABLE_SEQUENCE"/>
      from dual
    </selectKey>
    insert into DEBIT_MANIFEST_TEMPORARY (DM_TEMP_ID, DM_ID, COMPANY_ID, 
      SO_CODE, PD_SYSCODE, PD_NAME, 
      CT_CODE, DM_AMOUNT, DM_STATUS, 
      DM_CREATE_TIME, DM_HANDLE_TIME, DM_DAY, 
      DM_ORI_DAY, DM_TOTAL_PIECES, DM_TOTAL_WEIGHT, 
      DM_REMARK, DM_IETYPE)
    values (#{dmTempId,jdbcType=DECIMAL}, #{dmId,jdbcType=DECIMAL}, #{companyId,jdbcType=VARCHAR}, 
      #{soCode,jdbcType=VARCHAR}, #{pdSyscode,jdbcType=DECIMAL}, #{pdName,jdbcType=VARCHAR}, 
      #{ctCode,jdbcType=VARCHAR}, #{dmAmount,jdbcType=DECIMAL}, #{dmStatus,jdbcType=VARCHAR}, 
      #{dmCreateTime,jdbcType=TIMESTAMP}, #{dmHandleTime,jdbcType=TIMESTAMP}, #{dmDay,jdbcType=DECIMAL}, 
      #{dmOriDay,jdbcType=DECIMAL}, #{dmTotalPieces,jdbcType=DECIMAL}, #{dmTotalWeight,jdbcType=DECIMAL}, 
      #{dmRemark,jdbcType=VARCHAR}, #{dmIetype,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.DebitManifestTemporary" >
    <selectKey keyProperty="dmTempId" resultType="int" order="BEFORE">
      select
      <include refid="TABLE_SEQUENCE"/>
      from dual
    </selectKey>
    insert into DEBIT_MANIFEST_TEMPORARY
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="dmTempId != null" >
        DM_TEMP_ID,
      </if>
      <if test="dmId != null" >
        DM_ID,
      </if>
      <if test="companyId != null" >
        COMPANY_ID,
      </if>
      <if test="soCode != null" >
        SO_CODE,
      </if>
      <if test="pdSyscode != null" >
        PD_SYSCODE,
      </if>
      <if test="pdName != null" >
        PD_NAME,
      </if>
      <if test="ctCode != null" >
        CT_CODE,
      </if>
      <if test="dmAmount != null" >
        DM_AMOUNT,
      </if>
      <if test="dmStatus != null" >
        DM_STATUS,
      </if>
      <if test="dmCreateTime != null" >
        DM_CREATE_TIME,
      </if>
      <if test="dmHandleTime != null" >
        DM_HANDLE_TIME,
      </if>
      <if test="dmDay != null" >
        DM_DAY,
      </if>
      <if test="dmOriDay != null" >
        DM_ORI_DAY,
      </if>
      <if test="dmTotalPieces != null" >
        DM_TOTAL_PIECES,
      </if>
      <if test="dmTotalWeight != null" >
        DM_TOTAL_WEIGHT,
      </if>
      <if test="dmRemark != null" >
        DM_REMARK,
      </if>
      <if test="dmIetype != null" >
        DM_IETYPE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="dmTempId != null" >
        #{dmTempId,jdbcType=DECIMAL},
      </if>
      <if test="dmId != null" >
        #{dmId,jdbcType=DECIMAL},
      </if>
      <if test="companyId != null" >
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="soCode != null" >
        #{soCode,jdbcType=VARCHAR},
      </if>
      <if test="pdSyscode != null" >
        #{pdSyscode,jdbcType=DECIMAL},
      </if>
      <if test="pdName != null" >
        #{pdName,jdbcType=VARCHAR},
      </if>
      <if test="ctCode != null" >
        #{ctCode,jdbcType=VARCHAR},
      </if>
      <if test="dmAmount != null" >
        #{dmAmount,jdbcType=DECIMAL},
      </if>
      <if test="dmStatus != null" >
        #{dmStatus,jdbcType=VARCHAR},
      </if>
      <if test="dmCreateTime != null" >
        #{dmCreateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dmHandleTime != null" >
        #{dmHandleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dmDay != null" >
        #{dmDay,jdbcType=DECIMAL},
      </if>
      <if test="dmOriDay != null" >
        #{dmOriDay,jdbcType=DECIMAL},
      </if>
      <if test="dmTotalPieces != null" >
        #{dmTotalPieces,jdbcType=DECIMAL},
      </if>
      <if test="dmTotalWeight != null" >
        #{dmTotalWeight,jdbcType=DECIMAL},
      </if>
      <if test="dmRemark != null" >
        #{dmRemark,jdbcType=VARCHAR},
      </if>
      <if test="dmIetype != null" >
        #{dmIetype,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.DebitManifestTemporary" >
    update DEBIT_MANIFEST_TEMPORARY
    <set >
      <if test="dmId != null" >
        DM_ID = #{dmId,jdbcType=DECIMAL},
      </if>
      <if test="companyId != null" >
        COMPANY_ID = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="soCode != null" >
        SO_CODE = #{soCode,jdbcType=VARCHAR},
      </if>
      <if test="pdSyscode != null" >
        PD_SYSCODE = #{pdSyscode,jdbcType=DECIMAL},
      </if>
      <if test="pdName != null" >
        PD_NAME = #{pdName,jdbcType=VARCHAR},
      </if>
      <if test="ctCode != null" >
        CT_CODE = #{ctCode,jdbcType=VARCHAR},
      </if>
      <if test="dmAmount != null" >
        DM_AMOUNT = #{dmAmount,jdbcType=DECIMAL},
      </if>
      <if test="dmStatus != null" >
        DM_STATUS = #{dmStatus,jdbcType=VARCHAR},
      </if>
      <if test="dmCreateTime != null" >
        DM_CREATE_TIME = #{dmCreateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dmHandleTime != null" >
        DM_HANDLE_TIME = #{dmHandleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dmDay != null" >
        DM_DAY = #{dmDay,jdbcType=DECIMAL},
      </if>
      <if test="dmOriDay != null" >
        DM_ORI_DAY = #{dmOriDay,jdbcType=DECIMAL},
      </if>
      <if test="dmTotalPieces != null" >
        DM_TOTAL_PIECES = #{dmTotalPieces,jdbcType=DECIMAL},
      </if>
      <if test="dmTotalWeight != null" >
        DM_TOTAL_WEIGHT = #{dmTotalWeight,jdbcType=DECIMAL},
      </if>
      <if test="dmRemark != null" >
        DM_REMARK = #{dmRemark,jdbcType=VARCHAR},
      </if>
      <if test="dmIetype != null" >
        DM_IETYPE = #{dmIetype,jdbcType=VARCHAR},
      </if>
      <if test="isBalance != null" >
        IS_BALANCE = #{isBalance,jdbcType=VARCHAR},
      </if>
      <if test="isDebiteawb != null" >
        IS_DEBITEAWB = #{isDebiteawb,jdbcType=VARCHAR},
      </if>
    </set>
    where DM_TEMP_ID = #{dmTempId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.DebitManifestTemporary" >
    update DEBIT_MANIFEST_TEMPORARY
    set DM_ID = #{dmId,jdbcType=DECIMAL},
      COMPANY_ID = #{companyId,jdbcType=VARCHAR},
      SO_CODE = #{soCode,jdbcType=VARCHAR},
      PD_SYSCODE = #{pdSyscode,jdbcType=DECIMAL},
      PD_NAME = #{pdName,jdbcType=VARCHAR},
      CT_CODE = #{ctCode,jdbcType=VARCHAR},
      DM_AMOUNT = #{dmAmount,jdbcType=DECIMAL},
      DM_STATUS = #{dmStatus,jdbcType=VARCHAR},
      DM_CREATE_TIME = #{dmCreateTime,jdbcType=TIMESTAMP},
      DM_HANDLE_TIME = #{dmHandleTime,jdbcType=TIMESTAMP},
      DM_DAY = #{dmDay,jdbcType=DECIMAL},
      DM_ORI_DAY = #{dmOriDay,jdbcType=DECIMAL},
      DM_TOTAL_PIECES = #{dmTotalPieces,jdbcType=DECIMAL},
      DM_TOTAL_WEIGHT = #{dmTotalWeight,jdbcType=DECIMAL},
      DM_REMARK = #{dmRemark,jdbcType=VARCHAR},
      DM_IETYPE = #{dmIetype,jdbcType=VARCHAR}
    where DM_TEMP_ID = #{dmTempId,jdbcType=DECIMAL}
  </update>

  <select id="selectList" resultMap="BaseResultMap"  >
    select
    <include refid="Base_Column_List" />
    from DEBIT_MANIFEST_TEMPORARY
    where 1=1
  </select>

  <update id="updateTemporaryWeight" parameterType="com.sinoair.billing.domain.model.billing.DebitManifestTemporary" >
    update DEBIT_MANIFEST_TEMPORARY
    set
    DM_TOTAL_WEIGHT = #{dmTotalWeight,jdbcType=DECIMAL}
    where DM_TEMP_ID = #{dmTempId,jdbcType=DECIMAL}
  </update>
  <update id="updateTemporaryAmount" parameterType="com.sinoair.billing.domain.model.billing.DebitManifestTemporary" >
    update DEBIT_MANIFEST_TEMPORARY
    set
      DM_AMOUNT = #{dmAmount,jdbcType=DECIMAL},
      DM_TOTAL_WEIGHT = #{dmTotalWeight,jdbcType=DECIMAL},
      DM_TOTAL_PIECES = #{dmTotalPieces,jdbcType=DECIMAL}
    where DM_TEMP_ID = #{dmTempId,jdbcType=DECIMAL}
  </update>

  <select id="selectTmpList" resultMap="BaseResultMap"  >
    select
    <include refid="Base_Column_List" />
    from DEBIT_MANIFEST_TEMPORARY
    where 1=1
     and dm_temp_id in (select * from temp_dm_temporary)
  </select>

  <select id="selectIdByDmId" resultType="java.lang.Long"  >
    select
    <include refid="Base_Column_List" />
    from DEBIT_MANIFEST_TEMPORARY
    where DM_ID = #{dmId,jdbcType=DECIMAL}
  </select>

  <select id="selectTemporaryByDmId" resultMap="BaseResultMap"  >
    select
    <include refid="Base_Column_List" />
    from DEBIT_MANIFEST_TEMPORARY
    where DM_ID = #{dmId,jdbcType=DECIMAL}
  </select>
</mapper>