<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.DebitOrderMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.DebitOrder" >
    <id column="DM_ID" property="dmId" jdbcType="DECIMAL" />
    <result column="SYS_STATUS" property="sysStatus" jdbcType="VARCHAR" />
    <result column="THREAD_NUM" property="threadNum" jdbcType="DECIMAL" />
    <result column="HANDLE_NUM" property="handleNum" jdbcType="DECIMAL" />
    <result column="SYS_HANDLETIME" property="sysHandletime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    DM_ID, SYS_STATUS, THREAD_NUM, HANDLE_NUM, SYS_HANDLETIME
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from DEBIT_ORDER
    where DM_ID = #{dmId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from DEBIT_ORDER
    where DM_ID = #{dmId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.DebitOrder" >
    insert into DEBIT_ORDER (DM_ID, SYS_STATUS, THREAD_NUM, 
      HANDLE_NUM, SYS_HANDLETIME)
    values (#{dmId,jdbcType=DECIMAL}, #{sysStatus,jdbcType=VARCHAR}, #{threadNum,jdbcType=DECIMAL}, 
      #{handleNum,jdbcType=DECIMAL}, #{sysHandletime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.DebitOrder" >
    insert into DEBIT_ORDER
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="dmId != null" >
        DM_ID,
      </if>
      <if test="sysStatus != null" >
        SYS_STATUS,
      </if>
      <if test="threadNum != null" >
        THREAD_NUM,
      </if>
      <if test="handleNum != null" >
        HANDLE_NUM,
      </if>
      <if test="sysHandletime != null" >
        SYS_HANDLETIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="dmId != null" >
        #{dmId,jdbcType=DECIMAL},
      </if>
      <if test="sysStatus != null" >
        #{sysStatus,jdbcType=VARCHAR},
      </if>
      <if test="threadNum != null" >
        #{threadNum,jdbcType=DECIMAL},
      </if>
      <if test="handleNum != null" >
        #{handleNum,jdbcType=DECIMAL},
      </if>
      <if test="sysHandletime != null" >
        #{sysHandletime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.DebitOrder" >
    update DEBIT_ORDER
    <set >
      <if test="sysStatus != null" >
        SYS_STATUS = #{sysStatus,jdbcType=VARCHAR},
      </if>
      <if test="threadNum != null" >
        THREAD_NUM = #{threadNum,jdbcType=DECIMAL},
      </if>
      <if test="handleNum != null" >
        HANDLE_NUM = #{handleNum,jdbcType=DECIMAL},
      </if>
      <if test="sysHandletime != null" >
        SYS_HANDLETIME = #{sysHandletime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where DM_ID = #{dmId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.DebitOrder" >
    update DEBIT_ORDER
    set SYS_STATUS = #{sysStatus,jdbcType=VARCHAR},
      THREAD_NUM = #{threadNum,jdbcType=DECIMAL},
      HANDLE_NUM = #{handleNum,jdbcType=DECIMAL},
      SYS_HANDLETIME = #{sysHandletime,jdbcType=TIMESTAMP}
    where DM_ID = #{dmId,jdbcType=DECIMAL}
  </update>

  <select id="listDebitOrder" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select
    <include refid="Base_Column_List" />
    from DEBIT_ORDER
    where SYS_STATUS = #{sysStatus,jdbcType=DECIMAL}
  </select>

  <select id="countDebitOrder" resultType="java.lang.Integer" parameterType="java.lang.String" >
    select
    count(0)
    from DEBIT_ORDER
    where SYS_STATUS = #{sysStatus,jdbcType=DECIMAL}
  </select>

  <select id="listDebitManifestDetail" resultMap="BaseResultMap" parameterType="java.util.HashMap">
    select dm.dm_id,do.sys_status,do.sys_handletime
    from debit_manifest dm left join debit_order do on dm.dm_id = do.dm_id
    where 1=1
    <if test="soCode!=null and soCode!=''">
      and so_code = #{soCode}
    </if>
    <if test="startRrOccurtime!=null and startRrOccurtime!=''">
      and
      (
      dm_start_time >=
      to_date(#{startRrOccurtime}, 'yyyy-mm-dd')
      )
    </if>
    and do.sys_status != 'FINISHED'
  </select>
</mapper>