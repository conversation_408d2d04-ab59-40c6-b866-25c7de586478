<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.DebitRRMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.DebitRR" >
    <id column="DM_ID" property="dmId" jdbcType="DECIMAL" />
    <result column="RR_ID" property="rrId" jdbcType="DECIMAL" />
  </resultMap>
  <sql id="Base_Column_List" >
    DM_ID, RR_ID
  </sql>

  <select id="selectDebitRR" resultMap="BaseResultMap"  >
    select
    <include refid="Base_Column_List" />
    from DEBIT_RR
      where  dm_id = #{dmId,jdbcType=DECIMAL}
  </select>

  <delete id="deleteBatchByRR" parameterType="java.util.List" >
    delete from DEBIT_RR
    where RR_ID in
    <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
      #{item.rrId,jdbcType=DECIMAL}
    </foreach>
  </delete>


    <select id="countDebitRR" resultType="java.lang.Integer"  >
        select
        count(0)
        from DEBIT_RR
        where  dm_id = #{dmId,jdbcType=DECIMAL}
    </select>




</mapper>