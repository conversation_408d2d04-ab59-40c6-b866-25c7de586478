<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.DebitServicetypeMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.DebitServicetype" >
    <result column="DM_ID" property="dmId" jdbcType="DECIMAL" />
    <result column="EP_KEY" property="epKey" jdbcType="VARCHAR" />
    <result column="STATUS_WEIGHT" property="statusWeight" jdbcType="VARCHAR" />
    <result column="STATUS_AMOUNT" property="statusAmount" jdbcType="VARCHAR" />
    <result column="STATUS_EAWB" property="statusEawb" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    DM_ID, EP_KEY, STATUS_WEIGHT, STATUS_AMOUNT, STATUS_EAWB
  </sql>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.DebitServicetype" >
    insert into DEBIT_SERVICETYPE (DM_ID, EP_KEY, STATUS_WEIGHT, 
      STATUS_AMOUNT, STATUS_EAWB)
    values (#{dmId,jdbcType=DECIMAL}, #{epKey,jdbcType=VARCHAR}, #{statusWeight,jdbcType=VARCHAR}, 
      #{statusAmount,jdbcType=VARCHAR}, #{statusEawb,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.DebitServicetype" >
    insert into DEBIT_SERVICETYPE
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="dmId != null" >
        DM_ID,
      </if>
      <if test="epKey != null" >
        EP_KEY,
      </if>
      <if test="statusWeight != null" >
        STATUS_WEIGHT,
      </if>
      <if test="statusAmount != null" >
        STATUS_AMOUNT,
      </if>
      <if test="statusEawb != null" >
        STATUS_EAWB,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="dmId != null" >
        #{dmId,jdbcType=DECIMAL},
      </if>
      <if test="epKey != null" >
        #{epKey,jdbcType=VARCHAR},
      </if>
      <if test="statusWeight != null" >
        #{statusWeight,jdbcType=VARCHAR},
      </if>
      <if test="statusAmount != null" >
        #{statusAmount,jdbcType=VARCHAR},
      </if>
      <if test="statusEawb != null" >
        #{statusEawb,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.DebitServicetype" >
    update DEBIT_SERVICETYPE
    <set >
      <if test="statusEawb != null" >
        STATUS_EAWB = #{statusEawb,jdbcType=VARCHAR},
      </if>
      <if test="statusWeight != null" >
        STATUS_WEIGHT = #{statusWeight,jdbcType=DECIMAL},
      </if>
      <if test="statusAmount != null" >
        STATUS_AMOUNT = #{statusAmount,jdbcType=VARCHAR},
      </if>
    </set>
    where DM_ID = #{dmId,jdbcType=DECIMAL}
    <if test="epKey != null" >
      and EP_KEY = #{epKey,jdbcType=VARCHAR},
    </if>
  </update>

  <select id="selectByStatus" resultType="com.sinoair.billing.domain.model.billing.DebitServicetype"
          parameterType="com.sinoair.billing.domain.model.billing.DebitServicetype" >
    select
    <include refid="Base_Column_List" />
    from DEBIT_SERVICETYPE
    where 1=1
    <if test="statusWeight != null" >
      and STATUS_WEIGHT = #{statusWeight,jdbcType=VARCHAR}
    </if>
    <if test="statusAmount != null" >
      and STATUS_AMOUNT = #{statusAmount,jdbcType=DECIMAL}
    </if>
    <if test="statusEawb != null" >
      and STATUS_EAWB = #{statusEawb,jdbcType=VARCHAR}
    </if>
    <if test="dmId != null" >
      and DM_ID = #{dmId,jdbcType=VARCHAR}
    </if>
  </select>

  <select id="selectBmsManifestWeight" resultType="com.sinoair.billing.domain.model.billing.BmsManifestDetail"
          parameterType="com.sinoair.billing.domain.model.billing.DebitServicetype" >
    select
             sop.sinotrans_id,  -- 外运号
             count(sop.eawb_printcode) as bmPiece, -- 统计值件数
             sum(de.eawb_chargeableweight) as bmChargeableweight, -- 统计值计费重量
             sum(de.rr_actual_amount) as receiptAmount  --实收金额
    from debit_eawb de,sinotrans_order_pool sop
    where de.eawb_printcode = sop.eawb_printcode
      and de.dm_id = #{dmId,jdbcType=DECIMAL}
      and de.ep_key = #{epKey,jdbcType=VARCHAR}
    group by sop.sop.sinotrans_id
  </select>

</mapper>