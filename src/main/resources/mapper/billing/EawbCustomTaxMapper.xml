<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.EawbCustomTaxMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.EawbCustomTax" >
    <id column="ECT_SYSCODE" property="ectSyscode" jdbcType="DECIMAL" />
    <result column="TRACKING_NUMBER" property="trackingNumber" jdbcType="VARCHAR" />
    <result column="LOGISTICSORDERCODE" property="logisticsordercode" jdbcType="VARCHAR" />
    <result column="CT_CODE" property="ctCode" jdbcType="VARCHAR" />
    <result column="EAWB_CUSTCURRENCY" property="eawbCustcurrency" jdbcType="VARCHAR" />
    <result column="EAWB_CUSTDECLVAL" property="eawbCustdeclval" jdbcType="DECIMAL" />
    <result column="EAWB_KEYENTRYTIME" property="eawbKeyentrytime" jdbcType="TIMESTAMP" />
    <result column="EAWBC_SYSCODE" property="eawbcSyscode" jdbcType="DECIMAL" />
    <result column="EAWBCI_SYSCODE" property="eawbciSyscode" jdbcType="DECIMAL" />
    <result column="EAWBCIT_SYSCODE" property="eawbcitSyscode" jdbcType="DECIMAL" />
    <result column="HSCODE" property="hscode" jdbcType="VARCHAR" />
    <result column="MAWB_CODE" property="mawbCode" jdbcType="VARCHAR" />
    <result column="EAWBCIT_TAXITEMTYPE" property="eawbcitTaxitemtype" jdbcType="VARCHAR" />
    <result column="EAWBCIT_TAXES" property="eawbcitTaxes" jdbcType="DECIMAL" />
    <result column="EAWBCIT_TAXTATE" property="eawbcitTaxtate" jdbcType="DECIMAL" />
    <result column="EAWBCIT_CREATETIME" property="eawbcitCreatetime" jdbcType="TIMESTAMP" />
    <result column="EAWBCIT_PUSH_STATUS" property="eawbcitPushStatus" jdbcType="VARCHAR" />
    <result column="EAWBCIT_PUSH_TIME" property="eawbcitPushTime" jdbcType="TIMESTAMP" />
    <result column="EAWBCIT_PAYCURRENCY" property="eawbcitPaycurrency" jdbcType="VARCHAR" />
    <result column="EAWBCIT_EXCHANGE_RATE" property="eawbcitExchangeRate" jdbcType="DECIMAL" />
  </resultMap>
  <sql id="Base_Column_List" >
    ECT_SYSCODE, TRACKING_NUMBER, LOGISTICSORDERCODE, CT_CODE, EAWB_CUSTCURRENCY, EAWB_CUSTDECLVAL, 
    EAWB_KEYENTRYTIME, EAWBC_SYSCODE, EAWBCI_SYSCODE, EAWBCIT_SYSCODE, HSCODE, MAWB_CODE, 
    EAWBCIT_TAXITEMTYPE, EAWBCIT_TAXES, EAWBCIT_TAXTATE, EAWBCIT_CREATETIME, EAWBCIT_PUSH_STATUS, 
    EAWBCIT_PUSH_TIME, EAWBCIT_PAYCURRENCY, EAWBCIT_EXCHANGE_RATE
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from EAWB_CUSTOM_TAX
    where ECT_SYSCODE = #{ectSyscode,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from EAWB_CUSTOM_TAX
    where ECT_SYSCODE = #{ectSyscode,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.EawbCustomTax" >
    insert into EAWB_CUSTOM_TAX (ECT_SYSCODE, TRACKING_NUMBER, LOGISTICSORDERCODE, 
      CT_CODE, EAWB_CUSTCURRENCY, EAWB_CUSTDECLVAL, 
      EAWB_KEYENTRYTIME, EAWBC_SYSCODE, EAWBCI_SYSCODE, 
      EAWBCIT_SYSCODE, HSCODE, MAWB_CODE, 
      EAWBCIT_TAXITEMTYPE, EAWBCIT_TAXES, EAWBCIT_TAXTATE, 
      EAWBCIT_CREATETIME, EAWBCIT_PUSH_STATUS, 
      EAWBCIT_PUSH_TIME, EAWBCIT_PAYCURRENCY, 
      EAWBCIT_EXCHANGE_RATE)
    values (#{ectSyscode,jdbcType=DECIMAL}, #{trackingNumber,jdbcType=VARCHAR}, #{logisticsordercode,jdbcType=VARCHAR}, 
      #{ctCode,jdbcType=VARCHAR}, #{eawbCustcurrency,jdbcType=VARCHAR}, #{eawbCustdeclval,jdbcType=DECIMAL}, 
      #{eawbKeyentrytime,jdbcType=TIMESTAMP}, #{eawbcSyscode,jdbcType=DECIMAL}, #{eawbciSyscode,jdbcType=DECIMAL}, 
      #{eawbcitSyscode,jdbcType=DECIMAL}, #{hscode,jdbcType=VARCHAR}, #{mawbCode,jdbcType=VARCHAR}, 
      #{eawbcitTaxitemtype,jdbcType=VARCHAR}, #{eawbcitTaxes,jdbcType=DECIMAL}, #{eawbcitTaxtate,jdbcType=DECIMAL}, 
      #{eawbcitCreatetime,jdbcType=TIMESTAMP}, #{eawbcitPushStatus,jdbcType=VARCHAR}, 
      #{eawbcitPushTime,jdbcType=TIMESTAMP}, #{eawbcitPaycurrency,jdbcType=VARCHAR}, 
      #{eawbcitExchangeRate,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.EawbCustomTax" >
    insert into EAWB_CUSTOM_TAX
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="ectSyscode != null" >
        ECT_SYSCODE,
      </if>
      <if test="trackingNumber != null" >
        TRACKING_NUMBER,
      </if>
      <if test="logisticsordercode != null" >
        LOGISTICSORDERCODE,
      </if>
      <if test="ctCode != null" >
        CT_CODE,
      </if>
      <if test="eawbCustcurrency != null" >
        EAWB_CUSTCURRENCY,
      </if>
      <if test="eawbCustdeclval != null" >
        EAWB_CUSTDECLVAL,
      </if>
      <if test="eawbKeyentrytime != null" >
        EAWB_KEYENTRYTIME,
      </if>
      <if test="eawbcSyscode != null" >
        EAWBC_SYSCODE,
      </if>
      <if test="eawbciSyscode != null" >
        EAWBCI_SYSCODE,
      </if>
      <if test="eawbcitSyscode != null" >
        EAWBCIT_SYSCODE,
      </if>
      <if test="hscode != null" >
        HSCODE,
      </if>
      <if test="mawbCode != null" >
        MAWB_CODE,
      </if>
      <if test="eawbcitTaxitemtype != null" >
        EAWBCIT_TAXITEMTYPE,
      </if>
      <if test="eawbcitTaxes != null" >
        EAWBCIT_TAXES,
      </if>
      <if test="eawbcitTaxtate != null" >
        EAWBCIT_TAXTATE,
      </if>
      <if test="eawbcitCreatetime != null" >
        EAWBCIT_CREATETIME,
      </if>
      <if test="eawbcitPushStatus != null" >
        EAWBCIT_PUSH_STATUS,
      </if>
      <if test="eawbcitPushTime != null" >
        EAWBCIT_PUSH_TIME,
      </if>
      <if test="eawbcitPaycurrency != null" >
        EAWBCIT_PAYCURRENCY,
      </if>
      <if test="eawbcitExchangeRate != null" >
        EAWBCIT_EXCHANGE_RATE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="ectSyscode != null" >
        #{ectSyscode,jdbcType=DECIMAL},
      </if>
      <if test="trackingNumber != null" >
        #{trackingNumber,jdbcType=VARCHAR},
      </if>
      <if test="logisticsordercode != null" >
        #{logisticsordercode,jdbcType=VARCHAR},
      </if>
      <if test="ctCode != null" >
        #{ctCode,jdbcType=VARCHAR},
      </if>
      <if test="eawbCustcurrency != null" >
        #{eawbCustcurrency,jdbcType=VARCHAR},
      </if>
      <if test="eawbCustdeclval != null" >
        #{eawbCustdeclval,jdbcType=DECIMAL},
      </if>
      <if test="eawbKeyentrytime != null" >
        #{eawbKeyentrytime,jdbcType=TIMESTAMP},
      </if>
      <if test="eawbcSyscode != null" >
        #{eawbcSyscode,jdbcType=DECIMAL},
      </if>
      <if test="eawbciSyscode != null" >
        #{eawbciSyscode,jdbcType=DECIMAL},
      </if>
      <if test="eawbcitSyscode != null" >
        #{eawbcitSyscode,jdbcType=DECIMAL},
      </if>
      <if test="hscode != null" >
        #{hscode,jdbcType=VARCHAR},
      </if>
      <if test="mawbCode != null" >
        #{mawbCode,jdbcType=VARCHAR},
      </if>
      <if test="eawbcitTaxitemtype != null" >
        #{eawbcitTaxitemtype,jdbcType=VARCHAR},
      </if>
      <if test="eawbcitTaxes != null" >
        #{eawbcitTaxes,jdbcType=DECIMAL},
      </if>
      <if test="eawbcitTaxtate != null" >
        #{eawbcitTaxtate,jdbcType=DECIMAL},
      </if>
      <if test="eawbcitCreatetime != null" >
        #{eawbcitCreatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="eawbcitPushStatus != null" >
        #{eawbcitPushStatus,jdbcType=VARCHAR},
      </if>
      <if test="eawbcitPushTime != null" >
        #{eawbcitPushTime,jdbcType=TIMESTAMP},
      </if>
      <if test="eawbcitPaycurrency != null" >
        #{eawbcitPaycurrency,jdbcType=VARCHAR},
      </if>
      <if test="eawbcitExchangeRate != null" >
        #{eawbcitExchangeRate,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.EawbCustomTax" >
    update EAWB_CUSTOM_TAX
    <set >
      <if test="trackingNumber != null" >
        TRACKING_NUMBER = #{trackingNumber,jdbcType=VARCHAR},
      </if>
      <if test="logisticsordercode != null" >
        LOGISTICSORDERCODE = #{logisticsordercode,jdbcType=VARCHAR},
      </if>
      <if test="ctCode != null" >
        CT_CODE = #{ctCode,jdbcType=VARCHAR},
      </if>
      <if test="eawbCustcurrency != null" >
        EAWB_CUSTCURRENCY = #{eawbCustcurrency,jdbcType=VARCHAR},
      </if>
      <if test="eawbCustdeclval != null" >
        EAWB_CUSTDECLVAL = #{eawbCustdeclval,jdbcType=DECIMAL},
      </if>
      <if test="eawbKeyentrytime != null" >
        EAWB_KEYENTRYTIME = #{eawbKeyentrytime,jdbcType=TIMESTAMP},
      </if>
      <if test="eawbcSyscode != null" >
        EAWBC_SYSCODE = #{eawbcSyscode,jdbcType=DECIMAL},
      </if>
      <if test="eawbciSyscode != null" >
        EAWBCI_SYSCODE = #{eawbciSyscode,jdbcType=DECIMAL},
      </if>
      <if test="eawbcitSyscode != null" >
        EAWBCIT_SYSCODE = #{eawbcitSyscode,jdbcType=DECIMAL},
      </if>
      <if test="hscode != null" >
        HSCODE = #{hscode,jdbcType=VARCHAR},
      </if>
      <if test="mawbCode != null" >
        MAWB_CODE = #{mawbCode,jdbcType=VARCHAR},
      </if>
      <if test="eawbcitTaxitemtype != null" >
        EAWBCIT_TAXITEMTYPE = #{eawbcitTaxitemtype,jdbcType=VARCHAR},
      </if>
      <if test="eawbcitTaxes != null" >
        EAWBCIT_TAXES = #{eawbcitTaxes,jdbcType=DECIMAL},
      </if>
      <if test="eawbcitTaxtate != null" >
        EAWBCIT_TAXTATE = #{eawbcitTaxtate,jdbcType=DECIMAL},
      </if>
      <if test="eawbcitCreatetime != null" >
        EAWBCIT_CREATETIME = #{eawbcitCreatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="eawbcitPushStatus != null" >
        EAWBCIT_PUSH_STATUS = #{eawbcitPushStatus,jdbcType=VARCHAR},
      </if>
      <if test="eawbcitPushTime != null" >
        EAWBCIT_PUSH_TIME = #{eawbcitPushTime,jdbcType=TIMESTAMP},
      </if>
      <if test="eawbcitPaycurrency != null" >
        EAWBCIT_PAYCURRENCY = #{eawbcitPaycurrency,jdbcType=VARCHAR},
      </if>
      <if test="eawbcitExchangeRate != null" >
        EAWBCIT_EXCHANGE_RATE = #{eawbcitExchangeRate,jdbcType=DECIMAL},
      </if>
    </set>
    where ECT_SYSCODE = #{ectSyscode,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.EawbCustomTax" >
    update EAWB_CUSTOM_TAX
    set TRACKING_NUMBER = #{trackingNumber,jdbcType=VARCHAR},
      LOGISTICSORDERCODE = #{logisticsordercode,jdbcType=VARCHAR},
      CT_CODE = #{ctCode,jdbcType=VARCHAR},
      EAWB_CUSTCURRENCY = #{eawbCustcurrency,jdbcType=VARCHAR},
      EAWB_CUSTDECLVAL = #{eawbCustdeclval,jdbcType=DECIMAL},
      EAWB_KEYENTRYTIME = #{eawbKeyentrytime,jdbcType=TIMESTAMP},
      EAWBC_SYSCODE = #{eawbcSyscode,jdbcType=DECIMAL},
      EAWBCI_SYSCODE = #{eawbciSyscode,jdbcType=DECIMAL},
      EAWBCIT_SYSCODE = #{eawbcitSyscode,jdbcType=DECIMAL},
      HSCODE = #{hscode,jdbcType=VARCHAR},
      MAWB_CODE = #{mawbCode,jdbcType=VARCHAR},
      EAWBCIT_TAXITEMTYPE = #{eawbcitTaxitemtype,jdbcType=VARCHAR},
      EAWBCIT_TAXES = #{eawbcitTaxes,jdbcType=DECIMAL},
      EAWBCIT_TAXTATE = #{eawbcitTaxtate,jdbcType=DECIMAL},
      EAWBCIT_CREATETIME = #{eawbcitCreatetime,jdbcType=TIMESTAMP},
      EAWBCIT_PUSH_STATUS = #{eawbcitPushStatus,jdbcType=VARCHAR},
      EAWBCIT_PUSH_TIME = #{eawbcitPushTime,jdbcType=TIMESTAMP},
      EAWBCIT_PAYCURRENCY = #{eawbcitPaycurrency,jdbcType=VARCHAR},
      EAWBCIT_EXCHANGE_RATE = #{eawbcitExchangeRate,jdbcType=DECIMAL}
    where ECT_SYSCODE = #{ectSyscode,jdbcType=DECIMAL}
  </update>

  <insert id="insertBatch"  parameterType="java.util.List">
    insert into EAWB_CUSTOM_TAX (ECT_SYSCODE, TRACKING_NUMBER, LOGISTICSORDERCODE,
    CT_CODE, EAWB_CUSTCURRENCY, EAWB_CUSTDECLVAL,
    EAWB_KEYENTRYTIME, EAWBC_SYSCODE, EAWBCI_SYSCODE,
    EAWBCIT_SYSCODE, HSCODE, MAWB_CODE,
    EAWBCIT_TAXITEMTYPE, EAWBCIT_TAXES, EAWBCIT_TAXTATE,
    EAWBCIT_CREATETIME, EAWBCIT_PUSH_STATUS,
    EAWBCIT_PUSH_TIME, EAWBCIT_PAYCURRENCY,
    EAWBCIT_EXCHANGE_RATE)
    select SEQ_CHECK_DETAIL.NEXTVAL,pbd.* from (
    <foreach collection="list" item="item" index="index" separator="UNION ALL">
      select #{item.trackingNumber,jdbcType=VARCHAR}, #{item.logisticsordercode,jdbcType=VARCHAR},
      #{item.ctCode,jdbcType=VARCHAR}, #{item.eawbCustcurrency,jdbcType=VARCHAR}, #{item.eawbCustdeclval,jdbcType=DECIMAL},
      #{item.eawbKeyentrytime,jdbcType=TIMESTAMP}, #{item.eawbcSyscode,jdbcType=DECIMAL}, #{item.eawbciSyscode,jdbcType=DECIMAL},
      #{item.eawbcitSyscode,jdbcType=DECIMAL}, #{item.hscode,jdbcType=VARCHAR}, #{item.mawbCode,jdbcType=VARCHAR},
      #{item.eawbcitTaxitemtype,jdbcType=VARCHAR}, #{item.eawbcitTaxes,jdbcType=DECIMAL}, #{item.eawbcitTaxtate,jdbcType=DECIMAL},
      #{item.eawbcitCreatetime,jdbcType=TIMESTAMP}, #{item.eawbcitPushStatus,jdbcType=VARCHAR},
      #{item.eawbcitPushTime,jdbcType=TIMESTAMP}, #{item.eawbcitPaycurrency,jdbcType=VARCHAR},
      #{item.eawbcitExchangeRate,jdbcType=DECIMAL} from dual
    </foreach>
    ) pbd
  </insert>

</mapper>