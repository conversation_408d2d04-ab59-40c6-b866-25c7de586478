<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.EawbEbaTrackDisMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.EawbEbaTrackDis" >
    <id column="ETD_SYSCODE" property="etdSyscode" jdbcType="DECIMAL" />
    <result column="COMPANY_ID" property="companyId" jdbcType="VARCHAR" />
    <result column="SO_CODE" property="soCode" jdbcType="VARCHAR" />
    <result column="ET_COLUMNNAME" property="etColumnname" jdbcType="VARCHAR" />
    <result column="EAD_CODE" property="eadCode" jdbcType="VARCHAR" />
    <result column="EAST_CODE" property="eastCode" jdbcType="VARCHAR" />
    <result column="ETP_COLUMNSHOWNAME" property="etpColumnshowname" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    ETD_SYSCODE, COMPANY_ID, SO_CODE, ET_COLUMNNAME, EAD_CODE, EAST_CODE, ETP_COLUMNSHOWNAME
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Short" >
    select 
    <include refid="Base_Column_List" />
    from EAWB_EBA_TRACK_DIS
    where ETD_SYSCODE = #{etdSyscode,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Short" >
    delete from EAWB_EBA_TRACK_DIS
    where ETD_SYSCODE = #{etdSyscode,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.EawbEbaTrackDis" >
    insert into EAWB_EBA_TRACK_DIS (ETD_SYSCODE, COMPANY_ID, SO_CODE, 
      ET_COLUMNNAME, EAD_CODE, EAST_CODE, 
      ETP_COLUMNSHOWNAME)
    values (#{etdSyscode,jdbcType=DECIMAL}, #{companyId,jdbcType=VARCHAR}, #{soCode,jdbcType=VARCHAR}, 
      #{etColumnname,jdbcType=VARCHAR}, #{eadCode,jdbcType=VARCHAR}, #{eastCode,jdbcType=VARCHAR}, 
      #{etpColumnshowname,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.EawbEbaTrackDis" >
    insert into EAWB_EBA_TRACK_DIS
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="etdSyscode != null" >
        ETD_SYSCODE,
      </if>
      <if test="companyId != null" >
        COMPANY_ID,
      </if>
      <if test="soCode != null" >
        SO_CODE,
      </if>
      <if test="etColumnname != null" >
        ET_COLUMNNAME,
      </if>
      <if test="eadCode != null" >
        EAD_CODE,
      </if>
      <if test="eastCode != null" >
        EAST_CODE,
      </if>
      <if test="etpColumnshowname != null" >
        ETP_COLUMNSHOWNAME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="etdSyscode != null" >
        #{etdSyscode,jdbcType=DECIMAL},
      </if>
      <if test="companyId != null" >
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="soCode != null" >
        #{soCode,jdbcType=VARCHAR},
      </if>
      <if test="etColumnname != null" >
        #{etColumnname,jdbcType=VARCHAR},
      </if>
      <if test="eadCode != null" >
        #{eadCode,jdbcType=VARCHAR},
      </if>
      <if test="eastCode != null" >
        #{eastCode,jdbcType=VARCHAR},
      </if>
      <if test="etpColumnshowname != null" >
        #{etpColumnshowname,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.EawbEbaTrackDis" >
    update EAWB_EBA_TRACK_DIS
    <set >
      <if test="companyId != null" >
        COMPANY_ID = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="soCode != null" >
        SO_CODE = #{soCode,jdbcType=VARCHAR},
      </if>
      <if test="etColumnname != null" >
        ET_COLUMNNAME = #{etColumnname,jdbcType=VARCHAR},
      </if>
      <if test="eadCode != null" >
        EAD_CODE = #{eadCode,jdbcType=VARCHAR},
      </if>
      <if test="eastCode != null" >
        EAST_CODE = #{eastCode,jdbcType=VARCHAR},
      </if>
      <if test="etpColumnshowname != null" >
        ETP_COLUMNSHOWNAME = #{etpColumnshowname,jdbcType=VARCHAR},
      </if>
    </set>
    where ETD_SYSCODE = #{etdSyscode,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.EawbEbaTrackDis" >
    update EAWB_EBA_TRACK_DIS
    set COMPANY_ID = #{companyId,jdbcType=VARCHAR},
      SO_CODE = #{soCode,jdbcType=VARCHAR},
      ET_COLUMNNAME = #{etColumnname,jdbcType=VARCHAR},
      EAD_CODE = #{eadCode,jdbcType=VARCHAR},
      EAST_CODE = #{eastCode,jdbcType=VARCHAR},
      ETP_COLUMNSHOWNAME = #{etpColumnshowname,jdbcType=VARCHAR}
    where ETD_SYSCODE = #{etdSyscode,jdbcType=DECIMAL}
  </update>

  <select id="list" resultMap="BaseResultMap" parameterType="com.sinoair.billing.domain.model.billing.EawbEbaTrackDis" >
    select
    <include refid="Base_Column_List" />
    from EAWB_EBA_TRACK_DIS
    where SO_CODE = #{soCode,jdbcType=VARCHAR}
    <if test="companyId != null" >
      and COMPANY_ID = #{companyId,jdbcType=VARCHAR}
    </if>
    <if test="etColumnname != null" >
      and ET_COLUMNNAME = #{etColumnname,jdbcType=VARCHAR}
    </if>
    <if test="eadCode != null" >
      and EAD_CODE = #{eadCode,jdbcType=VARCHAR}
    </if>
    <if test="eastCode != null" >
      and EAST_CODE = #{eastCode,jdbcType=VARCHAR}
    </if>
    <if test="etpColumnshowname != null" >
      and ETP_COLUMNSHOWNAME = #{etpColumnshowname,jdbcType=VARCHAR}
    </if>
  </select>
</mapper>