<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.EawbPreMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.EawbPre" >
    <id column="EAWB_SYSCODE" property="eawbSyscode" jdbcType="DECIMAL" />
    <result column="EAWB_PRINTCODE" property="eawbPrintcode" jdbcType="VARCHAR" />
    <result column="EAWB_HANDLETIME" property="eawbHandletime" jdbcType="TIMESTAMP" />
    <result column="EAWB_DELIVER_ADDRESS" property="eawbDeliverAddress" jdbcType="VARCHAR" />
    <result column="EAWB_DELIVER_CONTACT" property="eawbDeliverContact" jdbcType="VARCHAR" />
    <result column="EAWB_DELIVER_PHONE" property="eawbDeliverPhone" jdbcType="VARCHAR" />
    <result column="EAWB_DELIVER_MOBILE" property="eawbDeliverMobile" jdbcType="VARCHAR" />
    <result column="EAWB_DELIVER_EMAIL" property="eawbDeliverEmail" jdbcType="VARCHAR" />
    <result column="EAWB_DESTINATION" property="eawbDestination" jdbcType="VARCHAR" />
    <result column="EAWB_SO_CODE" property="eawbSoCode" jdbcType="VARCHAR" />
    <result column="EAWB_REFERENCE1" property="eawbReference1" jdbcType="VARCHAR" />
    <result column="EAWB_REFERENCE2" property="eawbReference2" jdbcType="VARCHAR" />
    <result column="EAWB_STATUS" property="eawbStatus" jdbcType="VARCHAR" />
    <result column="EAWB_KEYENTRYTIME" property="eawbKeyentrytime" jdbcType="TIMESTAMP" />
    <result column="EAWB_DESTCOUNTRY" property="eawbDestcountry" jdbcType="VARCHAR" />
    <result column="EAWB_CUSTPRODNAME" property="eawbCustprodname" jdbcType="VARCHAR" />
    <result column="EAWB_TRANSMODEID" property="eawbTransmodeid" jdbcType="VARCHAR" />
    <result column="EAWB_SERVICETYPE" property="eawbServicetype" jdbcType="VARCHAR" />
    <result column="EAWB_DELIVER_POSTCODE" property="eawbDeliverPostcode" jdbcType="VARCHAR" />
    <result column="EAWB_CUSTDECLVAL" property="eawbCustdeclval" jdbcType="DECIMAL" />
    <result column="EAWB_DESTSTATE" property="eawbDeststate" jdbcType="VARCHAR" />
    <result column="EAWB_DESTCITY" property="eawbDestcity" jdbcType="VARCHAR" />
    <result column="EAWB_DECLAREGROSSWEIGHT" property="eawbDeclaregrossweight" jdbcType="DECIMAL" />
    <result column="PKG_PRINTCODE" property="pkgPrintcode" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    EAWB_SYSCODE, EAWB_PRINTCODE, EAWB_HANDLETIME, EAWB_DELIVER_ADDRESS, EAWB_DELIVER_CONTACT, 
    EAWB_DELIVER_PHONE, EAWB_DELIVER_MOBILE, EAWB_DELIVER_EMAIL, EAWB_DESTINATION, EAWB_SO_CODE, 
    EAWB_REFERENCE1, EAWB_REFERENCE2, EAWB_STATUS, EAWB_KEYENTRYTIME, EAWB_DESTCOUNTRY, 
    EAWB_CUSTPRODNAME, EAWB_TRANSMODEID, EAWB_SERVICETYPE, EAWB_DELIVER_POSTCODE, EAWB_CUSTDECLVAL, 
    EAWB_DESTSTATE, EAWB_DESTCITY, EAWB_DECLAREGROSSWEIGHT, PKG_PRINTCODE
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from EAWBPRE
    where EAWB_SYSCODE = #{eawbSyscode,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from EAWBPRE
    where EAWB_SYSCODE = #{eawbSyscode,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.EawbPre" >
    insert into EAWBPRE (EAWB_SYSCODE, EAWB_PRINTCODE, EAWB_HANDLETIME, 
      EAWB_DELIVER_ADDRESS, EAWB_DELIVER_CONTACT, 
      EAWB_DELIVER_PHONE, EAWB_DELIVER_MOBILE, EAWB_DELIVER_EMAIL, 
      EAWB_DESTINATION, EAWB_SO_CODE, EAWB_REFERENCE1, 
      EAWB_REFERENCE2, EAWB_STATUS, EAWB_KEYENTRYTIME, 
      EAWB_DESTCOUNTRY, EAWB_CUSTPRODNAME, EAWB_TRANSMODEID, 
      EAWB_SERVICETYPE, EAWB_DELIVER_POSTCODE, EAWB_CUSTDECLVAL, 
      EAWB_DESTSTATE, EAWB_DESTCITY, EAWB_DECLAREGROSSWEIGHT, 
      PKG_PRINTCODE)
    values (#{eawbSyscode,jdbcType=DECIMAL}, #{eawbPrintcode,jdbcType=VARCHAR}, #{eawbHandletime,jdbcType=TIMESTAMP}, 
      #{eawbDeliverAddress,jdbcType=VARCHAR}, #{eawbDeliverContact,jdbcType=VARCHAR}, 
      #{eawbDeliverPhone,jdbcType=VARCHAR}, #{eawbDeliverMobile,jdbcType=VARCHAR}, #{eawbDeliverEmail,jdbcType=VARCHAR}, 
      #{eawbDestination,jdbcType=VARCHAR}, #{eawbSoCode,jdbcType=VARCHAR}, #{eawbReference1,jdbcType=VARCHAR}, 
      #{eawbReference2,jdbcType=VARCHAR}, #{eawbStatus,jdbcType=VARCHAR}, #{eawbKeyentrytime,jdbcType=TIMESTAMP}, 
      #{eawbDestcountry,jdbcType=VARCHAR}, #{eawbCustprodname,jdbcType=VARCHAR}, #{eawbTransmodeid,jdbcType=VARCHAR}, 
      #{eawbServicetype,jdbcType=VARCHAR}, #{eawbDeliverPostcode,jdbcType=VARCHAR}, #{eawbCustdeclval,jdbcType=DECIMAL}, 
      #{eawbDeststate,jdbcType=VARCHAR}, #{eawbDestcity,jdbcType=VARCHAR}, #{eawbDeclaregrossweight,jdbcType=DECIMAL}, 
      #{pkgPrintcode,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.EawbPre" >
    insert into EAWBPRE
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="eawbSyscode != null" >
        EAWB_SYSCODE,
      </if>
      <if test="eawbPrintcode != null" >
        EAWB_PRINTCODE,
      </if>
      <if test="eawbHandletime != null" >
        EAWB_HANDLETIME,
      </if>
      <if test="eawbDeliverAddress != null" >
        EAWB_DELIVER_ADDRESS,
      </if>
      <if test="eawbDeliverContact != null" >
        EAWB_DELIVER_CONTACT,
      </if>
      <if test="eawbDeliverPhone != null" >
        EAWB_DELIVER_PHONE,
      </if>
      <if test="eawbDeliverMobile != null" >
        EAWB_DELIVER_MOBILE,
      </if>
      <if test="eawbDeliverEmail != null" >
        EAWB_DELIVER_EMAIL,
      </if>
      <if test="eawbDestination != null" >
        EAWB_DESTINATION,
      </if>
      <if test="eawbSoCode != null" >
        EAWB_SO_CODE,
      </if>
      <if test="eawbReference1 != null" >
        EAWB_REFERENCE1,
      </if>
      <if test="eawbReference2 != null" >
        EAWB_REFERENCE2,
      </if>
      <if test="eawbStatus != null" >
        EAWB_STATUS,
      </if>
      <if test="eawbKeyentrytime != null" >
        EAWB_KEYENTRYTIME,
      </if>
      <if test="eawbDestcountry != null" >
        EAWB_DESTCOUNTRY,
      </if>
      <if test="eawbCustprodname != null" >
        EAWB_CUSTPRODNAME,
      </if>
      <if test="eawbTransmodeid != null" >
        EAWB_TRANSMODEID,
      </if>
      <if test="eawbServicetype != null" >
        EAWB_SERVICETYPE,
      </if>
      <if test="eawbDeliverPostcode != null" >
        EAWB_DELIVER_POSTCODE,
      </if>
      <if test="eawbCustdeclval != null" >
        EAWB_CUSTDECLVAL,
      </if>
      <if test="eawbDeststate != null" >
        EAWB_DESTSTATE,
      </if>
      <if test="eawbDestcity != null" >
        EAWB_DESTCITY,
      </if>
      <if test="eawbDeclaregrossweight != null" >
        EAWB_DECLAREGROSSWEIGHT,
      </if>
      <if test="pkgPrintcode != null" >
        PKG_PRINTCODE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="eawbSyscode != null" >
        #{eawbSyscode,jdbcType=DECIMAL},
      </if>
      <if test="eawbPrintcode != null" >
        #{eawbPrintcode,jdbcType=VARCHAR},
      </if>
      <if test="eawbHandletime != null" >
        #{eawbHandletime,jdbcType=TIMESTAMP},
      </if>
      <if test="eawbDeliverAddress != null" >
        #{eawbDeliverAddress,jdbcType=VARCHAR},
      </if>
      <if test="eawbDeliverContact != null" >
        #{eawbDeliverContact,jdbcType=VARCHAR},
      </if>
      <if test="eawbDeliverPhone != null" >
        #{eawbDeliverPhone,jdbcType=VARCHAR},
      </if>
      <if test="eawbDeliverMobile != null" >
        #{eawbDeliverMobile,jdbcType=VARCHAR},
      </if>
      <if test="eawbDeliverEmail != null" >
        #{eawbDeliverEmail,jdbcType=VARCHAR},
      </if>
      <if test="eawbDestination != null" >
        #{eawbDestination,jdbcType=VARCHAR},
      </if>
      <if test="eawbSoCode != null" >
        #{eawbSoCode,jdbcType=VARCHAR},
      </if>
      <if test="eawbReference1 != null" >
        #{eawbReference1,jdbcType=VARCHAR},
      </if>
      <if test="eawbReference2 != null" >
        #{eawbReference2,jdbcType=VARCHAR},
      </if>
      <if test="eawbStatus != null" >
        #{eawbStatus,jdbcType=VARCHAR},
      </if>
      <if test="eawbKeyentrytime != null" >
        #{eawbKeyentrytime,jdbcType=TIMESTAMP},
      </if>
      <if test="eawbDestcountry != null" >
        #{eawbDestcountry,jdbcType=VARCHAR},
      </if>
      <if test="eawbCustprodname != null" >
        #{eawbCustprodname,jdbcType=VARCHAR},
      </if>
      <if test="eawbTransmodeid != null" >
        #{eawbTransmodeid,jdbcType=VARCHAR},
      </if>
      <if test="eawbServicetype != null" >
        #{eawbServicetype,jdbcType=VARCHAR},
      </if>
      <if test="eawbDeliverPostcode != null" >
        #{eawbDeliverPostcode,jdbcType=VARCHAR},
      </if>
      <if test="eawbCustdeclval != null" >
        #{eawbCustdeclval,jdbcType=DECIMAL},
      </if>
      <if test="eawbDeststate != null" >
        #{eawbDeststate,jdbcType=VARCHAR},
      </if>
      <if test="eawbDestcity != null" >
        #{eawbDestcity,jdbcType=VARCHAR},
      </if>
      <if test="eawbDeclaregrossweight != null" >
        #{eawbDeclaregrossweight,jdbcType=DECIMAL},
      </if>
      <if test="pkgPrintcode != null" >
        #{pkgPrintcode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.EawbPre" >
    update EAWBPRE
    <set >
      <if test="eawbPrintcode != null" >
        EAWB_PRINTCODE = #{eawbPrintcode,jdbcType=VARCHAR},
      </if>
      <if test="eawbHandletime != null" >
        EAWB_HANDLETIME = #{eawbHandletime,jdbcType=TIMESTAMP},
      </if>
      <if test="eawbDeliverAddress != null" >
        EAWB_DELIVER_ADDRESS = #{eawbDeliverAddress,jdbcType=VARCHAR},
      </if>
      <if test="eawbDeliverContact != null" >
        EAWB_DELIVER_CONTACT = #{eawbDeliverContact,jdbcType=VARCHAR},
      </if>
      <if test="eawbDeliverPhone != null" >
        EAWB_DELIVER_PHONE = #{eawbDeliverPhone,jdbcType=VARCHAR},
      </if>
      <if test="eawbDeliverMobile != null" >
        EAWB_DELIVER_MOBILE = #{eawbDeliverMobile,jdbcType=VARCHAR},
      </if>
      <if test="eawbDeliverEmail != null" >
        EAWB_DELIVER_EMAIL = #{eawbDeliverEmail,jdbcType=VARCHAR},
      </if>
      <if test="eawbDestination != null" >
        EAWB_DESTINATION = #{eawbDestination,jdbcType=VARCHAR},
      </if>
      <if test="eawbSoCode != null" >
        EAWB_SO_CODE = #{eawbSoCode,jdbcType=VARCHAR},
      </if>
      <if test="eawbReference1 != null" >
        EAWB_REFERENCE1 = #{eawbReference1,jdbcType=VARCHAR},
      </if>
      <if test="eawbReference2 != null" >
        EAWB_REFERENCE2 = #{eawbReference2,jdbcType=VARCHAR},
      </if>
      <if test="eawbStatus != null" >
        EAWB_STATUS = #{eawbStatus,jdbcType=VARCHAR},
      </if>
      <if test="eawbKeyentrytime != null" >
        EAWB_KEYENTRYTIME = #{eawbKeyentrytime,jdbcType=TIMESTAMP},
      </if>
      <if test="eawbDestcountry != null" >
        EAWB_DESTCOUNTRY = #{eawbDestcountry,jdbcType=VARCHAR},
      </if>
      <if test="eawbCustprodname != null" >
        EAWB_CUSTPRODNAME = #{eawbCustprodname,jdbcType=VARCHAR},
      </if>
      <if test="eawbTransmodeid != null" >
        EAWB_TRANSMODEID = #{eawbTransmodeid,jdbcType=VARCHAR},
      </if>
      <if test="eawbServicetype != null" >
        EAWB_SERVICETYPE = #{eawbServicetype,jdbcType=VARCHAR},
      </if>
      <if test="eawbDeliverPostcode != null" >
        EAWB_DELIVER_POSTCODE = #{eawbDeliverPostcode,jdbcType=VARCHAR},
      </if>
      <if test="eawbCustdeclval != null" >
        EAWB_CUSTDECLVAL = #{eawbCustdeclval,jdbcType=DECIMAL},
      </if>
      <if test="eawbDeststate != null" >
        EAWB_DESTSTATE = #{eawbDeststate,jdbcType=VARCHAR},
      </if>
      <if test="eawbDestcity != null" >
        EAWB_DESTCITY = #{eawbDestcity,jdbcType=VARCHAR},
      </if>
      <if test="eawbDeclaregrossweight != null" >
        EAWB_DECLAREGROSSWEIGHT = #{eawbDeclaregrossweight,jdbcType=DECIMAL},
      </if>
      <if test="pkgPrintcode != null" >
        PKG_PRINTCODE = #{pkgPrintcode,jdbcType=VARCHAR},
      </if>
    </set>
    where EAWB_SYSCODE = #{eawbSyscode,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.EawbPre" >
    update EAWBPRE
    set EAWB_PRINTCODE = #{eawbPrintcode,jdbcType=VARCHAR},
      EAWB_HANDLETIME = #{eawbHandletime,jdbcType=TIMESTAMP},
      EAWB_DELIVER_ADDRESS = #{eawbDeliverAddress,jdbcType=VARCHAR},
      EAWB_DELIVER_CONTACT = #{eawbDeliverContact,jdbcType=VARCHAR},
      EAWB_DELIVER_PHONE = #{eawbDeliverPhone,jdbcType=VARCHAR},
      EAWB_DELIVER_MOBILE = #{eawbDeliverMobile,jdbcType=VARCHAR},
      EAWB_DELIVER_EMAIL = #{eawbDeliverEmail,jdbcType=VARCHAR},
      EAWB_DESTINATION = #{eawbDestination,jdbcType=VARCHAR},
      EAWB_SO_CODE = #{eawbSoCode,jdbcType=VARCHAR},
      EAWB_REFERENCE1 = #{eawbReference1,jdbcType=VARCHAR},
      EAWB_REFERENCE2 = #{eawbReference2,jdbcType=VARCHAR},
      EAWB_STATUS = #{eawbStatus,jdbcType=VARCHAR},
      EAWB_KEYENTRYTIME = #{eawbKeyentrytime,jdbcType=TIMESTAMP},
      EAWB_DESTCOUNTRY = #{eawbDestcountry,jdbcType=VARCHAR},
      EAWB_CUSTPRODNAME = #{eawbCustprodname,jdbcType=VARCHAR},
      EAWB_TRANSMODEID = #{eawbTransmodeid,jdbcType=VARCHAR},
      EAWB_SERVICETYPE = #{eawbServicetype,jdbcType=VARCHAR},
      EAWB_DELIVER_POSTCODE = #{eawbDeliverPostcode,jdbcType=VARCHAR},
      EAWB_CUSTDECLVAL = #{eawbCustdeclval,jdbcType=DECIMAL},
      EAWB_DESTSTATE = #{eawbDeststate,jdbcType=VARCHAR},
      EAWB_DESTCITY = #{eawbDestcity,jdbcType=VARCHAR},
      EAWB_DECLAREGROSSWEIGHT = #{eawbDeclaregrossweight,jdbcType=DECIMAL},
      PKG_PRINTCODE = #{pkgPrintcode,jdbcType=VARCHAR}
    where EAWB_SYSCODE = #{eawbSyscode,jdbcType=DECIMAL}
  </update>

  <insert id="insertBatch" parameterType="java.util.ArrayList" >
    insert into EAWBPRE (EAWB_SYSCODE, EAWB_PRINTCODE, EAWB_HANDLETIME,
    EAWB_DELIVER_ADDRESS, EAWB_DELIVER_CONTACT,
    EAWB_DELIVER_PHONE, EAWB_DELIVER_MOBILE, EAWB_DELIVER_EMAIL,
    EAWB_DESTINATION, EAWB_SO_CODE, EAWB_REFERENCE1,
    EAWB_REFERENCE2, EAWB_STATUS, EAWB_KEYENTRYTIME,
    EAWB_DESTCOUNTRY, EAWB_CUSTPRODNAME, EAWB_TRANSMODEID,
    EAWB_SERVICETYPE, EAWB_DELIVER_POSTCODE, EAWB_CUSTDECLVAL,
    EAWB_DESTSTATE, EAWB_DESTCITY, EAWB_DECLAREGROSSWEIGHT,
    PKG_PRINTCODE)
    <foreach collection="list" item="item" index="index" separator="UNION" >
      (SELECT
      #{item.eawbSyscode,jdbcType=DECIMAL}, #{item.eawbPrintcode,jdbcType=VARCHAR}, #{item.eawbHandletime,jdbcType=TIMESTAMP},
      #{item.eawbDeliverAddress,jdbcType=VARCHAR}, #{item.eawbDeliverContact,jdbcType=VARCHAR},
      #{item.eawbDeliverPhone,jdbcType=VARCHAR}, #{item.eawbDeliverMobile,jdbcType=VARCHAR}, #{item.eawbDeliverEmail,jdbcType=VARCHAR},
      #{item.eawbDestination,jdbcType=VARCHAR}, #{item.eawbSoCode,jdbcType=VARCHAR}, #{item.eawbReference1,jdbcType=VARCHAR},
      #{item.eawbReference2,jdbcType=VARCHAR}, #{item.eawbStatus,jdbcType=VARCHAR}, #{item.eawbKeyentrytime,jdbcType=TIMESTAMP},
      #{item.eawbDestcountry,jdbcType=VARCHAR}, #{item.eawbCustprodname,jdbcType=VARCHAR}, #{item.eawbTransmodeid,jdbcType=VARCHAR},
      #{item.eawbServicetype,jdbcType=VARCHAR}, #{item.eawbDeliverPostcode,jdbcType=VARCHAR}, #{item.eawbCustdeclval,jdbcType=DECIMAL},
      #{item.eawbDeststate,jdbcType=VARCHAR}, #{item.eawbDestcity,jdbcType=VARCHAR}, #{item.eawbDeclaregrossweight,jdbcType=DECIMAL},
      #{item.pkgPrintcode,jdbcType=VARCHAR}
      FROM dual)
    </foreach>
  </insert>
  <!--  <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">-->
  <update id="updateBatch" parameterType="java.util.List">
    <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
      update EAWBPRE
      <set>
        <if test="item.eawbHandletime != null" >
          EAWB_HANDLETIME = #{item.eawbHandletime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.eawbDeliverAddress != null" >
          EAWB_DELIVER_ADDRESS = #{item.eawbDeliverAddress,jdbcType=VARCHAR},
        </if>
        <if test="item.eawbDeliverContact != null" >
          EAWB_DELIVER_CONTACT = #{item.eawbDeliverContact,jdbcType=VARCHAR},
        </if>
        <if test="item.eawbDeliverPhone != null" >
          EAWB_DELIVER_PHONE = #{item.eawbDeliverPhone,jdbcType=VARCHAR},
        </if>
        <if test="item.eawbDeliverMobile != null" >
          EAWB_DELIVER_MOBILE = #{item.eawbDeliverMobile,jdbcType=VARCHAR},
        </if>
        <if test="item.eawbDeliverEmail != null" >
          EAWB_DELIVER_EMAIL = #{item.eawbDeliverEmail,jdbcType=VARCHAR},
        </if>
        <if test="item.eawbDestination != null" >
          EAWB_DESTINATION = #{item.eawbDestination,jdbcType=VARCHAR},
        </if>
        <if test="item.eawbSoCode != null" >
          EAWB_SO_CODE = #{item.eawbSoCode,jdbcType=VARCHAR},
        </if>
        <if test="item.eawbReference1 != null" >
          EAWB_REFERENCE1 = #{item.eawbReference1,jdbcType=VARCHAR},
        </if>
        <if test="item.eawbReference2 != null" >
          EAWB_REFERENCE2 = #{item.eawbReference2,jdbcType=VARCHAR},
        </if>
        <if test="item.eawbStatus != null" >
          EAWB_STATUS = #{item.eawbStatus,jdbcType=VARCHAR},
        </if>
        <if test="item.eawbKeyentrytime != null" >
          EAWB_KEYENTRYTIME = #{item.eawbKeyentrytime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.eawbDestcountry != null" >
          EAWB_DESTCOUNTRY = #{item.eawbDestcountry,jdbcType=VARCHAR},
        </if>
        <if test="item.eawbCustprodname != null" >
          EAWB_CUSTPRODNAME = #{item.eawbCustprodname,jdbcType=VARCHAR},
        </if>
        <if test="item.eawbTransmodeid != null" >
          EAWB_TRANSMODEID = #{item.eawbTransmodeid,jdbcType=VARCHAR},
        </if>
        <if test="item.eawbServicetype != null" >
          EAWB_SERVICETYPE = #{item.eawbServicetype,jdbcType=VARCHAR},
        </if>
        <if test="item.eawbDeliverPostcode != null" >
          EAWB_DELIVER_POSTCODE = #{item.eawbDeliverPostcode,jdbcType=VARCHAR},
        </if>
        <if test="item.eawbCustdeclval != null" >
          EAWB_CUSTDECLVAL = #{item.eawbCustdeclval,jdbcType=DECIMAL},
        </if>
        <if test="item.eawbDeststate != null" >
          EAWB_DESTSTATE = #{item.eawbDeststate,jdbcType=VARCHAR},
        </if>
        <if test="item.eawbDestcity != null" >
          EAWB_DESTCITY = #{item.eawbDestcity,jdbcType=VARCHAR},
        </if>
        <if test="item.eawbDeclaregrossweight != null" >
          EAWB_DECLAREGROSSWEIGHT = #{item.eawbDeclaregrossweight,jdbcType=DECIMAL},
        </if>
        <if test="item.pkgPrintcode != null" >
          PKG_PRINTCODE = #{item.pkgPrintcode,jdbcType=VARCHAR},
        </if>
      </set>
      where
      EAWB_SYSCODE=#{item.eawbSyscode}

    </foreach>
  </update>

  <select id="selectByPrintcode" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select
    <include refid="Base_Column_List" />
    from EAWBPRE
    where EAWB_PRINTCODE = #{eawbPrintcode,jdbcType=VARCHAR}
  </select>
</mapper>