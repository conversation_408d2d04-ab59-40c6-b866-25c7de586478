<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.EbaTaskMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.EbaTask" >
    <id column="TASK_ID" property="taskId" jdbcType="DECIMAL" />
    <result column="TASK_TYPE" property="taskType" jdbcType="VARCHAR" />
    <result column="TASK_SO_CODE" property="taskSoCode" jdbcType="VARCHAR" />
    <result column="TASK_START_DATE" property="taskStartDate" jdbcType="VARCHAR" />
    <result column="TASK_END_DATE" property="taskEndDate" jdbcType="VARCHAR" />
    <result column="TASK_EAD_CODE" property="taskEadCode" jdbcType="VARCHAR" />
    <result column="TASK_EAST_CODE" property="taskEastCode" jdbcType="VARCHAR" />
    <result column="TASK_EBAP_STATUS" property="taskEbapStatus" jdbcType="VARCHAR" />
    <result column="TASK_REMARK" property="taskRemark" jdbcType="VARCHAR" />
    <result column="TASK_STATUS" property="taskStatus" jdbcType="VARCHAR" />
    <result column="TASK_FIELD1" property="taskField1" jdbcType="VARCHAR" />
    <result column="TASK_FIELD2" property="taskField2" jdbcType="VARCHAR" />
    <result column="TASK_FIELD3" property="taskField3" jdbcType="VARCHAR" />
    <result column="TASK_CREATETIME" property="taskCreatetime" jdbcType="TIMESTAMP" />
    <result column="TASK_HANDLETIME" property="taskHandletime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    TASK_ID, TASK_TYPE, TASK_SO_CODE, TASK_START_DATE, TASK_END_DATE, TASK_EAD_CODE, 
    TASK_EAST_CODE, TASK_EBAP_STATUS, TASK_REMARK, TASK_STATUS, TASK_FIELD1, TASK_FIELD2, 
    TASK_FIELD3, TASK_CREATETIME, TASK_HANDLETIME
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.math.BigDecimal" >
    select 
    <include refid="Base_Column_List" />
    from INS_EBA_TASK
    where TASK_ID = #{taskId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.math.BigDecimal" >
    delete from INS_EBA_TASK
    where TASK_ID = #{taskId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.EbaTask" >
    insert into INS_EBA_TASK (TASK_ID, TASK_TYPE, TASK_SO_CODE, 
      TASK_START_DATE, TASK_END_DATE, TASK_EAD_CODE, 
      TASK_EAST_CODE, TASK_EBAP_STATUS, TASK_REMARK, 
      TASK_STATUS, TASK_FIELD1, TASK_FIELD2, 
      TASK_FIELD3, TASK_CREATETIME, TASK_HANDLETIME
      )
    values (#{taskId,jdbcType=DECIMAL}, #{taskType,jdbcType=VARCHAR}, #{taskSoCode,jdbcType=VARCHAR}, 
      #{taskStartDate,jdbcType=VARCHAR}, #{taskEndDate,jdbcType=VARCHAR}, #{taskEadCode,jdbcType=VARCHAR}, 
      #{taskEastCode,jdbcType=VARCHAR}, #{taskEbapStatus,jdbcType=VARCHAR}, #{taskRemark,jdbcType=VARCHAR}, 
      #{taskStatus,jdbcType=VARCHAR}, #{taskField1,jdbcType=VARCHAR}, #{taskField2,jdbcType=VARCHAR}, 
      #{taskField3,jdbcType=VARCHAR}, #{taskCreatetime,jdbcType=TIMESTAMP}, #{taskHandletime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.EbaTask" >
    insert into INS_EBA_TASK
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="taskId != null" >
        TASK_ID,
      </if>
      <if test="taskType != null" >
        TASK_TYPE,
      </if>
      <if test="taskSoCode != null" >
        TASK_SO_CODE,
      </if>
      <if test="taskStartDate != null" >
        TASK_START_DATE,
      </if>
      <if test="taskEndDate != null" >
        TASK_END_DATE,
      </if>
      <if test="taskEadCode != null" >
        TASK_EAD_CODE,
      </if>
      <if test="taskEastCode != null" >
        TASK_EAST_CODE,
      </if>
      <if test="taskEbapStatus != null" >
        TASK_EBAP_STATUS,
      </if>
      <if test="taskRemark != null" >
        TASK_REMARK,
      </if>
      <if test="taskStatus != null" >
        TASK_STATUS,
      </if>
      <if test="taskField1 != null" >
        TASK_FIELD1,
      </if>
      <if test="taskField2 != null" >
        TASK_FIELD2,
      </if>
      <if test="taskField3 != null" >
        TASK_FIELD3,
      </if>
      <if test="taskCreatetime != null" >
        TASK_CREATETIME,
      </if>
      <if test="taskHandletime != null" >
        TASK_HANDLETIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="taskId != null" >
        #{taskId,jdbcType=DECIMAL},
      </if>
      <if test="taskType != null" >
        #{taskType,jdbcType=VARCHAR},
      </if>
      <if test="taskSoCode != null" >
        #{taskSoCode,jdbcType=VARCHAR},
      </if>
      <if test="taskStartDate != null" >
        #{taskStartDate,jdbcType=VARCHAR},
      </if>
      <if test="taskEndDate != null" >
        #{taskEndDate,jdbcType=VARCHAR},
      </if>
      <if test="taskEadCode != null" >
        #{taskEadCode,jdbcType=VARCHAR},
      </if>
      <if test="taskEastCode != null" >
        #{taskEastCode,jdbcType=VARCHAR},
      </if>
      <if test="taskEbapStatus != null" >
        #{taskEbapStatus,jdbcType=VARCHAR},
      </if>
      <if test="taskRemark != null" >
        #{taskRemark,jdbcType=VARCHAR},
      </if>
      <if test="taskStatus != null" >
        #{taskStatus,jdbcType=VARCHAR},
      </if>
      <if test="taskField1 != null" >
        #{taskField1,jdbcType=VARCHAR},
      </if>
      <if test="taskField2 != null" >
        #{taskField2,jdbcType=VARCHAR},
      </if>
      <if test="taskField3 != null" >
        #{taskField3,jdbcType=VARCHAR},
      </if>
      <if test="taskCreatetime != null" >
        #{taskCreatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="taskHandletime != null" >
        #{taskHandletime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.EbaTask" >
    update INS_EBA_TASK
    <set >
      <if test="taskType != null" >
        TASK_TYPE = #{taskType,jdbcType=VARCHAR},
      </if>
      <if test="taskSoCode != null" >
        TASK_SO_CODE = #{taskSoCode,jdbcType=VARCHAR},
      </if>
      <if test="taskStartDate != null" >
        TASK_START_DATE = #{taskStartDate,jdbcType=VARCHAR},
      </if>
      <if test="taskEndDate != null" >
        TASK_END_DATE = #{taskEndDate,jdbcType=VARCHAR},
      </if>
      <if test="taskEadCode != null" >
        TASK_EAD_CODE = #{taskEadCode,jdbcType=VARCHAR},
      </if>
      <if test="taskEastCode != null" >
        TASK_EAST_CODE = #{taskEastCode,jdbcType=VARCHAR},
      </if>
      <if test="taskEbapStatus != null" >
        TASK_EBAP_STATUS = #{taskEbapStatus,jdbcType=VARCHAR},
      </if>
      <if test="taskRemark != null" >
        TASK_REMARK = #{taskRemark,jdbcType=VARCHAR},
      </if>
      <if test="taskStatus != null" >
        TASK_STATUS = #{taskStatus,jdbcType=VARCHAR},
      </if>
      <if test="taskField1 != null" >
        TASK_FIELD1 = #{taskField1,jdbcType=VARCHAR},
      </if>
      <if test="taskField2 != null" >
        TASK_FIELD2 = #{taskField2,jdbcType=VARCHAR},
      </if>
      <if test="taskField3 != null" >
        TASK_FIELD3 = #{taskField3,jdbcType=VARCHAR},
      </if>
      <if test="taskCreatetime != null" >
        TASK_CREATETIME = #{taskCreatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="taskHandletime != null" >
        TASK_HANDLETIME = #{taskHandletime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where TASK_ID = #{taskId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.EbaTask" >
    update INS_EBA_TASK
    set TASK_TYPE = #{taskType,jdbcType=VARCHAR},
      TASK_SO_CODE = #{taskSoCode,jdbcType=VARCHAR},
      TASK_START_DATE = #{taskStartDate,jdbcType=VARCHAR},
      TASK_END_DATE = #{taskEndDate,jdbcType=VARCHAR},
      TASK_EAD_CODE = #{taskEadCode,jdbcType=VARCHAR},
      TASK_EAST_CODE = #{taskEastCode,jdbcType=VARCHAR},
      TASK_EBAP_STATUS = #{taskEbapStatus,jdbcType=VARCHAR},
      TASK_REMARK = #{taskRemark,jdbcType=VARCHAR},
      TASK_STATUS = #{taskStatus,jdbcType=VARCHAR},
      TASK_FIELD1 = #{taskField1,jdbcType=VARCHAR},
      TASK_FIELD2 = #{taskField2,jdbcType=VARCHAR},
      TASK_FIELD3 = #{taskField3,jdbcType=VARCHAR},
      TASK_CREATETIME = #{taskCreatetime,jdbcType=TIMESTAMP},
      TASK_HANDLETIME = #{taskHandletime,jdbcType=TIMESTAMP}
    where TASK_ID = #{taskId,jdbcType=DECIMAL}
  </update>
  <select id="selectByCondition" resultMap="BaseResultMap" parameterType="com.sinoair.billing.domain.vo.query.CeosQuery" >
    select
    <include refid="Base_Column_List" />
    from INS_EBA_TASK
    where TASK_STATUS = 'PENDING'
    and TASK_TYPE = #{handleType,jdbcType=VARCHAR}
  </select>

  <select id="selectByTaskType" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select
    <include refid="Base_Column_List" />
    from INS_EBA_TASK
    where TASK_TYPE = #{taskType,jdbcType=VARCHAR}
  </select>
</mapper>