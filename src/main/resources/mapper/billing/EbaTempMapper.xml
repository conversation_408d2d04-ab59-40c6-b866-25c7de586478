<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.EbaTempMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.EbaTemp" >
    <id column="EAWB_PRINTCODE" property="eawbPrintcode" jdbcType="VARCHAR" />
    <result column="EAWB_SERVICETYPE_ORIGINAL" property="eawbServicetypeOriginal" jdbcType="VARCHAR" />
    <result column="EAST_CODE" property="eastCode" jdbcType="VARCHAR" />
    <result column="QA_PUSHTIME" property="qaPushtime" jdbcType="TIMESTAMP" />
    <result column="EP_GROUP" property="epGroup" jdbcType="VARCHAR" />
    <result column="EP_KEY" property="epKey" jdbcType="VARCHAR" />
    <result column="EP_VALUE" property="epValue" jdbcType="VARCHAR" />
    <result column="EP_ID" property="epId" jdbcType="VARCHAR" />
    <result column="EP_TYPE" property="epType" jdbcType="VARCHAR" />
    <result column="DEST_COUNTRY" property="destCountry" jdbcType="VARCHAR" />

  </resultMap>
  <sql id="Base_Column_List" >
    EAWB_PRINTCODE, EAWB_SERVICETYPE_ORIGINAL, EAST_CODE, QA_PUSHTIME,
    EP_GROUP, EP_KEY, EP_VALUE, EP_ID, EP_TYPE, DEST_COUNTRY
  </sql>
  <select id="selectEbaTemp" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from temp_nhb_eba
  </select>

  <delete id="deleteBatchTemp"  parameterType="java.util.List">
    delete from temp_nhb_eba where EAWB_PRINTCODE in
    <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
      #{item.eawbPrintcode,jdbcType=VARCHAR}
    </foreach>

  </delete>


</mapper>