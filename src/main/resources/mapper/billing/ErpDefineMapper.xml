<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.ErpDefineMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.ErpDefine" >
    <id column="ERP_CODE" property="erpCode" jdbcType="VARCHAR" />
    <result column="ERP_NAME" property="erpName" jdbcType="VARCHAR" />
    <result column="ERP_MODE" property="erpMode" jdbcType="VARCHAR" />
    <result column="ERP_PLATFORM" property="erpPlatform" jdbcType="VARCHAR" />
    <result column="ERP_STATUS" property="erpStatus" jdbcType="VARCHAR" />
    <result column="HANDLETIME" property="handletime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    ERP_CODE, ERP_NAME, ERP_MODE, ERP_PLATFORM, ERP_STATUS, HANDLETIME
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from ERP_DEFINE
    where ERP_CODE = #{erpCode,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from ERP_DEFINE
    where ERP_CODE = #{erpCode,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.ErpDefine" >
    insert into ERP_DEFINE (ERP_CODE, ERP_NAME, ERP_MODE, 
      ERP_PLATFORM, ERP_STATUS, HANDLETIME
      )
    values (#{erpCode,jdbcType=VARCHAR}, #{erpName,jdbcType=VARCHAR}, #{erpMode,jdbcType=VARCHAR}, 
      #{erpPlatform,jdbcType=VARCHAR}, #{erpStatus,jdbcType=VARCHAR}, #{handletime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.ErpDefine" >
    insert into ERP_DEFINE
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="erpCode != null" >
        ERP_CODE,
      </if>
      <if test="erpName != null" >
        ERP_NAME,
      </if>
      <if test="erpMode != null" >
        ERP_MODE,
      </if>
      <if test="erpPlatform != null" >
        ERP_PLATFORM,
      </if>
      <if test="erpStatus != null" >
        ERP_STATUS,
      </if>
      <if test="handletime != null" >
        HANDLETIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="erpCode != null" >
        #{erpCode,jdbcType=VARCHAR},
      </if>
      <if test="erpName != null" >
        #{erpName,jdbcType=VARCHAR},
      </if>
      <if test="erpMode != null" >
        #{erpMode,jdbcType=VARCHAR},
      </if>
      <if test="erpPlatform != null" >
        #{erpPlatform,jdbcType=VARCHAR},
      </if>
      <if test="erpStatus != null" >
        #{erpStatus,jdbcType=VARCHAR},
      </if>
      <if test="handletime != null" >
        #{handletime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.ErpDefine" >
    update ERP_DEFINE
    <set >
      <if test="erpName != null" >
        ERP_NAME = #{erpName,jdbcType=VARCHAR},
      </if>
      <if test="erpMode != null" >
        ERP_MODE = #{erpMode,jdbcType=VARCHAR},
      </if>
      <if test="erpPlatform != null" >
        ERP_PLATFORM = #{erpPlatform,jdbcType=VARCHAR},
      </if>
      <if test="erpStatus != null" >
        ERP_STATUS = #{erpStatus,jdbcType=VARCHAR},
      </if>
      <if test="handletime != null" >
        HANDLETIME = #{handletime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ERP_CODE = #{erpCode,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.ErpDefine" >
    update ERP_DEFINE
    set ERP_NAME = #{erpName,jdbcType=VARCHAR},
      ERP_MODE = #{erpMode,jdbcType=VARCHAR},
      ERP_PLATFORM = #{erpPlatform,jdbcType=VARCHAR},
      ERP_STATUS = #{erpStatus,jdbcType=VARCHAR},
      HANDLETIME = #{handletime,jdbcType=TIMESTAMP}
    where ERP_CODE = #{erpCode,jdbcType=VARCHAR}
  </update>

  <select id="selectErpList" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from ERP_DEFINE
    where ERP_STATUS = 'ON'
    order by erp_sort
  </select>
</mapper>