<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.billing.dao.billing.ExpressActivityDefineMapper">
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.ExpressActivityDefine">
    <id column="EAD_SYSCODE" jdbcType="DECIMAL" property="eadSyscode" />
    <result column="EAD_CODE" jdbcType="VARCHAR" property="eadCode" />
    <result column="EAD_NAME" jdbcType="VARCHAR" property="eadName" />
    <result column="EAD_STATUS" jdbcType="VARCHAR" property="eadStatus" />
    <result column="EAD_TYPE" jdbcType="VARCHAR" property="eadType" />
    <result column="EAD_UNIT" jdbcType="VARCHAR" property="eadUnit" />
    <result column="EAD_ACTIVITY" jdbcType="VARCHAR" property="eadActivity" />
    <result column="EAD_DISPLAYNAME" jdbcType="VARCHAR" property="eadDisplayname" />
    <result column="EAD_PUBLIC" jdbcType="VARCHAR" property="eadPublic" />
  </resultMap>
  <sql id="Base_Column_List">
    EAD_SYSCODE, EAD_CODE, EAD_NAME, EAD_STATUS, EAD_TYPE, EAD_UNIT, EAD_ACTIVITY, EAD_DISPLAYNAME, 
    EAD_PUBLIC
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Short" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from EXPRESSACTIVITYDEFINE
    where EAD_SYSCODE = #{eadSyscode,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Short">
    delete from EXPRESSACTIVITYDEFINE
    where EAD_SYSCODE = #{eadSyscode,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.ExpressActivityDefine">
    insert into EXPRESSACTIVITYDEFINE (EAD_SYSCODE, EAD_CODE, EAD_NAME, 
      EAD_STATUS, EAD_TYPE, EAD_UNIT, 
      EAD_ACTIVITY, EAD_DISPLAYNAME, EAD_PUBLIC
      )
    values (#{eadSyscode,jdbcType=DECIMAL}, #{eadCode,jdbcType=VARCHAR}, #{eadName,jdbcType=VARCHAR}, 
      #{eadStatus,jdbcType=VARCHAR}, #{eadType,jdbcType=VARCHAR}, #{eadUnit,jdbcType=VARCHAR}, 
      #{eadActivity,jdbcType=VARCHAR}, #{eadDisplayname,jdbcType=VARCHAR}, #{eadPublic,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.ExpressActivityDefine">
    insert into EXPRESSACTIVITYDEFINE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="eadSyscode != null">
        EAD_SYSCODE,
      </if>
      <if test="eadCode != null">
        EAD_CODE,
      </if>
      <if test="eadName != null">
        EAD_NAME,
      </if>
      <if test="eadStatus != null">
        EAD_STATUS,
      </if>
      <if test="eadType != null">
        EAD_TYPE,
      </if>
      <if test="eadUnit != null">
        EAD_UNIT,
      </if>
      <if test="eadActivity != null">
        EAD_ACTIVITY,
      </if>
      <if test="eadDisplayname != null">
        EAD_DISPLAYNAME,
      </if>
      <if test="eadPublic != null">
        EAD_PUBLIC,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="eadSyscode != null">
        #{eadSyscode,jdbcType=DECIMAL},
      </if>
      <if test="eadCode != null">
        #{eadCode,jdbcType=VARCHAR},
      </if>
      <if test="eadName != null">
        #{eadName,jdbcType=VARCHAR},
      </if>
      <if test="eadStatus != null">
        #{eadStatus,jdbcType=VARCHAR},
      </if>
      <if test="eadType != null">
        #{eadType,jdbcType=VARCHAR},
      </if>
      <if test="eadUnit != null">
        #{eadUnit,jdbcType=VARCHAR},
      </if>
      <if test="eadActivity != null">
        #{eadActivity,jdbcType=VARCHAR},
      </if>
      <if test="eadDisplayname != null">
        #{eadDisplayname,jdbcType=VARCHAR},
      </if>
      <if test="eadPublic != null">
        #{eadPublic,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.ExpressActivityDefine">
    update EXPRESSACTIVITYDEFINE
    <set>
      <if test="eadCode != null">
        EAD_CODE = #{eadCode,jdbcType=VARCHAR},
      </if>
      <if test="eadName != null">
        EAD_NAME = #{eadName,jdbcType=VARCHAR},
      </if>
      <if test="eadStatus != null">
        EAD_STATUS = #{eadStatus,jdbcType=VARCHAR},
      </if>
      <if test="eadType != null">
        EAD_TYPE = #{eadType,jdbcType=VARCHAR},
      </if>
      <if test="eadUnit != null">
        EAD_UNIT = #{eadUnit,jdbcType=VARCHAR},
      </if>
      <if test="eadActivity != null">
        EAD_ACTIVITY = #{eadActivity,jdbcType=VARCHAR},
      </if>
      <if test="eadDisplayname != null">
        EAD_DISPLAYNAME = #{eadDisplayname,jdbcType=VARCHAR},
      </if>
      <if test="eadPublic != null">
        EAD_PUBLIC = #{eadPublic,jdbcType=VARCHAR},
      </if>
    </set>
    where EAD_SYSCODE = #{eadSyscode,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.ExpressActivityDefine">
    update EXPRESSACTIVITYDEFINE
    set EAD_CODE = #{eadCode,jdbcType=VARCHAR},
      EAD_NAME = #{eadName,jdbcType=VARCHAR},
      EAD_STATUS = #{eadStatus,jdbcType=VARCHAR},
      EAD_TYPE = #{eadType,jdbcType=VARCHAR},
      EAD_UNIT = #{eadUnit,jdbcType=VARCHAR},
      EAD_ACTIVITY = #{eadActivity,jdbcType=VARCHAR},
      EAD_DISPLAYNAME = #{eadDisplayname,jdbcType=VARCHAR},
      EAD_PUBLIC = #{eadPublic,jdbcType=VARCHAR}
    where EAD_SYSCODE = #{eadSyscode,jdbcType=DECIMAL}
  </update>

  <select id="selectEadCodeName" resultType="java.util.Map">
     select EAD_CODE,EAD_NAME
     from EXPRESSACTIVITYDEFINE
     where EAD_STATUS = 'ON'
     order by EAD_NAME
  </select>
</mapper>