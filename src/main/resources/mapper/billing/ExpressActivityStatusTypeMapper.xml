<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.billing.dao.billing.ExpressActivityStatusTypeMapper">
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.ExpressActivityStatusType">
    <id column="EAST_SYSCODE" jdbcType="DECIMAL" property="eastSyscode" />
    <result column="EAST_CODE" jdbcType="VARCHAR" property="eastCode" />
    <result column="EASC_CODE" jdbcType="VARCHAR" property="eascCode" />
    <result column="EAST_NAME" jdbcType="VARCHAR" property="eastName" />
    <result column="EAST_STATUS" jdbcType="VARCHAR" property="eastStatus" />
    <result column="EAA_CODE" jdbcType="VARCHAR" property="eaaCode" />
    <result column="EAST_HOLD" jdbcType="VARCHAR" property="eastHold" />
    <result column="DESCRIBE_EN" jdbcType="VARCHAR" property="describeEn" />
    <result column="EAST_SEQ" jdbcType="DECIMAL" property="eastSeq" />
  </resultMap>
  <sql id="Base_Column_List">
    EAST_SYSCODE, EAST_CODE, EASC_CODE, EAST_NAME, EAST_STATUS, EAA_CODE, EAST_HOLD, 
    DESCRIBE_EN, EAST_SEQ
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Short" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from EXPRESSACTIVITYSTATUSTYPE
    where EAST_SYSCODE = #{eastSyscode,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Short">
    delete from EXPRESSACTIVITYSTATUSTYPE
    where EAST_SYSCODE = #{eastSyscode,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.ExpressActivityStatusType">
    insert into EXPRESSACTIVITYSTATUSTYPE (EAST_SYSCODE, EAST_CODE, EASC_CODE, 
      EAST_NAME, EAST_STATUS, EAA_CODE, 
      EAST_HOLD, DESCRIBE_EN, EAST_SEQ
      )
    values (#{eastSyscode,jdbcType=DECIMAL}, #{eastCode,jdbcType=VARCHAR}, #{eascCode,jdbcType=VARCHAR}, 
      #{eastName,jdbcType=VARCHAR}, #{eastStatus,jdbcType=VARCHAR}, #{eaaCode,jdbcType=VARCHAR}, 
      #{eastHold,jdbcType=VARCHAR}, #{describeEn,jdbcType=VARCHAR}, #{eastSeq,jdbcType=DECIMAL}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.ExpressActivityStatusType">
    insert into EXPRESSACTIVITYSTATUSTYPE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="eastSyscode != null">
        EAST_SYSCODE,
      </if>
      <if test="eastCode != null">
        EAST_CODE,
      </if>
      <if test="eascCode != null">
        EASC_CODE,
      </if>
      <if test="eastName != null">
        EAST_NAME,
      </if>
      <if test="eastStatus != null">
        EAST_STATUS,
      </if>
      <if test="eaaCode != null">
        EAA_CODE,
      </if>
      <if test="eastHold != null">
        EAST_HOLD,
      </if>
      <if test="describeEn != null">
        DESCRIBE_EN,
      </if>
      <if test="eastSeq != null">
        EAST_SEQ,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="eastSyscode != null">
        #{eastSyscode,jdbcType=DECIMAL},
      </if>
      <if test="eastCode != null">
        #{eastCode,jdbcType=VARCHAR},
      </if>
      <if test="eascCode != null">
        #{eascCode,jdbcType=VARCHAR},
      </if>
      <if test="eastName != null">
        #{eastName,jdbcType=VARCHAR},
      </if>
      <if test="eastStatus != null">
        #{eastStatus,jdbcType=VARCHAR},
      </if>
      <if test="eaaCode != null">
        #{eaaCode,jdbcType=VARCHAR},
      </if>
      <if test="eastHold != null">
        #{eastHold,jdbcType=VARCHAR},
      </if>
      <if test="describeEn != null">
        #{describeEn,jdbcType=VARCHAR},
      </if>
      <if test="eastSeq != null">
        #{eastSeq,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.ExpressActivityStatusType">
    update EXPRESSACTIVITYSTATUSTYPE
    <set>
      <if test="eastCode != null">
        EAST_CODE = #{eastCode,jdbcType=VARCHAR},
      </if>
      <if test="eascCode != null">
        EASC_CODE = #{eascCode,jdbcType=VARCHAR},
      </if>
      <if test="eastName != null">
        EAST_NAME = #{eastName,jdbcType=VARCHAR},
      </if>
      <if test="eastStatus != null">
        EAST_STATUS = #{eastStatus,jdbcType=VARCHAR},
      </if>
      <if test="eaaCode != null">
        EAA_CODE = #{eaaCode,jdbcType=VARCHAR},
      </if>
      <if test="eastHold != null">
        EAST_HOLD = #{eastHold,jdbcType=VARCHAR},
      </if>
      <if test="describeEn != null">
        DESCRIBE_EN = #{describeEn,jdbcType=VARCHAR},
      </if>
      <if test="eastSeq != null">
        EAST_SEQ = #{eastSeq,jdbcType=DECIMAL},
      </if>
    </set>
    where EAST_SYSCODE = #{eastSyscode,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.ExpressActivityStatusType">
    update EXPRESSACTIVITYSTATUSTYPE
    set EAST_CODE = #{eastCode,jdbcType=VARCHAR},
      EASC_CODE = #{eascCode,jdbcType=VARCHAR},
      EAST_NAME = #{eastName,jdbcType=VARCHAR},
      EAST_STATUS = #{eastStatus,jdbcType=VARCHAR},
      EAA_CODE = #{eaaCode,jdbcType=VARCHAR},
      EAST_HOLD = #{eastHold,jdbcType=VARCHAR},
      DESCRIBE_EN = #{describeEn,jdbcType=VARCHAR},
      EAST_SEQ = #{eastSeq,jdbcType=DECIMAL}
    where EAST_SYSCODE = #{eastSyscode,jdbcType=DECIMAL}
  </update>

  <select id="selectEastCodeName" resultType="java.util.Map">
    select EAST_CODE,EAST_NAME
    FROM  EXPRESSACTIVITYSTATUSTYPE
    where EAST_STATUS = 'ON'
    order by EAST_NAME
  </select>
</mapper>