<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.ExpressAirWayBillCeosMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.ExpressAirWayBillCeos" >
    <id column="EAWB_SYSCODE" property="eawbSyscode" jdbcType="DECIMAL" />
    <result column="EAWB_CODE" property="eawbCode" jdbcType="VARCHAR" />
    <result column="EAWB_PRINTCODE" property="eawbPrintcode" jdbcType="VARCHAR" />
    <result column="EAWB_E_ID_HANDLER" property="eawbEIdHandler" jdbcType="DECIMAL" />
    <result column="EST_CODE" property="estCode" jdbcType="VARCHAR" />
    <result column="EAWB_HANDLETIME" property="eawbHandletime" jdbcType="TIMESTAMP" />
    <result column="EAWB_SHIPPER_ACCOUNT" property="eawbShipperAccount" jdbcType="VARCHAR" />
    <result column="EAWB_PICKUP_ADDRESS" property="eawbPickupAddress" jdbcType="VARCHAR" />
    <result column="EAWB_PICKUP_POSTCODE" property="eawbPickupPostcode" jdbcType="VARCHAR" />
    <result column="EAWB_PICKUP_CONTACT" property="eawbPickupContact" jdbcType="VARCHAR" />
    <result column="EAWB_PICKUP_PHONE" property="eawbPickupPhone" jdbcType="VARCHAR" />
    <result column="EAWB_CONSIGNEE_ACCOUNT" property="eawbConsigneeAccount" jdbcType="VARCHAR" />
    <result column="EAWB_DELIVER_ADDRESS" property="eawbDeliverAddress" jdbcType="VARCHAR" />
    <result column="EAWB_DELIVER_POSTCODE" property="eawbDeliverPostcode" jdbcType="VARCHAR" />
    <result column="EAWB_DELIVER_CONTACT" property="eawbDeliverContact" jdbcType="VARCHAR" />
    <result column="EAWB_DELIVER_PHONE" property="eawbDeliverPhone" jdbcType="VARCHAR" />
    <result column="EAWB_DEPARTURE" property="eawbDeparture" jdbcType="VARCHAR" />
    <result column="EAWB_DESTINATION" property="eawbDestination" jdbcType="VARCHAR" />
    <result column="EAWB_PRODUCTNAME" property="eawbProductname" jdbcType="VARCHAR" />
    <result column="EAWB_TOTALPIECES" property="eawbTotalpieces" jdbcType="DECIMAL" />
    <result column="EAWB_TOTALVOLUME" property="eawbTotalvolume" jdbcType="DECIMAL" />
    <result column="EAWB_TOTALGROSSWEIGHT" property="eawbTotalgrossweight" jdbcType="DECIMAL" />
    <result column="EAWB_TOTALCHARGEABLEWEIGHT" property="eawbTotalchargeableweight" jdbcType="DECIMAL" />
    <result column="EAWB_DECLAREVOLUME" property="eawbDeclarevolume" jdbcType="DECIMAL" />
    <result column="EAWB_DECLAREGROSSWEIGHT" property="eawbDeclaregrossweight" jdbcType="DECIMAL" />
    <result column="EAWB_DECLARECHARGEABLE" property="eawbDeclarechargeable" jdbcType="DECIMAL" />
    <result column="EAWB_PAYMENTMODE" property="eawbPaymentmode" jdbcType="VARCHAR" />
    <result column="EAWB_THIRDPARTY_ACCOUNT" property="eawbThirdpartyAccount" jdbcType="VARCHAR" />
    <result column="CT_CODE" property="ctCode" jdbcType="VARCHAR" />
    <result column="EAWB_CURRENCYRATE" property="eawbCurrencyrate" jdbcType="VARCHAR" />
    <result column="EAWB_TOTALRMB" property="eawbTotalrmb" jdbcType="DECIMAL" />
    <result column="EAWB_TOTALFC" property="eawbTotalfc" jdbcType="DECIMAL" />
    <result column="SAC_ID" property="sacId" jdbcType="VARCHAR" />
    <result column="EAWB_SAC_CODE" property="eawbSacCode" jdbcType="VARCHAR" />
    <result column="EB_CODE" property="ebCode" jdbcType="VARCHAR" />
    <result column="EAWB_SO_CODE" property="eawbSoCode" jdbcType="VARCHAR" />
    <result column="EAWB_SHIPPER_ACCOUNTNAME" property="eawbShipperAccountname" jdbcType="VARCHAR" />
    <result column="EAWB_CONSIGNEE_ACCOUNTNAME" property="eawbConsigneeAccountname" jdbcType="VARCHAR" />
    <result column="EAWB_PICKUP_BLOCK" property="eawbPickupBlock" jdbcType="VARCHAR" />
    <result column="EAWB_PICKUP_TIME" property="eawbPickupTime" jdbcType="TIMESTAMP" />
    <result column="EAWB_FREIGHTCHARGE" property="eawbFreightcharge" jdbcType="DECIMAL" />
    <result column="EAWB_INCIDENTALCHARGE" property="eawbIncidentalcharge" jdbcType="DECIMAL" />
    <result column="EAWB_INSURANCECHARGE" property="eawbInsurancecharge" jdbcType="DECIMAL" />
    <result column="EAWB_PPCC" property="eawbPpcc" jdbcType="VARCHAR" />
    <result column="EAWB_DECLAREVALUE" property="eawbDeclarevalue" jdbcType="DECIMAL" />
    <result column="EAWB_REFERENCE1" property="eawbReference1" jdbcType="VARCHAR" />
    <result column="EAWB_REFERENCE2" property="eawbReference2" jdbcType="VARCHAR" />
    <result column="EAWB_STANDARDFREIGHTPRICE" property="eawbStandardfreightprice" jdbcType="DECIMAL" />
    <result column="EAWB_STATUS" property="eawbStatus" jdbcType="VARCHAR" />
    <result column="EAWB_PICKUPBYCONSIGNEE" property="eawbPickupbyconsignee" jdbcType="VARCHAR" />
    <result column="EAWB_DELIVERYATHOLIDAY" property="eawbDeliveryatholiday" jdbcType="VARCHAR" />
    <result column="EAWB_INNER" property="eawbInner" jdbcType="VARCHAR" />
    <result column="EAWB_INBOUND_SAC_ID" property="eawbInboundSacId" jdbcType="VARCHAR" />
    <result column="EAWB_OUTBOUND_SAC_ID" property="eawbOutboundSacId" jdbcType="VARCHAR" />
    <result column="EAWB_PRODUCTDECLARE" property="eawbProductdeclare" jdbcType="VARCHAR" />
    <result column="EAWB_SERVICEREQUIREMENT" property="eawbServicerequirement" jdbcType="VARCHAR" />
    <result column="EAWB_REFERENCE3" property="eawbReference3" jdbcType="VARCHAR" />
    <result column="EAWB_C_CODE" property="eawbCCode" jdbcType="VARCHAR" />
    <result column="EAWB_TIME_LIMIT" property="eawbTimeLimit" jdbcType="TIMESTAMP" />
    <result column="EAWB_COLLECTPAYMENTFORGOODS" property="eawbCollectpaymentforgoods" jdbcType="DECIMAL" />
    <result column="EAWB_SELFINSURANCE" property="eawbSelfinsurance" jdbcType="VARCHAR" />
    <result column="EAWB_KEYENTRYTIME" property="eawbKeyentrytime" jdbcType="TIMESTAMP" />
    <result column="EAWB_PRESERVATION" property="eawbPreservation" jdbcType="VARCHAR" />
    <result column="EAWB_INSURANCESERVICE" property="eawbInsuranceservice" jdbcType="VARCHAR" />
    <result column="EAWB_AGENT_CODE" property="eawbAgentCode" jdbcType="VARCHAR" />
    <result column="EAWB_BUSINESSMODE" property="eawbBusinessmode" jdbcType="VARCHAR" />
    <result column="EAWB_PRE_FREIGHTPRICE" property="eawbPreFreightprice" jdbcType="DECIMAL" />
    <result column="EAWB_SENDVOICEREQUEST" property="eawbSendvoicerequest" jdbcType="CHAR" />
    <result column="CBC_SHIP_CODE" property="cbcShipCode" jdbcType="VARCHAR" />
    <result column="EAWB_ROUTE" property="eawbRoute" jdbcType="VARCHAR" />
    <result column="EAWB_DEPARTCITY" property="eawbDepartcity" jdbcType="VARCHAR" />
    <result column="EAWB_DEPARTSTATE" property="eawbDepartstate" jdbcType="VARCHAR" />
    <result column="EAWB_DEPARTCOUNTRY" property="eawbDepartcountry" jdbcType="VARCHAR" />
    <result column="EAWB_DESTCITY" property="eawbDestcity" jdbcType="VARCHAR" />
    <result column="EAWB_DESTSTATE" property="eawbDeststate" jdbcType="VARCHAR" />
    <result column="EAWB_DESTCOUNTRY" property="eawbDestcountry" jdbcType="VARCHAR" />
    <result column="EAWB_TYPE" property="eawbType" jdbcType="VARCHAR" />
    <result column="EAWB_CUSTPRODNAME" property="eawbCustprodname" jdbcType="VARCHAR" />
    <result column="EAWB_QUANTITY" property="eawbQuantity" jdbcType="DECIMAL" />
    <result column="EAWB_CUSTDECLVAL" property="eawbCustdeclval" jdbcType="DECIMAL" />
    <result column="EAWB_CUSTCURRENCY" property="eawbCustcurrency" jdbcType="VARCHAR" />
    <result column="EAWB_DECLCURRENCY" property="eawbDeclcurrency" jdbcType="VARCHAR" />
    <result column="EAWB_CONSIGNMENTNO" property="eawbConsignmentno" jdbcType="VARCHAR" />
    <result column="EAWB_KJTYPE" property="eawbKjtype" jdbcType="VARCHAR" />
    <result column="EAWB_IETYPE" property="eawbIetype" jdbcType="VARCHAR" />
    <result column="B_CODE" property="bCode" jdbcType="VARCHAR" />
    <result column="EAWB_FUELCHARGE" property="eawbFuelcharge" jdbcType="DECIMAL" />
    <result column="EAWB_INSUTYPE" property="eawbInsutype" jdbcType="VARCHAR" />
    <result column="EAWB_INSUAMOUNTTYPE" property="eawbInsuamounttype" jdbcType="VARCHAR" />
    <result column="EAWB_INSUAMOUNT" property="eawbInsuamount" jdbcType="DECIMAL" />
    <result column="EAWB_PRE_FUELPRICE" property="eawbPreFuelprice" jdbcType="DECIMAL" />
    <result column="HANDSET_CARGO" property="handsetCargo" jdbcType="VARCHAR" />
    <result column="INFOXML" property="infoxml" jdbcType="VARCHAR" />
    <result column="EAWB_DISCOUNTVALUE" property="eawbDiscountvalue" jdbcType="DECIMAL" />
    <result column="EAWB_TRANSMODEID" property="eawbTransmodeid" jdbcType="VARCHAR" />
    <result column="EAWB_PRODUCTTYPE" property="eawbProducttype" jdbcType="VARCHAR" />
    <result column="EAWB_ORIGINCITY" property="eawbOrigincity" jdbcType="VARCHAR" />
    <result column="EAWB_SHIPPER_CACCOUNTNAME" property="eawbShipperCaccountname" jdbcType="VARCHAR" />
    <result column="EAWB_DELIVER_FAX" property="eawbDeliverFax" jdbcType="VARCHAR" />
    <result column="EAWB_SPECIFICATION" property="eawbSpecification" jdbcType="VARCHAR" />
    <result column="EAWB_CCTYPE" property="eawbCctype" jdbcType="VARCHAR" />
    <result column="EAWB_CUSTREGISTRATIONCODE" property="eawbCustregistrationcode" jdbcType="VARCHAR" />
    <result column="EAWB_CUSTREGISTRATIONAME" property="eawbCustregistrationame" jdbcType="VARCHAR" />
    <result column="EAWB_ENTRUSTCODE" property="eawbEntrustcode" jdbcType="VARCHAR" />
    <result column="EAWB_HSCODE" property="eawbHscode" jdbcType="VARCHAR" />
    <result column="EAWB_CUSTPRODENNAME" property="eawbCustprodenname" jdbcType="VARCHAR" />
    <result column="EAWB_OPERATESTATUS" property="eawbOperatestatus" jdbcType="VARCHAR" />
    <result column="EAWB_UNIT" property="eawbUnit" jdbcType="VARCHAR" />
    <result column="EAWB_UNITCODE" property="eawbUnitcode" jdbcType="VARCHAR" />
    <result column="EAWB_CHECKSTATUS" property="eawbCheckstatus" jdbcType="VARCHAR" />
    <result column="EAWB_SERVICETYPE" property="eawbServicetype" jdbcType="VARCHAR" />
    <result column="MAWB_CODE" property="mawbCode" jdbcType="VARCHAR" />
    <result column="FLIGHT_NO" property="flightNo" jdbcType="VARCHAR" />
    <result column="CUSTOM_TYPE" property="customType" jdbcType="VARCHAR" />
    <result column="EAWB_ECOMMERCE" property="eawbEcommerce" jdbcType="VARCHAR" />
    <result column="EAWB_TAXCODE" property="eawbTaxcode" jdbcType="VARCHAR" />
    <result column="EAWB_DELIVER_INDENTITYCARDNO" property="eawbDeliverIndentitycardno" jdbcType="VARCHAR" />
    <result column="CUSTOMS_STATUS" property="customsStatus" jdbcType="VARCHAR" />
    <result column="CSEF_SYSCODE" property="csefSyscode" jdbcType="DECIMAL" />
    <result column="ETCOPY" property="etcopy" jdbcType="DECIMAL" />
    <result column="EAWB_TRACK" property="eawbTrack" jdbcType="VARCHAR" />
    <result column="EAWB_DELIVER_EMAIL" property="eawbDeliverEmail" jdbcType="VARCHAR" />
    <result column="EAWB_PARTITION" property="eawbPartition" jdbcType="VARCHAR" />
    <result column="EAWB_DELIVERY_POSTCODE_CORRECT" property="eawbDeliveryPostcodeCorrect" jdbcType="VARCHAR" />
    <result column="EAWB_CHANGE_LABEL_STATUS" property="eawbChangeLabelStatus" jdbcType="VARCHAR" />
    <result column="EAWB_REFUND_WANGWANG_ID" property="eawbRefundWangwangId" jdbcType="VARCHAR" />
    <result column="EAWB_REFUND_NAME" property="eawbRefundName" jdbcType="VARCHAR" />
    <result column="EAWB_REFUND_PHONE" property="eawbRefundPhone" jdbcType="VARCHAR" />
    <result column="EAWB_REFUND_MOBILE" property="eawbRefundMobile" jdbcType="VARCHAR" />
    <result column="EAWB_REFUND_EMAIL" property="eawbRefundEmail" jdbcType="VARCHAR" />
    <result column="EAWB_REFUND_COUNTRY" property="eawbRefundCountry" jdbcType="VARCHAR" />
    <result column="EAWB_REFUND_PRIVINCE" property="eawbRefundPrivince" jdbcType="VARCHAR" />
    <result column="EAWB_REFUND_CITY" property="eawbRefundCity" jdbcType="VARCHAR" />
    <result column="EAWB_REFUND_DISTRICT" property="eawbRefundDistrict" jdbcType="VARCHAR" />
    <result column="EAWB_REFUND_STREET" property="eawbRefundStreet" jdbcType="VARCHAR" />
    <result column="EAWB_REFUND_ZIPCODE" property="eawbRefundZipcode" jdbcType="VARCHAR" />
    <result column="EAWB_UNDELIVERY_OPTION" property="eawbUndeliveryOption" jdbcType="VARCHAR" />
    <result column="EAWB_CARRIERNAME" property="eawbCarriername" jdbcType="VARCHAR" />
    <result column="EAWB_CARRIERNO" property="eawbCarrierno" jdbcType="VARCHAR" />
    <result column="ORDER_CODE_IN" property="orderCodeIn" jdbcType="VARCHAR" />
    <result column="ORDER_SYSCODE_IN" property="orderSyscodeIn" jdbcType="DECIMAL" />
    <result column="ORDER_CODE_OUT" property="orderCodeOut" jdbcType="VARCHAR" />
    <result column="ORDER_SYSCODE_OUT" property="orderSyscodeOut" jdbcType="DECIMAL" />
    <result column="CUSTOMER_ORDER_CODE" property="customerOrderCode" jdbcType="VARCHAR" />
    <result column="EAWB_SERVICETYPE_ORIGINAL" property="eawbServicetypeOriginal" jdbcType="VARCHAR" />
    <result column="EAWB_LENGTH" property="eawbLength" jdbcType="DECIMAL" />
    <result column="EAWB_WIDTH" property="eawbWidth" jdbcType="DECIMAL" />
    <result column="EAWB_HEIGHT" property="eawbHeight" jdbcType="DECIMAL" />
    <result column="EAWB_DELIVER_MOBILE" property="eawbDeliverMobile" jdbcType="VARCHAR" />
    <result column="EAWB_COD" property="eawbCod" jdbcType="VARCHAR" />
    <result column="EAWB_CODVALUE" property="eawbCodvalue" jdbcType="DECIMAL" />
    <result column="EAWB_CODCURRENCY" property="eawbCodcurrency" jdbcType="VARCHAR" />
    <result column="EAWB_REFUND_REFERENCE" property="eawbRefundReference" jdbcType="VARCHAR" />
    <result column="EAWB_TRANSMODEID_ORIGINAL" property="eawbTransmodeidOriginal" jdbcType="VARCHAR" />
    <result column="EAWB_REFUND_SUPPLIER" property="eawbRefundSupplier" jdbcType="CHAR" />
    <result column="EAWB_REFUND_REFERENCE1" property="eawbRefundReference1" jdbcType="CHAR" />
    <result column="EAWB_REFUND_REFERENCE2" property="eawbRefundReference2" jdbcType="CHAR" />
    <result column="EAWB_REFUND_REFERENCE3" property="eawbRefundReference3" jdbcType="VARCHAR" />
    <result column="EAWB_REFUND_ADDRESS" property="eawbRefundAddress" jdbcType="VARCHAR" />
    <result column="REFUND_STATUS" property="refundStatus" jdbcType="VARCHAR" />
    <result column="EAWB_DELIVER_STREET" property="eawbDeliverStreet" jdbcType="VARCHAR" />
    <result column="EAWB_NEXT_OPT_CENTER" property="eawbNextOptCenter" jdbcType="VARCHAR" />
    <result column="EAWB_PRE_OPT_CENTER" property="eawbPreOptCenter" jdbcType="VARCHAR" />
    <result column="EAWB_TRUNK_CODE" property="eawbTrunkCode" jdbcType="VARCHAR" />
    <result column="EAWB_CHANNEL_CODE" property="eawbChannelCode" jdbcType="VARCHAR" />
    <result column="EAWB_DELIVER_DISTRICT" property="eawbDeliverDistrict" jdbcType="VARCHAR" />
    <result column="EAWB_PICKUP_DISTRICT" property="eawbPickupDistrict" jdbcType="VARCHAR" />
    <result column="EAWB_SENDER_ADDRESS" property="eawbSenderAddress" jdbcType="VARCHAR" />
    <result column="EAWB_SORTCODE" property="eawbSortcode" jdbcType="VARCHAR" />
    <result column="EAWB_CONSIGNEE_LATITUDE" property="eawbConsigneeLatitude" jdbcType="VARCHAR" />
    <result column="EAWB_CONSIGNEE_LONGITUDE" property="eawbConsigneeLongitude" jdbcType="VARCHAR" />
    <result column="EAWB_FIRSTMILE" property="eawbFirstmile" jdbcType="VARCHAR" />
    <result column="EAWB_PAYMENTID" property="eawbPaymentid" jdbcType="VARCHAR" />
    <result column="EAWB_PAYMENTEMAIL" property="eawbPaymentemail" jdbcType="VARCHAR" />
    <result column="EAWB_PAYMENTCONTACTNAME" property="eawbPaymentcontactname" jdbcType="VARCHAR" />
    <result column="EAWB_PAYMENTPHONENUMBER" property="eawbPaymentphonenumber" jdbcType="VARCHAR" />
    <result column="EAWB_SELLERTAXNUMBER" property="eawbSellertaxnumber" jdbcType="VARCHAR" />
    <result column="EAWB_INVOICENUMBER" property="eawbInvoicenumber" jdbcType="VARCHAR" />
    <result column="EAWB_PRODUCTURL" property="eawbProducturl" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    EAWB_SYSCODE, EAWB_CODE, EAWB_PRINTCODE, EAWB_E_ID_HANDLER, EST_CODE, EAWB_HANDLETIME, 
    EAWB_SHIPPER_ACCOUNT, EAWB_PICKUP_ADDRESS, EAWB_PICKUP_POSTCODE, EAWB_PICKUP_CONTACT, 
    EAWB_PICKUP_PHONE, EAWB_CONSIGNEE_ACCOUNT, EAWB_DELIVER_ADDRESS, EAWB_DELIVER_POSTCODE, 
    EAWB_DELIVER_CONTACT, EAWB_DELIVER_PHONE, EAWB_DEPARTURE, EAWB_DESTINATION, EAWB_PRODUCTNAME, 
    EAWB_TOTALPIECES, EAWB_TOTALVOLUME, EAWB_TOTALGROSSWEIGHT, EAWB_TOTALCHARGEABLEWEIGHT, 
    EAWB_DECLAREVOLUME, EAWB_DECLAREGROSSWEIGHT, EAWB_DECLARECHARGEABLE, EAWB_PAYMENTMODE, 
    EAWB_THIRDPARTY_ACCOUNT, CT_CODE, EAWB_CURRENCYRATE, EAWB_TOTALRMB, EAWB_TOTALFC, 
    SAC_ID, EAWB_SAC_CODE, EB_CODE, EAWB_SO_CODE, EAWB_SHIPPER_ACCOUNTNAME, EAWB_CONSIGNEE_ACCOUNTNAME, 
    EAWB_PICKUP_BLOCK, EAWB_PICKUP_TIME, EAWB_FREIGHTCHARGE, EAWB_INCIDENTALCHARGE, EAWB_INSURANCECHARGE, 
    EAWB_PPCC, EAWB_DECLAREVALUE, EAWB_REFERENCE1, EAWB_REFERENCE2, EAWB_STANDARDFREIGHTPRICE, 
    EAWB_STATUS, EAWB_PICKUPBYCONSIGNEE, EAWB_DELIVERYATHOLIDAY, EAWB_INNER, EAWB_INBOUND_SAC_ID, 
    EAWB_OUTBOUND_SAC_ID, EAWB_PRODUCTDECLARE, EAWB_SERVICEREQUIREMENT, EAWB_REFERENCE3, 
    EAWB_C_CODE, EAWB_TIME_LIMIT, EAWB_COLLECTPAYMENTFORGOODS, EAWB_SELFINSURANCE, EAWB_KEYENTRYTIME, 
    EAWB_PRESERVATION, EAWB_INSURANCESERVICE, EAWB_AGENT_CODE, EAWB_BUSINESSMODE, EAWB_PRE_FREIGHTPRICE, 
    EAWB_SENDVOICEREQUEST, CBC_SHIP_CODE, EAWB_ROUTE, EAWB_DEPARTCITY, EAWB_DEPARTSTATE, 
    EAWB_DEPARTCOUNTRY, EAWB_DESTCITY, EAWB_DESTSTATE, EAWB_DESTCOUNTRY, EAWB_TYPE, EAWB_CUSTPRODNAME, 
    EAWB_QUANTITY, EAWB_CUSTDECLVAL, EAWB_CUSTCURRENCY, EAWB_DECLCURRENCY, EAWB_CONSIGNMENTNO, 
    EAWB_KJTYPE, EAWB_IETYPE, B_CODE, EAWB_FUELCHARGE, EAWB_INSUTYPE, EAWB_INSUAMOUNTTYPE, 
    EAWB_INSUAMOUNT, EAWB_PRE_FUELPRICE, HANDSET_CARGO, INFOXML, EAWB_DISCOUNTVALUE, 
    EAWB_TRANSMODEID, EAWB_PRODUCTTYPE, EAWB_ORIGINCITY, EAWB_SHIPPER_CACCOUNTNAME, EAWB_DELIVER_FAX, 
    EAWB_SPECIFICATION, EAWB_CCTYPE, EAWB_CUSTREGISTRATIONCODE, EAWB_CUSTREGISTRATIONAME, 
    EAWB_ENTRUSTCODE, EAWB_HSCODE, EAWB_CUSTPRODENNAME, EAWB_OPERATESTATUS, EAWB_UNIT, 
    EAWB_UNITCODE, EAWB_CHECKSTATUS, EAWB_SERVICETYPE, MAWB_CODE, FLIGHT_NO, CUSTOM_TYPE, 
    EAWB_ECOMMERCE, EAWB_TAXCODE, EAWB_DELIVER_INDENTITYCARDNO, CUSTOMS_STATUS, CSEF_SYSCODE, 
    ETCOPY, EAWB_TRACK, EAWB_DELIVER_EMAIL, EAWB_PARTITION, EAWB_DELIVERY_POSTCODE_CORRECT, 
    EAWB_CHANGE_LABEL_STATUS, EAWB_REFUND_WANGWANG_ID, EAWB_REFUND_NAME, EAWB_REFUND_PHONE, 
    EAWB_REFUND_MOBILE, EAWB_REFUND_EMAIL, EAWB_REFUND_COUNTRY, EAWB_REFUND_PRIVINCE, 
    EAWB_REFUND_CITY, EAWB_REFUND_DISTRICT, EAWB_REFUND_STREET, EAWB_REFUND_ZIPCODE, 
    EAWB_UNDELIVERY_OPTION, EAWB_CARRIERNAME, EAWB_CARRIERNO, ORDER_CODE_IN, ORDER_SYSCODE_IN, 
    ORDER_CODE_OUT, ORDER_SYSCODE_OUT, CUSTOMER_ORDER_CODE, EAWB_SERVICETYPE_ORIGINAL, 
    EAWB_LENGTH, EAWB_WIDTH, EAWB_HEIGHT, EAWB_DELIVER_MOBILE, EAWB_COD, EAWB_CODVALUE, 
    EAWB_CODCURRENCY, EAWB_REFUND_REFERENCE, EAWB_TRANSMODEID_ORIGINAL, EAWB_REFUND_SUPPLIER, 
    EAWB_REFUND_REFERENCE1, EAWB_REFUND_REFERENCE2, EAWB_REFUND_REFERENCE3, EAWB_REFUND_ADDRESS, 
    REFUND_STATUS, EAWB_DELIVER_STREET, EAWB_NEXT_OPT_CENTER, EAWB_PRE_OPT_CENTER, EAWB_TRUNK_CODE, 
    EAWB_CHANNEL_CODE, EAWB_DELIVER_DISTRICT, EAWB_PICKUP_DISTRICT, EAWB_SENDER_ADDRESS, 
    EAWB_SORTCODE, EAWB_CONSIGNEE_LATITUDE, EAWB_CONSIGNEE_LONGITUDE, EAWB_FIRSTMILE, 
    EAWB_PAYMENTID, EAWB_PAYMENTEMAIL, EAWB_PAYMENTCONTACTNAME, EAWB_PAYMENTPHONENUMBER, 
    EAWB_SELLERTAXNUMBER, EAWB_INVOICENUMBER, EAWB_PRODUCTURL
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Short" >
    select 
    <include refid="Base_Column_List" />
    from EXPRESSAIRWAYBILL_CEOS
    where EAWB_SYSCODE = #{eawbSyscode,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Short" >
    delete from EXPRESSAIRWAYBILL_CEOS
    where EAWB_SYSCODE = #{eawbSyscode,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.ExpressAirWayBillCeos" >
    insert into EXPRESSAIRWAYBILL_CEOS (EAWB_SYSCODE, EAWB_CODE, EAWB_PRINTCODE, 
      EAWB_E_ID_HANDLER, EST_CODE, EAWB_HANDLETIME, 
      EAWB_SHIPPER_ACCOUNT, EAWB_PICKUP_ADDRESS, 
      EAWB_PICKUP_POSTCODE, EAWB_PICKUP_CONTACT, 
      EAWB_PICKUP_PHONE, EAWB_CONSIGNEE_ACCOUNT, 
      EAWB_DELIVER_ADDRESS, EAWB_DELIVER_POSTCODE, 
      EAWB_DELIVER_CONTACT, EAWB_DELIVER_PHONE, EAWB_DEPARTURE, 
      EAWB_DESTINATION, EAWB_PRODUCTNAME, EAWB_TOTALPIECES, 
      EAWB_TOTALVOLUME, EAWB_TOTALGROSSWEIGHT, 
      EAWB_TOTALCHARGEABLEWEIGHT, EAWB_DECLAREVOLUME, 
      EAWB_DECLAREGROSSWEIGHT, EAWB_DECLARECHARGEABLE, 
      EAWB_PAYMENTMODE, EAWB_THIRDPARTY_ACCOUNT, 
      CT_CODE, EAWB_CURRENCYRATE, EAWB_TOTALRMB, 
      EAWB_TOTALFC, SAC_ID, EAWB_SAC_CODE, 
      EB_CODE, EAWB_SO_CODE, EAWB_SHIPPER_ACCOUNTNAME, 
      EAWB_CONSIGNEE_ACCOUNTNAME, EAWB_PICKUP_BLOCK, 
      EAWB_PICKUP_TIME, EAWB_FREIGHTCHARGE, EAWB_INCIDENTALCHARGE, 
      EAWB_INSURANCECHARGE, EAWB_PPCC, EAWB_DECLAREVALUE, 
      EAWB_REFERENCE1, EAWB_REFERENCE2, EAWB_STANDARDFREIGHTPRICE, 
      EAWB_STATUS, EAWB_PICKUPBYCONSIGNEE, EAWB_DELIVERYATHOLIDAY, 
      EAWB_INNER, EAWB_INBOUND_SAC_ID, EAWB_OUTBOUND_SAC_ID, 
      EAWB_PRODUCTDECLARE, EAWB_SERVICEREQUIREMENT, 
      EAWB_REFERENCE3, EAWB_C_CODE, EAWB_TIME_LIMIT, 
      EAWB_COLLECTPAYMENTFORGOODS, EAWB_SELFINSURANCE, 
      EAWB_KEYENTRYTIME, EAWB_PRESERVATION, EAWB_INSURANCESERVICE, 
      EAWB_AGENT_CODE, EAWB_BUSINESSMODE, EAWB_PRE_FREIGHTPRICE, 
      EAWB_SENDVOICEREQUEST, CBC_SHIP_CODE, EAWB_ROUTE, 
      EAWB_DEPARTCITY, EAWB_DEPARTSTATE, EAWB_DEPARTCOUNTRY, 
      EAWB_DESTCITY, EAWB_DESTSTATE, EAWB_DESTCOUNTRY, 
      EAWB_TYPE, EAWB_CUSTPRODNAME, EAWB_QUANTITY, 
      EAWB_CUSTDECLVAL, EAWB_CUSTCURRENCY, EAWB_DECLCURRENCY, 
      EAWB_CONSIGNMENTNO, EAWB_KJTYPE, EAWB_IETYPE, 
      B_CODE, EAWB_FUELCHARGE, EAWB_INSUTYPE, 
      EAWB_INSUAMOUNTTYPE, EAWB_INSUAMOUNT, EAWB_PRE_FUELPRICE, 
      HANDSET_CARGO, INFOXML, EAWB_DISCOUNTVALUE, 
      EAWB_TRANSMODEID, EAWB_PRODUCTTYPE, EAWB_ORIGINCITY, 
      EAWB_SHIPPER_CACCOUNTNAME, EAWB_DELIVER_FAX, 
      EAWB_SPECIFICATION, EAWB_CCTYPE, EAWB_CUSTREGISTRATIONCODE, 
      EAWB_CUSTREGISTRATIONAME, EAWB_ENTRUSTCODE, 
      EAWB_HSCODE, EAWB_CUSTPRODENNAME, EAWB_OPERATESTATUS, 
      EAWB_UNIT, EAWB_UNITCODE, EAWB_CHECKSTATUS, 
      EAWB_SERVICETYPE, MAWB_CODE, FLIGHT_NO, 
      CUSTOM_TYPE, EAWB_ECOMMERCE, EAWB_TAXCODE, 
      EAWB_DELIVER_INDENTITYCARDNO, CUSTOMS_STATUS, 
      CSEF_SYSCODE, ETCOPY, EAWB_TRACK, 
      EAWB_DELIVER_EMAIL, EAWB_PARTITION, EAWB_DELIVERY_POSTCODE_CORRECT, 
      EAWB_CHANGE_LABEL_STATUS, EAWB_REFUND_WANGWANG_ID, 
      EAWB_REFUND_NAME, EAWB_REFUND_PHONE, EAWB_REFUND_MOBILE, 
      EAWB_REFUND_EMAIL, EAWB_REFUND_COUNTRY, EAWB_REFUND_PRIVINCE, 
      EAWB_REFUND_CITY, EAWB_REFUND_DISTRICT, EAWB_REFUND_STREET, 
      EAWB_REFUND_ZIPCODE, EAWB_UNDELIVERY_OPTION, 
      EAWB_CARRIERNAME, EAWB_CARRIERNO, ORDER_CODE_IN, 
      ORDER_SYSCODE_IN, ORDER_CODE_OUT, ORDER_SYSCODE_OUT, 
      CUSTOMER_ORDER_CODE, EAWB_SERVICETYPE_ORIGINAL, 
      EAWB_LENGTH, EAWB_WIDTH, EAWB_HEIGHT, 
      EAWB_DELIVER_MOBILE, EAWB_COD, EAWB_CODVALUE, 
      EAWB_CODCURRENCY, EAWB_REFUND_REFERENCE, EAWB_TRANSMODEID_ORIGINAL, 
      EAWB_REFUND_SUPPLIER, EAWB_REFUND_REFERENCE1, EAWB_REFUND_REFERENCE2, 
      EAWB_REFUND_REFERENCE3, EAWB_REFUND_ADDRESS, 
      REFUND_STATUS, EAWB_DELIVER_STREET, EAWB_NEXT_OPT_CENTER, 
      EAWB_PRE_OPT_CENTER, EAWB_TRUNK_CODE, EAWB_CHANNEL_CODE, 
      EAWB_DELIVER_DISTRICT, EAWB_PICKUP_DISTRICT, 
      EAWB_SENDER_ADDRESS, EAWB_SORTCODE, EAWB_CONSIGNEE_LATITUDE, 
      EAWB_CONSIGNEE_LONGITUDE, EAWB_FIRSTMILE, 
      EAWB_PAYMENTID, EAWB_PAYMENTEMAIL, EAWB_PAYMENTCONTACTNAME, 
      EAWB_PAYMENTPHONENUMBER, EAWB_SELLERTAXNUMBER, 
      EAWB_INVOICENUMBER, EAWB_PRODUCTURL)
    values (#{eawbSyscode,jdbcType=DECIMAL}, #{eawbCode,jdbcType=VARCHAR}, #{eawbPrintcode,jdbcType=VARCHAR}, 
      #{eawbEIdHandler,jdbcType=DECIMAL}, #{estCode,jdbcType=VARCHAR}, #{eawbHandletime,jdbcType=TIMESTAMP}, 
      #{eawbShipperAccount,jdbcType=VARCHAR}, #{eawbPickupAddress,jdbcType=VARCHAR}, 
      #{eawbPickupPostcode,jdbcType=VARCHAR}, #{eawbPickupContact,jdbcType=VARCHAR}, 
      #{eawbPickupPhone,jdbcType=VARCHAR}, #{eawbConsigneeAccount,jdbcType=VARCHAR}, 
      #{eawbDeliverAddress,jdbcType=VARCHAR}, #{eawbDeliverPostcode,jdbcType=VARCHAR}, 
      #{eawbDeliverContact,jdbcType=VARCHAR}, #{eawbDeliverPhone,jdbcType=VARCHAR}, #{eawbDeparture,jdbcType=VARCHAR}, 
      #{eawbDestination,jdbcType=VARCHAR}, #{eawbProductname,jdbcType=VARCHAR}, #{eawbTotalpieces,jdbcType=DECIMAL}, 
      #{eawbTotalvolume,jdbcType=DECIMAL}, #{eawbTotalgrossweight,jdbcType=DECIMAL}, 
      #{eawbTotalchargeableweight,jdbcType=DECIMAL}, #{eawbDeclarevolume,jdbcType=DECIMAL}, 
      #{eawbDeclaregrossweight,jdbcType=DECIMAL}, #{eawbDeclarechargeable,jdbcType=DECIMAL}, 
      #{eawbPaymentmode,jdbcType=VARCHAR}, #{eawbThirdpartyAccount,jdbcType=VARCHAR}, 
      #{ctCode,jdbcType=VARCHAR}, #{eawbCurrencyrate,jdbcType=VARCHAR}, #{eawbTotalrmb,jdbcType=DECIMAL}, 
      #{eawbTotalfc,jdbcType=DECIMAL}, #{sacId,jdbcType=VARCHAR}, #{eawbSacCode,jdbcType=VARCHAR}, 
      #{ebCode,jdbcType=VARCHAR}, #{eawbSoCode,jdbcType=VARCHAR}, #{eawbShipperAccountname,jdbcType=VARCHAR}, 
      #{eawbConsigneeAccountname,jdbcType=VARCHAR}, #{eawbPickupBlock,jdbcType=VARCHAR}, 
      #{eawbPickupTime,jdbcType=TIMESTAMP}, #{eawbFreightcharge,jdbcType=DECIMAL}, #{eawbIncidentalcharge,jdbcType=DECIMAL}, 
      #{eawbInsurancecharge,jdbcType=DECIMAL}, #{eawbPpcc,jdbcType=VARCHAR}, #{eawbDeclarevalue,jdbcType=DECIMAL}, 
      #{eawbReference1,jdbcType=VARCHAR}, #{eawbReference2,jdbcType=VARCHAR}, #{eawbStandardfreightprice,jdbcType=DECIMAL}, 
      #{eawbStatus,jdbcType=VARCHAR}, #{eawbPickupbyconsignee,jdbcType=VARCHAR}, #{eawbDeliveryatholiday,jdbcType=VARCHAR}, 
      #{eawbInner,jdbcType=VARCHAR}, #{eawbInboundSacId,jdbcType=VARCHAR}, #{eawbOutboundSacId,jdbcType=VARCHAR}, 
      #{eawbProductdeclare,jdbcType=VARCHAR}, #{eawbServicerequirement,jdbcType=VARCHAR}, 
      #{eawbReference3,jdbcType=VARCHAR}, #{eawbCCode,jdbcType=VARCHAR}, #{eawbTimeLimit,jdbcType=TIMESTAMP}, 
      #{eawbCollectpaymentforgoods,jdbcType=DECIMAL}, #{eawbSelfinsurance,jdbcType=VARCHAR}, 
      #{eawbKeyentrytime,jdbcType=TIMESTAMP}, #{eawbPreservation,jdbcType=VARCHAR}, #{eawbInsuranceservice,jdbcType=VARCHAR}, 
      #{eawbAgentCode,jdbcType=VARCHAR}, #{eawbBusinessmode,jdbcType=VARCHAR}, #{eawbPreFreightprice,jdbcType=DECIMAL}, 
      #{eawbSendvoicerequest,jdbcType=CHAR}, #{cbcShipCode,jdbcType=VARCHAR}, #{eawbRoute,jdbcType=VARCHAR}, 
      #{eawbDepartcity,jdbcType=VARCHAR}, #{eawbDepartstate,jdbcType=VARCHAR}, #{eawbDepartcountry,jdbcType=VARCHAR}, 
      #{eawbDestcity,jdbcType=VARCHAR}, #{eawbDeststate,jdbcType=VARCHAR}, #{eawbDestcountry,jdbcType=VARCHAR}, 
      #{eawbType,jdbcType=VARCHAR}, #{eawbCustprodname,jdbcType=VARCHAR}, #{eawbQuantity,jdbcType=DECIMAL}, 
      #{eawbCustdeclval,jdbcType=DECIMAL}, #{eawbCustcurrency,jdbcType=VARCHAR}, #{eawbDeclcurrency,jdbcType=VARCHAR}, 
      #{eawbConsignmentno,jdbcType=VARCHAR}, #{eawbKjtype,jdbcType=VARCHAR}, #{eawbIetype,jdbcType=VARCHAR}, 
      #{bCode,jdbcType=VARCHAR}, #{eawbFuelcharge,jdbcType=DECIMAL}, #{eawbInsutype,jdbcType=VARCHAR}, 
      #{eawbInsuamounttype,jdbcType=VARCHAR}, #{eawbInsuamount,jdbcType=DECIMAL}, #{eawbPreFuelprice,jdbcType=DECIMAL}, 
      #{handsetCargo,jdbcType=VARCHAR}, #{infoxml,jdbcType=VARCHAR}, #{eawbDiscountvalue,jdbcType=DECIMAL}, 
      #{eawbTransmodeid,jdbcType=VARCHAR}, #{eawbProducttype,jdbcType=VARCHAR}, #{eawbOrigincity,jdbcType=VARCHAR}, 
      #{eawbShipperCaccountname,jdbcType=VARCHAR}, #{eawbDeliverFax,jdbcType=VARCHAR}, 
      #{eawbSpecification,jdbcType=VARCHAR}, #{eawbCctype,jdbcType=VARCHAR}, #{eawbCustregistrationcode,jdbcType=VARCHAR}, 
      #{eawbCustregistrationame,jdbcType=VARCHAR}, #{eawbEntrustcode,jdbcType=VARCHAR}, 
      #{eawbHscode,jdbcType=VARCHAR}, #{eawbCustprodenname,jdbcType=VARCHAR}, #{eawbOperatestatus,jdbcType=VARCHAR}, 
      #{eawbUnit,jdbcType=VARCHAR}, #{eawbUnitcode,jdbcType=VARCHAR}, #{eawbCheckstatus,jdbcType=VARCHAR}, 
      #{eawbServicetype,jdbcType=VARCHAR}, #{mawbCode,jdbcType=VARCHAR}, #{flightNo,jdbcType=VARCHAR}, 
      #{customType,jdbcType=VARCHAR}, #{eawbEcommerce,jdbcType=VARCHAR}, #{eawbTaxcode,jdbcType=VARCHAR}, 
      #{eawbDeliverIndentitycardno,jdbcType=VARCHAR}, #{customsStatus,jdbcType=VARCHAR}, 
      #{csefSyscode,jdbcType=DECIMAL}, #{etcopy,jdbcType=DECIMAL}, #{eawbTrack,jdbcType=VARCHAR}, 
      #{eawbDeliverEmail,jdbcType=VARCHAR}, #{eawbPartition,jdbcType=VARCHAR}, #{eawbDeliveryPostcodeCorrect,jdbcType=VARCHAR}, 
      #{eawbChangeLabelStatus,jdbcType=VARCHAR}, #{eawbRefundWangwangId,jdbcType=VARCHAR}, 
      #{eawbRefundName,jdbcType=VARCHAR}, #{eawbRefundPhone,jdbcType=VARCHAR}, #{eawbRefundMobile,jdbcType=VARCHAR}, 
      #{eawbRefundEmail,jdbcType=VARCHAR}, #{eawbRefundCountry,jdbcType=VARCHAR}, #{eawbRefundPrivince,jdbcType=VARCHAR}, 
      #{eawbRefundCity,jdbcType=VARCHAR}, #{eawbRefundDistrict,jdbcType=VARCHAR}, #{eawbRefundStreet,jdbcType=VARCHAR}, 
      #{eawbRefundZipcode,jdbcType=VARCHAR}, #{eawbUndeliveryOption,jdbcType=VARCHAR}, 
      #{eawbCarriername,jdbcType=VARCHAR}, #{eawbCarrierno,jdbcType=VARCHAR}, #{orderCodeIn,jdbcType=VARCHAR}, 
      #{orderSyscodeIn,jdbcType=DECIMAL}, #{orderCodeOut,jdbcType=VARCHAR}, #{orderSyscodeOut,jdbcType=DECIMAL}, 
      #{customerOrderCode,jdbcType=VARCHAR}, #{eawbServicetypeOriginal,jdbcType=VARCHAR}, 
      #{eawbLength,jdbcType=DECIMAL}, #{eawbWidth,jdbcType=DECIMAL}, #{eawbHeight,jdbcType=DECIMAL}, 
      #{eawbDeliverMobile,jdbcType=VARCHAR}, #{eawbCod,jdbcType=VARCHAR}, #{eawbCodvalue,jdbcType=DECIMAL}, 
      #{eawbCodcurrency,jdbcType=VARCHAR}, #{eawbRefundReference,jdbcType=VARCHAR}, #{eawbTransmodeidOriginal,jdbcType=VARCHAR}, 
      #{eawbRefundSupplier,jdbcType=CHAR}, #{eawbRefundReference1,jdbcType=CHAR}, #{eawbRefundReference2,jdbcType=CHAR}, 
      #{eawbRefundReference3,jdbcType=VARCHAR}, #{eawbRefundAddress,jdbcType=VARCHAR}, 
      #{refundStatus,jdbcType=VARCHAR}, #{eawbDeliverStreet,jdbcType=VARCHAR}, #{eawbNextOptCenter,jdbcType=VARCHAR}, 
      #{eawbPreOptCenter,jdbcType=VARCHAR}, #{eawbTrunkCode,jdbcType=VARCHAR}, #{eawbChannelCode,jdbcType=VARCHAR}, 
      #{eawbDeliverDistrict,jdbcType=VARCHAR}, #{eawbPickupDistrict,jdbcType=VARCHAR}, 
      #{eawbSenderAddress,jdbcType=VARCHAR}, #{eawbSortcode,jdbcType=VARCHAR}, #{eawbConsigneeLatitude,jdbcType=VARCHAR}, 
      #{eawbConsigneeLongitude,jdbcType=VARCHAR}, #{eawbFirstmile,jdbcType=VARCHAR}, 
      #{eawbPaymentid,jdbcType=VARCHAR}, #{eawbPaymentemail,jdbcType=VARCHAR}, #{eawbPaymentcontactname,jdbcType=VARCHAR}, 
      #{eawbPaymentphonenumber,jdbcType=VARCHAR}, #{eawbSellertaxnumber,jdbcType=VARCHAR}, 
      #{eawbInvoicenumber,jdbcType=VARCHAR}, #{eawbProducturl,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.ExpressAirWayBillCeos" >
    insert into EXPRESSAIRWAYBILL_CEOS
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="eawbSyscode != null" >
        EAWB_SYSCODE,
      </if>
      <if test="eawbCode != null" >
        EAWB_CODE,
      </if>
      <if test="eawbPrintcode != null" >
        EAWB_PRINTCODE,
      </if>
      <if test="eawbEIdHandler != null" >
        EAWB_E_ID_HANDLER,
      </if>
      <if test="estCode != null" >
        EST_CODE,
      </if>
      <if test="eawbHandletime != null" >
        EAWB_HANDLETIME,
      </if>
      <if test="eawbShipperAccount != null" >
        EAWB_SHIPPER_ACCOUNT,
      </if>
      <if test="eawbPickupAddress != null" >
        EAWB_PICKUP_ADDRESS,
      </if>
      <if test="eawbPickupPostcode != null" >
        EAWB_PICKUP_POSTCODE,
      </if>
      <if test="eawbPickupContact != null" >
        EAWB_PICKUP_CONTACT,
      </if>
      <if test="eawbPickupPhone != null" >
        EAWB_PICKUP_PHONE,
      </if>
      <if test="eawbConsigneeAccount != null" >
        EAWB_CONSIGNEE_ACCOUNT,
      </if>
      <if test="eawbDeliverAddress != null" >
        EAWB_DELIVER_ADDRESS,
      </if>
      <if test="eawbDeliverPostcode != null" >
        EAWB_DELIVER_POSTCODE,
      </if>
      <if test="eawbDeliverContact != null" >
        EAWB_DELIVER_CONTACT,
      </if>
      <if test="eawbDeliverPhone != null" >
        EAWB_DELIVER_PHONE,
      </if>
      <if test="eawbDeparture != null" >
        EAWB_DEPARTURE,
      </if>
      <if test="eawbDestination != null" >
        EAWB_DESTINATION,
      </if>
      <if test="eawbProductname != null" >
        EAWB_PRODUCTNAME,
      </if>
      <if test="eawbTotalpieces != null" >
        EAWB_TOTALPIECES,
      </if>
      <if test="eawbTotalvolume != null" >
        EAWB_TOTALVOLUME,
      </if>
      <if test="eawbTotalgrossweight != null" >
        EAWB_TOTALGROSSWEIGHT,
      </if>
      <if test="eawbTotalchargeableweight != null" >
        EAWB_TOTALCHARGEABLEWEIGHT,
      </if>
      <if test="eawbDeclarevolume != null" >
        EAWB_DECLAREVOLUME,
      </if>
      <if test="eawbDeclaregrossweight != null" >
        EAWB_DECLAREGROSSWEIGHT,
      </if>
      <if test="eawbDeclarechargeable != null" >
        EAWB_DECLARECHARGEABLE,
      </if>
      <if test="eawbPaymentmode != null" >
        EAWB_PAYMENTMODE,
      </if>
      <if test="eawbThirdpartyAccount != null" >
        EAWB_THIRDPARTY_ACCOUNT,
      </if>
      <if test="ctCode != null" >
        CT_CODE,
      </if>
      <if test="eawbCurrencyrate != null" >
        EAWB_CURRENCYRATE,
      </if>
      <if test="eawbTotalrmb != null" >
        EAWB_TOTALRMB,
      </if>
      <if test="eawbTotalfc != null" >
        EAWB_TOTALFC,
      </if>
      <if test="sacId != null" >
        SAC_ID,
      </if>
      <if test="eawbSacCode != null" >
        EAWB_SAC_CODE,
      </if>
      <if test="ebCode != null" >
        EB_CODE,
      </if>
      <if test="eawbSoCode != null" >
        EAWB_SO_CODE,
      </if>
      <if test="eawbShipperAccountname != null" >
        EAWB_SHIPPER_ACCOUNTNAME,
      </if>
      <if test="eawbConsigneeAccountname != null" >
        EAWB_CONSIGNEE_ACCOUNTNAME,
      </if>
      <if test="eawbPickupBlock != null" >
        EAWB_PICKUP_BLOCK,
      </if>
      <if test="eawbPickupTime != null" >
        EAWB_PICKUP_TIME,
      </if>
      <if test="eawbFreightcharge != null" >
        EAWB_FREIGHTCHARGE,
      </if>
      <if test="eawbIncidentalcharge != null" >
        EAWB_INCIDENTALCHARGE,
      </if>
      <if test="eawbInsurancecharge != null" >
        EAWB_INSURANCECHARGE,
      </if>
      <if test="eawbPpcc != null" >
        EAWB_PPCC,
      </if>
      <if test="eawbDeclarevalue != null" >
        EAWB_DECLAREVALUE,
      </if>
      <if test="eawbReference1 != null" >
        EAWB_REFERENCE1,
      </if>
      <if test="eawbReference2 != null" >
        EAWB_REFERENCE2,
      </if>
      <if test="eawbStandardfreightprice != null" >
        EAWB_STANDARDFREIGHTPRICE,
      </if>
      <if test="eawbStatus != null" >
        EAWB_STATUS,
      </if>
      <if test="eawbPickupbyconsignee != null" >
        EAWB_PICKUPBYCONSIGNEE,
      </if>
      <if test="eawbDeliveryatholiday != null" >
        EAWB_DELIVERYATHOLIDAY,
      </if>
      <if test="eawbInner != null" >
        EAWB_INNER,
      </if>
      <if test="eawbInboundSacId != null" >
        EAWB_INBOUND_SAC_ID,
      </if>
      <if test="eawbOutboundSacId != null" >
        EAWB_OUTBOUND_SAC_ID,
      </if>
      <if test="eawbProductdeclare != null" >
        EAWB_PRODUCTDECLARE,
      </if>
      <if test="eawbServicerequirement != null" >
        EAWB_SERVICEREQUIREMENT,
      </if>
      <if test="eawbReference3 != null" >
        EAWB_REFERENCE3,
      </if>
      <if test="eawbCCode != null" >
        EAWB_C_CODE,
      </if>
      <if test="eawbTimeLimit != null" >
        EAWB_TIME_LIMIT,
      </if>
      <if test="eawbCollectpaymentforgoods != null" >
        EAWB_COLLECTPAYMENTFORGOODS,
      </if>
      <if test="eawbSelfinsurance != null" >
        EAWB_SELFINSURANCE,
      </if>
      <if test="eawbKeyentrytime != null" >
        EAWB_KEYENTRYTIME,
      </if>
      <if test="eawbPreservation != null" >
        EAWB_PRESERVATION,
      </if>
      <if test="eawbInsuranceservice != null" >
        EAWB_INSURANCESERVICE,
      </if>
      <if test="eawbAgentCode != null" >
        EAWB_AGENT_CODE,
      </if>
      <if test="eawbBusinessmode != null" >
        EAWB_BUSINESSMODE,
      </if>
      <if test="eawbPreFreightprice != null" >
        EAWB_PRE_FREIGHTPRICE,
      </if>
      <if test="eawbSendvoicerequest != null" >
        EAWB_SENDVOICEREQUEST,
      </if>
      <if test="cbcShipCode != null" >
        CBC_SHIP_CODE,
      </if>
      <if test="eawbRoute != null" >
        EAWB_ROUTE,
      </if>
      <if test="eawbDepartcity != null" >
        EAWB_DEPARTCITY,
      </if>
      <if test="eawbDepartstate != null" >
        EAWB_DEPARTSTATE,
      </if>
      <if test="eawbDepartcountry != null" >
        EAWB_DEPARTCOUNTRY,
      </if>
      <if test="eawbDestcity != null" >
        EAWB_DESTCITY,
      </if>
      <if test="eawbDeststate != null" >
        EAWB_DESTSTATE,
      </if>
      <if test="eawbDestcountry != null" >
        EAWB_DESTCOUNTRY,
      </if>
      <if test="eawbType != null" >
        EAWB_TYPE,
      </if>
      <if test="eawbCustprodname != null" >
        EAWB_CUSTPRODNAME,
      </if>
      <if test="eawbQuantity != null" >
        EAWB_QUANTITY,
      </if>
      <if test="eawbCustdeclval != null" >
        EAWB_CUSTDECLVAL,
      </if>
      <if test="eawbCustcurrency != null" >
        EAWB_CUSTCURRENCY,
      </if>
      <if test="eawbDeclcurrency != null" >
        EAWB_DECLCURRENCY,
      </if>
      <if test="eawbConsignmentno != null" >
        EAWB_CONSIGNMENTNO,
      </if>
      <if test="eawbKjtype != null" >
        EAWB_KJTYPE,
      </if>
      <if test="eawbIetype != null" >
        EAWB_IETYPE,
      </if>
      <if test="bCode != null" >
        B_CODE,
      </if>
      <if test="eawbFuelcharge != null" >
        EAWB_FUELCHARGE,
      </if>
      <if test="eawbInsutype != null" >
        EAWB_INSUTYPE,
      </if>
      <if test="eawbInsuamounttype != null" >
        EAWB_INSUAMOUNTTYPE,
      </if>
      <if test="eawbInsuamount != null" >
        EAWB_INSUAMOUNT,
      </if>
      <if test="eawbPreFuelprice != null" >
        EAWB_PRE_FUELPRICE,
      </if>
      <if test="handsetCargo != null" >
        HANDSET_CARGO,
      </if>
      <if test="infoxml != null" >
        INFOXML,
      </if>
      <if test="eawbDiscountvalue != null" >
        EAWB_DISCOUNTVALUE,
      </if>
      <if test="eawbTransmodeid != null" >
        EAWB_TRANSMODEID,
      </if>
      <if test="eawbProducttype != null" >
        EAWB_PRODUCTTYPE,
      </if>
      <if test="eawbOrigincity != null" >
        EAWB_ORIGINCITY,
      </if>
      <if test="eawbShipperCaccountname != null" >
        EAWB_SHIPPER_CACCOUNTNAME,
      </if>
      <if test="eawbDeliverFax != null" >
        EAWB_DELIVER_FAX,
      </if>
      <if test="eawbSpecification != null" >
        EAWB_SPECIFICATION,
      </if>
      <if test="eawbCctype != null" >
        EAWB_CCTYPE,
      </if>
      <if test="eawbCustregistrationcode != null" >
        EAWB_CUSTREGISTRATIONCODE,
      </if>
      <if test="eawbCustregistrationame != null" >
        EAWB_CUSTREGISTRATIONAME,
      </if>
      <if test="eawbEntrustcode != null" >
        EAWB_ENTRUSTCODE,
      </if>
      <if test="eawbHscode != null" >
        EAWB_HSCODE,
      </if>
      <if test="eawbCustprodenname != null" >
        EAWB_CUSTPRODENNAME,
      </if>
      <if test="eawbOperatestatus != null" >
        EAWB_OPERATESTATUS,
      </if>
      <if test="eawbUnit != null" >
        EAWB_UNIT,
      </if>
      <if test="eawbUnitcode != null" >
        EAWB_UNITCODE,
      </if>
      <if test="eawbCheckstatus != null" >
        EAWB_CHECKSTATUS,
      </if>
      <if test="eawbServicetype != null" >
        EAWB_SERVICETYPE,
      </if>
      <if test="mawbCode != null" >
        MAWB_CODE,
      </if>
      <if test="flightNo != null" >
        FLIGHT_NO,
      </if>
      <if test="customType != null" >
        CUSTOM_TYPE,
      </if>
      <if test="eawbEcommerce != null" >
        EAWB_ECOMMERCE,
      </if>
      <if test="eawbTaxcode != null" >
        EAWB_TAXCODE,
      </if>
      <if test="eawbDeliverIndentitycardno != null" >
        EAWB_DELIVER_INDENTITYCARDNO,
      </if>
      <if test="customsStatus != null" >
        CUSTOMS_STATUS,
      </if>
      <if test="csefSyscode != null" >
        CSEF_SYSCODE,
      </if>
      <if test="etcopy != null" >
        ETCOPY,
      </if>
      <if test="eawbTrack != null" >
        EAWB_TRACK,
      </if>
      <if test="eawbDeliverEmail != null" >
        EAWB_DELIVER_EMAIL,
      </if>
      <if test="eawbPartition != null" >
        EAWB_PARTITION,
      </if>
      <if test="eawbDeliveryPostcodeCorrect != null" >
        EAWB_DELIVERY_POSTCODE_CORRECT,
      </if>
      <if test="eawbChangeLabelStatus != null" >
        EAWB_CHANGE_LABEL_STATUS,
      </if>
      <if test="eawbRefundWangwangId != null" >
        EAWB_REFUND_WANGWANG_ID,
      </if>
      <if test="eawbRefundName != null" >
        EAWB_REFUND_NAME,
      </if>
      <if test="eawbRefundPhone != null" >
        EAWB_REFUND_PHONE,
      </if>
      <if test="eawbRefundMobile != null" >
        EAWB_REFUND_MOBILE,
      </if>
      <if test="eawbRefundEmail != null" >
        EAWB_REFUND_EMAIL,
      </if>
      <if test="eawbRefundCountry != null" >
        EAWB_REFUND_COUNTRY,
      </if>
      <if test="eawbRefundPrivince != null" >
        EAWB_REFUND_PRIVINCE,
      </if>
      <if test="eawbRefundCity != null" >
        EAWB_REFUND_CITY,
      </if>
      <if test="eawbRefundDistrict != null" >
        EAWB_REFUND_DISTRICT,
      </if>
      <if test="eawbRefundStreet != null" >
        EAWB_REFUND_STREET,
      </if>
      <if test="eawbRefundZipcode != null" >
        EAWB_REFUND_ZIPCODE,
      </if>
      <if test="eawbUndeliveryOption != null" >
        EAWB_UNDELIVERY_OPTION,
      </if>
      <if test="eawbCarriername != null" >
        EAWB_CARRIERNAME,
      </if>
      <if test="eawbCarrierno != null" >
        EAWB_CARRIERNO,
      </if>
      <if test="orderCodeIn != null" >
        ORDER_CODE_IN,
      </if>
      <if test="orderSyscodeIn != null" >
        ORDER_SYSCODE_IN,
      </if>
      <if test="orderCodeOut != null" >
        ORDER_CODE_OUT,
      </if>
      <if test="orderSyscodeOut != null" >
        ORDER_SYSCODE_OUT,
      </if>
      <if test="customerOrderCode != null" >
        CUSTOMER_ORDER_CODE,
      </if>
      <if test="eawbServicetypeOriginal != null" >
        EAWB_SERVICETYPE_ORIGINAL,
      </if>
      <if test="eawbLength != null" >
        EAWB_LENGTH,
      </if>
      <if test="eawbWidth != null" >
        EAWB_WIDTH,
      </if>
      <if test="eawbHeight != null" >
        EAWB_HEIGHT,
      </if>
      <if test="eawbDeliverMobile != null" >
        EAWB_DELIVER_MOBILE,
      </if>
      <if test="eawbCod != null" >
        EAWB_COD,
      </if>
      <if test="eawbCodvalue != null" >
        EAWB_CODVALUE,
      </if>
      <if test="eawbCodcurrency != null" >
        EAWB_CODCURRENCY,
      </if>
      <if test="eawbRefundReference != null" >
        EAWB_REFUND_REFERENCE,
      </if>
      <if test="eawbTransmodeidOriginal != null" >
        EAWB_TRANSMODEID_ORIGINAL,
      </if>
      <if test="eawbRefundSupplier != null" >
        EAWB_REFUND_SUPPLIER,
      </if>
      <if test="eawbRefundReference1 != null" >
        EAWB_REFUND_REFERENCE1,
      </if>
      <if test="eawbRefundReference2 != null" >
        EAWB_REFUND_REFERENCE2,
      </if>
      <if test="eawbRefundReference3 != null" >
        EAWB_REFUND_REFERENCE3,
      </if>
      <if test="eawbRefundAddress != null" >
        EAWB_REFUND_ADDRESS,
      </if>
      <if test="refundStatus != null" >
        REFUND_STATUS,
      </if>
      <if test="eawbDeliverStreet != null" >
        EAWB_DELIVER_STREET,
      </if>
      <if test="eawbNextOptCenter != null" >
        EAWB_NEXT_OPT_CENTER,
      </if>
      <if test="eawbPreOptCenter != null" >
        EAWB_PRE_OPT_CENTER,
      </if>
      <if test="eawbTrunkCode != null" >
        EAWB_TRUNK_CODE,
      </if>
      <if test="eawbChannelCode != null" >
        EAWB_CHANNEL_CODE,
      </if>
      <if test="eawbDeliverDistrict != null" >
        EAWB_DELIVER_DISTRICT,
      </if>
      <if test="eawbPickupDistrict != null" >
        EAWB_PICKUP_DISTRICT,
      </if>
      <if test="eawbSenderAddress != null" >
        EAWB_SENDER_ADDRESS,
      </if>
      <if test="eawbSortcode != null" >
        EAWB_SORTCODE,
      </if>
      <if test="eawbConsigneeLatitude != null" >
        EAWB_CONSIGNEE_LATITUDE,
      </if>
      <if test="eawbConsigneeLongitude != null" >
        EAWB_CONSIGNEE_LONGITUDE,
      </if>
      <if test="eawbFirstmile != null" >
        EAWB_FIRSTMILE,
      </if>
      <if test="eawbPaymentid != null" >
        EAWB_PAYMENTID,
      </if>
      <if test="eawbPaymentemail != null" >
        EAWB_PAYMENTEMAIL,
      </if>
      <if test="eawbPaymentcontactname != null" >
        EAWB_PAYMENTCONTACTNAME,
      </if>
      <if test="eawbPaymentphonenumber != null" >
        EAWB_PAYMENTPHONENUMBER,
      </if>
      <if test="eawbSellertaxnumber != null" >
        EAWB_SELLERTAXNUMBER,
      </if>
      <if test="eawbInvoicenumber != null" >
        EAWB_INVOICENUMBER,
      </if>
      <if test="eawbProducturl != null" >
        EAWB_PRODUCTURL,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="eawbSyscode != null" >
        #{eawbSyscode,jdbcType=DECIMAL},
      </if>
      <if test="eawbCode != null" >
        #{eawbCode,jdbcType=VARCHAR},
      </if>
      <if test="eawbPrintcode != null" >
        #{eawbPrintcode,jdbcType=VARCHAR},
      </if>
      <if test="eawbEIdHandler != null" >
        #{eawbEIdHandler,jdbcType=DECIMAL},
      </if>
      <if test="estCode != null" >
        #{estCode,jdbcType=VARCHAR},
      </if>
      <if test="eawbHandletime != null" >
        #{eawbHandletime,jdbcType=TIMESTAMP},
      </if>
      <if test="eawbShipperAccount != null" >
        #{eawbShipperAccount,jdbcType=VARCHAR},
      </if>
      <if test="eawbPickupAddress != null" >
        #{eawbPickupAddress,jdbcType=VARCHAR},
      </if>
      <if test="eawbPickupPostcode != null" >
        #{eawbPickupPostcode,jdbcType=VARCHAR},
      </if>
      <if test="eawbPickupContact != null" >
        #{eawbPickupContact,jdbcType=VARCHAR},
      </if>
      <if test="eawbPickupPhone != null" >
        #{eawbPickupPhone,jdbcType=VARCHAR},
      </if>
      <if test="eawbConsigneeAccount != null" >
        #{eawbConsigneeAccount,jdbcType=VARCHAR},
      </if>
      <if test="eawbDeliverAddress != null" >
        #{eawbDeliverAddress,jdbcType=VARCHAR},
      </if>
      <if test="eawbDeliverPostcode != null" >
        #{eawbDeliverPostcode,jdbcType=VARCHAR},
      </if>
      <if test="eawbDeliverContact != null" >
        #{eawbDeliverContact,jdbcType=VARCHAR},
      </if>
      <if test="eawbDeliverPhone != null" >
        #{eawbDeliverPhone,jdbcType=VARCHAR},
      </if>
      <if test="eawbDeparture != null" >
        #{eawbDeparture,jdbcType=VARCHAR},
      </if>
      <if test="eawbDestination != null" >
        #{eawbDestination,jdbcType=VARCHAR},
      </if>
      <if test="eawbProductname != null" >
        #{eawbProductname,jdbcType=VARCHAR},
      </if>
      <if test="eawbTotalpieces != null" >
        #{eawbTotalpieces,jdbcType=DECIMAL},
      </if>
      <if test="eawbTotalvolume != null" >
        #{eawbTotalvolume,jdbcType=DECIMAL},
      </if>
      <if test="eawbTotalgrossweight != null" >
        #{eawbTotalgrossweight,jdbcType=DECIMAL},
      </if>
      <if test="eawbTotalchargeableweight != null" >
        #{eawbTotalchargeableweight,jdbcType=DECIMAL},
      </if>
      <if test="eawbDeclarevolume != null" >
        #{eawbDeclarevolume,jdbcType=DECIMAL},
      </if>
      <if test="eawbDeclaregrossweight != null" >
        #{eawbDeclaregrossweight,jdbcType=DECIMAL},
      </if>
      <if test="eawbDeclarechargeable != null" >
        #{eawbDeclarechargeable,jdbcType=DECIMAL},
      </if>
      <if test="eawbPaymentmode != null" >
        #{eawbPaymentmode,jdbcType=VARCHAR},
      </if>
      <if test="eawbThirdpartyAccount != null" >
        #{eawbThirdpartyAccount,jdbcType=VARCHAR},
      </if>
      <if test="ctCode != null" >
        #{ctCode,jdbcType=VARCHAR},
      </if>
      <if test="eawbCurrencyrate != null" >
        #{eawbCurrencyrate,jdbcType=VARCHAR},
      </if>
      <if test="eawbTotalrmb != null" >
        #{eawbTotalrmb,jdbcType=DECIMAL},
      </if>
      <if test="eawbTotalfc != null" >
        #{eawbTotalfc,jdbcType=DECIMAL},
      </if>
      <if test="sacId != null" >
        #{sacId,jdbcType=VARCHAR},
      </if>
      <if test="eawbSacCode != null" >
        #{eawbSacCode,jdbcType=VARCHAR},
      </if>
      <if test="ebCode != null" >
        #{ebCode,jdbcType=VARCHAR},
      </if>
      <if test="eawbSoCode != null" >
        #{eawbSoCode,jdbcType=VARCHAR},
      </if>
      <if test="eawbShipperAccountname != null" >
        #{eawbShipperAccountname,jdbcType=VARCHAR},
      </if>
      <if test="eawbConsigneeAccountname != null" >
        #{eawbConsigneeAccountname,jdbcType=VARCHAR},
      </if>
      <if test="eawbPickupBlock != null" >
        #{eawbPickupBlock,jdbcType=VARCHAR},
      </if>
      <if test="eawbPickupTime != null" >
        #{eawbPickupTime,jdbcType=TIMESTAMP},
      </if>
      <if test="eawbFreightcharge != null" >
        #{eawbFreightcharge,jdbcType=DECIMAL},
      </if>
      <if test="eawbIncidentalcharge != null" >
        #{eawbIncidentalcharge,jdbcType=DECIMAL},
      </if>
      <if test="eawbInsurancecharge != null" >
        #{eawbInsurancecharge,jdbcType=DECIMAL},
      </if>
      <if test="eawbPpcc != null" >
        #{eawbPpcc,jdbcType=VARCHAR},
      </if>
      <if test="eawbDeclarevalue != null" >
        #{eawbDeclarevalue,jdbcType=DECIMAL},
      </if>
      <if test="eawbReference1 != null" >
        #{eawbReference1,jdbcType=VARCHAR},
      </if>
      <if test="eawbReference2 != null" >
        #{eawbReference2,jdbcType=VARCHAR},
      </if>
      <if test="eawbStandardfreightprice != null" >
        #{eawbStandardfreightprice,jdbcType=DECIMAL},
      </if>
      <if test="eawbStatus != null" >
        #{eawbStatus,jdbcType=VARCHAR},
      </if>
      <if test="eawbPickupbyconsignee != null" >
        #{eawbPickupbyconsignee,jdbcType=VARCHAR},
      </if>
      <if test="eawbDeliveryatholiday != null" >
        #{eawbDeliveryatholiday,jdbcType=VARCHAR},
      </if>
      <if test="eawbInner != null" >
        #{eawbInner,jdbcType=VARCHAR},
      </if>
      <if test="eawbInboundSacId != null" >
        #{eawbInboundSacId,jdbcType=VARCHAR},
      </if>
      <if test="eawbOutboundSacId != null" >
        #{eawbOutboundSacId,jdbcType=VARCHAR},
      </if>
      <if test="eawbProductdeclare != null" >
        #{eawbProductdeclare,jdbcType=VARCHAR},
      </if>
      <if test="eawbServicerequirement != null" >
        #{eawbServicerequirement,jdbcType=VARCHAR},
      </if>
      <if test="eawbReference3 != null" >
        #{eawbReference3,jdbcType=VARCHAR},
      </if>
      <if test="eawbCCode != null" >
        #{eawbCCode,jdbcType=VARCHAR},
      </if>
      <if test="eawbTimeLimit != null" >
        #{eawbTimeLimit,jdbcType=TIMESTAMP},
      </if>
      <if test="eawbCollectpaymentforgoods != null" >
        #{eawbCollectpaymentforgoods,jdbcType=DECIMAL},
      </if>
      <if test="eawbSelfinsurance != null" >
        #{eawbSelfinsurance,jdbcType=VARCHAR},
      </if>
      <if test="eawbKeyentrytime != null" >
        #{eawbKeyentrytime,jdbcType=TIMESTAMP},
      </if>
      <if test="eawbPreservation != null" >
        #{eawbPreservation,jdbcType=VARCHAR},
      </if>
      <if test="eawbInsuranceservice != null" >
        #{eawbInsuranceservice,jdbcType=VARCHAR},
      </if>
      <if test="eawbAgentCode != null" >
        #{eawbAgentCode,jdbcType=VARCHAR},
      </if>
      <if test="eawbBusinessmode != null" >
        #{eawbBusinessmode,jdbcType=VARCHAR},
      </if>
      <if test="eawbPreFreightprice != null" >
        #{eawbPreFreightprice,jdbcType=DECIMAL},
      </if>
      <if test="eawbSendvoicerequest != null" >
        #{eawbSendvoicerequest,jdbcType=CHAR},
      </if>
      <if test="cbcShipCode != null" >
        #{cbcShipCode,jdbcType=VARCHAR},
      </if>
      <if test="eawbRoute != null" >
        #{eawbRoute,jdbcType=VARCHAR},
      </if>
      <if test="eawbDepartcity != null" >
        #{eawbDepartcity,jdbcType=VARCHAR},
      </if>
      <if test="eawbDepartstate != null" >
        #{eawbDepartstate,jdbcType=VARCHAR},
      </if>
      <if test="eawbDepartcountry != null" >
        #{eawbDepartcountry,jdbcType=VARCHAR},
      </if>
      <if test="eawbDestcity != null" >
        #{eawbDestcity,jdbcType=VARCHAR},
      </if>
      <if test="eawbDeststate != null" >
        #{eawbDeststate,jdbcType=VARCHAR},
      </if>
      <if test="eawbDestcountry != null" >
        #{eawbDestcountry,jdbcType=VARCHAR},
      </if>
      <if test="eawbType != null" >
        #{eawbType,jdbcType=VARCHAR},
      </if>
      <if test="eawbCustprodname != null" >
        #{eawbCustprodname,jdbcType=VARCHAR},
      </if>
      <if test="eawbQuantity != null" >
        #{eawbQuantity,jdbcType=DECIMAL},
      </if>
      <if test="eawbCustdeclval != null" >
        #{eawbCustdeclval,jdbcType=DECIMAL},
      </if>
      <if test="eawbCustcurrency != null" >
        #{eawbCustcurrency,jdbcType=VARCHAR},
      </if>
      <if test="eawbDeclcurrency != null" >
        #{eawbDeclcurrency,jdbcType=VARCHAR},
      </if>
      <if test="eawbConsignmentno != null" >
        #{eawbConsignmentno,jdbcType=VARCHAR},
      </if>
      <if test="eawbKjtype != null" >
        #{eawbKjtype,jdbcType=VARCHAR},
      </if>
      <if test="eawbIetype != null" >
        #{eawbIetype,jdbcType=VARCHAR},
      </if>
      <if test="bCode != null" >
        #{bCode,jdbcType=VARCHAR},
      </if>
      <if test="eawbFuelcharge != null" >
        #{eawbFuelcharge,jdbcType=DECIMAL},
      </if>
      <if test="eawbInsutype != null" >
        #{eawbInsutype,jdbcType=VARCHAR},
      </if>
      <if test="eawbInsuamounttype != null" >
        #{eawbInsuamounttype,jdbcType=VARCHAR},
      </if>
      <if test="eawbInsuamount != null" >
        #{eawbInsuamount,jdbcType=DECIMAL},
      </if>
      <if test="eawbPreFuelprice != null" >
        #{eawbPreFuelprice,jdbcType=DECIMAL},
      </if>
      <if test="handsetCargo != null" >
        #{handsetCargo,jdbcType=VARCHAR},
      </if>
      <if test="infoxml != null" >
        #{infoxml,jdbcType=VARCHAR},
      </if>
      <if test="eawbDiscountvalue != null" >
        #{eawbDiscountvalue,jdbcType=DECIMAL},
      </if>
      <if test="eawbTransmodeid != null" >
        #{eawbTransmodeid,jdbcType=VARCHAR},
      </if>
      <if test="eawbProducttype != null" >
        #{eawbProducttype,jdbcType=VARCHAR},
      </if>
      <if test="eawbOrigincity != null" >
        #{eawbOrigincity,jdbcType=VARCHAR},
      </if>
      <if test="eawbShipperCaccountname != null" >
        #{eawbShipperCaccountname,jdbcType=VARCHAR},
      </if>
      <if test="eawbDeliverFax != null" >
        #{eawbDeliverFax,jdbcType=VARCHAR},
      </if>
      <if test="eawbSpecification != null" >
        #{eawbSpecification,jdbcType=VARCHAR},
      </if>
      <if test="eawbCctype != null" >
        #{eawbCctype,jdbcType=VARCHAR},
      </if>
      <if test="eawbCustregistrationcode != null" >
        #{eawbCustregistrationcode,jdbcType=VARCHAR},
      </if>
      <if test="eawbCustregistrationame != null" >
        #{eawbCustregistrationame,jdbcType=VARCHAR},
      </if>
      <if test="eawbEntrustcode != null" >
        #{eawbEntrustcode,jdbcType=VARCHAR},
      </if>
      <if test="eawbHscode != null" >
        #{eawbHscode,jdbcType=VARCHAR},
      </if>
      <if test="eawbCustprodenname != null" >
        #{eawbCustprodenname,jdbcType=VARCHAR},
      </if>
      <if test="eawbOperatestatus != null" >
        #{eawbOperatestatus,jdbcType=VARCHAR},
      </if>
      <if test="eawbUnit != null" >
        #{eawbUnit,jdbcType=VARCHAR},
      </if>
      <if test="eawbUnitcode != null" >
        #{eawbUnitcode,jdbcType=VARCHAR},
      </if>
      <if test="eawbCheckstatus != null" >
        #{eawbCheckstatus,jdbcType=VARCHAR},
      </if>
      <if test="eawbServicetype != null" >
        #{eawbServicetype,jdbcType=VARCHAR},
      </if>
      <if test="mawbCode != null" >
        #{mawbCode,jdbcType=VARCHAR},
      </if>
      <if test="flightNo != null" >
        #{flightNo,jdbcType=VARCHAR},
      </if>
      <if test="customType != null" >
        #{customType,jdbcType=VARCHAR},
      </if>
      <if test="eawbEcommerce != null" >
        #{eawbEcommerce,jdbcType=VARCHAR},
      </if>
      <if test="eawbTaxcode != null" >
        #{eawbTaxcode,jdbcType=VARCHAR},
      </if>
      <if test="eawbDeliverIndentitycardno != null" >
        #{eawbDeliverIndentitycardno,jdbcType=VARCHAR},
      </if>
      <if test="customsStatus != null" >
        #{customsStatus,jdbcType=VARCHAR},
      </if>
      <if test="csefSyscode != null" >
        #{csefSyscode,jdbcType=DECIMAL},
      </if>
      <if test="etcopy != null" >
        #{etcopy,jdbcType=DECIMAL},
      </if>
      <if test="eawbTrack != null" >
        #{eawbTrack,jdbcType=VARCHAR},
      </if>
      <if test="eawbDeliverEmail != null" >
        #{eawbDeliverEmail,jdbcType=VARCHAR},
      </if>
      <if test="eawbPartition != null" >
        #{eawbPartition,jdbcType=VARCHAR},
      </if>
      <if test="eawbDeliveryPostcodeCorrect != null" >
        #{eawbDeliveryPostcodeCorrect,jdbcType=VARCHAR},
      </if>
      <if test="eawbChangeLabelStatus != null" >
        #{eawbChangeLabelStatus,jdbcType=VARCHAR},
      </if>
      <if test="eawbRefundWangwangId != null" >
        #{eawbRefundWangwangId,jdbcType=VARCHAR},
      </if>
      <if test="eawbRefundName != null" >
        #{eawbRefundName,jdbcType=VARCHAR},
      </if>
      <if test="eawbRefundPhone != null" >
        #{eawbRefundPhone,jdbcType=VARCHAR},
      </if>
      <if test="eawbRefundMobile != null" >
        #{eawbRefundMobile,jdbcType=VARCHAR},
      </if>
      <if test="eawbRefundEmail != null" >
        #{eawbRefundEmail,jdbcType=VARCHAR},
      </if>
      <if test="eawbRefundCountry != null" >
        #{eawbRefundCountry,jdbcType=VARCHAR},
      </if>
      <if test="eawbRefundPrivince != null" >
        #{eawbRefundPrivince,jdbcType=VARCHAR},
      </if>
      <if test="eawbRefundCity != null" >
        #{eawbRefundCity,jdbcType=VARCHAR},
      </if>
      <if test="eawbRefundDistrict != null" >
        #{eawbRefundDistrict,jdbcType=VARCHAR},
      </if>
      <if test="eawbRefundStreet != null" >
        #{eawbRefundStreet,jdbcType=VARCHAR},
      </if>
      <if test="eawbRefundZipcode != null" >
        #{eawbRefundZipcode,jdbcType=VARCHAR},
      </if>
      <if test="eawbUndeliveryOption != null" >
        #{eawbUndeliveryOption,jdbcType=VARCHAR},
      </if>
      <if test="eawbCarriername != null" >
        #{eawbCarriername,jdbcType=VARCHAR},
      </if>
      <if test="eawbCarrierno != null" >
        #{eawbCarrierno,jdbcType=VARCHAR},
      </if>
      <if test="orderCodeIn != null" >
        #{orderCodeIn,jdbcType=VARCHAR},
      </if>
      <if test="orderSyscodeIn != null" >
        #{orderSyscodeIn,jdbcType=DECIMAL},
      </if>
      <if test="orderCodeOut != null" >
        #{orderCodeOut,jdbcType=VARCHAR},
      </if>
      <if test="orderSyscodeOut != null" >
        #{orderSyscodeOut,jdbcType=DECIMAL},
      </if>
      <if test="customerOrderCode != null" >
        #{customerOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="eawbServicetypeOriginal != null" >
        #{eawbServicetypeOriginal,jdbcType=VARCHAR},
      </if>
      <if test="eawbLength != null" >
        #{eawbLength,jdbcType=DECIMAL},
      </if>
      <if test="eawbWidth != null" >
        #{eawbWidth,jdbcType=DECIMAL},
      </if>
      <if test="eawbHeight != null" >
        #{eawbHeight,jdbcType=DECIMAL},
      </if>
      <if test="eawbDeliverMobile != null" >
        #{eawbDeliverMobile,jdbcType=VARCHAR},
      </if>
      <if test="eawbCod != null" >
        #{eawbCod,jdbcType=VARCHAR},
      </if>
      <if test="eawbCodvalue != null" >
        #{eawbCodvalue,jdbcType=DECIMAL},
      </if>
      <if test="eawbCodcurrency != null" >
        #{eawbCodcurrency,jdbcType=VARCHAR},
      </if>
      <if test="eawbRefundReference != null" >
        #{eawbRefundReference,jdbcType=VARCHAR},
      </if>
      <if test="eawbTransmodeidOriginal != null" >
        #{eawbTransmodeidOriginal,jdbcType=VARCHAR},
      </if>
      <if test="eawbRefundSupplier != null" >
        #{eawbRefundSupplier,jdbcType=CHAR},
      </if>
      <if test="eawbRefundReference1 != null" >
        #{eawbRefundReference1,jdbcType=CHAR},
      </if>
      <if test="eawbRefundReference2 != null" >
        #{eawbRefundReference2,jdbcType=CHAR},
      </if>
      <if test="eawbRefundReference3 != null" >
        #{eawbRefundReference3,jdbcType=VARCHAR},
      </if>
      <if test="eawbRefundAddress != null" >
        #{eawbRefundAddress,jdbcType=VARCHAR},
      </if>
      <if test="refundStatus != null" >
        #{refundStatus,jdbcType=VARCHAR},
      </if>
      <if test="eawbDeliverStreet != null" >
        #{eawbDeliverStreet,jdbcType=VARCHAR},
      </if>
      <if test="eawbNextOptCenter != null" >
        #{eawbNextOptCenter,jdbcType=VARCHAR},
      </if>
      <if test="eawbPreOptCenter != null" >
        #{eawbPreOptCenter,jdbcType=VARCHAR},
      </if>
      <if test="eawbTrunkCode != null" >
        #{eawbTrunkCode,jdbcType=VARCHAR},
      </if>
      <if test="eawbChannelCode != null" >
        #{eawbChannelCode,jdbcType=VARCHAR},
      </if>
      <if test="eawbDeliverDistrict != null" >
        #{eawbDeliverDistrict,jdbcType=VARCHAR},
      </if>
      <if test="eawbPickupDistrict != null" >
        #{eawbPickupDistrict,jdbcType=VARCHAR},
      </if>
      <if test="eawbSenderAddress != null" >
        #{eawbSenderAddress,jdbcType=VARCHAR},
      </if>
      <if test="eawbSortcode != null" >
        #{eawbSortcode,jdbcType=VARCHAR},
      </if>
      <if test="eawbConsigneeLatitude != null" >
        #{eawbConsigneeLatitude,jdbcType=VARCHAR},
      </if>
      <if test="eawbConsigneeLongitude != null" >
        #{eawbConsigneeLongitude,jdbcType=VARCHAR},
      </if>
      <if test="eawbFirstmile != null" >
        #{eawbFirstmile,jdbcType=VARCHAR},
      </if>
      <if test="eawbPaymentid != null" >
        #{eawbPaymentid,jdbcType=VARCHAR},
      </if>
      <if test="eawbPaymentemail != null" >
        #{eawbPaymentemail,jdbcType=VARCHAR},
      </if>
      <if test="eawbPaymentcontactname != null" >
        #{eawbPaymentcontactname,jdbcType=VARCHAR},
      </if>
      <if test="eawbPaymentphonenumber != null" >
        #{eawbPaymentphonenumber,jdbcType=VARCHAR},
      </if>
      <if test="eawbSellertaxnumber != null" >
        #{eawbSellertaxnumber,jdbcType=VARCHAR},
      </if>
      <if test="eawbInvoicenumber != null" >
        #{eawbInvoicenumber,jdbcType=VARCHAR},
      </if>
      <if test="eawbProducturl != null" >
        #{eawbProducturl,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.ExpressAirWayBillCeos" >
    update EXPRESSAIRWAYBILL_CEOS
    <set >
      <if test="eawbCode != null" >
        EAWB_CODE = #{eawbCode,jdbcType=VARCHAR},
      </if>
      <if test="eawbPrintcode != null" >
        EAWB_PRINTCODE = #{eawbPrintcode,jdbcType=VARCHAR},
      </if>
      <if test="eawbEIdHandler != null" >
        EAWB_E_ID_HANDLER = #{eawbEIdHandler,jdbcType=DECIMAL},
      </if>
      <if test="estCode != null" >
        EST_CODE = #{estCode,jdbcType=VARCHAR},
      </if>
      <if test="eawbHandletime != null" >
        EAWB_HANDLETIME = #{eawbHandletime,jdbcType=TIMESTAMP},
      </if>
      <if test="eawbShipperAccount != null" >
        EAWB_SHIPPER_ACCOUNT = #{eawbShipperAccount,jdbcType=VARCHAR},
      </if>
      <if test="eawbPickupAddress != null" >
        EAWB_PICKUP_ADDRESS = #{eawbPickupAddress,jdbcType=VARCHAR},
      </if>
      <if test="eawbPickupPostcode != null" >
        EAWB_PICKUP_POSTCODE = #{eawbPickupPostcode,jdbcType=VARCHAR},
      </if>
      <if test="eawbPickupContact != null" >
        EAWB_PICKUP_CONTACT = #{eawbPickupContact,jdbcType=VARCHAR},
      </if>
      <if test="eawbPickupPhone != null" >
        EAWB_PICKUP_PHONE = #{eawbPickupPhone,jdbcType=VARCHAR},
      </if>
      <if test="eawbConsigneeAccount != null" >
        EAWB_CONSIGNEE_ACCOUNT = #{eawbConsigneeAccount,jdbcType=VARCHAR},
      </if>
      <if test="eawbDeliverAddress != null" >
        EAWB_DELIVER_ADDRESS = #{eawbDeliverAddress,jdbcType=VARCHAR},
      </if>
      <if test="eawbDeliverPostcode != null" >
        EAWB_DELIVER_POSTCODE = #{eawbDeliverPostcode,jdbcType=VARCHAR},
      </if>
      <if test="eawbDeliverContact != null" >
        EAWB_DELIVER_CONTACT = #{eawbDeliverContact,jdbcType=VARCHAR},
      </if>
      <if test="eawbDeliverPhone != null" >
        EAWB_DELIVER_PHONE = #{eawbDeliverPhone,jdbcType=VARCHAR},
      </if>
      <if test="eawbDeparture != null" >
        EAWB_DEPARTURE = #{eawbDeparture,jdbcType=VARCHAR},
      </if>
      <if test="eawbDestination != null" >
        EAWB_DESTINATION = #{eawbDestination,jdbcType=VARCHAR},
      </if>
      <if test="eawbProductname != null" >
        EAWB_PRODUCTNAME = #{eawbProductname,jdbcType=VARCHAR},
      </if>
      <if test="eawbTotalpieces != null" >
        EAWB_TOTALPIECES = #{eawbTotalpieces,jdbcType=DECIMAL},
      </if>
      <if test="eawbTotalvolume != null" >
        EAWB_TOTALVOLUME = #{eawbTotalvolume,jdbcType=DECIMAL},
      </if>
      <if test="eawbTotalgrossweight != null" >
        EAWB_TOTALGROSSWEIGHT = #{eawbTotalgrossweight,jdbcType=DECIMAL},
      </if>
      <if test="eawbTotalchargeableweight != null" >
        EAWB_TOTALCHARGEABLEWEIGHT = #{eawbTotalchargeableweight,jdbcType=DECIMAL},
      </if>
      <if test="eawbDeclarevolume != null" >
        EAWB_DECLAREVOLUME = #{eawbDeclarevolume,jdbcType=DECIMAL},
      </if>
      <if test="eawbDeclaregrossweight != null" >
        EAWB_DECLAREGROSSWEIGHT = #{eawbDeclaregrossweight,jdbcType=DECIMAL},
      </if>
      <if test="eawbDeclarechargeable != null" >
        EAWB_DECLARECHARGEABLE = #{eawbDeclarechargeable,jdbcType=DECIMAL},
      </if>
      <if test="eawbPaymentmode != null" >
        EAWB_PAYMENTMODE = #{eawbPaymentmode,jdbcType=VARCHAR},
      </if>
      <if test="eawbThirdpartyAccount != null" >
        EAWB_THIRDPARTY_ACCOUNT = #{eawbThirdpartyAccount,jdbcType=VARCHAR},
      </if>
      <if test="ctCode != null" >
        CT_CODE = #{ctCode,jdbcType=VARCHAR},
      </if>
      <if test="eawbCurrencyrate != null" >
        EAWB_CURRENCYRATE = #{eawbCurrencyrate,jdbcType=VARCHAR},
      </if>
      <if test="eawbTotalrmb != null" >
        EAWB_TOTALRMB = #{eawbTotalrmb,jdbcType=DECIMAL},
      </if>
      <if test="eawbTotalfc != null" >
        EAWB_TOTALFC = #{eawbTotalfc,jdbcType=DECIMAL},
      </if>
      <if test="sacId != null" >
        SAC_ID = #{sacId,jdbcType=VARCHAR},
      </if>
      <if test="eawbSacCode != null" >
        EAWB_SAC_CODE = #{eawbSacCode,jdbcType=VARCHAR},
      </if>
      <if test="ebCode != null" >
        EB_CODE = #{ebCode,jdbcType=VARCHAR},
      </if>
      <if test="eawbSoCode != null" >
        EAWB_SO_CODE = #{eawbSoCode,jdbcType=VARCHAR},
      </if>
      <if test="eawbShipperAccountname != null" >
        EAWB_SHIPPER_ACCOUNTNAME = #{eawbShipperAccountname,jdbcType=VARCHAR},
      </if>
      <if test="eawbConsigneeAccountname != null" >
        EAWB_CONSIGNEE_ACCOUNTNAME = #{eawbConsigneeAccountname,jdbcType=VARCHAR},
      </if>
      <if test="eawbPickupBlock != null" >
        EAWB_PICKUP_BLOCK = #{eawbPickupBlock,jdbcType=VARCHAR},
      </if>
      <if test="eawbPickupTime != null" >
        EAWB_PICKUP_TIME = #{eawbPickupTime,jdbcType=TIMESTAMP},
      </if>
      <if test="eawbFreightcharge != null" >
        EAWB_FREIGHTCHARGE = #{eawbFreightcharge,jdbcType=DECIMAL},
      </if>
      <if test="eawbIncidentalcharge != null" >
        EAWB_INCIDENTALCHARGE = #{eawbIncidentalcharge,jdbcType=DECIMAL},
      </if>
      <if test="eawbInsurancecharge != null" >
        EAWB_INSURANCECHARGE = #{eawbInsurancecharge,jdbcType=DECIMAL},
      </if>
      <if test="eawbPpcc != null" >
        EAWB_PPCC = #{eawbPpcc,jdbcType=VARCHAR},
      </if>
      <if test="eawbDeclarevalue != null" >
        EAWB_DECLAREVALUE = #{eawbDeclarevalue,jdbcType=DECIMAL},
      </if>
      <if test="eawbReference1 != null" >
        EAWB_REFERENCE1 = #{eawbReference1,jdbcType=VARCHAR},
      </if>
      <if test="eawbReference2 != null" >
        EAWB_REFERENCE2 = #{eawbReference2,jdbcType=VARCHAR},
      </if>
      <if test="eawbStandardfreightprice != null" >
        EAWB_STANDARDFREIGHTPRICE = #{eawbStandardfreightprice,jdbcType=DECIMAL},
      </if>
      <if test="eawbStatus != null" >
        EAWB_STATUS = #{eawbStatus,jdbcType=VARCHAR},
      </if>
      <if test="eawbPickupbyconsignee != null" >
        EAWB_PICKUPBYCONSIGNEE = #{eawbPickupbyconsignee,jdbcType=VARCHAR},
      </if>
      <if test="eawbDeliveryatholiday != null" >
        EAWB_DELIVERYATHOLIDAY = #{eawbDeliveryatholiday,jdbcType=VARCHAR},
      </if>
      <if test="eawbInner != null" >
        EAWB_INNER = #{eawbInner,jdbcType=VARCHAR},
      </if>
      <if test="eawbInboundSacId != null" >
        EAWB_INBOUND_SAC_ID = #{eawbInboundSacId,jdbcType=VARCHAR},
      </if>
      <if test="eawbOutboundSacId != null" >
        EAWB_OUTBOUND_SAC_ID = #{eawbOutboundSacId,jdbcType=VARCHAR},
      </if>
      <if test="eawbProductdeclare != null" >
        EAWB_PRODUCTDECLARE = #{eawbProductdeclare,jdbcType=VARCHAR},
      </if>
      <if test="eawbServicerequirement != null" >
        EAWB_SERVICEREQUIREMENT = #{eawbServicerequirement,jdbcType=VARCHAR},
      </if>
      <if test="eawbReference3 != null" >
        EAWB_REFERENCE3 = #{eawbReference3,jdbcType=VARCHAR},
      </if>
      <if test="eawbCCode != null" >
        EAWB_C_CODE = #{eawbCCode,jdbcType=VARCHAR},
      </if>
      <if test="eawbTimeLimit != null" >
        EAWB_TIME_LIMIT = #{eawbTimeLimit,jdbcType=TIMESTAMP},
      </if>
      <if test="eawbCollectpaymentforgoods != null" >
        EAWB_COLLECTPAYMENTFORGOODS = #{eawbCollectpaymentforgoods,jdbcType=DECIMAL},
      </if>
      <if test="eawbSelfinsurance != null" >
        EAWB_SELFINSURANCE = #{eawbSelfinsurance,jdbcType=VARCHAR},
      </if>
      <if test="eawbKeyentrytime != null" >
        EAWB_KEYENTRYTIME = #{eawbKeyentrytime,jdbcType=TIMESTAMP},
      </if>
      <if test="eawbPreservation != null" >
        EAWB_PRESERVATION = #{eawbPreservation,jdbcType=VARCHAR},
      </if>
      <if test="eawbInsuranceservice != null" >
        EAWB_INSURANCESERVICE = #{eawbInsuranceservice,jdbcType=VARCHAR},
      </if>
      <if test="eawbAgentCode != null" >
        EAWB_AGENT_CODE = #{eawbAgentCode,jdbcType=VARCHAR},
      </if>
      <if test="eawbBusinessmode != null" >
        EAWB_BUSINESSMODE = #{eawbBusinessmode,jdbcType=VARCHAR},
      </if>
      <if test="eawbPreFreightprice != null" >
        EAWB_PRE_FREIGHTPRICE = #{eawbPreFreightprice,jdbcType=DECIMAL},
      </if>
      <if test="eawbSendvoicerequest != null" >
        EAWB_SENDVOICEREQUEST = #{eawbSendvoicerequest,jdbcType=CHAR},
      </if>
      <if test="cbcShipCode != null" >
        CBC_SHIP_CODE = #{cbcShipCode,jdbcType=VARCHAR},
      </if>
      <if test="eawbRoute != null" >
        EAWB_ROUTE = #{eawbRoute,jdbcType=VARCHAR},
      </if>
      <if test="eawbDepartcity != null" >
        EAWB_DEPARTCITY = #{eawbDepartcity,jdbcType=VARCHAR},
      </if>
      <if test="eawbDepartstate != null" >
        EAWB_DEPARTSTATE = #{eawbDepartstate,jdbcType=VARCHAR},
      </if>
      <if test="eawbDepartcountry != null" >
        EAWB_DEPARTCOUNTRY = #{eawbDepartcountry,jdbcType=VARCHAR},
      </if>
      <if test="eawbDestcity != null" >
        EAWB_DESTCITY = #{eawbDestcity,jdbcType=VARCHAR},
      </if>
      <if test="eawbDeststate != null" >
        EAWB_DESTSTATE = #{eawbDeststate,jdbcType=VARCHAR},
      </if>
      <if test="eawbDestcountry != null" >
        EAWB_DESTCOUNTRY = #{eawbDestcountry,jdbcType=VARCHAR},
      </if>
      <if test="eawbType != null" >
        EAWB_TYPE = #{eawbType,jdbcType=VARCHAR},
      </if>
      <if test="eawbCustprodname != null" >
        EAWB_CUSTPRODNAME = #{eawbCustprodname,jdbcType=VARCHAR},
      </if>
      <if test="eawbQuantity != null" >
        EAWB_QUANTITY = #{eawbQuantity,jdbcType=DECIMAL},
      </if>
      <if test="eawbCustdeclval != null" >
        EAWB_CUSTDECLVAL = #{eawbCustdeclval,jdbcType=DECIMAL},
      </if>
      <if test="eawbCustcurrency != null" >
        EAWB_CUSTCURRENCY = #{eawbCustcurrency,jdbcType=VARCHAR},
      </if>
      <if test="eawbDeclcurrency != null" >
        EAWB_DECLCURRENCY = #{eawbDeclcurrency,jdbcType=VARCHAR},
      </if>
      <if test="eawbConsignmentno != null" >
        EAWB_CONSIGNMENTNO = #{eawbConsignmentno,jdbcType=VARCHAR},
      </if>
      <if test="eawbKjtype != null" >
        EAWB_KJTYPE = #{eawbKjtype,jdbcType=VARCHAR},
      </if>
      <if test="eawbIetype != null" >
        EAWB_IETYPE = #{eawbIetype,jdbcType=VARCHAR},
      </if>
      <if test="bCode != null" >
        B_CODE = #{bCode,jdbcType=VARCHAR},
      </if>
      <if test="eawbFuelcharge != null" >
        EAWB_FUELCHARGE = #{eawbFuelcharge,jdbcType=DECIMAL},
      </if>
      <if test="eawbInsutype != null" >
        EAWB_INSUTYPE = #{eawbInsutype,jdbcType=VARCHAR},
      </if>
      <if test="eawbInsuamounttype != null" >
        EAWB_INSUAMOUNTTYPE = #{eawbInsuamounttype,jdbcType=VARCHAR},
      </if>
      <if test="eawbInsuamount != null" >
        EAWB_INSUAMOUNT = #{eawbInsuamount,jdbcType=DECIMAL},
      </if>
      <if test="eawbPreFuelprice != null" >
        EAWB_PRE_FUELPRICE = #{eawbPreFuelprice,jdbcType=DECIMAL},
      </if>
      <if test="handsetCargo != null" >
        HANDSET_CARGO = #{handsetCargo,jdbcType=VARCHAR},
      </if>
      <if test="infoxml != null" >
        INFOXML = #{infoxml,jdbcType=VARCHAR},
      </if>
      <if test="eawbDiscountvalue != null" >
        EAWB_DISCOUNTVALUE = #{eawbDiscountvalue,jdbcType=DECIMAL},
      </if>
      <if test="eawbTransmodeid != null" >
        EAWB_TRANSMODEID = #{eawbTransmodeid,jdbcType=VARCHAR},
      </if>
      <if test="eawbProducttype != null" >
        EAWB_PRODUCTTYPE = #{eawbProducttype,jdbcType=VARCHAR},
      </if>
      <if test="eawbOrigincity != null" >
        EAWB_ORIGINCITY = #{eawbOrigincity,jdbcType=VARCHAR},
      </if>
      <if test="eawbShipperCaccountname != null" >
        EAWB_SHIPPER_CACCOUNTNAME = #{eawbShipperCaccountname,jdbcType=VARCHAR},
      </if>
      <if test="eawbDeliverFax != null" >
        EAWB_DELIVER_FAX = #{eawbDeliverFax,jdbcType=VARCHAR},
      </if>
      <if test="eawbSpecification != null" >
        EAWB_SPECIFICATION = #{eawbSpecification,jdbcType=VARCHAR},
      </if>
      <if test="eawbCctype != null" >
        EAWB_CCTYPE = #{eawbCctype,jdbcType=VARCHAR},
      </if>
      <if test="eawbCustregistrationcode != null" >
        EAWB_CUSTREGISTRATIONCODE = #{eawbCustregistrationcode,jdbcType=VARCHAR},
      </if>
      <if test="eawbCustregistrationame != null" >
        EAWB_CUSTREGISTRATIONAME = #{eawbCustregistrationame,jdbcType=VARCHAR},
      </if>
      <if test="eawbEntrustcode != null" >
        EAWB_ENTRUSTCODE = #{eawbEntrustcode,jdbcType=VARCHAR},
      </if>
      <if test="eawbHscode != null" >
        EAWB_HSCODE = #{eawbHscode,jdbcType=VARCHAR},
      </if>
      <if test="eawbCustprodenname != null" >
        EAWB_CUSTPRODENNAME = #{eawbCustprodenname,jdbcType=VARCHAR},
      </if>
      <if test="eawbOperatestatus != null" >
        EAWB_OPERATESTATUS = #{eawbOperatestatus,jdbcType=VARCHAR},
      </if>
      <if test="eawbUnit != null" >
        EAWB_UNIT = #{eawbUnit,jdbcType=VARCHAR},
      </if>
      <if test="eawbUnitcode != null" >
        EAWB_UNITCODE = #{eawbUnitcode,jdbcType=VARCHAR},
      </if>
      <if test="eawbCheckstatus != null" >
        EAWB_CHECKSTATUS = #{eawbCheckstatus,jdbcType=VARCHAR},
      </if>
      <if test="eawbServicetype != null" >
        EAWB_SERVICETYPE = #{eawbServicetype,jdbcType=VARCHAR},
      </if>
      <if test="mawbCode != null" >
        MAWB_CODE = #{mawbCode,jdbcType=VARCHAR},
      </if>
      <if test="flightNo != null" >
        FLIGHT_NO = #{flightNo,jdbcType=VARCHAR},
      </if>
      <if test="customType != null" >
        CUSTOM_TYPE = #{customType,jdbcType=VARCHAR},
      </if>
      <if test="eawbEcommerce != null" >
        EAWB_ECOMMERCE = #{eawbEcommerce,jdbcType=VARCHAR},
      </if>
      <if test="eawbTaxcode != null" >
        EAWB_TAXCODE = #{eawbTaxcode,jdbcType=VARCHAR},
      </if>
      <if test="eawbDeliverIndentitycardno != null" >
        EAWB_DELIVER_INDENTITYCARDNO = #{eawbDeliverIndentitycardno,jdbcType=VARCHAR},
      </if>
      <if test="customsStatus != null" >
        CUSTOMS_STATUS = #{customsStatus,jdbcType=VARCHAR},
      </if>
      <if test="csefSyscode != null" >
        CSEF_SYSCODE = #{csefSyscode,jdbcType=DECIMAL},
      </if>
      <if test="etcopy != null" >
        ETCOPY = #{etcopy,jdbcType=DECIMAL},
      </if>
      <if test="eawbTrack != null" >
        EAWB_TRACK = #{eawbTrack,jdbcType=VARCHAR},
      </if>
      <if test="eawbDeliverEmail != null" >
        EAWB_DELIVER_EMAIL = #{eawbDeliverEmail,jdbcType=VARCHAR},
      </if>
      <if test="eawbPartition != null" >
        EAWB_PARTITION = #{eawbPartition,jdbcType=VARCHAR},
      </if>
      <if test="eawbDeliveryPostcodeCorrect != null" >
        EAWB_DELIVERY_POSTCODE_CORRECT = #{eawbDeliveryPostcodeCorrect,jdbcType=VARCHAR},
      </if>
      <if test="eawbChangeLabelStatus != null" >
        EAWB_CHANGE_LABEL_STATUS = #{eawbChangeLabelStatus,jdbcType=VARCHAR},
      </if>
      <if test="eawbRefundWangwangId != null" >
        EAWB_REFUND_WANGWANG_ID = #{eawbRefundWangwangId,jdbcType=VARCHAR},
      </if>
      <if test="eawbRefundName != null" >
        EAWB_REFUND_NAME = #{eawbRefundName,jdbcType=VARCHAR},
      </if>
      <if test="eawbRefundPhone != null" >
        EAWB_REFUND_PHONE = #{eawbRefundPhone,jdbcType=VARCHAR},
      </if>
      <if test="eawbRefundMobile != null" >
        EAWB_REFUND_MOBILE = #{eawbRefundMobile,jdbcType=VARCHAR},
      </if>
      <if test="eawbRefundEmail != null" >
        EAWB_REFUND_EMAIL = #{eawbRefundEmail,jdbcType=VARCHAR},
      </if>
      <if test="eawbRefundCountry != null" >
        EAWB_REFUND_COUNTRY = #{eawbRefundCountry,jdbcType=VARCHAR},
      </if>
      <if test="eawbRefundPrivince != null" >
        EAWB_REFUND_PRIVINCE = #{eawbRefundPrivince,jdbcType=VARCHAR},
      </if>
      <if test="eawbRefundCity != null" >
        EAWB_REFUND_CITY = #{eawbRefundCity,jdbcType=VARCHAR},
      </if>
      <if test="eawbRefundDistrict != null" >
        EAWB_REFUND_DISTRICT = #{eawbRefundDistrict,jdbcType=VARCHAR},
      </if>
      <if test="eawbRefundStreet != null" >
        EAWB_REFUND_STREET = #{eawbRefundStreet,jdbcType=VARCHAR},
      </if>
      <if test="eawbRefundZipcode != null" >
        EAWB_REFUND_ZIPCODE = #{eawbRefundZipcode,jdbcType=VARCHAR},
      </if>
      <if test="eawbUndeliveryOption != null" >
        EAWB_UNDELIVERY_OPTION = #{eawbUndeliveryOption,jdbcType=VARCHAR},
      </if>
      <if test="eawbCarriername != null" >
        EAWB_CARRIERNAME = #{eawbCarriername,jdbcType=VARCHAR},
      </if>
      <if test="eawbCarrierno != null" >
        EAWB_CARRIERNO = #{eawbCarrierno,jdbcType=VARCHAR},
      </if>
      <if test="orderCodeIn != null" >
        ORDER_CODE_IN = #{orderCodeIn,jdbcType=VARCHAR},
      </if>
      <if test="orderSyscodeIn != null" >
        ORDER_SYSCODE_IN = #{orderSyscodeIn,jdbcType=DECIMAL},
      </if>
      <if test="orderCodeOut != null" >
        ORDER_CODE_OUT = #{orderCodeOut,jdbcType=VARCHAR},
      </if>
      <if test="orderSyscodeOut != null" >
        ORDER_SYSCODE_OUT = #{orderSyscodeOut,jdbcType=DECIMAL},
      </if>
      <if test="customerOrderCode != null" >
        CUSTOMER_ORDER_CODE = #{customerOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="eawbServicetypeOriginal != null" >
        EAWB_SERVICETYPE_ORIGINAL = #{eawbServicetypeOriginal,jdbcType=VARCHAR},
      </if>
      <if test="eawbLength != null" >
        EAWB_LENGTH = #{eawbLength,jdbcType=DECIMAL},
      </if>
      <if test="eawbWidth != null" >
        EAWB_WIDTH = #{eawbWidth,jdbcType=DECIMAL},
      </if>
      <if test="eawbHeight != null" >
        EAWB_HEIGHT = #{eawbHeight,jdbcType=DECIMAL},
      </if>
      <if test="eawbDeliverMobile != null" >
        EAWB_DELIVER_MOBILE = #{eawbDeliverMobile,jdbcType=VARCHAR},
      </if>
      <if test="eawbCod != null" >
        EAWB_COD = #{eawbCod,jdbcType=VARCHAR},
      </if>
      <if test="eawbCodvalue != null" >
        EAWB_CODVALUE = #{eawbCodvalue,jdbcType=DECIMAL},
      </if>
      <if test="eawbCodcurrency != null" >
        EAWB_CODCURRENCY = #{eawbCodcurrency,jdbcType=VARCHAR},
      </if>
      <if test="eawbRefundReference != null" >
        EAWB_REFUND_REFERENCE = #{eawbRefundReference,jdbcType=VARCHAR},
      </if>
      <if test="eawbTransmodeidOriginal != null" >
        EAWB_TRANSMODEID_ORIGINAL = #{eawbTransmodeidOriginal,jdbcType=VARCHAR},
      </if>
      <if test="eawbRefundSupplier != null" >
        EAWB_REFUND_SUPPLIER = #{eawbRefundSupplier,jdbcType=CHAR},
      </if>
      <if test="eawbRefundReference1 != null" >
        EAWB_REFUND_REFERENCE1 = #{eawbRefundReference1,jdbcType=CHAR},
      </if>
      <if test="eawbRefundReference2 != null" >
        EAWB_REFUND_REFERENCE2 = #{eawbRefundReference2,jdbcType=CHAR},
      </if>
      <if test="eawbRefundReference3 != null" >
        EAWB_REFUND_REFERENCE3 = #{eawbRefundReference3,jdbcType=VARCHAR},
      </if>
      <if test="eawbRefundAddress != null" >
        EAWB_REFUND_ADDRESS = #{eawbRefundAddress,jdbcType=VARCHAR},
      </if>
      <if test="refundStatus != null" >
        REFUND_STATUS = #{refundStatus,jdbcType=VARCHAR},
      </if>
      <if test="eawbDeliverStreet != null" >
        EAWB_DELIVER_STREET = #{eawbDeliverStreet,jdbcType=VARCHAR},
      </if>
      <if test="eawbNextOptCenter != null" >
        EAWB_NEXT_OPT_CENTER = #{eawbNextOptCenter,jdbcType=VARCHAR},
      </if>
      <if test="eawbPreOptCenter != null" >
        EAWB_PRE_OPT_CENTER = #{eawbPreOptCenter,jdbcType=VARCHAR},
      </if>
      <if test="eawbTrunkCode != null" >
        EAWB_TRUNK_CODE = #{eawbTrunkCode,jdbcType=VARCHAR},
      </if>
      <if test="eawbChannelCode != null" >
        EAWB_CHANNEL_CODE = #{eawbChannelCode,jdbcType=VARCHAR},
      </if>
      <if test="eawbDeliverDistrict != null" >
        EAWB_DELIVER_DISTRICT = #{eawbDeliverDistrict,jdbcType=VARCHAR},
      </if>
      <if test="eawbPickupDistrict != null" >
        EAWB_PICKUP_DISTRICT = #{eawbPickupDistrict,jdbcType=VARCHAR},
      </if>
      <if test="eawbSenderAddress != null" >
        EAWB_SENDER_ADDRESS = #{eawbSenderAddress,jdbcType=VARCHAR},
      </if>
      <if test="eawbSortcode != null" >
        EAWB_SORTCODE = #{eawbSortcode,jdbcType=VARCHAR},
      </if>
      <if test="eawbConsigneeLatitude != null" >
        EAWB_CONSIGNEE_LATITUDE = #{eawbConsigneeLatitude,jdbcType=VARCHAR},
      </if>
      <if test="eawbConsigneeLongitude != null" >
        EAWB_CONSIGNEE_LONGITUDE = #{eawbConsigneeLongitude,jdbcType=VARCHAR},
      </if>
      <if test="eawbFirstmile != null" >
        EAWB_FIRSTMILE = #{eawbFirstmile,jdbcType=VARCHAR},
      </if>
      <if test="eawbPaymentid != null" >
        EAWB_PAYMENTID = #{eawbPaymentid,jdbcType=VARCHAR},
      </if>
      <if test="eawbPaymentemail != null" >
        EAWB_PAYMENTEMAIL = #{eawbPaymentemail,jdbcType=VARCHAR},
      </if>
      <if test="eawbPaymentcontactname != null" >
        EAWB_PAYMENTCONTACTNAME = #{eawbPaymentcontactname,jdbcType=VARCHAR},
      </if>
      <if test="eawbPaymentphonenumber != null" >
        EAWB_PAYMENTPHONENUMBER = #{eawbPaymentphonenumber,jdbcType=VARCHAR},
      </if>
      <if test="eawbSellertaxnumber != null" >
        EAWB_SELLERTAXNUMBER = #{eawbSellertaxnumber,jdbcType=VARCHAR},
      </if>
      <if test="eawbInvoicenumber != null" >
        EAWB_INVOICENUMBER = #{eawbInvoicenumber,jdbcType=VARCHAR},
      </if>
      <if test="eawbProducturl != null" >
        EAWB_PRODUCTURL = #{eawbProducturl,jdbcType=VARCHAR},
      </if>
    </set>
    where EAWB_SYSCODE = #{eawbSyscode,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.ExpressAirWayBillCeos" >
    update EXPRESSAIRWAYBILL_CEOS
    set EAWB_CODE = #{eawbCode,jdbcType=VARCHAR},
      EAWB_PRINTCODE = #{eawbPrintcode,jdbcType=VARCHAR},
      EAWB_E_ID_HANDLER = #{eawbEIdHandler,jdbcType=DECIMAL},
      EST_CODE = #{estCode,jdbcType=VARCHAR},
      EAWB_HANDLETIME = #{eawbHandletime,jdbcType=TIMESTAMP},
      EAWB_SHIPPER_ACCOUNT = #{eawbShipperAccount,jdbcType=VARCHAR},
      EAWB_PICKUP_ADDRESS = #{eawbPickupAddress,jdbcType=VARCHAR},
      EAWB_PICKUP_POSTCODE = #{eawbPickupPostcode,jdbcType=VARCHAR},
      EAWB_PICKUP_CONTACT = #{eawbPickupContact,jdbcType=VARCHAR},
      EAWB_PICKUP_PHONE = #{eawbPickupPhone,jdbcType=VARCHAR},
      EAWB_CONSIGNEE_ACCOUNT = #{eawbConsigneeAccount,jdbcType=VARCHAR},
      EAWB_DELIVER_ADDRESS = #{eawbDeliverAddress,jdbcType=VARCHAR},
      EAWB_DELIVER_POSTCODE = #{eawbDeliverPostcode,jdbcType=VARCHAR},
      EAWB_DELIVER_CONTACT = #{eawbDeliverContact,jdbcType=VARCHAR},
      EAWB_DELIVER_PHONE = #{eawbDeliverPhone,jdbcType=VARCHAR},
      EAWB_DEPARTURE = #{eawbDeparture,jdbcType=VARCHAR},
      EAWB_DESTINATION = #{eawbDestination,jdbcType=VARCHAR},
      EAWB_PRODUCTNAME = #{eawbProductname,jdbcType=VARCHAR},
      EAWB_TOTALPIECES = #{eawbTotalpieces,jdbcType=DECIMAL},
      EAWB_TOTALVOLUME = #{eawbTotalvolume,jdbcType=DECIMAL},
      EAWB_TOTALGROSSWEIGHT = #{eawbTotalgrossweight,jdbcType=DECIMAL},
      EAWB_TOTALCHARGEABLEWEIGHT = #{eawbTotalchargeableweight,jdbcType=DECIMAL},
      EAWB_DECLAREVOLUME = #{eawbDeclarevolume,jdbcType=DECIMAL},
      EAWB_DECLAREGROSSWEIGHT = #{eawbDeclaregrossweight,jdbcType=DECIMAL},
      EAWB_DECLARECHARGEABLE = #{eawbDeclarechargeable,jdbcType=DECIMAL},
      EAWB_PAYMENTMODE = #{eawbPaymentmode,jdbcType=VARCHAR},
      EAWB_THIRDPARTY_ACCOUNT = #{eawbThirdpartyAccount,jdbcType=VARCHAR},
      CT_CODE = #{ctCode,jdbcType=VARCHAR},
      EAWB_CURRENCYRATE = #{eawbCurrencyrate,jdbcType=VARCHAR},
      EAWB_TOTALRMB = #{eawbTotalrmb,jdbcType=DECIMAL},
      EAWB_TOTALFC = #{eawbTotalfc,jdbcType=DECIMAL},
      SAC_ID = #{sacId,jdbcType=VARCHAR},
      EAWB_SAC_CODE = #{eawbSacCode,jdbcType=VARCHAR},
      EB_CODE = #{ebCode,jdbcType=VARCHAR},
      EAWB_SO_CODE = #{eawbSoCode,jdbcType=VARCHAR},
      EAWB_SHIPPER_ACCOUNTNAME = #{eawbShipperAccountname,jdbcType=VARCHAR},
      EAWB_CONSIGNEE_ACCOUNTNAME = #{eawbConsigneeAccountname,jdbcType=VARCHAR},
      EAWB_PICKUP_BLOCK = #{eawbPickupBlock,jdbcType=VARCHAR},
      EAWB_PICKUP_TIME = #{eawbPickupTime,jdbcType=TIMESTAMP},
      EAWB_FREIGHTCHARGE = #{eawbFreightcharge,jdbcType=DECIMAL},
      EAWB_INCIDENTALCHARGE = #{eawbIncidentalcharge,jdbcType=DECIMAL},
      EAWB_INSURANCECHARGE = #{eawbInsurancecharge,jdbcType=DECIMAL},
      EAWB_PPCC = #{eawbPpcc,jdbcType=VARCHAR},
      EAWB_DECLAREVALUE = #{eawbDeclarevalue,jdbcType=DECIMAL},
      EAWB_REFERENCE1 = #{eawbReference1,jdbcType=VARCHAR},
      EAWB_REFERENCE2 = #{eawbReference2,jdbcType=VARCHAR},
      EAWB_STANDARDFREIGHTPRICE = #{eawbStandardfreightprice,jdbcType=DECIMAL},
      EAWB_STATUS = #{eawbStatus,jdbcType=VARCHAR},
      EAWB_PICKUPBYCONSIGNEE = #{eawbPickupbyconsignee,jdbcType=VARCHAR},
      EAWB_DELIVERYATHOLIDAY = #{eawbDeliveryatholiday,jdbcType=VARCHAR},
      EAWB_INNER = #{eawbInner,jdbcType=VARCHAR},
      EAWB_INBOUND_SAC_ID = #{eawbInboundSacId,jdbcType=VARCHAR},
      EAWB_OUTBOUND_SAC_ID = #{eawbOutboundSacId,jdbcType=VARCHAR},
      EAWB_PRODUCTDECLARE = #{eawbProductdeclare,jdbcType=VARCHAR},
      EAWB_SERVICEREQUIREMENT = #{eawbServicerequirement,jdbcType=VARCHAR},
      EAWB_REFERENCE3 = #{eawbReference3,jdbcType=VARCHAR},
      EAWB_C_CODE = #{eawbCCode,jdbcType=VARCHAR},
      EAWB_TIME_LIMIT = #{eawbTimeLimit,jdbcType=TIMESTAMP},
      EAWB_COLLECTPAYMENTFORGOODS = #{eawbCollectpaymentforgoods,jdbcType=DECIMAL},
      EAWB_SELFINSURANCE = #{eawbSelfinsurance,jdbcType=VARCHAR},
      EAWB_KEYENTRYTIME = #{eawbKeyentrytime,jdbcType=TIMESTAMP},
      EAWB_PRESERVATION = #{eawbPreservation,jdbcType=VARCHAR},
      EAWB_INSURANCESERVICE = #{eawbInsuranceservice,jdbcType=VARCHAR},
      EAWB_AGENT_CODE = #{eawbAgentCode,jdbcType=VARCHAR},
      EAWB_BUSINESSMODE = #{eawbBusinessmode,jdbcType=VARCHAR},
      EAWB_PRE_FREIGHTPRICE = #{eawbPreFreightprice,jdbcType=DECIMAL},
      EAWB_SENDVOICEREQUEST = #{eawbSendvoicerequest,jdbcType=CHAR},
      CBC_SHIP_CODE = #{cbcShipCode,jdbcType=VARCHAR},
      EAWB_ROUTE = #{eawbRoute,jdbcType=VARCHAR},
      EAWB_DEPARTCITY = #{eawbDepartcity,jdbcType=VARCHAR},
      EAWB_DEPARTSTATE = #{eawbDepartstate,jdbcType=VARCHAR},
      EAWB_DEPARTCOUNTRY = #{eawbDepartcountry,jdbcType=VARCHAR},
      EAWB_DESTCITY = #{eawbDestcity,jdbcType=VARCHAR},
      EAWB_DESTSTATE = #{eawbDeststate,jdbcType=VARCHAR},
      EAWB_DESTCOUNTRY = #{eawbDestcountry,jdbcType=VARCHAR},
      EAWB_TYPE = #{eawbType,jdbcType=VARCHAR},
      EAWB_CUSTPRODNAME = #{eawbCustprodname,jdbcType=VARCHAR},
      EAWB_QUANTITY = #{eawbQuantity,jdbcType=DECIMAL},
      EAWB_CUSTDECLVAL = #{eawbCustdeclval,jdbcType=DECIMAL},
      EAWB_CUSTCURRENCY = #{eawbCustcurrency,jdbcType=VARCHAR},
      EAWB_DECLCURRENCY = #{eawbDeclcurrency,jdbcType=VARCHAR},
      EAWB_CONSIGNMENTNO = #{eawbConsignmentno,jdbcType=VARCHAR},
      EAWB_KJTYPE = #{eawbKjtype,jdbcType=VARCHAR},
      EAWB_IETYPE = #{eawbIetype,jdbcType=VARCHAR},
      B_CODE = #{bCode,jdbcType=VARCHAR},
      EAWB_FUELCHARGE = #{eawbFuelcharge,jdbcType=DECIMAL},
      EAWB_INSUTYPE = #{eawbInsutype,jdbcType=VARCHAR},
      EAWB_INSUAMOUNTTYPE = #{eawbInsuamounttype,jdbcType=VARCHAR},
      EAWB_INSUAMOUNT = #{eawbInsuamount,jdbcType=DECIMAL},
      EAWB_PRE_FUELPRICE = #{eawbPreFuelprice,jdbcType=DECIMAL},
      HANDSET_CARGO = #{handsetCargo,jdbcType=VARCHAR},
      INFOXML = #{infoxml,jdbcType=VARCHAR},
      EAWB_DISCOUNTVALUE = #{eawbDiscountvalue,jdbcType=DECIMAL},
      EAWB_TRANSMODEID = #{eawbTransmodeid,jdbcType=VARCHAR},
      EAWB_PRODUCTTYPE = #{eawbProducttype,jdbcType=VARCHAR},
      EAWB_ORIGINCITY = #{eawbOrigincity,jdbcType=VARCHAR},
      EAWB_SHIPPER_CACCOUNTNAME = #{eawbShipperCaccountname,jdbcType=VARCHAR},
      EAWB_DELIVER_FAX = #{eawbDeliverFax,jdbcType=VARCHAR},
      EAWB_SPECIFICATION = #{eawbSpecification,jdbcType=VARCHAR},
      EAWB_CCTYPE = #{eawbCctype,jdbcType=VARCHAR},
      EAWB_CUSTREGISTRATIONCODE = #{eawbCustregistrationcode,jdbcType=VARCHAR},
      EAWB_CUSTREGISTRATIONAME = #{eawbCustregistrationame,jdbcType=VARCHAR},
      EAWB_ENTRUSTCODE = #{eawbEntrustcode,jdbcType=VARCHAR},
      EAWB_HSCODE = #{eawbHscode,jdbcType=VARCHAR},
      EAWB_CUSTPRODENNAME = #{eawbCustprodenname,jdbcType=VARCHAR},
      EAWB_OPERATESTATUS = #{eawbOperatestatus,jdbcType=VARCHAR},
      EAWB_UNIT = #{eawbUnit,jdbcType=VARCHAR},
      EAWB_UNITCODE = #{eawbUnitcode,jdbcType=VARCHAR},
      EAWB_CHECKSTATUS = #{eawbCheckstatus,jdbcType=VARCHAR},
      EAWB_SERVICETYPE = #{eawbServicetype,jdbcType=VARCHAR},
      MAWB_CODE = #{mawbCode,jdbcType=VARCHAR},
      FLIGHT_NO = #{flightNo,jdbcType=VARCHAR},
      CUSTOM_TYPE = #{customType,jdbcType=VARCHAR},
      EAWB_ECOMMERCE = #{eawbEcommerce,jdbcType=VARCHAR},
      EAWB_TAXCODE = #{eawbTaxcode,jdbcType=VARCHAR},
      EAWB_DELIVER_INDENTITYCARDNO = #{eawbDeliverIndentitycardno,jdbcType=VARCHAR},
      CUSTOMS_STATUS = #{customsStatus,jdbcType=VARCHAR},
      CSEF_SYSCODE = #{csefSyscode,jdbcType=DECIMAL},
      ETCOPY = #{etcopy,jdbcType=DECIMAL},
      EAWB_TRACK = #{eawbTrack,jdbcType=VARCHAR},
      EAWB_DELIVER_EMAIL = #{eawbDeliverEmail,jdbcType=VARCHAR},
      EAWB_PARTITION = #{eawbPartition,jdbcType=VARCHAR},
      EAWB_DELIVERY_POSTCODE_CORRECT = #{eawbDeliveryPostcodeCorrect,jdbcType=VARCHAR},
      EAWB_CHANGE_LABEL_STATUS = #{eawbChangeLabelStatus,jdbcType=VARCHAR},
      EAWB_REFUND_WANGWANG_ID = #{eawbRefundWangwangId,jdbcType=VARCHAR},
      EAWB_REFUND_NAME = #{eawbRefundName,jdbcType=VARCHAR},
      EAWB_REFUND_PHONE = #{eawbRefundPhone,jdbcType=VARCHAR},
      EAWB_REFUND_MOBILE = #{eawbRefundMobile,jdbcType=VARCHAR},
      EAWB_REFUND_EMAIL = #{eawbRefundEmail,jdbcType=VARCHAR},
      EAWB_REFUND_COUNTRY = #{eawbRefundCountry,jdbcType=VARCHAR},
      EAWB_REFUND_PRIVINCE = #{eawbRefundPrivince,jdbcType=VARCHAR},
      EAWB_REFUND_CITY = #{eawbRefundCity,jdbcType=VARCHAR},
      EAWB_REFUND_DISTRICT = #{eawbRefundDistrict,jdbcType=VARCHAR},
      EAWB_REFUND_STREET = #{eawbRefundStreet,jdbcType=VARCHAR},
      EAWB_REFUND_ZIPCODE = #{eawbRefundZipcode,jdbcType=VARCHAR},
      EAWB_UNDELIVERY_OPTION = #{eawbUndeliveryOption,jdbcType=VARCHAR},
      EAWB_CARRIERNAME = #{eawbCarriername,jdbcType=VARCHAR},
      EAWB_CARRIERNO = #{eawbCarrierno,jdbcType=VARCHAR},
      ORDER_CODE_IN = #{orderCodeIn,jdbcType=VARCHAR},
      ORDER_SYSCODE_IN = #{orderSyscodeIn,jdbcType=DECIMAL},
      ORDER_CODE_OUT = #{orderCodeOut,jdbcType=VARCHAR},
      ORDER_SYSCODE_OUT = #{orderSyscodeOut,jdbcType=DECIMAL},
      CUSTOMER_ORDER_CODE = #{customerOrderCode,jdbcType=VARCHAR},
      EAWB_SERVICETYPE_ORIGINAL = #{eawbServicetypeOriginal,jdbcType=VARCHAR},
      EAWB_LENGTH = #{eawbLength,jdbcType=DECIMAL},
      EAWB_WIDTH = #{eawbWidth,jdbcType=DECIMAL},
      EAWB_HEIGHT = #{eawbHeight,jdbcType=DECIMAL},
      EAWB_DELIVER_MOBILE = #{eawbDeliverMobile,jdbcType=VARCHAR},
      EAWB_COD = #{eawbCod,jdbcType=VARCHAR},
      EAWB_CODVALUE = #{eawbCodvalue,jdbcType=DECIMAL},
      EAWB_CODCURRENCY = #{eawbCodcurrency,jdbcType=VARCHAR},
      EAWB_REFUND_REFERENCE = #{eawbRefundReference,jdbcType=VARCHAR},
      EAWB_TRANSMODEID_ORIGINAL = #{eawbTransmodeidOriginal,jdbcType=VARCHAR},
      EAWB_REFUND_SUPPLIER = #{eawbRefundSupplier,jdbcType=CHAR},
      EAWB_REFUND_REFERENCE1 = #{eawbRefundReference1,jdbcType=CHAR},
      EAWB_REFUND_REFERENCE2 = #{eawbRefundReference2,jdbcType=CHAR},
      EAWB_REFUND_REFERENCE3 = #{eawbRefundReference3,jdbcType=VARCHAR},
      EAWB_REFUND_ADDRESS = #{eawbRefundAddress,jdbcType=VARCHAR},
      REFUND_STATUS = #{refundStatus,jdbcType=VARCHAR},
      EAWB_DELIVER_STREET = #{eawbDeliverStreet,jdbcType=VARCHAR},
      EAWB_NEXT_OPT_CENTER = #{eawbNextOptCenter,jdbcType=VARCHAR},
      EAWB_PRE_OPT_CENTER = #{eawbPreOptCenter,jdbcType=VARCHAR},
      EAWB_TRUNK_CODE = #{eawbTrunkCode,jdbcType=VARCHAR},
      EAWB_CHANNEL_CODE = #{eawbChannelCode,jdbcType=VARCHAR},
      EAWB_DELIVER_DISTRICT = #{eawbDeliverDistrict,jdbcType=VARCHAR},
      EAWB_PICKUP_DISTRICT = #{eawbPickupDistrict,jdbcType=VARCHAR},
      EAWB_SENDER_ADDRESS = #{eawbSenderAddress,jdbcType=VARCHAR},
      EAWB_SORTCODE = #{eawbSortcode,jdbcType=VARCHAR},
      EAWB_CONSIGNEE_LATITUDE = #{eawbConsigneeLatitude,jdbcType=VARCHAR},
      EAWB_CONSIGNEE_LONGITUDE = #{eawbConsigneeLongitude,jdbcType=VARCHAR},
      EAWB_FIRSTMILE = #{eawbFirstmile,jdbcType=VARCHAR},
      EAWB_PAYMENTID = #{eawbPaymentid,jdbcType=VARCHAR},
      EAWB_PAYMENTEMAIL = #{eawbPaymentemail,jdbcType=VARCHAR},
      EAWB_PAYMENTCONTACTNAME = #{eawbPaymentcontactname,jdbcType=VARCHAR},
      EAWB_PAYMENTPHONENUMBER = #{eawbPaymentphonenumber,jdbcType=VARCHAR},
      EAWB_SELLERTAXNUMBER = #{eawbSellertaxnumber,jdbcType=VARCHAR},
      EAWB_INVOICENUMBER = #{eawbInvoicenumber,jdbcType=VARCHAR},
      EAWB_PRODUCTURL = #{eawbProducturl,jdbcType=VARCHAR}
    where EAWB_SYSCODE = #{eawbSyscode,jdbcType=DECIMAL}
  </update>

  <insert id="insertBatch" parameterType="java.util.ArrayList">
    insert into EXPRESSAIRWAYBILL_CEOS (EAWB_SYSCODE, EAWB_CODE, EAWB_PRINTCODE,
    EAWB_E_ID_HANDLER, EST_CODE, EAWB_HANDLETIME,
    EAWB_SHIPPER_ACCOUNT, EAWB_PICKUP_ADDRESS,
    EAWB_PICKUP_POSTCODE, EAWB_PICKUP_CONTACT,
    EAWB_PICKUP_PHONE, EAWB_CONSIGNEE_ACCOUNT,
    EAWB_DELIVER_ADDRESS, EAWB_DELIVER_POSTCODE,
    EAWB_DELIVER_CONTACT, EAWB_DELIVER_PHONE, EAWB_DEPARTURE,
    EAWB_DESTINATION, EAWB_PRODUCTNAME, EAWB_TOTALPIECES,
    EAWB_TOTALVOLUME, EAWB_TOTALGROSSWEIGHT,
    EAWB_TOTALCHARGEABLEWEIGHT, EAWB_DECLAREVOLUME,
    EAWB_DECLAREGROSSWEIGHT, EAWB_DECLARECHARGEABLE,
    EAWB_PAYMENTMODE, EAWB_THIRDPARTY_ACCOUNT,
    CT_CODE, EAWB_CURRENCYRATE, EAWB_TOTALRMB,
    EAWB_TOTALFC, SAC_ID, EAWB_SAC_CODE,
    EB_CODE, EAWB_SO_CODE, EAWB_SHIPPER_ACCOUNTNAME,
    EAWB_CONSIGNEE_ACCOUNTNAME, EAWB_PICKUP_BLOCK,
    EAWB_PICKUP_TIME, EAWB_FREIGHTCHARGE, EAWB_INCIDENTALCHARGE,
    EAWB_INSURANCECHARGE, EAWB_PPCC, EAWB_DECLAREVALUE,
    EAWB_REFERENCE1, EAWB_REFERENCE2, EAWB_STANDARDFREIGHTPRICE,
    EAWB_STATUS, EAWB_PICKUPBYCONSIGNEE, EAWB_DELIVERYATHOLIDAY,
    EAWB_INNER, EAWB_INBOUND_SAC_ID, EAWB_OUTBOUND_SAC_ID,
    EAWB_PRODUCTDECLARE, EAWB_SERVICEREQUIREMENT,
    EAWB_REFERENCE3, EAWB_C_CODE, EAWB_TIME_LIMIT,
    EAWB_COLLECTPAYMENTFORGOODS, EAWB_SELFINSURANCE,
    EAWB_KEYENTRYTIME, EAWB_PRESERVATION, EAWB_INSURANCESERVICE,
    EAWB_AGENT_CODE, EAWB_BUSINESSMODE, EAWB_PRE_FREIGHTPRICE,
    EAWB_SENDVOICEREQUEST, CBC_SHIP_CODE, EAWB_ROUTE,
    EAWB_DEPARTCITY, EAWB_DEPARTSTATE, EAWB_DEPARTCOUNTRY,
    EAWB_DESTCITY, EAWB_DESTSTATE, EAWB_DESTCOUNTRY,
    EAWB_TYPE, EAWB_CUSTPRODNAME, EAWB_QUANTITY,
    EAWB_CUSTDECLVAL, EAWB_CUSTCURRENCY, EAWB_DECLCURRENCY,
    EAWB_CONSIGNMENTNO, EAWB_KJTYPE, EAWB_IETYPE,
    B_CODE, EAWB_FUELCHARGE, EAWB_INSUTYPE,
    EAWB_INSUAMOUNTTYPE, EAWB_INSUAMOUNT, EAWB_PRE_FUELPRICE,
    HANDSET_CARGO, INFOXML, EAWB_DISCOUNTVALUE,
    EAWB_TRANSMODEID, EAWB_PRODUCTTYPE, EAWB_ORIGINCITY,
    EAWB_SHIPPER_CACCOUNTNAME, EAWB_DELIVER_FAX,
    EAWB_SPECIFICATION, EAWB_CCTYPE, EAWB_CUSTREGISTRATIONCODE,
    EAWB_CUSTREGISTRATIONAME, EAWB_ENTRUSTCODE,
    EAWB_HSCODE, EAWB_CUSTPRODENNAME, EAWB_OPERATESTATUS,
    EAWB_UNIT, EAWB_UNITCODE, EAWB_CHECKSTATUS,
    EAWB_SERVICETYPE, MAWB_CODE, FLIGHT_NO,
    CUSTOM_TYPE, EAWB_ECOMMERCE, EAWB_TAXCODE,
    EAWB_DELIVER_INDENTITYCARDNO, CUSTOMS_STATUS,
    CSEF_SYSCODE, ETCOPY, EAWB_TRACK,
    EAWB_DELIVER_EMAIL, EAWB_PARTITION, EAWB_DELIVERY_POSTCODE_CORRECT,
    EAWB_CHANGE_LABEL_STATUS, EAWB_REFUND_WANGWANG_ID,
    EAWB_REFUND_NAME, EAWB_REFUND_PHONE, EAWB_REFUND_MOBILE,
    EAWB_REFUND_EMAIL, EAWB_REFUND_COUNTRY, EAWB_REFUND_PRIVINCE,
    EAWB_REFUND_CITY, EAWB_REFUND_DISTRICT, EAWB_REFUND_STREET,
    EAWB_REFUND_ZIPCODE, EAWB_UNDELIVERY_OPTION,
    EAWB_CARRIERNAME, EAWB_CARRIERNO, ORDER_CODE_IN,
    ORDER_SYSCODE_IN, ORDER_CODE_OUT, ORDER_SYSCODE_OUT,
    CUSTOMER_ORDER_CODE, EAWB_SERVICETYPE_ORIGINAL,
    EAWB_LENGTH, EAWB_WIDTH, EAWB_HEIGHT,
    EAWB_DELIVER_MOBILE, EAWB_COD, EAWB_CODVALUE,
    EAWB_CODCURRENCY, EAWB_REFUND_REFERENCE, EAWB_TRANSMODEID_ORIGINAL,
    EAWB_REFUND_SUPPLIER, EAWB_REFUND_REFERENCE1, EAWB_REFUND_REFERENCE2,
    EAWB_REFUND_REFERENCE3, EAWB_REFUND_ADDRESS,
    REFUND_STATUS, EAWB_DELIVER_STREET, EAWB_NEXT_OPT_CENTER,
    EAWB_PRE_OPT_CENTER, EAWB_TRUNK_CODE, EAWB_CHANNEL_CODE,
    EAWB_DELIVER_DISTRICT, EAWB_PICKUP_DISTRICT,
    EAWB_SENDER_ADDRESS, EAWB_SORTCODE, EAWB_CONSIGNEE_LATITUDE,
    EAWB_CONSIGNEE_LONGITUDE, EAWB_FIRSTMILE,
    EAWB_PAYMENTID, EAWB_PAYMENTEMAIL, EAWB_PAYMENTCONTACTNAME,
    EAWB_PAYMENTPHONENUMBER, EAWB_SELLERTAXNUMBER,
    EAWB_INVOICENUMBER, EAWB_PRODUCTURL)

    <foreach collection="list" item="item" index="index" separator="UNION ALL">
      <![CDATA[
      select #{item.eawbSyscode,jdbcType=DECIMAL}, #{item.eawbCode,jdbcType=VARCHAR}, #{item.eawbPrintcode,jdbcType=VARCHAR},
      #{item.eawbEIdHandler,jdbcType=DECIMAL}, #{item.estCode,jdbcType=VARCHAR}, #{item.eawbHandletime,jdbcType=TIMESTAMP},
      #{item.eawbShipperAccount,jdbcType=VARCHAR}, #{item.eawbPickupAddress,jdbcType=VARCHAR},
      #{item.eawbPickupPostcode,jdbcType=VARCHAR}, #{item.eawbPickupContact,jdbcType=VARCHAR},
      #{item.eawbPickupPhone,jdbcType=VARCHAR}, #{item.eawbConsigneeAccount,jdbcType=VARCHAR},
      #{item.eawbDeliverAddress,jdbcType=VARCHAR}, #{item.eawbDeliverPostcode,jdbcType=VARCHAR},
      #{item.eawbDeliverContact,jdbcType=VARCHAR}, #{item.eawbDeliverPhone,jdbcType=VARCHAR}, #{item.eawbDeparture,jdbcType=VARCHAR},
      #{item.eawbDestination,jdbcType=VARCHAR}, #{item.eawbProductname,jdbcType=VARCHAR}, #{item.eawbTotalpieces,jdbcType=DECIMAL},
      #{item.eawbTotalvolume,jdbcType=DECIMAL}, #{item.eawbTotalgrossweight,jdbcType=DECIMAL},
      #{item.eawbTotalchargeableweight,jdbcType=DECIMAL}, #{item.eawbDeclarevolume,jdbcType=DECIMAL},
      #{item.eawbDeclaregrossweight,jdbcType=DECIMAL}, #{item.eawbDeclarechargeable,jdbcType=DECIMAL},
      #{item.eawbPaymentmode,jdbcType=VARCHAR}, #{item.eawbThirdpartyAccount,jdbcType=VARCHAR},
      #{item.ctCode,jdbcType=VARCHAR}, #{item.eawbCurrencyrate,jdbcType=VARCHAR}, #{item.eawbTotalrmb,jdbcType=DECIMAL},
      #{item.eawbTotalfc,jdbcType=DECIMAL}, #{item.sacId,jdbcType=VARCHAR}, #{item.eawbSacCode,jdbcType=VARCHAR},
      #{item.ebCode,jdbcType=VARCHAR}, #{item.eawbSoCode,jdbcType=VARCHAR}, #{item.eawbShipperAccountname,jdbcType=VARCHAR},
      #{item.eawbConsigneeAccountname,jdbcType=VARCHAR}, #{item.eawbPickupBlock,jdbcType=VARCHAR},
      #{item.eawbPickupTime,jdbcType=TIMESTAMP}, #{item.eawbFreightcharge,jdbcType=DECIMAL}, #{item.eawbIncidentalcharge,jdbcType=DECIMAL},
      #{item.eawbInsurancecharge,jdbcType=DECIMAL}, #{item.eawbPpcc,jdbcType=VARCHAR}, #{item.eawbDeclarevalue,jdbcType=DECIMAL},
      #{item.eawbReference1,jdbcType=VARCHAR}, #{item.eawbReference2,jdbcType=VARCHAR}, #{item.eawbStandardfreightprice,jdbcType=DECIMAL},
      #{item.eawbStatus,jdbcType=VARCHAR}, #{item.eawbPickupbyconsignee,jdbcType=VARCHAR}, #{item.eawbDeliveryatholiday,jdbcType=VARCHAR},
      #{item.eawbInner,jdbcType=VARCHAR}, #{item.eawbInboundSacId,jdbcType=VARCHAR}, #{item.eawbOutboundSacId,jdbcType=VARCHAR},
      #{item.eawbProductdeclare,jdbcType=VARCHAR}, #{item.eawbServicerequirement,jdbcType=VARCHAR},
      #{item.eawbReference3,jdbcType=VARCHAR}, #{item.eawbCCode,jdbcType=VARCHAR}, #{item.eawbTimeLimit,jdbcType=TIMESTAMP},
      #{item.eawbCollectpaymentforgoods,jdbcType=DECIMAL}, #{item.eawbSelfinsurance,jdbcType=VARCHAR},
      #{item.eawbKeyentrytime,jdbcType=TIMESTAMP}, #{item.eawbPreservation,jdbcType=VARCHAR}, #{item.eawbInsuranceservice,jdbcType=VARCHAR},
      #{item.eawbAgentCode,jdbcType=VARCHAR}, #{item.eawbBusinessmode,jdbcType=VARCHAR}, #{item.eawbPreFreightprice,jdbcType=DECIMAL},
      #{item.eawbSendvoicerequest,jdbcType=CHAR}, #{item.cbcShipCode,jdbcType=VARCHAR}, #{item.eawbRoute,jdbcType=VARCHAR},
      #{item.eawbDepartcity,jdbcType=VARCHAR}, #{item.eawbDepartstate,jdbcType=VARCHAR}, #{item.eawbDepartcountry,jdbcType=VARCHAR},
      #{item.eawbDestcity,jdbcType=VARCHAR}, #{item.eawbDeststate,jdbcType=VARCHAR}, #{item.eawbDestcountry,jdbcType=VARCHAR},
      #{item.eawbType,jdbcType=VARCHAR}, #{item.eawbCustprodname,jdbcType=VARCHAR}, #{item.eawbQuantity,jdbcType=DECIMAL},
      #{item.eawbCustdeclval,jdbcType=DECIMAL}, #{item.eawbCustcurrency,jdbcType=VARCHAR}, #{item.eawbDeclcurrency,jdbcType=VARCHAR},
      #{item.eawbConsignmentno,jdbcType=VARCHAR}, #{item.eawbKjtype,jdbcType=VARCHAR}, #{item.eawbIetype,jdbcType=VARCHAR},
      #{item.bCode,jdbcType=VARCHAR}, #{item.eawbFuelcharge,jdbcType=DECIMAL}, #{item.eawbInsutype,jdbcType=VARCHAR},
      #{item.eawbInsuamounttype,jdbcType=VARCHAR}, #{item.eawbInsuamount,jdbcType=DECIMAL}, #{item.eawbPreFuelprice,jdbcType=DECIMAL},
      #{item.handsetCargo,jdbcType=VARCHAR}, #{item.infoxml,jdbcType=VARCHAR}, #{item.eawbDiscountvalue,jdbcType=DECIMAL},
      #{item.eawbTransmodeid,jdbcType=VARCHAR}, #{item.eawbProducttype,jdbcType=VARCHAR}, #{item.eawbOrigincity,jdbcType=VARCHAR},
      #{item.eawbShipperCaccountname,jdbcType=VARCHAR}, #{item.eawbDeliverFax,jdbcType=VARCHAR},
      #{item.eawbSpecification,jdbcType=VARCHAR}, #{item.eawbCctype,jdbcType=VARCHAR}, #{item.eawbCustregistrationcode,jdbcType=VARCHAR},
      #{item.eawbCustregistrationame,jdbcType=VARCHAR}, #{item.eawbEntrustcode,jdbcType=VARCHAR},
      #{item.eawbHscode,jdbcType=VARCHAR}, #{item.eawbCustprodenname,jdbcType=VARCHAR}, #{item.eawbOperatestatus,jdbcType=VARCHAR},
      #{item.eawbUnit,jdbcType=VARCHAR}, #{item.eawbUnitcode,jdbcType=VARCHAR}, #{item.eawbCheckstatus,jdbcType=VARCHAR},
      #{item.eawbServicetype,jdbcType=VARCHAR}, #{item.mawbCode,jdbcType=VARCHAR}, #{item.flightNo,jdbcType=VARCHAR},
      #{item.customType,jdbcType=VARCHAR}, #{item.eawbEcommerce,jdbcType=VARCHAR}, #{item.eawbTaxcode,jdbcType=VARCHAR},
      #{item.eawbDeliverIndentitycardno,jdbcType=VARCHAR}, #{item.customsStatus,jdbcType=VARCHAR},
      #{item.csefSyscode,jdbcType=DECIMAL}, #{item.etcopy,jdbcType=DECIMAL}, #{item.eawbTrack,jdbcType=VARCHAR},
      #{item.eawbDeliverEmail,jdbcType=VARCHAR}, #{item.eawbPartition,jdbcType=VARCHAR}, #{item.eawbDeliveryPostcodeCorrect,jdbcType=VARCHAR},
      #{item.eawbChangeLabelStatus,jdbcType=VARCHAR}, #{item.eawbRefundWangwangId,jdbcType=VARCHAR},
      #{item.eawbRefundName,jdbcType=VARCHAR}, #{item.eawbRefundPhone,jdbcType=VARCHAR}, #{item.eawbRefundMobile,jdbcType=VARCHAR},
      #{item.eawbRefundEmail,jdbcType=VARCHAR}, #{item.eawbRefundCountry,jdbcType=VARCHAR}, #{item.eawbRefundPrivince,jdbcType=VARCHAR},
      #{item.eawbRefundCity,jdbcType=VARCHAR}, #{item.eawbRefundDistrict,jdbcType=VARCHAR}, #{item.eawbRefundStreet,jdbcType=VARCHAR},
      #{item.eawbRefundZipcode,jdbcType=VARCHAR}, #{item.eawbUndeliveryOption,jdbcType=VARCHAR},
      #{item.eawbCarriername,jdbcType=VARCHAR}, #{item.eawbCarrierno,jdbcType=VARCHAR}, #{item.orderCodeIn,jdbcType=VARCHAR},
      #{item.orderSyscodeIn,jdbcType=DECIMAL}, #{item.orderCodeOut,jdbcType=VARCHAR}, #{item.orderSyscodeOut,jdbcType=DECIMAL},
      #{item.customerOrderCode,jdbcType=VARCHAR}, #{item.eawbServicetypeOriginal,jdbcType=VARCHAR},
      #{item.eawbLength,jdbcType=DECIMAL}, #{item.eawbWidth,jdbcType=DECIMAL}, #{item.eawbHeight,jdbcType=DECIMAL},
      #{item.eawbDeliverMobile,jdbcType=VARCHAR}, #{item.eawbCod,jdbcType=VARCHAR}, #{item.eawbCodvalue,jdbcType=DECIMAL},
      #{item.eawbCodcurrency,jdbcType=VARCHAR}, #{item.eawbRefundReference,jdbcType=VARCHAR}, #{item.eawbTransmodeidOriginal,jdbcType=VARCHAR},
      #{item.eawbRefundSupplier,jdbcType=CHAR}, #{item.eawbRefundReference1,jdbcType=CHAR}, #{item.eawbRefundReference2,jdbcType=CHAR},
      #{item.eawbRefundReference3,jdbcType=VARCHAR}, #{item.eawbRefundAddress,jdbcType=VARCHAR},
      #{item.refundStatus,jdbcType=VARCHAR}, #{item.eawbDeliverStreet,jdbcType=VARCHAR}, #{item.eawbNextOptCenter,jdbcType=VARCHAR},
      #{item.eawbPreOptCenter,jdbcType=VARCHAR}, #{item.eawbTrunkCode,jdbcType=VARCHAR}, #{item.eawbChannelCode,jdbcType=VARCHAR},
      #{item.eawbDeliverDistrict,jdbcType=VARCHAR}, #{item.eawbPickupDistrict,jdbcType=VARCHAR},
      #{item.eawbSenderAddress,jdbcType=VARCHAR}, #{item.eawbSortcode,jdbcType=VARCHAR}, #{item.eawbConsigneeLatitude,jdbcType=VARCHAR},
      #{item.eawbConsigneeLongitude,jdbcType=VARCHAR}, #{item.eawbFirstmile,jdbcType=VARCHAR},
      #{item.eawbPaymentid,jdbcType=VARCHAR}, #{item.eawbPaymentemail,jdbcType=VARCHAR}, #{item.eawbPaymentcontactname,jdbcType=VARCHAR},
      #{item.eawbPaymentphonenumber,jdbcType=VARCHAR}, #{item.eawbSellertaxnumber,jdbcType=VARCHAR},
      #{item.eawbInvoicenumber,jdbcType=VARCHAR}, #{item.eawbProducturl,jdbcType=VARCHAR} from dual
      ]]>
    </foreach>
  </insert>

  <delete id="deleteBySoCodeEmpty"  >
    delete from EXPRESSAIRWAYBILL_CEOS
    where EAWB_SO_CODE IS NULL
  </delete>
</mapper>