<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.ExpressAirWayBillMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.ExpressAirWayBill" >
    <id column="EAWB_SYSCODE" property="eawbSyscode" jdbcType="DECIMAL" />
    <result column="EAWB_PRINTCODE" property="eawbPrintcode" jdbcType="VARCHAR" />
    <result column="EST_CODE" property="estCode" jdbcType="VARCHAR" />
    <result column="EAWB_HANDLETIME" property="eawbHandletime" jdbcType="TIMESTAMP" />
    <result column="EAWB_DEPARTURE" property="eawbDeparture" jdbcType="VARCHAR" />
    <result column="EAWB_DESTINATION" property="eawbDestination" jdbcType="VARCHAR" />
    <result column="EAWB_PIECES" property="eawbPieces" jdbcType="DECIMAL" />
    <result column="EAWB_VOLUME" property="eawbVolume" jdbcType="DECIMAL" />
    <result column="EAWB_GROSSWEIGHT" property="eawbGrossweight" jdbcType="DECIMAL" />
    <result column="EAWB_CHARGEABLEWEIGHT" property="eawbChargeableweight" jdbcType="DECIMAL" />
    <result column="CT_CODE" property="ctCode" jdbcType="VARCHAR" />
    <result column="SAC_ID" property="sacId" jdbcType="VARCHAR" />
    <result column="EAWB_SO_CODE" property="eawbSoCode" jdbcType="VARCHAR" />
    <result column="EAWB_REFERENCE1" property="eawbReference1" jdbcType="VARCHAR" />
    <result column="EAWB_REFERENCE2" property="eawbReference2" jdbcType="VARCHAR" />
    <result column="EAWB_STATUS" property="eawbStatus" jdbcType="VARCHAR" />
    <result column="EAWB_OUTBOUND_SAC_ID" property="eawbOutboundSacId" jdbcType="VARCHAR" />
    <result column="EAWB_KEYENTRYTIME" property="eawbKeyentrytime" jdbcType="TIMESTAMP" />
    <result column="EAWB_DEPARTCOUNTRY" property="eawbDepartcountry" jdbcType="VARCHAR" />
    <result column="EAWB_DESTCOUNTRY" property="eawbDestcountry" jdbcType="VARCHAR" />
    <result column="EAWB_CUSTPRODNAME" property="eawbCustprodname" jdbcType="VARCHAR" />
    <result column="EAWB_TRANSMODEID" property="eawbTransmodeid" jdbcType="VARCHAR" />
    <result column="EAWB_SERVICETYPE" property="eawbServicetype" jdbcType="VARCHAR" />
    <result column="MAWB_CODE" property="mawbCode" jdbcType="VARCHAR" />
    <result column="EAWB_LENGTH" property="eawbLength" jdbcType="DECIMAL" />
    <result column="EAWB_WIDTH" property="eawbWidth" jdbcType="DECIMAL" />
    <result column="EAWB_HEIGHT" property="eawbHeight" jdbcType="DECIMAL" />
    <result column="EAWB_SHIPPER_ACCOUNTNAME" property="eawbShipperAccountname" jdbcType="VARCHAR" />
    <result column="EAWB_CONSIGNEE_ACCOUNTNAME" property="eawbConsigneeAccountname" jdbcType="VARCHAR" />
    <result column="EAWB_BT_CODE" property="eawbBtCode" jdbcType="VARCHAR" />
    <result column="EAWB_PICKUP_POSTCODE" property="eawbPickupPostcode" jdbcType="VARCHAR" />
    <result column="EAWB_DELIVER_POSTCODE" property="eawbDeliverPostcode" jdbcType="VARCHAR" />
    <result column="EAWB_TRACKING_NO" property="eawbTrackingNo" jdbcType="VARCHAR" />
    <result column="EAWB_QUANTITY" property="eawbQuantity" jdbcType="DECIMAL" />
    <result column="EAWB_CUSTDECLVAL" property="eawbCustdeclval" jdbcType="DECIMAL" />
    <result column="EAWB_SPECIFICATION" property="eawbSpecification" jdbcType="VARCHAR" />
    <result column="EAWB_HSCODE" property="eawbHscode" jdbcType="VARCHAR" />
    <result column="EAWB_CUSTPRODENNAME" property="eawbCustprodenname" jdbcType="VARCHAR" />
    <result column="CUSTOMER_ORDER_CODE" property="customerOrderCode" jdbcType="VARCHAR" />
    <result column="ORDER_CODE_IN" property="orderCodeIn" jdbcType="VARCHAR" />
    <result column="EAWB_SF_CODE" property="eawbSfCode" jdbcType="VARCHAR" />
    <result column="EAWB_PLATE_CODE" property="eawbPlateCode" jdbcType="VARCHAR" />
    <result column="EAWB_TRANSMODEID_ORIGINAL" property="eawbTransmodeIdOriginal" jdbcType="VARCHAR" />
    <result column="EAWB_SERVICETYPE_ORIGINAL" property="eawbServiceTypeOriginal" jdbcType="VARCHAR" />
    <result column="EAWB_UPDATETIME" property="eawbUpdatetime" jdbcType="TIMESTAMP" />
    <result column="EAWB_IETYPE" property="eawbIetype" jdbcType="VARCHAR"/>
    <result column="WEIGHT_VALUE" property="weightValue" jdbcType="DECIMAL"/>
    <result column="WEIGHT_UNIT" property="weightUnit" jdbcType="VARCHAR"/>
    <result column="ZONE_CODE" property="zoneCode" jdbcType="VARCHAR"/>
    <!-- add-->
    <result column="EAWB_DESTCITY" property="eawbDestcity" jdbcType="VARCHAR" />
    <result column="EAWB_DECLAREVOLUME" property="eawbDeclarevolume" jdbcType="DECIMAL" />
    <result column="EAWB_DECLAREGROSSWEIGHT" property="eawbDeclaregrossweight" jdbcType="DECIMAL" />
    <result column="EAWB_DECLARECHARGEABLE" property="eawbDeclarechargeable" jdbcType="DECIMAL" />
    <result column="EAWB_DESTSTATE" property="eawbDeststate" jdbcType="VARCHAR" />
    <!-- add-->
    <result column="REFUND_STATUS" property="refundStatus" jdbcType="VARCHAR"/>
    <result column="TRANSPORT_TYPE" property="transpostType" jdbcType="VARCHAR"/>
    <result column="EA_FLIGHT_TYPE" property="eaFlightType" jdbcType="VARCHAR"/>
    <result column="EAWB_ECOMMERCE" property="eawbEcommerce" jdbcType="VARCHAR"/>
    <!-- add-->
    <result column="EAWB_DECLAREVALUE" property="eawbDeclarevalue" jdbcType="DECIMAL" />
    <result column="EAWB_COD" property="eawbCod" jdbcType="VARCHAR" />
    <result column="EAWB_CODVALUE" property="eawbCodvalue" jdbcType="DECIMAL" />
    <result column="EAWB_CODCURRENCY" property="eawbCodcurrency" jdbcType="VARCHAR" />
    <!-- add-->
    <result column="EAWB_FIRSTMILE" property="eawbFirstmile" jdbcType="VARCHAR"/>
    <result column="DEST_SAC_ID" property="destSacId" jdbcType="VARCHAR"/>
    <!-- add-->
    <result column="SINOTRANS_ID" property="sinotransId" jdbcType="VARCHAR" />

    <result column="EAWB_DELIVER_ADDRESS" jdbcType="VARCHAR" property="eawbDeliverAddress" />
    <result column="EAWB_DELIVER_CONTACT" jdbcType="VARCHAR" property="eawbDeliverContact" />
    <result column="EAWB_DELIVER_PHONE" jdbcType="VARCHAR" property="eawbDeliverPhone" />
    <result column="EAWB_DELIVER_MOBILE" jdbcType="VARCHAR" property="eawbDeliverMobile" />
    <result column="EAWB_DELIVER_EMAIL" jdbcType="VARCHAR" property="eawbDeliverEmail" />
    <result column="EAWB_SERVICEREQUIREMENT" jdbcType="VARCHAR" property="eawbServicerequirement" />
  </resultMap>
  <sql id="Base_Column_List" >
    EAWB_SYSCODE, EAWB_PRINTCODE, EST_CODE, EAWB_HANDLETIME, EAWB_DEPARTURE, EAWB_DESTINATION,
    EAWB_PIECES, EAWB_VOLUME, EAWB_GROSSWEIGHT, EAWB_CHARGEABLEWEIGHT, CT_CODE,  (select s.sac_id from settlementobject s
       where s.so_Code= eawb_so_code and s.sac_id not in('SNR')) as SAC_ID,
    EAWB_SO_CODE, EAWB_REFERENCE1, EAWB_REFERENCE2, EAWB_STATUS, EAWB_OUTBOUND_SAC_ID,
    EAWB_KEYENTRYTIME, EAWB_DEPARTCOUNTRY, EAWB_DESTCOUNTRY, EAWB_CUSTPRODNAME, EAWB_TRANSMODEID,
    EAWB_SERVICETYPE, MAWB_CODE, EAWB_LENGTH, EAWB_WIDTH, EAWB_HEIGHT, EAWB_SHIPPER_ACCOUNTNAME,
    EAWB_CONSIGNEE_ACCOUNTNAME, EAWB_BT_CODE, EAWB_PICKUP_POSTCODE, EAWB_DELIVER_POSTCODE,
    EAWB_TRACKING_NO, EAWB_QUANTITY, EAWB_CUSTDECLVAL, EAWB_SPECIFICATION, EAWB_HSCODE,
    EAWB_CUSTPRODENNAME, CUSTOMER_ORDER_CODE, ORDER_CODE_IN, EAWB_SF_CODE, EAWB_PLATE_CODE,
    EAWB_TRANSMODEID_ORIGINAL, EAWB_SERVICETYPE_ORIGINAL,EAWB_UPDATETIME,EAWB_IETYPE,SINOTRANS_ID,
    EAWB_DELIVER_ADDRESS, EAWB_DELIVER_CONTACT,EAWB_DELIVER_PHONE,
    EAWB_DELIVER_MOBILE, EAWB_DELIVER_EMAIL, EAWB_SERVICEREQUIREMENT

  </sql>
<!--  ,SINOTRANS_ID-->
  <!-- 序列 -->
  <sql id='TABLE_SEQUENCE'>SEQ_EXPRESSAIRWAYBILL.NEXTVAL</sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select
    <include refid="Base_Column_List" />
    from EXPRESSAIRWAYBILL
    where EAWB_SYSCODE = #{eawbSyscode,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from EXPRESSAIRWAYBILL
    where EAWB_SYSCODE = #{eawbSyscode,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.ExpressAirWayBill" >
    <selectKey keyProperty="eawbSyscode" resultType="int" order="BEFORE">
      select <include refid="TABLE_SEQUENCE" /> from dual
    </selectKey>
    insert into EXPRESSAIRWAYBILL (EAWB_SYSCODE, EAWB_PRINTCODE, EST_CODE,
    EAWB_HANDLETIME, EAWB_DEPARTURE, EAWB_DESTINATION,
    EAWB_PIECES, EAWB_VOLUME, EAWB_GROSSWEIGHT,
    EAWB_CHARGEABLEWEIGHT, CT_CODE, SAC_ID,
    EAWB_SO_CODE, EAWB_REFERENCE1, EAWB_REFERENCE2,
    EAWB_STATUS, EAWB_OUTBOUND_SAC_ID, EAWB_KEYENTRYTIME,
    EAWB_DEPARTCOUNTRY, EAWB_DESTCOUNTRY, EAWB_CUSTPRODNAME,
    EAWB_TRANSMODEID, EAWB_SERVICETYPE, MAWB_CODE,
    EAWB_LENGTH, EAWB_WIDTH, EAWB_HEIGHT,
    EAWB_SHIPPER_ACCOUNTNAME, EAWB_CONSIGNEE_ACCOUNTNAME,
    EAWB_BT_CODE, EAWB_PICKUP_POSTCODE, EAWB_DELIVER_POSTCODE,
    EAWB_TRACKING_NO, EAWB_QUANTITY, EAWB_CUSTDECLVAL,
    EAWB_SPECIFICATION, EAWB_HSCODE, EAWB_CUSTPRODENNAME,
    CUSTOMER_ORDER_CODE, ORDER_CODE_IN, EAWB_SF_CODE,
    EAWB_PLATE_CODE,EAWB_TRANSMODEID_ORIGINAL, EAWB_SERVICETYPE_ORIGINAL,EAWB_IETYPE, EAWB_DELIVER_ADDRESS, EAWB_DELIVER_CONTACT,
    EAWB_DELIVER_PHONE, EAWB_DELIVER_MOBILE, EAWB_DELIVER_EMAIL,
    EAWB_SERVICEREQUIREMENT)
    values (#{eawbSyscode,jdbcType=DECIMAL}, #{eawbPrintcode,jdbcType=VARCHAR}, #{estCode,jdbcType=VARCHAR},
    #{eawbHandletime,jdbcType=TIMESTAMP}, #{eawbDeparture,jdbcType=VARCHAR}, #{eawbDestination,jdbcType=VARCHAR},
    #{eawbPieces,jdbcType=DECIMAL}, #{eawbVolume,jdbcType=DECIMAL}, #{eawbGrossweight,jdbcType=DECIMAL},
    #{eawbChargeableweight,jdbcType=DECIMAL}, #{ctCode,jdbcType=VARCHAR}, #{sacId,jdbcType=VARCHAR},
    #{eawbSoCode,jdbcType=VARCHAR}, #{eawbReference1,jdbcType=VARCHAR}, #{eawbReference2,jdbcType=VARCHAR},
    #{eawbStatus,jdbcType=VARCHAR}, #{eawbOutboundSacId,jdbcType=VARCHAR}, #{eawbKeyentrytime,jdbcType=TIMESTAMP},
    #{eawbDepartcountry,jdbcType=VARCHAR}, #{eawbDestcountry,jdbcType=VARCHAR}, #{eawbCustprodname,jdbcType=VARCHAR},
    #{eawbTransmodeid,jdbcType=VARCHAR}, #{eawbServicetype,jdbcType=VARCHAR}, #{mawbCode,jdbcType=VARCHAR},
    #{eawbLength,jdbcType=DECIMAL}, #{eawbWidth,jdbcType=DECIMAL}, #{eawbHeight,jdbcType=DECIMAL},
    #{eawbShipperAccountname,jdbcType=VARCHAR}, #{eawbConsigneeAccountname,jdbcType=VARCHAR},
    #{eawbBtCode,jdbcType=VARCHAR}, #{eawbPickupPostcode,jdbcType=VARCHAR}, #{eawbDeliverPostcode,jdbcType=VARCHAR},
    #{eawbTrackingNo,jdbcType=VARCHAR}, #{eawbQuantity,jdbcType=DECIMAL}, #{eawbCustdeclval,jdbcType=DECIMAL},
    #{eawbSpecification,jdbcType=VARCHAR}, #{eawbHscode,jdbcType=VARCHAR}, #{eawbCustprodenname,jdbcType=VARCHAR},
    #{customerOrderCode,jdbcType=VARCHAR}, #{orderCodeIn,jdbcType=VARCHAR}, #{eawbSfCode,jdbcType=VARCHAR},
    #{eawbPlateCode,jdbcType=VARCHAR},
    #{eawbTransmodeIdOriginal,jdbcType=VARCHAR},#{eawbServiceTypeOriginal,jdbcType=VARCHAR},
    #{eawbIetype,jdbcType=VARCHAR}, #{eawbDeliverAddress,jdbcType=VARCHAR}, #{eawbDeliverContact,jdbcType=VARCHAR},
    #{eawbDeliverPhone,jdbcType=VARCHAR}, #{eawbDeliverMobile,jdbcType=VARCHAR}, #{eawbDeliverEmail,jdbcType=VARCHAR},
    #{eawbServicerequirement,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.ExpressAirWayBill" >
    <selectKey keyProperty="eawbSyscode" resultType="int" order="BEFORE">
      select <include refid="TABLE_SEQUENCE" /> from dual
    </selectKey>
    insert into EXPRESSAIRWAYBILL
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="eawbSyscode != null" >
        EAWB_SYSCODE,
      </if>
      <if test="eawbPrintcode != null" >
        EAWB_PRINTCODE,
      </if>
      <if test="estCode != null" >
        EST_CODE,
      </if>
      <if test="eawbHandletime != null" >
        EAWB_HANDLETIME,
      </if>
      <if test="eawbDeparture != null" >
        EAWB_DEPARTURE,
      </if>
      <if test="eawbDestination != null" >
        EAWB_DESTINATION,
      </if>
      <if test="eawbPieces != null" >
        EAWB_PIECES,
      </if>
      <if test="eawbVolume != null" >
        EAWB_VOLUME,
      </if>
      <if test="eawbGrossweight != null" >
        EAWB_GROSSWEIGHT,
      </if>
      <if test="eawbChargeableweight != null" >
        EAWB_CHARGEABLEWEIGHT,
      </if>
      <if test="ctCode != null" >
        CT_CODE,
      </if>
      <if test="sacId != null" >
        SAC_ID,
      </if>
      <if test="eawbSoCode != null" >
        EAWB_SO_CODE,
      </if>
      <if test="eawbReference1 != null" >
        EAWB_REFERENCE1,
      </if>
      <if test="eawbReference2 != null" >
        EAWB_REFERENCE2,
      </if>
      <if test="eawbStatus != null" >
        EAWB_STATUS,
      </if>
      <if test="eawbOutboundSacId != null" >
        EAWB_OUTBOUND_SAC_ID,
      </if>
      <if test="eawbKeyentrytime != null" >
        EAWB_KEYENTRYTIME,
      </if>
      <if test="eawbDepartcountry != null" >
        EAWB_DEPARTCOUNTRY,
      </if>
      <if test="eawbDestcountry != null" >
        EAWB_DESTCOUNTRY,
      </if>
      <if test="eawbCustprodname != null" >
        EAWB_CUSTPRODNAME,
      </if>
      <if test="eawbTransmodeid != null" >
        EAWB_TRANSMODEID,
      </if>
      <if test="eawbServicetype != null" >
        EAWB_SERVICETYPE,
      </if>
      <if test="mawbCode != null" >
        MAWB_CODE,
      </if>
      <if test="eawbLength != null" >
        EAWB_LENGTH,
      </if>
      <if test="eawbWidth != null" >
        EAWB_WIDTH,
      </if>
      <if test="eawbHeight != null" >
        EAWB_HEIGHT,
      </if>
      <if test="eawbShipperAccountname != null" >
        EAWB_SHIPPER_ACCOUNTNAME,
      </if>
      <if test="eawbConsigneeAccountname != null" >
        EAWB_CONSIGNEE_ACCOUNTNAME,
      </if>
      <if test="eawbBtCode != null" >
        EAWB_BT_CODE,
      </if>
      <if test="eawbPickupPostcode != null" >
        EAWB_PICKUP_POSTCODE,
      </if>
      <if test="eawbDeliverPostcode != null" >
        EAWB_DELIVER_POSTCODE,
      </if>
      <if test="eawbTrackingNo != null" >
        EAWB_TRACKING_NO,
      </if>
      <if test="eawbQuantity != null" >
        EAWB_QUANTITY,
      </if>
      <if test="eawbCustdeclval != null" >
        EAWB_CUSTDECLVAL,
      </if>
      <if test="eawbSpecification != null" >
        EAWB_SPECIFICATION,
      </if>
      <if test="eawbHscode != null" >
        EAWB_HSCODE,
      </if>
      <if test="eawbCustprodenname != null" >
        EAWB_CUSTPRODENNAME,
      </if>
      <if test="customerOrderCode != null" >
        CUSTOMER_ORDER_CODE,
      </if>
      <if test="orderCodeIn != null" >
        ORDER_CODE_IN,
      </if>
      <if test="eawbSfCode != null" >
        EAWB_SF_CODE,
      </if>
      <if test="eawbPlateCode != null" >
        EAWB_PLATE_CODE,
      </if>
      <if test="eawbTransmodeIdOriginal != null" >
        EAWB_TRANSMODEID_ORIGINAL,
      </if>
      <if test="eawbServiceTypeOriginal != null" >
        EAWB_SERVICETYPE_ORIGINAL,
      </if>
      <if test="eawbIetype != null">
        EAWB_IETYPE,
      </if>

      <if test="eawbDestcity != null">
        EAWB_DESTCITY,
      </if>
      <if test="eawbDeclarevolume != null">
        EAWB_DECLAREVOLUME,
      </if>
      <if test="eawbDeclaregrossweight != null">
        EAWB_DECLAREGROSSWEIGHT,
      </if>
      <if test="eawbDeclarechargeable != null">
        EAWB_DECLARECHARGEABLE,
      </if>
      <if test="eawbDeststate != null">
        EAWB_DESTSTATE,
      </if>

      <if test="refundStatus != null">
        REFUND_STATUS,
      </if>
      <if test="transpostType != null">
        TRANSPORT_TYPE,
      </if>
      <if test="eaFlightType != null">
        EA_FLIGHT_TYPE,
      </if>
      <if test="eawbEcommerce != null">
        EAWB_ECOMMERCE,
      </if>
      <if test="eawbDeclarevalue != null">
        EAWB_DECLAREVALUE,
      </if>

      <if test="eawbCod != null">
        EAWB_COD,
      </if>
      <if test="eawbCodvalue != null">
        EAWB_CODVALUE,
      </if>
      <if test="eawbCodcurrency != null">
        EAWB_CODCURRENCY,
      </if>
      <if test="eawbFirstmile != null">
        EAWB_FIRSTMILE,
      </if>
      <if test="destSacId != null">
        DEST_SAC_ID,
      </if>
      <if test="eawbUpdatetime != null">
        EAWB_UPDATETIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="eawbSyscode != null" >
        #{eawbSyscode,jdbcType=DECIMAL},
      </if>
      <if test="eawbPrintcode != null" >
        #{eawbPrintcode,jdbcType=VARCHAR},
      </if>
      <if test="estCode != null" >
        #{estCode,jdbcType=VARCHAR},
      </if>
      <if test="eawbHandletime != null" >
        #{eawbHandletime,jdbcType=TIMESTAMP},
      </if>
      <if test="eawbDeparture != null" >
        #{eawbDeparture,jdbcType=VARCHAR},
      </if>
      <if test="eawbDestination != null" >
        #{eawbDestination,jdbcType=VARCHAR},
      </if>
      <if test="eawbPieces != null" >
        #{eawbPieces,jdbcType=DECIMAL},
      </if>
      <if test="eawbVolume != null" >
        #{eawbVolume,jdbcType=DECIMAL},
      </if>
      <if test="eawbGrossweight != null" >
        #{eawbGrossweight,jdbcType=DECIMAL},
      </if>
      <if test="eawbChargeableweight != null" >
        #{eawbChargeableweight,jdbcType=DECIMAL},
      </if>
      <if test="ctCode != null" >
        #{ctCode,jdbcType=VARCHAR},
      </if>
      <if test="sacId != null" >
        #{sacId,jdbcType=VARCHAR},
      </if>
      <if test="eawbSoCode != null" >
        #{eawbSoCode,jdbcType=VARCHAR},
      </if>
      <if test="eawbReference1 != null" >
        #{eawbReference1,jdbcType=VARCHAR},
      </if>
      <if test="eawbReference2 != null" >
        #{eawbReference2,jdbcType=VARCHAR},
      </if>
      <if test="eawbStatus != null" >
        #{eawbStatus,jdbcType=VARCHAR},
      </if>
      <if test="eawbOutboundSacId != null" >
        #{eawbOutboundSacId,jdbcType=VARCHAR},
      </if>
      <if test="eawbKeyentrytime != null" >
        #{eawbKeyentrytime,jdbcType=TIMESTAMP},
      </if>
      <if test="eawbDepartcountry != null" >
        #{eawbDepartcountry,jdbcType=VARCHAR},
      </if>
      <if test="eawbDestcountry != null" >
        #{eawbDestcountry,jdbcType=VARCHAR},
      </if>
      <if test="eawbCustprodname != null" >
        #{eawbCustprodname,jdbcType=VARCHAR},
      </if>
      <if test="eawbTransmodeid != null" >
        #{eawbTransmodeid,jdbcType=VARCHAR},
      </if>
      <if test="eawbServicetype != null" >
        #{eawbServicetype,jdbcType=VARCHAR},
      </if>
      <if test="mawbCode != null" >
        #{mawbCode,jdbcType=VARCHAR},
      </if>
      <if test="eawbLength != null" >
        #{eawbLength,jdbcType=DECIMAL},
      </if>
      <if test="eawbWidth != null" >
        #{eawbWidth,jdbcType=DECIMAL},
      </if>
      <if test="eawbHeight != null" >
        #{eawbHeight,jdbcType=DECIMAL},
      </if>
      <if test="eawbShipperAccountname != null" >
        #{eawbShipperAccountname,jdbcType=VARCHAR},
      </if>
      <if test="eawbConsigneeAccountname != null" >
        #{eawbConsigneeAccountname,jdbcType=VARCHAR},
      </if>
      <if test="eawbBtCode != null" >
        #{eawbBtCode,jdbcType=VARCHAR},
      </if>
      <if test="eawbPickupPostcode != null" >
        #{eawbPickupPostcode,jdbcType=VARCHAR},
      </if>
      <if test="eawbDeliverPostcode != null" >
        #{eawbDeliverPostcode,jdbcType=VARCHAR},
      </if>
      <if test="eawbTrackingNo != null" >
        #{eawbTrackingNo,jdbcType=VARCHAR},
      </if>
      <if test="eawbQuantity != null" >
        #{eawbQuantity,jdbcType=DECIMAL},
      </if>
      <if test="eawbCustdeclval != null" >
        #{eawbCustdeclval,jdbcType=DECIMAL},
      </if>
      <if test="eawbSpecification != null" >
        #{eawbSpecification,jdbcType=VARCHAR},
      </if>
      <if test="eawbHscode != null" >
        #{eawbHscode,jdbcType=VARCHAR},
      </if>
      <if test="eawbCustprodenname != null" >
        #{eawbCustprodenname,jdbcType=VARCHAR},
      </if>
      <if test="customerOrderCode != null" >
        #{customerOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="orderCodeIn != null" >
        #{orderCodeIn,jdbcType=VARCHAR},
      </if>
      <if test="eawbSfCode != null" >
        #{eawbSfCode,jdbcType=VARCHAR},
      </if>
      <if test="eawbPlateCode != null" >
        #{eawbPlateCode,jdbcType=VARCHAR},
      </if>
      <if test="eawbTransmodeIdOriginal != null" >
        #{eawbTransmodeIdOriginal,jdbcType=VARCHAR},
      </if>
      <if test="eawbServiceTypeOriginal != null" >
        #{eawbServiceTypeOriginal,jdbcType=VARCHAR},
      </if>
      <if test="eawbIetype != null">
        #{eawbIetype,jdbcType=VARCHAR},
      </if>

      <if test="eawbDestcity != null">
        #{eawbDestcity,jdbcType=VARCHAR},
      </if>
      <if test="eawbDeclarevolume != null">
        #{eawbDeclarevolume,jdbcType=DECIMAL},
      </if>
      <if test="eawbDeclaregrossweight != null">
        #{eawbDeclaregrossweight,jdbcType=DECIMAL},
      </if>
      <if test="eawbDeclarechargeable != null">
        #{eawbDeclarechargeable,jdbcType=DECIMAL},
      </if>
      <if test="eawbDeststate != null">
        #{eawbDeststate,jdbcType=VARCHAR},
      </if>

      <if test="refundStatus != null">
        #{refundStatus,jdbcType=VARCHAR},
      </if>
      <if test="transpostType != null">
        #{transpostType,jdbcType=VARCHAR},
      </if>
      <if test="eaFlightType != null">
        #{eaFlightType,jdbcType=VARCHAR},
      </if>
      <if test="eawbEcommerce != null">
        #{eawbEcommerce,jdbcType=VARCHAR},
      </if>
      <if test="eawbDeclarevalue != null">
        #{eawbDeclarevalue,jdbcType=DECIMAL},
      </if>

      <if test="eawbCod != null">
        #{eawbCod,jdbcType=VARCHAR},
      </if>
      <if test="eawbCodvalue != null">
        #{eawbCodvalue,jdbcType=DECIMAL},
      </if>
      <if test="eawbCodcurrency != null">
        #{eawbCodcurrency,jdbcType=VARCHAR},
      </if>
      <if test="eawbFirstmile != null">
        #{eawbFirstmile,jdbcType=VARCHAR},
      </if>
      <if test="destSacId != null">
        #{destSacId,jdbcType=VARCHAR},
      </if>
      <if test="eawbUpdatetime != null">
        #{eawbUpdatetime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.ExpressAirWayBill" >
    update EXPRESSAIRWAYBILL
    <set >
      <if test="eawbPrintcode != null" >
        EAWB_PRINTCODE = #{eawbPrintcode,jdbcType=VARCHAR},
      </if>
      <if test="estCode != null" >
        EST_CODE = #{estCode,jdbcType=VARCHAR},
      </if>
      <if test="eawbHandletime != null" >
        EAWB_HANDLETIME = #{eawbHandletime,jdbcType=TIMESTAMP},
      </if>
      <if test="eawbDeparture != null" >
        EAWB_DEPARTURE = #{eawbDeparture,jdbcType=VARCHAR},
      </if>
      <if test="eawbDestination != null" >
        EAWB_DESTINATION = #{eawbDestination,jdbcType=VARCHAR},
      </if>
      <if test="eawbPieces != null" >
        EAWB_PIECES = #{eawbPieces,jdbcType=DECIMAL},
      </if>
      <if test="eawbVolume != null" >
        EAWB_VOLUME = #{eawbVolume,jdbcType=DECIMAL},
      </if>
      <if test="eawbGrossweight != null" >
        EAWB_GROSSWEIGHT = #{eawbGrossweight,jdbcType=DECIMAL},
      </if>
      <if test="eawbChargeableweight != null" >
        EAWB_CHARGEABLEWEIGHT = #{eawbChargeableweight,jdbcType=DECIMAL},
      </if>
      <if test="ctCode != null" >
        CT_CODE = #{ctCode,jdbcType=VARCHAR},
      </if>
      <if test="sacId != null" >
        SAC_ID = #{sacId,jdbcType=VARCHAR},
      </if>
      <if test="eawbSoCode != null" >
        EAWB_SO_CODE = #{eawbSoCode,jdbcType=VARCHAR},
      </if>
      <if test="eawbReference1 != null" >
        EAWB_REFERENCE1 = #{eawbReference1,jdbcType=VARCHAR},
      </if>
      <if test="eawbReference2 != null" >
        EAWB_REFERENCE2 = #{eawbReference2,jdbcType=VARCHAR},
      </if>
      <if test="eawbStatus != null" >
        EAWB_STATUS = #{eawbStatus,jdbcType=VARCHAR},
      </if>
      <if test="eawbOutboundSacId != null" >
        EAWB_OUTBOUND_SAC_ID = #{eawbOutboundSacId,jdbcType=VARCHAR},
      </if>
      <if test="eawbKeyentrytime != null" >
        EAWB_KEYENTRYTIME = #{eawbKeyentrytime,jdbcType=TIMESTAMP},
      </if>
      <if test="eawbDepartcountry != null" >
        EAWB_DEPARTCOUNTRY = #{eawbDepartcountry,jdbcType=VARCHAR},
      </if>
      <if test="eawbDestcountry != null" >
        EAWB_DESTCOUNTRY = #{eawbDestcountry,jdbcType=VARCHAR},
      </if>
      <if test="eawbCustprodname != null" >
        EAWB_CUSTPRODNAME = #{eawbCustprodname,jdbcType=VARCHAR},
      </if>
      <if test="eawbTransmodeid != null" >
        EAWB_TRANSMODEID = #{eawbTransmodeid,jdbcType=VARCHAR},
      </if>
      <if test="eawbServicetype != null" >
        EAWB_SERVICETYPE = #{eawbServicetype,jdbcType=VARCHAR},
      </if>
      <if test="mawbCode != null" >
        MAWB_CODE = #{mawbCode,jdbcType=VARCHAR},
      </if>
      <if test="eawbLength != null" >
        EAWB_LENGTH = #{eawbLength,jdbcType=DECIMAL},
      </if>
      <if test="eawbWidth != null" >
        EAWB_WIDTH = #{eawbWidth,jdbcType=DECIMAL},
      </if>
      <if test="eawbHeight != null" >
        EAWB_HEIGHT = #{eawbHeight,jdbcType=DECIMAL},
      </if>
      <if test="eawbShipperAccountname != null" >
        EAWB_SHIPPER_ACCOUNTNAME = #{eawbShipperAccountname,jdbcType=VARCHAR},
      </if>
      <if test="eawbConsigneeAccountname != null" >
        EAWB_CONSIGNEE_ACCOUNTNAME = #{eawbConsigneeAccountname,jdbcType=VARCHAR},
      </if>
      <if test="eawbBtCode != null" >
        EAWB_BT_CODE = #{eawbBtCode,jdbcType=VARCHAR},
      </if>
      <if test="eawbPickupPostcode != null" >
        EAWB_PICKUP_POSTCODE = #{eawbPickupPostcode,jdbcType=VARCHAR},
      </if>
      <if test="eawbDeliverPostcode != null" >
        EAWB_DELIVER_POSTCODE = #{eawbDeliverPostcode,jdbcType=VARCHAR},
      </if>
      <if test="eawbTrackingNo != null" >
        EAWB_TRACKING_NO = #{eawbTrackingNo,jdbcType=VARCHAR},
      </if>
      <if test="eawbQuantity != null" >
        EAWB_QUANTITY = #{eawbQuantity,jdbcType=DECIMAL},
      </if>
      <if test="eawbCustdeclval != null" >
        EAWB_CUSTDECLVAL = #{eawbCustdeclval,jdbcType=DECIMAL},
      </if>
      <if test="eawbSpecification != null" >
        EAWB_SPECIFICATION = #{eawbSpecification,jdbcType=VARCHAR},
      </if>
      <if test="eawbHscode != null" >
        EAWB_HSCODE = #{eawbHscode,jdbcType=VARCHAR},
      </if>
      <if test="eawbCustprodenname != null" >
        EAWB_CUSTPRODENNAME = #{eawbCustprodenname,jdbcType=VARCHAR},
      </if>
      <if test="customerOrderCode != null" >
        CUSTOMER_ORDER_CODE = #{customerOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="orderCodeIn != null" >
        ORDER_CODE_IN = #{orderCodeIn,jdbcType=VARCHAR},
      </if>
      <if test="eawbSfCode != null" >
        EAWB_SF_CODE = #{eawbSfCode,jdbcType=VARCHAR},
      </if>
      <if test="eawbPlateCode != null" >
        EAWB_PLATE_CODE = #{eawbPlateCode,jdbcType=VARCHAR},
      </if>
      <if test="eawbTransmodeIdOriginal != null" >
        EAWB_TRANSMODEID_ORIGINAL = #{eawbTransmodeIdOriginal,jdbcType=VARCHAR},
      </if>
      <if test="eawbServiceTypeOriginal != null" >
        EAWB_SERVICETYPE_ORIGINAL = #{eawbServiceTypeOriginal,jdbcType=VARCHAR},
      </if>
      <if test="eawbIetype != null">
        EAWB_IETYPE=#{eawbIetype,jdbcType=VARCHAR},
      </if>
    </set>
    where EAWB_SYSCODE = #{eawbSyscode,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.ExpressAirWayBill" >
    update EXPRESSAIRWAYBILL
    set EAWB_PRINTCODE = #{eawbPrintcode,jdbcType=VARCHAR},
    EST_CODE = #{estCode,jdbcType=VARCHAR},
    EAWB_HANDLETIME = #{eawbHandletime,jdbcType=TIMESTAMP},
    EAWB_DEPARTURE = #{eawbDeparture,jdbcType=VARCHAR},
    EAWB_DESTINATION = #{eawbDestination,jdbcType=VARCHAR},
    EAWB_PIECES = #{eawbPieces,jdbcType=DECIMAL},
    EAWB_VOLUME = #{eawbVolume,jdbcType=DECIMAL},
    EAWB_GROSSWEIGHT = #{eawbGrossweight,jdbcType=DECIMAL},
    EAWB_CHARGEABLEWEIGHT = #{eawbChargeableweight,jdbcType=DECIMAL},
    CT_CODE = #{ctCode,jdbcType=VARCHAR},
    SAC_ID = #{sacId,jdbcType=VARCHAR},
    EAWB_SO_CODE = #{eawbSoCode,jdbcType=VARCHAR},
    EAWB_REFERENCE1 = #{eawbReference1,jdbcType=VARCHAR},
    EAWB_REFERENCE2 = #{eawbReference2,jdbcType=VARCHAR},
    EAWB_STATUS = #{eawbStatus,jdbcType=VARCHAR},
    EAWB_OUTBOUND_SAC_ID = #{eawbOutboundSacId,jdbcType=VARCHAR},
    EAWB_KEYENTRYTIME = #{eawbKeyentrytime,jdbcType=TIMESTAMP},
    EAWB_DEPARTCOUNTRY = #{eawbDepartcountry,jdbcType=VARCHAR},
    EAWB_DESTCOUNTRY = #{eawbDestcountry,jdbcType=VARCHAR},
    EAWB_CUSTPRODNAME = #{eawbCustprodname,jdbcType=VARCHAR},
    EAWB_TRANSMODEID = #{eawbTransmodeid,jdbcType=VARCHAR},
    EAWB_SERVICETYPE = #{eawbServicetype,jdbcType=VARCHAR},
    MAWB_CODE = #{mawbCode,jdbcType=VARCHAR},
    EAWB_LENGTH = #{eawbLength,jdbcType=DECIMAL},
    EAWB_WIDTH = #{eawbWidth,jdbcType=DECIMAL},
    EAWB_HEIGHT = #{eawbHeight,jdbcType=DECIMAL},
    EAWB_SHIPPER_ACCOUNTNAME = #{eawbShipperAccountname,jdbcType=VARCHAR},
    EAWB_CONSIGNEE_ACCOUNTNAME = #{eawbConsigneeAccountname,jdbcType=VARCHAR},
    EAWB_BT_CODE = #{eawbBtCode,jdbcType=VARCHAR},
    EAWB_PICKUP_POSTCODE = #{eawbPickupPostcode,jdbcType=VARCHAR},
    EAWB_DELIVER_POSTCODE = #{eawbDeliverPostcode,jdbcType=VARCHAR},
    EAWB_TRACKING_NO = #{eawbTrackingNo,jdbcType=VARCHAR},
    EAWB_QUANTITY = #{eawbQuantity,jdbcType=DECIMAL},
    EAWB_CUSTDECLVAL = #{eawbCustdeclval,jdbcType=DECIMAL},
    EAWB_SPECIFICATION = #{eawbSpecification,jdbcType=VARCHAR},
    EAWB_HSCODE = #{eawbHscode,jdbcType=VARCHAR},
    EAWB_CUSTPRODENNAME = #{eawbCustprodenname,jdbcType=VARCHAR},
    CUSTOMER_ORDER_CODE = #{customerOrderCode,jdbcType=VARCHAR},
    ORDER_CODE_IN = #{orderCodeIn,jdbcType=VARCHAR},
    EAWB_SF_CODE = #{eawbSfCode,jdbcType=VARCHAR},
    EAWB_PLATE_CODE = #{eawbPlateCode,jdbcType=VARCHAR},
    EAWB_TRANSMODEID_ORIGINAL = #{eawbTransmodeIdOriginal,jdbcType=VARCHAR},
    EAWB_SERVICETYPE_ORIGINAL = #{eawbServiceTypeOriginal,jdbcType=VARCHAR},
    EAWB_IETYPE=#{eawbIetype,jdbcType=VARCHAR}
    where EAWB_SYSCODE = #{eawbSyscode,jdbcType=DECIMAL}
  </update>

  <!-- 根据sacid 和 mawbCode 查询 -->
  <select id="selectBySacIDAndMawbCodeAndRef2" resultType="com.sinoair.billing.domain.model.billing.ExpressAirWayBill" parameterType="com.sinoair.billing.domain.model.billing.ExpressAirWayBill">
      SELECT * FROM EXPRESSAIRWAYBILL EAWB WHERE EAWB.SAC_ID = #{sacId,jdbcType=VARCHAR} AND MAWB_CODE = #{mawbCode,jdbcType=VARCHAR} and EAWB_REFERENCE2 = #{eawbReference2,jdbcType=VARCHAR}
  </select>
  <select id="selectByPrintCodeAndRef2" resultType="com.sinoair.billing.domain.model.billing.ExpressAirWayBill" parameterType="com.sinoair.billing.domain.model.billing.ExpressAirWayBill">
    SELECT * FROM EXPRESSAIRWAYBILL EAWB WHERE EAWB_PRINTCODE = #{eawbPrintcode,jdbcType=VARCHAR} and EAWB_REFERENCE2 = #{eawbReference2,jdbcType=VARCHAR}
  </select>
  <select id="selectCountBySacIDandMawbCode" resultType="java.lang.Integer" parameterType="com.sinoair.billing.domain.model.billing.ExpressAirWayBill">
     SELECT count(0) FROM EXPRESSAIRWAYBILL EAWB WHERE EAWB.SAC_ID = #{sacId,jdbcType=VARCHAR} AND MAWB_CODE = #{mawbCode,jdbcType=VARCHAR}
  </select>

  <select id="selectCountGWeight" resultType="java.math.BigDecimal" parameterType="com.sinoair.billing.domain.model.billing.ExpressAirWayBill">
    select sum(EAWB_GROSSWEIGHT)  FROM EXPRESSAIRWAYBILL EAWB WHERE EAWB.SAC_ID = #{sacId,jdbcType=VARCHAR} AND MAWB_CODE = #{mawbCode,jdbcType=VARCHAR}
  </select>

  <select id="selectCountCWeight" resultType="java.math.BigDecimal" parameterType="com.sinoair.billing.domain.model.billing.ExpressAirWayBill">
     select sum(EAWB_CHARGEABLEWEIGHT)  FROM EXPRESSAIRWAYBILL EAWB WHERE EAWB.SAC_ID = #{sacId,jdbcType=VARCHAR} AND MAWB_CODE = #{mawbCode,jdbcType=VARCHAR}
  </select>

  <select id="selectCountByPrintCode" resultType="java.lang.Integer" parameterType="java.lang.String">
    select count(0) from EXPRESSAIRWAYBILL where EAWB_PRINTCODE = #{eawbPrintcode,jdbcType=VARCHAR}
  </select>

  <select id="selectEawbList" resultType="java.util.HashMap" parameterType="com.sinoair.billing.domain.vo.FilterParam">
  select DISTINCT eawb.EAWB_SYSCODE as SYSCODE,eawb.EAWB_PRINTCODE as PRINTCODE,
  eawb.EAWB_DEPARTURE as DEPARTURE,eawb.EAWB_DESTINATION as DESTINATION,
  eawb.EAWB_PIECES as PIECES,eawb.EAWB_GROSSWEIGHT as GROSSWEIGHT,
  eawb.EAWB_SO_CODE as SOCODE,so.so_name as SONAME,eawb.EAWB_TRANSMODEID as TRANSMODEID,
  eawb.EAWB_SERVICETYPE as SERVICETYPE,eawb.EAWB_REFERENCE1 as REFERENCE1,
    eawb.EAWB_REFERENCE2 as REFERENCE2,eawb.EAWB_KEYENTRYTIME as KEYENTRYTIME,
    eawb.EAWB_SERVICETYPE_ORIGINAL as SERVICETYPEORIGINAL,
    eawb.EAWB_IETYPE as IETYPE
    from expressairwaybill eawb,SETTLEMENTOBJECT so
    where so.so_code=eawb.EAWB_SO_CODE
  <if test="code!=null and code!=''">
  and eawb.EAWB_PRINTCODE=#{code}
  </if>
  <if test="serviceType!=null and serviceType!=''">
  and eawb.EAWB_SERVICETYPE=#{serviceType}
  </if>
  <if test="soCode!=null and soCode!=''">
  and eawb.EAWB_SO_CODE=#{soCode}
  </if>

  <if test="(soCode==null or soCode=='')and(serviceType==null or serviceType=='')and(transmodeId==null or transmodeId=='')and(eastCode==null or eastCode=='')and(code==null or code=='')and(starttime==null or starttime=='') and (endtime==null or endtime=='')">
    and 1=2
  </if>
  </select>
  <select id="selectPriceReceipt" resultType="java.util.HashMap">
    select DISTINCT ct.ct_name as CTNAME,pr.ct_code as CTCODE,pr.pr_name as PRNAME from
   product p,price_receipt pr,product_customer pc,CURRENCYTYPE ct
    where p.p_servicetype=#{serviceType}
    and p.occ_company_id=#{companyId}
   and p.p_id=pc.p_id
   and ct.ct_code=pr.ct_code
   and pc.so_code=#{soCode}
   and pc.pc_id=pr.pc_id
   and to_date(#{entryTime},'yyyy-mm-dd hh24:mi:ss') &gt;=  pr.PR_EFFECTIVEDATE
   and to_date(#{entryTime},'yyyy-mm-dd hh24:mi:ss') &lt;=  pr.PR_EXPIREDDATE
  </select>
  <select id="selectPriceReceiptPlan" resultType="java.util.HashMap">
 select pr.pr_id as PRID,ct.ct_name as CTNAME,pr.pr_name as PRNAME,rr.rr_plan_amount as PLANAMOUNT from
 product p,price_receipt pr,product_customer pc,receipt_record rr,CURRENCYTYPE ct
    where p.p_servicetype=#{serviceType}
    and p.occ_company_id=#{companyId}
   and p.p_id=pc.p_id
   and ct.ct_code=pr.ct_code
   and pc.so_code=#{soCode}
   and pc.pc_id=pr.pc_id
   and rr.pr_id=pr.pr_id
   and rr.eawb_printcode=#{printCode}
  </select>


  <select id="selectServerPay" resultType="java.util.HashMap">
    select s.s_id as SID, s.s_name as SNAME
    from product p,service s,product_service ps
    where
    p.p_servicetype=#{serviceType}
    and p.company_id=#{companyId}
    and p.p_id=ps.p_id
    and s.s_id=ps.s_id
  </select>
  <select id="selectPricePay" resultType="java.util.HashMap">
    select DISTINCT pp.pp_id as PPID,ct.ct_name as CTNAME,su.sp_name as SPNAME,su.sp_code as SPCODE,pp.ct_code as
    CTCODE,pp.pp_name as PPNAME
    from service s,
    price_payment pp,supplier su,CURRENCYTYPE ct
    where
    pp.s_id=#{sid}
    and s.s_id=pp.s_id
    and ct.ct_code=pp.ct_code
    and s.so_code=su.sp_code
    and to_date(#{entryTime},'yyyy-mm-dd hh24:mi:ss') &gt;=  pp.PP_EFFECTIVEDATE
    and to_date(#{entryTime},'yyyy-mm-dd hh24:mi:ss') &lt;=  pp.PP_EXPIREDDATE
  </select>
  <select id="selectPricePayPlan" resultType="java.util.HashMap">
    select pp.pp_name as PPNAME,ct.ct_name as CTNAME,su.sp_name as SPNAME,pr.pr_plan_amount as PLANAMOUNT
    from product p,service s,product_service ps,
    price_payment pp,supplier su,payment_record pr,CURRENCYTYPE ct
    where
    p.p_servicetype=#{serviceType}
    and p.occ_company_id=#{companyId}
    and p.p_id=ps.p_id
    and ct.ct_code=pp.ct_code
    and s.s_id=ps.s_id
    and s.s_id=pp.s_id
    and s.so_code=su.sp_code
    and pr.pp_id=pp.pp_id
    and pr.eawb_printcode=#{printCode}
  </select>
  <select id="searchExpressAirWayBillTool" resultType="java.util.Map" parameterType="com.sinoair.billing.domain.vo.FilterParam">
    SELECT * FROM EXPRESSAIRWAYBILL WHERE EAWB_STATUS='ON' and
      <if test="id==1">
         EAWB_PRINTCODE IN
        <foreach collection="servers" item="item" open="(" close=")" separator=",">
          #{item}
        </foreach>
      </if>
      <if test="id==2">
         MAWB_CODE IN
        <foreach collection="servers" item="item" open="(" close=")" separator=",">
          #{item}
        </foreach>
      </if>
      <if test="id==3">
         EAWB_REFERENCE1 IN
        <foreach collection="servers" item="item" open="(" close=")" separator=",">
          #{item}
        </foreach>
      </if>
      <if test="id==4">
         EAWB_REFERENCE2 IN
        <foreach collection="servers" item="item" open="(" close=")" separator=",">
          #{item}
        </foreach>
      </if>
      <if test="id==5">
         EAWB_TRACKING_NO IN
        <foreach collection="servers" item="item" open="(" close=")" separator=",">
          #{item}
        </foreach>
      </if>
  </select>

  <select id="selectEawbPrintcodeList" resultType="java.lang.String">
      select eawb.eawb_printcode from expressairwaybill eawb where eawb.eawb_printcode in
        <foreach collection="eawbPintCodes" item="eawbPintCode" open="(" close=")" separator=",">
          #{eawbPintCode}
        </foreach>
  </select>
<delete id="deleteEawbByPrintCode" parameterType="java.util.ArrayList">
        DELETE FROM expressairwaybill eawb WHERE eawb.EAWB_PRINTCODE IN
        <foreach collection="eawbPintCodes" item="eawbPintCode" open="(" close=")" separator=",">
          #{eawbPintCode}
        </foreach>
</delete>
  <delete id="deleteActivityByPrintCode" parameterType="java.util.ArrayList">
    DELETE FROM EXPRESSBUSINESSACTIVITY eba WHERE eba.EAWB_PRINTCODE IN
    <foreach collection="eawbPintCodes" item="eawbPintCode" open="(" close=")" separator=",">
      #{eawbPintCode}
    </foreach>
  </delete>

<!--  ,-->
<!--  eawb.EAWB_DELIVER_ADDRESS,-->
<!--  eawb.EAWB_DELIVER_CONTACT,-->
<!--  eawb.EAWB_DELIVER_PHONE,-->
<!--  eawb.EAWB_DELIVER_MOBILE,-->
<!--  eawb.EAWB_DELIVER_EMAIL,-->
<!--  eawb.EAWB_SERVICEREQUIREMENT-->

<!--  ,-->
<!--  #{item.eawbDeliverAddress,jdbcType=VARCHAR},-->
<!--  #{item.eawbDeliverContact,jdbcType=VARCHAR},-->
<!--  #{item.eawbDeliverPhone,jdbcType=VARCHAR},-->
<!--  #{item.eawbDeliverMobile,jdbcType=VARCHAR},-->
<!--  #{item.eawbDeliverEmail,jdbcType=VARCHAR},-->
<!--  #{item.eawbServicerequirement,jdbcType=VARCHAR}-->
  <insert id="insertBatch" parameterType="java.util.ArrayList">
    insert into expressairwaybill eawb (eawb.eawb_syscode,
    eawb.eawb_printcode,
    eawb.est_code,
    eawb.eawb_handletime,
    eawb.eawb_departure,
    eawb.eawb_destination,
    eawb.eawb_pieces,
    eawb.eawb_volume,
    eawb.eawb_grossweight,
    eawb.eawb_chargeableweight,
    eawb.ct_code,
    eawb.sac_id,
    eawb.eawb_so_code,
    eawb.eawb_reference1,
    eawb.eawb_reference2,
    eawb.eawb_status,
    eawb.eawb_outbound_sac_id,
    eawb.eawb_keyentrytime,
    eawb.eawb_departcountry,
    eawb.eawb_destcountry,
    eawb.eawb_custprodname,
    eawb.eawb_transmodeid,
    eawb.eawb_servicetype,
    eawb.mawb_code,
    eawb.eawb_length,
    eawb.eawb_width,
    eawb.eawb_height,
    eawb.eawb_shipper_accountname,
    eawb.eawb_consignee_accountname,
    eawb.eawb_bt_code,
    eawb.eawb_pickup_postcode,
    eawb.eawb_deliver_postcode,
    eawb.eawb_tracking_no,
    eawb.eawb_sf_code,
    eawb.eawb_plate_code,
    eawb.eawb_quantity,
    eawb.eawb_custdeclval,
    eawb.eawb_specification,
    eawb.eawb_hscode,
    eawb.eawb_custprodenname,
    eawb.customer_order_code,
    eawb.order_code_in,

    eawb.EAWB_PARTITION,
    eawb.EAWB_TRANSMODEID_ORIGINAL,
    eawb.EAWB_SERVICETYPE_ORIGINAL,
    eawb.EAWB_IETYPE,
    eawb.EAWB_DESTCITY,
    eawb.EAWB_DECLAREVOLUME,
    eawb.EAWB_DECLAREGROSSWEIGHT,
    eawb.EAWB_DECLARECHARGEABLE,
    eawb.EAWB_DESTSTATE,
    eawb.REFUND_STATUS,
    eawb.TRANSPORT_TYPE,
    eawb.EA_FLIGHT_TYPE,
    eawb.EAWB_ECOMMERCE,
    eawb.WEIGHT_VALUE,
    eawb.EAWB_DECLAREVALUE,
    eawb.EAWB_COD,
    eawb.EAWB_CODVALUE,
    eawb.EAWB_CODCURRENCY,
    eawb.EAWB_FIRSTMILE,
    eawb.weight_unit,
    eawb.zone_code,
    eawb.DEST_SAC_ID,
    eawb.EAWB_UPDATETIME,
    eawb.EAWB_SERVICEREQUIREMENT,
    eawb.EAWB_INNER,
    eawb.EAWB_BILLING_STATUS
    )
    select SEQ_EXPRESSAIRWAYBILL.NEXTVAL,cd.* from(
    <foreach collection="list" item="item" index="index" separator="UNION ALL">
      (select

          #{item.eawbPrintcode,jdbcType=VARCHAR},
          #{item.estCode,jdbcType=VARCHAR},
          #{item.eawbHandletime,jdbcType=TIMESTAMP},
          #{item.eawbDeparture,jdbcType=VARCHAR},
          #{item.eawbDestination,jdbcType=VARCHAR},
          #{item.eawbPieces,jdbcType=DECIMAL},
          #{item.eawbVolume,jdbcType=DECIMAL},
          #{item.eawbGrossweight,jdbcType=DECIMAL},
          #{item.eawbChargeableweight,jdbcType=DECIMAL},
          #{item.ctCode,jdbcType=VARCHAR},
          #{item.sacId,jdbcType=VARCHAR},
          #{item.eawbSoCode,jdbcType=VARCHAR},
          #{item.eawbReference1,jdbcType=VARCHAR},
          #{item.eawbReference2,jdbcType=VARCHAR},
          #{item.eawbStatus,jdbcType=VARCHAR},
          #{item.eawbOutboundSacId,jdbcType=VARCHAR},
          #{item.eawbKeyentrytime,jdbcType=TIMESTAMP},
          #{item.eawbDepartcountry,jdbcType=VARCHAR},
          #{item.eawbDestcountry,jdbcType=VARCHAR},
          #{item.eawbCustprodname,jdbcType=VARCHAR},
          #{item.eawbTransmodeid,jdbcType=VARCHAR},
          #{item.eawbServicetype,jdbcType=VARCHAR},
          #{item.mawbCode,jdbcType=VARCHAR},
          #{item.eawbLength,jdbcType=DECIMAL},
          #{item.eawbWidth,jdbcType=DECIMAL},
          #{item.eawbHeight,jdbcType=DECIMAL},
          #{item.eawbShipperAccountname,jdbcType=VARCHAR},
          #{item.eawbConsigneeAccountname,jdbcType=VARCHAR},
          #{item.eawbBtCode,jdbcType=VARCHAR},
          #{item.eawbPickupPostcode,jdbcType=VARCHAR},
          #{item.eawbDeliverPostcode,jdbcType=VARCHAR},
          #{item.eawbTrackingNo,jdbcType=VARCHAR},
          #{item.eawbSfCode,jdbcType=VARCHAR},
          #{item.eawbPlateCode,jdbcType=VARCHAR},
          #{item.eawbQuantity,jdbcType=DECIMAL},
          #{item.eawbCustdeclval,jdbcType=DECIMAL},
          #{item.eawbSpecification,jdbcType=VARCHAR},
          #{item.eawbHscode,jdbcType=VARCHAR},
          #{item.eawbCustprodenname,jdbcType=VARCHAR},
          #{item.customerOrderCode,jdbcType=VARCHAR},
          #{item.orderCodeIn,jdbcType=VARCHAR},
          #{item.eawbPartition,jdbcType=VARCHAR},
          #{item.eawbTransmodeIdOriginal,jdbcType=VARCHAR},
          #{item.eawbServiceTypeOriginal,jdbcType=VARCHAR},
          #{item.eawbIetype,jdbcType=VARCHAR},
          #{item.eawbDestcity,jdbcType=VARCHAR},
          #{item.eawbDeclarevolume,jdbcType=DECIMAL},
          #{item.eawbDeclaregrossweight,jdbcType=DECIMAL},
          #{item.eawbDeclarechargeable,jdbcType=DECIMAL},
          #{item.eawbDeststate,jdbcType=VARCHAR},
          #{item.refundStatus,jdbcType=VARCHAR},
          #{item.transpostType,jdbcType=VARCHAR},
          #{item.eaFlightType,jdbcType=VARCHAR},
          #{item.eawbEcommerce,jdbcType=VARCHAR},
          #{item.weightValue,jdbcType=DECIMAL},
          #{item.eawbDeclarevalue,jdbcType=DECIMAL},
          #{item.eawbCod,jdbcType=VARCHAR},
          #{item.eawbCodvalue,jdbcType=DECIMAL},
          #{item.eawbCodcurrency,jdbcType=VARCHAR},
          #{item.eawbFirstmile,jdbcType=VARCHAR},
          #{item.weightUnit,jdbcType=VARCHAR},
          #{item.zoneCode,jdbcType=VARCHAR},
          #{item.destSacId,jdbcType=VARCHAR},
          #{item.eawbUpdatetime,jdbcType=TIMESTAMP},
          #{item.eawbServicerequirement,jdbcType=VARCHAR},
          #{item.eawbInner,jdbcType=VARCHAR},
          #{item.eawbBillingStatus,jdbcType=VARCHAR}
      from dual)
    </foreach>) cd
  </insert>



  <!--按按eawbprintcode,eawbReference1, eawbReference2更新-->
  <update id="batchUpdateEawbTrackingNoByCode" parameterType="java.util.List">
    <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
      update expressairwaybill
      <set>
        <if test="item.serviceType !=null and item.serviceType !=''">
          eawb_servicetype = #{item.serviceType},
        </if>
        <if test="item.eawbTrackingNo!=null and item.eawbTrackingNo!=''">
          eawb_tracking_no = #{item.eawbTrackingNo},
        </if>
        <if test="item.eawbUpdatetime!=null and item.eawbUpdatetime!=''">
          eawb_updatetime =#{item.eawbUpdatetime}
        </if>
      </set>
      where
      <if test="item.serviceType == null or item.serviceType == ''">
        eawb_servicetype in ('DISTRIBUTOR_11180269','DISTRIBUTOR_MBXPXB',
        'DISTRIBUTOR_MBXPXN','DISTRIBUTOR_MBXPXP')
        and
      </if>
      (
        eawb_printcode=#{item.code}
        or eawb_reference1=#{item.code}
        or eawb_reference2=#{item.code}
      )
    </foreach>
  </update>

<!--按总单更新-->
  <update id="updateEawbTrackingNoByMawbCode" parameterType="java.lang.String">
      update expressairwaybill
        <set>
          <if test="serviceType!=null and serviceType !=''">
            eawb_servicetype = #{serviceType},
          </if>
          <if test="eawbTrackingNo!=null and eawbTrackingNo!=''">
            eawb_tracking_no = #{eawbTrackingNo},
          </if>
          <if test="eawbUpdatetime!=null and eawbUpdatetime!=''">
            eawb_updatetime = #{eawbUpdatetime}
          </if>
        </set>
     where
        <if test="serviceType ==null or serviceType ==''">
           eawb_servicetype = 'DISTRIBUTOR_11180269' and
        </if>
        mawb_code = #{mawbCode}
  </update>

  <!--按按eawbReference1, eawbReference2更新-->
  <update id="batchUpdateEawbWeightByReference" parameterType="java.util.List">
    <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
      update expressairwaybill
      set
        WEIGHT_VALUE = #{item.weightValue},
        WEIGHT_UNIT = #{item.weightUnit},
        ZONE_CODE = #{item.zoneCode},
        EAWB_HANDLETIME = #{item.eawbHandletime}
      where
      eawb_reference1=#{item.eawbReference1}
    </foreach>
  </update>

  <select id="selectByRef1" resultType="com.sinoair.billing.domain.model.billing.ExpressAirWayBill">
    SELECT <include refid="Base_Column_List" />
    FROM EXPRESSAIRWAYBILL EAWB WHERE  EAWB_REFERENCE1 = #{eawbReference1,jdbcType=VARCHAR}
  </select>

  <select id="countBySoCode" resultType="java.lang.Integer" parameterType="com.sinoair.billing.domain.vo.query.ActivityQuery">
    select count(0)
    from expressairwaybill
    where eawb_printcode=#{eawbPrintcode,jdbcType=VARCHAR}
    <if test="soCode !=null and soCode !=''">
        and EAWB_SO_CODE = #{soCode,jdbcType=VARCHAR}
    </if>
    <if test="nonSoCode !=null and nonSoCode !=''">
        and EAWB_SO_CODE != #{nonSoCode,jdbcType=VARCHAR}
    </if>
    <if test="eawbServicetype !=null and eawbServicetype !=''">
        and EAWB_SERVICETYPE_ORIGINAL = #{eawbServicetype,jdbcType=VARCHAR}
    </if>
  </select>

  <!--按按eawbprintcode更新时间-->
  <update id="batchUpdateHandletime" parameterType="java.util.List">
    <foreach collection="list" item="eawbPintCode" index="index" open="begin" close=";end;" separator=";">
      update expressairwaybill
      <set>
        eawb_handletime = sysdate
      </set>
      where
        eawb_printcode=#{eawbPintCode}

    </foreach>

  </update>

  <select id="selectByEawbPrintCode" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select
    <!--eawb_printcode,eawb_so_code,eawb_outbound_sac_id,eawb_keyentrytime,sac_id,EAWB_SERVICETYPE_ORIGINAL -->
    <include refid="Base_Column_List" />
    from EXPRESSAIRWAYBILL
    where eawb_printcode = #{eawbPrintCode,jdbcType=VARCHAR}
  </select>
  <select id="selectOtherByEawbPrintCode" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select
    <!--eawb_printcode,eawb_so_code,eawb_outbound_sac_id,eawb_keyentrytime,sac_id,EAWB_SERVICETYPE_ORIGINAL -->
    <include refid="Base_Column_List" />
    from EXPRESSAIRWAYBILL_OTHER
    where eawb_printcode = #{eawbPrintCode,jdbcType=VARCHAR}
  </select>

  <select id="selectByRef2" resultType="com.sinoair.billing.domain.model.billing.ExpressAirWayBill">
    SELECT <include refid="Base_Column_List" />
    FROM EXPRESSAIRWAYBILL EAWB WHERE  EAWB_REFERENCE2 = #{eawbReference2,jdbcType=VARCHAR}
  </select>

  <select id="selectOtherByRef2" resultType="com.sinoair.billing.domain.model.billing.ExpressAirWayBill">
    SELECT <include refid="Base_Column_List" />
    FROM EXPRESSAIRWAYBILL_OTHER EAWB WHERE  EAWB_REFERENCE2 = #{eawbReference2,jdbcType=VARCHAR}
  </select>

  <!--按按eawbprintcode更新主单信息-->
  <update id="batchUpdatMawb" parameterType="java.util.List">
    <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
      update expressairwaybill
      <set>
        MAWB_CODE=#{item.mawbCode},
        transport_type=#{item.transportType},
        ea_flight_type=#{item.eaFlightType},
        eawb_sf_code=#{item.eawbSfCode},
        dest_sac_id=#{item.destSacId},
        eawb_handletime = sysdate
      </set>
      where
      eawb_printcode=#{item.eawbPrintcode}

    </foreach>
  </update>

  <!--按按eawbprintcode更新主单信息-->
  <update id="batchUpdateMawbOther" parameterType="java.util.List">
    <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
      update expressairwaybill_other
      <set>
        MAWB_CODE=#{item.mawbCode},
        transport_type=#{item.transportType},
        ea_flight_type=#{item.eaFlightType},
        eawb_sf_code=#{item.eawbSfCode},
        dest_sac_id=#{item.destSacId},
        eawb_handletime = sysdate
      </set>
      where
      eawb_printcode=#{item.eawbPrintcode}

    </foreach>
  </update>

  <!--按按eawbprintcode更新基本信息-->
  <update id="batchUpdatInfo" parameterType="java.util.List">
    <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
      update expressairwaybill
      <set>
        <if test="item.eawbReference1 !=null">
        EAWB_REFERENCE1=#{item.eawbReference1,jdbcType=VARCHAR},
        </if>
        customer_order_code=#{item.customerOrderCode,jdbcType=VARCHAR},
        eawb_chargeableweight=#{item.eawbChargeableweight,jdbcType=DECIMAL},
        eawb_cod=#{item.eawbCod,jdbcType=VARCHAR},
        eawb_codcurrency=#{item.eawbCodcurrency,jdbcType=VARCHAR},
        eawb_codvalue=#{item.eawbCodvalue,jdbcType=DECIMAL},
        eawb_custdeclval=#{item.eawbCustdeclval,jdbcType=DECIMAL},
        eawb_deliver_postcode=#{item.eawbDeliverPostcode,jdbcType=VARCHAR},
        eawb_destcity=#{item.eawbDestcity,jdbcType=VARCHAR},
        eawb_destcountry=#{item.eawbDestcountry,jdbcType=VARCHAR},
        eawb_destination=#{item.eawbDestination,jdbcType=VARCHAR},
        eawb_deststate=#{item.eawbDeststate,jdbcType=VARCHAR},
        eawb_ecommerce=#{item.eawbEcommerce,jdbcType=VARCHAR},
        eawb_handletime=#{item.eawbHandletime,jdbcType=TIMESTAMP},
        eawb_hscode=#{item.eawbHscode,jdbcType=VARCHAR},
        eawb_partition=#{item.eawbPartition,jdbcType=VARCHAR},
        order_code_in=#{item.orderCodeIn,jdbcType=VARCHAR},
        refund_status=#{item.refundStatus,jdbcType=VARCHAR},
        WEIGHT_VALUE=#{item.weightValue,jdbcType=DECIMAL},
        eawb_tracking_no=#{item.eawbTrackingNo,jdbcType=VARCHAR},
        EAWB_SERVICEREQUIREMENT=#{item.eawbServicerequirement,jdbcType=VARCHAR}
      </set>
      where
      eawb_printcode=#{item.eawbPrintcode,jdbcType=VARCHAR}

    </foreach>
  </update>


  <select id="countEawbByMawbCode" resultType="java.lang.Integer" >
    select count(0)
    from expressairwaybill
    where eawb_printcode=#{eawbPrintCode}
      and MAWB_CODE=#{mawbCode}
  </select>

  <select id="countEawbOtherByMawbCode" resultType="java.lang.Integer" >
    select count(0)
    from expressairwaybill_other
    where eawb_printcode=#{eawbPrintCode}
      and MAWB_CODE=#{mawbCode}
  </select>

  <select id="countByMawbCode" resultType="java.lang.Integer" >
    select count(0)
    from expressairwaybill
    where MAWB_CODE=#{mawbCode}
  </select>

  <select id="countOtherByMawbCode" resultType="java.lang.Integer" >
    select count(0)
    from expressairwaybill
    where MAWB_CODE=#{mawbCode}
  </select>

  <select id="selectOrderWeightCollect" resultType="com.sinoair.billing.domain.model.billing.SinotransOrderWeight">
    select
      nvl((select sl.so_code from settlementobject sl where
          sl.so_syscode =( select so.p_id from settlementobject so where so.so_code=eawb_so_code)), eawb_so_code) as so_code,
    min(eawb_departcountry) as departure_code ,
    min(eawb_destcountry) as dest_code,
    to_char(eawb_updatetime,'yyyymmdd') as collect_month,
    to_char(eawb_updatetime+1,'yyyymmdd') as end_day,
    min(sac_id) as sac_id,
    eawb_servicetype_original as ep_key,
    (select SO_VENDOR_TYPE from settlementobject where so_code = eawb_so_code) as so_vendor_type,
    (select so_mode from settlementobject where so_code = eawb_so_code) as so_mode,
    sum(eawb_chargeableweight) as bm_chargeableweight,
    (select cust_code from settlementobject where so_code = eawb_so_code) as cust_code,
    count(0) as bm_piece
    from expressairwaybill
    where SINOTRANS_ID is null
    and eawb_type = 'B2C'
    and sac_id is not null
    and eawb_servicetype_original is not null
    and eawb_handletime >= sysdate-30
    and eawb_handletime &lt; TRUNC(SYSDATE)
    and eawb_so_code in (select so_code from settlementobject s where s.so_vendor_type = 'ONLINE' and s.bms_status = 'Y' )
    group by eawb_so_code,eawb_servicetype_original,to_char(eawb_updatetime,'yyyymmdd'),to_char(eawb_updatetime+1,'yyyymmdd')
  </select>

  <update id="updateSinotransIdBySo" parameterType="com.sinoair.billing.domain.vo.query.CommonQuery">
    update expressairwaybill
    <set>
        SINOTRANS_ID = #{sinotransId}
    </set>
    where
       eawb_so_code = #{soCode}
      and SINOTRANS_ID is null
      and eawb_servicetype_original = #{epKey}
      and eawb_updatetime >= to_date(${strStartDate},'yyyymmdd')
      and eawb_updatetime &lt; to_date(${strEndDate},'yyyymmdd')
      and eawb_type = 'B2C'
  </update>

  <select id="selectUpdateTimeNull" resultType="java.lang.String" >
    select eawb_printcode
    from expressairwaybill
    where eawb_updatetime is null
    and eawb_handletime>sysdate-30
  </select>

  <update id="updateEawbTime" parameterType="com.sinoair.billing.domain.model.billing.ExpressAirWayBill">
    update expressairwaybill
    <set>
      eawb_updatetime = #{eawbUpdatetime}
    </set>
    where
    eawb_printcode = #{eawbPrintcode}

  </update>

  <select id="selectOrderWeightCollectFba" resultType="com.sinoair.billing.domain.model.billing.BmsRecHeadFba"
          parameterType="com.sinoair.billing.domain.vo.query.CommonQuery" >
    select
    eawb_printcode as sinotransId,
    eawb_so_code as so_code,
    eawb_departcountry as departure_code ,
    eawb_destcountry as dest_code,
    to_char(eawb_updatetime,'yyyymmdd') as collect_month,
    t.sac_id as sac_id,
    eawb_servicetype_original as ep_key,
    (select SO_VENDOR_TYPE from settlementobject where so_code = eawb_so_code) as so_vendor_type,
    weight_value as bm_chargeableweight,
    1 as bm_piece
    from expressairwaybill t ,settlementobject s
    where t.eawb_so_code = s.so_code
    and SINOTRANS_ID is null
    and eawb_type = 'B2B'
    and t.eawb_billing_status = 'FINAL_REVIEW'
    and eawb_updatetime >= to_date(${strStartDate},'yyyymmdd')
    and eawb_updatetime &lt; to_date(${strEndDate},'yyyymmdd')

  </select>

  <update id="updateSinotransId" parameterType="com.sinoair.billing.domain.model.billing.ExpressAirWayBill">
    update expressairwaybill
    <set>
      SINOTRANS_ID = #{sinotransId}
    </set>
    where
    eawb_printcode = #{eawbPrintcode}

  </update>

  <select id="selectByOrderId" resultMap="BaseResultMap">
    select * from EXPRESSAIRWAYBILL where EAWB_PRINTCODE=(select eawb_printcode from app_order where id=#{orderId,jdbcType=DECIMAL})
  </select>

  <update id="updateStatusByEawb" parameterType="java.util.Map">
      begin
    <foreach collection="params.entrySet()" item="info" index="index" separator=";">
      update expressairwaybill set EAWB_REVIEW_STATUS='SETTLE_REVIEW',EAWB_REVIEW_TIME=#{info}
      where  EAWB_PRINTCODE=#{index} and EAWB_REVIEW_STATUS !='SETTLE_REVIEW'
    </foreach>
      ;end;
  </update>
</mapper>