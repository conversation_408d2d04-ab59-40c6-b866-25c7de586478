<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.billing.dao.billing.ExpressAssignmentActualMapper">

  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.ExpressAssignmentActual">
    <id column="EAA_SYSCODE" jdbcType="DECIMAL" property="eaaSyscode" />
    <result column="EAA_CODE" jdbcType="VARCHAR" property="eaaCode" />
    <result column="FLIGHT_NUMBER" jdbcType="VARCHAR" property="flightNumber" />
    <result column="HANDLER" jdbcType="VARCHAR" property="handler" />
    <result column="ORI_SAC_ID" jdbcType="VARCHAR" property="oriSacId" />
    <result column="EAA_TOTAL_EAWBS" jdbcType="DECIMAL" property="eaaTotalEawbs" />
    <result column="EAA_TOTAL_MAWBS" jdbcType="DECIMAL" property="eaaTotalMawbs" />
    <result column="MAWB_P_WEIGHT" jdbcType="DECIMAL" property="mawbPWeight" />
    <result column="MAWB_C_WEIGHT" jdbcType="DECIMAL" property="mawbCWeight" />
    <result column="ETD_DATE" jdbcType="TIMESTAMP" property="etdDate" />
    <result column="EAA_CREATETIME" jdbcType="TIMESTAMP" property="eaaCreatetime" />
    <result column="EAA_PUSHTIME" jdbcType="TIMESTAMP" property="eaaPushtime" />
    <result column="DEST_SAC_ID" jdbcType="VARCHAR" property="destSacId" />
    <result column="EAA_HANDLETIME" jdbcType="TIMESTAMP" property="eaaHandletime" />
    <result column="EAA_STATUS" jdbcType="VARCHAR" property="eaaStatus" />
    <result column="EAA_PUSHSTATUS" jdbcType="VARCHAR" property="eaaPushstatus" />
    <result column="TRANSMODEID" jdbcType="VARCHAR" property="transmodeid" />
    <result column="SAC_ID" jdbcType="VARCHAR" property="sacId" />
    <result column="TRANSPORT_TYPE" jdbcType="VARCHAR" property="transportType" />
    <result column="EAA_REMARK" jdbcType="VARCHAR" property="eaaRemark" />
    <result column="EAA_FLIGHT_TYPE" jdbcType="VARCHAR" property="eaaFlightType" />
    <result column="ETA_DATE" jdbcType="TIMESTAMP" property="etaDate" />
    <result column="MAWB_VOLUMN" jdbcType="DECIMAL" property="mawbVolumn" />
    <result column="MAWB_ULDTYPE" jdbcType="VARCHAR" property="mawbUldtype" />
    <result column="EXPORTCLEARANCECITY" jdbcType="VARCHAR" property="exportclearancecity" />
    <result column="AGENT" jdbcType="VARCHAR" property="agent" />
    <result column="FOREIGNCLEARANCESAC_ID" jdbcType="VARCHAR" property="foreignclearancesacId" />
    <result column="EAA_INSERTPUSHTIME" jdbcType="TIMESTAMP" property="eaaInsertpushtime" />
    <result column="EAA_INSERTPUSHSTATUS" jdbcType="VARCHAR" property="eaaInsertpushstatus" />
    <result column="EAA_CLEARANCETYPE" jdbcType="VARCHAR" property="eaaClearancetype" />
    <result column="FIRST_FLIGHT_NUMBER" jdbcType="VARCHAR" property="firstFlightNumber" />
    <result column="FIRST_ORI_SAC_ID" jdbcType="VARCHAR" property="firstOriSacId" />
    <result column="FIRST_DEST_SAC_ID" jdbcType="VARCHAR" property="firstDestSacId" />
    <result column="FIRST_ETD" jdbcType="TIMESTAMP" property="firstEtd" />
    <result column="FIRST_ETA" jdbcType="TIMESTAMP" property="firstEta" />
    <result column="SECOND_FLIGHT_NUMBER" jdbcType="VARCHAR" property="secondFlightNumber" />
    <result column="SECOND_ORI_SAC_ID" jdbcType="VARCHAR" property="secondOriSacId" />
    <result column="SECOND_DEST_SAC_ID" jdbcType="VARCHAR" property="secondDestSacId" />
    <result column="SECOND_ETD" jdbcType="TIMESTAMP" property="secondEtd" />
    <result column="SECOND_ETA" jdbcType="TIMESTAMP" property="secondEta" />
    <result column="THIRD_FLIGHT_NUMBER" jdbcType="VARCHAR" property="thirdFlightNumber" />
    <result column="THIRD_ORI_SAC_ID" jdbcType="VARCHAR" property="thirdOriSacId" />
    <result column="THIRD_DEST_SAC_ID" jdbcType="VARCHAR" property="thirdDestSacId" />
    <result column="THIRD_ETD" jdbcType="TIMESTAMP" property="thirdEtd" />
    <result column="THIRD_ETA" jdbcType="TIMESTAMP" property="thirdEta" />
    <result column="AIRCRAFTTYPE" jdbcType="VARCHAR" property="aircrafttype" />
    <result column="FREIGHTAGENT" jdbcType="VARCHAR" property="freightagent" />
    <result column="TERMINALCODE" jdbcType="VARCHAR" property="terminalcode" />
    <result column="EAA_ASSTIME" jdbcType="TIMESTAMP" property="eaaAsstime" />
    <result column="AIRLINECODE" jdbcType="VARCHAR" property="airlinecode" />
    <result column="EAA_COMMENT" jdbcType="VARCHAR" property="eaaComment" />
    <result column="STD" jdbcType="TIMESTAMP" property="std" />
    <result column="SO_CODE" jdbcType="VARCHAR" property="soCode" />
    <result column="ISCNDIRECT" jdbcType="VARCHAR" property="iscndirect" />
    <result column="EAA_AMOUNT" jdbcType="DECIMAL" property="eaaAmount" />
  </resultMap>
  <sql id="Base_Column_List">
    EAA_SYSCODE, EAA_CODE, FLIGHT_NUMBER, HANDLER, ORI_SAC_ID, EAA_TOTAL_EAWBS, EAA_TOTAL_MAWBS, 
    MAWB_P_WEIGHT, MAWB_C_WEIGHT, ETD_DATE, EAA_CREATETIME, EAA_PUSHTIME, DEST_SAC_ID, 
    EAA_HANDLETIME, EAA_STATUS, EAA_PUSHSTATUS, TRANSMODEID, SAC_ID, TRANSPORT_TYPE, 
    EAA_REMARK, EAA_FLIGHT_TYPE, ETA_DATE, MAWB_VOLUMN, MAWB_ULDTYPE, EXPORTCLEARANCECITY, 
    AGENT, FOREIGNCLEARANCESAC_ID, EAA_INSERTPUSHTIME, EAA_INSERTPUSHSTATUS, EAA_CLEARANCETYPE,
    FIRST_FLIGHT_NUMBER, FIRST_ORI_SAC_ID, FIRST_DEST_SAC_ID, FIRST_ETD, FIRST_ETA, SECOND_FLIGHT_NUMBER,
    SECOND_ORI_SAC_ID, SECOND_DEST_SAC_ID, SECOND_ETD, SECOND_ETA, THIRD_FLIGHT_NUMBER,
    THIRD_ORI_SAC_ID, THIRD_DEST_SAC_ID, THIRD_ETD, THIRD_ETA, AIRCRAFTTYPE, FREIGHTAGENT,
    TERMINALCODE, EAA_ASSTIME, AIRLINECODE, EAA_COMMENT, STD, SO_CODE, ISCNDIRECT,EAA_AMOUNT
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from EXPRESSASSIGNMENTACTUAL
    where EAA_SYSCODE = #{eaaSyscode,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from EXPRESSASSIGNMENTACTUAL
    where EAA_SYSCODE = #{eaaSyscode,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.ExpressAssignmentActual">
    insert into EXPRESSASSIGNMENTACTUAL (EAA_SYSCODE, EAA_CODE, FLIGHT_NUMBER,
                                         HANDLER, ORI_SAC_ID, EAA_TOTAL_EAWBS,
                                         EAA_TOTAL_MAWBS, MAWB_P_WEIGHT, MAWB_C_WEIGHT,
                                         ETD_DATE, EAA_CREATETIME, EAA_PUSHTIME,
                                         DEST_SAC_ID, EAA_HANDLETIME, EAA_STATUS,
                                         EAA_PUSHSTATUS, TRANSMODEID, SAC_ID,
                                         TRANSPORT_TYPE, EAA_REMARK, EAA_FLIGHT_TYPE,
                                         ETA_DATE, MAWB_VOLUMN, MAWB_ULDTYPE,
                                         EXPORTCLEARANCECITY, AGENT, FOREIGNCLEARANCESAC_ID,
                                         EAA_INSERTPUSHTIME, EAA_INSERTPUSHSTATUS,
                                         EAA_CLEARANCETYPE, FIRST_FLIGHT_NUMBER, FIRST_ORI_SAC_ID,
                                         FIRST_DEST_SAC_ID, FIRST_ETD, FIRST_ETA,
                                         SECOND_FLIGHT_NUMBER, SECOND_ORI_SAC_ID, SECOND_DEST_SAC_ID,
                                         SECOND_ETD, SECOND_ETA, THIRD_FLIGHT_NUMBER,
                                         THIRD_ORI_SAC_ID, THIRD_DEST_SAC_ID, THIRD_ETD,
                                         THIRD_ETA, AIRCRAFTTYPE, FREIGHTAGENT,
                                         TERMINALCODE, EAA_ASSTIME, AIRLINECODE,
                                         EAA_COMMENT, STD, SO_CODE,
                                         ISCNDIRECT)
    values (#{eaaSyscode,jdbcType=DECIMAL}, #{eaaCode,jdbcType=VARCHAR}, #{flightNumber,jdbcType=VARCHAR},
            #{handler,jdbcType=VARCHAR}, #{oriSacId,jdbcType=VARCHAR}, #{eaaTotalEawbs,jdbcType=DECIMAL},
            #{eaaTotalMawbs,jdbcType=DECIMAL}, #{mawbPWeight,jdbcType=DECIMAL}, #{mawbCWeight,jdbcType=DECIMAL},
            #{etdDate,jdbcType=TIMESTAMP}, #{eaaCreatetime,jdbcType=TIMESTAMP}, #{eaaPushtime,jdbcType=TIMESTAMP},
            #{destSacId,jdbcType=VARCHAR}, #{eaaHandletime,jdbcType=TIMESTAMP}, #{eaaStatus,jdbcType=VARCHAR},
            #{eaaPushstatus,jdbcType=VARCHAR}, #{transmodeid,jdbcType=VARCHAR}, #{sacId,jdbcType=VARCHAR},
            #{transportType,jdbcType=VARCHAR}, #{eaaRemark,jdbcType=VARCHAR}, #{eaaFlightType,jdbcType=VARCHAR},
            #{etaDate,jdbcType=TIMESTAMP}, #{mawbVolumn,jdbcType=DECIMAL}, #{mawbUldtype,jdbcType=VARCHAR},
            #{exportclearancecity,jdbcType=VARCHAR}, #{agent,jdbcType=VARCHAR}, #{foreignclearancesacId,jdbcType=VARCHAR},
            #{eaaInsertpushtime,jdbcType=TIMESTAMP}, #{eaaInsertpushstatus,jdbcType=VARCHAR},
            #{eaaClearancetype,jdbcType=VARCHAR}, #{firstFlightNumber,jdbcType=VARCHAR}, #{firstOriSacId,jdbcType=VARCHAR},
            #{firstDestSacId,jdbcType=VARCHAR}, #{firstEtd,jdbcType=TIMESTAMP}, #{firstEta,jdbcType=TIMESTAMP},
            #{secondFlightNumber,jdbcType=VARCHAR}, #{secondOriSacId,jdbcType=VARCHAR}, #{secondDestSacId,jdbcType=VARCHAR},
            #{secondEtd,jdbcType=TIMESTAMP}, #{secondEta,jdbcType=TIMESTAMP}, #{thirdFlightNumber,jdbcType=VARCHAR},
            #{thirdOriSacId,jdbcType=VARCHAR}, #{thirdDestSacId,jdbcType=VARCHAR}, #{thirdEtd,jdbcType=TIMESTAMP},
            #{thirdEta,jdbcType=TIMESTAMP}, #{aircrafttype,jdbcType=VARCHAR}, #{freightagent,jdbcType=VARCHAR},
            #{terminalcode,jdbcType=VARCHAR}, #{eaaAsstime,jdbcType=TIMESTAMP}, #{airlinecode,jdbcType=VARCHAR},
            #{eaaComment,jdbcType=VARCHAR}, #{std,jdbcType=TIMESTAMP}, #{soCode,jdbcType=VARCHAR},
            #{iscndirect,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.ExpressAssignmentActual">
    insert into EXPRESSASSIGNMENTACTUAL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="eaaSyscode != null">
        EAA_SYSCODE,
      </if>
      <if test="eaaCode != null">
        EAA_CODE,
      </if>
      <if test="flightNumber != null">
        FLIGHT_NUMBER,
      </if>
      <if test="handler != null">
        HANDLER,
      </if>
      <if test="oriSacId != null">
        ORI_SAC_ID,
      </if>
      <if test="eaaTotalEawbs != null">
        EAA_TOTAL_EAWBS,
      </if>
      <if test="eaaTotalMawbs != null">
        EAA_TOTAL_MAWBS,
      </if>
      <if test="mawbPWeight != null">
        MAWB_P_WEIGHT,
      </if>
      <if test="mawbCWeight != null">
        MAWB_C_WEIGHT,
      </if>
      <if test="etdDate != null">
        ETD_DATE,
      </if>
      <if test="eaaCreatetime != null">
        EAA_CREATETIME,
      </if>
      <if test="eaaPushtime != null">
        EAA_PUSHTIME,
      </if>
      <if test="destSacId != null">
        DEST_SAC_ID,
      </if>
      <if test="eaaHandletime != null">
        EAA_HANDLETIME,
      </if>
      <if test="eaaStatus != null">
        EAA_STATUS,
      </if>
      <if test="eaaPushstatus != null">
        EAA_PUSHSTATUS,
      </if>
      <if test="transmodeid != null">
        TRANSMODEID,
      </if>
      <if test="sacId != null">
        SAC_ID,
      </if>
      <if test="transportType != null">
        TRANSPORT_TYPE,
      </if>
      <if test="eaaRemark != null">
        EAA_REMARK,
      </if>
      <if test="eaaFlightType != null">
        EAA_FLIGHT_TYPE,
      </if>
      <if test="etaDate != null">
        ETA_DATE,
      </if>
      <if test="mawbVolumn != null">
        MAWB_VOLUMN,
      </if>
      <if test="mawbUldtype != null">
        MAWB_ULDTYPE,
      </if>
      <if test="exportclearancecity != null">
        EXPORTCLEARANCECITY,
      </if>
      <if test="agent != null">
        AGENT,
      </if>
      <if test="foreignclearancesacId != null">
        FOREIGNCLEARANCESAC_ID,
      </if>
      <if test="eaaInsertpushtime != null">
        EAA_INSERTPUSHTIME,
      </if>
      <if test="eaaInsertpushstatus != null">
        EAA_INSERTPUSHSTATUS,
      </if>
      <if test="eaaClearancetype != null">
        EAA_CLEARANCETYPE,
      </if>
      <if test="firstFlightNumber != null">
        FIRST_FLIGHT_NUMBER,
      </if>
      <if test="firstOriSacId != null">
        FIRST_ORI_SAC_ID,
      </if>
      <if test="firstDestSacId != null">
        FIRST_DEST_SAC_ID,
      </if>
      <if test="firstEtd != null">
        FIRST_ETD,
      </if>
      <if test="firstEta != null">
        FIRST_ETA,
      </if>
      <if test="secondFlightNumber != null">
        SECOND_FLIGHT_NUMBER,
      </if>
      <if test="secondOriSacId != null">
        SECOND_ORI_SAC_ID,
      </if>
      <if test="secondDestSacId != null">
        SECOND_DEST_SAC_ID,
      </if>
      <if test="secondEtd != null">
        SECOND_ETD,
      </if>
      <if test="secondEta != null">
        SECOND_ETA,
      </if>
      <if test="thirdFlightNumber != null">
        THIRD_FLIGHT_NUMBER,
      </if>
      <if test="thirdOriSacId != null">
        THIRD_ORI_SAC_ID,
      </if>
      <if test="thirdDestSacId != null">
        THIRD_DEST_SAC_ID,
      </if>
      <if test="thirdEtd != null">
        THIRD_ETD,
      </if>
      <if test="thirdEta != null">
        THIRD_ETA,
      </if>
      <if test="aircrafttype != null">
        AIRCRAFTTYPE,
      </if>
      <if test="freightagent != null">
        FREIGHTAGENT,
      </if>
      <if test="terminalcode != null">
        TERMINALCODE,
      </if>
      <if test="eaaAsstime != null">
        EAA_ASSTIME,
      </if>
      <if test="airlinecode != null">
        AIRLINECODE,
      </if>
      <if test="eaaComment != null">
        EAA_COMMENT,
      </if>
      <if test="std != null">
        STD,
      </if>
      <if test="soCode != null">
        SO_CODE,
      </if>
      <if test="iscndirect != null">
        ISCNDIRECT,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="eaaSyscode != null">
        #{eaaSyscode,jdbcType=DECIMAL},
      </if>
      <if test="eaaCode != null">
        #{eaaCode,jdbcType=VARCHAR},
      </if>
      <if test="flightNumber != null">
        #{flightNumber,jdbcType=VARCHAR},
      </if>
      <if test="handler != null">
        #{handler,jdbcType=VARCHAR},
      </if>
      <if test="oriSacId != null">
        #{oriSacId,jdbcType=VARCHAR},
      </if>
      <if test="eaaTotalEawbs != null">
        #{eaaTotalEawbs,jdbcType=DECIMAL},
      </if>
      <if test="eaaTotalMawbs != null">
        #{eaaTotalMawbs,jdbcType=DECIMAL},
      </if>
      <if test="mawbPWeight != null">
        #{mawbPWeight,jdbcType=DECIMAL},
      </if>
      <if test="mawbCWeight != null">
        #{mawbCWeight,jdbcType=DECIMAL},
      </if>
      <if test="etdDate != null">
        #{etdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="eaaCreatetime != null">
        #{eaaCreatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="eaaPushtime != null">
        #{eaaPushtime,jdbcType=TIMESTAMP},
      </if>
      <if test="destSacId != null">
        #{destSacId,jdbcType=VARCHAR},
      </if>
      <if test="eaaHandletime != null">
        #{eaaHandletime,jdbcType=TIMESTAMP},
      </if>
      <if test="eaaStatus != null">
        #{eaaStatus,jdbcType=VARCHAR},
      </if>
      <if test="eaaPushstatus != null">
        #{eaaPushstatus,jdbcType=VARCHAR},
      </if>
      <if test="transmodeid != null">
        #{transmodeid,jdbcType=VARCHAR},
      </if>
      <if test="sacId != null">
        #{sacId,jdbcType=VARCHAR},
      </if>
      <if test="transportType != null">
        #{transportType,jdbcType=VARCHAR},
      </if>
      <if test="eaaRemark != null">
        #{eaaRemark,jdbcType=VARCHAR},
      </if>
      <if test="eaaFlightType != null">
        #{eaaFlightType,jdbcType=VARCHAR},
      </if>
      <if test="etaDate != null">
        #{etaDate,jdbcType=TIMESTAMP},
      </if>
      <if test="mawbVolumn != null">
        #{mawbVolumn,jdbcType=DECIMAL},
      </if>
      <if test="mawbUldtype != null">
        #{mawbUldtype,jdbcType=VARCHAR},
      </if>
      <if test="exportclearancecity != null">
        #{exportclearancecity,jdbcType=VARCHAR},
      </if>
      <if test="agent != null">
        #{agent,jdbcType=VARCHAR},
      </if>
      <if test="foreignclearancesacId != null">
        #{foreignclearancesacId,jdbcType=VARCHAR},
      </if>
      <if test="eaaInsertpushtime != null">
        #{eaaInsertpushtime,jdbcType=TIMESTAMP},
      </if>
      <if test="eaaInsertpushstatus != null">
        #{eaaInsertpushstatus,jdbcType=VARCHAR},
      </if>
      <if test="eaaClearancetype != null">
        #{eaaClearancetype,jdbcType=VARCHAR},
      </if>
      <if test="firstFlightNumber != null">
        #{firstFlightNumber,jdbcType=VARCHAR},
      </if>
      <if test="firstOriSacId != null">
        #{firstOriSacId,jdbcType=VARCHAR},
      </if>
      <if test="firstDestSacId != null">
        #{firstDestSacId,jdbcType=VARCHAR},
      </if>
      <if test="firstEtd != null">
        #{firstEtd,jdbcType=TIMESTAMP},
      </if>
      <if test="firstEta != null">
        #{firstEta,jdbcType=TIMESTAMP},
      </if>
      <if test="secondFlightNumber != null">
        #{secondFlightNumber,jdbcType=VARCHAR},
      </if>
      <if test="secondOriSacId != null">
        #{secondOriSacId,jdbcType=VARCHAR},
      </if>
      <if test="secondDestSacId != null">
        #{secondDestSacId,jdbcType=VARCHAR},
      </if>
      <if test="secondEtd != null">
        #{secondEtd,jdbcType=TIMESTAMP},
      </if>
      <if test="secondEta != null">
        #{secondEta,jdbcType=TIMESTAMP},
      </if>
      <if test="thirdFlightNumber != null">
        #{thirdFlightNumber,jdbcType=VARCHAR},
      </if>
      <if test="thirdOriSacId != null">
        #{thirdOriSacId,jdbcType=VARCHAR},
      </if>
      <if test="thirdDestSacId != null">
        #{thirdDestSacId,jdbcType=VARCHAR},
      </if>
      <if test="thirdEtd != null">
        #{thirdEtd,jdbcType=TIMESTAMP},
      </if>
      <if test="thirdEta != null">
        #{thirdEta,jdbcType=TIMESTAMP},
      </if>
      <if test="aircrafttype != null">
        #{aircrafttype,jdbcType=VARCHAR},
      </if>
      <if test="freightagent != null">
        #{freightagent,jdbcType=VARCHAR},
      </if>
      <if test="terminalcode != null">
        #{terminalcode,jdbcType=VARCHAR},
      </if>
      <if test="eaaAsstime != null">
        #{eaaAsstime,jdbcType=TIMESTAMP},
      </if>
      <if test="airlinecode != null">
        #{airlinecode,jdbcType=VARCHAR},
      </if>
      <if test="eaaComment != null">
        #{eaaComment,jdbcType=VARCHAR},
      </if>
      <if test="std != null">
        #{std,jdbcType=TIMESTAMP},
      </if>
      <if test="soCode != null">
        #{soCode,jdbcType=VARCHAR},
      </if>
      <if test="iscndirect != null">
        #{iscndirect,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.ExpressAssignmentActual">
    update EXPRESSASSIGNMENTACTUAL
    <set>
      <if test="eaaCode != null">
        EAA_CODE = #{eaaCode,jdbcType=VARCHAR},
      </if>
      <if test="flightNumber != null">
        FLIGHT_NUMBER = #{flightNumber,jdbcType=VARCHAR},
      </if>
      <if test="handler != null">
        HANDLER = #{handler,jdbcType=VARCHAR},
      </if>
      <if test="oriSacId != null">
        ORI_SAC_ID = #{oriSacId,jdbcType=VARCHAR},
      </if>
      <if test="eaaTotalEawbs != null">
        EAA_TOTAL_EAWBS = #{eaaTotalEawbs,jdbcType=DECIMAL},
      </if>
      <if test="eaaTotalMawbs != null">
        EAA_TOTAL_MAWBS = #{eaaTotalMawbs,jdbcType=DECIMAL},
      </if>
      <if test="mawbPWeight != null">
        MAWB_P_WEIGHT = #{mawbPWeight,jdbcType=DECIMAL},
      </if>
      <if test="mawbCWeight != null">
        MAWB_C_WEIGHT = #{mawbCWeight,jdbcType=DECIMAL},
      </if>
      <if test="etdDate != null">
        ETD_DATE = #{etdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="eaaCreatetime != null">
        EAA_CREATETIME = #{eaaCreatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="eaaPushtime != null">
        EAA_PUSHTIME = #{eaaPushtime,jdbcType=TIMESTAMP},
      </if>
      <if test="destSacId != null">
        DEST_SAC_ID = #{destSacId,jdbcType=VARCHAR},
      </if>
      <if test="eaaHandletime != null">
        EAA_HANDLETIME = #{eaaHandletime,jdbcType=TIMESTAMP},
      </if>
      <if test="eaaStatus != null">
        EAA_STATUS = #{eaaStatus,jdbcType=VARCHAR},
      </if>
      <if test="eaaPushstatus != null">
        EAA_PUSHSTATUS = #{eaaPushstatus,jdbcType=VARCHAR},
      </if>
      <if test="transmodeid != null">
        TRANSMODEID = #{transmodeid,jdbcType=VARCHAR},
      </if>
      <if test="sacId != null">
        SAC_ID = #{sacId,jdbcType=VARCHAR},
      </if>
      <if test="transportType != null">
        TRANSPORT_TYPE = #{transportType,jdbcType=VARCHAR},
      </if>
      <if test="eaaRemark != null">
        EAA_REMARK = #{eaaRemark,jdbcType=VARCHAR},
      </if>
      <if test="eaaFlightType != null">
        EAA_FLIGHT_TYPE = #{eaaFlightType,jdbcType=VARCHAR},
      </if>
      <if test="etaDate != null">
        ETA_DATE = #{etaDate,jdbcType=TIMESTAMP},
      </if>
      <if test="mawbVolumn != null">
        MAWB_VOLUMN = #{mawbVolumn,jdbcType=DECIMAL},
      </if>
      <if test="mawbUldtype != null">
        MAWB_ULDTYPE = #{mawbUldtype,jdbcType=VARCHAR},
      </if>
      <if test="exportclearancecity != null">
        EXPORTCLEARANCECITY = #{exportclearancecity,jdbcType=VARCHAR},
      </if>
      <if test="agent != null">
        AGENT = #{agent,jdbcType=VARCHAR},
      </if>
      <if test="foreignclearancesacId != null">
        FOREIGNCLEARANCESAC_ID = #{foreignclearancesacId,jdbcType=VARCHAR},
      </if>
      <if test="eaaInsertpushtime != null">
        EAA_INSERTPUSHTIME = #{eaaInsertpushtime,jdbcType=TIMESTAMP},
      </if>
      <if test="eaaInsertpushstatus != null">
        EAA_INSERTPUSHSTATUS = #{eaaInsertpushstatus,jdbcType=VARCHAR},
      </if>
      <if test="eaaClearancetype != null">
        EAA_CLEARANCETYPE = #{eaaClearancetype,jdbcType=VARCHAR},
      </if>
      <if test="firstFlightNumber != null">
        FIRST_FLIGHT_NUMBER = #{firstFlightNumber,jdbcType=VARCHAR},
      </if>
      <if test="firstOriSacId != null">
        FIRST_ORI_SAC_ID = #{firstOriSacId,jdbcType=VARCHAR},
      </if>
      <if test="firstDestSacId != null">
        FIRST_DEST_SAC_ID = #{firstDestSacId,jdbcType=VARCHAR},
      </if>
      <if test="firstEtd != null">
        FIRST_ETD = #{firstEtd,jdbcType=TIMESTAMP},
      </if>
      <if test="firstEta != null">
        FIRST_ETA = #{firstEta,jdbcType=TIMESTAMP},
      </if>
      <if test="secondFlightNumber != null">
        SECOND_FLIGHT_NUMBER = #{secondFlightNumber,jdbcType=VARCHAR},
      </if>
      <if test="secondOriSacId != null">
        SECOND_ORI_SAC_ID = #{secondOriSacId,jdbcType=VARCHAR},
      </if>
      <if test="secondDestSacId != null">
        SECOND_DEST_SAC_ID = #{secondDestSacId,jdbcType=VARCHAR},
      </if>
      <if test="secondEtd != null">
        SECOND_ETD = #{secondEtd,jdbcType=TIMESTAMP},
      </if>
      <if test="secondEta != null">
        SECOND_ETA = #{secondEta,jdbcType=TIMESTAMP},
      </if>
      <if test="thirdFlightNumber != null">
        THIRD_FLIGHT_NUMBER = #{thirdFlightNumber,jdbcType=VARCHAR},
      </if>
      <if test="thirdOriSacId != null">
        THIRD_ORI_SAC_ID = #{thirdOriSacId,jdbcType=VARCHAR},
      </if>
      <if test="thirdDestSacId != null">
        THIRD_DEST_SAC_ID = #{thirdDestSacId,jdbcType=VARCHAR},
      </if>
      <if test="thirdEtd != null">
        THIRD_ETD = #{thirdEtd,jdbcType=TIMESTAMP},
      </if>
      <if test="thirdEta != null">
        THIRD_ETA = #{thirdEta,jdbcType=TIMESTAMP},
      </if>
      <if test="aircrafttype != null">
        AIRCRAFTTYPE = #{aircrafttype,jdbcType=VARCHAR},
      </if>
      <if test="freightagent != null">
        FREIGHTAGENT = #{freightagent,jdbcType=VARCHAR},
      </if>
      <if test="terminalcode != null">
        TERMINALCODE = #{terminalcode,jdbcType=VARCHAR},
      </if>
      <if test="eaaAsstime != null">
        EAA_ASSTIME = #{eaaAsstime,jdbcType=TIMESTAMP},
      </if>
      <if test="airlinecode != null">
        AIRLINECODE = #{airlinecode,jdbcType=VARCHAR},
      </if>
      <if test="eaaComment != null">
        EAA_COMMENT = #{eaaComment,jdbcType=VARCHAR},
      </if>
      <if test="std != null">
        STD = #{std,jdbcType=TIMESTAMP},
      </if>
      <if test="soCode != null">
        SO_CODE = #{soCode,jdbcType=VARCHAR},
      </if>
      <if test="iscndirect != null">
        ISCNDIRECT = #{iscndirect,jdbcType=VARCHAR},
      </if>
    </set>
    where EAA_SYSCODE = #{eaaSyscode,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.ExpressAssignmentActual">
    update EXPRESSASSIGNMENTACTUAL
    set EAA_CODE = #{eaaCode,jdbcType=VARCHAR},
        FLIGHT_NUMBER = #{flightNumber,jdbcType=VARCHAR},
        HANDLER = #{handler,jdbcType=VARCHAR},
        ORI_SAC_ID = #{oriSacId,jdbcType=VARCHAR},
        EAA_TOTAL_EAWBS = #{eaaTotalEawbs,jdbcType=DECIMAL},
        EAA_TOTAL_MAWBS = #{eaaTotalMawbs,jdbcType=DECIMAL},
        MAWB_P_WEIGHT = #{mawbPWeight,jdbcType=DECIMAL},
        MAWB_C_WEIGHT = #{mawbCWeight,jdbcType=DECIMAL},
        ETD_DATE = #{etdDate,jdbcType=TIMESTAMP},
        EAA_CREATETIME = #{eaaCreatetime,jdbcType=TIMESTAMP},
        EAA_PUSHTIME = #{eaaPushtime,jdbcType=TIMESTAMP},
        DEST_SAC_ID = #{destSacId,jdbcType=VARCHAR},
        EAA_HANDLETIME = #{eaaHandletime,jdbcType=TIMESTAMP},
        EAA_STATUS = #{eaaStatus,jdbcType=VARCHAR},
        EAA_PUSHSTATUS = #{eaaPushstatus,jdbcType=VARCHAR},
        TRANSMODEID = #{transmodeid,jdbcType=VARCHAR},
        SAC_ID = #{sacId,jdbcType=VARCHAR},
        TRANSPORT_TYPE = #{transportType,jdbcType=VARCHAR},
        EAA_REMARK = #{eaaRemark,jdbcType=VARCHAR},
        EAA_FLIGHT_TYPE = #{eaaFlightType,jdbcType=VARCHAR},
        ETA_DATE = #{etaDate,jdbcType=TIMESTAMP},
        MAWB_VOLUMN = #{mawbVolumn,jdbcType=DECIMAL},
        MAWB_ULDTYPE = #{mawbUldtype,jdbcType=VARCHAR},
        EXPORTCLEARANCECITY = #{exportclearancecity,jdbcType=VARCHAR},
        AGENT = #{agent,jdbcType=VARCHAR},
        FOREIGNCLEARANCESAC_ID = #{foreignclearancesacId,jdbcType=VARCHAR},
        EAA_INSERTPUSHTIME = #{eaaInsertpushtime,jdbcType=TIMESTAMP},
        EAA_INSERTPUSHSTATUS = #{eaaInsertpushstatus,jdbcType=VARCHAR},
        EAA_CLEARANCETYPE = #{eaaClearancetype,jdbcType=VARCHAR},
        FIRST_FLIGHT_NUMBER = #{firstFlightNumber,jdbcType=VARCHAR},
        FIRST_ORI_SAC_ID = #{firstOriSacId,jdbcType=VARCHAR},
        FIRST_DEST_SAC_ID = #{firstDestSacId,jdbcType=VARCHAR},
        FIRST_ETD = #{firstEtd,jdbcType=TIMESTAMP},
        FIRST_ETA = #{firstEta,jdbcType=TIMESTAMP},
        SECOND_FLIGHT_NUMBER = #{secondFlightNumber,jdbcType=VARCHAR},
        SECOND_ORI_SAC_ID = #{secondOriSacId,jdbcType=VARCHAR},
        SECOND_DEST_SAC_ID = #{secondDestSacId,jdbcType=VARCHAR},
        SECOND_ETD = #{secondEtd,jdbcType=TIMESTAMP},
        SECOND_ETA = #{secondEta,jdbcType=TIMESTAMP},
        THIRD_FLIGHT_NUMBER = #{thirdFlightNumber,jdbcType=VARCHAR},
        THIRD_ORI_SAC_ID = #{thirdOriSacId,jdbcType=VARCHAR},
        THIRD_DEST_SAC_ID = #{thirdDestSacId,jdbcType=VARCHAR},
        THIRD_ETD = #{thirdEtd,jdbcType=TIMESTAMP},
        THIRD_ETA = #{thirdEta,jdbcType=TIMESTAMP},
        AIRCRAFTTYPE = #{aircrafttype,jdbcType=VARCHAR},
        FREIGHTAGENT = #{freightagent,jdbcType=VARCHAR},
        TERMINALCODE = #{terminalcode,jdbcType=VARCHAR},
        EAA_ASSTIME = #{eaaAsstime,jdbcType=TIMESTAMP},
        AIRLINECODE = #{airlinecode,jdbcType=VARCHAR},
        EAA_COMMENT = #{eaaComment,jdbcType=VARCHAR},
        STD = #{std,jdbcType=TIMESTAMP},
        SO_CODE = #{soCode,jdbcType=VARCHAR},
        ISCNDIRECT = #{iscndirect,jdbcType=VARCHAR}
    where EAA_SYSCODE = #{eaaSyscode,jdbcType=DECIMAL}
  </update>

  <select id="selectByEaCode" resultType="com.sinoair.billing.domain.model.billing.ExpressAssignmentActual" parameterType="com.sinoair.billing.domain.model.billing.ExpressAssignment">
    SELECT * FROM EXPRESSASSIGNMENTACTUAL EA WHERE EA.EAA_CODE = #{eaCode,jdbcType=VARCHAR}
  </select>

  <select id="selectByDate" resultType="com.sinoair.billing.domain.model.billing.ExpressAssignmentActual" parameterType="com.sinoair.billing.domain.vo.query.CommonQuery">
    SELECT * FROM EXPRESSASSIGNMENTACTUAL EA WHERE EAA_HANDLETIME >= to_date(${strStartDate},'yyyymmdd')
                                         and EAA_HANDLETIME &lt; to_date(${strEndDate},'yyyymmdd')
  </select>

  <insert id="insertBatch" parameterType="java.util.List">
    insert into EXPRESSASSIGNMENTACTUAL (EAA_SYSCODE, EAA_CODE, FLIGHT_NUMBER,
                                         HANDLER, ORI_SAC_ID, EAA_TOTAL_EAWBS,
                                         EAA_TOTAL_MAWBS, MAWB_P_WEIGHT, MAWB_C_WEIGHT,
                                         ETD_DATE, EAA_CREATETIME, EAA_PUSHTIME,
                                         DEST_SAC_ID, EAA_HANDLETIME, EAA_STATUS,
                                         EAA_PUSHSTATUS, TRANSMODEID, SAC_ID,
                                         TRANSPORT_TYPE, EAA_REMARK, EAA_FLIGHT_TYPE,
                                         ETA_DATE, MAWB_VOLUMN, MAWB_ULDTYPE,
                                         EXPORTCLEARANCECITY, AGENT, FOREIGNCLEARANCESAC_ID,
                                         EAA_INSERTPUSHTIME, EAA_INSERTPUSHSTATUS,
                                         EAA_CLEARANCETYPE, FIRST_FLIGHT_NUMBER, FIRST_ORI_SAC_ID,
                                         FIRST_DEST_SAC_ID, FIRST_ETD, FIRST_ETA,
                                         SECOND_FLIGHT_NUMBER, SECOND_ORI_SAC_ID, SECOND_DEST_SAC_ID,
                                         SECOND_ETD, SECOND_ETA, THIRD_FLIGHT_NUMBER,
                                         THIRD_ORI_SAC_ID, THIRD_DEST_SAC_ID, THIRD_ETD,
                                         THIRD_ETA, AIRCRAFTTYPE, FREIGHTAGENT,
                                         TERMINALCODE, EAA_ASSTIME, AIRLINECODE,
                                         EAA_COMMENT, STD, SO_CODE,
                                         ISCNDIRECT)
    <foreach collection="list" item="item" index="index" separator="UNION ALL">
      select #{item.eaaSyscode,jdbcType=DECIMAL}, #{item.eaaCode,jdbcType=VARCHAR}, #{item.flightNumber,jdbcType=VARCHAR},
      #{item.handler,jdbcType=VARCHAR}, #{item.oriSacId,jdbcType=VARCHAR}, #{item.eaaTotalEawbs,jdbcType=DECIMAL},
      #{item.eaaTotalMawbs,jdbcType=DECIMAL}, #{item.mawbPWeight,jdbcType=DECIMAL}, #{item.mawbCWeight,jdbcType=DECIMAL},
      #{item.etdDate,jdbcType=TIMESTAMP}, #{item.eaaCreatetime,jdbcType=TIMESTAMP}, #{item.eaaPushtime,jdbcType=TIMESTAMP},
      #{item.destSacId,jdbcType=VARCHAR}, #{item.eaaHandletime,jdbcType=TIMESTAMP}, #{item.eaaStatus,jdbcType=VARCHAR},
      #{item.eaaPushstatus,jdbcType=VARCHAR}, #{item.transmodeid,jdbcType=VARCHAR}, #{item.sacId,jdbcType=VARCHAR},
      #{item.transportType,jdbcType=VARCHAR}, #{item.eaaRemark,jdbcType=VARCHAR}, #{item.eaaFlightType,jdbcType=VARCHAR},
      #{item.etaDate,jdbcType=TIMESTAMP}, #{item.mawbVolumn,jdbcType=DECIMAL}, #{item.mawbUldtype,jdbcType=VARCHAR},
      #{item.exportclearancecity,jdbcType=VARCHAR}, #{item.agent,jdbcType=VARCHAR}, #{item.foreignclearancesacId,jdbcType=VARCHAR},
      #{item.eaaInsertpushtime,jdbcType=TIMESTAMP}, #{item.eaaInsertpushstatus,jdbcType=VARCHAR},
      #{item.eaaClearancetype,jdbcType=VARCHAR}, #{item.firstFlightNumber,jdbcType=VARCHAR}, #{item.firstOriSacId,jdbcType=VARCHAR},
      #{item.firstDestSacId,jdbcType=VARCHAR}, #{item.firstEtd,jdbcType=TIMESTAMP}, #{item.firstEta,jdbcType=TIMESTAMP},
      #{item.secondFlightNumber,jdbcType=VARCHAR}, #{item.secondOriSacId,jdbcType=VARCHAR}, #{item.secondDestSacId,jdbcType=VARCHAR},
      #{item.secondEtd,jdbcType=TIMESTAMP}, #{item.secondEta,jdbcType=TIMESTAMP}, #{item.thirdFlightNumber,jdbcType=VARCHAR},
      #{item.thirdOriSacId,jdbcType=VARCHAR}, #{item.thirdDestSacId,jdbcType=VARCHAR}, #{item.thirdEtd,jdbcType=TIMESTAMP},
      #{item.thirdEta,jdbcType=TIMESTAMP}, #{item.aircrafttype,jdbcType=VARCHAR}, #{item.freightagent,jdbcType=VARCHAR},
      #{item.terminalcode,jdbcType=VARCHAR}, #{item.eaaAsstime,jdbcType=TIMESTAMP}, #{item.airlinecode,jdbcType=VARCHAR},
      #{item.eaaComment,jdbcType=VARCHAR}, #{item.std,jdbcType=TIMESTAMP}, #{item.soCode,jdbcType=VARCHAR},
      #{item.iscndirect,jdbcType=VARCHAR} from dual
    </foreach>

  </insert>

  <update id="updateBatch" parameterType="java.util.List">
    <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
      update EXPRESSASSIGNMENTACTUAL
      <set>
        <if test="item.eaaCode != null">
          EAA_CODE = #{item.eaaCode,jdbcType=VARCHAR},
        </if>
        <if test="item.flightNumber != null">
          FLIGHT_NUMBER = #{item.flightNumber,jdbcType=VARCHAR},
        </if>
        <if test="item.handler != null">
          HANDLER = #{item.handler,jdbcType=VARCHAR},
        </if>
        <if test="item.oriSacId != null">
          ORI_SAC_ID = #{item.oriSacId,jdbcType=VARCHAR},
        </if>
        <if test="item.eaaTotalEawbs != null">
          EAA_TOTAL_EAWBS = #{item.eaaTotalEawbs,jdbcType=DECIMAL},
        </if>
        <if test="item.eaaTotalMawbs != null">
          EAA_TOTAL_MAWBS = #{item.eaaTotalMawbs,jdbcType=DECIMAL},
        </if>
        <if test="item.mawbPWeight != null">
          MAWB_P_WEIGHT = #{item.mawbPWeight,jdbcType=DECIMAL},
        </if>
        <if test="item.mawbCWeight != null">
          MAWB_C_WEIGHT = #{item.mawbCWeight,jdbcType=DECIMAL},
        </if>
        <if test="item.etdDate != null">
          ETD_DATE = #{item.etdDate,jdbcType=TIMESTAMP},
        </if>
        <if test="item.eaaCreatetime != null">
          EAA_CREATETIME = #{item.eaaCreatetime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.eaaPushtime != null">
          EAA_PUSHTIME = #{item.eaaPushtime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.destSacId != null">
          DEST_SAC_ID = #{item.destSacId,jdbcType=VARCHAR},
        </if>
        <if test="item.eaaHandletime != null">
          EAA_HANDLETIME = #{item.eaaHandletime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.eaaStatus != null">
          EAA_STATUS = #{item.eaaStatus,jdbcType=VARCHAR},
        </if>
        <if test="item.eaaPushstatus != null">
          EAA_PUSHSTATUS = #{item.eaaPushstatus,jdbcType=VARCHAR},
        </if>
        <if test="item.transmodeid != null">
          TRANSMODEID = #{item.transmodeid,jdbcType=VARCHAR},
        </if>
        <if test="item.sacId != null">
          SAC_ID = #{item.sacId,jdbcType=VARCHAR},
        </if>
        <if test="item.transportType != null">
          TRANSPORT_TYPE = #{item.transportType,jdbcType=VARCHAR},
        </if>
        <if test="item.eaaRemark != null">
          EAA_REMARK = #{item.eaaRemark,jdbcType=VARCHAR},
        </if>
        <if test="item.eaaFlightType != null">
          EAA_FLIGHT_TYPE = #{item.eaaFlightType,jdbcType=VARCHAR},
        </if>
        <if test="item.etaDate != null">
          ETA_DATE = #{item.etaDate,jdbcType=TIMESTAMP},
        </if>
        <if test="item.mawbVolumn != null">
          MAWB_VOLUMN = #{item.mawbVolumn,jdbcType=DECIMAL},
        </if>
        <if test="item.mawbUldtype != null">
          MAWB_ULDTYPE = #{item.mawbUldtype,jdbcType=VARCHAR},
        </if>
        <if test="item.exportclearancecity != null">
          EXPORTCLEARANCECITY = #{item.exportclearancecity,jdbcType=VARCHAR},
        </if>
        <if test="item.agent != null">
          AGENT = #{item.agent,jdbcType=VARCHAR},
        </if>
        <if test="item.foreignclearancesacId != null">
          FOREIGNCLEARANCESAC_ID = #{item.foreignclearancesacId,jdbcType=VARCHAR},
        </if>
        <if test="item.eaaInsertpushtime != null">
          EAA_INSERTPUSHTIME = #{item.eaaInsertpushtime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.eaaInsertpushstatus != null">
          EAA_INSERTPUSHSTATUS = #{item.eaaInsertpushstatus,jdbcType=VARCHAR},
        </if>
        <if test="item.eaaClearancetype != null">
          EAA_CLEARANCETYPE = #{item.eaaClearancetype,jdbcType=VARCHAR},
        </if>
        <if test="item.firstFlightNumber != null">
          FIRST_FLIGHT_NUMBER = #{item.firstFlightNumber,jdbcType=VARCHAR},
        </if>
        <if test="item.firstOriSacId != null">
          FIRST_ORI_SAC_ID = #{item.firstOriSacId,jdbcType=VARCHAR},
        </if>
        <if test="item.firstDestSacId != null">
          FIRST_DEST_SAC_ID = #{item.firstDestSacId,jdbcType=VARCHAR},
        </if>
        <if test="item.firstEtd != null">
          FIRST_ETD = #{item.firstEtd,jdbcType=TIMESTAMP},
        </if>
        <if test="item.firstEta != null">
          FIRST_ETA = #{item.firstEta,jdbcType=TIMESTAMP},
        </if>
        <if test="item.secondFlightNumber != null">
          SECOND_FLIGHT_NUMBER = #{item.secondFlightNumber,jdbcType=VARCHAR},
        </if>
        <if test="item.secondOriSacId != null">
          SECOND_ORI_SAC_ID = #{item.secondOriSacId,jdbcType=VARCHAR},
        </if>
        <if test="item.secondDestSacId != null">
          SECOND_DEST_SAC_ID = #{item.secondDestSacId,jdbcType=VARCHAR},
        </if>
        <if test="item.secondEtd != null">
          SECOND_ETD = #{item.secondEtd,jdbcType=TIMESTAMP},
        </if>
        <if test="item.secondEta != null">
          SECOND_ETA = #{item.secondEta,jdbcType=TIMESTAMP},
        </if>
        <if test="item.thirdFlightNumber != null">
          THIRD_FLIGHT_NUMBER = #{item.thirdFlightNumber,jdbcType=VARCHAR},
        </if>
        <if test="item.thirdOriSacId != null">
          THIRD_ORI_SAC_ID = #{item.thirdOriSacId,jdbcType=VARCHAR},
        </if>
        <if test="item.thirdDestSacId != null">
          THIRD_DEST_SAC_ID = #{item.thirdDestSacId,jdbcType=VARCHAR},
        </if>
        <if test="item.thirdEtd != null">
          THIRD_ETD = #{item.thirdEtd,jdbcType=TIMESTAMP},
        </if>
        <if test="item.thirdEta != null">
          THIRD_ETA = #{item.thirdEta,jdbcType=TIMESTAMP},
        </if>
        <if test="item.aircrafttype != null">
          AIRCRAFTTYPE = #{item.aircrafttype,jdbcType=VARCHAR},
        </if>
        <if test="item.freightagent != null">
          FREIGHTAGENT = #{item.freightagent,jdbcType=VARCHAR},
        </if>
        <if test="item.terminalcode != null">
          TERMINALCODE = #{item.terminalcode,jdbcType=VARCHAR},
        </if>
        <if test="item.eaaAsstime != null">
          EAA_ASSTIME = #{item.eaaAsstime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.airlinecode != null">
          AIRLINECODE = #{item.airlinecode,jdbcType=VARCHAR},
        </if>
        <if test="item.eaaComment != null">
          EAA_COMMENT = #{item.eaaComment,jdbcType=VARCHAR},
        </if>
        <if test="item.std != null">
          STD = #{item.std,jdbcType=TIMESTAMP},
        </if>
        <if test="item.soCode != null">
          SO_CODE = #{item.soCode,jdbcType=VARCHAR},
        </if>
        <if test="item.iscndirect != null">
          ISCNDIRECT = #{item.iscndirect,jdbcType=VARCHAR},
        </if>
      </set>
      where EAA_SYSCODE = #{item.eaaSyscode,jdbcType=DECIMAL}
    </foreach>
  </update>
</mapper>