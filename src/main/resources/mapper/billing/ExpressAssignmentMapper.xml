<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.billing.dao.billing.ExpressAssignmentMapper">
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.ExpressAssignment">
    <id column="EA_SYSCODE" jdbcType="DECIMAL" property="eaSyscode" />
    <result column="EA_CODE" jdbcType="VARCHAR" property="eaCode" />
    <result column="FLIGHT_NUMBER" jdbcType="VARCHAR" property="flightNumber" />
    <result column="ORI_SAC_ID" jdbcType="VARCHAR" property="oriSacId" />
    <result column="EA_TOTAL_EAWBS" jdbcType="DECIMAL" property="eaTotalEawbs" />
    <result column="EA_TOTAL_MAWBS" jdbcType="DECIMAL" property="eaTotalMawbs" />
    <result column="MAWB_P_WEIGHT" jdbcType="DECIMAL" property="mawbPWeight" />
    <result column="MAWB_C_WEIGHT" jdbcType="DECIMAL" property="mawbCWeight" />
    <result column="FLIGHT_DATE" jdbcType="TIMESTAMP" property="flightDate" />
    <result column="ETA" jdbcType="TIMESTAMP" property="eta" />
    <result column="DEST_SAC_ID" jdbcType="VARCHAR" property="destSacId" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="EA_HANDLETIME" jdbcType="TIMESTAMP" property="eaHandletime" />
    <result column="EA_STATUS" jdbcType="VARCHAR" property="eaStatus" />
    <result column="TRANSMODEID" jdbcType="VARCHAR" property="transmodeid" />
    <result column="SAC_ID" jdbcType="VARCHAR" property="sacId" />
    <result column="EAWB_IETYPE" jdbcType="VARCHAR" property="eawbIetype" />
  </resultMap>
  <sql id="Base_Column_List">
    EA_SYSCODE, EA_CODE, FLIGHT_NUMBER, ORI_SAC_ID, EA_TOTAL_EAWBS, EA_TOTAL_MAWBS, MAWB_P_WEIGHT, 
    MAWB_C_WEIGHT, FLIGHT_DATE, ETA, DEST_SAC_ID, REMARK, EA_HANDLETIME, EA_STATUS, TRANSMODEID, 
    SAC_ID
  </sql>

  <!-- 序列 -->
  <sql id='TABLE_SEQUENCE'>SEQ_EXPRESSASSIGNMENT.NEXTVAL</sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Short" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from EXPRESSASSIGNMENT
    where EA_SYSCODE = #{eaSyscode,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Short">
    delete from EXPRESSASSIGNMENT
    where EA_SYSCODE = #{eaSyscode,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.ExpressAssignment">
    <selectKey keyProperty="eaSyscode" resultType="int" order="BEFORE">
      select <include refid="TABLE_SEQUENCE" /> from dual
    </selectKey>
    insert into EXPRESSASSIGNMENT (EA_SYSCODE, EA_CODE, FLIGHT_NUMBER, 
      ORI_SAC_ID, EA_TOTAL_EAWBS, EA_TOTAL_MAWBS, 
      MAWB_P_WEIGHT, MAWB_C_WEIGHT, FLIGHT_DATE, 
      ETA, DEST_SAC_ID, REMARK, 
      EA_HANDLETIME, EA_STATUS, TRANSMODEID, 
      SAC_ID,EAWB_IETYPE)
    values (#{eaSyscode,jdbcType=DECIMAL}, #{eaCode,jdbcType=VARCHAR}, #{flightNumber,jdbcType=VARCHAR}, 
      #{oriSacId,jdbcType=VARCHAR}, #{eaTotalEawbs,jdbcType=DECIMAL}, #{eaTotalMawbs,jdbcType=DECIMAL}, 
      #{mawbPWeight,jdbcType=DECIMAL}, #{mawbCWeight,jdbcType=DECIMAL}, #{flightDate,jdbcType=TIMESTAMP}, 
      #{eta,jdbcType=TIMESTAMP}, #{destSacId,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{eaHandletime,jdbcType=TIMESTAMP}, #{eaStatus,jdbcType=VARCHAR}, #{transmodeid,jdbcType=VARCHAR}, 
      #{sacId,jdbcType=VARCHAR},#{eawbIetype,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.ExpressAssignment">
    <selectKey keyProperty="eaSyscode" resultType="int" order="BEFORE">
      select <include refid="TABLE_SEQUENCE" /> from dual
    </selectKey>
    insert into EXPRESSASSIGNMENT
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="eaSyscode != null">
        EA_SYSCODE,
      </if>
      <if test="eaCode != null">
        EA_CODE,
      </if>
      <if test="flightNumber != null">
        FLIGHT_NUMBER,
      </if>
      <if test="oriSacId != null">
        ORI_SAC_ID,
      </if>
      <if test="eaTotalEawbs != null">
        EA_TOTAL_EAWBS,
      </if>
      <if test="eaTotalMawbs != null">
        EA_TOTAL_MAWBS,
      </if>
      <if test="mawbPWeight != null">
        MAWB_P_WEIGHT,
      </if>
      <if test="mawbCWeight != null">
        MAWB_C_WEIGHT,
      </if>
      <if test="flightDate != null">
        FLIGHT_DATE,
      </if>
      <if test="eta != null">
        ETA,
      </if>
      <if test="destSacId != null">
        DEST_SAC_ID,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="eaHandletime != null">
        EA_HANDLETIME,
      </if>
      <if test="eaStatus != null">
        EA_STATUS,
      </if>
      <if test="transmodeid != null">
        TRANSMODEID,
      </if>
      <if test="sacId != null">
        SAC_ID,
      </if>
      <if test="eawbIetype != null">
        EAWB_IETYPE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="eaSyscode != null">
        #{eaSyscode,jdbcType=DECIMAL},
      </if>
      <if test="eaCode != null">
        #{eaCode,jdbcType=VARCHAR},
      </if>
      <if test="flightNumber != null">
        #{flightNumber,jdbcType=VARCHAR},
      </if>
      <if test="oriSacId != null">
        #{oriSacId,jdbcType=VARCHAR},
      </if>
      <if test="eaTotalEawbs != null">
        #{eaTotalEawbs,jdbcType=DECIMAL},
      </if>
      <if test="eaTotalMawbs != null">
        #{eaTotalMawbs,jdbcType=DECIMAL},
      </if>
      <if test="mawbPWeight != null">
        #{mawbPWeight,jdbcType=DECIMAL},
      </if>
      <if test="mawbCWeight != null">
        #{mawbCWeight,jdbcType=DECIMAL},
      </if>
      <if test="flightDate != null">
        #{flightDate,jdbcType=TIMESTAMP},
      </if>
      <if test="eta != null">
        #{eta,jdbcType=TIMESTAMP},
      </if>
      <if test="destSacId != null">
        #{destSacId,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="eaHandletime != null">
        #{eaHandletime,jdbcType=TIMESTAMP},
      </if>
      <if test="eaStatus != null">
        #{eaStatus,jdbcType=VARCHAR},
      </if>
      <if test="transmodeid != null">
        #{transmodeid,jdbcType=VARCHAR},
      </if>
      <if test="sacId != null">
        #{sacId,jdbcType=VARCHAR},
      </if>
      <if test="eawbIetype != null">
        #{eawbIetype,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.ExpressAssignment">
    update EXPRESSASSIGNMENT
    <set>
      <if test="eaCode != null">
        EA_CODE = #{eaCode,jdbcType=VARCHAR},
      </if>
      <if test="flightNumber != null">
        FLIGHT_NUMBER = #{flightNumber,jdbcType=VARCHAR},
      </if>
      <if test="oriSacId != null">
        ORI_SAC_ID = #{oriSacId,jdbcType=VARCHAR},
      </if>
      <if test="eaTotalEawbs != null">
        EA_TOTAL_EAWBS = #{eaTotalEawbs,jdbcType=DECIMAL},
      </if>
      <if test="eaTotalMawbs != null">
        EA_TOTAL_MAWBS = #{eaTotalMawbs,jdbcType=DECIMAL},
      </if>
      <if test="mawbPWeight != null">
        MAWB_P_WEIGHT = #{mawbPWeight,jdbcType=DECIMAL},
      </if>
      <if test="mawbCWeight != null">
        MAWB_C_WEIGHT = #{mawbCWeight,jdbcType=DECIMAL},
      </if>
      <if test="flightDate != null">
        FLIGHT_DATE = #{flightDate,jdbcType=TIMESTAMP},
      </if>
      <if test="eta != null">
        ETA = #{eta,jdbcType=TIMESTAMP},
      </if>
      <if test="destSacId != null">
        DEST_SAC_ID = #{destSacId,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="eaHandletime != null">
        EA_HANDLETIME = #{eaHandletime,jdbcType=TIMESTAMP},
      </if>
      <if test="eaStatus != null">
        EA_STATUS = #{eaStatus,jdbcType=VARCHAR},
      </if>
      <if test="transmodeid != null">
        TRANSMODEID = #{transmodeid,jdbcType=VARCHAR},
      </if>
      <if test="sacId != null">
        SAC_ID = #{sacId,jdbcType=VARCHAR},
      </if>
    </set>
    where EA_SYSCODE = #{eaSyscode,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.ExpressAssignment">
    update EXPRESSASSIGNMENT
    set EA_CODE = #{eaCode,jdbcType=VARCHAR},
      FLIGHT_NUMBER = #{flightNumber,jdbcType=VARCHAR},
      ORI_SAC_ID = #{oriSacId,jdbcType=VARCHAR},
      EA_TOTAL_EAWBS = #{eaTotalEawbs,jdbcType=DECIMAL},
      EA_TOTAL_MAWBS = #{eaTotalMawbs,jdbcType=DECIMAL},
      MAWB_P_WEIGHT = #{mawbPWeight,jdbcType=DECIMAL},
      MAWB_C_WEIGHT = #{mawbCWeight,jdbcType=DECIMAL},
      FLIGHT_DATE = #{flightDate,jdbcType=TIMESTAMP},
      ETA = #{eta,jdbcType=TIMESTAMP},
      DEST_SAC_ID = #{destSacId,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR},
      EA_HANDLETIME = #{eaHandletime,jdbcType=TIMESTAMP},
      EA_STATUS = #{eaStatus,jdbcType=VARCHAR},
      TRANSMODEID = #{transmodeid,jdbcType=VARCHAR},
      SAC_ID = #{sacId,jdbcType=VARCHAR}
    where EA_SYSCODE = #{eaSyscode,jdbcType=DECIMAL}
  </update>

  <select id="selectByEaCode" resultType="com.sinoair.billing.domain.model.billing.ExpressAssignment" parameterType="com.sinoair.billing.domain.model.billing.ExpressAssignment">
     SELECT * FROM EXPRESSASSIGNMENT EA WHERE EA.EA_CODE = #{eaCode,jdbcType=VARCHAR}
  </select>

  <select id="selectByDate" resultType="com.sinoair.billing.domain.model.billing.ExpressAssignment" parameterType="com.sinoair.billing.domain.vo.query.CommonQuery">
    SELECT * FROM EXPRESSASSIGNMENT EA WHERE flight_date >= to_date(${strStartDate},'yyyymmdd')
      and flight_date &lt; to_date(${strEndDate},'yyyymmdd')
  </select>
</mapper>