<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.ExpressBusinessActivityMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.ExpressBusinessActivity" >
    <id column="EBA_SYSCODE" property="ebaSyscode" jdbcType="DECIMAL" />
    <result column="EAWB_PRINTCODE" property="eawbPrintcode" jdbcType="VARCHAR" />
    <result column="EAWB_SYSCODE" property="eawbSyscode" jdbcType="DECIMAL" />
    <result column="EAD_CODE" property="eadCode" jdbcType="VARCHAR" />
    <result column="EAST_CODE" property="eastCode" jdbcType="VARCHAR" />
    <result column="EBA_E_ID_HANDLER" property="ebaEIdHandler" jdbcType="DECIMAL" />
    <result column="EBA_HANDLETIME" property="ebaHandletime" jdbcType="TIMESTAMP" />
    <result column="EBA_REMARK" property="ebaRemark" jdbcType="VARCHAR" />
    <result column="SAC_ID" property="sacId" jdbcType="VARCHAR" />
    <result column="EBA_OCCURTIME" property="ebaOccurtime" jdbcType="TIMESTAMP" />
    <result column="EBA_SOURCE" property="ebaSource" jdbcType="VARCHAR" />
    <result column="EBA_OCCURPLACE" property="ebaOccurplace" jdbcType="VARCHAR" />
    <result column="FLAG" property="flag" jdbcType="VARCHAR"/>
    <result column="QA" property="qa" jdbcType="VARCHAR"/>
    <result column="UK_TRACK" property="ukTrack" jdbcType="VARCHAR"/>
    <result column="EAT_PARTNER_ACTIVITY_CODE" property="eatPartnerActivityCode" jdbcType="VARCHAR"/>
    <result column="EAT_PARTNER_ORIGIN" property="eatPartnerOrigin" jdbcType="VARCHAR"/>
    <result column="EAT_PARTNER_ID" property="eatPartnerId" jdbcType="VARCHAR"/>
    <result column="EBA_SAC_CODE" property="ebaSacCode" jdbcType="VARCHAR"/>
    <result column="QA_PUSHTIME" property="qaPushtime" jdbcType="TIMESTAMP"/>
    <result column="QA2" property="qa2" jdbcType="VARCHAR"/>
    <result column="QA2_PUSHTIME" property="qa2Pushtime" jdbcType="TIMESTAMP"/>
    <result column="QA3" property="qa3" jdbcType="VARCHAR"/>
    <result column="QA3_PUSHTIME" property="qa3Pushtime" jdbcType="TIMESTAMP"/>
    <result column="CEOS_EBA_SYSCODE" property="ceosEbaSyscode" jdbcType="DECIMAL"/>
    <result column="BILLING_SYSCODE" property="billingSyscode" jdbcType="DECIMAL"/>
    <result column="P_EADCODE" property="pEadcode" jdbcType="VARCHAR"/>
    <result column="p_EASTCODE" property="pEastcode" jdbcType="VARCHAR"/>

  </resultMap>
  <resultMap id="GenerateFeeMap" type="com.sinoair.billing.domain.vo.price.GenerateFee">
    <result column="planAmount" property="planAmount" jdbcType="DECIMAL" />
    <result column="formula" property="formula" jdbcType="VARCHAR"/>
    <result column="psdName" property="psdName" jdbcType="VARCHAR"/>
    <result column="eawb_printcode" property="eawbPrintcode" jdbcType="VARCHAR"/>
    <result column="pdName" property="pdName" jdbcType="VARCHAR"/>
    <result column="service_id" property="serviceId" jdbcType="DECIMAL"/>
    <result column="partition_code" property="partitionCode" jdbcType="VARCHAR"/>
    <result column="EAWB_SO_CODE" property="eawbSoCode" jdbcType="VARCHAR"/>
    <result column="soCode" property="soCode" jdbcType="VARCHAR"/>
    <result column="pdSyscode" property="pdSyscode" jdbcType="DECIMAL"/>
    <result column="ppId" property="ppId" jdbcType="DECIMAL"/>
    <result column="cId" property="cId" jdbcType="DECIMAL"/>
    <result column="psdId" property="psdId" jdbcType="DECIMAL"/>
    <result column="ctCode" property="ctCode" jdbcType="VARCHAR"/>
    <result column="pp_cate" property="ppCate" jdbcType="VARCHAR"/>
    <result column="EBA_OCCURTIME" property="ebaOccurtime" jdbcType="TIMESTAMP"/>
    <result column="EAWB_CHARGEABLEWEIGHT" property="eawbChargeableweight" jdbcType="DECIMAL"/>
    <result column="EAWB_OUTBOUND_SAC_ID" property="eawbOutboundSacId" jdbcType="VARCHAR"/>
    <result column="EAWB_REFERENCE1" property="eawbTrackingNo" jdbcType="VARCHAR"/>
    <result column="EAWB_REFERENCE2" property="eawbReference2" jdbcType="VARCHAR"/>
    <result column="MAWB_CODE" property="mawbCode" jdbcType="VARCHAR"/>
  </resultMap>
  <resultMap id="GenerateReceiptFeeMap" type="com.sinoair.billing.domain.vo.price.GenerateReceiptFee">
    <result column="planAmount" property="planAmount" jdbcType="DECIMAL" />
    <result column="formula" property="formula" jdbcType="VARCHAR"/>
    <result column="psdName" property="psdName" jdbcType="VARCHAR"/>
    <result column="eawb_printcode" property="eawbPrintcode" jdbcType="VARCHAR"/>
    <result column="pdName" property="pdName" jdbcType="VARCHAR"/>
    <result column="service_id" property="serviceId" jdbcType="DECIMAL"/>
    <result column="partition_code" property="partitionCode" jdbcType="VARCHAR"/>
    <result column="EAWB_SO_CODE" property="eawbSoCode" jdbcType="VARCHAR"/>
    <result column="soCode" property="soCode" jdbcType="VARCHAR"/>
    <result column="pdSyscode" property="pdSyscode" jdbcType="DECIMAL"/>
    <result column="cId" property="cId" jdbcType="DECIMAL"/>
    <result column="rpId" property="rpId" jdbcType="DECIMAL"/>
    <result column="ctCode" property="ctCode" jdbcType="VARCHAR"/>
    <result column="pp_cate" property="ppCate" jdbcType="VARCHAR"/>
    <result column="EBA_OCCURTIME" property="ebaOccurtime" jdbcType="TIMESTAMP"/>
    <result column="EAWB_CHARGEABLEWEIGHT" property="eawbChargeableweight" jdbcType="DECIMAL"/>
    <result column="EAWB_OUTBOUND_SAC_ID" property="eawbOutboundSacId" jdbcType="VARCHAR"/>
    <result column="EAWB_REFERENCE1" property="eawbTrackingNo" jdbcType="VARCHAR"/>
    <result column="EAWB_REFERENCE2" property="eawbReference2" jdbcType="VARCHAR"/>
    <result column="MAWB_CODE" property="mawbCode" jdbcType="VARCHAR"/>
    <result column="rpd_cate" property="rpdCate" jdbcType="VARCHAR"/>
    <result column="ptType" property="ptType" jdbcType="VARCHAR"/>
    <result column="EAWB_DESTINATION" property="eawbDestination" jdbcType="VARCHAR"/>
    <result column="EAWB_DESTCOUNTRY" property="eawbDestcountry" jdbcType="VARCHAR"/>
    <result column="EAWB_DEPARTCOUNTRY" property="eawbDepartcountry" jdbcType="VARCHAR"/>
    <result column="EAWB_DEPARTURE" property="eawbDeparture" jdbcType="VARCHAR"/>
    <result column="so_mode" property="soMode" jdbcType="VARCHAR"/>
    <result column="C_CODE" property="cCode" jdbcType="VARCHAR"/>

  </resultMap>
  <sql id="Base_Column_List" >
    EBA_SYSCODE, EAWB_PRINTCODE, EAWB_SYSCODE, EAD_CODE, EAST_CODE, EBA_E_ID_HANDLER, 
    EBA_HANDLETIME, EBA_REMARK, SAC_ID, EBA_OCCURTIME, EBA_SOURCE, EBA_OCCURPLACE, FLAG, 
    QA, UK_TRACK, EAT_PARTNER_ACTIVITY_CODE, EAT_PARTNER_ORIGIN, EAT_PARTNER_ID, EBA_SAC_CODE,
    QA_PUSHTIME,QA2,QA2_PUSHTIME,QA3,QA3_PUSHTIME,CEOS_EBA_SYSCODE
  </sql>

  <!-- 序列 -->
  <sql id='TABLE_SEQUENCE'>SEQ_EXPRESSBUSINESSACTIVITY.NEXTVAL</sql>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from EXPRESSBUSINESSACTIVITY
    where EBA_SYSCODE = #{ebaSyscode,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from EXPRESSBUSINESSACTIVITY
    where EBA_SYSCODE = #{ebaSyscode,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.ExpressBusinessActivity" >

    <selectKey keyProperty="ebaSyscode" resultType="int" order="BEFORE">
      select <include refid="TABLE_SEQUENCE" /> from dual
    </selectKey>

    insert into EXPRESSBUSINESSACTIVITY (EBA_SYSCODE, EAWB_PRINTCODE, EAWB_SYSCODE, 
      EAD_CODE, EAST_CODE, EBA_E_ID_HANDLER, 
      EBA_HANDLETIME, EBA_REMARK, SAC_ID, 
      EBA_OCCURTIME, EBA_SOURCE, EBA_OCCURPLACE, 
      FLAG, QA, UK_TRACK, 
      EAT_PARTNER_ACTIVITY_CODE, EAT_PARTNER_ORIGIN, 
      EAT_PARTNER_ID, EBA_SAC_CODE,QA_PUSHTIME,QA2,QA2_PUSHTIME,QA3,QA3_PUSHTIME)
    values (#{ebaSyscode,jdbcType=DECIMAL}, #{eawbPrintcode,jdbcType=VARCHAR}, #{eawbSyscode,jdbcType=DECIMAL}, 
      #{eadCode,jdbcType=VARCHAR}, #{eastCode,jdbcType=VARCHAR}, #{ebaEIdHandler,jdbcType=DECIMAL}, 
      #{ebaHandletime,jdbcType=TIMESTAMP}, #{ebaRemark,jdbcType=VARCHAR}, #{sacId,jdbcType=VARCHAR}, 
      #{ebaOccurtime,jdbcType=TIMESTAMP}, #{ebaSource,jdbcType=VARCHAR}, #{ebaOccurplace,jdbcType=VARCHAR}, 
      #{flag,jdbcType=VARCHAR}, #{qa,jdbcType=VARCHAR}, #{ukTrack,jdbcType=VARCHAR}, 
      #{eatPartnerActivityCode,jdbcType=VARCHAR}, #{eatPartnerOrigin,jdbcType=VARCHAR}, 
      #{eatPartnerId,jdbcType=VARCHAR}, #{ebaSacCode,jdbcType=VARCHAR},#{qaPushtime,jdbcType=TIMESTAMP},
      #{qa2,jdbcType=VARCHAR},#{qa2Pushtime,jdbcType=TIMESTAMP},
      #{qa3,jdbcType=VARCHAR},#{qa3Pushtime,jdbcType=TIMESTAMP}
    )
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.ExpressBusinessActivity" >

    <selectKey keyProperty="ebaSyscode" resultType="int" order="BEFORE">
      select <include refid="TABLE_SEQUENCE" /> from dual
    </selectKey>

    insert into EXPRESSBUSINESSACTIVITY
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="ebaSyscode != null" >
        EBA_SYSCODE,
      </if>
      <if test="eawbPrintcode != null" >
        EAWB_PRINTCODE,
      </if>
      <if test="eawbSyscode != null" >
        EAWB_SYSCODE,
      </if>
      <if test="eadCode != null" >
        EAD_CODE,
      </if>
      <if test="eastCode != null" >
        EAST_CODE,
      </if>
      <if test="ebaEIdHandler != null" >
        EBA_E_ID_HANDLER,
      </if>
      <if test="ebaHandletime != null" >
        EBA_HANDLETIME,
      </if>
      <if test="ebaRemark != null" >
        EBA_REMARK,
      </if>
      <if test="sacId != null" >
        SAC_ID,
      </if>
      <if test="ebaOccurtime != null" >
        EBA_OCCURTIME,
      </if>
      <if test="ebaSource != null" >
        EBA_SOURCE,
      </if>
      <if test="ebaOccurplace != null" >
        EBA_OCCURPLACE,
      </if>
      <if test="flag != null" >
        FLAG,
      </if>
      <if test="qa != null" >
        QA,
      </if>
      <if test="ukTrack != null" >
        UK_TRACK,
      </if>
      <if test="eatPartnerActivityCode != null" >
        EAT_PARTNER_ACTIVITY_CODE,
      </if>
      <if test="eatPartnerOrigin != null" >
        EAT_PARTNER_ORIGIN,
      </if>
      <if test="eatPartnerId != null" >
        EAT_PARTNER_ID,
      </if>
      <if test="ebaSacCode != null" >
        EBA_SAC_CODE,
      </if>
      <if test="qaPushtime != null" >
        QA_PUSHTIME,
      </if>
      <if test="qa2 != null" >
        QA2,
      </if>
      <if test="qa2Pushtime != null" >
        QA2_PUSHTIME,
      </if>
      <if test="qa3 != null" >
        QA3,
      </if>
      <if test="qa3Pushtime != null" >
        QA3_PUSHTIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="ebaSyscode != null" >
        #{ebaSyscode,jdbcType=DECIMAL},
      </if>
      <if test="eawbPrintcode != null" >
        #{eawbPrintcode,jdbcType=VARCHAR},
      </if>
      <if test="eawbSyscode != null" >
        #{eawbSyscode,jdbcType=DECIMAL},
      </if>
      <if test="eadCode != null" >
        #{eadCode,jdbcType=VARCHAR},
      </if>
      <if test="eastCode != null" >
        #{eastCode,jdbcType=VARCHAR},
      </if>
      <if test="ebaEIdHandler != null" >
        #{ebaEIdHandler,jdbcType=DECIMAL},
      </if>
      <if test="ebaHandletime != null" >
        #{ebaHandletime,jdbcType=TIMESTAMP},
      </if>
      <if test="ebaRemark != null" >
        #{ebaRemark,jdbcType=VARCHAR},
      </if>
      <if test="sacId != null" >
        #{sacId,jdbcType=VARCHAR},
      </if>
      <if test="ebaOccurtime != null" >
        #{ebaOccurtime,jdbcType=TIMESTAMP},
      </if>
      <if test="ebaSource != null" >
        #{ebaSource,jdbcType=VARCHAR},
      </if>
      <if test="ebaOccurplace != null" >
        #{ebaOccurplace,jdbcType=VARCHAR},
      </if>
      <if test="flag != null" >
        #{flag,jdbcType=VARCHAR},
      </if>
      <if test="qa != null" >
        #{qa,jdbcType=VARCHAR},
      </if>
      <if test="ukTrack != null" >
        #{ukTrack,jdbcType=VARCHAR},
      </if>
      <if test="eatPartnerActivityCode != null" >
        #{eatPartnerActivityCode,jdbcType=VARCHAR},
      </if>
      <if test="eatPartnerOrigin != null" >
        #{eatPartnerOrigin,jdbcType=VARCHAR},
      </if>
      <if test="eatPartnerId != null" >
        #{eatPartnerId,jdbcType=VARCHAR},
      </if>
      <if test="ebaSacCode != null" >
        #{ebaSacCode,jdbcType=VARCHAR},
      </if>,
      <if test="qaPushtime != null" >
        #{qaPushtime,jdbcType=TIMESTAMP},
      </if>
      <if test="qa2 != null" >
        #{qa2,jdbcType=VARCHAR},
      </if>
      <if test="qa2Pushtime != null" >
        #{qa2Pushtime,jdbcType=TIMESTAMP},
      </if>
      <if test="qa3 != null" >
        #{qa3,jdbcType=VARCHAR},
      </if>
      <if test="qa3Pushtime != null" >
        #{qa3Pushtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.ExpressBusinessActivity" >
    update EXPRESSBUSINESSACTIVITY
    <set >
      <if test="eawbPrintcode != null" >
        EAWB_PRINTCODE = #{eawbPrintcode,jdbcType=VARCHAR},
      </if>
      <if test="eawbSyscode != null" >
        EAWB_SYSCODE = #{eawbSyscode,jdbcType=DECIMAL},
      </if>
      <if test="eadCode != null" >
        EAD_CODE = #{eadCode,jdbcType=VARCHAR},
      </if>
      <if test="eastCode != null" >
        EAST_CODE = #{eastCode,jdbcType=VARCHAR},
      </if>
      <if test="ebaEIdHandler != null" >
        EBA_E_ID_HANDLER = #{ebaEIdHandler,jdbcType=DECIMAL},
      </if>
      <if test="ebaHandletime != null" >
        EBA_HANDLETIME = #{ebaHandletime,jdbcType=TIMESTAMP},
      </if>
      <if test="ebaRemark != null" >
        EBA_REMARK = #{ebaRemark,jdbcType=VARCHAR},
      </if>
      <if test="sacId != null" >
        SAC_ID = #{sacId,jdbcType=VARCHAR},
      </if>
      <if test="ebaOccurtime != null" >
        EBA_OCCURTIME = #{ebaOccurtime,jdbcType=TIMESTAMP},
      </if>
      <if test="ebaSource != null" >
        EBA_SOURCE = #{ebaSource,jdbcType=VARCHAR},
      </if>
      <if test="ebaOccurplace != null" >
        EBA_OCCURPLACE = #{ebaOccurplace,jdbcType=VARCHAR},
      </if>
      <if test="flag != null" >
        FLAG = #{flag,jdbcType=VARCHAR},
      </if>
      <if test="qa != null" >
        QA = #{qa,jdbcType=VARCHAR},
      </if>
      <if test="ukTrack != null" >
        UK_TRACK = #{ukTrack,jdbcType=VARCHAR},
      </if>
      <if test="eatPartnerActivityCode != null" >
        EAT_PARTNER_ACTIVITY_CODE = #{eatPartnerActivityCode,jdbcType=VARCHAR},
      </if>
      <if test="eatPartnerOrigin != null" >
        EAT_PARTNER_ORIGIN = #{eatPartnerOrigin,jdbcType=VARCHAR},
      </if>
      <if test="eatPartnerId != null" >
        EAT_PARTNER_ID = #{eatPartnerId,jdbcType=VARCHAR},
      </if>
      <if test="ebaSacCode != null" >
        EBA_SAC_CODE = #{ebaSacCode,jdbcType=VARCHAR},
      </if>
    </set>
    where EBA_SYSCODE = #{ebaSyscode,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.ExpressBusinessActivity" >
    update EXPRESSBUSINESSACTIVITY
    set EAWB_PRINTCODE = #{eawbPrintcode,jdbcType=VARCHAR},
      EAWB_SYSCODE = #{eawbSyscode,jdbcType=DECIMAL},
      EAD_CODE = #{eadCode,jdbcType=VARCHAR},
      EAST_CODE = #{eastCode,jdbcType=VARCHAR},
      EBA_E_ID_HANDLER = #{ebaEIdHandler,jdbcType=DECIMAL},
      EBA_HANDLETIME = #{ebaHandletime,jdbcType=TIMESTAMP},
      EBA_REMARK = #{ebaRemark,jdbcType=VARCHAR},
      SAC_ID = #{sacId,jdbcType=VARCHAR},
      EBA_OCCURTIME = #{ebaOccurtime,jdbcType=TIMESTAMP},
      EBA_SOURCE = #{ebaSource,jdbcType=VARCHAR},
      EBA_OCCURPLACE = #{ebaOccurplace,jdbcType=VARCHAR},
      FLAG = #{flag,jdbcType=VARCHAR},
      QA = #{qa,jdbcType=VARCHAR},
      UK_TRACK = #{ukTrack,jdbcType=VARCHAR},
      EAT_PARTNER_ACTIVITY_CODE = #{eatPartnerActivityCode,jdbcType=VARCHAR},
      EAT_PARTNER_ORIGIN = #{eatPartnerOrigin,jdbcType=VARCHAR},
      EAT_PARTNER_ID = #{eatPartnerId,jdbcType=VARCHAR},
      EBA_SAC_CODE = #{ebaSacCode,jdbcType=VARCHAR}
    where EBA_SYSCODE = #{ebaSyscode,jdbcType=DECIMAL}
  </update>


  <insert id="insertBatch" parameterType="java.util.ArrayList">
    insert into EXPRESSBUSINESSACTIVITY (EBA_SYSCODE, EAWB_PRINTCODE, EAWB_SYSCODE,
    EAD_CODE, EAST_CODE, EBA_E_ID_HANDLER,
    EBA_HANDLETIME, EBA_REMARK, SAC_ID,
    EBA_OCCURTIME, EBA_SOURCE, EBA_OCCURPLACE,
    FLAG, QA, UK_TRACK,
    EAT_PARTNER_ACTIVITY_CODE, EAT_PARTNER_ORIGIN,
    EAT_PARTNER_ID, EBA_SAC_CODE,QA_PUSHTIME,QA2,QA2_PUSHTIME,QA3,QA3_PUSHTIME)

    select SEQ_EXPRESSBUSINESSACTIVITY.NEXTVAL,cd.* from(
    <foreach collection="list" item="item" index="index" separator="UNION ALL">
      (select #{item.eawbPrintcode,jdbcType=VARCHAR},#{item.eawbSyscode,jdbcType=DECIMAL},
      #{item.eadCode,jdbcType=VARCHAR}, #{item.eastCode,jdbcType=VARCHAR}, #{item.ebaEIdHandler,jdbcType=DECIMAL},
      #{item.ebaHandletime,jdbcType=TIMESTAMP}, #{item.ebaRemark,jdbcType=VARCHAR}, #{item.sacId,jdbcType=VARCHAR},
      #{item.ebaOccurtime,jdbcType=TIMESTAMP}, #{item.ebaSource,jdbcType=VARCHAR},
      #{item.ebaOccurplace,jdbcType=VARCHAR},
      #{item.flag,jdbcType=VARCHAR}, #{item.qa,jdbcType=VARCHAR}, #{item.ukTrack,jdbcType=VARCHAR},
      #{item.eatPartnerActivityCode,jdbcType=VARCHAR}, #{item.eatPartnerOrigin,jdbcType=VARCHAR},
      #{item.eatPartnerId,jdbcType=VARCHAR}, #{item.ebaSacCode,jdbcType=VARCHAR},
      sysdate,
      #{item.qa2,jdbcType=VARCHAR},#{item.qa2Pushtime,jdbcType=TIMESTAMP},
      #{item.qa3,jdbcType=VARCHAR},#{item.qa3Pushtime,jdbcType=TIMESTAMP}
      from dual)
    </foreach>) cd
  </insert>

  <select id="selectSequenceNextVal" resultType="java.lang.Integer">
    select SEQ_EXPRESSBUSINESSACTIVITY.NEXTVAL from dual
  </select>

  <select id="countByAllCode" resultType="java.lang.Integer" parameterType="com.sinoair.billing.domain.vo.query.ActivityQuery">
  select count(0)
  from EXPRESSBUSINESSACTIVITY
  where eawb_printcode=#{eawbPrintcode,jdbcType=VARCHAR}
  <if test="eadCode != null" >
    and EAD_CODE=#{eadCode,jdbcType=VARCHAR}
  </if>
  <if test="eastCode != null" >
    and EAST_CODE=#{eastCode,jdbcType=VARCHAR}
  </if>
    <if test="isCheckDate != null" >
      <![CDATA[
      and eba_handletime < trunc(sysdate-1,'dd')
      ]]>
    </if>
</select>

  <select id="countByAllCode_N" resultType="java.lang.Integer" parameterType="com.sinoair.billing.domain.vo.query.ActivityQuery">
    select count(0)
    from EXPRESSBUSINESSACTIVITY_N
    where eawb_printcode=#{eawbPrintcode,jdbcType=VARCHAR}
    <if test="eadCode != null" >
      and EAD_CODE=#{eadCode,jdbcType=VARCHAR}
    </if>
    <if test="eastCode != null" >
      and EAST_CODE=#{eastCode,jdbcType=VARCHAR}
    </if>
  </select>

  <insert id="insertBatch_N" parameterType="java.util.ArrayList">
    insert into EXPRESSBUSINESSACTIVITY_N (EBA_SYSCODE, EAWB_PRINTCODE, EAWB_SYSCODE,
    EAD_CODE, EAST_CODE, EBA_E_ID_HANDLER,
    EBA_HANDLETIME, EBA_REMARK, SAC_ID,
    EBA_OCCURTIME, EBA_SOURCE, EBA_OCCURPLACE,
    FLAG, QA, UK_TRACK,
    EAT_PARTNER_ACTIVITY_CODE, EAT_PARTNER_ORIGIN,
    EAT_PARTNER_ID, EBA_SAC_CODE,QA_PUSHTIME,QA2,QA2_PUSHTIME,QA3,QA3_PUSHTIME,
    CEOS_EBA_SYSCODE)

    select SEQ_EXPRESSBUSINESSACTIVITY.NEXTVAL,cd.* from(
    <foreach collection="list" item="item" index="index" separator="UNION ALL">
      (select #{item.eawbPrintcode,jdbcType=VARCHAR},#{item.eawbSyscode,jdbcType=DECIMAL},
      #{item.eadCode,jdbcType=VARCHAR}, #{item.eastCode,jdbcType=VARCHAR}, #{item.ebaEIdHandler,jdbcType=DECIMAL},
      #{item.ebaHandletime,jdbcType=TIMESTAMP}, #{item.ebaRemark,jdbcType=VARCHAR}, #{item.sacId,jdbcType=VARCHAR},
      #{item.ebaOccurtime,jdbcType=TIMESTAMP}, #{item.ebaSource,jdbcType=VARCHAR},
      #{item.ebaOccurplace,jdbcType=VARCHAR},
      #{item.flag,jdbcType=VARCHAR}, #{item.qa,jdbcType=VARCHAR}, #{item.ukTrack,jdbcType=VARCHAR},
      #{item.eatPartnerActivityCode,jdbcType=VARCHAR}, #{item.eatPartnerOrigin,jdbcType=VARCHAR},
      #{item.eatPartnerId,jdbcType=VARCHAR}, #{item.ebaSacCode,jdbcType=VARCHAR},
      sysdate,
      #{item.qa2,jdbcType=VARCHAR},#{item.qa2Pushtime,jdbcType=TIMESTAMP},
      #{item.qa3,jdbcType=VARCHAR},#{item.qa3Pushtime,jdbcType=TIMESTAMP},
      #{item.ceosEbaSyscode,jdbcType=DECIMAL}
      from dual)
    </foreach>) cd
  </insert>

    <delete id="deleteEbaDuplicate_N" parameterType="com.sinoair.billing.domain.vo.query.CeosQuery" >
        delete from expressbusinessactivity_n a
         where (a.eawb_printcode, a.ead_code, a.east_code) in
               (select eawb_printcode, ead_code, east_code
                  from expressbusinessactivity_n
                 group by eawb_printcode, ead_code, east_code
                having count(*) > 1)
           and rowid not in (select min(rowid)
                               from expressbusinessactivity_n
                              group by eawb_printcode, ead_code, east_code
                             having count(*) > 1)
    </delete>

  <select id="selectTimeByAllCode" resultType="com.sinoair.billing.domain.model.billing.ExpressBusinessActivity" parameterType="com.sinoair.billing.domain.vo.query.ActivityQuery">
    select eba_handletime
    from EXPRESSBUSINESSACTIVITY
    where eawb_printcode=#{eawbPrintcode,jdbcType=VARCHAR}
      and EAD_CODE=#{eadCode,jdbcType=VARCHAR}
      and EAST_CODE=#{eastCode,jdbcType=VARCHAR}
  </select>

  <select id="selectMaxEbaCode" resultType="java.lang.Long">
    SELECT
      max(eba.eba_syscode)
    FROM EXPRESSBUSINESSACTIVITY eba
  </select>

  <select id="selectActivityList" resultType="com.sinoair.billing.domain.model.billing.ReceiptEawb"
          parameterType="com.sinoair.billing.domain.vo.check.EawbCheckVO">

    SELECT
    e.EAWB_PRINTCODE as eawbPrintcode,
    e.EAWB_SYSCODE as eawbSyscode,
    EAD_CODE as eadCode,
    EAST_CODE as eastCode,
    EBA_E_ID_HANDLER as ebaEIdHandler,
    EBA_HANDLETIME as ebaHandletime,
    EBA_REMARK as ebaRemark,
    e.sac_id,
    EBA_OCCURTIME as ebaOccurtime,
    EBA_SOURCE,
    EBA_OCCURPLACE,
    FLAG,
    QA,
    UK_TRACK,
    EAT_PARTNER_ACTIVITY_CODE,
    EAT_PARTNER_ID,
    EBA_SAC_CODE,
    (case when EAD_CODE='GFC_OUTBOUND' then QA_PUSHTIME
    else EBA_OCCURTIME end) as qaPushtime,
    QA2,
    QA2_PUSHTIME,
    QA3,
    QA3_PUSHTIME


    FROM EXPRESSBUSINESSACTIVITY E
    WHERE  1=1

    <if test="beginNo != null">
      and e.eba_syscode >=#{beginNo}
      <![CDATA[
            and e.eba_syscode <#{endNo}
            ]]>
    </if>
    <if test="eawbPrintcode != null and eawbPrintcode != ''">
      and e.EAWB_PRINTCODE=#{eawbPrintcode}
    </if>
  </select>

  <select id="selectMaxBillingSyscodeByBeginNo" resultType="java.math.BigDecimal">
    SELECT max(BILLING_SYSCODE)
    FROM (SELECT BILLING_SYSCODE
          FROM expressbusinessactivity eba,expressairwaybill eawb
          WHERE eawb.eawb_printcode=eba.eawb_printcode
     <![CDATA[
            and BILLING_SYSCODE > #{beginNo}
     ]]> ORDER BY BILLING_SYSCODE ASC)
    where
     <![CDATA[
      rownum <= 100000
    ]]>
  </select>

  <select id="selectsysCodeList" resultMap="BaseResultMap">
    select *
    from (select distinct EBA_SYSCODE, eba.EAWB_PRINTCODE,EAD_CODE,EAST_CODE, p.P_EADCODE, p.p_EASTCODE, EBA_OCCURTIME, BILLING_SYSCODE
          from expressbusinessactivity eba,expressairwaybill eawb,product p
          where eawb.eawb_printcode=eba.eawb_printcode and eawb.EAWB_SERVICETYPE_ORIGINAL=p.P_SERVICETYPE_ORIGINAL
     <![CDATA[
            and BILLING_SYSCODE > #{beginNo}
     ]]>
    order by BILLING_SYSCODE asc)
    where
    <![CDATA[
        rownum <= 100000
    ]]>
  </select>

  <select id="selectFixEba" resultMap="BaseResultMap">
    select *
    from (select distinct EBA_SYSCODE, eba.EAWB_PRINTCODE,EAD_CODE,EAST_CODE, p.P_EADCODE, p.p_EASTCODE, EBA_OCCURTIME, BILLING_SYSCODE
          from expressbusinessactivity eba,expressairwaybill eawb,product p
          where eawb.eawb_printcode=eba.eawb_printcode and eawb.EAWB_SERVICETYPE_ORIGINAL=p.P_SERVICETYPE_ORIGINAL
          and BILLING_SYSCODE between #{beginNo} and #{endNo}
    order by BILLING_SYSCODE asc)
  </select>

  <select id="selectByEawbPrintcodes" resultMap="BaseResultMap">
    select distinct EBA_SYSCODE, eba.EAWB_PRINTCODE,EAD_CODE,EAST_CODE, p.P_EADCODE, p.p_EASTCODE, EBA_OCCURTIME, BILLING_SYSCODE
    from expressbusinessactivity eba,expressairwaybill eawb,product p
    where eawb.eawb_printcode=eba.eawb_printcode
    and eawb.EAWB_SERVICETYPE_ORIGINAL=p.P_SERVICETYPE_ORIGINAL
    and eba.eawb_printcode in
    <foreach collection="eawbPrintcodes" item="eawbPrintcode" open="(" close=")" separator=",">
      #{eawbPrintcode}
    </foreach>
    order by BILLING_SYSCODE asc
  </select>

  <select id="generateFeeList" resultMap="GenerateFeeMap">
    select *
    from (select round(getPayPrice_NEW(e.eawb_printcode, pr1.pp_id,
                                       getPartitonCode_pay(e.eawb_printcode, pr1.psd_id, pr1.partition_sql)).price,
                       2)                                                                                     planAmount,
                 getPayPrice_NEW(e.eawb_printcode, pr1.pp_id,
                                 getPartitonCode_pay(e.eawb_printcode, pr1.psd_id, pr1.partition_sql)).v_desc formula,
                 getPartitonCode_pay(e.eawb_printcode, pr1.psd_id, pr1.partition_sql)                         partition_code,
                 pr1.psd_name                                                                                 psdName,
                 e.eawb_printcode,
                 e.EAWB_SO_CODE,
                 pr1.EBA_OCCURTIME,
                 e.EAWB_SERVICETYPE,
                 ( case when pr1.pp_unit='CW' then e.eawb_chargeableweight
                        when pr1.pp_unit='AW'then e.eawb_grossweight
                        when pr1.pp_unit='DW'then e.eawb_declaregrossweight
                        when pr1.pp_unit='VW'then e.eawb_volume
                        when pr1.pp_unit='VM'then e.eawb_declarevolume end) as EAWB_CHARGEABLEWEIGHT,
				 e.EAWB_OUTBOUND_SAC_ID,
				 e.EAWB_REFERENCE1,
				 e.EAWB_REFERENCE2,
				 e.MAWB_CODE,
                 pr1.pd_syscode                                                                               pdSyscode,
                 pr1.supplier_code                                                                            soCode,
                 pr1.pp_id                                                                                    ppId,
                 ro.COMPANY_ID                                                                                cId,
                 pr1.service_id,
                 pr1.ct_code                                                                                  ctCode,
                 pr1.pp_cate,
                 pr1.psd_id                                                                                   psdId,
                 (select pd.pd_name from price_define pd where pd.pd_syscode = pr1.pd_syscode)                pdName
          from expressairwaybill e,
               pay_route ro,
               pay_route_price prp,
               (select distinct pr.*,eba.EBA_OCCURTIME,psd.psd_name, psd.supplier_code,psd.service_id, psd.partition_sql
                from pay_service_detail psd,
                     pay_psd_eba ppe,
                     expressbusinessactivity eba,
                     pay_price pr
                where ppe.psd_id = psd.psd_id
                  and ppe.ead_code = eba.ead_code
                  and ppe.east_code = eba.east_code
                  and pr.psd_id = psd.psd_id
                  and pr.pp_auto = 'Y'
                  and pr.pp_status = 'ON'
                  and eba.EBA_SYSCODE = #{ebaSyscode}
                  and ppe.status = 'ON'
                  and pr.pp_expireddate &gt;= trunc(eba.eba_occurtime,'dd')
                  and pr.pp_effectivedate &lt;= trunc(eba.eba_occurtime,'dd')) pr1
          where e.eawb_servicetype = ro.r_code
            and ro.id = prp.route_id
            and prp.psd_id = pr1.psd_id
            and prp.status = 'ON'
            and ro.status = 'ON'
            and e.eawb_printcode = #{eawbPrintcode}) s
  </select>

  <select id="generateFeeByEba" resultMap="GenerateFeeMap">
    select *
    from (select round(getPayPrice_NEW(e.eawb_printcode, pr1.pp_id,
                                       getPartitonCode_pay(e.eawb_printcode, pr1.psd_id, pr1.partition_sql)).price,
                       2)                                                                                     planAmount,
                 getPayPrice_NEW(e.eawb_printcode, pr1.pp_id,
                                 getPartitonCode_pay(e.eawb_printcode, pr1.psd_id, pr1.partition_sql)).v_desc formula,
                 getPartitonCode_pay(e.eawb_printcode, pr1.psd_id,
                                     pr1.partition_sql)                                                       partition_code,
                 pr1.psd_name                                                                                 psdName,
                 e.eawb_printcode,
                 e.EAWB_SO_CODE,
                 e.EAWB_SERVICETYPE,
                 ( case when pr1.pp_unit='CW' then e.eawb_chargeableweight
                        when pr1.pp_unit='AW'then e.eawb_grossweight
                        when pr1.pp_unit='DW'then e.eawb_declaregrossweight
                        when pr1.pp_unit='VW'then e.eawb_volume
                        when pr1.pp_unit='VM'then e.eawb_declarevolume end) as EAWB_CHARGEABLEWEIGHT,
                 e.EAWB_OUTBOUND_SAC_ID,
                 e.EAWB_REFERENCE1,
                 e.EAWB_REFERENCE2,
                 e.MAWB_CODE,
                 pr1.pd_syscode                                                                               pdSyscode,
                 pr1.supplier_code                                                                            soCode,
                 pr1.pp_id                                                                                    ppId,
                 ro.COMPANY_ID                                                                                cId,
                 pr1.service_id,
                 pr1.ct_code                                                                                  ctCode,
                 pr1.pp_cate,
                 pr1.psd_id                                                                                   psdId,
                 (select pd.pd_name from price_define pd where pd.pd_syscode = pr1.pd_syscode)                pdName
          from expressairwaybill e,
               pay_route ro,
               pay_route_price prp,
               (select distinct pr.*,
                                psd.psd_name,
                                psd.supplier_code,
                                psd.service_id,
                                psd.partition_sql
                from pay_service_detail psd,
                     pay_psd_eba ppe,
                     pay_price pr
                where ppe.psd_id = psd.psd_id
                  and pr.psd_id = psd.psd_id
                  and pr.pp_auto = 'Y'
                  and pr.pp_status = 'ON'
                  and ppe.status = 'ON'
                  and pr.pp_expireddate &gt;= trunc(#{eba.ebaOccurtime},'dd')
                  and pr.pp_effectivedate &lt;= trunc(#{eba.ebaOccurtime},'dd')) pr1
          where e.eawb_servicetype = ro.r_code
            and ro.id = prp.route_id
            and prp.psd_id = pr1.psd_id
            and prp.status = 'ON'
            and ro.status = 'ON'
            and e.eawb_printcode = #{eba.eawbPrintcode}) s
  </select>

  <select id="selectExchangeRate" resultType="java.math.BigDecimal">
    select exchange_rate
    from EXCHANGE_RATE
    where sac_ct_sign = 'CNY'
      and ct_sign = (select ct_sign from CURRENCYTYPE where ct_code = #{ctCode})
      and term_code = TO_CHAR(sysdate, 'Mon-YYYY', 'NLS_DATE_LANGUAGE = AMERICAN')
  </select>

  <select id="generateReceiptFeeList" resultMap="GenerateReceiptFeeMap">
    select round(getReceiptPrice(e.eawb_printcode, pr1.rpd_id, getPartitonCode_pay(e.eawb_printcode, pr1.rp_id, pr1.partition_sql), pc.pc_id,pr1.pd_syscode).price,2)  planAmount,
    getReceiptPrice(e.eawb_printcode, pr1.rpd_id, getPartitonCode_pay(e.eawb_printcode, pr1.rp_id, pr1.partition_sql), pc.pc_id,pr1.pd_syscode).v_desc formula,
    getPartitonCode_pay(e.eawb_printcode, pr1.rp_id, pr1.partition_sql)                         partition_code,
    pr1.rp_name                                                                                 rpName,
    case when 'OUTER'=(select distinct so.so_type from settlementobject so where so.so_code=pc.so_code) then '1'
    else 'INNER'   end   as  ptType,
    (select distinct so.so_mode from settlementobject so where so.so_code = e.EAWB_SO_CODE) as so_mode,
    (select distinct so.C_CODE from settlementobject so where so.so_code = pc.so_code) as C_CODE,
    e.eawb_printcode,
    e.EAWB_SO_CODE,
    pr1.EBA_OCCURTIME,
    po.p_servicetype_original                                                                   EAWB_SERVICETYPE,
           ( case when pr1.rpd_unit='CW' then e.eawb_chargeableweight
                  when pr1.rpd_unit='AW'then e.eawb_grossweight
                  when pr1.rpd_unit='DW'then e.eawb_declaregrossweight
                  when pr1.rpd_unit='VW'then e.eawb_volume
                  when pr1.rpd_unit='VM'then e.eawb_declarevolume end) as EAWB_CHARGEABLEWEIGHT,
    e.EAWB_OUTBOUND_SAC_ID,
    e.EAWB_REFERENCE1,
    e.EAWB_REFERENCE2,
    e.MAWB_CODE,
    e.EAWB_DESTINATION,
    e.EAWB_DESTCOUNTRY,
    e.EAWB_DEPARTCOUNTRY,
    e.EAWB_DEPARTURE,
    pr1.pd_syscode                                                                               pdSyscode,
    pc.so_code                                                                                   soCode,
    pr1.rp_id                                                                                    rpId,
    po.company_id                                                                                cId,
    pr1.ct_code                                                                                  ctCode,
    pr1.rpd_cate,
    (select pd.pd_name from price_define pd where pd.pd_syscode = pr1.pd_syscode)                pdName
    from expressairwaybill e,
    product po,
    product_customer pc,
    settlementobject sob,
    (select distinct rpd.*,eba.EBA_OCCURTIME,rp.rp_name, rp.partition_sql
    from receipt_price rp,
    receipt_price_detail rpd,
    expressbusinessactivity eba,
    receipt_rp_eba rre
    where rre.rp_id = rp.rp_id
    and rre.ead_code = eba.ead_code
    and rre.east_code = eba.east_code
    and rp.rp_id = rpd.rp_id
    and rpd.rpd_auto = 'Y'
    and rpd.rpd_status = 'ON'
    and eba.EBA_SYSCODE = #{ebaSyscode}
    and rre.status = 'ON'
    and rp.status='ON'
    and rpd.rpd_expireddate &gt;= trunc(eba.eba_occurtime,'dd')
    and rpd.rpd_effectivedate &lt;= trunc(eba.eba_occurtime,'dd')) pr1
    where
    pc.pc_id in (select * from table(getall_pclist(e.eawb_servicetype_original,e.eawb_so_code,pr1.eba_occurtime)))
    and sob.so_code = e.EAWB_SO_CODE
    and sob.so_vendor_type='OFFLINE'
    and po.p_id=pc.p_id
    and pc.rp_id=pr1.rp_id
    and po.p_status='ON'
    and e.eawb_printcode = #{eawbPrintcode}
  </select>
  <select id="generateReceiptFeeByEba" resultMap="GenerateReceiptFeeMap">
    select round(getReceiptPrice(e.eawb_printcode, pr1.rpd_id, getPartitonCode_pay(e.eawb_printcode, pr1.rp_id, pr1.partition_sql), pc.pc_id,pr1.pd_syscode).price,2)  planAmount,
           getReceiptPrice(e.eawb_printcode, pr1.rpd_id, getPartitonCode_pay(e.eawb_printcode, pr1.rp_id, pr1.partition_sql), pc.pc_id,pr1.pd_syscode).v_desc formula,
    getPartitonCode_pay(e.eawb_printcode, pr1.rp_id, pr1.partition_sql)                         partition_code,
    pr1.rp_name                                                                                 rpName,
    case
             when 'OUTER' = (select distinct so.so_type from settlementobject so where so.so_code = pc.so_code) then '1'
             else 'INNER' end as ptType,
    (select distinct so.so_mode from settlementobject so where so.so_code = e.EAWB_SO_CODE) as so_mode,
    (select distinct so.C_CODE from settlementobject so where so.so_code = pc.so_code) as C_CODE,
    e.eawb_printcode,
    e.EAWB_SO_CODE,
    po.p_servicetype_original                                                                   EAWB_SERVICETYPE,
           ( case when pr1.rpd_unit='CW' then e.eawb_chargeableweight
                  when pr1.rpd_unit='AW'then e.eawb_grossweight
                  when pr1.rpd_unit='DW'then e.eawb_declaregrossweight
                  when pr1.rpd_unit='VW'then e.eawb_volume
                  when pr1.rpd_unit='VM'then e.eawb_declarevolume end) as EAWB_CHARGEABLEWEIGHT,
    e.EAWB_OUTBOUND_SAC_ID,
    e.EAWB_REFERENCE1,
    e.EAWB_REFERENCE2,
    e.MAWB_CODE,
    e.EAWB_DESTINATION,
    e.EAWB_DESTCOUNTRY,
    e.EAWB_DEPARTCOUNTRY,
    e.EAWB_DEPARTURE,
    pr1.pd_syscode                                                                               pdSyscode,
    pc.so_code                                                                                   soCode,
    pr1.rp_id                                                                                    rpId,
    po.company_id                                                                                cId,
    pr1.ct_code                                                                                  ctCode,
    pr1.rpd_cate,
    (select pd.pd_name from price_define pd where pd.pd_syscode = pr1.pd_syscode)                pdName
    from expressairwaybill e,
    product po,
    product_customer pc,
    settlementobject sob,
    (select distinct rpd.*,rp.rp_name, rp.partition_sql
    from receipt_price rp,
    receipt_price_detail rpd,
    receipt_rp_eba rre
    where rre.rp_id = rp.rp_id
    and rp.rp_id = rpd.rp_id
    and rpd.rpd_auto = 'Y'
    and rpd.rpd_status = 'ON'
    and rre.status = 'ON'
    and rp.status='ON'
    and rpd.rpd_expireddate &gt;= trunc(#{eba.ebaOccurtime},'dd')
    and rpd.rpd_effectivedate &lt;= trunc(#{eba.ebaOccurtime},'dd')) pr1
    where
    pc.pc_id in (select * from table(getall_pclist(e.eawb_servicetype_original,e.eawb_so_code,trunc(#{eba.ebaOccurtime},'dd'))))
    and sob.so_code=e.eawb_so_code
    and sob.so_vendor_type='OFFLINE'
    and po.p_id=pc.p_id
    and pc.rp_id=pr1.rp_id
    and po.p_status='ON'
    and e.eawb_printcode = #{eba.eawbPrintcode}
  </select>
</mapper>