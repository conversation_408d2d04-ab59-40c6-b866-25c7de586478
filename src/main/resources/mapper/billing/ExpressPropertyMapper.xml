<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.billing.dao.billing.ExpressPropertyMapper">
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.ExpressProperty">
    <result column="EP_GROUP" jdbcType="VARCHAR" property="epGroup" />
    <result column="EP_KEY" jdbcType="VARCHAR" property="epKey" />
    <result column="EP_VALUE" jdbcType="VARCHAR" property="epValue" />
  </resultMap>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.ExpressProperty">
    insert into EXPRESS_PROPERTY (EP_GROUP, EP_KEY, EP_VALUE
      )
    values (#{epGroup,jdbcType=VARCHAR}, #{epKey,jdbcType=VARCHAR}, #{epValue,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.ExpressProperty">
    insert into EXPRESS_PROPERTY
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="epGroup != null">
        EP_GROUP,
      </if>
      <if test="epKey != null">
        EP_KEY,
      </if>
      <if test="epValue != null">
        EP_VALUE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="epGroup != null">
        #{epGroup,jdbcType=VARCHAR},
      </if>
      <if test="epKey != null">
        #{epKey,jdbcType=VARCHAR},
      </if>
      <if test="epValue != null">
        #{epValue,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="selectServiceTypeList" resultType="com.sinoair.billing.domain.model.billing.ExpressProperty">
    select * from EXPRESS_PROPERTY order by EP_VALUE
  </select>
  <!--<select id="selectAllEp" resultType="com.sinoair.billing.domain.model.billing.ExpressProperty">-->
    <!--select * from EXPRESS_PROPERTY w order by EP_VALUE-->
  <!--</select>-->

  <select id="selectMxxbKeyList" resultType="java.lang.String">
    select ep_key from express_property where ep_type = '美线小包'
  </select>

  <select id="selectEpListFlatByGroup" resultType="com.sinoair.billing.domain.model.billing.ExpressProperty"
          parameterType="java.lang.String">
    select EP_GROUP, EP_KEY, EP_VALUE,to_number(t.ep_value) as epValueInt from EXPRESS_PROPERTY t where ep_group = #{epGroup,jdbcType=VARCHAR}
    order by to_number(t.ep_value) desc
  </select>

</mapper>