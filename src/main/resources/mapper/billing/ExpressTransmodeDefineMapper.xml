<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.billing.dao.billing.ExpressTransmodeDefineMapper">
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.ExpressTransmodeDefine">
    <id column="TRANSMODEID" jdbcType="VARCHAR" property="transmodeid" />
    <result column="TRANSLINE" jdbcType="VARCHAR" property="transline" />
    <result column="TRANSMODE" jdbcType="VARCHAR" property="transmode" />
    <result column="SAC_ID" jdbcType="VARCHAR" property="sacId" />
    <result column="TRANSMODENAME" jdbcType="VARCHAR" property="transmodename" />
    <result column="STUTAS" jdbcType="VARCHAR" property="stutas" />
    <result column="BT_CODE" jdbcType="VARCHAR" property="btCode" />
    <result column="TRANSROUT" jdbcType="VARCHAR" property="transrout" />
  </resultMap>
  <sql id="Base_Column_List">
    TRANSMODEID, TRANSLINE, TRANSMODE, SAC_ID, TRANSMODENAME, STUTAS, BT_CODE, TRANSROUT
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from EXPRESSTRANSMODEDEFINE
    where TRANSMODEID = #{transmodeid,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from EXPRESSTRANSMODEDEFINE
    where TRANSMODEID = #{transmodeid,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.ExpressTransmodeDefine">
    insert into EXPRESSTRANSMODEDEFINE (TRANSMODEID, TRANSLINE, TRANSMODE, 
      SAC_ID, TRANSMODENAME, STUTAS, 
      BT_CODE, TRANSROUT)
    values (#{transmodeid,jdbcType=VARCHAR}, #{transline,jdbcType=VARCHAR}, #{transmode,jdbcType=VARCHAR}, 
      #{sacId,jdbcType=VARCHAR}, #{transmodename,jdbcType=VARCHAR}, #{stutas,jdbcType=VARCHAR}, 
      #{btCode,jdbcType=VARCHAR}, #{transrout,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.ExpressTransmodeDefine">
    insert into EXPRESSTRANSMODEDEFINE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="transmodeid != null">
        TRANSMODEID,
      </if>
      <if test="transline != null">
        TRANSLINE,
      </if>
      <if test="transmode != null">
        TRANSMODE,
      </if>
      <if test="sacId != null">
        SAC_ID,
      </if>
      <if test="transmodename != null">
        TRANSMODENAME,
      </if>
      <if test="stutas != null">
        STUTAS,
      </if>
      <if test="btCode != null">
        BT_CODE,
      </if>
      <if test="transrout != null">
        TRANSROUT,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="transmodeid != null">
        #{transmodeid,jdbcType=VARCHAR},
      </if>
      <if test="transline != null">
        #{transline,jdbcType=VARCHAR},
      </if>
      <if test="transmode != null">
        #{transmode,jdbcType=VARCHAR},
      </if>
      <if test="sacId != null">
        #{sacId,jdbcType=VARCHAR},
      </if>
      <if test="transmodename != null">
        #{transmodename,jdbcType=VARCHAR},
      </if>
      <if test="stutas != null">
        #{stutas,jdbcType=VARCHAR},
      </if>
      <if test="btCode != null">
        #{btCode,jdbcType=VARCHAR},
      </if>
      <if test="transrout != null">
        #{transrout,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.ExpressTransmodeDefine">
    update EXPRESSTRANSMODEDEFINE
    <set>
      <if test="transline != null">
        TRANSLINE = #{transline,jdbcType=VARCHAR},
      </if>
      <if test="transmode != null">
        TRANSMODE = #{transmode,jdbcType=VARCHAR},
      </if>
      <if test="sacId != null">
        SAC_ID = #{sacId,jdbcType=VARCHAR},
      </if>
      <if test="transmodename != null">
        TRANSMODENAME = #{transmodename,jdbcType=VARCHAR},
      </if>
      <if test="stutas != null">
        STUTAS = #{stutas,jdbcType=VARCHAR},
      </if>
      <if test="btCode != null">
        BT_CODE = #{btCode,jdbcType=VARCHAR},
      </if>
      <if test="transrout != null">
        TRANSROUT = #{transrout,jdbcType=VARCHAR},
      </if>
    </set>
    where TRANSMODEID = #{transmodeid,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.ExpressTransmodeDefine">
    update EXPRESSTRANSMODEDEFINE
    set TRANSLINE = #{transline,jdbcType=VARCHAR},
      TRANSMODE = #{transmode,jdbcType=VARCHAR},
      SAC_ID = #{sacId,jdbcType=VARCHAR},
      TRANSMODENAME = #{transmodename,jdbcType=VARCHAR},
      STUTAS = #{stutas,jdbcType=VARCHAR},
      BT_CODE = #{btCode,jdbcType=VARCHAR},
      TRANSROUT = #{transrout,jdbcType=VARCHAR}
    where TRANSMODEID = #{transmodeid,jdbcType=VARCHAR}
  </update>

  <select id="getTransmodeList" resultType="com.sinoair.billing.domain.model.billing.ExpressTransmodeDefine">
    select * from EXPRESSTRANSMODEDEFINE ORDER BY TRANSMODENAME
  </select>

  <select id="selectListIdName" resultType="java.util.Map">
    select TRANSMODEID as TRANSMODE_ID,TRANSMODENAME as TRANSMODE_NAME
    from EXPRESSTRANSMODEDEFINE
    WHERE  STUTAS = 'ON'
   ORDER BY TRANSMODENAME
  </select>
</mapper>