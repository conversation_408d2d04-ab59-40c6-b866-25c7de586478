<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.GeneratePaymentFeeLogMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.GeneratePaymentFeeLog" >
    <id column="id" property="id" jdbcType="DECIMAL" />
    <result column="begin_num" property="beginNum" jdbcType="DECIMAL" />
    <result column="end_num" property="endNum" jdbcType="DECIMAL" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="deal_status" property="status" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, begin_num, end_num, create_time, remark,status
  </sql>
  <sql id='TABLE_SEQUENCE'>seq_generate_payment_fee_log.nextval</sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.math.BigDecimal" >
    select 
    <include refid="Base_Column_List" />
    from GENERATE_PAYMENT_FEE_LOG
    where id = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.math.BigDecimal" >
    delete from GENERATE_PAYMENT_FEE_LOG
    where id = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.GeneratePaymentFeeLog" >
    <selectKey keyProperty="id" resultType="java.math.BigDecimal" order="BEFORE">
      select
      <include refid="TABLE_SEQUENCE"/>
      from dual
    </selectKey>
    insert into GENERATE_PAYMENT_FEE_LOG (ID, BEGIN_NUM, END_NUM,
      CREATE_TIME, REMARK,DEAL_STATUS)
    values (#{id,jdbcType=DECIMAL}, #{beginNum,jdbcType=DECIMAL}, #{endNum,jdbcType=DECIMAL}, 
      #{createTime,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR},#{status,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.GeneratePaymentFeeLog" >
    <selectKey keyProperty="id" resultType="java.math.BigDecimal" order="BEFORE">
      select
      <include refid="TABLE_SEQUENCE"/>
      from dual
    </selectKey>
    insert into GENERATE_PAYMENT_FEE_LOG
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="beginNum != null" >
        BEGIN_NUM,
      </if>
      <if test="endNum != null" >
        END_NUM,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
      <if test="remark != null" >
        REMARK,
      </if>
      <if test="status != null" >
        DEAL_STATUS,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="beginNum != null" >
        #{beginNum,jdbcType=DECIMAL},
      </if>
      <if test="endNum != null" >
        #{endNum,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.GeneratePaymentFeeLog" >
    update GENERATE_PAYMENT_FEE_LOG
    <set >
      <if test="beginNum != null" >
        BEGIN_NUM = #{beginNum,jdbcType=DECIMAL},
      </if>
      <if test="endNum != null" >
        END_NUM = #{endNum,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null" >
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null" >
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        DEAL_STATUS = #{status,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.GeneratePaymentFeeLog" >
    update GENERATE_PAYMENT_FEE_LOG
    set BEGIN_NUM = #{beginNum,jdbcType=DECIMAL},
        END_NUM = #{endNum,jdbcType=DECIMAL},
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
        REMARK = #{remark,jdbcType=VARCHAR},
        DEAL_STATUS = #{status,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <select id="selectDealStatusIsN" resultMap="BaseResultMap">
      select * from GENERATE_PAYMENT_FEE_LOG where DEAL_STATUS='N'
      <![CDATA[
         and rownum <= 10
      ]]>
  </select>
</mapper>