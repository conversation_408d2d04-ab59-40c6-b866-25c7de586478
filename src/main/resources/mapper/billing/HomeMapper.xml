<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.HomeMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.Home" >
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="LABEL" property="label" jdbcType="VARCHAR" />
    <result column="TITLE" property="title" jdbcType="VARCHAR" />
    <result column="TYPE" property="type" jdbcType="DECIMAL" />
    <result column="STATUS" property="status" jdbcType="VARCHAR" />
    <result column="ADD_TIME" property="addTime" jdbcType="TIMESTAMP" />
    <result column="REMARK" property="remark" jdbcType="VARCHAR" />
    <result column="IS_TOP" property="isTop" jdbcType="VARCHAR" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.sinoair.billing.domain.model.billing.Home" extends="BaseResultMap" >
    <result column="CONTENT" property="content" jdbcType="CLOB" />
  </resultMap>

  <!-- 序列 -->
  <sql id='TABLE_SEQUENCE'>SEQ_HOME.NEXTVAL</sql>

  <sql id="Base_Column_List" >
    ID, LABEL, TITLE, TYPE, STATUS, ADD_TIME, REMARK, IS_TOP
  </sql>
  <sql id="Blob_Column_List" >
    CONTENT
  </sql>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from HOME
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from HOME
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.Home" >
    <selectKey keyProperty="id" resultType="int" order="BEFORE">
      select <include refid="TABLE_SEQUENCE" /> from dual
    </selectKey>
    insert into HOME (ID, LABEL, TITLE, 
      TYPE, STATUS, ADD_TIME, 
      REMARK, IS_TOP, CONTENT
      )
    values (#{id,jdbcType=DECIMAL}, #{label,jdbcType=VARCHAR}, #{title,jdbcType=VARCHAR}, 
      #{type,jdbcType=DECIMAL}, #{status,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, 
      #{remark,jdbcType=VARCHAR}, #{isTop,jdbcType=VARCHAR}, #{content,jdbcType=CLOB}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.Home" >
    <selectKey keyProperty="id" resultType="int" order="BEFORE">
      select <include refid="TABLE_SEQUENCE" /> from dual
    </selectKey>
    insert into HOME
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="label != null" >
        LABEL,
      </if>
      <if test="title != null" >
        TITLE,
      </if>
      <if test="type != null" >
        TYPE,
      </if>
      <if test="status != null" >
        STATUS,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="remark != null" >
        REMARK,
      </if>
      <if test="isTop != null" >
        IS_TOP,
      </if>
      <if test="content != null" >
        CONTENT,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="label != null" >
        #{label,jdbcType=VARCHAR},
      </if>
      <if test="title != null" >
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        #{type,jdbcType=DECIMAL},
      </if>
      <if test="status != null" >
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isTop != null" >
        #{isTop,jdbcType=VARCHAR},
      </if>
      <if test="content != null" >
        #{content,jdbcType=CLOB},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.Home" >
    update HOME
    <set >
      <if test="label != null" >
        LABEL = #{label,jdbcType=VARCHAR},
      </if>
      <if test="title != null" >
        TITLE = #{title,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        TYPE = #{type,jdbcType=DECIMAL},
      </if>
      <if test="status != null" >
        STATUS = #{status,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null" >
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isTop != null" >
        IS_TOP = #{isTop,jdbcType=VARCHAR},
      </if>
      <if test="content != null" >
        CONTENT = #{content,jdbcType=CLOB},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sinoair.billing.domain.model.billing.Home" >
    update HOME
    set LABEL = #{label,jdbcType=VARCHAR},
      TITLE = #{title,jdbcType=VARCHAR},
      TYPE = #{type,jdbcType=DECIMAL},
      STATUS = #{status,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      REMARK = #{remark,jdbcType=VARCHAR},
      IS_TOP = #{isTop,jdbcType=VARCHAR},
      CONTENT = #{content,jdbcType=CLOB}
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.Home" >
    update HOME
    set LABEL = #{label,jdbcType=VARCHAR},
      TITLE = #{title,jdbcType=VARCHAR},
      TYPE = #{type,jdbcType=DECIMAL},
      STATUS = #{status,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      REMARK = #{remark,jdbcType=VARCHAR},
      IS_TOP = #{isTop,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>

  <!-- 查询置顶页签 -->
  <select id="selectTopHomes" resultType="com.sinoair.billing.domain.model.billing.Home">
    SELECT * from HOME WHERE 1=1
    AND IS_TOP = 'Y'
    and STATUS = 'Y'
    order by ADD_TIME desc
  </select>

  <!-- 查询非置顶页签 -->
  <select id="selectNoTopHomes" resultType="com.sinoair.billing.domain.model.billing.Home">
    SELECT * from HOME WHERE 1=1
    AND IS_TOP != 'Y'
    and STATUS = 'Y'
    order by ADD_TIME desc
  </select>

  <!-- 查询所有 -->
  <select id="selectHome" resultType="com.sinoair.billing.domain.model.billing.Home" parameterType="java.lang.String">
    SELECT * from HOME WHERE 1=1
    <if test="search != null and search != ''">
      and (UPPER(title) like '%'||UPPER(#{search})||'%'
          or UPPER(LABEL) like '%'||UPPER(#{search})||'%'
          or UPPER(CONTENT) like '%'||UPPER(#{search})||'%')
    </if>
    order by ADD_TIME desc
  </select>
</mapper>