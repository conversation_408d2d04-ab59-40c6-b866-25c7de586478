<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.InqueryTemplateMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.InqueryTemplate" >
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="TYPE" property="type" jdbcType="DECIMAL" />
    <result column="TITLE_ZH" property="titleZh" jdbcType="VARCHAR" />
    <result column="TITLE_EN" property="titleEn" jdbcType="VARCHAR" />
    <result column="STATUS" property="status" jdbcType="VARCHAR" />
    <result column="DESCRIPTION" property="description" jdbcType="VARCHAR" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.sinoair.billing.domain.model.billing.InqueryTemplateWithBLOBs" extends="BaseResultMap" >
    <result column="SQL_CONFIG" property="sqlConfig" jdbcType="CLOB" />
    <result column="WHERE_CONFIG" property="whereConfig" jdbcType="CLOB" />
    <result column="TABLE_CONFIG" property="tableConfig" jdbcType="CLOB" />
    <result column="HEAD_CONFIG" property="headConfig" jdbcType="CLOB" />
  </resultMap>

  <!-- 序列 -->
  <sql id='TABLE_SEQUENCE'>SEQ_INQUERY_TEMPLATE.NEXTVAL</sql>

  <sql id="Base_Column_List" >
    ID, TYPE, TITLE_ZH, TITLE_EN, STATUS, DESCRIPTION
  </sql>
  <sql id="Blob_Column_List" >
    SQL_CONFIG, WHERE_CONFIG, TABLE_CONFIG, HEAD_CONFIG
  </sql>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from INQUERY_TEMPLATE
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from INQUERY_TEMPLATE
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.InqueryTemplateWithBLOBs" >

    <selectKey keyProperty="id" resultType="int" order="BEFORE">
      select <include refid="TABLE_SEQUENCE" /> from dual
    </selectKey>

    insert into INQUERY_TEMPLATE (ID, TYPE, TITLE_ZH, 
      TITLE_EN, STATUS, DESCRIPTION, 
      SQL_CONFIG, WHERE_CONFIG, TABLE_CONFIG, 
      HEAD_CONFIG)
    values (#{id,jdbcType=DECIMAL}, #{type,jdbcType=DECIMAL}, #{titleZh,jdbcType=VARCHAR}, 
      #{titleEn,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, 
      #{sqlConfig,jdbcType=CLOB}, #{whereConfig,jdbcType=CLOB}, #{tableConfig,jdbcType=CLOB}, 
      #{headConfig,jdbcType=CLOB})
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.InqueryTemplateWithBLOBs" >

    <selectKey keyProperty="id" resultType="int" order="BEFORE">
      select <include refid="TABLE_SEQUENCE" /> from dual
    </selectKey>

    insert into INQUERY_TEMPLATE
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="type != null" >
        TYPE,
      </if>
      <if test="titleZh != null" >
        TITLE_ZH,
      </if>
      <if test="titleEn != null" >
        TITLE_EN,
      </if>
      <if test="status != null" >
        STATUS,
      </if>
      <if test="description != null" >
        DESCRIPTION,
      </if>
      <if test="sqlConfig != null" >
        SQL_CONFIG,
      </if>
      <if test="whereConfig != null" >
        WHERE_CONFIG,
      </if>
      <if test="tableConfig != null" >
        TABLE_CONFIG,
      </if>
      <if test="headConfig != null" >
        HEAD_CONFIG,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="type != null" >
        #{type,jdbcType=DECIMAL},
      </if>
      <if test="titleZh != null" >
        #{titleZh,jdbcType=VARCHAR},
      </if>
      <if test="titleEn != null" >
        #{titleEn,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="description != null" >
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="sqlConfig != null" >
        #{sqlConfig,jdbcType=CLOB},
      </if>
      <if test="whereConfig != null" >
        #{whereConfig,jdbcType=CLOB},
      </if>
      <if test="tableConfig != null" >
        #{tableConfig,jdbcType=CLOB},
      </if>
      <if test="headConfig != null" >
        #{headConfig,jdbcType=CLOB},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.InqueryTemplateWithBLOBs" >
    update INQUERY_TEMPLATE
    <set >
      <if test="type != null" >
        TYPE = #{type,jdbcType=DECIMAL},
      </if>
      <if test="titleZh != null" >
        TITLE_ZH = #{titleZh,jdbcType=VARCHAR},
      </if>
      <if test="titleEn != null" >
        TITLE_EN = #{titleEn,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        STATUS = #{status,jdbcType=VARCHAR},
      </if>
      <if test="description != null" >
        DESCRIPTION = #{description,jdbcType=VARCHAR},
      </if>
      <if test="sqlConfig != null" >
        SQL_CONFIG = #{sqlConfig,jdbcType=CLOB},
      </if>
      <if test="whereConfig != null" >
        WHERE_CONFIG = #{whereConfig,jdbcType=CLOB},
      </if>
      <if test="tableConfig != null" >
        TABLE_CONFIG = #{tableConfig,jdbcType=CLOB},
      </if>
      <if test="headConfig != null" >
        HEAD_CONFIG = #{headConfig,jdbcType=CLOB},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sinoair.billing.domain.model.billing.InqueryTemplateWithBLOBs" >
    update INQUERY_TEMPLATE
    set TYPE = #{type,jdbcType=DECIMAL},
      TITLE_ZH = #{titleZh,jdbcType=VARCHAR},
      TITLE_EN = #{titleEn,jdbcType=VARCHAR},
      STATUS = #{status,jdbcType=VARCHAR},
      DESCRIPTION = #{description,jdbcType=VARCHAR},
      SQL_CONFIG = #{sqlConfig,jdbcType=CLOB},
      WHERE_CONFIG = #{whereConfig,jdbcType=CLOB},
      TABLE_CONFIG = #{tableConfig,jdbcType=CLOB},
      HEAD_CONFIG = #{headConfig,jdbcType=CLOB},
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.InqueryTemplate" >
    update INQUERY_TEMPLATE
    set TYPE = #{type,jdbcType=DECIMAL},
      TITLE_ZH = #{titleZh,jdbcType=VARCHAR},
      TITLE_EN = #{titleEn,jdbcType=VARCHAR},
      STATUS = #{status,jdbcType=VARCHAR},
      DESCRIPTION = #{description,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>

  <select id="executeSqlQuery" resultType="java.util.HashMap" parameterType="java.lang.String">
    ${sql}
  </select>
</mapper>