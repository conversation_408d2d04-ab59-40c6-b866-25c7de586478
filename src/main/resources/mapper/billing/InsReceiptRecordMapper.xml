<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.billing.dao.billing.InsReceiptRecordMapper">

    <select id="getReceiptPrice" resultType="java.lang.Double" parameterType="java.util.Map">
        select round(getreceiptprice(#{eawbPrintcode},#{prId}),2) result from dual
    </select>

    <select id="getNonReceiptPrice" resultType="java.lang.Double" parameterType="java.util.Map">
        select round(getReceiptPrice_YEBA(#{eawbPrintcode},#{prId}),2) result from dual
    </select>




</mapper>