<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.InsTaskMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.InsTask" >
    <id column="TASK_ID" property="taskId" jdbcType="DECIMAL" />
    <result column="TASK_NAME" property="taskName" jdbcType="VARCHAR" />
    <result column="TASK_TYPE" property="taskType" jdbcType="VARCHAR" />
    <result column="TASK_SO_CODE" property="taskSoCode" jdbcType="VARCHAR" />
    <result column="TASK_START_DATE" property="taskStartDate" jdbcType="VARCHAR" />
    <result column="TASK_END_DATE" property="taskEndDate" jdbcType="VARCHAR" />
    <result column="TASK_FILED" property="taskFiled" jdbcType="VARCHAR" />
    <result column="TASK_REMARK" property="taskRemark" jdbcType="VARCHAR" />
    <result column="TASK_STATUS" property="taskStatus" jdbcType="VARCHAR" />
    <result column="TASK_CREATETIME" property="taskCreatetime" jdbcType="TIMESTAMP" />
    <result column="TASK_HANDLETIME" property="taskHandletime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    TASK_ID, TASK_NAME,TASK_TYPE, TASK_SO_CODE, TASK_START_DATE, TASK_END_DATE, TASK_FILED,
    TASK_REMARK, TASK_STATUS, TASK_CREATETIME, TASK_HANDLETIME
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.math.BigDecimal" >
    select 
    <include refid="Base_Column_List" />
    from INS_TASK
    where TASK_ID = #{taskId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.math.BigDecimal" >
    delete from INS_TASK
    where TASK_ID = #{taskId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.InsTask" >
    insert into INS_TASK (TASK_ID, TASK_NAME,TASK_TYPE, TASK_SO_CODE,
      TASK_START_DATE, TASK_END_DATE, TASK_FILED,
      TASK_REMARK, TASK_STATUS, TASK_CREATETIME, 
      TASK_HANDLETIME)
    values (#{taskId,jdbcType=DECIMAL},#{taskName,jdbcType=VARCHAR}, #{taskType,jdbcType=VARCHAR}, #{taskSoCode,jdbcType=VARCHAR},
      #{taskStartDate,jdbcType=VARCHAR}, #{taskEndDate,jdbcType=VARCHAR}, #{taskFiled,jdbcType=VARCHAR},
      #{taskRemark,jdbcType=VARCHAR}, #{taskStatus,jdbcType=VARCHAR}, #{taskCreatetime,jdbcType=TIMESTAMP}, 
      #{taskHandletime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.InsTask" >
    insert into INS_TASK
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="taskId != null" >
        TASK_ID,
      </if>
      <if test="taskName != null" >
        TASK_NAME,
      </if>
      <if test="taskType != null" >
        TASK_TYPE,
      </if>
      <if test="taskSoCode != null" >
        TASK_SO_CODE,
      </if>
      <if test="taskStartDate != null" >
        TASK_START_DATE,
      </if>
      <if test="taskEndDate != null" >
        TASK_END_DATE,
      </if>
      <if test="taskFiled != null" >
        TASK_FILED,
      </if>
      <if test="taskRemark != null" >
        TASK_REMARK,
      </if>
      <if test="taskStatus != null" >
        TASK_STATUS,
      </if>
      <if test="taskCreatetime != null" >
        TASK_CREATETIME,
      </if>
      <if test="taskHandletime != null" >
        TASK_HANDLETIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="taskId != null" >
        #{taskId,jdbcType=DECIMAL},
      </if>
      <if test="taskName != null" >
        #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="taskType != null" >
        #{taskType,jdbcType=VARCHAR},
      </if>
      <if test="taskSoCode != null" >
        #{taskSoCode,jdbcType=VARCHAR},
      </if>
      <if test="taskStartDate != null" >
        #{taskStartDate,jdbcType=VARCHAR},
      </if>
      <if test="taskEndDate != null" >
        #{taskEndDate,jdbcType=VARCHAR},
      </if>
      <if test="taskFiled != null" >
        #{taskFiled,jdbcType=VARCHAR},
      </if>
      <if test="taskRemark != null" >
        #{taskRemark,jdbcType=VARCHAR},
      </if>
      <if test="taskStatus != null" >
        #{taskStatus,jdbcType=VARCHAR},
      </if>
      <if test="taskCreatetime != null" >
        #{taskCreatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="taskHandletime != null" >
        #{taskHandletime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.InsTask" >
    update INS_TASK
    <set >
      <if test="taskName != null" >
        TASK_NAME = #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="taskType != null" >
        TASK_TYPE = #{taskType,jdbcType=VARCHAR},
      </if>
      <if test="taskSoCode != null" >
        TASK_SO_CODE = #{taskSoCode,jdbcType=VARCHAR},
      </if>
      <if test="taskStartDate != null" >
        TASK_START_DATE = #{taskStartDate,jdbcType=VARCHAR},
      </if>
      <if test="taskEndDate != null" >
        TASK_END_DATE = #{taskEndDate,jdbcType=VARCHAR},
      </if>
      <if test="taskFiled != null" >
        TASK_FILED = #{taskFiled,jdbcType=VARCHAR},
      </if>
      <if test="taskRemark != null" >
        TASK_REMARK = #{taskRemark,jdbcType=VARCHAR},
      </if>
      <if test="taskStatus != null" >
        TASK_STATUS = #{taskStatus,jdbcType=VARCHAR},
      </if>
      <if test="taskCreatetime != null" >
        TASK_CREATETIME = #{taskCreatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="taskHandletime != null" >
        TASK_HANDLETIME = #{taskHandletime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where TASK_ID = #{taskId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.InsTask" >
    update INS_TASK
    set TASK_TYPE = #{taskType,jdbcType=VARCHAR},
      TASK_NAME = #{taskName,jdbcType=VARCHAR},
      TASK_SO_CODE = #{taskSoCode,jdbcType=VARCHAR},
      TASK_START_DATE = #{taskStartDate,jdbcType=VARCHAR},
      TASK_END_DATE = #{taskEndDate,jdbcType=VARCHAR},
      TASK_FILED = #{taskFiled,jdbcType=VARCHAR},
      TASK_REMARK = #{taskRemark,jdbcType=VARCHAR},
      TASK_STATUS = #{taskStatus,jdbcType=VARCHAR},
      TASK_CREATETIME = #{taskCreatetime,jdbcType=TIMESTAMP},
      TASK_HANDLETIME = #{taskHandletime,jdbcType=TIMESTAMP}
    where TASK_ID = #{taskId,jdbcType=DECIMAL}
  </update>

  <select id="selectByCondition" resultMap="BaseResultMap" parameterType="com.sinoair.billing.domain.vo.query.InsRecordQuery" >
    select
    <include refid="Base_Column_List" />
    from INS_TASK
    where TASK_TYPE = #{handleType,jdbcType=VARCHAR}
  </select>

  <select id="selectByTaskType" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select
    <include refid="Base_Column_List" />
    from INS_TASK
    where TASK_TYPE = #{taskType,jdbcType=VARCHAR}
  </select>
</mapper>