<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.InterfaceLogMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.InterfaceLog" >
    <id column="LOG_ID" property="logId" jdbcType="DECIMAL" />
    <result column="INTERFACE_SYSTEM" property="interfaceSystem" jdbcType="VARCHAR" />
    <result column="INTERFACE_MODULE" property="interfaceModule" jdbcType="VARCHAR" />
    <result column="INTERFACE_NAME" property="interfaceName" jdbcType="VARCHAR" />
    <result column="INTERFACE_OPERATION" property="interfaceOperation" jdbcType="VARCHAR" />
    <result column="INTERFACE_URL" property="interfaceUrl" jdbcType="VARCHAR" />
    <result column="INTERFACE_STATUS" property="interfaceStatus" jdbcType="VARCHAR" />
    <result column="INTERFACE_REASON" property="interfaceReason" jdbcType="VARCHAR" />
    <result column="INTERFACE_PUSHTIME" property="interfacePushtime" jdbcType="TIMESTAMP" />
    <result column="LOG_HANDLETIME" property="logHandletime" jdbcType="TIMESTAMP" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.sinoair.billing.domain.model.billing.InterfaceLogWithBLOBs" extends="BaseResultMap" >
    <result column="INTERFACE_PARAM" property="interfaceParam" jdbcType="CLOB" />
    <result column="INTERFACE_RESULT" property="interfaceResult" jdbcType="CLOB" />
  </resultMap>
  <sql id="Base_Column_List" >
    LOG_ID, INTERFACE_SYSTEM, INTERFACE_MODULE, INTERFACE_NAME, INTERFACE_OPERATION, 
    INTERFACE_URL, INTERFACE_STATUS, INTERFACE_REASON, INTERFACE_PUSHTIME, LOG_HANDLETIME
  </sql>
  <sql id="Blob_Column_List" >
    INTERFACE_PARAM, INTERFACE_RESULT
  </sql>
  <sql id='TABLE_SEQUENCE'>SEQ_LOG.NEXTVAL</sql>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.math.BigDecimal" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from INTERFACE_LOG
    where LOG_ID = #{logId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.math.BigDecimal" >
    delete from INTERFACE_LOG
    where LOG_ID = #{logId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.InterfaceLogWithBLOBs" >
    insert into INTERFACE_LOG (LOG_ID, INTERFACE_SYSTEM, INTERFACE_MODULE, 
      INTERFACE_NAME, INTERFACE_OPERATION, INTERFACE_URL, 
      INTERFACE_STATUS, INTERFACE_REASON, INTERFACE_PUSHTIME, 
      LOG_HANDLETIME, INTERFACE_PARAM, INTERFACE_RESULT
      )
    values (#{logId,jdbcType=DECIMAL}, #{interfaceSystem,jdbcType=VARCHAR}, #{interfaceModule,jdbcType=VARCHAR}, 
      #{interfaceName,jdbcType=VARCHAR}, #{interfaceOperation,jdbcType=VARCHAR}, #{interfaceUrl,jdbcType=VARCHAR}, 
      #{interfaceStatus,jdbcType=VARCHAR}, #{interfaceReason,jdbcType=VARCHAR}, #{interfacePushtime,jdbcType=TIMESTAMP}, 
      #{logHandletime,jdbcType=TIMESTAMP}, #{interfaceParam,jdbcType=CLOB}, #{interfaceResult,jdbcType=CLOB}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.InterfaceLogWithBLOBs" >
    insert into INTERFACE_LOG
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="logId != null" >
        LOG_ID,
      </if>
      <if test="interfaceSystem != null" >
        INTERFACE_SYSTEM,
      </if>
      <if test="interfaceModule != null" >
        INTERFACE_MODULE,
      </if>
      <if test="interfaceName != null" >
        INTERFACE_NAME,
      </if>
      <if test="interfaceOperation != null" >
        INTERFACE_OPERATION,
      </if>
      <if test="interfaceUrl != null" >
        INTERFACE_URL,
      </if>
      <if test="interfaceStatus != null" >
        INTERFACE_STATUS,
      </if>
      <if test="interfaceReason != null" >
        INTERFACE_REASON,
      </if>
      <if test="interfacePushtime != null" >
        INTERFACE_PUSHTIME,
      </if>
      <if test="logHandletime != null" >
        LOG_HANDLETIME,
      </if>
      <if test="interfaceParam != null" >
        INTERFACE_PARAM,
      </if>
      <if test="interfaceResult != null" >
        INTERFACE_RESULT,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="logId != null" >
        #{logId,jdbcType=DECIMAL},
      </if>
      <if test="interfaceSystem != null" >
        #{interfaceSystem,jdbcType=VARCHAR},
      </if>
      <if test="interfaceModule != null" >
        #{interfaceModule,jdbcType=VARCHAR},
      </if>
      <if test="interfaceName != null" >
        #{interfaceName,jdbcType=VARCHAR},
      </if>
      <if test="interfaceOperation != null" >
        #{interfaceOperation,jdbcType=VARCHAR},
      </if>
      <if test="interfaceUrl != null" >
        #{interfaceUrl,jdbcType=VARCHAR},
      </if>
      <if test="interfaceStatus != null" >
        #{interfaceStatus,jdbcType=VARCHAR},
      </if>
      <if test="interfaceReason != null" >
        #{interfaceReason,jdbcType=VARCHAR},
      </if>
      <if test="interfacePushtime != null" >
        #{interfacePushtime,jdbcType=TIMESTAMP},
      </if>
      <if test="logHandletime != null" >
        #{logHandletime,jdbcType=TIMESTAMP},
      </if>
      <if test="interfaceParam != null" >
        #{interfaceParam,jdbcType=CLOB},
      </if>
      <if test="interfaceResult != null" >
        #{interfaceResult,jdbcType=CLOB},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.InterfaceLogWithBLOBs" >
    update INTERFACE_LOG
    <set >
      <if test="interfaceSystem != null" >
        INTERFACE_SYSTEM = #{interfaceSystem,jdbcType=VARCHAR},
      </if>
      <if test="interfaceModule != null" >
        INTERFACE_MODULE = #{interfaceModule,jdbcType=VARCHAR},
      </if>
      <if test="interfaceName != null" >
        INTERFACE_NAME = #{interfaceName,jdbcType=VARCHAR},
      </if>
      <if test="interfaceOperation != null" >
        INTERFACE_OPERATION = #{interfaceOperation,jdbcType=VARCHAR},
      </if>
      <if test="interfaceUrl != null" >
        INTERFACE_URL = #{interfaceUrl,jdbcType=VARCHAR},
      </if>
      <if test="interfaceStatus != null" >
        INTERFACE_STATUS = #{interfaceStatus,jdbcType=VARCHAR},
      </if>
      <if test="interfaceReason != null" >
        INTERFACE_REASON = #{interfaceReason,jdbcType=VARCHAR},
      </if>
      <if test="interfacePushtime != null" >
        INTERFACE_PUSHTIME = #{interfacePushtime,jdbcType=TIMESTAMP},
      </if>
      <if test="logHandletime != null" >
        LOG_HANDLETIME = #{logHandletime,jdbcType=TIMESTAMP},
      </if>
      <if test="interfaceParam != null" >
        INTERFACE_PARAM = #{interfaceParam,jdbcType=CLOB},
      </if>
      <if test="interfaceResult != null" >
        INTERFACE_RESULT = #{interfaceResult,jdbcType=CLOB},
      </if>
    </set>
    where LOG_ID = #{logId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sinoair.billing.domain.model.billing.InterfaceLogWithBLOBs" >
    update INTERFACE_LOG
    set INTERFACE_SYSTEM = #{interfaceSystem,jdbcType=VARCHAR},
      INTERFACE_MODULE = #{interfaceModule,jdbcType=VARCHAR},
      INTERFACE_NAME = #{interfaceName,jdbcType=VARCHAR},
      INTERFACE_OPERATION = #{interfaceOperation,jdbcType=VARCHAR},
      INTERFACE_URL = #{interfaceUrl,jdbcType=VARCHAR},
      INTERFACE_STATUS = #{interfaceStatus,jdbcType=VARCHAR},
      INTERFACE_REASON = #{interfaceReason,jdbcType=VARCHAR},
      INTERFACE_PUSHTIME = #{interfacePushtime,jdbcType=TIMESTAMP},
      LOG_HANDLETIME = #{logHandletime,jdbcType=TIMESTAMP},
      INTERFACE_PARAM = #{interfaceParam,jdbcType=CLOB},
      INTERFACE_RESULT = #{interfaceResult,jdbcType=CLOB}
    where LOG_ID = #{logId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.InterfaceLog" >
    update INTERFACE_LOG
    set INTERFACE_SYSTEM = #{interfaceSystem,jdbcType=VARCHAR},
      INTERFACE_MODULE = #{interfaceModule,jdbcType=VARCHAR},
      INTERFACE_NAME = #{interfaceName,jdbcType=VARCHAR},
      INTERFACE_OPERATION = #{interfaceOperation,jdbcType=VARCHAR},
      INTERFACE_URL = #{interfaceUrl,jdbcType=VARCHAR},
      INTERFACE_STATUS = #{interfaceStatus,jdbcType=VARCHAR},
      INTERFACE_REASON = #{interfaceReason,jdbcType=VARCHAR},
      INTERFACE_PUSHTIME = #{interfacePushtime,jdbcType=TIMESTAMP},
      LOG_HANDLETIME = #{logHandletime,jdbcType=TIMESTAMP}
    where LOG_ID = #{logId,jdbcType=DECIMAL}
  </update>

  <select id="selectSeq" resultType="java.lang.Integer">
    select
    <include refid="TABLE_SEQUENCE"/>
    from dual
  </select>

  <select id="selectRecord" resultMap="ResultMapWithBLOBs" parameterType="com.sinoair.billing.domain.vo.FilterParam">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from INTERFACE_LOG
    where 1=1
    <if test="code!=null and code!=''">
      and INTERFACE_SYSTEM = #{code}
    </if>
    <if test="status!=null and status!='ALL'">
      and INTERFACE_STATUS = #{status}
    </if>
    <if test="serviceName!=null and serviceName!=''">
      and INTERFACE_OPERATION = #{serviceName}
    </if>
    <if test="starttime!=null and endtime!=null and starttime!='' and endtime!=''">
      and (to_char(INTERFACE_PUSHTIME, 'yyyy-mm-dd') >= #{starttime} and
      to_char(INTERFACE_PUSHTIME, 'yyyy-mm-dd') &lt;= #{endtime})
    </if>
    order by LOG_HANDLETIME desc
  </select>
</mapper>