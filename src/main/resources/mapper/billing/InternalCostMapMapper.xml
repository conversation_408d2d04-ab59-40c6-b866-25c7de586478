<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.InternalCostMapMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.InternalCostMap" >
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="SO_CODE" property="soCode" jdbcType="VARCHAR" />
    <result column="SAC_ID_PAY" property="sacIdPay" jdbcType="VARCHAR" />
    <result column="SAC_ID_REC" property="sacIdRec" jdbcType="VARCHAR" />
    <result column="SP_CODE_PAY" property="spCodePay" jdbcType="VARCHAR" />
    <result column="SO_CODE_REC" property="soCodeRec" jdbcType="VARCHAR" />
    <result column="BILLING_TYPE" property="billingType" jdbcType="VARCHAR" />
    <result column="CT_CODE" property="ctCode" jdbcType="VARCHAR" />
    <result column="PD_SYSCODE" property="pdSyscode" jdbcType="DECIMAL" />
    <result column="DM_TYPE" property="dmType" jdbcType="VARCHAR" />
    <result column="BUSINESS_CODE" property="businessCode" jdbcType="VARCHAR" />
    <result column="PRICE_ID" property="priceId" jdbcType="VARCHAR" />
    <result column="PRICE_PIECES_PER" property="pricePiecesPer" jdbcType="DECIMAL" />
    <result column="PRICE_WEIGHT_PER" property="priceWeightPer" jdbcType="DECIMAL" />
    <result column="ICM_STATUS" property="icmStatus" jdbcType="VARCHAR" />
    <result column="REMARK" property="remark" jdbcType="VARCHAR" />
    <result column="TAX" property="tax" jdbcType="DECIMAL" />
    <result column="CURRENCYRATE" property="currencyrate" jdbcType="DECIMAL" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, SO_CODE, SAC_ID_PAY, SAC_ID_REC, SP_CODE_PAY, SO_CODE_REC, BILLING_TYPE, CT_CODE,
    PD_SYSCODE, DM_TYPE, BUSINESS_CODE, PRICE_ID, PRICE_PIECES_PER, PRICE_WEIGHT_PER,
    ICM_STATUS, REMARK, TAX, CURRENCYRATE
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Short" >
    select
    <include refid="Base_Column_List" />
    from INTERNAL_COST_MAP
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Short" >
    delete from INTERNAL_COST_MAP
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.InternalCostMap" >
    insert into INTERNAL_COST_MAP (ID, SO_CODE, SAC_ID_PAY,
    SAC_ID_REC, SP_CODE_PAY, SO_CODE_REC,
    BILLING_TYPE, CT_CODE, PD_SYSCODE,
    DM_TYPE, BUSINESS_CODE, PRICE_ID,
    PRICE_PIECES_PER, PRICE_WEIGHT_PER, ICM_STATUS,
    REMARK, TAX, CURRENCYRATE
    )
    values (#{id,jdbcType=DECIMAL}, #{soCode,jdbcType=VARCHAR}, #{sacIdPay,jdbcType=VARCHAR},
    #{sacIdRec,jdbcType=VARCHAR}, #{spCodePay,jdbcType=VARCHAR}, #{soCodeRec,jdbcType=VARCHAR},
    #{billingType,jdbcType=VARCHAR}, #{ctCode,jdbcType=VARCHAR}, #{pdSyscode,jdbcType=DECIMAL},
    #{dmType,jdbcType=VARCHAR}, #{businessCode,jdbcType=VARCHAR}, #{priceId,jdbcType=VARCHAR},
    #{pricePiecesPer,jdbcType=DECIMAL}, #{priceWeightPer,jdbcType=DECIMAL}, #{icmStatus,jdbcType=VARCHAR},
    #{remark,jdbcType=VARCHAR}, #{tax,jdbcType=DECIMAL}, #{currencyrate,jdbcType=DECIMAL}
    )
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.InternalCostMap" >
    insert into INTERNAL_COST_MAP
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="soCode != null" >
        SO_CODE,
      </if>
      <if test="sacIdPay != null" >
        SAC_ID_PAY,
      </if>
      <if test="sacIdRec != null" >
        SAC_ID_REC,
      </if>
      <if test="spCodePay != null" >
        SP_CODE_PAY,
      </if>
      <if test="soCodeRec != null" >
        SO_CODE_REC,
      </if>
      <if test="billingType != null" >
        BILLING_TYPE,
      </if>
      <if test="ctCode != null" >
        CT_CODE,
      </if>
      <if test="pdSyscode != null" >
        PD_SYSCODE,
      </if>
      <if test="dmType != null" >
        DM_TYPE,
      </if>
      <if test="businessCode != null" >
        BUSINESS_CODE,
      </if>
      <if test="priceId != null" >
        PRICE_ID,
      </if>
      <if test="pricePiecesPer != null" >
        PRICE_PIECES_PER,
      </if>
      <if test="priceWeightPer != null" >
        PRICE_WEIGHT_PER,
      </if>
      <if test="icmStatus != null" >
        ICM_STATUS,
      </if>
      <if test="remark != null" >
        REMARK,
      </if>
      <if test="tax != null" >
        TAX,
      </if>
      <if test="currencyrate != null" >
        CURRENCYRATE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="soCode != null" >
        #{soCode,jdbcType=VARCHAR},
      </if>
      <if test="sacIdPay != null" >
        #{sacIdPay,jdbcType=VARCHAR},
      </if>
      <if test="sacIdRec != null" >
        #{sacIdRec,jdbcType=VARCHAR},
      </if>
      <if test="spCodePay != null" >
        #{spCodePay,jdbcType=VARCHAR},
      </if>
      <if test="soCodeRec != null" >
        #{soCodeRec,jdbcType=VARCHAR},
      </if>
      <if test="billingType != null" >
        #{billingType,jdbcType=VARCHAR},
      </if>
      <if test="ctCode != null" >
        #{ctCode,jdbcType=VARCHAR},
      </if>
      <if test="pdSyscode != null" >
        #{pdSyscode,jdbcType=DECIMAL},
      </if>
      <if test="dmType != null" >
        #{dmType,jdbcType=VARCHAR},
      </if>
      <if test="businessCode != null" >
        #{businessCode,jdbcType=VARCHAR},
      </if>
      <if test="priceId != null" >
        #{priceId,jdbcType=VARCHAR},
      </if>
      <if test="pricePiecesPer != null" >
        #{pricePiecesPer,jdbcType=DECIMAL},
      </if>
      <if test="priceWeightPer != null" >
        #{priceWeightPer,jdbcType=DECIMAL},
      </if>
      <if test="icmStatus != null" >
        #{icmStatus,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="tax != null" >
        #{tax,jdbcType=DECIMAL},
      </if>
      <if test="currencyrate != null" >
        #{currencyrate,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.InternalCostMap" >
    update INTERNAL_COST_MAP
    <set >
      <if test="soCode != null" >
        SO_CODE = #{soCode,jdbcType=VARCHAR},
      </if>
      <if test="sacIdPay != null" >
        SAC_ID_PAY = #{sacIdPay,jdbcType=VARCHAR},
      </if>
      <if test="sacIdRec != null" >
        SAC_ID_REC = #{sacIdRec,jdbcType=VARCHAR},
      </if>
      <if test="spCodePay != null" >
        SP_CODE_PAY = #{spCodePay,jdbcType=VARCHAR},
      </if>
      <if test="soCodeRec != null" >
        SO_CODE_REC = #{soCodeRec,jdbcType=VARCHAR},
      </if>
      <if test="billingType != null" >
        BILLING_TYPE = #{billingType,jdbcType=VARCHAR},
      </if>
      <if test="ctCode != null" >
        CT_CODE = #{ctCode,jdbcType=VARCHAR},
      </if>
      <if test="pdSyscode != null" >
        PD_SYSCODE = #{pdSyscode,jdbcType=DECIMAL},
      </if>
      <if test="dmType != null" >
        DM_TYPE = #{dmType,jdbcType=VARCHAR},
      </if>
      <if test="businessCode != null" >
        BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR},
      </if>
      <if test="priceId != null" >
        PRICE_ID = #{priceId,jdbcType=VARCHAR},
      </if>
      <if test="pricePiecesPer != null" >
        PRICE_PIECES_PER = #{pricePiecesPer,jdbcType=DECIMAL},
      </if>
      <if test="priceWeightPer != null" >
        PRICE_WEIGHT_PER = #{priceWeightPer,jdbcType=DECIMAL},
      </if>
      <if test="icmStatus != null" >
        ICM_STATUS = #{icmStatus,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="tax != null" >
        TAX = #{tax,jdbcType=DECIMAL},
      </if>
      <if test="currencyrate != null" >
        CURRENCYRATE = #{currencyrate,jdbcType=DECIMAL},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.InternalCostMap" >
    update INTERNAL_COST_MAP
    set SO_CODE = #{soCode,jdbcType=VARCHAR},
    SAC_ID_PAY = #{sacIdPay,jdbcType=VARCHAR},
    SAC_ID_REC = #{sacIdRec,jdbcType=VARCHAR},
    SP_CODE_PAY = #{spCodePay,jdbcType=VARCHAR},
    SO_CODE_REC = #{soCodeRec,jdbcType=VARCHAR},
    BILLING_TYPE = #{billingType,jdbcType=VARCHAR},
    CT_CODE = #{ctCode,jdbcType=VARCHAR},
    PD_SYSCODE = #{pdSyscode,jdbcType=DECIMAL},
    DM_TYPE = #{dmType,jdbcType=VARCHAR},
    BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR},
    PRICE_ID = #{priceId,jdbcType=VARCHAR},
    PRICE_PIECES_PER = #{pricePiecesPer,jdbcType=DECIMAL},
    PRICE_WEIGHT_PER = #{priceWeightPer,jdbcType=DECIMAL},
    ICM_STATUS = #{icmStatus,jdbcType=VARCHAR},
    REMARK = #{remark,jdbcType=VARCHAR},
    TAX = #{tax,jdbcType=DECIMAL},
    CURRENCYRATE = #{currencyrate,jdbcType=DECIMAL}
    where ID = #{id,jdbcType=DECIMAL}
  </update>

  <select id="selectRecord" resultMap="BaseResultMap">
    select * from INTERNAL_COST_MAP
    where ICM_STATUS = 'ON'
    and SAC_ID_PAY = #{sacIdPay}
    and SO_CODE = #{soCode}
  </select>

  <select id="selectRecordBySoCode" resultMap="BaseResultMap">
    select * from INTERNAL_COST_MAP
    where ICM_STATUS = 'ON'
    and SO_CODE = #{soCode}
  </select>
</mapper>