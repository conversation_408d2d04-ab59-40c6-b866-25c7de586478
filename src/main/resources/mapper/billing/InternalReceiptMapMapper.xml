<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.InternalReceiptMapMapper">
    <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.InternalReceiptMap">
        <id column="IRM_ID" property="irmId" jdbcType="DECIMAL"/>
        <result column="SP_CODE" property="spCode" jdbcType="VARCHAR"/>
        <result column="SAC_ID_PAY" property="sacIdPay" jdbcType="VARCHAR"/>
        <result column="SAC_ID_REC" property="sacIdRec" jdbcType="VARCHAR"/>
        <result column="SP_CODE_PAY" property="spCodePay" jdbcType="VARCHAR"/>
        <result column="SO_CODE_REC" property="soCodeRec" jdbcType="VARCHAR"/>
        <result column="CONFIRM_TYPE" property="confirmType" jdbcType="VARCHAR"/>
        <result column="CT_CODE" property="ctCode" jdbcType="VARCHAR"/>
        <result column="CM_TYPE" property="cmType" jdbcType="VARCHAR"/>
        <result column="DM_TYPE" property="dmType" jdbcType="VARCHAR"/>
        <result column="IRM_STATUS" property="irmStatus" jdbcType="VARCHAR"/>
        <result column="REMARK" property="remark" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
    IRM_ID, SP_CODE, SAC_ID_PAY, SAC_ID_REC, SP_CODE_PAY, SO_CODE_REC, CONFIRM_TYPE, 
    CT_CODE, CM_TYPE, DM_TYPE, IRM_STATUS, REMARK
  </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Short">
        select
        <include refid="Base_Column_List"/>
        from INTERNAL_RECEIPT_MAP
        where IRM_ID = #{irmId,jdbcType=DECIMAL}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Short">
    delete from INTERNAL_RECEIPT_MAP
    where IRM_ID = #{irmId,jdbcType=DECIMAL}
  </delete>
    <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.InternalReceiptMap">
    insert into INTERNAL_RECEIPT_MAP (IRM_ID, SP_CODE, SAC_ID_PAY, 
      SAC_ID_REC, SP_CODE_PAY, SO_CODE_REC, 
      CONFIRM_TYPE, CT_CODE, CM_TYPE, 
      DM_TYPE, IRM_STATUS, REMARK
      )
    values (#{irmId,jdbcType=DECIMAL}, #{spCode,jdbcType=VARCHAR}, #{sacIdPay,jdbcType=VARCHAR}, 
      #{sacIdRec,jdbcType=VARCHAR}, #{spCodePay,jdbcType=VARCHAR}, #{soCodeRec,jdbcType=VARCHAR}, 
      #{confirmType,jdbcType=VARCHAR}, #{ctCode,jdbcType=VARCHAR}, #{cmType,jdbcType=VARCHAR}, 
      #{dmType,jdbcType=VARCHAR}, #{irmStatus,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}
      )
  </insert>
    <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.InternalReceiptMap">
        insert into INTERNAL_RECEIPT_MAP
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="irmId != null">
                IRM_ID,
            </if>
            <if test="spCode != null">
                SP_CODE,
            </if>
            <if test="sacIdPay != null">
                SAC_ID_PAY,
            </if>
            <if test="sacIdRec != null">
                SAC_ID_REC,
            </if>
            <if test="spCodePay != null">
                SP_CODE_PAY,
            </if>
            <if test="soCodeRec != null">
                SO_CODE_REC,
            </if>
            <if test="confirmType != null">
                CONFIRM_TYPE,
            </if>
            <if test="ctCode != null">
                CT_CODE,
            </if>
            <if test="cmType != null">
                CM_TYPE,
            </if>
            <if test="dmType != null">
                DM_TYPE,
            </if>
            <if test="irmStatus != null">
                IRM_STATUS,
            </if>
            <if test="remark != null">
                REMARK,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="irmId != null">
                #{irmId,jdbcType=DECIMAL},
            </if>
            <if test="spCode != null">
                #{spCode,jdbcType=VARCHAR},
            </if>
            <if test="sacIdPay != null">
                #{sacIdPay,jdbcType=VARCHAR},
            </if>
            <if test="sacIdRec != null">
                #{sacIdRec,jdbcType=VARCHAR},
            </if>
            <if test="spCodePay != null">
                #{spCodePay,jdbcType=VARCHAR},
            </if>
            <if test="soCodeRec != null">
                #{soCodeRec,jdbcType=VARCHAR},
            </if>
            <if test="confirmType != null">
                #{confirmType,jdbcType=VARCHAR},
            </if>
            <if test="ctCode != null">
                #{ctCode,jdbcType=VARCHAR},
            </if>
            <if test="cmType != null">
                #{cmType,jdbcType=VARCHAR},
            </if>
            <if test="dmType != null">
                #{dmType,jdbcType=VARCHAR},
            </if>
            <if test="irmStatus != null">
                #{irmStatus,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.InternalReceiptMap">
        update INTERNAL_RECEIPT_MAP
        <set>
            <if test="spCode != null">
                SP_CODE = #{spCode,jdbcType=VARCHAR},
            </if>
            <if test="sacIdPay != null">
                SAC_ID_PAY = #{sacIdPay,jdbcType=VARCHAR},
            </if>
            <if test="sacIdRec != null">
                SAC_ID_REC = #{sacIdRec,jdbcType=VARCHAR},
            </if>
            <if test="spCodePay != null">
                SP_CODE_PAY = #{spCodePay,jdbcType=VARCHAR},
            </if>
            <if test="soCodeRec != null">
                SO_CODE_REC = #{soCodeRec,jdbcType=VARCHAR},
            </if>
            <if test="confirmType != null">
                CONFIRM_TYPE = #{confirmType,jdbcType=VARCHAR},
            </if>
            <if test="ctCode != null">
                CT_CODE = #{ctCode,jdbcType=VARCHAR},
            </if>
            <if test="cmType != null">
                CM_TYPE = #{cmType,jdbcType=VARCHAR},
            </if>
            <if test="dmType != null">
                DM_TYPE = #{dmType,jdbcType=VARCHAR},
            </if>
            <if test="irmStatus != null">
                IRM_STATUS = #{irmStatus,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                REMARK = #{remark,jdbcType=VARCHAR},
            </if>
        </set>
        where IRM_ID = #{irmId,jdbcType=DECIMAL}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.InternalReceiptMap">
    update INTERNAL_RECEIPT_MAP
    set SP_CODE = #{spCode,jdbcType=VARCHAR},
      SAC_ID_PAY = #{sacIdPay,jdbcType=VARCHAR},
      SAC_ID_REC = #{sacIdRec,jdbcType=VARCHAR},
      SP_CODE_PAY = #{spCodePay,jdbcType=VARCHAR},
      SO_CODE_REC = #{soCodeRec,jdbcType=VARCHAR},
      CONFIRM_TYPE = #{confirmType,jdbcType=VARCHAR},
      CT_CODE = #{ctCode,jdbcType=VARCHAR},
      CM_TYPE = #{cmType,jdbcType=VARCHAR},
      DM_TYPE = #{dmType,jdbcType=VARCHAR},
      IRM_STATUS = #{irmStatus,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR}
    where IRM_ID = #{irmId,jdbcType=DECIMAL}
  </update>

    <select id="selectIsExsite" resultType="com.sinoair.billing.domain.model.billing.InternalReceiptMap">
    select * from INTERNAL_RECEIPT_MAP irm
    where irm.SP_CODE = #{spCode}
    and irm.SAC_ID_PAY=#{companyId}
    and irm.IRM_STATUS='ON'
        <if test="cmType != null">
            and irm.CM_TYPE=#{cmType}
        </if>
  </select>
</mapper>