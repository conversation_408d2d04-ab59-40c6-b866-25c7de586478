<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.KanBanConfigMapper" >
    <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.KanBanConfig" >
        <id column="ID" property="id" jdbcType="DECIMAL" />
        <result column="PROJECT_CODE" property="projectCode" jdbcType="DECIMAL" />
        <result column="TITLE_EN" property="titleEn" jdbcType="VARCHAR" />
        <result column="TITLE_ZH" property="titleZh" jdbcType="VARCHAR" />
        <result column="STATUS" property="status" jdbcType="VARCHAR" />
        <result column="TYPE" property="type" jdbcType="DECIMAL" />
    </resultMap>
    <resultMap id="ResultMapWithBLOBs" type="com.sinoair.billing.domain.model.billing.KanBanConfigWithBLOBs" extends="BaseResultMap" >
        <result column="HEAD_CONFIG" property="headConfig" jdbcType="CLOB" />
        <result column="SQL_CONFIG" property="sqlConfig" jdbcType="CLOB" />
        <result column="WHERE_CONFIG" property="whereConfig" jdbcType="CLOB" />
    </resultMap>

    <!-- 序列 -->
    <sql id='TABLE_SEQUENCE'>SEQ_KANBAN_CONFIG.NEXTVAL</sql>

    <sql id="Base_Column_List" >
        ID, PROJECT_CODE, TITLE_EN, TITLE_ZH, STATUS, TYPE
    </sql>
    <sql id="Blob_Column_List" >
        HEAD_CONFIG, SQL_CONFIG, WHERE_CONFIG
    </sql>
    <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Integer" >
        select
        <include refid="Base_Column_List" />
        ,
        <include refid="Blob_Column_List" />
        from KANBAN_CONFIG
        where ID = #{id,jdbcType=DECIMAL}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
        delete from KANBAN_CONFIG
        where ID = #{id,jdbcType=DECIMAL}
    </delete>
    <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.KanBanConfigWithBLOBs" >

        <selectKey keyProperty="id" resultType="int" order="BEFORE">
            select <include refid="TABLE_SEQUENCE" /> from dual
        </selectKey>

        insert into KANBAN_CONFIG (ID, PROJECT_CODE, TITLE_EN,
        TITLE_ZH, STATUS, TYPE,
        HEAD_CONFIG, SQL_CONFIG, WHERE_CONFIG
        )
        values (#{id,jdbcType=DECIMAL}, #{projectCode,jdbcType=DECIMAL}, #{titleEn,jdbcType=VARCHAR},
        #{titleZh,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{type,jdbcType=DECIMAL},
        #{headConfig,jdbcType=CLOB}, #{sqlConfig,jdbcType=CLOB}, #{whereConfig,jdbcType=CLOB}
        )
    </insert>
    <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.KanBanConfigWithBLOBs" >

        <selectKey keyProperty="id" resultType="int" order="BEFORE">
            select <include refid="TABLE_SEQUENCE" /> from dual
        </selectKey>

        insert into KANBAN_CONFIG
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                ID,
            </if>
            <if test="projectCode != null" >
                PROJECT_CODE,
            </if>
            <if test="titleEn != null" >
                TITLE_EN,
            </if>
            <if test="titleZh != null" >
                TITLE_ZH,
            </if>
            <if test="status != null" >
                STATUS,
            </if>
            <if test="type != null" >
                TYPE,
            </if>
            <if test="headConfig != null" >
                HEAD_CONFIG,
            </if>
            <if test="sqlConfig != null" >
                SQL_CONFIG,
            </if>
            <if test="whereConfig != null" >
                WHERE_CONFIG,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=DECIMAL},
            </if>
            <if test="projectCode != null" >
                #{projectCode,jdbcType=DECIMAL},
            </if>
            <if test="titleEn != null" >
                #{titleEn,jdbcType=VARCHAR},
            </if>
            <if test="titleZh != null" >
                #{titleZh,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="type != null" >
                #{type,jdbcType=DECIMAL},
            </if>
            <if test="headConfig != null" >
                #{headConfig,jdbcType=CLOB},
            </if>
            <if test="sqlConfig != null" >
                #{sqlConfig,jdbcType=CLOB},
            </if>
            <if test="whereConfig != null" >
                #{whereConfig,jdbcType=CLOB},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.KanBanConfigWithBLOBs" >
        update KANBAN_CONFIG
        <set >
            <if test="projectCode != null" >
                PROJECT_CODE = #{projectCode,jdbcType=DECIMAL},
            </if>
            <if test="titleEn != null" >
                TITLE_EN = #{titleEn,jdbcType=VARCHAR},
            </if>
            <if test="titleZh != null" >
                TITLE_ZH = #{titleZh,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                STATUS = #{status,jdbcType=VARCHAR},
            </if>
            <if test="type != null" >
                TYPE = #{type,jdbcType=DECIMAL},
            </if>
            <if test="headConfig != null" >
                HEAD_CONFIG = #{headConfig,jdbcType=CLOB},
            </if>
            <if test="sqlConfig != null" >
                SQL_CONFIG = #{sqlConfig,jdbcType=CLOB},
            </if>
            <if test="whereConfig != null" >
                WHERE_CONFIG = #{whereConfig,jdbcType=CLOB},
            </if>
        </set>
        where ID = #{id,jdbcType=DECIMAL}
    </update>
    <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sinoair.billing.domain.model.billing.KanBanConfigWithBLOBs" >
        update KANBAN_CONFIG
        set PROJECT_CODE = #{projectCode,jdbcType=DECIMAL},
        TITLE_EN = #{titleEn,jdbcType=VARCHAR},
        TITLE_ZH = #{titleZh,jdbcType=VARCHAR},
        STATUS = #{status,jdbcType=VARCHAR},
        TYPE = #{type,jdbcType=DECIMAL},
        HEAD_CONFIG = #{headConfig,jdbcType=CLOB},
        SQL_CONFIG = #{sqlConfig,jdbcType=CLOB},
        WHERE_CONFIG = #{whereConfig,jdbcType=CLOB}
        where ID = #{id,jdbcType=DECIMAL}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.KanBanConfig" >
        update KANBAN_CONFIG
        set PROJECT_CODE = #{projectCode,jdbcType=DECIMAL},
        TITLE_EN = #{titleEn,jdbcType=VARCHAR},
        TITLE_ZH = #{titleZh,jdbcType=VARCHAR},
        STATUS = #{status,jdbcType=VARCHAR},
        TYPE = #{type,jdbcType=DECIMAL}
        where ID = #{id,jdbcType=DECIMAL}
    </update>

    <select id="executeSqlQuery" resultType="java.util.HashMap" parameterType="java.lang.String">
        ${sql}
    </select>
</mapper>