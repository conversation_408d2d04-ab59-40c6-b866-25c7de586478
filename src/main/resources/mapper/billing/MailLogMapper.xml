<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.MailLogMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.MailLog" >
    <id column="LOG_ID" property="logId" jdbcType="DECIMAL" />
    <result column="LOG_TYPE" property="logType" jdbcType="VARCHAR" />
    <result column="LOG_BATCH_ID" property="logBatchId" jdbcType="DECIMAL" />
    <result column="MAIL_SYSTEM" property="mailSystem" jdbcType="VARCHAR" />
    <result column="MAIL_MODULE" property="mailModule" jdbcType="VARCHAR" />
    <result column="MAIL_SENDER" property="mailSender" jdbcType="VARCHAR" />
    <result column="MAIL_RECEIVER" property="mailReceiver" jdbcType="VARCHAR" />
    <result column="MAIL_SUBJECT" property="mailSubject" jdbcType="VARCHAR" />
    <result column="MAIL_STATUS" property="mailStatus" jdbcType="VARCHAR" />
    <result column="PUSH_URL" property="pushUrl" jdbcType="VARCHAR" />
    <result column="PUSH_PARAM" property="pushParam" jdbcType="VARCHAR" />
    <result column="PUSH_RESULT" property="pushResult" jdbcType="VARCHAR" />
    <result column="PUSH_STATUS" property="pushStatus" jdbcType="VARCHAR" />
    <result column="PUSH_TIME" property="pushTime" jdbcType="TIMESTAMP" />
    <result column="LOG_HANDLETIME" property="logHandletime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    LOG_ID, LOG_TYPE, LOG_BATCH_ID, MAIL_SYSTEM, MAIL_MODULE, MAIL_SENDER, MAIL_RECEIVER, 
    MAIL_SUBJECT, MAIL_STATUS, PUSH_URL, PUSH_PARAM, PUSH_RESULT, PUSH_STATUS, PUSH_TIME, 
    LOG_HANDLETIME
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.math.BigDecimal" >
    select 
    <include refid="Base_Column_List" />
    from MAIL_LOG
    where LOG_ID = #{logId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.math.BigDecimal" >
    delete from MAIL_LOG
    where LOG_ID = #{logId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.MailLog" >
    insert into MAIL_LOG (LOG_ID, LOG_TYPE, LOG_BATCH_ID, 
      MAIL_SYSTEM, MAIL_MODULE, MAIL_SENDER, 
      MAIL_RECEIVER, MAIL_SUBJECT, MAIL_STATUS, 
      PUSH_URL, PUSH_PARAM, PUSH_RESULT, 
      PUSH_STATUS, PUSH_TIME, LOG_HANDLETIME
      )
    values (#{logId,jdbcType=DECIMAL}, #{logType,jdbcType=VARCHAR}, #{logBatchId,jdbcType=DECIMAL}, 
      #{mailSystem,jdbcType=VARCHAR}, #{mailModule,jdbcType=VARCHAR}, #{mailSender,jdbcType=VARCHAR}, 
      #{mailReceiver,jdbcType=VARCHAR}, #{mailSubject,jdbcType=VARCHAR}, #{mailStatus,jdbcType=VARCHAR}, 
      #{pushUrl,jdbcType=VARCHAR}, #{pushParam,jdbcType=VARCHAR}, #{pushResult,jdbcType=VARCHAR}, 
      #{pushStatus,jdbcType=VARCHAR}, #{pushTime,jdbcType=TIMESTAMP}, #{logHandletime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.MailLog" >
    insert into MAIL_LOG
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="logId != null" >
        LOG_ID,
      </if>
      <if test="logType != null" >
        LOG_TYPE,
      </if>
      <if test="logBatchId != null" >
        LOG_BATCH_ID,
      </if>
      <if test="mailSystem != null" >
        MAIL_SYSTEM,
      </if>
      <if test="mailModule != null" >
        MAIL_MODULE,
      </if>
      <if test="mailSender != null" >
        MAIL_SENDER,
      </if>
      <if test="mailReceiver != null" >
        MAIL_RECEIVER,
      </if>
      <if test="mailSubject != null" >
        MAIL_SUBJECT,
      </if>
      <if test="mailStatus != null" >
        MAIL_STATUS,
      </if>
      <if test="pushUrl != null" >
        PUSH_URL,
      </if>
      <if test="pushParam != null" >
        PUSH_PARAM,
      </if>
      <if test="pushResult != null" >
        PUSH_RESULT,
      </if>
      <if test="pushStatus != null" >
        PUSH_STATUS,
      </if>
      <if test="pushTime != null" >
        PUSH_TIME,
      </if>
      <if test="logHandletime != null" >
        LOG_HANDLETIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="logId != null" >
        #{logId,jdbcType=DECIMAL},
      </if>
      <if test="logType != null" >
        #{logType,jdbcType=VARCHAR},
      </if>
      <if test="logBatchId != null" >
        #{logBatchId,jdbcType=DECIMAL},
      </if>
      <if test="mailSystem != null" >
        #{mailSystem,jdbcType=VARCHAR},
      </if>
      <if test="mailModule != null" >
        #{mailModule,jdbcType=VARCHAR},
      </if>
      <if test="mailSender != null" >
        #{mailSender,jdbcType=VARCHAR},
      </if>
      <if test="mailReceiver != null" >
        #{mailReceiver,jdbcType=VARCHAR},
      </if>
      <if test="mailSubject != null" >
        #{mailSubject,jdbcType=VARCHAR},
      </if>
      <if test="mailStatus != null" >
        #{mailStatus,jdbcType=VARCHAR},
      </if>
      <if test="pushUrl != null" >
        #{pushUrl,jdbcType=VARCHAR},
      </if>
      <if test="pushParam != null" >
        #{pushParam,jdbcType=VARCHAR},
      </if>
      <if test="pushResult != null" >
        #{pushResult,jdbcType=VARCHAR},
      </if>
      <if test="pushStatus != null" >
        #{pushStatus,jdbcType=VARCHAR},
      </if>
      <if test="pushTime != null" >
        #{pushTime,jdbcType=TIMESTAMP},
      </if>
      <if test="logHandletime != null" >
        #{logHandletime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.MailLog" >
    update MAIL_LOG
    <set >
      <if test="logType != null" >
        LOG_TYPE = #{logType,jdbcType=VARCHAR},
      </if>
      <if test="logBatchId != null" >
        LOG_BATCH_ID = #{logBatchId,jdbcType=DECIMAL},
      </if>
      <if test="mailSystem != null" >
        MAIL_SYSTEM = #{mailSystem,jdbcType=VARCHAR},
      </if>
      <if test="mailModule != null" >
        MAIL_MODULE = #{mailModule,jdbcType=VARCHAR},
      </if>
      <if test="mailSender != null" >
        MAIL_SENDER = #{mailSender,jdbcType=VARCHAR},
      </if>
      <if test="mailReceiver != null" >
        MAIL_RECEIVER = #{mailReceiver,jdbcType=VARCHAR},
      </if>
      <if test="mailSubject != null" >
        MAIL_SUBJECT = #{mailSubject,jdbcType=VARCHAR},
      </if>
      <if test="mailStatus != null" >
        MAIL_STATUS = #{mailStatus,jdbcType=VARCHAR},
      </if>
      <if test="pushUrl != null" >
        PUSH_URL = #{pushUrl,jdbcType=VARCHAR},
      </if>
      <if test="pushParam != null" >
        PUSH_PARAM = #{pushParam,jdbcType=VARCHAR},
      </if>
      <if test="pushResult != null" >
        PUSH_RESULT = #{pushResult,jdbcType=VARCHAR},
      </if>
      <if test="pushStatus != null" >
        PUSH_STATUS = #{pushStatus,jdbcType=VARCHAR},
      </if>
      <if test="pushTime != null" >
        PUSH_TIME = #{pushTime,jdbcType=TIMESTAMP},
      </if>
      <if test="logHandletime != null" >
        LOG_HANDLETIME = #{logHandletime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where LOG_ID = #{logId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.MailLog" >
    update MAIL_LOG
    set LOG_TYPE = #{logType,jdbcType=VARCHAR},
      LOG_BATCH_ID = #{logBatchId,jdbcType=DECIMAL},
      MAIL_SYSTEM = #{mailSystem,jdbcType=VARCHAR},
      MAIL_MODULE = #{mailModule,jdbcType=VARCHAR},
      MAIL_SENDER = #{mailSender,jdbcType=VARCHAR},
      MAIL_RECEIVER = #{mailReceiver,jdbcType=VARCHAR},
      MAIL_SUBJECT = #{mailSubject,jdbcType=VARCHAR},
      MAIL_STATUS = #{mailStatus,jdbcType=VARCHAR},
      PUSH_URL = #{pushUrl,jdbcType=VARCHAR},
      PUSH_PARAM = #{pushParam,jdbcType=VARCHAR},
      PUSH_RESULT = #{pushResult,jdbcType=VARCHAR},
      PUSH_STATUS = #{pushStatus,jdbcType=VARCHAR},
      PUSH_TIME = #{pushTime,jdbcType=TIMESTAMP},
      LOG_HANDLETIME = #{logHandletime,jdbcType=TIMESTAMP}
    where LOG_ID = #{logId,jdbcType=DECIMAL}
  </update>
  <select id="selectToSendList" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from MAIL_LOG
    where MAIL_STATUS = 'N'
    ORDER BY LOG_HANDLETIME DESC
  </select>
</mapper>