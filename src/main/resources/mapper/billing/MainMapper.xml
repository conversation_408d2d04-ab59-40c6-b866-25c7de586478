<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.billing.dao.billing.MainMapper">
    <select id="checkDb" resultType="java.lang.String">
        select 1 from dual
    </select>
    <select id="selectDbTableSpace" resultType="com.sinoair.billing.domain.vo.system.DBTableSpaceVO">
        SELECT a.tablespace_name "tablespaceName",
               total "total",
               free "free",
               (total - free) "used",
               total / (1024 * 1024 * 1024) "totalG",
               free / (1024 * 1024 * 1024) "freeG",
               (total - free) / (1024 * 1024 * 1024) "usedG",
               round((total - free) / total, 4) * 100 "round"
        FROM (SELECT tablespace_name, SUM(bytes) free
              FROM dba_free_space
              GROUP BY tablespace_name) a,
             (SELECT tablespace_name, SUM(bytes) total
              FROM dba_data_files
              GROUP BY tablespace_name) b
        WHERE a.tablespace_name = b.tablespace_name
          and a.tablespace_name = 'SINOAIR'
    </select>
</mapper>