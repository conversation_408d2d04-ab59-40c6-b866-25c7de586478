<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.ManifestEawbMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.ManifestEawb" >
    <id column="DM_ID" property="dmId" jdbcType="DECIMAL" />
    <result column="EAWB_PRINTCODE" property="eawbPrintcode" jdbcType="VARCHAR" />
    <result column="M_ID" property="mId" jdbcType="DECIMAL" />
    <result column="STATUS_TIME" property="statusTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    DM_ID, EAWB_PRINTCODE,M_ID,STATUS_TIME
  </sql>

  <select id="selectManifestEawb" resultMap="BaseResultMap"  >
    select
    <include refid="Base_Column_List" />
    from MANIFEST_EAWB
  </select>

  <delete id="deleteBatchByEawb" parameterType="java.util.List" >
    delete from MANIFEST_EAWB
    where EAWB_PRINTCODE in
    <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
      #{item.eawbPrintcode,jdbcType=DECIMAL}
    </foreach>
  </delete>

  <insert id="insertByDmId" >
    insert into MANIFEST_EAWB (eawb_printcode,dm_id,m_id,status_time)
    select eawb_printcode,dm_id,#{mId,jdbcType=DECIMAL},#{statusTime,jdbcType=TIMESTAMP} from receipt_record  where dm_id = #{dmId,jdbcType=DECIMAL}
  </insert>

    <select id="countManifestEawb" resultType="java.lang.Integer"  >
        select
        count(0)
        from MANIFEST_EAWB
    </select>

  <insert id="insertByTemporaryDmId" >
    insert into MANIFEST_EAWB (eawb_printcode,dm_id,m_id,status_time)
    select eawb_printcode,dmt.dm_id,#{mId,jdbcType=DECIMAL},#{statusTime,jdbcType=TIMESTAMP}
    from debit_manifest_temporary dmt,receipt_record rr
    where dmt.dm_temp_id = rr.dm_id
    and dmt.dm_id = #{dmId,jdbcType=DECIMAL}
  </insert>

</mapper>