<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.ManifestListMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.ManifestList" >
    <id column="M_ID" property="mId" jdbcType="DECIMAL" />
    <result column="M_CODE" property="mCode" jdbcType="VARCHAR" />
    <result column="M_STATUS" property="mStatus" jdbcType="VARCHAR" />
    <result column="M_RC_TYPE" property="mRcType" jdbcType="VARCHAR" />
    <result column="BT_CODE" property="btCode" jdbcType="VARCHAR" />
    <result column="COMPANY_ID" property="companyId" jdbcType="VARCHAR" />
    <result column="INVOICE_TYPE" property="invoiceType" jdbcType="VARCHAR" />
    <result column="INVOICE_NUM" property="invoiceNum" jdbcType="VARCHAR" />
    <result column="SO_CODE" property="soCode" jdbcType="VARCHAR" />
    <result column="SO_NAME" property="soName" jdbcType="VARCHAR" />
    <result column="CT_CODE" property="ctCode" jdbcType="VARCHAR" />
    <result column="CURRENCYRATE" property="currencyrate" jdbcType="DECIMAL" />
    <result column="SO_TAX" property="soTax" jdbcType="DECIMAL" />
    <result column="M_TOTALRMB" property="mTotalrmb" jdbcType="DECIMAL" />
    <result column="M_TOTALFC" property="mTotalfc" jdbcType="DECIMAL" />
    <result column="TAX_AMOUNT" property="taxAmount" jdbcType="DECIMAL" />
    <result column="NOTAX_AMOUNT" property="notaxAmount" jdbcType="DECIMAL" />
    <result column="TAX_AMOUNT_FC" property="taxAmountFc" jdbcType="DECIMAL" />
    <result column="NOTAX_AMOUNT_FC" property="notaxAmountFc" jdbcType="DECIMAL" />
    <result column="M_TOTAL_PIECES" property="mTotalPieces" jdbcType="DECIMAL" />
    <result column="M_TOTAL_WEIGHT" property="mTotalWeight" jdbcType="DECIMAL" />
    <result column="M_E_ID" property="mEId" jdbcType="DECIMAL" />
    <result column="M_HANDLETIME" property="mHandletime" jdbcType="TIMESTAMP" />
    <result column="M_TYPE" property="mType" jdbcType="VARCHAR" />
    <result column="M_REMARK" property="mRemark" jdbcType="VARCHAR" />
    <result column="M_CONFIRMTIME" property="mConfirmtime" jdbcType="TIMESTAMP" />
    <result column="IS_BMS_COLLECT" property="isBmsCollect" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    M_ID, M_CODE, M_STATUS, M_RC_TYPE, BT_CODE, COMPANY_ID, INVOICE_TYPE, INVOICE_NUM, 
    SO_CODE, SO_NAME, CT_CODE, CURRENCYRATE, SO_TAX, M_TOTALRMB, M_TOTALFC, TAX_AMOUNT, 
    NOTAX_AMOUNT, TAX_AMOUNT_FC, NOTAX_AMOUNT_FC, M_TOTAL_PIECES, M_TOTAL_WEIGHT, M_E_ID, 
    M_HANDLETIME, M_TYPE, M_REMARK,M_CONFIRMTIME,IS_BMS_COLLECT
  </sql>
  <sql id='TABLE_SEQUENCE'>SEQ_MANIFEST_LIST.NEXTVAL</sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from MANIFEST_LIST
    where M_ID = #{mId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from MANIFEST_LIST
    where M_ID = #{mId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.ManifestList" >
    <selectKey keyProperty="mId" resultType="long" order="BEFORE">
      select
      <include refid="TABLE_SEQUENCE"/>
      from dual
    </selectKey>
    insert into MANIFEST_LIST (M_ID, M_CODE, M_STATUS, 
      M_RC_TYPE, BT_CODE, COMPANY_ID, 
      INVOICE_TYPE, INVOICE_NUM, SO_CODE, 
      SO_NAME, CT_CODE, CURRENCYRATE, 
      SO_TAX, M_TOTALRMB, M_TOTALFC, 
      TAX_AMOUNT, NOTAX_AMOUNT, TAX_AMOUNT_FC, 
      NOTAX_AMOUNT_FC, M_TOTAL_PIECES, M_TOTAL_WEIGHT, 
      M_E_ID, M_HANDLETIME, M_TYPE, 
      M_REMARK,M_CONFIRMTIME,IS_BMS_COLLECT)
    values (#{mId,jdbcType=DECIMAL}, #{mCode,jdbcType=VARCHAR}, #{mStatus,jdbcType=VARCHAR}, 
      #{mRcType,jdbcType=VARCHAR}, #{btCode,jdbcType=VARCHAR}, #{companyId,jdbcType=VARCHAR}, 
      #{invoiceType,jdbcType=VARCHAR}, #{invoiceNum,jdbcType=VARCHAR}, #{soCode,jdbcType=VARCHAR}, 
      #{soName,jdbcType=VARCHAR}, #{ctCode,jdbcType=VARCHAR}, #{currencyrate,jdbcType=DECIMAL}, 
      #{soTax,jdbcType=DECIMAL}, #{mTotalrmb,jdbcType=DECIMAL}, #{mTotalfc,jdbcType=DECIMAL}, 
      #{taxAmount,jdbcType=DECIMAL}, #{notaxAmount,jdbcType=DECIMAL}, #{taxAmountFc,jdbcType=DECIMAL}, 
      #{notaxAmountFc,jdbcType=DECIMAL}, #{mTotalPieces,jdbcType=DECIMAL}, #{mTotalWeight,jdbcType=DECIMAL}, 
      #{mEId,jdbcType=DECIMAL}, #{mHandletime,jdbcType=TIMESTAMP}, #{mType,jdbcType=VARCHAR}, 
      #{mRemark,jdbcType=VARCHAR}, #{mConfirmtime,jdbcType=TIMESTAMP}, #{isBmsCollect,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.ManifestList" >
    <selectKey keyProperty="mId" resultType="long" order="BEFORE">
      select
      <include refid="TABLE_SEQUENCE"/>
      from dual
    </selectKey>
    insert into MANIFEST_LIST
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="mId != null" >
        M_ID,
      </if>
      <if test="mCode != null" >
        M_CODE,
      </if>
      <if test="mStatus != null" >
        M_STATUS,
      </if>
      <if test="mRcType != null" >
        M_RC_TYPE,
      </if>
      <if test="btCode != null" >
        BT_CODE,
      </if>
      <if test="companyId != null" >
        COMPANY_ID,
      </if>
      <if test="invoiceType != null" >
        INVOICE_TYPE,
      </if>
      <if test="invoiceNum != null" >
        INVOICE_NUM,
      </if>
      <if test="soCode != null" >
        SO_CODE,
      </if>
      <if test="soName != null" >
        SO_NAME,
      </if>
      <if test="ctCode != null" >
        CT_CODE,
      </if>
      <if test="currencyrate != null" >
        CURRENCYRATE,
      </if>
      <if test="soTax != null" >
        SO_TAX,
      </if>
      <if test="mTotalrmb != null" >
        M_TOTALRMB,
      </if>
      <if test="mTotalfc != null" >
        M_TOTALFC,
      </if>
      <if test="taxAmount != null" >
        TAX_AMOUNT,
      </if>
      <if test="notaxAmount != null" >
        NOTAX_AMOUNT,
      </if>
      <if test="taxAmountFc != null" >
        TAX_AMOUNT_FC,
      </if>
      <if test="notaxAmountFc != null" >
        NOTAX_AMOUNT_FC,
      </if>
      <if test="mTotalPieces != null" >
        M_TOTAL_PIECES,
      </if>
      <if test="mTotalWeight != null" >
        M_TOTAL_WEIGHT,
      </if>
      <if test="mEId != null" >
        M_E_ID,
      </if>
      <if test="mHandletime != null" >
        M_HANDLETIME,
      </if>
      <if test="mType != null" >
        M_TYPE,
      </if>
      <if test="mRemark != null" >
        M_REMARK,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="mId != null" >
        #{mId,jdbcType=DECIMAL},
      </if>
      <if test="mCode != null" >
        #{mCode,jdbcType=VARCHAR},
      </if>
      <if test="mStatus != null" >
        #{mStatus,jdbcType=VARCHAR},
      </if>
      <if test="mRcType != null" >
        #{mRcType,jdbcType=VARCHAR},
      </if>
      <if test="btCode != null" >
        #{btCode,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null" >
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="invoiceType != null" >
        #{invoiceType,jdbcType=VARCHAR},
      </if>
      <if test="invoiceNum != null" >
        #{invoiceNum,jdbcType=VARCHAR},
      </if>
      <if test="soCode != null" >
        #{soCode,jdbcType=VARCHAR},
      </if>
      <if test="soName != null" >
        #{soName,jdbcType=VARCHAR},
      </if>
      <if test="ctCode != null" >
        #{ctCode,jdbcType=VARCHAR},
      </if>
      <if test="currencyrate != null" >
        #{currencyrate,jdbcType=DECIMAL},
      </if>
      <if test="soTax != null" >
        #{soTax,jdbcType=DECIMAL},
      </if>
      <if test="mTotalrmb != null" >
        #{mTotalrmb,jdbcType=DECIMAL},
      </if>
      <if test="mTotalfc != null" >
        #{mTotalfc,jdbcType=DECIMAL},
      </if>
      <if test="taxAmount != null" >
        #{taxAmount,jdbcType=DECIMAL},
      </if>
      <if test="notaxAmount != null" >
        #{notaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="taxAmountFc != null" >
        #{taxAmountFc,jdbcType=DECIMAL},
      </if>
      <if test="notaxAmountFc != null" >
        #{notaxAmountFc,jdbcType=DECIMAL},
      </if>
      <if test="mTotalPieces != null" >
        #{mTotalPieces,jdbcType=DECIMAL},
      </if>
      <if test="mTotalWeight != null" >
        #{mTotalWeight,jdbcType=DECIMAL},
      </if>
      <if test="mEId != null" >
        #{mEId,jdbcType=DECIMAL},
      </if>
      <if test="mHandletime != null" >
        #{mHandletime,jdbcType=TIMESTAMP},
      </if>
      <if test="mType != null" >
        #{mType,jdbcType=VARCHAR},
      </if>
      <if test="mRemark != null" >
        #{mRemark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.ManifestList" >
    update MANIFEST_LIST
    <set >
      <if test="mCode != null" >
        M_CODE = #{mCode,jdbcType=VARCHAR},
      </if>
      <if test="mStatus != null" >
        M_STATUS = #{mStatus,jdbcType=VARCHAR},
      </if>
      <if test="mRcType != null" >
        M_RC_TYPE = #{mRcType,jdbcType=VARCHAR},
      </if>
      <if test="btCode != null" >
        BT_CODE = #{btCode,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null" >
        COMPANY_ID = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="invoiceType != null" >
        INVOICE_TYPE = #{invoiceType,jdbcType=VARCHAR},
      </if>
      <if test="invoiceNum != null" >
        INVOICE_NUM = #{invoiceNum,jdbcType=VARCHAR},
      </if>
      <if test="soCode != null" >
        SO_CODE = #{soCode,jdbcType=VARCHAR},
      </if>
      <if test="soName != null" >
        SO_NAME = #{soName,jdbcType=VARCHAR},
      </if>
      <if test="ctCode != null" >
        CT_CODE = #{ctCode,jdbcType=VARCHAR},
      </if>
      <if test="currencyrate != null" >
        CURRENCYRATE = #{currencyrate,jdbcType=DECIMAL},
      </if>
      <if test="soTax != null" >
        SO_TAX = #{soTax,jdbcType=DECIMAL},
      </if>
      <if test="mTotalrmb != null" >
        M_TOTALRMB = #{mTotalrmb,jdbcType=DECIMAL},
      </if>
      <if test="mTotalfc != null" >
        M_TOTALFC = #{mTotalfc,jdbcType=DECIMAL},
      </if>
      <if test="taxAmount != null" >
        TAX_AMOUNT = #{taxAmount,jdbcType=DECIMAL},
      </if>
      <if test="notaxAmount != null" >
        NOTAX_AMOUNT = #{notaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="taxAmountFc != null" >
        TAX_AMOUNT_FC = #{taxAmountFc,jdbcType=DECIMAL},
      </if>
      <if test="notaxAmountFc != null" >
        NOTAX_AMOUNT_FC = #{notaxAmountFc,jdbcType=DECIMAL},
      </if>
      <if test="mTotalPieces != null" >
        M_TOTAL_PIECES = #{mTotalPieces,jdbcType=DECIMAL},
      </if>
      <if test="mTotalWeight != null" >
        M_TOTAL_WEIGHT = #{mTotalWeight,jdbcType=DECIMAL},
      </if>
      <if test="mEId != null" >
        M_E_ID = #{mEId,jdbcType=DECIMAL},
      </if>
      <if test="mHandletime != null" >
        M_HANDLETIME = #{mHandletime,jdbcType=TIMESTAMP},
      </if>
      <if test="mType != null" >
        M_TYPE = #{mType,jdbcType=VARCHAR},
      </if>
      <if test="mRemark != null" >
        M_REMARK = #{mRemark,jdbcType=VARCHAR},
      </if>
      <if test="mConfirmtime != null" >
        M_CONFIRMTIME = #{mConfirmtime,jdbcType=TIMESTAMP},
      </if>
        <if test="isBmsCollect != null" >
            IS_BMS_COLLECT = #{isBmsCollect,jdbcType=TIMESTAMP},
        </if>
    </set>
    where M_ID = #{mId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.ManifestList" >
    update MANIFEST_LIST
    set M_CODE = #{mCode,jdbcType=VARCHAR},
      M_STATUS = #{mStatus,jdbcType=VARCHAR},
      M_RC_TYPE = #{mRcType,jdbcType=VARCHAR},
      BT_CODE = #{btCode,jdbcType=VARCHAR},
      COMPANY_ID = #{companyId,jdbcType=VARCHAR},
      INVOICE_TYPE = #{invoiceType,jdbcType=VARCHAR},
      INVOICE_NUM = #{invoiceNum,jdbcType=VARCHAR},
      SO_CODE = #{soCode,jdbcType=VARCHAR},
      SO_NAME = #{soName,jdbcType=VARCHAR},
      CT_CODE = #{ctCode,jdbcType=VARCHAR},
      CURRENCYRATE = #{currencyrate,jdbcType=DECIMAL},
      SO_TAX = #{soTax,jdbcType=DECIMAL},
      M_TOTALRMB = #{mTotalrmb,jdbcType=DECIMAL},
      M_TOTALFC = #{mTotalfc,jdbcType=DECIMAL},
      TAX_AMOUNT = #{taxAmount,jdbcType=DECIMAL},
      NOTAX_AMOUNT = #{notaxAmount,jdbcType=DECIMAL},
      TAX_AMOUNT_FC = #{taxAmountFc,jdbcType=DECIMAL},
      NOTAX_AMOUNT_FC = #{notaxAmountFc,jdbcType=DECIMAL},
      M_TOTAL_PIECES = #{mTotalPieces,jdbcType=DECIMAL},
      M_TOTAL_WEIGHT = #{mTotalWeight,jdbcType=DECIMAL},
      M_E_ID = #{mEId,jdbcType=DECIMAL},
      M_HANDLETIME = #{mHandletime,jdbcType=TIMESTAMP},
      M_TYPE = #{mType,jdbcType=VARCHAR},
      M_REMARK = #{mRemark,jdbcType=VARCHAR}
    where M_ID = #{mId,jdbcType=DECIMAL}
  </update>

  <select id="getManifestList" resultType="com.sinoair.billing.domain.vo.manifest.ManifestListVO"
          parameterType="com.sinoair.billing.domain.vo.FilterParam">
    select M_ID,
            M_CODE,
            M_STATUS,
            M_RC_TYPE,
            BT_CODE,
            m.COMPANY_ID,
            INVOICE_TYPE,
            INVOICE_NUM,
            m.SO_CODE,
      <if test='sdType =="R"'>
            stt.SO_NAME,
      </if>
      <if test='sdType =="C"'>
            s.SP_NAME as soName,
      </if>
            m.CT_CODE,
            CURRENCYRATE,
            SO_TAX,
            M_TOTALRMB,
            M_TOTALFC,
            TAX_AMOUNT,
            NOTAX_AMOUNT,
            TAX_AMOUNT_FC,
            NOTAX_AMOUNT_FC,
            M_TOTAL_PIECES,
            M_TOTAL_WEIGHT,
            M_E_ID,
            M_HANDLETIME,
            M_TYPE,
            M_REMARK,
            M_CONFIRMTIME,
            ct.CT_NAME,
           (SELECT count(0) from bill_manifest WHERE m_id = m.m_id) as manifestCount
    from MANIFEST_LIST m
      left join currencytype ct on m.ct_code = ct.ct_code
      <if test='sdType =="R"'>
          LEFT join settlementobject stt on m.so_code = stt.so_code
      </if>
      <if test='sdType =="C"'>
          LEFT join supplier s on m.so_code = s.sp_code
      </if>

    where 1 = 1
    and m.m_status in ('DRAFT','CONFIRM')
    and m.company_id = #{companyId}
    and m.M_RC_TYPE = #{sdType}
    <if test="code!=null and code!=''">
      and m.m_code = #{code}
    </if>
    <if test="status!=null and status!='ALL'">
      and m.m_status = #{status}
    </if>
    <if test="soCode!=null and soCode!=''">
      and m.so_code = #{soCode}
    </if>
    <if test="starttime!=null and endtime!=null and starttime!='' and endtime!=''">
      and (to_char(m.m_start_time, 'yyyy-mm-dd') >= #{starttime} and
      to_char(m.m_start_time, 'yyyy-mm-dd') &lt;= #{endtime})
    </if>
    <if test='sdType =="C"'>
      <if test="name != null and name !=''">
        and s.SP_NAME like '%'||#{name}||'%'
      </if>
    </if>

  </select>

    <select id="getDebitManifestSummary" resultType="com.sinoair.billing.domain.model.billing.ManifestList"
            parameterType="java.lang.Long">
        select m.m_id,
            nvl(sum(dm.dm_totalrmb),0) as mTotalrmb,
            nvl(sum(dm.dm_totalfc),0) as mTotalfc,
            nvl(sum(dm.tax_amount),0) as taxAmount,
            nvl(sum(dm.tax_amount_fc),0) as taxAmountFc,
            nvl(sum(dm.notax_amount),0) as notaxAmount,
            nvl(sum(dm.notax_amount_fc),0) as notaxAmountFc,
            nvl(sum(dm.dm_total_pieces),0) as mTotalPieces,
            nvl(sum(dm.dm_total_weight),0) as mTotalWeight
        from manifest_list m
        left join BILL_MANIFEST bm on m.m_id = bm.m_id
        left join debit_manifest dm on bm.dm_id = dm.dm_id
        where m.m_id = #{mId,jdbcType=DECIMAL}
        group by m.m_id

    </select>

  <select id="getCreditManifestSummary" resultType="com.sinoair.billing.domain.model.billing.ManifestList"
          parameterType="java.lang.Long">
    select m.m_id,
    nvl(sum(cm.cm_totalrmb),0) as mTotalrmb,
    nvl(sum(cm.cm_totalfc),0) as mTotalfc,
    nvl(sum(cm.tax_amount),0) as taxAmount,
    nvl(sum(cm.tax_amount_fc),0) as taxAmountFc,
    nvl(sum(cm.notax_amount),0) as notaxAmount,
    nvl(sum(cm.notax_amount_fc),0) as notaxAmountFc,
    nvl(sum(cm.cm_total_pieces),0) as mTotalPieces,
    nvl(sum(cm.cm_total_weight),0) as mTotalWeight
    from manifest_list m
    left join BILL_MANIFEST bm on m.m_id = bm.m_id
    left join credit_manifest cm on bm.cm_id = cm.cm_id
    where m.m_id = #{mId,jdbcType=DECIMAL}
    group by m.m_id

  </select>

  <select id="countDebitManifestDraft" resultType="java.lang.Integer"
          parameterType="java.lang.Long">
    select count(0) from manifest_list m,bill_manifest bm,debit_manifest dm
    where m.m_id = bm.m_id
    and bm.dm_id = dm.dm_id
    and dm.dm_status = 'DRAFT'
    and m.m_id = #{mId,jdbcType=DECIMAL}

  </select>

  <select id="countCreditManifestDraft" resultType="java.lang.Integer"
          parameterType="java.lang.Long">
    select count(0) from manifest_list m,bill_manifest bm,credit_manifest cm
    where m.m_id = bm.m_id
    and bm.cm_id = cm.cm_id
    and cm.cm_status = 'DRAFT'
    and m.m_id = #{mId,jdbcType=DECIMAL}

  </select>

  <select id="listManifest" resultType="com.sinoair.billing.domain.model.billing.ManifestList" >
    select
    M_ID, M_CODE, M_STATUS, M_RC_TYPE, BT_CODE, COMPANY_ID, INVOICE_TYPE, INVOICE_NUM,
    m.SO_CODE, so.SO_NAME, m.CT_CODE, CURRENCYRATE, SO_TAX, M_TOTALRMB, M_TOTALFC, TAX_AMOUNT,
    NOTAX_AMOUNT, TAX_AMOUNT_FC, NOTAX_AMOUNT_FC, M_TOTAL_PIECES, M_TOTAL_WEIGHT, M_E_ID,
    M_HANDLETIME, M_TYPE, M_REMARK,M_CONFIRMTIME,ct.ct_sign,IS_BMS_COLLECT
    from MANIFEST_LIST m,settlementobject so,currencytype ct
    where  m.so_code = so.so_code
    and m.ct_code = ct.ct_code
    and m.m_rc_type = 'R'
    and m_status = 'CONFIRM'
    and IS_BMS_COLLECT = 'N'
  </select>

  <select id="selectCollectBmsList" resultType="com.sinoair.billing.domain.model.billing.BmsManifest" parameterType="java.lang.Long" >
     select
             sop.sinotrans_id,  -- 外运号
             rr.ep_key,
             nvl(sum(rr.eawb_hawb_qty),1) as bmPiece, -- 统计值件数
             sum(rr.eawb_chargeableweight) as bmChargeableweight, -- 统计值计费重量
             sum(rr.rr_actual_amount) as receiptAmount  --实收金额

      from manifest_list ml,bill_manifest bm,debit_manifest dm,receipt_record rr,sinotrans_order_pool sop
      where ml.m_id = bm.m_id
        and bm.dm_id = dm.dm_id
        and dm.dm_id = rr.dm_id
        and rr.eawb_printcode = sop.eawb_printcode
        and ml.M_ID = #{mId,jdbcType=DECIMAL}
        group by sop.sinotrans_id,rr.ep_key
  </select>

  <select id="selectCreditCollectBmsList" resultType="com.sinoair.billing.domain.model.billing.BmsManifest" parameterType="java.lang.Long" >
    select
      sum(pr.pr_actual_amount) as rrActualAmount

    from manifest_list ml,bill_manifest bm,credit_manifest cm,payment_record pr
    where ml.m_id = bm.m_id
    and bm.cm_id = cm.cm_id
    and cm.cm_id = pr.cm_id
    and ml.M_ID = #{mId,jdbcType=DECIMAL}
  </select>

  <delete id="deleteByInit" >
    delete from MANIFEST_LIST
    where m_status = 'INIT'
  </delete>
</mapper>