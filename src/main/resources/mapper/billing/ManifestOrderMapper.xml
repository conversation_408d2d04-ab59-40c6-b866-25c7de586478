<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.ManifestOrderMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.ManifestOrder" >
    <id column="M_ID" property="mId" jdbcType="DECIMAL" />
    <result column="SYS_STATUS" property="sysStatus" jdbcType="VARCHAR" />
    <result column="THREAD_NUM" property="threadNum" jdbcType="DECIMAL" />
    <result column="HANDLE_NUM" property="handleNum" jdbcType="DECIMAL" />
    <result column="SYS_HANDLETIME" property="sysHandletime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    M_<PERSON>, SYS_STATUS, THREAD_NUM, HANDLE_NUM, SYS_HANDLETIME
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from MANIFEST_ORDER
    where M_ID = #{mId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from MANIFEST_ORDER
    where M_ID = #{mId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.ManifestOrder" >
    insert into MANIFEST_ORDER (M_ID, SYS_STATUS, THREAD_NUM, 
      HANDLE_NUM, SYS_HANDLETIME)
    values (#{mId,jdbcType=DECIMAL}, #{sysStatus,jdbcType=VARCHAR}, #{threadNum,jdbcType=DECIMAL}, 
      #{handleNum,jdbcType=DECIMAL}, #{sysHandletime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.ManifestOrder" >
    insert into MANIFEST_ORDER
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="mId != null" >
        M_ID,
      </if>
      <if test="sysStatus != null" >
        SYS_STATUS,
      </if>
      <if test="threadNum != null" >
        THREAD_NUM,
      </if>
      <if test="handleNum != null" >
        HANDLE_NUM,
      </if>
      <if test="sysHandletime != null" >
        SYS_HANDLETIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="mId != null" >
        #{mId,jdbcType=DECIMAL},
      </if>
      <if test="sysStatus != null" >
        #{sysStatus,jdbcType=VARCHAR},
      </if>
      <if test="threadNum != null" >
        #{threadNum,jdbcType=DECIMAL},
      </if>
      <if test="handleNum != null" >
        #{handleNum,jdbcType=DECIMAL},
      </if>
      <if test="sysHandletime != null" >
        #{sysHandletime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.ManifestOrder" >
    update MANIFEST_ORDER
    <set >
      <if test="sysStatus != null" >
        SYS_STATUS = #{sysStatus,jdbcType=VARCHAR},
      </if>
      <if test="threadNum != null" >
        THREAD_NUM = #{threadNum,jdbcType=DECIMAL},
      </if>
      <if test="handleNum != null" >
        HANDLE_NUM = #{handleNum,jdbcType=DECIMAL},
      </if>
      <if test="sysHandletime != null" >
        SYS_HANDLETIME = #{sysHandletime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where M_ID = #{mId,jdbcType=DECIMAL} and THREAD_NUM = #{threadNum,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.ManifestOrder" >
    update MANIFEST_ORDER
    set SYS_STATUS = #{sysStatus,jdbcType=VARCHAR},
      THREAD_NUM = #{threadNum,jdbcType=DECIMAL},
      HANDLE_NUM = #{handleNum,jdbcType=DECIMAL},
      SYS_HANDLETIME = #{sysHandletime,jdbcType=TIMESTAMP}
    where M_ID = #{mId,jdbcType=DECIMAL}
  </update>

  <select id="listManifestOrder" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select
    <include refid="Base_Column_List" />
    from MANIFEST_ORDER
    where SYS_STATUS = #{sysStatus,jdbcType=DECIMAL}
  </select>

  <select id="countManifestOrder" resultType="java.lang.Integer" parameterType="java.lang.String" >
    select
    count(0)
    from MANIFEST_ORDER
    where SYS_STATUS = #{sysStatus,jdbcType=DECIMAL}
  </select>
</mapper>