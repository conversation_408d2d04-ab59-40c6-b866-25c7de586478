<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.MenuMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.Menu" >
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="SORT_NUM" property="sortNum" jdbcType="DECIMAL" />
    <result column="PARENT_ID" property="parentId" jdbcType="DECIMAL" />
    <result column="NAME_ZH" property="nameZh" jdbcType="VARCHAR" />
    <result column="NAME_EN" property="nameEn" jdbcType="VARCHAR" />
    <result column="URL" property="url" jdbcType="VARCHAR" />
    <result column="DESCRIPTION" property="description" jdbcType="VARCHAR" />
    <result column="IMAGE" property="image" jdbcType="VARCHAR" />
    <result column="STATUS" property="status" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS
  </sql>

  <!-- 序列 -->
  <sql id='TABLE_SEQUENCE'>SEQ_MENU.NEXTVAL</sql>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from MENU
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from MENU
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.Menu" >
    <selectKey keyProperty="id" resultType="int" order="BEFORE">
      select <include refid="TABLE_SEQUENCE" /> from dual
    </selectKey>
    insert into MENU (ID, SORT_NUM, PARENT_ID,
      NAME_ZH, NAME_EN, URL, 
      DESCRIPTION, IMAGE, STATUS
      )
    values (#{id,jdbcType=DECIMAL}, #{sortNum,jdbcType=DECIMAL}, #{parentId,jdbcType=DECIMAL}, 
      #{nameZh,jdbcType=VARCHAR}, #{nameEn,jdbcType=VARCHAR}, #{url,jdbcType=VARCHAR}, 
      #{description,jdbcType=VARCHAR}, #{image,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.Menu" >
    <selectKey keyProperty="id" resultType="int" order="BEFORE">
      select <include refid="TABLE_SEQUENCE" /> from dual
    </selectKey>
    insert into MENU
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="sortNum != null" >
        SORT_NUM,
      </if>
      <if test="parentId != null" >
        PARENT_ID,
      </if>
      <if test="nameZh != null" >
        NAME_ZH,
      </if>
      <if test="nameEn != null" >
        NAME_EN,
      </if>
      <if test="url != null" >
        URL,
      </if>
      <if test="description != null" >
        DESCRIPTION,
      </if>
      <if test="image != null" >
        IMAGE,
      </if>
      <if test="status != null" >
        STATUS,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="sortNum != null" >
        #{sortNum,jdbcType=DECIMAL},
      </if>
      <if test="parentId != null" >
        #{parentId,jdbcType=DECIMAL},
      </if>
      <if test="nameZh != null" >
        #{nameZh,jdbcType=VARCHAR},
      </if>
      <if test="nameEn != null" >
        #{nameEn,jdbcType=VARCHAR},
      </if>
      <if test="url != null" >
        #{url,jdbcType=VARCHAR},
      </if>
      <if test="description != null" >
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="image != null" >
        #{image,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.Menu" >
    update MENU
    <set >
      <if test="sortNum != null" >
        SORT_NUM = #{sortNum,jdbcType=DECIMAL},
      </if>
      <if test="parentId != null" >
        PARENT_ID = #{parentId,jdbcType=DECIMAL},
      </if>
      <if test="nameZh != null" >
        NAME_ZH = #{nameZh,jdbcType=VARCHAR},
      </if>
      <if test="nameEn != null" >
        NAME_EN = #{nameEn,jdbcType=VARCHAR},
      </if>
      <if test="url != null" >
        URL = #{url,jdbcType=VARCHAR},
      </if>
      <if test="description != null" >
        DESCRIPTION = #{description,jdbcType=VARCHAR},
      </if>
      <if test="image != null" >
        IMAGE = #{image,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        STATUS = #{status,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.Menu" >
    update MENU
    set SORT_NUM = #{sortNum,jdbcType=DECIMAL},
      PARENT_ID = #{parentId,jdbcType=DECIMAL},
      NAME_ZH = #{nameZh,jdbcType=VARCHAR},
      NAME_EN = #{nameEn,jdbcType=VARCHAR},
      URL = #{url,jdbcType=VARCHAR},
      DESCRIPTION = #{description,jdbcType=VARCHAR},
      IMAGE = #{image,jdbcType=VARCHAR},
      STATUS = #{status,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>

  <!-- 测试bootstrap table -->
  <select id="selectTest" resultType="java.util.HashMap" parameterType="com.sinoair.billing.domain.vo.FilterParam">
    select NAME_ZH,NAME_EN,URL,DESCRIPTION
    from menu
    where 1=1
    <if test="search != null and search != ''">
      and ( UPPER(NAME_ZH) like  '%'||UPPER (#{search})||'%'
      or  UPPER(NAME_EN) like  '%'||UPPER (#{search})||'%'
      or  UPPER(DESCRIPTION) like  '%'||UPPER (#{search})||'%')
    </if>
    <if test="sort != null">
      order by ${sort} ${order}
    </if>
  </select>

  <select id="getMenuListByRoleID" resultMap="BaseResultMap" parameterType="java.lang.Integer">
    select * from MENU where id in (
    select menu_id from role_menu where role_id = #{role_id}
    ) and status = 'Y'
    order by SORT_NUM
  </select>
  <!-- 通过一级菜单列表，获得二级菜单列表 -->
  <select id="getMenuListByMenuID"  resultMap="BaseResultMap" parameterType="java.lang.Integer">
    select * from MENU where  status = 'Y'
    and PARENT_ID = #{menu_id}
    order by SORT_NUM
  </select>

  <!-- 获取可用的菜单列表 -->
  <select id="getMenuListY"  resultMap="BaseResultMap" >
    select * from MENU where  status = 'Y'
    order by id
  </select>

  <select id="getMenuListMap" resultType="java.util.HashMap" parameterType="com.sinoair.billing.domain.vo.FilterParam">
    select m1.id as id,m1.name_zh as m1name_zh,m1.name_en as m1name_en,m2.name_zh as m2name_zh,m1.sort_num as m1sort_num,m1.image as m1image,m1.url as m1url,m1.status as m1status,m1.description as m1description from MENU m1
    LEFT JOIN MENU m2
    ON m1.parent_id = m2.id
    where
    <!-- parent_menu_id判断 -->
    m1.parent_id = #{parent_menu_id}
    <!-- name_zh判断 -->
    <if test="menu_name != null and menu_name != ''">
      and ( UPPER(m1.name_zh) like  '%'||UPPER (#{menu_name})||'%' or
      UPPER(m1.name_en) like  '%'||UPPER (#{menu_name})||'%'
      )
    </if>

    <!-- url判断 -->
    <if test="url != null and url != ''">
      and  UPPER(m1.url) LIKE '%'||UPPER(#{url})||'%'
    </if>

    <!-- id -->

    <if test="id != 0 ">
      and  m1.id = #{id}
    </if>

    order by m1.id
  </select>

  <select id="getMaxSortNumByParentId" resultType="java.lang.Integer" parameterType="java.lang.Integer">
    select max(d.sort_num) from menu d where d.parent_id = #{parent_id}
  </select>

  <select id="selectParentId" resultType="java.lang.Integer" parameterType="java.util.ArrayList">
    select distinct PARENT_ID from menu where id in
    <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>
</mapper>