<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.NationCountryMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.NationCountry" >
    <id column="NC_ID" property="ncId" jdbcType="DECIMAL" />
    <result column="NC_NAME" property="ncName" jdbcType="VARCHAR" />
    <result column="NC_3CODE" property="nc3code" jdbcType="VARCHAR" />
    <result column="NC_2CODE" property="nc2code" jdbcType="VARCHAR" />
    <result column="NC_REMARK" property="ncRemark" jdbcType="VARCHAR" />
    <result column="NC_STATUS" property="ncStatus" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    NC_ID, NC_NAME, NC_3CODE, NC_2CODE, NC_REMARK, NC_STATUS
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from NATIONCOUNTRY
    where NC_ID = #{ncId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from NATIONCOUNTRY
    where NC_ID = #{ncId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.NationCountry" >
    insert into NATIONCOUNTRY (NC_ID, NC_NAME, NC_3CODE, 
      NC_2CODE, NC_REMARK, NC_STATUS
      )
    values (#{ncId,jdbcType=DECIMAL}, #{ncName,jdbcType=VARCHAR}, #{nc3code,jdbcType=VARCHAR}, 
      #{nc2code,jdbcType=VARCHAR}, #{ncRemark,jdbcType=VARCHAR}, #{ncStatus,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.NationCountry" >
    insert into NATIONCOUNTRY
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="ncId != null" >
        NC_ID,
      </if>
      <if test="ncName != null" >
        NC_NAME,
      </if>
      <if test="nc3code != null" >
        NC_3CODE,
      </if>
      <if test="nc2code != null" >
        NC_2CODE,
      </if>
      <if test="ncRemark != null" >
        NC_REMARK,
      </if>
      <if test="ncStatus != null" >
        NC_STATUS,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="ncId != null" >
        #{ncId,jdbcType=DECIMAL},
      </if>
      <if test="ncName != null" >
        #{ncName,jdbcType=VARCHAR},
      </if>
      <if test="nc3code != null" >
        #{nc3code,jdbcType=VARCHAR},
      </if>
      <if test="nc2code != null" >
        #{nc2code,jdbcType=VARCHAR},
      </if>
      <if test="ncRemark != null" >
        #{ncRemark,jdbcType=VARCHAR},
      </if>
      <if test="ncStatus != null" >
        #{ncStatus,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.NationCountry" >
    update NATIONCOUNTRY
    <set >
      <if test="ncName != null" >
        NC_NAME = #{ncName,jdbcType=VARCHAR},
      </if>
      <if test="nc3code != null" >
        NC_3CODE = #{nc3code,jdbcType=VARCHAR},
      </if>
      <if test="nc2code != null" >
        NC_2CODE = #{nc2code,jdbcType=VARCHAR},
      </if>
      <if test="ncRemark != null" >
        NC_REMARK = #{ncRemark,jdbcType=VARCHAR},
      </if>
      <if test="ncStatus != null" >
        NC_STATUS = #{ncStatus,jdbcType=VARCHAR},
      </if>
    </set>
    where NC_ID = #{ncId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.NationCountry" >
    update NATIONCOUNTRY
    set NC_NAME = #{ncName,jdbcType=VARCHAR},
      NC_3CODE = #{nc3code,jdbcType=VARCHAR},
      NC_2CODE = #{nc2code,jdbcType=VARCHAR},
      NC_REMARK = #{ncRemark,jdbcType=VARCHAR},
      NC_STATUS = #{ncStatus,jdbcType=VARCHAR}
    where NC_ID = #{ncId,jdbcType=DECIMAL}
  </update>

  <select id="listAll" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from NATIONCOUNTRY
  </select>
</mapper>