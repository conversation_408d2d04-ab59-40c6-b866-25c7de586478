<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.OrderPoolMapper" >

  <select id="selectMaxEawbSysCode" resultType="java.lang.Long">
    select eawb_syscode from (
    select eawb_syscode from expressairwaybill
    where 1=1
    order by eawb_syscode desc )
    where
    <![CDATA[
        rownum < 2
        ]]>
  </select>
  <!-- SEQ_SINOTRANS_ID.NEXTVAL as SINOTRANS_ID, -->
  <select id="selectOrderAccept" resultType="com.sinoair.billing.domain.model.billing.SinotransOrderPool"
          parameterType="com.sinoair.billing.domain.vo.check.EawbCheckVO">
    select
      eawb.eawb_printcode,

      eawb.eawb_printcode as ORDER_ID,
      eawb.eawb_syscode as SOURCE_ID,
      eawb.eawb_so_code as ORIGINAL_CUST_ID,
      eawb.eawb_chargeableweight as BUSINESS_VOLUME,
      eawb.eawb_reference1 as BL_NO,
      null as extend1,
      null as extend2,
      null as extend3,
      null as extend4,
      null as extend5,
      null as extend6,
      null as extend7,
      null as extend8,
      nvl(eawb.eawb_pieces,1) as BM_PIECE,
      eawb.eawb_chargeableweight as BM_CHARGEABLEWEIGHT,
      eawb.eawb_departcountry as TA_NC_CODE_DEPARTURE,
      eawb.eawb_destcountry as TA_NC_CODE_DESTINATION,
      eawb.eawb_updatetime as statusTime,
      eawb.sac_id,
      eawb.eawb_servicetype_original as ep_key


    from expressairwaybill eawb
    where 1 =1
     and eawb.eawb_syscode > #{beginNo}
    <![CDATA[
     and eawb.eawb_syscode <= #{endNo}
     ]]>
  </select>

  <select id="countOrderAccept" resultType="java.lang.Integer"
          parameterType="com.sinoair.billing.domain.vo.check.EawbCheckVO">
    select
      count(0)
    from expressairwaybill eawb
    where 1 =1
    and eawb.eawb_syscode > #{beginNo}
    <![CDATA[
     and eawb.eawb_syscode <= #{endNo}
     ]]>
  </select>

  <select id="selectSinotransIdSequence" resultType="java.lang.String">
    SELECT SEQ_SINOTRANS_ID.NEXTVAL
    FROM dual
  </select>

  <select id="countEawbPrintcodeByDmId" resultType="java.lang.Integer" parameterType="java.lang.Long">
    select count(eawb_printcode) from receipt_record rr where dm_id = #{dmId}
  </select>

  <select id="selectEawbPrintcodeByDmId" resultType="java.lang.String" parameterType="java.lang.Long">
    select eawb_printcode from receipt_record rr where dm_id = #{dmId}
  </select>

  <select id="selectDebitEawb" resultType="java.lang.String" >
    select eawb_printcode from debit_eawb
  </select>

</mapper>