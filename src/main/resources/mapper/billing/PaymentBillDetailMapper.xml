<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.PaymentBillDetailMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.PaymentBillDetail" >
    <id column="PBD_SYSCODE" property="pbdSyscode" jdbcType="DECIMAL" />
    <result column="INVOICE_CODE" property="invoiceCode" jdbcType="VARCHAR" />
    <result column="BUSINESS_CODE" property="businessCode" jdbcType="VARCHAR" />
    <result column="DSBMS_CODE" property="dsbmsCode" jdbcType="VARCHAR" />
    <result column="SP_CODE" property="spCode" jdbcType="VARCHAR" />
    <result column="PBD_PIECES" property="pbdPieces" jdbcType="DECIMAL" />
    <result column="CHARGEWEIGHT_SCOPE" property="chargeweightScope" jdbcType="VARCHAR" />
    <result column="PBD_CHARGEWEIGHT" property="pbdChargeweight" jdbcType="DECIMAL" />
    <result column="PBD_AMOUNT" property="pbdAmount" jdbcType="DECIMAL" />
    <result column="CT_ID" property="ctId" jdbcType="VARCHAR" />
    <result column="PBD_DATE" property="pbdDate" jdbcType="TIMESTAMP" />
    <result column="EAWB_SERVICETYPE" property="eawbServicetype" jdbcType="VARCHAR" />
    <result column="CM_ID" property="cmId" jdbcType="DECIMAL" />
    <result column="PBD_REMARK" property="pbdRemark" jdbcType="VARCHAR" />
    <result column="PBD_FILENAME" property="pbdFilename" jdbcType="VARCHAR" />
    <result column="PBD_HANDLETIME" property="pbdHandletime" jdbcType="TIMESTAMP" />
    <result column="PBD_USER_ID" property="pbdUserId" jdbcType="DECIMAL" />
    <result column="PBD_ACTUAL_AMOUNT" property="pbdActualAmount" jdbcType="DECIMAL" />
    <result column="PBD_ACTUAL_CHARGEWEIGHT" property="pbdActualChargeweight" jdbcType="DECIMAL" />
    <result column="PBD_ACTUAL_PIECES" property="pbdActualPieces" jdbcType="DECIMAL" />
    <result column="PR_NAME" property="prName" jdbcType="VARCHAR"/>
    <result column="PD_SYSCODE" property="pdSyscode" jdbcType="DECIMAL"/>
    <result column="COMPANY_ID" property="companyId" jdbcType="VARCHAR"/>
    <result column="EAWB_IETYPE" property="eawbIetype" jdbcType="VARCHAR"/>
  </resultMap>
  <sql id="Base_Column_List" >
    PBD_SYSCODE, INVOICE_CODE, BUSINESS_CODE, DSBMS_CODE, SP_CODE, PBD_PIECES, CHARGEWEIGHT_SCOPE,
    PBD_CHARGEWEIGHT, PBD_AMOUNT, CT_ID, PBD_DATE, EAWB_SERVICETYPE, CM_ID, PBD_REMARK,
    PBD_FILENAME, PBD_HANDLETIME, PBD_USER_ID, PBD_ACTUAL_AMOUNT, PBD_ACTUAL_CHARGEWEIGHT,
    PBD_ACTUAL_PIECES, PR_NAME, PD_SYSCODE, COMPANY_ID,EAWB_IETYPE
  </sql>
  <sql id='TABLE_SEQUENCE'>SEQ_payment_bill_detail.NEXTVAL</sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from PAYMENT_BILL_DETAIL
    where PBD_SYSCODE = #{pbdSyscode,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from PAYMENT_BILL_DETAIL
    where PBD_SYSCODE = #{pbdSyscode,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.PaymentBillDetail" >
    <selectKey keyProperty="pbdSyscode" resultType="java.lang.Integer" order="BEFORE">
      select
      <include refid="TABLE_SEQUENCE"/>
      from dual
    </selectKey>
    insert into PAYMENT_BILL_DETAIL (PBD_SYSCODE, INVOICE_CODE, BUSINESS_CODE,
    DSBMS_CODE, SP_CODE, PBD_PIECES,
    CHARGEWEIGHT_SCOPE, PBD_CHARGEWEIGHT, PBD_AMOUNT,
    CT_ID, PBD_DATE, EAWB_SERVICETYPE,
    CM_ID, PBD_REMARK, PBD_FILENAME,
    PBD_HANDLETIME, PBD_USER_ID, PBD_ACTUAL_AMOUNT,
    PBD_ACTUAL_CHARGEWEIGHT, PBD_ACTUAL_PIECES,
    PR_NAME, PD_SYSCODE, COMPANY_ID,EAWB_IETYPE
    )
    values (#{pbdSyscode,jdbcType=DECIMAL}, #{invoiceCode,jdbcType=VARCHAR}, #{businessCode,jdbcType=VARCHAR},
    #{dsbmsCode,jdbcType=VARCHAR}, #{spCode,jdbcType=VARCHAR}, #{pbdPieces,jdbcType=DECIMAL},
    #{chargeweightScope,jdbcType=VARCHAR}, #{pbdChargeweight,jdbcType=DECIMAL}, #{pbdAmount,jdbcType=DECIMAL},
    #{ctId,jdbcType=VARCHAR}, #{pbdDate,jdbcType=TIMESTAMP}, #{eawbServicetype,jdbcType=VARCHAR},
    #{cmId,jdbcType=DECIMAL}, #{pbdRemark,jdbcType=VARCHAR}, #{pbdFilename,jdbcType=VARCHAR},
    #{pbdHandletime,jdbcType=TIMESTAMP}, #{pbdUserId,jdbcType=DECIMAL}, #{pbdActualAmount,jdbcType=DECIMAL},
    #{pbdActualChargeweight,jdbcType=DECIMAL}, #{pbdActualPieces,jdbcType=DECIMAL},
    #{prName,jdbcType=VARCHAR}, #{pdSyscode,jdbcType=DECIMAL},
    #{companyId,jdbcType=VARCHAR},#{eawbIetype,jdbcType=VARCHAR}
    )
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.PaymentBillDetail" >
    <selectKey keyProperty="pbdSyscode" resultType="java.lang.Integer" order="BEFORE">
      select
      <include refid="TABLE_SEQUENCE"/>
      from dual
    </selectKey>
    insert into PAYMENT_BILL_DETAIL
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="pbdSyscode != null" >
        PBD_SYSCODE,
      </if>
      <if test="invoiceCode != null" >
        INVOICE_CODE,
      </if>
      <if test="businessCode != null" >
        BUSINESS_CODE,
      </if>
      <if test="dsbmsCode != null" >
        DSBMS_CODE,
      </if>
      <if test="spCode != null" >
        SP_CODE,
      </if>
      <if test="pbdPieces != null" >
        PBD_PIECES,
      </if>
      <if test="chargeweightScope != null" >
        CHARGEWEIGHT_SCOPE,
      </if>
      <if test="pbdChargeweight != null" >
        PBD_CHARGEWEIGHT,
      </if>
      <if test="pbdAmount != null" >
        PBD_AMOUNT,
      </if>
      <if test="ctId != null" >
        CT_ID,
      </if>
      <if test="pbdDate != null" >
        PBD_DATE,
      </if>
      <if test="eawbServicetype != null" >
        EAWB_SERVICETYPE,
      </if>
      <if test="cmId != null" >
        CM_ID,
      </if>
      <if test="pbdRemark != null" >
        PBD_REMARK,
      </if>
      <if test="pbdFilename != null" >
        PBD_FILENAME,
      </if>
      <if test="pbdHandletime != null" >
        PBD_HANDLETIME,
      </if>
      <if test="pbdUserId != null" >
        PBD_USER_ID,
      </if>
      <if test="pbdActualAmount != null" >
        PBD_ACTUAL_AMOUNT,
      </if>
      <if test="pbdActualChargeweight != null" >
        PBD_ACTUAL_CHARGEWEIGHT,
      </if>
      <if test="pbdActualPieces != null" >
        PBD_ACTUAL_PIECES,
      </if>
      <if test="prName != null">
        PR_NAME,
      </if>
      <if test="pdSyscode != null">
        PD_SYSCODE,
      </if>
      <if test="companyId != null">
        COMPANY_ID,
      </if>
      <if test="eawbIetype != null">
        EAWB_IETYPE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="pbdSyscode != null" >
        #{pbdSyscode,jdbcType=DECIMAL},
      </if>
      <if test="invoiceCode != null" >
        #{invoiceCode,jdbcType=VARCHAR},
      </if>
      <if test="businessCode != null" >
        #{businessCode,jdbcType=VARCHAR},
      </if>
      <if test="dsbmsCode != null" >
        #{dsbmsCode,jdbcType=VARCHAR},
      </if>
      <if test="spCode != null" >
        #{spCode,jdbcType=VARCHAR},
      </if>
      <if test="pbdPieces != null" >
        #{pbdPieces,jdbcType=DECIMAL},
      </if>
      <if test="chargeweightScope != null" >
        #{chargeweightScope,jdbcType=VARCHAR},
      </if>
      <if test="pbdChargeweight != null" >
        #{pbdChargeweight,jdbcType=DECIMAL},
      </if>
      <if test="pbdAmount != null" >
        #{pbdAmount,jdbcType=DECIMAL},
      </if>
      <if test="ctId != null" >
        #{ctId,jdbcType=VARCHAR},
      </if>
      <if test="pbdDate != null" >
        #{pbdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="eawbServicetype != null" >
        #{eawbServicetype,jdbcType=VARCHAR},
      </if>
      <if test="cmId != null" >
        #{cmId,jdbcType=DECIMAL},
      </if>
      <if test="pbdRemark != null" >
        #{pbdRemark,jdbcType=VARCHAR},
      </if>
      <if test="pbdFilename != null" >
        #{pbdFilename,jdbcType=VARCHAR},
      </if>
      <if test="pbdHandletime != null" >
        #{pbdHandletime,jdbcType=TIMESTAMP},
      </if>
      <if test="pbdUserId != null" >
        #{pbdUserId,jdbcType=DECIMAL},
      </if>
      <if test="pbdActualAmount != null" >
        #{pbdActualAmount,jdbcType=DECIMAL},
      </if>
      <if test="pbdActualChargeweight != null" >
        #{pbdActualChargeweight,jdbcType=DECIMAL},
      </if>
      <if test="pbdActualPieces != null" >
        #{pbdActualPieces,jdbcType=DECIMAL},
      </if>
      <if test="prName != null">
        #{prName,jdbcType=VARCHAR},
      </if>
      <if test="pdSyscode != null">
        #{pdSyscode,jdbcType=DECIMAL},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="eawbIetype != null">
        #{eawbIetype,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.PaymentBillDetail" >
    update PAYMENT_BILL_DETAIL
    <set >
      <if test="invoiceCode != null" >
        INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR},
      </if>
      <if test="businessCode != null" >
        BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR},
      </if>
      <if test="dsbmsCode != null" >
        DSBMS_CODE = #{dsbmsCode,jdbcType=VARCHAR},
      </if>
      <if test="spCode != null" >
        SP_CODE = #{spCode,jdbcType=VARCHAR},
      </if>
      <if test="pbdPieces != null" >
        PBD_PIECES = #{pbdPieces,jdbcType=DECIMAL},
      </if>
      <if test="chargeweightScope != null" >
        CHARGEWEIGHT_SCOPE = #{chargeweightScope,jdbcType=VARCHAR},
      </if>
      <if test="pbdChargeweight != null" >
        PBD_CHARGEWEIGHT = #{pbdChargeweight,jdbcType=DECIMAL},
      </if>
      <if test="pbdAmount != null" >
        PBD_AMOUNT = #{pbdAmount,jdbcType=DECIMAL},
      </if>
      <if test="ctId != null" >
        CT_ID = #{ctId,jdbcType=VARCHAR},
      </if>
      <if test="pbdDate != null" >
        PBD_DATE = #{pbdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="eawbServicetype != null" >
        EAWB_SERVICETYPE = #{eawbServicetype,jdbcType=VARCHAR},
      </if>
      <if test="cmId != null" >
        CM_ID = #{cmId,jdbcType=DECIMAL},
      </if>
      <if test="pbdRemark != null" >
        PBD_REMARK = #{pbdRemark,jdbcType=VARCHAR},
      </if>
      <if test="pbdFilename != null" >
        PBD_FILENAME = #{pbdFilename,jdbcType=VARCHAR},
      </if>
      <if test="pbdHandletime != null" >
        PBD_HANDLETIME = #{pbdHandletime,jdbcType=TIMESTAMP},
      </if>
      <if test="pbdUserId != null" >
        PBD_USER_ID = #{pbdUserId,jdbcType=DECIMAL},
      </if>
      <if test="pbdActualAmount != null" >
        PBD_ACTUAL_AMOUNT = #{pbdActualAmount,jdbcType=DECIMAL},
      </if>
      <if test="pbdActualChargeweight != null" >
        PBD_ACTUAL_CHARGEWEIGHT = #{pbdActualChargeweight,jdbcType=DECIMAL},
      </if>
      <if test="pbdActualPieces != null" >
        PBD_ACTUAL_PIECES = #{pbdActualPieces,jdbcType=DECIMAL},
      </if>
      <if test="prName != null">
        PR_NAME = #{prName,jdbcType=VARCHAR},
      </if>
      <if test="pdSyscode != null">
        PD_SYSCODE = #{pdSyscode,jdbcType=DECIMAL},
      </if>
      <if test="companyId != null">
        COMPANY_ID = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="eawbIetype != null">
        EAWB_IETYPE=#{eawbIetype,jdbcType=VARCHAR},
      </if>
    </set>
    where PBD_SYSCODE = #{pbdSyscode,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.PaymentBillDetail" >
    update PAYMENT_BILL_DETAIL
    set INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR},
    BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR},
    DSBMS_CODE = #{dsbmsCode,jdbcType=VARCHAR},
    SP_CODE = #{spCode,jdbcType=VARCHAR},
    PBD_PIECES = #{pbdPieces,jdbcType=DECIMAL},
    CHARGEWEIGHT_SCOPE = #{chargeweightScope,jdbcType=VARCHAR},
    PBD_CHARGEWEIGHT = #{pbdChargeweight,jdbcType=DECIMAL},
    PBD_AMOUNT = #{pbdAmount,jdbcType=DECIMAL},
    CT_ID = #{ctId,jdbcType=VARCHAR},
    PBD_DATE = #{pbdDate,jdbcType=TIMESTAMP},
    EAWB_SERVICETYPE = #{eawbServicetype,jdbcType=VARCHAR},
    CM_ID = #{cmId,jdbcType=DECIMAL},
    PBD_REMARK = #{pbdRemark,jdbcType=VARCHAR},
    PBD_FILENAME = #{pbdFilename,jdbcType=VARCHAR},
    PBD_HANDLETIME = #{pbdHandletime,jdbcType=TIMESTAMP},
    PBD_USER_ID = #{pbdUserId,jdbcType=DECIMAL},
    PBD_ACTUAL_AMOUNT = #{pbdActualAmount,jdbcType=DECIMAL},
    PBD_ACTUAL_CHARGEWEIGHT = #{pbdActualChargeweight,jdbcType=DECIMAL},
    PBD_ACTUAL_PIECES = #{pbdActualPieces,jdbcType=DECIMAL},
    PR_NAME = #{prName,jdbcType=VARCHAR},
    PD_SYSCODE = #{pdSyscode,jdbcType=DECIMAL},
    COMPANY_ID = #{companyId,jdbcType=VARCHAR},
    EAWB_IETYPE=#{eawbIetype,jdbcType=VARCHAR}
    where PBD_SYSCODE = #{pbdSyscode,jdbcType=DECIMAL}
  </update>
  <insert id="insertBatch"  parameterType="java.util.List">
    insert into PAYMENT_BILL_DETAIL (PBD_SYSCODE,BUSINESS_CODE, EAWB_SERVICETYPE, PBD_FILENAME,
    INVOICE_CODE, DSBMS_CODE, SP_CODE,
    PBD_PIECES, CHARGEWEIGHT_SCOPE, PBD_CHARGEWEIGHT,
    PBD_AMOUNT, CT_ID, PBD_DATE,
    CM_ID, PBD_REMARK, PBD_HANDLETIME,
    PBD_USER_ID,PBD_ACTUAL_AMOUNT, PBD_ACTUAL_CHARGEWEIGHT,
    PBD_ACTUAL_PIECES, PR_NAME, PD_SYSCODE, COMPANY_ID,EAWB_IETYPE)
    select SEQ_payment_bill_detail.NEXTVAL,pbd.* from (
    <foreach collection="list" item="item" index="index" separator="UNION">
      (select
      #{item.businessCode,jdbcType=VARCHAR}, #{item.eawbServicetype,jdbcType=VARCHAR},
      #{item.pbdFilename,jdbcType=VARCHAR},
      #{item.invoiceCode,jdbcType=VARCHAR}, #{item.dsbmsCode,jdbcType=VARCHAR}, #{item.spCode,jdbcType=VARCHAR},
      #{item.pbdPieces,jdbcType=DECIMAL}, #{item.chargeweightScope,jdbcType=VARCHAR},
      #{item.pbdChargeweight,jdbcType=DECIMAL},
      #{item.pbdAmount,jdbcType=DECIMAL}, #{item.ctId,jdbcType=VARCHAR}, #{item.pbdDate,jdbcType=TIMESTAMP},
      #{item.cmId,jdbcType=DECIMAL}, #{item.pbdRemark,jdbcType=VARCHAR}, #{item.pbdHandletime,jdbcType=TIMESTAMP},
      #{item.pbdUserId,jdbcType=DECIMAL},#{item.pbdActualAmount,jdbcType=DECIMAL},
      #{item.pbdActualChargeweight,jdbcType=DECIMAL},
      #{item.pbdActualPieces,jdbcType=DECIMAL},#{item.prName,jdbcType=VARCHAR},#{item.pdSyscode,jdbcType=DECIMAL},
      #{item.companyId,jdbcType=VARCHAR},#{item.eawbIetype,jdbcType=VARCHAR}
      from dual)
    </foreach>
    ) pbd
  </insert>
  <update id="updateBatch"  parameterType="java.util.List">
    <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
      UPDATE PAYMENT_BILL_DETAIL
      <set>
        <if test="item.pbdActualAmount != null">
          PBD_ACTUAL_AMOUNT = #{item.pbdActualAmount,jdbcType=DECIMAL},
        </if>
        <if test="item.pbdActualChargeweight != null">
          PBD_ACTUAL_CHARGEWEIGHT = #{item.pbdActualChargeweight,jdbcType=DECIMAL},
        </if>
        <if test="item.pbdActualPieces != null">
          PBD_ACTUAL_PIECES = #{item.pbdActualPieces,jdbcType=DECIMAL}
        </if>
      </set>
      where PBD_SYSCODE=#{item.pbdSyscode,jdbcType=DECIMAL}
    </foreach>
  </update>
  <select id="selectDifferenceRecord" resultType="java.util.HashMap" parameterType="java.lang.Integer">
    select pbd.*,pbd.dsbms_code as dsbmscode,nvl(pbd.eawb_servicetype,pt.ep_key) as servicetype,
    pt.eawb_chargeableweight as WEIGHT,pt.pr_plan_amount as AMOUNT,pt.eawb_hawb_qty as pieces ,
    '金额不等' as type
    from payment_bill_detail pbd,
    (select pr.eawb_tracking_no,pr.ep_key,pr.SO_CODE,sum(pr.eawb_chargeableweight) as eawb_chargeableweight,
    sum(pr.pr_plan_amount) as pr_plan_amount,count(distinct pr.eawb_printcode)  as eawb_hawb_qty
    from payment_record pr
    group by pr.eawb_tracking_no,pr.ep_key,pr.SO_CODE) pt
    where
    pbd.dsbms_code=pt.eawb_tracking_no(+)
    and pbd.SP_CODE=pt.SO_CODE
    and pbd.cm_id=#{cmId}
    and pbd.dsbms_code is not null
    and pt.pr_plan_amount is not null
    and pbd.pbd_amount &lt;&gt; pt.pr_plan_amount
    and not exists(select pd.business_code,count(*) from payment_bill_detail pd
    where pd.business_code=pbd.business_code
    group by pd.business_code having count(*)>1)
    union all
    select pbd.*,pbd.dsbms_code as dsbmscode,pbd.eawb_servicetype as servicetype,
    0 as WEIGHT,0 as AMOUNT,0 as pieces ,'账单有系统无' as type
    from payment_bill_detail pbd,payment_record pr
    where
    pbd.dsbms_code=pr.eawb_tracking_no(+)
    and pbd.cm_id=#{cmId}
    and pbd.dsbms_code is not null
    and pr.pr_plan_amount is null
    and not exists(select pd.business_code,count(*) from payment_bill_detail pd
    where  pd.business_code=pbd.business_code
    group by pd.business_code having count(*)>1)
    union all
    select pbd.*,bc.dsbms_code as dsbmscode,nvl(pbd.eawb_servicetype,pt.ep_key) as servicetype,
    pt.eawb_chargeableweight as WEIGHT,pt.pr_plan_amount as AMOUNT,pt.eawb_hawb_qty as pieces ,
    '金额不等' as type
    from payment_bill_detail pbd,business_compare bc,
    (select pr.eawb_tracking_no,pr.ep_key,pr.SO_CODE,sum(pr.eawb_chargeableweight) as eawb_chargeableweight,
    sum(pr.pr_plan_amount) as pr_plan_amount,count(distinct pr.eawb_printcode) as eawb_hawb_qty
    from payment_record pr
    group by pr.eawb_tracking_no,pr.ep_key,pr.SO_CODE) pt
    where
    pbd.business_code=bc.business_code
    and pbd.SP_CODE=pt.SO_CODE
    and bc.dsbms_code=pt.eawb_tracking_no
    and pbd.dsbms_code is null
    and pt.pr_plan_amount is not null
    and pbd.pbd_amount &lt;&gt; pt.pr_plan_amount
    and pbd.cm_id=#{cmId}
    and not exists(select pd.business_code,count(*) from payment_bill_detail pd
    where pd.business_code=pbd.business_code
    group by pd.business_code having count(*)>1)
    union all
    select pbd.*,bc.dsbms_code as dsbmscode,pbd.eawb_servicetype as servicetype,
    0 as WEIGHT,0 as AMOUNT,0 as pieces ,'账单有系统无' as type
    from payment_bill_detail pbd,business_compare bc
    where pbd.business_code=bc.business_code
    and not exists(select * from payment_record p where p.eawb_tracking_no=bc.dsbms_code)
    and pbd.cm_id=#{cmId}
    and not exists(select pd.business_code,count(*) from payment_bill_detail pd
    where pd.business_code=pbd.business_code
    group by pd.business_code having count(*)>1)
    union all
    select pbd.*,'' dsbmscode,pbd.eawb_servicetype as servicetype,
    0 as WEIGHT,0 as AMOUNT,0 as pieces ,'账单有系统无' as type
    from payment_bill_detail pbd
    where not exists(select * from business_compare bc
    where pbd.business_code=bc.business_code)
    and pbd.cm_id=#{cmId}
    and not exists(select pd.business_code,count(*) from payment_bill_detail pd
    where pd.business_code=pbd.business_code
    group by pd.business_code having count(*)>1)

    /* select pbd.*,bc.dsbms_code as dsbmscode,pbd.eawb_servicetype as servicetype,
    0 as WEIGHT,0 as AMOUNT,0 as pieces ,'账单有系统无' as type
    from payment_bill_detail pbd,business_compare bc,payment_record pr
    where
    pbd.business_code=bc.business_code
    and bc.dsbms_code=pr.eawb_tracking_no(+)
    and pbd.dsbms_code is null
    and pr.pr_plan_amount is null
    and pbd.cm_id=#{cmId}
    and not exists(select pd.business_code,count(*) from payment_bill_detail pd
    where pd.business_code=pbd.business_code
    group by pd.business_code having count(*)>1)*/
    union all
    select pbd.*,pbd.dsbms_code as dsbmscode,nvl(pbd.eawb_servicetype,pt.ep_key) as servicetype,
    pt.eawb_chargeableweight as WEIGHT,pt.pr_plan_amount as AMOUNT,pt.eawb_hawb_qty as pieces ,
    '账单重复' as type
    from payment_bill_detail pbd,
    (select pr.eawb_tracking_no,pr.ep_key,pr.SO_CODE,sum(pr.eawb_chargeableweight) as eawb_chargeableweight,
    sum(pr.pr_plan_amount) as pr_plan_amount,count(distinct pr.eawb_printcode)  as eawb_hawb_qty
    from payment_record pr
    group by pr.eawb_tracking_no,pr.ep_key,pr.SO_CODE) pt
    where
    pbd.dsbms_code=pt.eawb_tracking_no(+)
    and pbd.SP_CODE=pt.SO_CODE
    and pbd.cm_id=#{cmId}
    and exists(select pd.business_code,count(*) from payment_bill_detail pd
    where
    pd.business_code=pbd.business_code
    group by pd.business_code having count(*)>1)

  </select>
  <delete id="deleteByCmId" parameterType="java.lang.Integer">
    delete from PAYMENT_BILL_DETAIL
    where cm_id = #{cmId}
  </delete>
  <!--删除对照号-->
  <delete id="deleteBCByCmId" parameterType="java.lang.Integer">
    DELETE FROM BUSINESS_COMPARE WHERE BUSINESS_CODE IN
    (SELECT BUSINESS_CODE FROM PAYMENT_BILL_DETAIL WHERE cm_id = #{cmId} AND
    DSBMS_CODE is NULL )
  </delete>
  <!--工具查询明细-->
  <select id="selectPaymentRecordTool" resultType="java.util.Map" parameterType="com.sinoair.billing.domain.vo.FilterParam">
    SELECT * FROM PAYMENT_BILL_DETAIL WHERE
    BUSINESS_CODE IN
    <foreach collection="servers" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </select>
</mapper>