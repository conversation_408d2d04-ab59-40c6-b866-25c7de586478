<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.PaymentBillDiffMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.PaymentBillDiff" >
    <result column="INVOICE_CODE" property="invoiceCode" jdbcType="VARCHAR" />
    <result column="BUSINESS_CODE" property="businessCode" jdbcType="VARCHAR" />
    <result column="DSBMS_CODE" property="dsbmsCode" jdbcType="VARCHAR" />
    <result column="SP_CODE" property="spCode" jdbcType="VARCHAR" />
    <result column="PBD_PIECES" property="pbdPieces" jdbcType="DECIMAL" />
    <result column="CHARGEWEIGHT_SCOPE" property="chargeweightScope" jdbcType="VARCHAR" />
    <result column="PBD_CHARGEWEIGHT" property="pbdChargeweight" jdbcType="DECIMAL" />
    <result column="DSBMS_CHARGEWEIGHT" property="dsbmsChargeweight" jdbcType="DECIMAL" />
    <result column="PBD_AMOUNT" property="pbdAmount" jdbcType="DECIMAL" />
    <result column="DSBMS_AMOUNT" property="dsbmsAmount" jdbcType="DECIMAL" />
    <result column="ACTUAL_AMOUNT" property="actualAmount" jdbcType="DECIMAL" />
    <result column="CT_ID" property="ctId" jdbcType="VARCHAR" />
    <result column="PBD_DATE" property="pbdDate" jdbcType="TIMESTAMP" />
    <result column="EAWB_SERVICETYPE" property="eawbServicetype" jdbcType="VARCHAR" />
    <result column="CM_ID" property="cmId" jdbcType="DECIMAL" />
    <result column="PBD_REMARK" property="pbdRemark" jdbcType="VARCHAR" />
    <result column="PBD_FILENAME" property="pbdFilename" jdbcType="VARCHAR" />
    <result column="DMBMS_ITEMS" property="dmbmsItems" jdbcType="DECIMAL" />
    <result column="ACTUAL_CHARGEWEIGHT" property="actualChargeweight" jdbcType="DECIMAL" />
    <result column="ACTUAL_ITEMS" property="actualItems" jdbcType="DECIMAL" />
  </resultMap>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.PaymentBillDiff" >
    insert into PAYMENT_BILL_DIFF (INVOICE_CODE, BUSINESS_CODE, DSBMS_CODE, 
      SP_CODE, PBD_PIECES, CHARGEWEIGHT_SCOPE, 
      PBD_CHARGEWEIGHT, DSBMS_CHARGEWEIGHT, PBD_AMOUNT, 
      DSBMS_AMOUNT, ACTUAL_AMOUNT, CT_ID, 
      PBD_DATE, EAWB_SERVICETYPE, CM_ID, 
      PBD_REMARK, PBD_FILENAME, DMBMS_ITEMS, 
      ACTUAL_CHARGEWEIGHT, ACTUAL_ITEMS)
    values (#{invoiceCode,jdbcType=VARCHAR}, #{businessCode,jdbcType=VARCHAR}, #{dsbmsCode,jdbcType=VARCHAR}, 
      #{spCode,jdbcType=VARCHAR}, #{pbdPieces,jdbcType=DECIMAL}, #{chargeweightScope,jdbcType=VARCHAR}, 
      #{pbdChargeweight,jdbcType=DECIMAL}, #{dsbmsChargeweight,jdbcType=DECIMAL}, #{pbdAmount,jdbcType=DECIMAL}, 
      #{dsbmsAmount,jdbcType=DECIMAL}, #{actualAmount,jdbcType=DECIMAL}, #{ctId,jdbcType=VARCHAR}, 
      #{pbdDate,jdbcType=TIMESTAMP}, #{eawbServicetype,jdbcType=VARCHAR}, #{cmId,jdbcType=DECIMAL}, 
      #{pbdRemark,jdbcType=VARCHAR}, #{pbdFilename,jdbcType=VARCHAR}, #{dmbmsItems,jdbcType=DECIMAL}, 
      #{actualChargeweight,jdbcType=DECIMAL}, #{actualItems,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.PaymentBillDiff" >
    insert into PAYMENT_BILL_DIFF
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="invoiceCode != null" >
        INVOICE_CODE,
      </if>
      <if test="businessCode != null" >
        BUSINESS_CODE,
      </if>
      <if test="dsbmsCode != null" >
        DSBMS_CODE,
      </if>
      <if test="spCode != null" >
        SP_CODE,
      </if>
      <if test="pbdPieces != null" >
        PBD_PIECES,
      </if>
      <if test="chargeweightScope != null" >
        CHARGEWEIGHT_SCOPE,
      </if>
      <if test="pbdChargeweight != null" >
        PBD_CHARGEWEIGHT,
      </if>
      <if test="dsbmsChargeweight != null" >
        DSBMS_CHARGEWEIGHT,
      </if>
      <if test="pbdAmount != null" >
        PBD_AMOUNT,
      </if>
      <if test="dsbmsAmount != null" >
        DSBMS_AMOUNT,
      </if>
      <if test="actualAmount != null" >
        ACTUAL_AMOUNT,
      </if>
      <if test="ctId != null" >
        CT_ID,
      </if>
      <if test="pbdDate != null" >
        PBD_DATE,
      </if>
      <if test="eawbServicetype != null" >
        EAWB_SERVICETYPE,
      </if>
      <if test="cmId != null" >
        CM_ID,
      </if>
      <if test="pbdRemark != null" >
        PBD_REMARK,
      </if>
      <if test="pbdFilename != null" >
        PBD_FILENAME,
      </if>
      <if test="dmbmsItems != null" >
        DMBMS_ITEMS,
      </if>
      <if test="actualChargeweight != null" >
        ACTUAL_CHARGEWEIGHT,
      </if>
      <if test="actualItems != null" >
        ACTUAL_ITEMS,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="invoiceCode != null" >
        #{invoiceCode,jdbcType=VARCHAR},
      </if>
      <if test="businessCode != null" >
        #{businessCode,jdbcType=VARCHAR},
      </if>
      <if test="dsbmsCode != null" >
        #{dsbmsCode,jdbcType=VARCHAR},
      </if>
      <if test="spCode != null" >
        #{spCode,jdbcType=VARCHAR},
      </if>
      <if test="pbdPieces != null" >
        #{pbdPieces,jdbcType=DECIMAL},
      </if>
      <if test="chargeweightScope != null" >
        #{chargeweightScope,jdbcType=VARCHAR},
      </if>
      <if test="pbdChargeweight != null" >
        #{pbdChargeweight,jdbcType=DECIMAL},
      </if>
      <if test="dsbmsChargeweight != null" >
        #{dsbmsChargeweight,jdbcType=DECIMAL},
      </if>
      <if test="pbdAmount != null" >
        #{pbdAmount,jdbcType=DECIMAL},
      </if>
      <if test="dsbmsAmount != null" >
        #{dsbmsAmount,jdbcType=DECIMAL},
      </if>
      <if test="actualAmount != null" >
        #{actualAmount,jdbcType=DECIMAL},
      </if>
      <if test="ctId != null" >
        #{ctId,jdbcType=VARCHAR},
      </if>
      <if test="pbdDate != null" >
        #{pbdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="eawbServicetype != null" >
        #{eawbServicetype,jdbcType=VARCHAR},
      </if>
      <if test="cmId != null" >
        #{cmId,jdbcType=DECIMAL},
      </if>
      <if test="pbdRemark != null" >
        #{pbdRemark,jdbcType=VARCHAR},
      </if>
      <if test="pbdFilename != null" >
        #{pbdFilename,jdbcType=VARCHAR},
      </if>
      <if test="dmbmsItems != null" >
        #{dmbmsItems,jdbcType=DECIMAL},
      </if>
      <if test="actualChargeweight != null" >
        #{actualChargeweight,jdbcType=DECIMAL},
      </if>
      <if test="actualItems != null" >
        #{actualItems,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <insert id="insertBatch"  parameterType="java.util.List">
    insert into PAYMENT_BILL_DIFF (INVOICE_CODE, BUSINESS_CODE, DSBMS_CODE,
    SP_CODE, PBD_PIECES, CHARGEWEIGHT_SCOPE,
    PBD_CHARGEWEIGHT, DSBMS_CHARGEWEIGHT, PBD_AMOUNT,
    DSBMS_AMOUNT, ACTUAL_AMOUNT, CT_ID,
    PBD_DATE, EAWB_SERVICETYPE, CM_ID,
    PBD_REMARK, PBD_FILENAME, DMBMS_ITEMS,
    ACTUAL_CHARGEWEIGHT, ACTUAL_ITEMS)
    <foreach collection="list" item="item" index="index" separator="UNION" >
      (select
      #{item.invoiceCode,jdbcType=VARCHAR}, #{item.businessCode,jdbcType=VARCHAR}, #{item.dsbmsCode,jdbcType=VARCHAR},
      #{item.spCode,jdbcType=VARCHAR}, #{item.pbdPieces,jdbcType=DECIMAL}, #{item.chargeweightScope,jdbcType=VARCHAR},
      #{item.pbdChargeweight,jdbcType=DECIMAL}, #{item.dsbmsChargeweight,jdbcType=DECIMAL}, #{item.pbdAmount,jdbcType=DECIMAL},
      #{item.dsbmsAmount,jdbcType=DECIMAL}, #{item.actualAmount,jdbcType=DECIMAL}, #{item.ctId,jdbcType=VARCHAR},
      #{item.pbdDate,jdbcType=TIMESTAMP}, #{item.eawbServicetype,jdbcType=VARCHAR}, #{item.cmId,jdbcType=DECIMAL},
      #{item.pbdRemark,jdbcType=VARCHAR}, #{item.pbdFilename,jdbcType=VARCHAR}, #{item.dmbmsItems,jdbcType=DECIMAL},
      #{item.actualChargeweight,jdbcType=DECIMAL}, #{item.actualItems,jdbcType=DECIMAL}
      from dual)
    </foreach>
  </insert>

  <delete id="deleteByCmId" parameterType="java.lang.Integer">
    DELETE FROM PAYMENT_BILL_DIFF
    WHERE cm_id=#{cmId}
  </delete>
</mapper>