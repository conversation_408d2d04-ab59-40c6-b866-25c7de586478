<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.PaymentRecordMapper">
    <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.PaymentRecord">
        <id column="PR_ID" property="prId" jdbcType="DECIMAL"/>
        <result column="EAWB_PRINTCODE" property="eawbPrintcode" jdbcType="VARCHAR"/>
        <result column="MAWB_CODE" property="mawbCode" jdbcType="VARCHAR"/>
        <result column="PP_ID" property="ppId" jdbcType="DECIMAL"/>
        <result column="CM_ID" property="cmId" jdbcType="DECIMAL"/>
        <result column="SO_CODE" property="soCode" jdbcType="VARCHAR"/>
        <result column="CT_CODE" property="ctCode" jdbcType="VARCHAR"/>
        <result column="COMPANY_ID" property="companyId" jdbcType="VARCHAR"/>
        <result column="PR_NAME" property="prName" jdbcType="VARCHAR"/>
        <result column="PR_TYPE" property="prType" jdbcType="VARCHAR"/>
        <result column="PR_PLAN_AMOUNT" property="prPlanAmount" jdbcType="DECIMAL"/>
        <result column="PR_ACTUAL_AMOUNT" property="prActualAmount" jdbcType="DECIMAL"/>
        <result column="PR_STATUS" property="prStatus" jdbcType="VARCHAR"/>
        <result column="PR_USER_ID" property="prUserId" jdbcType="DECIMAL"/>
        <result column="PR_HANDLETIME" property="prHandletime" jdbcType="TIMESTAMP"/>
        <result column="PR_AWB_TYPE" property="prAwbType" jdbcType="VARCHAR"/>
        <result column="PR_REMARK" property="prRemark" jdbcType="VARCHAR"/>
        <result column="EAWB_REFERENCE1" property="eawbReference1" jdbcType="VARCHAR"/>
        <result column="EAWB_REFERENCE2" property="eawbReference2" jdbcType="VARCHAR"/>
        <result column="EAWB_CHARGEABLEWEIGHT" property="eawbChargeableweight" jdbcType="DECIMAL"/>
        <result column="EAWB_HAWB_QTY" property="eawbHawbQty" jdbcType="DECIMAL"/>
        <result column="AGENT_CODE" property="agentCode" jdbcType="VARCHAR"/>
        <result column="PR_OCCURTIME" property="prOccurtime" jdbcType="TIMESTAMP"/>
        <result column="EAWB_TRACKING_NO" property="eawbTrackingNo" jdbcType="VARCHAR"/>
        <result column="EP_KEY" property="epKey" jdbcType="VARCHAR"/>
        <result column="CHARGEABLEWEIGHT" property="chargeableweight" jdbcType="DECIMAL"/>
        <result column="PD_SYSCODE" property="pdSyscode" jdbcType="DECIMAL"/>
        <result column="OUTBOUND_COMPANY_ID" property="outboundCompanyId" jdbcType="VARCHAR"/>
        <result column="EAWB_IETYPE" property="eawbIetype" jdbcType="VARCHAR"/>
        <result column="PR_CATE" property="prCate" jdbcType="VARCHAR"/>
        <result column="CT_RATE" property="ctRate" jdbcType="DECIMAL"/>
        <result column="PR_TOTAL_RMB" property="prTotalRmb" jdbcType="DECIMAL"/>
        <result column="PR_TOTAL_FC" property="prTotalFc" jdbcType="DECIMAL"/>
        <result column="RR_ID" property="rrId" jdbcType="DECIMAL"/>
        <result column="SERVICE_ID" property="serviceId" jdbcType="DECIMAL"/>
        <result column="PARTITION_CODE" property="partitionCode" jdbcType="DECIMAL"/>
    </resultMap>
    <sql id="Base_Column_List">
    PR_ID, EAWB_PRINTCODE, MAWB_CODE, PP_ID, CM_ID, SO_CODE, CT_CODE, COMPANY_ID, PR_NAME,
    PR_TYPE, PR_PLAN_AMOUNT, PR_ACTUAL_AMOUNT, PR_STATUS, PR_USER_ID, PR_HANDLETIME,
    PR_AWB_TYPE, PR_REMARK, EAWB_REFERENCE1, EAWB_REFERENCE2, EAWB_CHARGEABLEWEIGHT,
    EAWB_HAWB_QTY, AGENT_CODE, PR_OCCURTIME, EAWB_TRACKING_NO, EP_KEY, CHARGEABLEWEIGHT,
    PD_SYSCODE, OUTBOUND_COMPANY_ID, EAWB_IETYPE,PR_CATE,CT_RATE,PR_TOTAL_RMB,PR_TOTAL_FC,RR_ID
  </sql>

    <sql id='TABLE_SEQUENCE'>SEQ_PAYMENT_RECORD.NEXTVAL</sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from PAYMENT_RECORD
        where PR_ID = #{prId,jdbcType=DECIMAL}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from PAYMENT_RECORD
    where PR_ID = #{prId,jdbcType=DECIMAL}
  </delete>
    <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.PaymentRecord">
        <selectKey keyProperty="prId" resultType="java.lang.Long" order="BEFORE">
            select
            <include refid="TABLE_SEQUENCE"/>
            from dual
        </selectKey>
        insert into PAYMENT_RECORD (PR_ID, EAWB_PRINTCODE, MAWB_CODE,
        PP_ID, CM_ID, SO_CODE,
        CT_CODE, COMPANY_ID, PR_NAME,
        PR_TYPE, PR_PLAN_AMOUNT, PR_ACTUAL_AMOUNT,
        PR_STATUS, PR_USER_ID, PR_HANDLETIME,
        PR_AWB_TYPE, PR_REMARK, EAWB_REFERENCE1,
        EAWB_REFERENCE2, EAWB_CHARGEABLEWEIGHT, EAWB_HAWB_QTY,
        AGENT_CODE, PR_OCCURTIME, EAWB_TRACKING_NO,
        EP_KEY, CHARGEABLEWEIGHT, PD_SYSCODE,
        OUTBOUND_COMPANY_ID, EAWB_IETYPE,PR_CATE,CT_RATE,PR_TOTAL_RMB,PR_TOTAL_FC,RR_ID)
        values (#{prId,jdbcType=DECIMAL}, #{eawbPrintcode,jdbcType=VARCHAR}, #{mawbCode,jdbcType=VARCHAR},
        #{ppId,jdbcType=DECIMAL}, #{cmId,jdbcType=DECIMAL}, #{soCode,jdbcType=VARCHAR},
        #{ctCode,jdbcType=VARCHAR}, #{companyId,jdbcType=VARCHAR}, #{prName,jdbcType=VARCHAR},
        #{prType,jdbcType=VARCHAR}, #{prPlanAmount,jdbcType=DECIMAL}, #{prActualAmount,jdbcType=DECIMAL},
        #{prStatus,jdbcType=VARCHAR}, #{prUserId,jdbcType=DECIMAL}, #{prHandletime,jdbcType=TIMESTAMP},
        #{prAwbType,jdbcType=VARCHAR}, #{prRemark,jdbcType=VARCHAR}, #{eawbReference1,jdbcType=VARCHAR},
        #{eawbReference2,jdbcType=VARCHAR}, #{eawbChargeableweight,jdbcType=DECIMAL}, #{eawbHawbQty,jdbcType=DECIMAL},
        #{agentCode,jdbcType=VARCHAR}, #{prOccurtime,jdbcType=TIMESTAMP}, #{eawbTrackingNo,jdbcType=VARCHAR},
        #{epKey,jdbcType=VARCHAR}, #{chargeableweight,jdbcType=DECIMAL}, #{pdSyscode,jdbcType=DECIMAL},
        #{outboundCompanyId,jdbcType=VARCHAR}, #{eawbIetype,jdbcType=VARCHAR}, #{prCate,jdbcType=VARCHAR},
        #{ctRate,jdbcType=DECIMAL},#{prTotalRmb,jdbcType=DECIMAL},#{prTotalFc,jdbcType=DECIMAL},
        #{rrId,jdbcType=DECIMAL})
    </insert>
    <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.PaymentRecord">
        <selectKey keyProperty="prId" resultType="java.lang.Long" order="BEFORE">
            select
            <include refid="TABLE_SEQUENCE"/>
            from dual
        </selectKey>
        insert into PAYMENT_RECORD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="prId != null">
                PR_ID,
            </if>
            <if test="eawbPrintcode != null">
                EAWB_PRINTCODE,
            </if>
            <if test="mawbCode != null">
                MAWB_CODE,
            </if>
            <if test="ppId != null">
                PP_ID,
            </if>
            <if test="cmId != null">
                CM_ID,
            </if>
            <if test="soCode != null">
                SO_CODE,
            </if>
            <if test="ctCode != null">
                CT_CODE,
            </if>
            <if test="companyId != null">
                COMPANY_ID,
            </if>
            <if test="prName != null">
                PR_NAME,
            </if>
            <if test="prType != null">
                PR_TYPE,
            </if>
            <if test="prPlanAmount != null">
                PR_PLAN_AMOUNT,
            </if>
            <if test="prActualAmount != null">
                PR_ACTUAL_AMOUNT,
            </if>
            <if test="prStatus != null">
                PR_STATUS,
            </if>
            <if test="prUserId != null">
                PR_USER_ID,
            </if>
            <if test="prHandletime != null">
                PR_HANDLETIME,
            </if>
            <if test="prAwbType != null">
                PR_AWB_TYPE,
            </if>
            <if test="prRemark != null">
                PR_REMARK,
            </if>
            <if test="eawbReference1 != null">
                EAWB_REFERENCE1,
            </if>
            <if test="eawbReference2 != null">
                EAWB_REFERENCE2,
            </if>
            <if test="eawbChargeableweight != null">
                EAWB_CHARGEABLEWEIGHT,
            </if>
            <if test="eawbHawbQty != null">
                EAWB_HAWB_QTY,
            </if>
            <if test="agentCode != null">
                AGENT_CODE,
            </if>
            <if test="prOccurtime != null">
                PR_OCCURTIME,
            </if>
            <if test="eawbTrackingNo != null">
                EAWB_TRACKING_NO,
            </if>
            <if test="epKey != null">
                EP_KEY,
            </if>
            <if test="chargeableweight != null">
                CHARGEABLEWEIGHT,
            </if>
            <if test="pdSyscode != null">
                PD_SYSCODE,
            </if>
            <if test="outboundCompanyId != null">
                OUTBOUND_COMPANY_ID,
            </if>
            <if test="eawbIetype != null">
                EAWB_IETYPE,
            </if>
            <if test="prCate != null">
                PR_CATE,
            </if>
            <if test="ctRate != null">
                CT_RATE,
            </if>
            <if test="prTotalRmb != null">
                PR_TOTAL_RMB,
            </if>
            <if test="prTotalFc != null">
                PR_TOTAL_FC,
            </if>
            <if test="rrId != null">
                RR_ID,
            </if>

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="prId != null">
                #{prId,jdbcType=DECIMAL},
            </if>
            <if test="eawbPrintcode != null">
                #{eawbPrintcode,jdbcType=VARCHAR},
            </if>
            <if test="mawbCode != null">
                #{mawbCode,jdbcType=VARCHAR},
            </if>
            <if test="ppId != null">
                #{ppId,jdbcType=DECIMAL},
            </if>
            <if test="cmId != null">
                #{cmId,jdbcType=DECIMAL},
            </if>
            <if test="soCode != null">
                #{soCode,jdbcType=VARCHAR},
            </if>
            <if test="ctCode != null">
                #{ctCode,jdbcType=VARCHAR},
            </if>
            <if test="companyId != null">
                #{companyId,jdbcType=VARCHAR},
            </if>
            <if test="prName != null">
                #{prName,jdbcType=VARCHAR},
            </if>
            <if test="prType != null">
                #{prType,jdbcType=VARCHAR},
            </if>
            <if test="prPlanAmount != null">
                #{prPlanAmount,jdbcType=DECIMAL},
            </if>
            <if test="prActualAmount != null">
                #{prActualAmount,jdbcType=DECIMAL},
            </if>
            <if test="prStatus != null">
                #{prStatus,jdbcType=VARCHAR},
            </if>
            <if test="prUserId != null">
                #{prUserId,jdbcType=DECIMAL},
            </if>
            <if test="prHandletime != null">
                #{prHandletime,jdbcType=TIMESTAMP},
            </if>
            <if test="prAwbType != null">
                #{prAwbType,jdbcType=VARCHAR},
            </if>
            <if test="prRemark != null">
                #{prRemark,jdbcType=VARCHAR},
            </if>
            <if test="eawbReference1 != null">
                #{eawbReference1,jdbcType=VARCHAR},
            </if>
            <if test="eawbReference2 != null">
                #{eawbReference2,jdbcType=VARCHAR},
            </if>
            <if test="eawbChargeableweight != null">
                #{eawbChargeableweight,jdbcType=DECIMAL},
            </if>
            <if test="eawbHawbQty != null">
                #{eawbHawbQty,jdbcType=DECIMAL},
            </if>
            <if test="agentCode != null">
                #{agentCode,jdbcType=VARCHAR},
            </if>
            <if test="prOccurtime != null">
                #{prOccurtime,jdbcType=TIMESTAMP},
            </if>
            <if test="eawbTrackingNo != null">
                #{eawbTrackingNo,jdbcType=VARCHAR},
            </if>
            <if test="epKey != null">
                #{epKey,jdbcType=VARCHAR},
            </if>
            <if test="chargeableweight != null">
                #{chargeableweight,jdbcType=DECIMAL},
            </if>
            <if test="pdSyscode != null">
                #{pdSyscode,jdbcType=DECIMAL},
            </if>
            <if test="outboundCompanyId != null">
                #{outboundCompanyId,jdbcType=VARCHAR},
            </if>
            <if test="eawbIetype != null">
                #{eawbIetype,jdbcType=VARCHAR},
            </if>
            <if test="prCate != null">
                #{prCate,jdbcType=VARCHAR},
            </if>
            <if test="ctRate != null">
                #{ctRate,jdbcType=DECIMAL},
            </if>
            <if test="prTotalRmb != null">
                #{prTotalRmb,jdbcType=DECIMAL},
            </if>
            <if test="prTotalFc != null">
                #{prTotalFc,jdbcType=DECIMAL},
            </if>
            <if test="rrId != null">
                #{rrId,jdbcType=DECIMAL},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.PaymentRecord">
        update PAYMENT_RECORD
        <set>
            <if test="eawbPrintcode != null">
                EAWB_PRINTCODE = #{eawbPrintcode,jdbcType=VARCHAR},
            </if>
            <if test="mawbCode != null">
                MAWB_CODE = #{mawbCode,jdbcType=VARCHAR},
            </if>
            <if test="ppId != null">
                PP_ID = #{ppId,jdbcType=DECIMAL},
            </if>
            <if test="cmId != null">
                CM_ID = #{cmId,jdbcType=DECIMAL},
            </if>
            <if test="soCode != null">
                SO_CODE = #{soCode,jdbcType=VARCHAR},
            </if>
            <if test="ctCode != null">
                CT_CODE = #{ctCode,jdbcType=VARCHAR},
            </if>
            <if test="companyId != null">
                COMPANY_ID = #{companyId,jdbcType=VARCHAR},
            </if>
            <if test="prName != null">
                PR_NAME = #{prName,jdbcType=VARCHAR},
            </if>
            <if test="prType != null">
                PR_TYPE = #{prType,jdbcType=VARCHAR},
            </if>
            <if test="prPlanAmount != null">
                PR_PLAN_AMOUNT = #{prPlanAmount,jdbcType=DECIMAL},
            </if>
            <if test="prActualAmount != null">
                PR_ACTUAL_AMOUNT = #{prActualAmount,jdbcType=DECIMAL},
            </if>
            <if test="prStatus != null">
                PR_STATUS = #{prStatus,jdbcType=VARCHAR},
            </if>
            <if test="prUserId != null">
                PR_USER_ID = #{prUserId,jdbcType=DECIMAL},
            </if>
            <if test="prHandletime != null">
                PR_HANDLETIME = #{prHandletime,jdbcType=TIMESTAMP},
            </if>
            <if test="prAwbType != null">
                PR_AWB_TYPE = #{prAwbType,jdbcType=VARCHAR},
            </if>
            <if test="prRemark != null">
                PR_REMARK = #{prRemark,jdbcType=VARCHAR},
            </if>
            <if test="eawbReference1 != null">
                EAWB_REFERENCE1 = #{eawbReference1,jdbcType=VARCHAR},
            </if>
            <if test="eawbReference2 != null">
                EAWB_REFERENCE2 = #{eawbReference2,jdbcType=VARCHAR},
            </if>
            <if test="eawbChargeableweight != null">
                EAWB_CHARGEABLEWEIGHT = #{eawbChargeableweight,jdbcType=DECIMAL},
            </if>
            <if test="eawbHawbQty != null">
                EAWB_HAWB_QTY = #{eawbHawbQty,jdbcType=DECIMAL},
            </if>
            <if test="agentCode != null">
                AGENT_CODE = #{agentCode,jdbcType=VARCHAR},
            </if>
            <if test="prOccurtime != null">
                PR_OCCURTIME = #{prOccurtime,jdbcType=TIMESTAMP},
            </if>
            <if test="eawbTrackingNo != null">
                EAWB_TRACKING_NO = #{eawbTrackingNo,jdbcType=VARCHAR},
            </if>
            <if test="epKey != null">
                EP_KEY = #{epKey,jdbcType=VARCHAR},
            </if>
            <if test="chargeableweight != null">
                CHARGEABLEWEIGHT = #{chargeableweight,jdbcType=DECIMAL},
            </if>
            <if test="pdSyscode != null">
                PD_SYSCODE = #{pdSyscode,jdbcType=DECIMAL},
            </if>
            <if test="outboundCompanyId != null">
                OUTBOUND_COMPANY_ID = #{outboundCompanyId,jdbcType=VARCHAR},
            </if>
            <if test="eawbIetype != null">
                EAWB_IETYPE = #{eawbIetype,jdbcType=VARCHAR},
            </if>
            <if test="prCate != null">
                PR_CATE = #{prCate,jdbcType=VARCHAR},
            </if>
            <if test="ctRate != null">
                CT_RATE = #{ctRate,jdbcType=DECIMAL},
            </if>
            <if test="prTotalRmb != null">
                PR_TOTAL_RMB = #{prTotalRmb,jdbcType=DECIMAL},
            </if>
            <if test="prTotalFc != null">
                PR_TOTAL_FC = #{prTotalFc,jdbcType=DECIMAL},
            </if>
            <if test="rrId != null">
                RR_ID = #{rrId,jdbcType=DECIMAL},
            </if>
        </set>
        where PR_ID = #{prId,jdbcType=DECIMAL}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.PaymentRecord">
    update PAYMENT_RECORD
    set EAWB_PRINTCODE = #{eawbPrintcode,jdbcType=VARCHAR},
      MAWB_CODE = #{mawbCode,jdbcType=VARCHAR},
      PP_ID = #{ppId,jdbcType=DECIMAL},
      CM_ID = #{cmId,jdbcType=DECIMAL},
      SO_CODE = #{soCode,jdbcType=VARCHAR},
      CT_CODE = #{ctCode,jdbcType=VARCHAR},
      COMPANY_ID = #{companyId,jdbcType=VARCHAR},
      PR_NAME = #{prName,jdbcType=VARCHAR},
      PR_TYPE = #{prType,jdbcType=VARCHAR},
      PR_PLAN_AMOUNT = #{prPlanAmount,jdbcType=DECIMAL},
      PR_ACTUAL_AMOUNT = #{prActualAmount,jdbcType=DECIMAL},
      PR_STATUS = #{prStatus,jdbcType=VARCHAR},
      PR_USER_ID = #{prUserId,jdbcType=DECIMAL},
      PR_HANDLETIME = #{prHandletime,jdbcType=TIMESTAMP},
      PR_AWB_TYPE = #{prAwbType,jdbcType=VARCHAR},
      PR_REMARK = #{prRemark,jdbcType=VARCHAR},
      EAWB_REFERENCE1 = #{eawbReference1,jdbcType=VARCHAR},
      EAWB_REFERENCE2 = #{eawbReference2,jdbcType=VARCHAR},
      EAWB_CHARGEABLEWEIGHT = #{eawbChargeableweight,jdbcType=DECIMAL},
      EAWB_HAWB_QTY = #{eawbHawbQty,jdbcType=DECIMAL},
      AGENT_CODE = #{agentCode,jdbcType=VARCHAR},
      PR_OCCURTIME = #{prOccurtime,jdbcType=TIMESTAMP},
      EAWB_TRACKING_NO = #{eawbTrackingNo,jdbcType=VARCHAR},
      EP_KEY = #{epKey,jdbcType=VARCHAR},
      CHARGEABLEWEIGHT = #{chargeableweight,jdbcType=DECIMAL},
      PD_SYSCODE = #{pdSyscode,jdbcType=DECIMAL},
      OUTBOUND_COMPANY_ID = #{outboundCompanyId,jdbcType=VARCHAR},
      EAWB_IETYPE = #{eawbIetype,jdbcType=VARCHAR},
      PR_CATE = #{prCate,jdbcType=VARCHAR},
      CT_RATE = #{ctRate,jdbcType=DECIMAL},
      PR_TOTAL_RMB = #{prTotalRmb,jdbcType=DECIMAL},
      PR_TOTAL_FC = #{prTotalFc,jdbcType=DECIMAL},
      RR_ID = #{rrId,jdbcType=DECIMAL}
    where PR_ID = #{prId,jdbcType=DECIMAL}
  </update>
    <select id="selectPaymentForTools" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from PAYMENT_RECORD
        where 1=1
        <if test="searchCode!=null and searchCode!=''">
            and (EAWB_PRINTCODE=#{searchCode} or MAWB_CODE=#{searchCode} or EAWB_REFERENCE1=#{searchCode} or
            EAWB_REFERENCE2=#{searchCode})
        </if>
        <if test="feeName!=null and feeName!=''">
            and PR_NAME=#{feeName}
        </if>
        <if test="soCode!=null and soCode!=''">
            and SO_CODE=#{soCode}
        </if>
    </select>

    <select id="selectPaymentForToolsByPrintCode" resultType="java.util.HashMap">
        select
        t1.*,t2.*,nvl(t1.EAWB_CHARGEABLEWEIGHT,t2.EAWB_CHARGEABLEWEIGHT) as WEIGHT, T2.EAWB_SERVICETYPE,
        T2.EAWB_SO_CODE,T2.EAWB_TRANSMODEID,T2.EAWB_TRACKING_NO
        from PAYMENT_RECORD t1 LEFT JOIN EXPRESSAIRWAYBILL t2 ON
        t2.EAWB_PRINTCODE=t1.EAWB_PRINTCODE
        where 1=1
        <if test="searchCodes!=null and searchCodes!=''">
            and t1.EAWB_PRINTCODE in
            <foreach collection="searchCodes" item="searchCode" open="(" close=")" separator=",">
                #{searchCode}
            </foreach>
        </if>
        <if test="feeName!=null and feeName!=''">
            and PR_NAME=#{feeName}
        </if>
        <if test="soCode!=null and soCode!=''">
            and SO_CODE=#{soCode}
        </if>
    </select>
    <select id="selectPaymentForToolsByMawbCode" resultType="java.util.HashMap">
        select DISTINCT
        t1.*,t2.*,nvl(t1.EAWB_CHARGEABLEWEIGHT,t2.EAWB_CHARGEABLEWEIGHT) as WEIGHT, null,
        T2.EAWB_SO_CODE,T2.EAWB_TRANSMODEID,T2.EAWB_TRACKING_NO
        from PAYMENT_RECORD t1 LEFT JOIN EXPRESSAIRWAYBILL t2 ON
        t2.MAWB_CODE=t1.MAWB_CODE
        where 1=1
        <if test="searchCodes!=null and searchCodes!=''">
            and t1.MAWB_CODE in
            <foreach collection="searchCodes" item="searchCode" open="(" close=")" separator=",">
                #{searchCode}
            </foreach>
        </if>
        <if test="feeName!=null and feeName!=''">
            and PR_NAME=#{feeName}
        </if>
        <if test="soCode!=null and soCode!=''">
            and SO_CODE=#{soCode}
        </if>
    </select>
    <select id="selectPaymentForToolsByRefernce1" resultType="java.util.HashMap">
        select
        t1.*,t2.*,nvl(t1.EAWB_CHARGEABLEWEIGHT,t2.EAWB_CHARGEABLEWEIGHT) as WEIGHT, T2.EAWB_SERVICETYPE,
        T2.EAWB_SO_CODE,T2.EAWB_TRANSMODEID,T2.EAWB_TRACKING_NO
        from PAYMENT_RECORD t1 LEFT JOIN EXPRESSAIRWAYBILL t2 ON
        t2.EAWB_PRINTCODE=t1.EAWB_PRINTCODE
        where 1=1
        <if test="searchCodes!=null and searchCodes!=''">
            and t1.EAWB_REFERENCE1 in
            <foreach collection="searchCodes" item="searchCode" open="(" close=")" separator=",">
                #{searchCode}
            </foreach>
        </if>
        <if test="feeName!=null and feeName!=''">
            and PR_NAME=#{feeName}
        </if>
        <if test="soCode!=null and soCode!=''">
            and SO_CODE=#{soCode}
        </if>
    </select>
    <select id="selectPaymentForToolsByRefrence2" resultType="java.util.HashMap">
        select
        t1.*,t2.*,nvl(t1.EAWB_CHARGEABLEWEIGHT,t2.EAWB_CHARGEABLEWEIGHT) as WEIGHT, T2.EAWB_SERVICETYPE,
        T2.EAWB_SO_CODE,T2.EAWB_TRANSMODEID,T2.EAWB_TRACKING_NO
        from PAYMENT_RECORD t1 LEFT JOIN EXPRESSAIRWAYBILL t2 ON
        t2.EAWB_PRINTCODE=t1.EAWB_PRINTCODE
        where 1=1
        <if test="searchCodes!=null and searchCodes!=''">
            and t1.EAWB_REFERENCE2 in
            <foreach collection="searchCodes" item="searchCode" open="(" close=")" separator=",">
                #{searchCode}
            </foreach>
        </if>
        <if test="feeName!=null and feeName!=''">
            and PR_NAME=#{feeName}
        </if>
        <if test="soCode!=null and soCode!=''">
            and SO_CODE=#{soCode}
        </if>
    </select>
    <select id="selectPaymentForToolsByTrackingNo" resultType="java.util.HashMap">
        select
        t1.*,t2.*,nvl(t1.EAWB_CHARGEABLEWEIGHT,t2.EAWB_CHARGEABLEWEIGHT) as WEIGHT, T2.EAWB_SERVICETYPE,
        T2.EAWB_SO_CODE,T2.EAWB_TRANSMODEID,T2.EAWB_TRACKING_NO
        from PAYMENT_RECORD t1 LEFT JOIN EXPRESSAIRWAYBILL t2 ON
        t2.EAWB_PRINTCODE=t1.EAWB_PRINTCODE
        where 1=1
        <if test="searchCodes!=null and searchCodes!=''">
            and T2.EAWB_TRACKING_NO in
            <foreach collection="searchCodes" item="searchCode" open="(" close=")" separator=",">
                #{searchCode}
            </foreach>
        </if>
        <if test="feeName!=null and feeName!=''">
            and PR_NAME=#{feeName}
        </if>
        <if test="soCode!=null and soCode!=''">
            and SO_CODE=#{soCode}
        </if>


    </select>


    <!--解除账单明细绑定-->
    <update id="updateToUnlockCredit">
        update PAYMENT_RECORD set CM_ID=NULL where CM_ID=#{invoiceId}
    </update>
    <select id="getAwbPayMent" resultType="java.util.HashMap" parameterType="com.sinoair.billing.domain.vo.FilterParam">
        select DISTINCT '已录入' as STATUS,pr.pr_id as PRID, ea.ea_code as EACODE,sp.sp_name as SONAME,sp.sp_code as
        SOCODE,s.s_name asSNAME,
        ea.EAWB_IETYPE as IETYPE,
        pp.pp_type as
        PPTYPE,pp.PP_BASEPRICE as BASEPRICE,
        pr.eawb_chargeableweight as WEIGHT,
        pr.eawb_hawb_qty as EAWBS,
        pr.PR_PLAN_AMOUNT as PRPLANAMOUNT,
        pp.ct_code as CTCODE,
        ct.ct_name as CTNAME,
        pp.pp_name as PPNAME,
        pp.pp_price as PPPRICE,pp.pp_minprice as PPMINPRICE,pp.pp_firstweight as PPFIRSTWEIGHT,pp.PP_ID as PPID,
        pp.pp_firstprice as PPFIRSTPRICE,pp.pp_additionalweight as PPADDITIONALWEIGHT,pp.PP_ADDITIONALPRICE
        as PPADDITIONALPRICE,pg.PG_INNERWEIGHT as PGINNERWEIGHT,pg.PG_INNERPRICE as PGINNERPRICE,
        pg.pg_start as PGSTART,pg.pg_end as PGEND,pg.pg_fixprice as PGFIXPRICE,pg.pg_type as PGTYPE,
        sp.sp_type as SPTYPE,pr.pr_remark as REMARK
        from service s,SUPPLIER sp,
        (select
        e.MAWB_P_WEIGHT,e.EAWB_IETYPE,e.ea_handletime,e.transmodeid,p.pp_status,p.pp_id,e.EA_TOTAL_EAWBS,e.ea_code,p.pp_name
        from
        expressassignment e,payment_record pr,
        price_payment p,service s where s.s_id=p.s_id and e.ea_code=pr.MAWB_CODE and pr.pp_id=p.pp_id
        and p.PP_EFFECTIVEDATE &lt;= e.ea_handletime and e.ea_handletime &lt;= p.PP_EXPIREDDATE)
        ea ,
        payment_record pr,
        CURRENCYTYPE ct, price_payment pp
        left join price_grade pg on
        pp.pp_type='GRADE'
        where s.s_id=pp.s_id
        and ea.ea_code=pr.MAWB_CODE
        and ea.pp_name=pr.pr_name
        and pp.ct_code=ct.ct_code
        and s.s_status='ON'
        and ea.pp_id = pp.pp_id
        and ea.pp_status='ON'
        and s.so_code=sp.sp_code
        and pr.so_code=sp.sp_code
        and sp.sp_status='ON'
        and s.s_awb_type='M'
        and ((pp.pp_type='GRADE' and
        pg.pg_id=getPGRowNum(nvl(pr.eawb_chargeableweight,ea.MAWB_P_WEIGHT),pp.pp_id))
        or(pp.pp_type!='GRADE')or(pp.pp_type is null))
        and pr.cm_id is null
        and pr.COMPANY_ID=#{companyId}
        <if test="code!=null and code!= ''">
            and sp.SP_code=#{code}
        </if>
        <if test="starttime!=null and starttime != ''">
            and to_char(ea.ea_handletime,'yyyy-mm-dd') &gt;= #{starttime}
        </if>
        <if test="endtime!=null and endtime != ''">
            and to_char(ea.ea_handletime,'yyyy-mm-dd') &lt;= #{endtime}
        </if>
        <!-- 初始进入时 返回空 AND ea.ea_handletime &gt;=sysdate-2 AND ea.ea_handletime &lt;sysdate+1 -->
        <if test="(mawbs==null or mawbs=='') and (name==null or name == '') and (starttime==null or starttime == '') and (endtime==null or endtime == '') and (code==null or code == '') and (servers==null or servers == '')">
            AND 1=2
        </if>
        <if test="mawbs!=null and mawbs!=''">
            and ea.ea_code in
            <foreach collection="mawbs" item="mawb" open="(" close=")" separator=",">
                #{mawb}
            </foreach>
        </if>
        <if test="servers !=null and servers !=''">
            and s.S_NAME in
            <foreach collection="servers" item="server" open="(" close=")" separator=",">
                #{server}
            </foreach>
        </if>
        UNION
        select DISTINCT '未录入' as STATUS,null,ea.ea_code as EACODE,sp.sp_name as SONAME,sp.sp_code as SOCODE,s.s_name as
        SNAME,ea.EAWB_IETYPE as IETYPE,pp.pp_type as
        PPTYPE,pp.PP_BASEPRICE as BASEPRICE,
        ea.MAWB_P_WEIGHT as WEIGHT,
        ea.EA_TOTAL_EAWBS as EAWBS,
        null as PRPLANAMOUNT,
        pp.ct_code as CTCODE,
        ct.ct_name as CTNAME,
        pp.pp_name as PPNAME,
        pp.pp_price as PPPRICE,pp.pp_minprice as PPMINPRICE,pp.pp_firstweight as PPFIRSTWEIGHT,pp.PP_ID as PPID,
        pp.pp_firstprice as PPFIRSTPRICE,pp.pp_additionalweight as PPADDITIONALWEIGHT,pp.PP_ADDITIONALPRICE
        as PPADDITIONALPRICE,pg.PG_INNERWEIGHT as PGINNERWEIGHT,pg.PG_INNERPRICE as PGINNERPRICE,
        pg.pg_start as PGSTART,pg.pg_end as PGEND,pg.pg_fixprice as PGFIXPRICE,pg.pg_type as PGTYPE,
        sp.sp_type as SPTYPE,null
        from service s,SUPPLIER sp,
        (select
        e.MAWB_P_WEIGHT,e.EAWB_IETYPE,e.ea_handletime,e.transmodeid,p.pp_status,p.pp_id,e.EA_TOTAL_EAWBS,e.ea_code,p.pp_name
        from
        expressassignment e,
        price_payment p,service s where s.s_id=p.s_id
        and p.PP_EFFECTIVEDATE &lt;= e.ea_handletime and e.ea_handletime &lt;= p.PP_EXPIREDDATE)
        ea ,
        CURRENCYTYPE ct, price_payment pp
        left join price_grade pg on
        pp.pp_type='GRADE'
        where s.s_id=pp.s_id
        and pp.ct_code=ct.ct_code
        and s.s_status='ON'
        and ea.pp_id = pp.pp_id
        and ea.pp_status='ON'
        and s.so_code=sp.sp_code
        and sp.sp_status='ON'
        and s.s_awb_type='M'
        and (sp.sp_type &lt;&gt; '外运发展' or sp.sp_fax ='Y')
        and sp.sp_type &lt;&gt; '内部部门'
        and ((pp.pp_type='GRADE' and
        pg.pg_id=getPGRowNum(ea.MAWB_P_WEIGHT,pp.pp_id))
        or(pp.pp_type!='GRADE')or(pp.pp_type is null))
        <if test="code!=null and code!= ''">
            and sp.SP_code=#{code}
        </if>
        <if test="starttime!=null and starttime != ''">
            and to_char(ea.ea_handletime,'yyyy-mm-dd') &gt;= #{starttime}
        </if>
        <if test="endtime!=null and endtime != ''">
            and to_char(ea.ea_handletime,'yyyy-mm-dd') &lt;= #{endtime}
        </if>
        <!-- 初始进入时 返回空 AND ea.ea_handletime &gt;=sysdate-2 AND ea.ea_handletime &lt;sysdate+1 -->
        <if test="(mawbs==null or mawbs=='') and (name==null or name == '') and (starttime==null or starttime == '') and (endtime==null or endtime == '')  and (code==null or code == '') and (servers==null or servers == '')">
            AND 1=2
        </if>
        <if test="mawbs!=null and mawbs!=''">
            and ea.ea_code in
            <foreach collection="mawbs" item="mawb" open="(" close=")" separator=",">
                #{mawb}
            </foreach>
        </if>
        <if test="servers !=null and servers !=''">
            and s.S_NAME in
            <foreach collection="servers" item="server" open="(" close=")" separator=",">
                #{server}
            </foreach>
        </if>
    </select>
    <select id="getGradePrice" resultType="com.sinoair.billing.domain.model.billing.PriceGrade">
        select * from price_grade where pg_id=getpgrownum(#{val},#{ppid})
    </select>


    <select id="countByCM" resultType="java.util.HashMap">
        select sum(PR_PLAN_AMOUNT) as PR_PLAN_AMOUNT_COUNT,
        sum(PR_ACTUAL_AMOUNT) as PR_ACTUAL_AMOUNT_COUNT,
        sum(EAWB_CHARGEABLEWEIGHT) as EAWB_CHARGEABLEWEIGHT_COUNT,
        count(PR_ID) as EAWB_HAWB_QTY_COUNT
        from PAYMENT_RECORD
        where CM_ID=#{cmId}
    </select>
    <select id="selectPaymentByMawbCodeAndPrName" parameterType="com.sinoair.billing.domain.model.billing.PaymentRecord"
            resultType="com.sinoair.billing.domain.model.billing.PaymentRecord">
        select * from PAYMENT_RECORD where MAWB_CODE=#{mawbCode} and PR_NAME=#{prName}
    </select>

    <select id="selectPaymentForDownload" resultType="java.util.HashMap">
        select
        nvl(t1.EAWB_PRINTCODE,'') as EAWB_PRINTCODE,
        nvl(t1.EAWB_REFERENCE1,'') as EAWB_REFERENCE1,
        nvl(t1.EAWB_REFERENCE2,'') as EAWB_REFERENCE2,
        nvl(t1.MAWB_CODE,'') as MAWB_CODE,
        nvl( t1.PR_NAME,'') as PR_NAME,
        nvl( t1.PR_PLAN_AMOUNT,'') as PR_PLAN_AMOUNT,
        nvl(t1.PR_AWB_TYPE,'') as PR_AWB_TYPE,
        nvl(t1.EAWB_CHARGEABLEWEIGHT,'') as EAWB_CHARGEABLEWEIGHT,
        nvl( t1.EAWB_HAWB_QTY,'') as EAWB_HAWB_QTY,
        nvl( t1.AGENT_CODE,'') as AGENT_CODE,
        nvl( t1.SO_CODE,'') as SO_CODE,
        nvl( t1.EAWB_TRACKING_NO,'') as EAWB_TRACKING_NO,
        nvl(t2.SP_NAME,'') as SP_NAME,
        nvl(t3.CM_CODE,'') as CM_CODE

        from
        PAYMENT_RECORD t1 left join
        SUPPLIER t2 on t1.SO_CODE = t2.SP_CODE
        LEFT JOIN
        CREDIT_MANIFEST t3 on t1.CM_ID = t3.CM_ID
        where 1=1
        <if test="startTime != null and startTime !=''">
            and t1.PR_OCCURTIME&gt;=TO_DATE(#{startTime},'yyyy-mm-dd')
        </if>
        <if test="endTime != null and endTime !=''">
            and t1 .PR_OCCURTIME&lt;=TO_DATE(#{endTime},'yyyy-mm-dd')
        </if>
        <if test="companyId != null and companyId !=''">
            and t1.COMPANY_ID=#{companyId}
        </if>
        <if test="comsele != null and comsele !=''">
            and t1.SO_CODE=#{comsele}
        </if>

    </select>

    <select id="selectPaymentForDownload2" resultType="java.util.HashMap">
        select
        nvl(t1.EAWB_PRINTCODE,'') as EAWB_PRINTCODE,
        nvl(t1.EAWB_REFERENCE1,'') as EAWB_REFERENCE1,
        nvl(t1.EAWB_REFERENCE2,'') as EAWB_REFERENCE2,
        nvl(t1.MAWB_CODE,'') as MAWB_CODE,
        nvl( t1.PR_NAME,'') as PR_NAME,
        nvl( t1.PR_PLAN_AMOUNT,'') as PR_PLAN_AMOUNT,
        nvl(t1.PR_AWB_TYPE,'') as PR_AWB_TYPE,
        nvl(t1.EAWB_CHARGEABLEWEIGHT,'') as EAWB_CHARGEABLEWEIGHT,
        nvl( t1.EAWB_HAWB_QTY,'') as EAWB_HAWB_QTY,
        nvl( t1.EAWB_TRACKING_NO,'') as EAWB_TRACKING_NO,
        nvl( t1.AGENT_CODE,'') as AGENT_CODE,
        nvl( t1.SO_CODE,'') as SO_CODE,
        nvl(t2.SP_NAME,'') as SP_NAME,
        nvl(t3.CM_CODE,'') as CM_CODE

        from
        PAYMENT_RECORD t1 left join
        SUPPLIER t2 on t1.SO_CODE = t2.SP_CODE
        LEFT JOIN
        CREDIT_MANIFEST t3 on t1.CM_ID = t3.CM_ID
        where 1=1
        <if test="companyId != null and companyId !=''">
            and t1.COMPANY_ID=#{companyId}
        </if>
        <if test="comsele != null and comsele !=''">
            and t1.SO_CODE=#{comsele}
        </if>
        <if test="inoiceID != null and inoiceID !=''">
            and t1.CM_ID=#{inoiceID}
        </if>
    </select>
    <!-- 没有条件时，将pr_handletime 换成 pr_occurtime ，同时增加单号查询条件-->
    <select id="selectInciPayList" resultType="java.util.Map" parameterType="com.sinoair.billing.domain.vo.FilterParam">
        select pr.*,sp.SP_NAME from PAYMENT_RECORD pr,price_define pd,SUPPLIER sp where
        pr.pd_syscode=pd.pd_syscode
        and pd.pd_type='INCIDENT'
        and pr.so_code=sp.sp_code
        and pr.COMPANY_ID=#{companyId}
        <if test=" pdSyscode!=0 ">
            and pr.pd_syscode=#{pdSyscode}
        </if>
        <if test="starttime!=null and starttime != ''">
            and to_char(pr.pr_occurtime,'yyyy-mm-dd') &gt;= #{starttime}
        </if>
        <if test="endtime!=null and endtime != ''">
            and to_char(pr.pr_occurtime,'yyyy-mm-dd') &lt;= #{endtime}
        </if>
        <if test="code!=null and code!=''">
            and pr.so_code=#{code}
        </if>
        <if test="eastCode!=null and eastCode!=''">
            and pr.EAWB_PRINTCODE=#{eastCode}
        </if>
        <!-- AND pr.pr_occurtime &gt;=sysdate-2 AND pr.pr_occurtime &lt;sysdate+1 -->
        <if test="(code==null or code=='')and pdSyscode==0 and (starttime==null or starttime == '')
        and (endtime==null or endtime == '') and (eastCode==null or eastCode == '')">
            AND  1 =2
        </if>
    </select>
    <!--应收账单转应付修改账单ID-->
    <update id="updateCmIdByDmId">
        update PAYMENT_RECORD set CM_ID = #{cmId,jdbcType=DECIMAL} WHERE
        MAWB_CODE in (select mawb_code from RECEIPT_RECORD WHERE dm_id=#{dmId,jdbcType=DECIMAL})
    </update>

    <!--按按eawbprintcode,eawbReference1, eawbReference2更新-->
    <update id="batchUpdateEawbTrackingNoByCode" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
            update payment_record
            <set>
                <if test="item.serviceType!=null and item.serviceType !=''">
                    ep_key = #{item.serviceType},
                </if>
                <if test="item.eawbTrackingNo!=null and item.eawbTrackingNo!=''">
                    eawb_tracking_no = #{item.eawbTrackingNo},
                </if>
                <if test="item.eawbUpdatetime!=null and item.eawbUpdatetime!=''">
                    pr_handletime =#{item.eawbUpdatetime}
                </if>
            </set>
            where
            <if test="item.serviceType ==null or item.serviceType ==''">
                ep_key in ('DISTRIBUTOR_11180269','DISTRIBUTOR_MBXPXB',
                'DISTRIBUTOR_MBXPXN','DISTRIBUTOR_MBXPXP')
                and
            </if>
            (
            eawb_printcode=#{item.code}
            or eawb_reference1=#{item.code}
            or eawb_reference2=#{item.code}
            )
        </foreach>
    </update>


    <!--按总单更新-->
    <update id="updateEawbTrackingNoByMawbCode" parameterType="java.lang.String">
        update payment_record
        <set>
            <if test="serviceType !=null and serviceType !=''">
                ep_key = #{serviceType},
            </if>
            <if test="eawbTrackingNo!=null and eawbTrackingNo!=''">
                eawb_tracking_no = #{eawbTrackingNo},
            </if>
            <if test="prHandletime!=null and prHandletime!=''">
                pr_handletime = #{prHandletime}
            </if>
        </set>
        where
        <if test="serviceType ==null or serviceType ==''">
            ep_key = 'DISTRIBUTOR_11180269' and
        </if>
        mawb_code = #{mawbCode}

    </update>
    <update id="updateBacth" parameterType="java.util.List">

        <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
            update payment_record
            <set>
                <if test="item.prActualAmount !=null and item.prActualAmount!=''">
                    PR_ACTUAL_AMOUNT=#{item.prActualAmount},
                </if>
                <if test="item.prAwbType !=null and item.prAwbType!=''">
                    PR_AWB_TYPE=#{item.prAwbType},
                </if>
                <if test="item.eawbChargeableweight !=null and item.eawbChargeableweight!=''">
                    EAWB_CHARGEABLEWEIGHT=#{item.eawbChargeableweight},
                </if>
                <if test="item.eawbHawbQty !=null and item.eawbHawbQty!=''">
                    EAWB_HAWB_QTY=#{item.eawbHawbQty},
                </if>
                <if test="item.cmId !=null and item.cmId!=''">
                    CM_ID=#{item.cmId},
                </if>
            </set>
            where PR_ID = ${item.prId} and CM_ID is NULL
        </foreach>

    </update>

    <select id="selectByPrimaryKey2" resultType="com.sinoair.billing.domain.model.billing.PaymentRecord"
            parameterType="java.lang.Long">
        select
        PR_ID,CM_ID
        from PAYMENT_RECORD
        where PR_ID = #{prId,jdbcType=DECIMAL}
    </select>
    <select id="selectUploadVerifyIdList" resultType="java.math.BigDecimal">
      select PR_ID from PAYMENT_RECORD
      where MAWB_CODE=#{mawbCode} and PD_SYSCODE=#{pdSysCode} and SO_CODE=#{soCode}
    </select>
    <update id="updatePRCMID">
        ${s}
    </update>
    <select id="selectOneline" resultType="com.sinoair.billing.domain.model.billing.PaymentRecord">
        select * from PAYMENT_RECORD where cm_id=#{cmId} and rownum=1
    </select>
    <select id="selectByReference1" resultType="com.sinoair.billing.domain.model.billing.PaymentRecord">
        select eawb_reference1,eawb_chargeableweight,pr_actual_amount,ep_key
        from PAYMENT_RECORD where eawb_reference1=#{eawbReference1} and pr_name = '代付税金' and rownum=1
    </select>
    <select id="selectByReference2" resultType="com.sinoair.billing.domain.model.billing.PaymentRecord">
        select eawb_reference2 as eawb_reference1,eawb_chargeableweight,pr_actual_amount,ep_key
        from PAYMENT_RECORD where EAWB_REFERENCE2=#{eawbReference1} and pr_name = '配送服务费' and rownum=1
    </select>
    <select id="selectByTrackingNo" resultType="com.sinoair.billing.domain.model.billing.PaymentRecord">
        select eawb_tracking_no as eawb_reference1,eawb_chargeableweight,pr_actual_amount,ep_key
        from PAYMENT_RECORD where eawb_tracking_no=#{eawbReference1} and pr_name = '挂号服务费' and rownum=1
    </select>

    <insert id="insertBatch">
        insert into PAYMENT_RECORD (PR_ID, EAWB_PRINTCODE, MAWB_CODE,
        PP_ID, CM_ID, SO_CODE,
        CT_CODE, COMPANY_ID, PR_NAME,
        PR_TYPE, PR_PLAN_AMOUNT, PR_ACTUAL_AMOUNT,
        PR_STATUS, PR_USER_ID, PR_HANDLETIME,
        PR_AWB_TYPE, PR_REMARK, EAWB_REFERENCE1,
        EAWB_REFERENCE2, EAWB_CHARGEABLEWEIGHT, EAWB_HAWB_QTY,
        AGENT_CODE, PR_OCCURTIME,PR_OCCURTIME2, EAWB_TRACKING_NO,
        EP_KEY, CHARGEABLEWEIGHT, PD_SYSCODE,
        OUTBOUND_COMPANY_ID, EAWB_IETYPE,ESTIMATE_STATUS,
        PR_CATE,CT_RATE,PR_TOTAL_RMB,PR_TOTAL_FC,
        RR_ID,EAWB_SO_CODE,SERVICE_ID,PARTITION_CODE,SERVICE_DETAIL_ID,FEE_STATUS)
        select SEQ_PAYMENT_RECORD.NEXTVAL,pbd.* from (
        <foreach collection="list" item="item" index="index" separator="UNION ALL">
            select #{item.eawbPrintcode,jdbcType=VARCHAR}, #{item.mawbCode,jdbcType=VARCHAR},
            #{item.ppId,jdbcType=DECIMAL}, #{item.cmId,jdbcType=DECIMAL}, #{item.soCode,jdbcType=VARCHAR},
            #{item.ctCode,jdbcType=VARCHAR}, #{item.companyId,jdbcType=VARCHAR}, #{item.prName,jdbcType=VARCHAR},
            #{item.prType,jdbcType=VARCHAR}, #{item.prPlanAmount,jdbcType=DECIMAL}, #{item.prActualAmount,jdbcType=DECIMAL},
            'ON', 1, sysdate,
            #{item.prAwbType,jdbcType=VARCHAR}, #{item.prRemark,jdbcType=VARCHAR}, #{item.eawbReference1,jdbcType=VARCHAR},
            #{item.eawbReference2,jdbcType=VARCHAR}, #{item.eawbChargeableweight,jdbcType=DECIMAL}, #{item.eawbHawbQty,jdbcType=DECIMAL},
            #{item.agentCode,jdbcType=VARCHAR}, #{item.prOccurtime,jdbcType=TIMESTAMP},#{item.prOccurtime,jdbcType=TIMESTAMP}, #{item.eawbTrackingNo,jdbcType=VARCHAR},
            #{item.epKey,jdbcType=VARCHAR}, #{item.chargeableweight,jdbcType=DECIMAL}, #{item.pdSyscode,jdbcType=DECIMAL},
            #{item.outboundCompanyId,jdbcType=VARCHAR}, 'E', #{item.estimateStatus,jdbcType=VARCHAR} , #{item.prCate,jdbcType=VARCHAR},
            #{item.ctRate,jdbcType=DECIMAL}, #{item.prTotalRmb,jdbcType=DECIMAL},#{item.prTotalFc,jdbcType=DECIMAL},#{item.rrId,jdbcType=DECIMAL},
            #{item.eawbSoCode,jdbcType=VARCHAR},#{item.serviceId,jdbcType=DECIMAL},#{item.partitionCode,jdbcType=VARCHAR},#{item.serviceDetailId,jdbcType=DECIMAL},
            #{item.feeStatus,jdbcType=DECIMAL}
            from dual
        </foreach>
        ) pbd
    </insert>

    <select id="countPaymentRecord" resultType="int" parameterType="com.sinoair.billing.domain.model.billing.PaymentRecord">
        select count(0)
        from PAYMENT_RECORD pr
        where pr.eawb_printcode=#{eawbPrintcode,jdbcType=VARCHAR}
          and pr.company_id=#{companyId,jdbcType=VARCHAR}
          and pr.so_code=#{soCode,jdbcType=VARCHAR}
          and pr.PR_NAME=#{prName,jdbcType=VARCHAR}
    </select>

    <select id="getPaymentPrice1" resultType="java.lang.Double" parameterType="java.util.Map">
        select round(getPaymentprice1(#{eawbPrintcode},#{ppId}),2) result from dual
    </select>

    <select id="getPaymentPrice2" resultType="java.lang.Double" parameterType="java.util.Map">
        select round(getPaymentprice2(#{eawbPrintcode},#{ppId}),2) result from dual
    </select>


    <update id="updateCM" parameterType="java.lang.String">
        update payment_record
        <set>
            CM_ID = #{cmId}
        </set>
        where 1=1
        and PR_OCCURTIME>=to_date(#{startOccurtime}, 'yyyy-mm-dd')
        and PR_OCCURTIME &lt;to_date(#{endOccurtime}, 'yyyy-mm-dd')
        and COMPANY_ID =#{sacId}
        and SO_CODE = #{soCode}
        and CT_CODE = #{ctCode}
        and EAWB_IETYPE = #{ieType}
        and CM_ID is null

    </update>

    <select id="getCMByOccurtime" resultType="java.lang.Integer" parameterType="java.util.Map">
        select count(0) from payment_record
        where 1=1
        and PR_OCCURTIME>=to_date(#{startOccurtime}, 'yyyy-mm-dd')
        and PR_OCCURTIME &lt;to_date(#{endOccurtime}, 'yyyy-mm-dd')
        and COMPANY_ID =#{sacId}
        and SO_CODE = #{soCode}
        and CT_CODE = #{ctCode}
        and EAWB_IETYPE = #{ieType}
        and CM_ID is null

    </select>

    <select id="selectPpSupplierList" resultType="com.sinoair.billing.domain.model.billing.Supplier"
        parameterType="com.sinoair.billing.domain.model.billing.CreditManifest">
        select distinct so_code as spCode ,company_id ,ct_code
        from payment_record
        where so_code is not null
            and bpd_syscode is null
          and PR_OCCURTIME>=sysdate - 60
          and PR_OCCURTIME &lt;=#{cmEndTime}
        <if test="companyId !=null and companyId!=''">
            and COMPANY_ID =#{companyId}
        </if>

    </select>

    <update id="updateBpdSyscode" parameterType="java.util.Map">
        update payment_record rd
        set rd.bpd_syscode         = #{bpdSyscode},
        rd.pr_user_id    = #{rrUserId},
        rd.pr_handletime = #{rrHandletime}
        <if test="discountValue!=null and discountValue!=1.0 and discountValue!=0.0">
            ,rd.pr_actual_amount=rd.pr_actual_amount*#{discountValue}
        </if>
        where
        rd.pr_id in
        (
        select pr.pr_id
        from payment_record pr,expressairwaybill e
        where  pr.eawb_printcode = e.eawb_printcode
        and pr.bpd_syscode is null
        and pr.company_id=#{sacId}
        and pr.pr_status='ON'
        and pr.ct_code=#{ctCode}
        and pr.so_code=#{soCode}
        <if test="cmId!=null and cmId!=''">
            and pr.cm_id=#{cmId}
        </if>
        <if test="cmId==null or cmId==''">
            and pr.cm_id is null
        </if>
        <if test="sinotransId!=null and sinotransId!=''">
            and e.sinotrans_id=#{sinotransId}
        </if>
        <if test="sinotransId==null or sinotransId==''">
            and e.sinotrans_id is null
        </if>
        <if test="cmId==null ">
            and pr.cm_id is null
        </if>
        <if test="cmId !=null ">
            and pr.cm_id=#{cmId}
        </if>
        and e.eawb_servicetype_original=#{epKey}
        and to_char(pr.pr_occurtime,'yyyymmdd') =#{day}
        )


    </update>

    <select id="selectRecordCountByServiceId" resultType="com.sinoair.billing.domain.vo.price.RecordTem">
        select service_id serviceId,count(0) cou from payment_record where SERVICE_ID in
        <foreach collection="list" item="serviceId" index="index" open="(" close=")" separator=",">
           #{serviceId}
        </foreach>
        and PR_TYPE='A'
        group by service_id having count(0)>0
    </select>
    <select id="existPaymentRecordActualityList" resultType="java.lang.Integer">
        select count(0) from payment_record where eawb_printcode=#{eawbPrintcode} and SERVICE_ID=#{serviceId} and PD_SYSCODE=#{pdSyscode} and so_code=#{soCode}
    </select>
    <select id="existPaymentRecordPlanList" resultType="java.lang.Integer">
        select count(0) from payment_record where eawb_printcode=#{eawbPrintcode} and SERVICE_ID=#{serviceId} and PD_SYSCODE=#{pdSyscode} and pr_type='P'
    </select>

    <select id="selectRecordIdByDetailIdAndFeeId" resultMap="BaseResultMap">
       select * from (select * from payment_record where EAWB_PRINTCODE=#{eawbPrintCode} and SERVICE_DETAIL_ID=#{serviceDetailId} and PD_SYSCODE=#{pdSyscode} and FEE_STATUS='0') where rownum=1
    </select>

    <update id="updateTimeAndStatusById">
       update payment_record set FEE_STATUS=1,PR_OCCURTIME=#{prOccurtime,jdbcType=TIMESTAMP} where PR_ID = ${prId}
    </update>
</mapper>