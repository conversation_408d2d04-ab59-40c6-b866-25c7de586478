<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.PriceDefineMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.PriceDefine" >
    <id column="PD_SYSCODE" property="pdSyscode" jdbcType="DECIMAL" />
    <result column="PD_NAME" property="pdName" jdbcType="VARCHAR" />
    <result column="PD_TYPE" property="pdType" jdbcType="VARCHAR" />
    <result column="PD_MODE" property="pdMode" jdbcType="VARCHAR" />
    <result column="SAC_ID" property="sacId" jdbcType="VARCHAR" />
    <result column="BT_CODE" property="btCode" jdbcType="VARCHAR" />
    <result column="PD_UNIT" property="pdUnit" jdbcType="VARCHAR" />
    <result column="PD_REMARK" property="pdRemark" jdbcType="VARCHAR" />
    <result column="PD_HANDLETIME" property="pdHandletime" jdbcType="TIMESTAMP" />
    <result column="PD_USER_ID" property="pdUserId" jdbcType="DECIMAL" />
    <result column="PD_STATUS" property="pdStatus" jdbcType="VARCHAR" />
    <result column="PD_ENAME" property="pdEname" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    PD_SYSCODE, PD_NAME, PD_TYPE, PD_MODE, SAC_ID, BT_CODE, PD_UNIT, PD_REMARK, PD_HANDLETIME, 
    PD_USER_ID, PD_STATUS, PD_ENAME
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from PRICE_DEFINE
    where PD_SYSCODE = #{pdSyscode,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from PRICE_DEFINE
    where PD_SYSCODE = #{pdSyscode,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.PriceDefine" >
    insert into PRICE_DEFINE (PD_SYSCODE, PD_NAME, PD_TYPE, 
      PD_MODE, SAC_ID, BT_CODE, 
      PD_UNIT, PD_REMARK, PD_HANDLETIME, 
      PD_USER_ID, PD_STATUS, PD_ENAME
      )
    values (#{pdSyscode,jdbcType=DECIMAL}, #{pdName,jdbcType=VARCHAR}, #{pdType,jdbcType=VARCHAR}, 
      #{pdMode,jdbcType=VARCHAR}, #{sacId,jdbcType=VARCHAR}, #{btCode,jdbcType=VARCHAR},
      #{pdUnit,jdbcType=VARCHAR}, #{pdRemark,jdbcType=VARCHAR}, #{pdHandletime,jdbcType=TIMESTAMP}, 
      #{pdUserId,jdbcType=DECIMAL}, #{pdStatus,jdbcType=VARCHAR}, #{pdEname,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.PriceDefine" >
    insert into PRICE_DEFINE
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="pdSyscode != null" >
        PD_SYSCODE,
      </if>
      <if test="pdName != null" >
        PD_NAME,
      </if>
      <if test="pdType != null" >
        PD_TYPE,
      </if>
      <if test="pdMode != null" >
        PD_MODE,
      </if>
      <if test="sacId != null" >
        SAC_ID,
      </if>
      <if test="btCode != null" >
        BT_CODE,
      </if>
      <if test="pdUnit != null" >
        PD_UNIT,
      </if>
      <if test="pdRemark != null" >
        PD_REMARK,
      </if>
      <if test="pdHandletime != null" >
        PD_HANDLETIME,
      </if>
      <if test="pdUserId != null" >
        PD_USER_ID,
      </if>
      <if test="pdStatus != null" >
        PD_STATUS,
      </if>
      <if test="pdEname != null" >
        PD_ENAME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="pdSyscode != null" >
        #{pdSyscode,jdbcType=DECIMAL},
      </if>
      <if test="pdName != null" >
        #{pdName,jdbcType=VARCHAR},
      </if>
      <if test="pdType != null" >
        #{pdType,jdbcType=VARCHAR},
      </if>
      <if test="pdMode != null" >
        #{pdMode,jdbcType=VARCHAR},
      </if>
      <if test="sacId != null" >
        #{sacId,jdbcType=VARCHAR},
      </if>
      <if test="btCode != null" >
        #{btCode,jdbcType=VARCHAR},
      </if>
      <if test="pdUnit != null" >
        #{pdUnit,jdbcType=VARCHAR},
      </if>
      <if test="pdRemark != null" >
        #{pdRemark,jdbcType=VARCHAR},
      </if>
      <if test="pdHandletime != null" >
        #{pdHandletime,jdbcType=TIMESTAMP},
      </if>
      <if test="pdUserId != null" >
        #{pdUserId,jdbcType=DECIMAL},
      </if>
      <if test="pdStatus != null" >
        #{pdStatus,jdbcType=VARCHAR},
      </if>
      <if test="pdEname != null" >
        #{pdEname,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.PriceDefine" >
    update PRICE_DEFINE
    <set >
      <if test="pdName != null" >
        PD_NAME = #{pdName,jdbcType=VARCHAR},
      </if>
      <if test="pdType != null" >
        PD_TYPE = #{pdType,jdbcType=VARCHAR},
      </if>
      <if test="pdMode != null" >
        PD_MODE = #{pdMode,jdbcType=VARCHAR},
      </if>
      <if test="sacId != null" >
        SAC_ID = #{sacId,jdbcType=VARCHAR},
      </if>
      <if test="btCode != null" >
        BT_CODE = #{btCode,jdbcType=VARCHAR},
      </if>
      <if test="pdUnit != null" >
        PD_UNIT = #{pdUnit,jdbcType=VARCHAR},
      </if>
      <if test="pdRemark != null" >
        PD_REMARK = #{pdRemark,jdbcType=VARCHAR},
      </if>
      <if test="pdHandletime != null" >
        PD_HANDLETIME = #{pdHandletime,jdbcType=TIMESTAMP},
      </if>
      <if test="pdUserId != null" >
        PD_USER_ID = #{pdUserId,jdbcType=DECIMAL},
      </if>
      <if test="pdStatus != null" >
        PD_STATUS = #{pdStatus,jdbcType=VARCHAR},
      </if>
      <if test="pdEname != null" >
        PD_ENAME = #{pdEname,jdbcType=VARCHAR},
      </if>
    </set>
    where PD_SYSCODE = #{pdSyscode,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.PriceDefine" >
    update PRICE_DEFINE
    set PD_NAME = #{pdName,jdbcType=VARCHAR},
      PD_TYPE = #{pdType,jdbcType=VARCHAR},
      PD_MODE = #{pdMode,jdbcType=VARCHAR},
      SAC_ID = #{sacId,jdbcType=VARCHAR},
      BT_CODE = #{btCode,jdbcType=VARCHAR},
      PD_UNIT = #{pdUnit,jdbcType=VARCHAR},
      PD_REMARK = #{pdRemark,jdbcType=VARCHAR},
      PD_HANDLETIME = #{pdHandletime,jdbcType=TIMESTAMP},
      PD_USER_ID = #{pdUserId,jdbcType=DECIMAL},
      PD_STATUS = #{pdStatus,jdbcType=VARCHAR},
      PD_ENAME = #{pdEname,jdbcType=VARCHAR}
    where PD_SYSCODE = #{pdSyscode,jdbcType=DECIMAL}
  </update>
  <select id="selectPriceIncideList" resultType="com.sinoair.billing.domain.model.billing.PriceDefine">
    select * from PRICE_DEFINE where PD_TYPE='INCIDENT' and PD_STATUS='ON'
  </select>
  <select id="selectPrinceByName" resultType="com.sinoair.billing.domain.model.billing.PriceDefine">
    select * from PRICE_DEFINE where  PD_STATUS='ON' and PD_NAME=#{pdName}
  </select>

  <select id="selectPriceList" resultType="com.sinoair.billing.domain.model.billing.PriceDefine">
    select * from PRICE_DEFINE where PD_TYPE!='INCIDENT' and PD_STATUS='ON' ORDER BY PD_NAME ASC
  </select>

  <select id="getReceiptPdNameList" resultType="com.sinoair.billing.domain.model.billing.PriceDefine">
    select pd.pd_syscode,pd.pd_name  from price_define pd where pd.pd_mode in('BASIC','ADJUST') and pd.pd_status='ON' order by pd.pd_syscode
  </select>
  <select id="selectAllPriceForSelector" resultType="java.util.HashMap">
    select pd.pd_syscode,pd.pd_name  from price_define pd where pd.pd_type in('BASIC','FREIGHT') and pd.pd_status='ON' order by pd.pd_name
  </select>
  <select id="selectAllPriceForSelectorForPayInvoice" resultType="java.util.HashMap">
    select pd.pd_syscode,pd.pd_name,pd.pd_mode  from price_define pd where  pd.pd_status='ON' order by pd.pd_name
  </select>
  <select id="selectPrinceByCondition" parameterType="com.sinoair.billing.domain.model.billing.PriceDefine" resultType="com.sinoair.billing.domain.model.billing.PriceDefine">
    select * from PRICE_DEFINE
    where  PD_STATUS='ON'
    <if test="pdName!=null and pdName !=''">
      and  PD_NAME like '%'|| #{pdName} ||'%'
    </if>
    <if test="pdType!=null and pdType!=''">
      and PD_TYPE=#{pdType}
    </if>
    <if test="pdMode!=null and pdMode!=''">
      and PD_MODE=#{pdMode}
    </if>
    order by pd_name
  </select>
  <select id="countPrinceByName" parameterType="java.lang.String" resultType="int">
    select count(*) from PRICE_DEFINE where  PD_STATUS='ON' and PD_NAME=#{pdName}
  </select>
  <select id="getSeq" resultType="java.lang.String">
    select seq_price_define.nextval from dual
  </select>

</mapper>