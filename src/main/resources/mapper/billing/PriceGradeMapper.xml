<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.billing.dao.billing.PriceGradeMapper">
    <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.PriceGrade">
        <id column="PG_ID" jdbcType="DECIMAL" property="pgId"/>
        <result column="PR_ID" jdbcType="DECIMAL" property="prId"/>
        <result column="PP_ID" jdbcType="DECIMAL" property="ppId"/>
        <result column="PG_TYPE" jdbcType="VARCHAR" property="pgType"/>
        <result column="PG_START" jdbcType="DECIMAL" property="pgStart"/>
        <result column="PG_END" jdbcType="DECIMAL" property="pgEnd"/>
        <result column="PG_FIXPRICE" jdbcType="DECIMAL" property="pgFixprice"/>
        <result column="PG_INNERPRICE" jdbcType="DECIMAL" property="pgInnerprice"/>
        <result column="PG_INNERWEIGHT" jdbcType="DECIMAL" property="pgInnerweight"/>
        <result column="PG_HANDLETIME" jdbcType="TIMESTAMP" property="pgHandletime"/>
        <result column="PG_USER_ID" jdbcType="DECIMAL" property="pgUserId"/>
        <result column="PG_KGPRICE" jdbcType="DECIMAL" property="pgKgPrice"/>
        <result column="KG_TYPE" jdbcType="VARCHAR" property="kgType"/>
        <result column="ZONE_NUM" jdbcType="VARCHAR" property="zoneNum" />
    </resultMap>
    <sql id="Base_Column_List">
    PG_ID, PR_ID, PP_ID, PG_TYPE, PG_START, PG_END, PG_FIXPRICE, PG_INNERPRICE, PG_INNERWEIGHT, 
    PG_HANDLETIME, PG_USER_ID,PG_KGPRICE,KG_TYPE, ZONE_NUM
  </sql>
    <sql id='TABLE_SEQUENCE'>SEQ_PRICE_GRADE.NEXTVAL</sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from PRICE_GRADE
        where PG_ID = #{pgId,jdbcType=DECIMAL}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from PRICE_GRADE
    where PG_ID = #{pgId,jdbcType=DECIMAL}
  </delete>
    <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.PriceGrade">
        <selectKey keyProperty="pgId" resultType="java.lang.Integer" order="BEFORE">
            select
            <include refid="TABLE_SEQUENCE"/>
            from dual
        </selectKey>
        insert into PRICE_GRADE (PG_ID, PR_ID, PP_ID,
        PG_TYPE, PG_START, PG_END,
        PG_FIXPRICE, PG_INNERPRICE, PG_INNERWEIGHT,
        PG_HANDLETIME, PG_USER_ID,PG_KGPRICE,KG_TYPE, ZONE_NUM)
        values (#{pgId,jdbcType=DECIMAL}, #{prId,jdbcType=DECIMAL}, #{ppId,jdbcType=DECIMAL},
        #{pgType,jdbcType=VARCHAR}, #{pgStart,jdbcType=DECIMAL}, #{pgEnd,jdbcType=DECIMAL},
        #{pgFixprice,jdbcType=DECIMAL}, #{pgInnerprice,jdbcType=DECIMAL}, #{pgInnerweight,jdbcType=DECIMAL},
        #{pgHandletime,jdbcType=TIMESTAMP}, #{pgUserId,jdbcType=DECIMAL}, #{pgKgPrice,jdbcType=DECIMAL},
        #{kgType,jdbcType=VARCHAR}, #{zoneNum,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.PriceGrade">
        <selectKey keyProperty="pgId" resultType="java.lang.Integer" order="BEFORE">
            select
            <include refid="TABLE_SEQUENCE"/>
            from dual
        </selectKey>
        insert into PRICE_GRADE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="pgId != null">
                PG_ID,
            </if>
            <if test="prId != null">
                PR_ID,
            </if>
            <if test="ppId != null">
                PP_ID,
            </if>
            <if test="pgType != null">
                PG_TYPE,
            </if>
            <if test="pgStart != null">
                PG_START,
            </if>
            <if test="pgEnd != null">
                PG_END,
            </if>
            <if test="pgFixprice != null">
                PG_FIXPRICE,
            </if>
            <if test="pgInnerprice != null">
                PG_INNERPRICE,
            </if>
            <if test="pgInnerweight != null">
                PG_INNERWEIGHT,
            </if>
            <if test="pgHandletime != null">
                PG_HANDLETIME,
            </if>
            <if test="pgUserId != null">
                PG_USER_ID,
            </if>
            <if test="pgKgPrice != null">
                PG_KGPRICE,
            </if>
            <if test="kgType != null">
                KG_TYPE,
            </if>
            <if test="zoneNum != null">
                ZONE_NUM,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="pgId != null">
                #{pgId,jdbcType=DECIMAL},
            </if>
            <if test="prId != null">
                #{prId,jdbcType=DECIMAL},
            </if>
            <if test="ppId != null">
                #{ppId,jdbcType=DECIMAL},
            </if>
            <if test="pgType != null">
                #{pgType,jdbcType=VARCHAR},
            </if>
            <if test="pgStart != null">
                #{pgStart,jdbcType=DECIMAL},
            </if>
            <if test="pgEnd != null">
                #{pgEnd,jdbcType=DECIMAL},
            </if>
            <if test="pgFixprice != null">
                #{pgFixprice,jdbcType=DECIMAL},
            </if>
            <if test="pgInnerprice != null">
                #{pgInnerprice,jdbcType=DECIMAL},
            </if>
            <if test="pgInnerweight != null">
                #{pgInnerweight,jdbcType=DECIMAL},
            </if>
            <if test="pgHandletime != null">
                #{pgHandletime,jdbcType=TIMESTAMP},
            </if>
            <if test="pgUserId != null">
                #{pgUserId,jdbcType=DECIMAL},
            </if>
            <if test="pgKgPrice != null">
                #{pgKgPrice,jdbcType=DECIMAL},
            </if>
            <if test="kgType != null">
                #{kgType,jdbcType=VARCHAR},
            </if>
            <if test="zoneNum != null">
                #{zoneNum,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.PriceGrade">
        update PRICE_GRADE
        <set>
            <if test="prId != null">
                PR_ID = #{prId,jdbcType=DECIMAL},
            </if>
            <if test="ppId != null">
                PP_ID = #{ppId,jdbcType=DECIMAL},
            </if>
            <if test="pgType != null">
                PG_TYPE = #{pgType,jdbcType=VARCHAR},
            </if>
            <if test="pgStart != null">
                PG_START = #{pgStart,jdbcType=DECIMAL},
            </if>
            <if test="pgEnd != null">
                PG_END = #{pgEnd,jdbcType=DECIMAL},
            </if>
            <if test="pgFixprice != null">
                PG_FIXPRICE = #{pgFixprice,jdbcType=DECIMAL},
            </if>
            <if test="pgInnerprice != null">
                PG_INNERPRICE = #{pgInnerprice,jdbcType=DECIMAL},
            </if>
            <if test="pgInnerweight != null">
                PG_INNERWEIGHT = #{pgInnerweight,jdbcType=DECIMAL},
            </if>
            <if test="pgHandletime != null">
                PG_HANDLETIME = #{pgHandletime,jdbcType=TIMESTAMP},
            </if>
            <if test="pgUserId != null">
                PG_USER_ID = #{pgUserId,jdbcType=DECIMAL},
            </if>
            <if test="pgKgprice != null">
                PG_KGPRICE = #{pgKgprice,jdbcType=DECIMAL},
            </if>
            <if test="kgType != null">
                KG_TYPE = #{kgType,jdbcType=VARCHAR},
            </if>
            <if test="zoneNum != null">
                ZONE_NUM = #{zoneNum,jdbcType=VARCHAR},
            </if>
        </set>
        where PG_ID = #{pgId,jdbcType=DECIMAL}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.PriceGrade">
    update PRICE_GRADE
    set PR_ID = #{prId,jdbcType=DECIMAL},
      PP_ID = #{ppId,jdbcType=DECIMAL},
      PG_TYPE = #{pgType,jdbcType=VARCHAR},
      PG_START = #{pgStart,jdbcType=DECIMAL},
      PG_END = #{pgEnd,jdbcType=DECIMAL},
      PG_FIXPRICE = #{pgFixprice,jdbcType=DECIMAL},
      PG_INNERPRICE = #{pgInnerprice,jdbcType=DECIMAL},
      PG_INNERWEIGHT = #{pgInnerweight,jdbcType=DECIMAL},
      PG_HANDLETIME = #{pgHandletime,jdbcType=TIMESTAMP},
      PG_USER_ID = #{pgUserId,jdbcType=DECIMAL},
      PG_KGPRICE = #{pgKgprice,jdbcType=DECIMAL},
      KG_TYPE = #{kgType,jdbcType=VARCHAR},
      ZONE_NUM = #{zoneNum,jdbcType=VARCHAR}
    where PG_ID = #{pgId,jdbcType=DECIMAL}
  </update>
    <select id="selectGradeByprid" resultMap="BaseResultMap">
        SELECT * from PRICE_GRADE where 1=1
        <if test="prId!=null and prId!=''">
            and PR_ID=#{prId}
        </if>
        <if test="ppId!=null and ppId!=''">
            and PP_ID=#{ppId}
        </if>
        order by PG_HANDLETIME DESC
    </select>
    <select id="selectGradeBypriceId" resultMap="BaseResultMap">
        SELECT * from PRICE_GRADE where 1=1
        <if test="prId!=null and prId!=''">
            and PR_ID=#{prId}
        </if>
        <if test="ppId!=null and ppId!=''">
            and PP_ID=#{ppId}
        </if>
        order by PG_HANDLETIME DESC
    </select>

    <delete id="deleteGradeByPrId" parameterType="java.lang.Integer">
        delete from PRICE_GRADE
        where PR_ID = #{prId,jdbcType=DECIMAL}
    </delete>

    <select id="selectGradeZoneByprid" resultType="java.util.Map">
        SELECT T.pg_end as ZTMP_0,
            SUM(DECODE(T.Zone_Num, '2', T.Pg_Fixprice)) as ZTMP_2 ,
            SUM(DECODE(T.Zone_Num, '3', T.Pg_Fixprice)) as ZTMP_3 ,
            SUM(DECODE(T.Zone_Num, '4', T.Pg_Fixprice)) as ZTMP_4,
            SUM(DECODE(T.Zone_Num, '5', T.Pg_Fixprice)) as ZTMP_5 ,
            SUM(DECODE(T.Zone_Num, '6', T.Pg_Fixprice)) as ZTMP_6 ,
            SUM(DECODE(T.Zone_Num, '7', T.Pg_Fixprice)) as ZTMP_7 ,
            SUM(DECODE(T.Zone_Num, '8', T.Pg_Fixprice)) as ZTMP_8
        FROM price_grade T
        where t.PR_ID = #{prId,jdbcType=DECIMAL}
        GROUP BY t.pg_end
        ORDER BY t.pg_end
    </select>

    <select id="selectZoneNumBypriceId" resultType="java.lang.String">
        SELECT distinct zone_num from PRICE_GRADE where 1=1
        <if test="prId!=null and prId!=''">
            and PR_ID=#{prId}
        </if>
        <if test="ppId!=null and ppId!=''">
            and PP_ID=#{ppId}
        </if>
        order by to_number(Zone_Num)
    </select>

    <select id="selectGradeBypriceIdAndZone" resultMap="BaseResultMap">
        SELECT * from PRICE_GRADE where 1=1
        <if test="prId!=null and prId!=''">
            and PR_ID=#{prId}
        </if>
        <if test="ppId!=null and ppId!=''">
            and PP_ID=#{ppId}
        </if>
        and Zone_Num = #{zoneNum}
        order by pg_end
    </select>

    <select id="selectPriceGradeByPrId" resultType="com.sinoair.billing.domain.vo.price.PriceGradeVO">
        SELECT * from PRICE_GRADE where 1=1
        and PR_ID=#{prId}
        order by PG_HANDLETIME DESC
    </select>
</mapper>