<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.billing.dao.billing.PricePaymentMapper">
    <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.PricePayment">
        <id column="PP_ID" jdbcType="DECIMAL" property="ppId"/>
        <result column="S_ID" jdbcType="DECIMAL" property="sId"/>
        <result column="PP_NAME" jdbcType="VARCHAR" property="ppName"/>
        <result column="PP_TYPE" jdbcType="VARCHAR" property="ppType"/>
        <result column="PP_PRICE" jdbcType="DECIMAL" property="ppPrice"/>
        <result column="PP_MINPRICE" jdbcType="DECIMAL" property="ppMinprice"/>
        <result column="PP_FIRSTWEIGHT" jdbcType="DECIMAL" property="ppFirstweight"/>
        <result column="PP_FIRSTPRICE" jdbcType="DECIMAL" property="ppFirstprice"/>
        <result column="PP_ADDITIONALWEIGHT" jdbcType="DECIMAL" property="ppAdditionalweight"/>
        <result column="PP_ADDITIONALPRICE" jdbcType="DECIMAL" property="ppAdditionalprice"/>
        <result column="CT_CODE" jdbcType="DECIMAL" property="ctCode"/>
        <result column="PP_EFFECTIVEDATE" jdbcType="TIMESTAMP" property="ppEffectivedate"/>
        <result column="PP_EXPIREDDATE" jdbcType="TIMESTAMP" property="ppExpireddate"/>
        <result column="PP_HANDLETIME" jdbcType="TIMESTAMP" property="ppHandletime"/>
        <result column="PP_USER_ID" jdbcType="DECIMAL" property="ppUserId"/>
        <result column="PP_STATUS" jdbcType="VARCHAR" property="ppStatus"/>
        <result column="PP_SPECIAL_KEY" jdbcType="VARCHAR" property="ppSpecialKey"/>
        <result column="PP_BASEPRICE" jdbcType="DECIMAL" property="ppBaseprice"/>
        <result column="PP_AUTO" jdbcType="VARCHAR" property="ppAuto"/>
        <result column="PD_SYSCODE" jdbcType="DECIMAL" property="pdSyscode"/>
        <result column="PP_DEST" jdbcType="VARCHAR" property="ppDest"/>
        <result column="PP_AWB_TYPE" jdbcType="VARCHAR" property="ppAwbType"/>
        <result column="COMPANY_ID" jdbcType="VARCHAR" property="companyId"/>
        <result column="WEIGHT_UNIT" jdbcType="VARCHAR" property="weightUnit" />
    </resultMap>
    <sql id="Base_Column_List">
    PP_ID, S_ID, PP_NAME, PP_TYPE, PP_PRICE, PP_MINPRICE, PP_FIRSTWEIGHT, PP_FIRSTPRICE,
    PP_ADDITIONALWEIGHT, PP_ADDITIONALPRICE, CT_CODE, PP_EFFECTIVEDATE, PP_EXPIREDDATE,
    PP_HANDLETIME, PP_USER_ID, PP_STATUS, PP_SPECIAL_KEY, PP_BASEPRICE, PP_AUTO, PD_SYSCODE,
    PP_DEST, PP_AWB_TYPE, COMPANY_ID, WEIGHT_UNIT
  </sql>
    <sql id='TABLE_SEQUENCE'>SEQ_PRICE_PAYMENT.NEXTVAL</sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from PRICE_PAYMENT
        where PP_ID = #{ppId,jdbcType=DECIMAL}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from PRICE_PAYMENT
        where PP_ID = #{ppId,jdbcType=DECIMAL}
    </delete>
    <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.PricePayment">
        <selectKey keyProperty="ppId" resultType="java.lang.Integer" order="BEFORE">
            select
            <include refid="TABLE_SEQUENCE"/>
            from dual
        </selectKey>
        insert into PRICE_PAYMENT (PP_ID, S_ID, PP_NAME,
        PP_TYPE, PP_PRICE, PP_MINPRICE,
        PP_FIRSTWEIGHT, PP_FIRSTPRICE, PP_ADDITIONALWEIGHT,
        PP_ADDITIONALPRICE, CT_CODE, PP_EFFECTIVEDATE,
        PP_EXPIREDDATE, PP_HANDLETIME, PP_USER_ID,
        PP_STATUS, PP_SPECIAL_KEY, PP_BASEPRICE,
        PP_AUTO, PD_SYSCODE, PP_DEST,
        PP_AWB_TYPE, COMPANY_ID, WEIGHT_UNIT)
        values (#{ppId,jdbcType=DECIMAL}, #{sId,jdbcType=DECIMAL}, #{ppName,jdbcType=VARCHAR},
        #{ppType,jdbcType=VARCHAR}, #{ppPrice,jdbcType=DECIMAL}, #{ppMinprice,jdbcType=DECIMAL},
        #{ppFirstweight,jdbcType=DECIMAL}, #{ppFirstprice,jdbcType=DECIMAL}, #{ppAdditionalweight,jdbcType=DECIMAL},
        #{ppAdditionalprice,jdbcType=DECIMAL}, #{ctCode,jdbcType=DECIMAL}, #{ppEffectivedate,jdbcType=TIMESTAMP},
        #{ppExpireddate,jdbcType=TIMESTAMP}, #{ppHandletime,jdbcType=TIMESTAMP}, #{ppUserId,jdbcType=DECIMAL},
        #{ppStatus,jdbcType=VARCHAR}, #{ppSpecialKey,jdbcType=VARCHAR}, #{ppBaseprice,jdbcType=DECIMAL},
        #{ppAuto,jdbcType=VARCHAR}, #{pdSyscode,jdbcType=DECIMAL}, #{ppDest,jdbcType=VARCHAR},
        #{ppAwbType,jdbcType=VARCHAR}, #{companyId,jdbcType=VARCHAR}, #{weightUnit,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.PricePayment">
        <selectKey keyProperty="ppId" resultType="java.lang.Integer" order="BEFORE">
            select
            <include refid="TABLE_SEQUENCE"/>
            from dual
        </selectKey>
        insert into PRICE_PAYMENT
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ppId != null">
                PP_ID,
            </if>
            <if test="sId != null">
                S_ID,
            </if>
            <if test="ppName != null">
                PP_NAME,
            </if>
            <if test="ppType != null and ppType != ''">
                PP_TYPE,
            </if>
            <if test="ppPrice != null">
                PP_PRICE,
            </if>
            <if test="ppMinprice != null">
                PP_MINPRICE,
            </if>
            <if test="ppFirstweight != null">
                PP_FIRSTWEIGHT,
            </if>
            <if test="ppFirstprice != null">
                PP_FIRSTPRICE,
            </if>
            <if test="ppAdditionalweight != null">
                PP_ADDITIONALWEIGHT,
            </if>
            <if test="ppAdditionalprice != null">
                PP_ADDITIONALPRICE,
            </if>
            <if test="ctCode != null">
                CT_CODE,
            </if>
            <if test="ppEffectivedate != null">
                PP_EFFECTIVEDATE,
            </if>
            <if test="ppExpireddate != null">
                PP_EXPIREDDATE,
            </if>
            <if test="ppHandletime != null">
                PP_HANDLETIME,
            </if>
            <if test="ppUserId != null">
                PP_USER_ID,
            </if>
            <if test="ppStatus != null">
                PP_STATUS,
            </if>
            <if test="ppSpecialKey != null">
                PP_SPECIAL_KEY,
            </if>
            <if test="ppBaseprice != null">
                PP_BASEPRICE,
            </if>
            <if test="ppAuto != null">
                PP_AUTO,
            </if>
            <if test="pdSyscode != null">
                PD_SYSCODE,
            </if>
            <if test="ppDest != null and ppDest != ''">
                PP_DEST,
            </if>
            <if test="ppAwbType != null">
                PP_AWB_TYPE,
            </if>
            <if test="companyId != null">
                COMPANY_ID,
            </if>
            <if test="weightUnit != null">
                WEIGHT_UNIT,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ppId != null">
                #{ppId,jdbcType=DECIMAL},
            </if>
            <if test="sId != null">
                #{sId,jdbcType=DECIMAL},
            </if>
            <if test="ppName != null">
                #{ppName,jdbcType=VARCHAR},
            </if>
            <if test="ppType != null and ppType != ''">
                #{ppType,jdbcType=VARCHAR},
            </if>
            <if test="ppPrice != null">
                #{ppPrice,jdbcType=DECIMAL},
            </if>
            <if test="ppMinprice != null">
                #{ppMinprice,jdbcType=DECIMAL},
            </if>
            <if test="ppFirstweight != null">
                #{ppFirstweight,jdbcType=DECIMAL},
            </if>
            <if test="ppFirstprice != null">
                #{ppFirstprice,jdbcType=DECIMAL},
            </if>
            <if test="ppAdditionalweight != null">
                #{ppAdditionalweight,jdbcType=DECIMAL},
            </if>
            <if test="ppAdditionalprice != null">
                #{ppAdditionalprice,jdbcType=DECIMAL},
            </if>
            <if test="ctCode != null">
                #{ctCode,jdbcType=DECIMAL},
            </if>
            <if test="ppEffectivedate != null">
                #{ppEffectivedate,jdbcType=TIMESTAMP},
            </if>
            <if test="ppExpireddate != null">
                #{ppExpireddate,jdbcType=TIMESTAMP},
            </if>
            <if test="ppHandletime != null">
                #{ppHandletime,jdbcType=TIMESTAMP},
            </if>
            <if test="ppUserId != null">
                #{ppUserId,jdbcType=DECIMAL},
            </if>
            <if test="ppStatus != null">
                #{ppStatus,jdbcType=VARCHAR},
            </if>
            <if test="ppSpecialKey != null">
                #{ppSpecialKey,jdbcType=VARCHAR},
            </if>
            <if test="ppBaseprice != null">
                #{ppBaseprice,jdbcType=DECIMAL},
            </if>
            <if test="ppAuto != null">
                #{ppAuto,jdbcType=VARCHAR},
            </if>
            <if test="pdSyscode != null">
                #{pdSyscode,jdbcType=DECIMAL},
            </if>
            <if test="ppDest != null and ppDest != ''">
                #{ppDest,jdbcType=VARCHAR},
            </if>
            <if test="ppAwbType != null">
                #{ppAwbType,jdbcType=VARCHAR},
            </if>
            <if test="companyId != null">
                #{companyId,jdbcType=VARCHAR},
            </if>
            <if test="weightUnit != null">
                #{weightUnit,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.PricePayment">
        update PRICE_PAYMENT
        <set>
            <if test="sId != null">
                S_ID = #{sId,jdbcType=DECIMAL},
            </if>
            <if test="ppName != null">
                PP_NAME = #{ppName,jdbcType=VARCHAR},
            </if>
            <if test="ppType != null">
                PP_TYPE = #{ppType,jdbcType=VARCHAR},
            </if>
            <if test="ppPrice != null">
                PP_PRICE = #{ppPrice,jdbcType=DECIMAL},
            </if>
            <if test="ppMinprice != null">
                PP_MINPRICE = #{ppMinprice,jdbcType=DECIMAL},
            </if>
            <if test="ppFirstweight != null">
                PP_FIRSTWEIGHT = #{ppFirstweight,jdbcType=DECIMAL},
            </if>
            <if test="ppFirstprice != null">
                PP_FIRSTPRICE = #{ppFirstprice,jdbcType=DECIMAL},
            </if>
            <if test="ppAdditionalweight != null">
                PP_ADDITIONALWEIGHT = #{ppAdditionalweight,jdbcType=DECIMAL},
            </if>
            <if test="ppAdditionalprice != null">
                PP_ADDITIONALPRICE = #{ppAdditionalprice,jdbcType=DECIMAL},
            </if>
            <if test="ctCode != null">
                CT_CODE = #{ctCode,jdbcType=DECIMAL},
            </if>
            <if test="ppEffectivedate != null">
                PP_EFFECTIVEDATE = #{ppEffectivedate,jdbcType=TIMESTAMP},
            </if>
            <if test="ppExpireddate != null">
                PP_EXPIREDDATE = #{ppExpireddate,jdbcType=TIMESTAMP},
            </if>
            <if test="ppHandletime != null">
                PP_HANDLETIME = #{ppHandletime,jdbcType=TIMESTAMP},
            </if>
            <if test="ppUserId != null">
                PP_USER_ID = #{ppUserId,jdbcType=DECIMAL},
            </if>
            <if test="ppStatus != null">
                PP_STATUS = #{ppStatus,jdbcType=VARCHAR},
            </if>
            <if test="ppSpecialKey != null">
                PP_SPECIAL_KEY = #{ppSpecialKey,jdbcType=VARCHAR},
            </if>
            <if test="ppBaseprice != null">
                PP_BASEPRICE = #{ppBaseprice,jdbcType=DECIMAL},
            </if>
            <if test="ppAuto != null">
                PP_AUTO = #{ppAuto,jdbcType=VARCHAR},
            </if>
            <if test="pdSyscode != null">
                PD_SYSCODE = #{pdSyscode,jdbcType=DECIMAL},
            </if>
            <if test="ppDest != null">
                PP_DEST = #{ppDest,jdbcType=VARCHAR},
            </if>
            <if test="ppAwbType != null">
                PP_AWB_TYPE = #{ppAwbType,jdbcType=VARCHAR},
            </if>
            <if test="companyId != null">
                COMPANY_ID = #{companyId,jdbcType=VARCHAR},
            </if>
            <if test="weightUnit != null">
                WEIGHT_UNIT = #{weightUnit,jdbcType=VARCHAR},
            </if>
        </set>
        where PP_ID = #{ppId,jdbcType=DECIMAL}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.PricePayment">
    update PRICE_PAYMENT
    set S_ID = #{sId,jdbcType=DECIMAL},
      PP_NAME = #{ppName,jdbcType=VARCHAR},
      PP_TYPE = #{ppType,jdbcType=VARCHAR},
      PP_PRICE = #{ppPrice,jdbcType=DECIMAL},
      PP_MINPRICE = #{ppMinprice,jdbcType=DECIMAL},
      PP_FIRSTWEIGHT = #{ppFirstweight,jdbcType=DECIMAL},
      PP_FIRSTPRICE = #{ppFirstprice,jdbcType=DECIMAL},
      PP_ADDITIONALWEIGHT = #{ppAdditionalweight,jdbcType=DECIMAL},
      PP_ADDITIONALPRICE = #{ppAdditionalprice,jdbcType=DECIMAL},
      CT_CODE = #{ctCode,jdbcType=DECIMAL},
      PP_EFFECTIVEDATE = #{ppEffectivedate,jdbcType=TIMESTAMP},
      PP_EXPIREDDATE = #{ppExpireddate,jdbcType=TIMESTAMP},
      PP_HANDLETIME = #{ppHandletime,jdbcType=TIMESTAMP},
      PP_USER_ID = #{ppUserId,jdbcType=DECIMAL},
      PP_STATUS = #{ppStatus,jdbcType=VARCHAR},
      PP_SPECIAL_KEY = #{ppSpecialKey,jdbcType=VARCHAR},
      PP_BASEPRICE = #{ppBaseprice,jdbcType=DECIMAL},
      PP_AUTO = #{ppAuto,jdbcType=VARCHAR},
      PD_SYSCODE = #{pdSyscode,jdbcType=DECIMAL},
      PP_DEST = #{ppDest,jdbcType=VARCHAR},
      PP_AWB_TYPE = #{ppAwbType,jdbcType=VARCHAR},
      COMPANY_ID = #{companyId,jdbcType=VARCHAR},
      WEIGHT_UNIT = #{weightUnit,jdbcType=VARCHAR}
    where PP_ID = #{ppId,jdbcType=DECIMAL}
  </update>

    <update id="updateOFFBySID" parameterType="java.lang.Integer">
        update PRICE_PAYMENT
        set PP_STATUS = 'OFF'
        where S_ID = #{sId,jdbcType=DECIMAL}
    </update>
    <select id="selectAllSupplier" resultType="java.util.HashMap">
        select * from SUPPLIER where 1=1
    </select>
    <select id="selectAllService" resultType="java.util.HashMap">
        select * from SERVICE where 1=1
    </select>
    <select id="selectPriceForSearch" resultType="java.util.HashMap">
        select DISTINCT t1.*,
        t3.CT_NAME as CT_NAME,
        t4.S_NAME AS S_NAME,
        T5.SP_NAME AS SP_NAME,
        (select count(0) from price_grade where pp_id = t1.pp_id) as GRADE_NUM
        from PRICE_PAYMENT t1
        left join CURRENCYTYPE t3 on t1.CT_CODE=t3.CT_CODE,
        SERVICE t4,
        SUPPLIER t5
        where 1=1
        AND T1.S_ID = T4.S_ID
        AND T4.SO_CODE = T5.SP_CODE
        <if test="fservice!=null and fservice!=''">
            and t1.S_ID=#{fservice}
        </if>
        and (t1.PP_STATUS='ON' or t1.pp_status='DEFULT')
        order by t1.PP_HANDLETIME DESC
    </select>
    <select id="selectServiceByPk" resultMap="BaseResultMap">
        select * from SERVICE where S_ID=#{fservice}
    </select>

    <select id="supplierQuery" parameterType="java.lang.String" resultType="java.util.Map">
        select sp_code as "id", sp_name as "text", sp_name as "full_name"
        from SUPPLIER where 1=1 and SP_STATUS = 'ON'
        <if test="q != null">
            AND (UPPER(sp_name) like '%'||UPPER(#{q})||'%' or UPPER(sp_code) like '%'||UPPER(#{q})||'%')
        </if>
        <if test="companyId != null">
            AND (UPPER(COMPANY_ID)=UPPER(#{companyId}) OR (UPPER(SP_FAX)='Y' AND sp_type='外运发展'))
        </if>
    </select>
    <select id="routeQuery" parameterType="java.lang.String" resultType="java.util.Map">
        select EP_KEY as "id", EP_VALUE as "text", EP_VALUE as "full_name"
        from EXPRESS_PROPERTY where 1=1
        <if test="q != null">
            AND (UPPER(EP_KEY) like '%'||UPPER(#{q})||'%' or UPPER(EP_VALUE) like '%'||UPPER(#{q})||'%')
        </if>
    </select>
    <select id="selectServiceBySupplier" parameterType="java.lang.String" resultType="java.util.Map">
        select *
        from service where 1=1
        <if test="curSupplier != null">
            and so_code=#{curSupplier}
        </if>
    </select>
    <select id="selectPriceByExample" resultMap="BaseResultMap">
        select *
        from PRICE_PAYMENT
        where 1=1
        and PP_STATUS ='ON'
        <if test="ppName!=null and ppName!=''">
            and PP_NAME=#{ppName}
        </if>
        <if test="sId!=null and sId!=''">
            and S_ID=#{sId}
        </if>
        <if test="ppDest!=null and ppDest!=''">
            and PP_DEST=#{ppDest}
        </if>
        <if test="ppSpecialKey!=null and ppSpecialKey!=''">
            and PP_SPECIAL_KEY=#{ppSpecialKey}
        </if>

    </select>
    <select id="selectPriceBySid" resultType="com.sinoair.billing.domain.model.billing.PricePayment">
        select * from PRICE_PAYMENT where S_ID=#{sId} and PP_EFFECTIVEDATE &lt;= sysdate and sysdate &lt;=
        PP_EXPIREDDATE
    </select>
    <select id="selectAllPriceForSelector" resultType="java.util.HashMap">
        select t.PP_NAME,t.PP_ID from PRICE_PAYMENT t where t.COMPANY_ID = #{companyId}

    </select>
</mapper>