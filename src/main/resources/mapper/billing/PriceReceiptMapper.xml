<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.PriceReceiptMapper">
    <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.PriceReceipt" >
        <id column="PR_ID" property="prId" jdbcType="DECIMAL" />
        <result column="PC_ID" property="pcId" jdbcType="DECIMAL" />
        <result column="PR_NAME" property="prName" jdbcType="VARCHAR" />
        <result column="EAD_CODE" property="eadCode" jdbcType="VARCHAR" />
        <result column="EAST_CODE" property="eastCode" jdbcType="VARCHAR" />
        <result column="PR_TYPE" property="prType" jdbcType="VARCHAR" />
        <result column="PR_PRICE" property="prPrice" jdbcType="DECIMAL" />
        <result column="PR_MINPRICE" property="prMinprice" jdbcType="DECIMAL" />
        <result column="PR_FIRSTWEIGHT" property="prFirstweight" jdbcType="DECIMAL" />
        <result column="PR_FIRSTPRICE" property="prFirstprice" jdbcType="DECIMAL" />
        <result column="PR_ADDITIONALWEIGHT" property="prAdditionalweight" jdbcType="DECIMAL" />
        <result column="PR_ADDITIONALPRICE" property="prAdditionalprice" jdbcType="DECIMAL" />
        <result column="CT_CODE" property="ctCode" jdbcType="DECIMAL" />
        <result column="PR_EFFECTIVEDATE" property="prEffectivedate" jdbcType="TIMESTAMP" />
        <result column="PR_EXPIREDDATE" property="prExpireddate" jdbcType="TIMESTAMP" />
        <result column="PR_HANDLETIME" property="prHandletime" jdbcType="TIMESTAMP" />
        <result column="PR_USER_ID" property="prUserId" jdbcType="DECIMAL" />
        <result column="PR_STATUS" property="prStatus" jdbcType="VARCHAR" />
        <result column="PR_BASEPRICE" property="prBaseprice" jdbcType="DECIMAL" />
        <result column="PR_SPECIAL_KEY" property="prSpecialKey" jdbcType="VARCHAR" />
        <result column="PD_SYSCODE" property="pdSyscode" jdbcType="DECIMAL" />
        <result column="PR_AWB_TYPE" property="prAwbType" jdbcType="VARCHAR" />
        <result column="PR_AUTO" property="prAuto" jdbcType="VARCHAR" />
        <result column="PR_DEST" property="prDest" jdbcType="VARCHAR" />
        <result column="COMPANY_ID" property="companyId" jdbcType="VARCHAR" />
        <result column="WEIGHT_UNIT" jdbcType="VARCHAR" property="weightUnit" />
    </resultMap>
    <sql id="Base_Column_List" >
    PR_ID, PC_ID, PR_NAME, EAD_CODE, EAST_CODE, PR_TYPE, PR_PRICE, PR_MINPRICE, PR_FIRSTWEIGHT,
    PR_FIRSTPRICE, PR_ADDITIONALWEIGHT, PR_ADDITIONALPRICE, CT_CODE, PR_EFFECTIVEDATE,
    PR_EXPIREDDATE, PR_HANDLETIME, PR_USER_ID, PR_STATUS, PR_BASEPRICE, PR_SPECIAL_KEY,
    PD_SYSCODE, PR_AWB_TYPE, PR_AUTO, PR_DEST, COMPANY_ID, WEIGHT_UNIT
  </sql>
    <sql id='TABLE_SEQUENCE'>SEQ_PRICE_RECEIPT.NEXTVAL</sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select
        <include refid="Base_Column_List"/>
        from PRICE_RECEIPT
        where PR_ID = #{prId,jdbcType=DECIMAL}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from PRICE_RECEIPT
    where PR_ID = #{prId,jdbcType=DECIMAL}
  </delete>
    <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.PriceReceipt">
        <selectKey keyProperty="prId" resultType="java.lang.Integer" order="BEFORE">
            select
            <include refid="TABLE_SEQUENCE"/>
            from dual
        </selectKey>
        insert into PRICE_RECEIPT (PR_ID, PC_ID, PR_NAME,
        EAD_CODE, EAST_CODE, PR_TYPE,
        PR_PRICE, PR_MINPRICE, PR_FIRSTWEIGHT,
        PR_FIRSTPRICE, PR_ADDITIONALWEIGHT, PR_ADDITIONALPRICE,
        CT_CODE, PR_EFFECTIVEDATE, PR_EXPIREDDATE,
        PR_HANDLETIME, PR_USER_ID, PR_STATUS,
        PR_BASEPRICE, PR_SPECIAL_KEY, PD_SYSCODE,
        PR_AWB_TYPE, PR_AUTO, PR_DEST,
        COMPANY_ID, WEIGHT_UNIT)
        values (#{prId,jdbcType=DECIMAL}, #{pcId,jdbcType=DECIMAL}, #{prName,jdbcType=VARCHAR},
        #{eadCode,jdbcType=VARCHAR}, #{eastCode,jdbcType=VARCHAR}, #{prType,jdbcType=VARCHAR},
        #{prPrice,jdbcType=DECIMAL}, #{prMinprice,jdbcType=DECIMAL}, #{prFirstweight,jdbcType=DECIMAL},
        #{prFirstprice,jdbcType=DECIMAL}, #{prAdditionalweight,jdbcType=DECIMAL}, #{prAdditionalprice,jdbcType=DECIMAL},
        #{ctCode,jdbcType=DECIMAL}, #{prEffectivedate,jdbcType=TIMESTAMP}, #{prExpireddate,jdbcType=TIMESTAMP},
        #{prHandletime,jdbcType=TIMESTAMP}, #{prUserId,jdbcType=DECIMAL}, #{prStatus,jdbcType=VARCHAR},
        #{prBaseprice,jdbcType=DECIMAL}, #{prSpecialKey,jdbcType=VARCHAR}, #{pdSyscode,jdbcType=DECIMAL},
        #{prAwbType,jdbcType=VARCHAR}, #{prAuto,jdbcType=VARCHAR}, #{prDest,jdbcType=VARCHAR},
        #{companyId,jdbcType=VARCHAR}, #{weightUnit,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.PriceReceipt">
        <selectKey keyProperty="prId" resultType="java.lang.Integer" order="BEFORE">
            select
            <include refid="TABLE_SEQUENCE"/>
            from dual
        </selectKey>
        insert into PRICE_RECEIPT
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="prId != null" >
                PR_ID,
            </if>
            <if test="pcId != null" >
                PC_ID,
            </if>
            <if test="prName != null" >
                PR_NAME,
            </if>
            <if test="eadCode != null" >
                EAD_CODE,
            </if>
            <if test="eastCode != null" >
                EAST_CODE,
            </if>
            <if test="prType != null" >
                PR_TYPE,
            </if>
            <if test="prPrice != null" >
                PR_PRICE,
            </if>
            <if test="prMinprice != null" >
                PR_MINPRICE,
            </if>
            <if test="prFirstweight != null" >
                PR_FIRSTWEIGHT,
            </if>
            <if test="prFirstprice != null" >
                PR_FIRSTPRICE,
            </if>
            <if test="prAdditionalweight != null" >
                PR_ADDITIONALWEIGHT,
            </if>
            <if test="prAdditionalprice != null" >
                PR_ADDITIONALPRICE,
            </if>
            <if test="ctCode != null" >
                CT_CODE,
            </if>
            <if test="prEffectivedate != null" >
                PR_EFFECTIVEDATE,
            </if>
            <if test="prExpireddate != null" >
                PR_EXPIREDDATE,
            </if>
            <if test="prHandletime != null" >
                PR_HANDLETIME,
            </if>
            <if test="prUserId != null" >
                PR_USER_ID,
            </if>
            <if test="prStatus != null" >
                PR_STATUS,
            </if>
            <if test="prBaseprice != null" >
                PR_BASEPRICE,
            </if>
            <if test="prSpecialKey != null" >
                PR_SPECIAL_KEY,
            </if>
            <if test="pdSyscode != null" >
                PD_SYSCODE,
            </if>
            <if test="prAwbType != null" >
                PR_AWB_TYPE,
            </if>
            <if test="prAuto != null" >
                PR_AUTO,
            </if>
            <if test="prDest != null" >
                PR_DEST,
            </if>
            <if test="companyId != null" >
                COMPANY_ID,
            </if>
            <if test="weightUnit != null">
                WEIGHT_UNIT,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="prId != null" >
                #{prId,jdbcType=DECIMAL},
            </if>
            <if test="pcId != null" >
                #{pcId,jdbcType=DECIMAL},
            </if>
            <if test="prName != null" >
                #{prName,jdbcType=VARCHAR},
            </if>
            <if test="eadCode != null" >
                #{eadCode,jdbcType=VARCHAR},
            </if>
            <if test="eastCode != null" >
                #{eastCode,jdbcType=VARCHAR},
            </if>
            <if test="prType != null" >
                #{prType,jdbcType=VARCHAR},
            </if>
            <if test="prPrice != null" >
                #{prPrice,jdbcType=DECIMAL},
            </if>
            <if test="prMinprice != null" >
                #{prMinprice,jdbcType=DECIMAL},
            </if>
            <if test="prFirstweight != null" >
                #{prFirstweight,jdbcType=DECIMAL},
            </if>
            <if test="prFirstprice != null" >
                #{prFirstprice,jdbcType=DECIMAL},
            </if>
            <if test="prAdditionalweight != null" >
                #{prAdditionalweight,jdbcType=DECIMAL},
            </if>
            <if test="prAdditionalprice != null" >
                #{prAdditionalprice,jdbcType=DECIMAL},
            </if>
            <if test="ctCode != null" >
                #{ctCode,jdbcType=DECIMAL},
            </if>
            <if test="prEffectivedate != null" >
                #{prEffectivedate,jdbcType=TIMESTAMP},
            </if>
            <if test="prExpireddate != null" >
                #{prExpireddate,jdbcType=TIMESTAMP},
            </if>
            <if test="prHandletime != null" >
                #{prHandletime,jdbcType=TIMESTAMP},
            </if>
            <if test="prUserId != null" >
                #{prUserId,jdbcType=DECIMAL},
            </if>
            <if test="prStatus != null" >
                #{prStatus,jdbcType=VARCHAR},
            </if>
            <if test="prBaseprice != null" >
                #{prBaseprice,jdbcType=DECIMAL},
            </if>
            <if test="prSpecialKey != null" >
                #{prSpecialKey,jdbcType=VARCHAR},
            </if>
            <if test="pdSyscode != null" >
                #{pdSyscode,jdbcType=DECIMAL},
            </if>
            <if test="prAwbType != null" >
                #{prAwbType,jdbcType=VARCHAR},
            </if>
            <if test="prAuto != null" >
                #{prAuto,jdbcType=VARCHAR},
            </if>
            <if test="prDest != null" >
                #{prDest,jdbcType=VARCHAR},
            </if>
            <if test="companyId != null" >
                #{companyId,jdbcType=VARCHAR},
            </if>
            <if test="weightUnit != null">
                #{weightUnit,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.PriceReceipt" >
        update PRICE_RECEIPT
        <set >
            <if test="pcId != null" >
                PC_ID = #{pcId,jdbcType=DECIMAL},
            </if>
            <if test="prName != null" >
                PR_NAME = #{prName,jdbcType=VARCHAR},
            </if>
            <if test="eadCode != null" >
                EAD_CODE = #{eadCode,jdbcType=VARCHAR},
            </if>
            <if test="eastCode != null" >
                EAST_CODE = #{eastCode,jdbcType=VARCHAR},
            </if>
            <if test="prType != null" >
                PR_TYPE = #{prType,jdbcType=VARCHAR},
            </if>
            <if test="prPrice != null" >
                PR_PRICE = #{prPrice,jdbcType=DECIMAL},
            </if>
            <if test="prMinprice != null" >
                PR_MINPRICE = #{prMinprice,jdbcType=DECIMAL},
            </if>
            <if test="prFirstweight != null" >
                PR_FIRSTWEIGHT = #{prFirstweight,jdbcType=DECIMAL},
            </if>
            <if test="prFirstprice != null" >
                PR_FIRSTPRICE = #{prFirstprice,jdbcType=DECIMAL},
            </if>
            <if test="prAdditionalweight != null" >
                PR_ADDITIONALWEIGHT = #{prAdditionalweight,jdbcType=DECIMAL},
            </if>
            <if test="prAdditionalprice != null" >
                PR_ADDITIONALPRICE = #{prAdditionalprice,jdbcType=DECIMAL},
            </if>
            <if test="ctCode != null" >
                CT_CODE = #{ctCode,jdbcType=DECIMAL},
            </if>
            <if test="prEffectivedate != null" >
                PR_EFFECTIVEDATE = #{prEffectivedate,jdbcType=TIMESTAMP},
            </if>
            <if test="prExpireddate != null" >
                PR_EXPIREDDATE = #{prExpireddate,jdbcType=TIMESTAMP},
            </if>
            <if test="prHandletime != null" >
                PR_HANDLETIME = #{prHandletime,jdbcType=TIMESTAMP},
            </if>
            <if test="prUserId != null" >
                PR_USER_ID = #{prUserId,jdbcType=DECIMAL},
            </if>
            <if test="prStatus != null" >
                PR_STATUS = #{prStatus,jdbcType=VARCHAR},
            </if>
            <if test="prBaseprice != null" >
                PR_BASEPRICE = #{prBaseprice,jdbcType=DECIMAL},
            </if>
            <if test="prSpecialKey != null" >
                PR_SPECIAL_KEY = #{prSpecialKey,jdbcType=VARCHAR},
            </if>
            <if test="pdSyscode != null" >
                PD_SYSCODE = #{pdSyscode,jdbcType=DECIMAL},
            </if>
            <if test="prAwbType != null" >
                PR_AWB_TYPE = #{prAwbType,jdbcType=VARCHAR},
            </if>
            <if test="prAuto != null" >
                PR_AUTO = #{prAuto,jdbcType=VARCHAR},
            </if>
            <if test="prDest != null" >
                PR_DEST = #{prDest,jdbcType=VARCHAR},
            </if>
            <if test="companyId != null" >
                COMPANY_ID = #{companyId,jdbcType=VARCHAR},
            </if>
            <if test="weightUnit != null">
                WEIGHT_UNIT = #{weightUnit,jdbcType=VARCHAR},
            </if>
        </set>
        where PR_ID = #{prId,jdbcType=DECIMAL}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.PriceReceipt" >
    update PRICE_RECEIPT
    set PC_ID = #{pcId,jdbcType=DECIMAL},
      PR_NAME = #{prName,jdbcType=VARCHAR},
      EAD_CODE = #{eadCode,jdbcType=VARCHAR},
      EAST_CODE = #{eastCode,jdbcType=VARCHAR},
      PR_TYPE = #{prType,jdbcType=VARCHAR},
      PR_PRICE = #{prPrice,jdbcType=DECIMAL},
      PR_MINPRICE = #{prMinprice,jdbcType=DECIMAL},
      PR_FIRSTWEIGHT = #{prFirstweight,jdbcType=DECIMAL},
      PR_FIRSTPRICE = #{prFirstprice,jdbcType=DECIMAL},
      PR_ADDITIONALWEIGHT = #{prAdditionalweight,jdbcType=DECIMAL},
      PR_ADDITIONALPRICE = #{prAdditionalprice,jdbcType=DECIMAL},
      CT_CODE = #{ctCode,jdbcType=DECIMAL},
      PR_EFFECTIVEDATE = #{prEffectivedate,jdbcType=TIMESTAMP},
      PR_EXPIREDDATE = #{prExpireddate,jdbcType=TIMESTAMP},
      PR_HANDLETIME = #{prHandletime,jdbcType=TIMESTAMP},
      PR_USER_ID = #{prUserId,jdbcType=DECIMAL},
      PR_STATUS = #{prStatus,jdbcType=VARCHAR},
      PR_BASEPRICE = #{prBaseprice,jdbcType=DECIMAL},
      PR_SPECIAL_KEY = #{prSpecialKey,jdbcType=VARCHAR},
      PD_SYSCODE = #{pdSyscode,jdbcType=DECIMAL},
      PR_AWB_TYPE = #{prAwbType,jdbcType=VARCHAR},
      PR_AUTO = #{prAuto,jdbcType=VARCHAR},
      PR_DEST = #{prDest,jdbcType=VARCHAR},
      COMPANY_ID = #{companyId,jdbcType=VARCHAR},
      WEIGHT_UNIT = #{weightUnit,jdbcType=VARCHAR}
    where PR_ID = #{prId,jdbcType=DECIMAL}
  </update>
    <select id="selectAllSettlementObject" resultType="java.util.HashMap">
    select SO_CODE,SO_NAME from SETTLEMENTOBJECT where 1=1
  </select>
    <select id="selectAllProduct" resultType="java.util.HashMap">
    select P_ID,P_NAME from PRODUCT where 1=1
  </select>
    <select id="selectAllEAD" resultType="java.util.HashMap">
    select EAD_CODE,EAD_NAME from EXPRESSACTIVITYDEFINE where 1=1
  </select>
    <select id="selectAllEAST" resultType="java.util.HashMap">
    select EAST_CODE,EAST_NAME from EXPRESSACTIVITYSTATUSTYPE where 1=1
  </select>
    <select id="selectAllPrice" resultType="java.util.HashMap">
    select PD_SYSCODE,PD_NAME from PRICE_DEFINE where 1=1

  </select>
    <select id="selectPriceForSearch" resultType="java.util.HashMap">
        select DISTINCT t1.* ,
        t3.CT_NAME as CT_NAME,
        t4.EAD_NAME as EAD_NAME,
        t5.EAST_NAME as EAST_NAME,
        t6.SO_NAME AS SO_NAME,
        t7.P_NAME as P_NAME,
        (select count(0) from price_grade where pr_id = t1.pr_id) as GRADE_NUM
        from PRICE_RECEIPT t1
        left join CURRENCYTYPE t3 on t1.CT_CODE=t3.CT_CODE
        left join EXPRESSACTIVITYDEFINE t4 on t1.EAD_CODE=t4.EAD_CODE
        left join EXPRESSACTIVITYSTATUSTYPE t5 on t1.EAST_CODE=t5.EAST_CODE
        ,SETTLEMENTOBJECT t6,PRODUCT t7

        where 1=1
        and t6.so_code=(select so_code from PRODUCT_CUSTOMER where pc_id=t1.pc_id)
        and t7.p_id=(select p_id from PRODUCT_CUSTOMER where pc_id=t1.pc_id)
        and (t1.PR_STATUS='ON' or t1.PR_STATUS='DEFULT')
        <if test="(settlementObj!=null and settlementObj!='') or (fproduct!=null and fproduct!='')">
            and t1.PC_ID IN (select t2.PC_ID from PRODUCT_CUSTOMER t2
            where 1=1
            <if test="settlementObj!=null and settlementObj!=''">and t2.SO_CODE=#{settlementObj}</if>
            <if test="fproduct!=null and fproduct!=''">and t2.P_ID=#{fproduct}</if>
            )
        </if>
        order by t1.PR_HANDLETIME DESC
    </select>
    <select id="querySettlementObj" parameterType="java.lang.String" resultType="java.util.Map">
        select so_code as "id",so_name as "text",so_name as "full_name"
        from settlementobject where 1=1

        <if test="q != null">
        AND UPPER(so_name) like '%'||UPPER(#{q})||'%'
        </if>
        <if test="companyId != null">
            <if test='companyId != "SNR" '>
                AND (UPPER(SAC_ID)=UPPER(#{companyId}) OR UPPER(SAC_ID)='SNR' OR so_type='外运发展')
            </if>
        </if>
        <if test='soCate == "P" '>
            AND (SO_CATE = #{soCate} or SO_CATE IS NULL)
        </if>
    </select>
    <select id="queryProduct" parameterType="java.lang.String" resultType="java.util.Map">
        select P_ID as "id",p_name as "text",p_name as "full_name"
        from product where 1=1
        and OCC_COMPANY_ID is not NULL
        <if test="q != null">
            AND UPPER(p_name) like '%'||UPPER(#{q})||'%'
        </if>
        <if test="companyId != null">
            AND (UPPER(COMPANY_ID)=UPPER(#{companyId}) OR UPPER(COMPANY_ID)='SNR' OR UPPER(OCC_COMPANY_ID)=UPPER(#{companyId}))
        </if>
    </select>
    <select id="priceNameQuery" parameterType="java.lang.String" resultType="java.util.Map">
        select PD_SYSCODE as "id",pd_name as "text",pd_name as "full_name"
        from price_define where 1=1
        <if test="q != null">
            AND UPPER(pd_name) like '%'||UPPER(#{q})||'%'
        </if>
    </select>
    <select id="eadQuery" parameterType="java.lang.String" resultType="java.util.Map">
        select EAD_CODE as "id",concat(concat(concat(EAD_NAME,'('),EAD_CODE),')') as
        "text",concat(concat(concat(EAD_NAME,'('),EAD_CODE),')') as "full_name"
        from EXPRESSACTIVITYDEFINE where 1=1 and EAD_STATUS='ON'
        <if test="q != null">
            AND (UPPER(EAD_NAME) like '%'||UPPER(#{q})||'%' or UPPER(EAD_CODE) like '%'||UPPER(#{q})||'%')
        </if>
    </select>
    <select id="eastQuery" parameterType="java.lang.String" resultType="java.util.Map">
        select EAST_CODE as "id",concat(concat(concat(EAST_NAME,'('),EAST_CODE),')') as
        "text",concat(concat(concat(EAST_NAME,'('),EAST_CODE),')') as "full_name"
        from EXPRESSACTIVITYSTATUSTYPE where 1=1 and EAST_STATUS='ON'
        <if test="q != null">
            AND (UPPER(EAST_NAME) like '%'||UPPER(#{q})||'%' or UPPER(EAST_CODE) like '%'||UPPER(#{q})||'%')
        </if>
    </select>
    <select id="selectProductBysettle" parameterType="java.lang.String" resultType="java.util.Map">
       select p.P_ID,p.P_NAME
       from PRODUCT p
       where p.P_ID
       in(select P1.p_id from PRODUCT_CUSTOMER p1 where P1.so_code=#{settlment} )
       and p.OCC_COMPANY_ID is not NULL
    </select>
    <select id="selectPriceByExample" parameterType="com.sinoair.billing.domain.model.billing.PriceReceipt"
            resultMap="BaseResultMap">
        select *
        from PRICE_RECEIPT
        where 1=1
        and PR_STATUS='ON'
        <if test="prName!=null and prName!=''">
            and PR_NAME=#{prName}
        </if>
        <if test="pcId!=null and pcId!=''">
            and PC_ID=#{pcId}
        </if>
        <if test="prDest!=null and prDest!=''">
            and PR_DEST=#{prDest}
        </if>
        <if test="prSpecialKey!=null and prSpecialKey!=''">
            and PR_SPECIAL_KEY=#{prSpecialKey}
        </if>

    </select>

    <select id="selectPriceReceipt" resultType="com.sinoair.billing.domain.vo.price.PriceReceiptVO">
        select *
        from PRICE_RECEIPT
        where 1=1
        and PR_STATUS='ON'

    </select>

    <select id="selectPriceReceiptCustomer" resultType="com.sinoair.billing.domain.vo.price.PriceReceiptVO">
        select distinct
        pc.so_code,rr.pr_name,rr.ead_code,rr.east_code,rr.pr_effectivedate,rr.pr_expireddate,
        p.p_servicetype_original as serviceType,p.occ_company_id as occCompanyId
        from
        PRICE_RECEIPT rr,product_customer pc,product p
        where rr.pc_id = pc.pc_id
        and pc.p_id = p.p_id
        and rr.pr_status = 'ON'
        and pr_auto = 'Y'
        and pc.so_code = '00060491'

    </select>

</mapper>