<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.billing.dao.billing.ProductCustomerMapper">
    <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.ProductCustomer">
        <id column="PC_ID" jdbcType="DECIMAL" property="pcId"/>
        <result column="P_ID" jdbcType="DECIMAL" property="pId"/>
        <result column="SO_CODE" jdbcType="VARCHAR" property="soCode"/>
    </resultMap>
    <sql id="Base_Column_List">
    PC_ID, P_ID, SO_CODE
  </sql>
    <sql id='TABLE_SEQUENCE'>SEQ_PRODUCT_CUSTOMER.NEXTVAL</sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from PRODUCT_CUSTOMER
        where PC_ID = #{pcId,jdbcType=DECIMAL}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from PRODUCT_CUSTOMER
    where PC_ID = #{pcId,jdbcType=DECIMAL}
  </delete>
    <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.ProductCustomer">
        <selectKey keyProperty="pcId" resultType="java.lang.Integer" order="BEFORE">
            select
            <include refid="TABLE_SEQUENCE"/>
            from dual
        </selectKey>
    insert into PRODUCT_CUSTOMER (PC_ID, P_ID, SO_CODE
      )
    values (#{pcId,jdbcType=DECIMAL}, #{pId,jdbcType=DECIMAL}, #{soCode,jdbcType=VARCHAR}
      )
  </insert>
    <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.ProductCustomer">
        <selectKey keyProperty="pcId" resultType="java.lang.Integer" order="BEFORE">
            select
            <include refid="TABLE_SEQUENCE"/>
            from dual
        </selectKey>
        insert into PRODUCT_CUSTOMER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="pcId != null">
                PC_ID,
            </if>
            <if test="pId != null">
                P_ID,
            </if>
            <if test="soCode != null">
                SO_CODE,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="pcId != null">
                #{pcId,jdbcType=DECIMAL},
            </if>
            <if test="pId != null">
                #{pId,jdbcType=DECIMAL},
            </if>
            <if test="soCode != null">
                #{soCode,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.ProductCustomer">
        update PRODUCT_CUSTOMER
        <set>
            <if test="pId != null">
                P_ID = #{pId,jdbcType=DECIMAL},
            </if>
            <if test="soCode != null">
                SO_CODE = #{soCode,jdbcType=VARCHAR},
            </if>
        </set>
        where PC_ID = #{pcId,jdbcType=DECIMAL}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.ProductCustomer">
    update PRODUCT_CUSTOMER
    set P_ID = #{pId,jdbcType=DECIMAL},
      SO_CODE = #{soCode,jdbcType=VARCHAR}
    where PC_ID = #{pcId,jdbcType=DECIMAL}
  </update>
    <select id="selectByExample" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from PRODUCT_CUSTOMER
        where 1=1
        <if test="pId!=null and pId!=''">
            and P_ID=#{pId}
        </if>
        <if test="so_code!=null and so_code!=''">
            and SO_CODE=#{so_code}
        </if>
    </select>
</mapper>