<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.billing.dao.billing.ProductMapper">
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.Product">
    <id column="P_ID" jdbcType="DECIMAL" property="pId" />
    <result column="P_CODE" jdbcType="VARCHAR" property="pCode" />
    <result column="P_NAME" jdbcType="VARCHAR" property="pName" />
    <result column="COMPANY_ID" jdbcType="VARCHAR" property="companyId" />
    <result column="OCC_COMPANY_ID" jdbcType="VARCHAR" property="occCompanyId" />
    <result column="P_TRANSMODEID" jdbcType="VARCHAR" property="pTransmodeid" />
    <result column="P_SERVICETYPE" jdbcType="VARCHAR" property="pServicetype" />
    <result column="P_DEPART" jdbcType="VARCHAR" property="pDepart" />
    <result column="P_DEST" jdbcType="VARCHAR" property="pDest" />
    <result column="P_BUSINESSTYPE" jdbcType="VARCHAR" property="pBusinesstype" />
    <result column="P_TYPE" jdbcType="VARCHAR" property="pType" />
    <result column="P_DESC" jdbcType="VARCHAR" property="pDesc" />
    <result column="P_STATUS" jdbcType="VARCHAR" property="pStatus" />
    <result column="P_HANDLETIME" jdbcType="TIMESTAMP" property="pHandletime" />
    <result column="P_USER_ID" jdbcType="DECIMAL" property="pUserId" />
    <result column="P_TRANSMODEID_ORIGINAL" jdbcType="VARCHAR" property="pTransmodeIdOriginal" />
    <result column="P_SERVICETYPE_ORIGINAL" jdbcType="VARCHAR" property="pServicetypeOriginal" />
      <result column="M_P_ID" jdbcType="VARCHAR" property="mPId"/>

  </resultMap>
  <sql id="Base_Column_List">
    P_ID, P_CODE, P_NAME, COMPANY_ID, OCC_COMPANY_ID, P_TRANSMODEID, P_SERVICETYPE, P_DEPART,
      P_DEST, P_BUSINESSTYPE, P_TYPE, P_DESC, P_STATUS, P_HANDLETIME,
      P_USER_ID,P_TRANSMODEID_ORIGINAL,P_SERVICETYPE_ORIGINAL,M_P_ID
  </sql>

  <!-- 序列 -->
  <sql id='TABLE_SEQUENCE'>SEQ_PRODUCT.NEXTVAL</sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from PRODUCT
    where P_ID = #{pId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from PRODUCT
    where P_ID = #{pId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.Product">
    <selectKey keyProperty="pId" resultType="int" order="BEFORE">
      select <include refid="TABLE_SEQUENCE" /> from dual
    </selectKey>
    insert into PRODUCT (P_ID, P_CODE, P_NAME, 
      COMPANY_ID, OCC_COMPANY_ID, P_TRANSMODEID, 
      P_SERVICETYPE, P_DEPART, P_DEST, 
      P_BUSINESSTYPE, P_TYPE, P_DESC, 
      P_STATUS, P_HANDLETIME, P_USER_ID,
      P_TRANSMODEID_ORIGINAL,P_SERVICETYPE_ORIGINAL,M_P_ID
      )
    values (#{pId,jdbcType=DECIMAL}, #{pCode,jdbcType=VARCHAR}, #{pName,jdbcType=VARCHAR}, 
      #{companyId,jdbcType=VARCHAR}, #{occCompanyId,jdbcType=VARCHAR}, #{pTransmodeid,jdbcType=VARCHAR}, 
      #{pServicetype,jdbcType=VARCHAR}, #{pDepart,jdbcType=VARCHAR}, #{pDest,jdbcType=VARCHAR}, 
      #{pBusinesstype,jdbcType=VARCHAR}, #{pType,jdbcType=VARCHAR}, #{pDesc,jdbcType=VARCHAR}, 
      #{pStatus,jdbcType=VARCHAR}, #{pHandletime,jdbcType=TIMESTAMP}, #{pUserId,jdbcType=DECIMAL},
      #{pTransmodeIdOriginal,jdbcType=VARCHAR},#{pServicetypeOriginal,jdbcType=VARCHAR},#{mPId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.Product">
    <selectKey keyProperty="pId" resultType="int" order="BEFORE">
      select <include refid="TABLE_SEQUENCE" /> from dual
    </selectKey>
    insert into PRODUCT
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="pId != null">
        P_ID,
      </if>
      <if test="pCode != null">
        P_CODE,
      </if>
      <if test="pName != null">
        P_NAME,
      </if>
      <if test="companyId != null">
        COMPANY_ID,
      </if>
      <if test="occCompanyId != null">
        OCC_COMPANY_ID,
      </if>
      <if test="pTransmodeid != null">
        P_TRANSMODEID,
      </if>
      <if test="pServicetype != null">
        P_SERVICETYPE,
      </if>
      <if test="pDepart != null">
        P_DEPART,
      </if>
      <if test="pDest != null">
        P_DEST,
      </if>
      <if test="pBusinesstype != null">
        P_BUSINESSTYPE,
      </if>
      <if test="pType != null">
        P_TYPE,
      </if>
      <if test="pDesc != null">
        P_DESC,
      </if>
      <if test="pStatus != null">
        P_STATUS,
      </if>
      <if test="pHandletime != null">
        P_HANDLETIME,
      </if>
      <if test="pUserId != null">
        P_USER_ID,
      </if>
      <if test="pTransmodeIdOriginal != null">
        P_TRANSMODEID_ORIGINAL,
      </if>
      <if test="pServicetypeOriginal != null">
        P_SERVICETYPE_ORIGINAL,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="pId != null">
        #{pId,jdbcType=DECIMAL},
      </if>
      <if test="pCode != null">
        #{pCode,jdbcType=VARCHAR},
      </if>
      <if test="pName != null">
        #{pName,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="occCompanyId != null">
        #{occCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="pTransmodeid != null">
        #{pTransmodeid,jdbcType=VARCHAR},
      </if>
      <if test="pServicetype != null">
        #{pServicetype,jdbcType=VARCHAR},
      </if>
      <if test="pDepart != null">
        #{pDepart,jdbcType=VARCHAR},
      </if>
      <if test="pDest != null">
        #{pDest,jdbcType=VARCHAR},
      </if>
      <if test="pBusinesstype != null">
        #{pBusinesstype,jdbcType=VARCHAR},
      </if>
      <if test="pType != null">
        #{pType,jdbcType=VARCHAR},
      </if>
      <if test="pDesc != null">
        #{pDesc,jdbcType=VARCHAR},
      </if>
      <if test="pStatus != null">
        #{pStatus,jdbcType=VARCHAR},
      </if>
      <if test="pHandletime != null">
        #{pHandletime,jdbcType=TIMESTAMP},
      </if>
      <if test="pUserId != null">
        #{pUserId,jdbcType=DECIMAL},
      </if>
      <if test="pTransmodeIdOriginal != null">
        #{pTransmodeIdOriginal,jdbcType=VARCHAR},
      </if>
      <if test="pServicetypeOriginal != null">
        #{pServicetypeOriginal,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.Product">
    update PRODUCT
    <set>
      <if test="pCode != null">
        P_CODE = #{pCode,jdbcType=VARCHAR},
      </if>
      <if test="pName != null">
        P_NAME = #{pName,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        COMPANY_ID = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="occCompanyId != null">
        OCC_COMPANY_ID = #{occCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="pTransmodeid != null">
        P_TRANSMODEID = #{pTransmodeid,jdbcType=VARCHAR},
      </if>
      <if test="pServicetype != null">
        P_SERVICETYPE = #{pServicetype,jdbcType=VARCHAR},
      </if>
      <if test="pDepart != null">
        P_DEPART = #{pDepart,jdbcType=VARCHAR},
      </if>
      <if test="pDest != null">
        P_DEST = #{pDest,jdbcType=VARCHAR},
      </if>
      <if test="pBusinesstype != null">
        P_BUSINESSTYPE = #{pBusinesstype,jdbcType=VARCHAR},
      </if>
      <if test="pType != null">
        P_TYPE = #{pType,jdbcType=VARCHAR},
      </if>
      <if test="pDesc != null">
        P_DESC = #{pDesc,jdbcType=VARCHAR},
      </if>
      <if test="pStatus != null">
        P_STATUS = #{pStatus,jdbcType=VARCHAR},
      </if>
      <if test="pHandletime != null">
        P_HANDLETIME = #{pHandletime,jdbcType=TIMESTAMP},
      </if>
      <if test="pUserId != null">
        P_USER_ID = #{pUserId,jdbcType=DECIMAL},
      </if>
      <if test="pTransmodeIdOriginal != null">
        P_TRANSMODEID_ORIGINAL = #{pTransmodeIdOriginal,jdbcType=VARCHAR},
      </if>
      <if test="pServicetypeOriginal != null">
        P_SERVICETYPE_ORIGINAL = #{pServicetypeOriginal,jdbcType=DECIMAL},
      </if>
        <if test="mPId != null">
            M_P_ID = #{mPId,jdbcType=VARCHAR},
        </if>
    </set>
    where P_ID = #{pId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.Product">
    update PRODUCT
    set P_CODE = #{pCode,jdbcType=VARCHAR},
      P_NAME = #{pName,jdbcType=VARCHAR},
      COMPANY_ID = #{companyId,jdbcType=VARCHAR},
      OCC_COMPANY_ID = #{occCompanyId,jdbcType=VARCHAR},
      P_TRANSMODEID = #{pTransmodeid,jdbcType=VARCHAR},
      P_SERVICETYPE = #{pServicetype,jdbcType=VARCHAR},
      P_DEPART = #{pDepart,jdbcType=VARCHAR},
      P_DEST = #{pDest,jdbcType=VARCHAR},
      P_BUSINESSTYPE = #{pBusinesstype,jdbcType=VARCHAR},
      P_TYPE = #{pType,jdbcType=VARCHAR},
      P_DESC = #{pDesc,jdbcType=VARCHAR},
      P_STATUS = #{pStatus,jdbcType=VARCHAR},
      P_HANDLETIME = #{pHandletime,jdbcType=TIMESTAMP},
      P_USER_ID = #{pUserId,jdbcType=DECIMAL},
      P_TRANSMODEID_ORIGINAL = #{pTransmodeIdOriginal,jdbcType=VARCHAR},
      P_SERVICETYPE_ORIGINAL = #{pServicetypeOriginal,jdbcType=DECIMAL}，
      M_P_ID = #{mPId,jdbcType=VARCHAR}

    where P_ID = #{pId,jdbcType=DECIMAL}
  </update>

  <select id="selectPageInfo" resultType="com.alibaba.fastjson.JSONObject">
      select DISTINCT
        p.p_id AS P_ID,
        p.p_code AS P_CODE,
        p.P_NAME AS P_NAME,
        C1.COMPANY_NAME AS COMPANY_NAME,
        c2.company_name AS OCC_COMPANY_NAME,
        etdo.TRANSMODENAME AS transmode_name_original,
        epo.ep_value as p_service_name_original,
        etd.TRANSMODENAME AS transmode_name,
        ep.ep_value as p_service_name,
        c1.COMPANY_CODE as p_depart,
      p.p_dest as P_DEST,
      p.p_businesstype as businesstype,
      p.p_type,
      p.p_desc,
      p.P_STATUS,
      mp.P_NAME as MP_NAME
      from PRODUCT p
      LEFT JOIN COMPANY c1 on c1.COMPANY_ID = p.COMPANY_ID
      LEFT JOIN COMPANY c2 on c2.COMPANY_ID = p.OCC_COMPANY_ID
      LEFT JOIN EXPRESSTRANSMODEDEFINE etdo on etdo.TRANSMODEID = P.P_TRANSMODEID_ORIGINAL
      LEFT JOIN EXPRESSTRANSMODEDEFINE etd on etd.TRANSMODEID = P.P_TRANSMODEID
      LEFT JOIN EXPRESS_PROPERTY epo on epo.ep_key = p.P_SERVICETYPE_ORIGINAL
      LEFT JOIN EXPRESS_PROPERTY ep on ep.ep_key = p.P_SERVICETYPE
      LEFT JOIN PRODUCT mp on p.m_p_id = mp.p_id
      WHERE 1=1
      <if test=" company_id != null and company_id != 'SNR' ">
        and ( p.COMPANY_ID = #{company_id} or p.OCC_COMPANY_ID =  #{company_id})
      </if>
      <if test="name != null and name!=''">
          AND UPPER(p.P_NAME) like '%'||UPPER(#{name})||'%'
      </if>
      <if test="occ_company_id != '-1' and occ_company_id != null">
        and p.OCC_COMPANY_ID = #{occ_company_id}
      </if>
      <if test="transmode_id != '-1' and transmode_id != null">
        and p.P_TRANSMODEID_ORIGINAL = #{transmode_id}
      </if>
      <if test="service_tpye != '-1' and service_tpye != null">
        and p.P_SERVICETYPE_ORIGINAL = #{service_tpye}
      </if>
      <if test="dest != '' and dest != null">
        AND UPPER(p.P_DEST) like '%'||UPPER(#{dest})||'%'
      </if>
      <if test="business_type != '-1' and business_type != null">
        and p.P_BUSINESSTYPE = #{business_type}
      </if>
      and p.P_STATUS = 'ON'
      order by p.p_id desc
  </select>

  <select id="selectListByCode" resultMap="BaseResultMap" parameterType="java.lang.String">
    select * from PRODUCT p where p.P_CODE = #{code}
  </select>
    <select id="queryMainProduct" parameterType="java.lang.String" resultType="java.util.Map">
        select P_ID as "id",p_name as "text",p_name as "full_name"
        from product where 1=1
        and OCC_COMPANY_ID is not NULL
        and P_TYPE = '销售'
        <if test="companyId != null">
            AND (UPPER(COMPANY_ID)=UPPER(#{companyId}) OR UPPER(COMPANY_ID)='SNR' OR
            UPPER(OCC_COMPANY_ID)=UPPER(#{companyId}))
        </if>
    </select>
</mapper>