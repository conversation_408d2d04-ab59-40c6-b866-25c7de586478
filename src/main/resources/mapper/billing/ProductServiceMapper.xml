<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.billing.dao.billing.ProductServiceMapper">
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.ProductService">
    <id column="PS_ID" jdbcType="DECIMAL" property="psId" />
    <result column="P_ID" jdbcType="DECIMAL" property="pId" />
    <result column="S_ID" jdbcType="DECIMAL" property="sId" />
  </resultMap>
  <sql id="Base_Column_List">
    PS_ID, P_ID, S_ID
  </sql>

  <!-- 序列 -->
  <sql id='TABLE_SEQUENCE'>SEQ_PRODUCT_SERVICE.NEXTVAL</sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from PRODUCT_SERVICE
    where PS_ID = #{psId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from PRODUCT_SERVICE
    where PS_ID = #{psId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.ProductService">
    <selectKey keyProperty="psId" resultType="int" order="BEFORE">
      select <include refid="TABLE_SEQUENCE" /> from dual
    </selectKey>
    insert into PRODUCT_SERVICE (PS_ID, P_ID, S_ID
      )
    values (#{psId,jdbcType=DECIMAL}, #{pId,jdbcType=DECIMAL}, #{sId,jdbcType=DECIMAL}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.ProductService">
    <selectKey keyProperty="psId" resultType="int" order="BEFORE">
      select <include refid="TABLE_SEQUENCE" /> from dual
    </selectKey>
    insert into PRODUCT_SERVICE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="psId != null">
        PS_ID,
      </if>
      <if test="pId != null">
        P_ID,
      </if>
      <if test="sId != null">
        S_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="psId != null">
        #{psId,jdbcType=DECIMAL},
      </if>
      <if test="pId != null">
        #{pId,jdbcType=DECIMAL},
      </if>
      <if test="sId != null">
        #{sId,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.ProductService">
    update PRODUCT_SERVICE
    <set>
      <if test="pId != null">
        P_ID = #{pId,jdbcType=DECIMAL},
      </if>
      <if test="sId != null">
        S_ID = #{sId,jdbcType=DECIMAL},
      </if>
    </set>
    where PS_ID = #{psId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.ProductService">
    update PRODUCT_SERVICE
    set P_ID = #{pId,jdbcType=DECIMAL},
      S_ID = #{sId,jdbcType=DECIMAL}
    where PS_ID = #{psId,jdbcType=DECIMAL}
  </update>

  <delete id="deleteByProductId" parameterType="java.lang.Integer">
    DELETE FROM PRODUCT_SERVICE ps where ps.P_ID = #{pId}
  </delete>

  <select id="selectByProductId" resultMap="BaseResultMap" parameterType="java.lang.Integer">
    select * FROM PRODUCT_SERVICE ps where ps.P_ID = #{pId}
  </select>

  <delete id="deleteBySupplierCode">
    delete from PRODUCT_SERVICE ps where ps.P_ID = #{p_id} AND
    ps.s_id in
    (select s.s_id from service s where s.so_code = #{so_code})
  </delete>
</mapper>