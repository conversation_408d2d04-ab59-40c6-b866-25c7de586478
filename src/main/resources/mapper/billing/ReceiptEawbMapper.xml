<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.billing.dao.billing.ReceiptEawbMapper">
    <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.ReceiptEawb">
        <id column="RE_ID" jdbcType="DECIMAL" property="reId"/>
        <result column="EAWB_PRINTCODE" jdbcType="VARCHAR" property="eawbPrintcode"/>
        <result column="SO_CODE" jdbcType="VARCHAR" property="soCode"/>
        <result column="RR_OCCURTIME" jdbcType="TIMESTAMP" property="rrOccurtime"/>
        <result column="REN_HANDLETIME" jdbcType="TIMESTAMP" property="renHandletime"/>
        <result column="P_STATUS" jdbcType="VARCHAR" property="pStatus"/>
    </resultMap>
    <sql id='TABLE_SEQUENCE'>SEQ_RECEIPT_EAWB.NEXTVAL</sql>
    <sql id="Base_Column_List">
        RE_ID, EAWB_PRINTCODE, SO_CODE, RR_OCCURTIME, REN_HANDLETIME, P_STATUS
    </sql>


    <select id="countReceiptEawb" resultType="int" parameterType="java.lang.String">
        select count(0)
        from receipt_eawb re
        where re.eawb_printcode=#{eawbPrintcode,jdbcType=VARCHAR}
    </select>

    <insert id="insertBatchRrEawb"  parameterType="java.util.List">
        insert into receipt_eawb (eawb_printcode, rr_occurtime)
        <foreach collection="list" item="item" index="index" separator="UNION ALL">
            select #{item.eawbPrintcode,jdbcType=VARCHAR},#{item.rrOccurtime,jdbcType=TIMESTAMP} from dual
        </foreach>

    </insert>

    <select id="countReceiptEawbN" resultType="int" parameterType="java.lang.String">
        select count(0)
        from receipt_eawb_n re
        where re.eawb_printcode=#{eawbPrintcode,jdbcType=VARCHAR}
    </select>

    <insert id="insertBatchRrEawbN"  parameterType="java.util.List">
        insert into receipt_eawb_n (re_id,eawb_printcode, rr_occurtime,so_code,p_status,ren_handletime)
        select SEQ_RECEIPT_EAWB.NEXTVAL,pbd.* from (
        <foreach collection="list" item="item" index="index" separator="UNION ALL">
            select #{item.eawbPrintcode,jdbcType=VARCHAR}, #{item.rrOccurtime,jdbcType=TIMESTAMP},#{item.soCode,jdbcType=VARCHAR},'N',sysdate  from dual
        </foreach>
        ) pbd

    </insert>
    <update id="updateBatchRrEawbN" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
            update receipt_eawb_n
            <set>
                p_status = 'Y',
                ren_handletime = sysdate
            </set>
            where RE_ID = #{item,jdbcType=DECIMAL}
        </foreach>
    </update>
    <select id="selectMaxReId" resultType="java.lang.Long">
        SELECT
            max(RE_ID)
        FROM receipt_eawb_n
    </select>

    <select id="selectMaxRrId" resultType="java.lang.Long">
        SELECT
            max(RR_ID)
        FROM receipt_record
    </select>

    <select id="listReceiptEawb" resultMap="BaseResultMap"
            parameterType="com.sinoair.billing.domain.vo.check.EawbCheckVO">

        SELECT
        rr_id as RE_ID, EAWB_PRINTCODE, SO_CODE, RR_OCCURTIME
        FROM PAYMENT_RECORD
        WHERE 1=1

        <if test="eawbPrintcode != null" >
            and eawb_printcode = #{eawbPrintcode}
        </if>
        <if test="beginNo != null">
            and re_id >= #{beginNo}
            <![CDATA[
          and re_id < #{endNo}
          ]]>
        </if>
        <if test="codeStatus != null" >
            and p_status = 'N'
        </if>
    </select>

    <select id="listReceiptEawb_rr" resultMap="BaseResultMap"
            parameterType="com.sinoair.billing.domain.vo.check.EawbCheckVO">

        SELECT
            EAWB_PRINTCODE, SO_CODE, RR_OCCURTIME
        FROM receipt_record
        WHERE 1=1
            and eawb_printcode is not null
        <if test="eawbPrintcode != null" >
            and eawb_printcode = #{eawbPrintcode}
        </if>
        <if test="beginNo != null">
            and rr_id >= #{beginNo}
            <![CDATA[
          and rr_id < #{endNo}
          ]]>
        </if>
        <if test="codeStatus != null" >
            and p_status = 'N'
        </if>
    </select>
</mapper>