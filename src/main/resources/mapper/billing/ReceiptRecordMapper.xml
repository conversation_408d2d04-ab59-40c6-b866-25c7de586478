<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.billing.dao.billing.ReceiptRecordMapper">
    <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.ReceiptRecord">
        <id column="RR_ID" jdbcType="DECIMAL" property="rrId"/>
        <result column="EAWB_PRINTCODE" jdbcType="VARCHAR" property="eawbPrintcode"/>
        <result column="MAWB_CODE" jdbcType="VARCHAR" property="mawbCode"/>
        <result column="PR_ID" jdbcType="DECIMAL" property="prId"/>
        <result column="DM_ID" jdbcType="DECIMAL" property="dmId"/>
        <result column="SO_CODE" jdbcType="VARCHAR" property="soCode"/>
        <result column="CT_CODE" jdbcType="VARCHAR" property="ctCode"/>
        <result column="COMPANY_ID" jdbcType="VARCHAR" property="companyId"/>
        <result column="RR_NAME" jdbcType="VARCHAR" property="rrName"/>
        <result column="RR_TYPE" jdbcType="VARCHAR" property="rrType"/>
        <result column="RR_PLAN_AMOUNT" jdbcType="DECIMAL" property="rrPlanAmount"/>
        <result column="RR_ACTUAL_AMOUNT" jdbcType="DECIMAL" property="rrActualAmount"/>
        <result column="RR_STATUS" jdbcType="VARCHAR" property="rrStatus"/>
        <result column="RR_USER_ID" jdbcType="DECIMAL" property="rrUserId"/>
        <result column="RR_HANDLETIME" jdbcType="TIMESTAMP" property="rrHandletime"/>
        <result column="RR_AWB_TYPE" jdbcType="VARCHAR" property="rrAwbType"/>
        <result column="RR_REMARK" jdbcType="VARCHAR" property="rrRemark"/>
        <result column="EAWB_REFERENCE1" jdbcType="VARCHAR" property="eawbReference1"/>
        <result column="EAWB_REFERENCE2" jdbcType="VARCHAR" property="eawbReference2"/>
        <result column="EAWB_CHARGEABLEWEIGHT" jdbcType="DECIMAL" property="eawbChargeableweight"/>
        <result column="EAWB_HAWB_QTY" jdbcType="DECIMAL" property="eawbHawbQty"/>
        <result column="RR_OCCURTIME" jdbcType="TIMESTAMP" property="rrOccurtime"/>
        <result column="EP_KEY" jdbcType="VARCHAR" property="epKey"/>
        <result column="PD_SYSCODE" jdbcType="DECIMAL" property="pdSyscode"/>
        <result column="CHARGEABLEWEIGHT" jdbcType="DECIMAL" property="chargeableweight"/>
        <result column="OUTBOUND_COMPANY_ID" jdbcType="VARCHAR" property="outboundCompanyId"/>
        <result column="EAWB_IETYPE" jdbcType="VARCHAR" property="eawbIeType"/>
    </resultMap>
    <resultMap id="ResultReceiptVo" type="com.sinoair.billing.domain.vo.receipt.ReceiptVo">
        <result column="dmId" property="dmId"></result>
        <result column="dmCode" property="dmCode"></result>
        <result column="countNum" property="countNum"></result>
        <result column="sumRrPlanAmount" property="sumRrPlanAmount"></result>
        <result column="sumEawbChargeableweight" property="sumEawbChargeableweight"></result>
    </resultMap>

    <sql id="Common_NC_Column_List" >
        e.eawb_printcode,
        e.mawb_code,
        pr.pr_id,
        e.eawb_so_code as so_code,
        pr.ct_code,
        e.sac_id as companyId,
        pr.pr_name as rrName,
        e.eawb_reference1,
        e.eawb_reference2,e.weight_value as eawbChargeableweight,
        sysdate rrOccurtime,
        nvl(e.eawb_servicetype_original,e.eawb_servicetype) epKey,
        (select pd.pd_syscode from price_define pd where pd.pd_name=pr.pr_name) pdSyscode,
        e.eawb_outbound_sac_id as outboundCompanyId,
        e.eawb_ietype,e.eawb_destcountry,e.eawb_destination,e.eawb_departcountry,e.eawb_departure
        ,e.eawb_quantity as eawbHawbQty
    </sql>

    <sql id='TABLE_SEQUENCE'>SEQ_RECEIPT_RECORD.NEXTVAL</sql>
    <sql id="Base_Column_List">
    RR_ID, EAWB_PRINTCODE, MAWB_CODE, PR_ID, DM_ID, SO_CODE, CT_CODE, COMPANY_ID, RR_NAME,
    RR_TYPE, RR_PLAN_AMOUNT, RR_ACTUAL_AMOUNT, RR_STATUS, RR_USER_ID, RR_HANDLETIME,
    RR_AWB_TYPE, RR_REMARK, EAWB_REFERENCE1, EAWB_REFERENCE2, EAWB_CHARGEABLEWEIGHT,
        EAWB_HAWB_QTY,RR_OCCURTIME,EP_KEY,PD_SYSCODE,CHARGEABLEWEIGHT,OUTBOUND_COMPANY_ID,EAWB_IETYPE
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from RECEIPT_RECORD
        where RR_ID = #{rrId,jdbcType=DECIMAL}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from RECEIPT_RECORD
    where RR_ID = #{rrId,jdbcType=DECIMAL}
  </delete>
    <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.ReceiptRecord">
        <selectKey keyProperty="rrId" resultType="java.math.BigDecimal" order="BEFORE">
            select
            <include refid="TABLE_SEQUENCE"/>
            from dual
        </selectKey>
        insert into RECEIPT_RECORD (RR_ID, EAWB_PRINTCODE, MAWB_CODE,
        PR_ID, DM_ID, SO_CODE,
        CT_CODE, COMPANY_ID, RR_NAME,
        RR_TYPE, RR_PLAN_AMOUNT, RR_ACTUAL_AMOUNT,
        RR_STATUS, RR_USER_ID, RR_HANDLETIME,
        RR_AWB_TYPE, RR_REMARK, EAWB_REFERENCE1,
        EAWB_REFERENCE2, EAWB_CHARGEABLEWEIGHT, EAWB_HAWB_QTY,RR_OCCURTIME,
        EP_KEY,PD_SYSCODE,CHARGEABLEWEIGHT,OUTBOUND_COMPANY_ID,EAWB_IETYPE
        )
        values (#{rrId,jdbcType=DECIMAL}, #{eawbPrintcode,jdbcType=VARCHAR}, #{mawbCode,jdbcType=VARCHAR},
        #{prId,jdbcType=DECIMAL}, #{dmId,jdbcType=DECIMAL}, #{soCode,jdbcType=VARCHAR},
        #{ctCode,jdbcType=VARCHAR}, #{companyId,jdbcType=VARCHAR}, #{rrName,jdbcType=VARCHAR},
        #{rrType,jdbcType=VARCHAR}, #{rrPlanAmount,jdbcType=DECIMAL}, #{rrActualAmount,jdbcType=DECIMAL},
        #{rrStatus,jdbcType=VARCHAR}, #{rrUserId,jdbcType=DECIMAL}, #{rrHandletime,jdbcType=TIMESTAMP},
        #{rrAwbType,jdbcType=VARCHAR}, #{rrRemark,jdbcType=VARCHAR}, #{eawbReference1,jdbcType=VARCHAR},
        #{eawbReference2,jdbcType=VARCHAR}, #{eawbChargeableweight,jdbcType=DECIMAL}, #{eawbHawbQty,jdbcType=DECIMAL},
        #{rrOccurtime,jdbcType=TIMESTAMP},#{epKey,jdbcType=VARCHAR},#{pdSyscode,jdbcType=DECIMAL},
        #{chargeableweight,jdbcType=DECIMAL},#{outboundCompnyId,jdbcType=VARCHAR},#{eawbIeType,jdbcType=VARCHAR}
        )
    </insert>
    <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.ReceiptRecord">
        <selectKey keyProperty="rrId" resultType="java.math.BigDecimal" order="BEFORE">
            select
            <include refid="TABLE_SEQUENCE"/>
            from dual
        </selectKey>
        insert into RECEIPT_RECORD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="rrId != null">
                RR_ID,
            </if>
            <if test="eawbPrintcode != null">
                EAWB_PRINTCODE,
            </if>
            <if test="mawbCode != null">
                MAWB_CODE,
            </if>
            <if test="prId != null">
                PR_ID,
            </if>
            <if test="dmId != null">
                DM_ID,
            </if>
            <if test="soCode != null">
                SO_CODE,
            </if>
            <if test="ctCode != null">
                CT_CODE,
            </if>
            <if test="companyId != null">
                COMPANY_ID,
            </if>
            <if test="rrName != null">
                RR_NAME,
            </if>
            <if test="rrType != null">
                RR_TYPE,
            </if>
            <if test="rrPlanAmount != null">
                RR_PLAN_AMOUNT,
            </if>
            <if test="rrActualAmount != null">
                RR_ACTUAL_AMOUNT,
            </if>
            <if test="rrStatus != null">
                RR_STATUS,
            </if>
            <if test="rrUserId != null">
                RR_USER_ID,
            </if>
            <if test="rrHandletime != null">
                RR_HANDLETIME,
            </if>
            <if test="rrAwbType != null">
                RR_AWB_TYPE,
            </if>
            <if test="rrRemark != null">
                RR_REMARK,
            </if>
            <if test="eawbReference1 != null">
                EAWB_REFERENCE1,
            </if>
            <if test="eawbReference2 != null">
                EAWB_REFERENCE2,
            </if>
            <if test="eawbChargeableweight != null">
                EAWB_CHARGEABLEWEIGHT,
            </if>
            <if test="eawbHawbQty != null">
                EAWB_HAWB_QTY,
            </if>
            <if test="rrOccurtime != null">
                RR_OCCURTIME,
            </if>
            <if test="epKey != null">
                EP_KEY,
            </if>
            <if test="pdSyscode != null">
                PD_SYSCODE,
            </if>
            <if test="chargeableweight != null">
                CHARGEABLEWEIGHT,
            </if>
            <if test="outboundCompnyId != null">
                OUTBOUND_COMPANY_ID,
            </if>
            <if test="eawbIeType != null">
                EAWB_IETYPE,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="rrId != null">
                #{rrId,jdbcType=DECIMAL},
            </if>
            <if test="eawbPrintcode != null">
                #{eawbPrintcode,jdbcType=VARCHAR},
            </if>
            <if test="mawbCode != null">
                #{mawbCode,jdbcType=VARCHAR},
            </if>
            <if test="prId != null">
                #{prId,jdbcType=DECIMAL},
            </if>
            <if test="dmId != null">
                #{dmId,jdbcType=DECIMAL},
            </if>
            <if test="soCode != null">
                #{soCode,jdbcType=VARCHAR},
            </if>
            <if test="ctCode != null">
                #{ctCode,jdbcType=VARCHAR},
            </if>
            <if test="companyId != null">
                #{companyId,jdbcType=VARCHAR},
            </if>
            <if test="rrName != null">
                #{rrName,jdbcType=VARCHAR},
            </if>
            <if test="rrType != null">
                #{rrType,jdbcType=VARCHAR},
            </if>
            <if test="rrPlanAmount != null">
                #{rrPlanAmount,jdbcType=DECIMAL},
            </if>
            <if test="rrActualAmount != null">
                #{rrActualAmount,jdbcType=DECIMAL},
            </if>
            <if test="rrStatus != null">
                #{rrStatus,jdbcType=VARCHAR},
            </if>
            <if test="rrUserId != null">
                #{rrUserId,jdbcType=DECIMAL},
            </if>
            <if test="rrHandletime != null">
                #{rrHandletime,jdbcType=TIMESTAMP},
            </if>
            <if test="rrAwbType != null">
                #{rrAwbType,jdbcType=VARCHAR},
            </if>
            <if test="rrRemark != null">
                #{rrRemark,jdbcType=VARCHAR},
            </if>
            <if test="eawbReference1 != null">
                #{eawbReference1,jdbcType=VARCHAR},
            </if>
            <if test="eawbReference2 != null">
                #{eawbReference2,jdbcType=VARCHAR},
            </if>
            <if test="eawbChargeableweight != null">
                #{eawbChargeableweight,jdbcType=DECIMAL},
            </if>
            <if test="eawbHawbQty != null">
                #{eawbHawbQty,jdbcType=DECIMAL},
            </if>
            <if test="rrOccurtime != null">
                #{rrOccurtime,jdbcType=TIMESTAMP},
            </if>
            <if test="epKey != null">
                #{epKey,jdbcType=VARCHAR},
            </if>
            <if test="pdSyscode != null">
                #{pdSyscode,jdbcType=DECIMAL},
            </if>
            <if test="chargeableweight != null">
                #{chargeableweight,jdbcType=DECIMAL},
            </if>
            <if test="outboundCompnyId != null">
                #{outboundCompnyId,jdbcType=VARCHAR},
            </if>
            <if test="eawbIeType != null">
                #{eawbIeType,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <insert id="insertSelectiveTmp" parameterType="com.sinoair.billing.domain.model.billing.ReceiptRecordTmp">
        <selectKey keyProperty="rrId" resultType="java.math.BigDecimal" order="BEFORE">
            select
            <include refid="TABLE_SEQUENCE"/>
            from dual
        </selectKey>
        insert into RECEIPT_RECORD (RR_ID, EAWB_PRINTCODE, MAWB_CODE,
        PR_ID, DM_ID, SO_CODE,
        CT_CODE, COMPANY_ID, RR_NAME,
        RR_TYPE, RR_PLAN_AMOUNT, RR_ACTUAL_AMOUNT,
        RR_STATUS, RR_USER_ID, RR_HANDLETIME,
        RR_AWB_TYPE, RR_REMARK, EAWB_REFERENCE1,
        EAWB_REFERENCE2, EAWB_CHARGEABLEWEIGHT, EAWB_HAWB_QTY,
        RR_OCCURTIME, EP_KEY, CHARGEABLEWEIGHT,
        PD_SYSCODE, OUTBOUND_COMPANY_ID, ESTIMATE_STATUS,
        EAWB_IETYPE, EAWB_DESTCOUNTRY, EAWB_DESTINATION,
        EAWB_DEPARTCOUNTRY, EAWB_DEPARTURE, BMS_NUM,
        BMS_DIRTY, RR_OCCURTIME2, SO_MODE)
        values (
            #{rrId,jdbcType=DECIMAL}, #{eawbPrintcode,jdbcType=VARCHAR} , #{mawbCode,jdbcType=VARCHAR} ,
            #{prId,jdbcType=DECIMAL} , #{dmId,jdbcType=DECIMAL} , #{soCode,jdbcType=VARCHAR} ,
            #{ctCode,jdbcType=VARCHAR} , #{companyId,jdbcType=VARCHAR} , #{rrName,jdbcType=VARCHAR} ,
            #{rrType,jdbcType=VARCHAR}, #{rrPlanAmount,jdbcType=DECIMAL} , #{rrPlanAmount,jdbcType=DECIMAL} ,
            'ON' , 1 , sysdate,
            'H' , #{rrRemark,jdbcType=VARCHAR} , #{eawbReference1,jdbcType=VARCHAR} ,
            #{eawbReference2,jdbcType=VARCHAR} , #{eawbChargeableweight,jdbcType=DECIMAL} , #{eawbHawbQty,jdbcType=DECIMAL} ,
            #{rrOccurtime,jdbcType=TIMESTAMP} , #{epKey,jdbcType=VARCHAR} , #{chargeableweight,jdbcType=DECIMAL} ,
            #{pdSyscode,jdbcType=DECIMAL} , #{outboundCompanyId,jdbcType=VARCHAR} , #{estimateStatus,jdbcType=VARCHAR} ,
            #{eawbIetype,jdbcType=VARCHAR} , #{eawbDestcountry,jdbcType=VARCHAR} , #{eawbDestination,jdbcType=VARCHAR} ,
            #{eawbDepartcountry,jdbcType=VARCHAR} , #{eawbDeparture,jdbcType=VARCHAR} , #{bmsNum,jdbcType=VARCHAR} ,
            #{bmsDirty,jdbcType=VARCHAR} , #{rrOccurtime,jdbcType=TIMESTAMP}, #{soMode,jdbcType=VARCHAR}
        )
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.ReceiptRecord">
        update RECEIPT_RECORD
        <set>
            <if test="eawbPrintcode != null">
                EAWB_PRINTCODE = #{eawbPrintcode,jdbcType=VARCHAR},
            </if>
            <if test="mawbCode != null">
                MAWB_CODE = #{mawbCode,jdbcType=VARCHAR},
            </if>
            <if test="prId != null">
                PR_ID = #{prId,jdbcType=DECIMAL},
            </if>
            <if test="dmId != null">
                DM_ID = #{dmId,jdbcType=DECIMAL},
            </if>
            <if test="soCode != null">
                SO_CODE = #{soCode,jdbcType=VARCHAR},
            </if>
            <if test="ctCode != null">
                CT_CODE = #{ctCode,jdbcType=VARCHAR},
            </if>
            <if test="companyId != null">
                COMPANY_ID = #{companyId,jdbcType=VARCHAR},
            </if>
            <if test="rrName != null">
                RR_NAME = #{rrName,jdbcType=VARCHAR},
            </if>
            <if test="rrType != null">
                RR_TYPE = #{rrType,jdbcType=VARCHAR},
            </if>
            <if test="rrPlanAmount != null">
                RR_PLAN_AMOUNT = #{rrPlanAmount,jdbcType=DECIMAL},
            </if>
            <if test="rrActualAmount != null">
                RR_ACTUAL_AMOUNT = #{rrActualAmount,jdbcType=DECIMAL},
            </if>
            <if test="rrStatus != null">
                RR_STATUS = #{rrStatus,jdbcType=VARCHAR},
            </if>
            <if test="rrUserId != null">
                RR_USER_ID = #{rrUserId,jdbcType=DECIMAL},
            </if>
            <if test="rrHandletime != null">
                RR_HANDLETIME = #{rrHandletime,jdbcType=TIMESTAMP},
            </if>
            <if test="rrAwbType != null">
                RR_AWB_TYPE = #{rrAwbType,jdbcType=VARCHAR},
            </if>
            <if test="rrRemark != null">
                RR_REMARK = #{rrRemark,jdbcType=VARCHAR},
            </if>
            <if test="eawbReference1 != null">
                EAWB_REFERENCE1 = #{eawbReference1,jdbcType=VARCHAR},
            </if>
            <if test="eawbReference2 != null">
                EAWB_REFERENCE2 = #{eawbReference2,jdbcType=VARCHAR},
            </if>
            <if test="eawbChargeableweight != null">
                EAWB_CHARGEABLEWEIGHT = #{eawbChargeableweight,jdbcType=DECIMAL},
            </if>
            <if test="eawbHawbQty != null">
                EAWB_HAWB_QTY = #{eawbHawbQty,jdbcType=DECIMAL},
            </if>
            <if test="rrOccurtime != null">
                RR_OCCURTIME = #{rrOccurtime,jdbcType=TIMESTAMP},
            </if>
            <if test="epKey != null">
                EP_KEY = #{epKey,jdbcType=VARCHAR},
            </if>
            <if test="pdSyscode != null">
                PD_SYSCODE = #{pdSyscode,jdbcType=DECIMAL},
            </if>
            <if test="chargeableweight != null">
                CHARGEABLEWEIGHT=#{chargeableweight,jdbcType=DECIMAL},
            </if>
            <if test="outboundCompnyId != null">
                OUTBOUND_COMPANY_ID=#{outboundCompnyId,jdbcType=VARCHAR},
            </if>
            <if test="eawbIeType != null">
                EAWB_IETYPE=#{eawbIeType,jdbcType=VARCHAR},
            </if>
        </set>
        where RR_ID = #{rrId,jdbcType=DECIMAL}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.ReceiptRecord">
    update RECEIPT_RECORD
    set EAWB_PRINTCODE = #{eawbPrintcode,jdbcType=VARCHAR},
      MAWB_CODE = #{mawbCode,jdbcType=VARCHAR},
      PR_ID = #{prId,jdbcType=DECIMAL},
      DM_ID = #{dmId,jdbcType=DECIMAL},
      SO_CODE = #{soCode,jdbcType=VARCHAR},
      CT_CODE = #{ctCode,jdbcType=VARCHAR},
      COMPANY_ID = #{companyId,jdbcType=VARCHAR},
      RR_NAME = #{rrName,jdbcType=VARCHAR},
      RR_TYPE = #{rrType,jdbcType=VARCHAR},
      RR_PLAN_AMOUNT = #{rrPlanAmount,jdbcType=DECIMAL},
      RR_ACTUAL_AMOUNT = #{rrActualAmount,jdbcType=DECIMAL},
      RR_STATUS = #{rrStatus,jdbcType=VARCHAR},
      RR_USER_ID = #{rrUserId,jdbcType=DECIMAL},
      RR_HANDLETIME = #{rrHandletime,jdbcType=TIMESTAMP},
      RR_AWB_TYPE = #{rrAwbType,jdbcType=VARCHAR},
      RR_REMARK = #{rrRemark,jdbcType=VARCHAR},
      EAWB_REFERENCE1 = #{eawbReference1,jdbcType=VARCHAR},
      EAWB_REFERENCE2 = #{eawbReference2,jdbcType=VARCHAR},
      EAWB_CHARGEABLEWEIGHT = #{eawbChargeableweight,jdbcType=DECIMAL},
      EAWB_HAWB_QTY = #{eawbHawbQty,jdbcType=DECIMAL},
      RR_OCCURTIME = #{rrOccurtime,jdbcType=TIMESTAMP},
      EP_KEY = #{epKey,jdbcType=VARCHAR},
        PD_SYSCODE = #{pdSyscode,jdbcType=DECIMAL},
        CHARGEABLEWEIGHT=#{chargeableweight,jdbcType=DECIMAL},
        OUTBOUND_COMPANY_ID=#{outboundCompnyId,jdbcType=VARCHAR},
        EAWB_IETYPE=#{eawbIeType,jdbcType=VARCHAR}
    where RR_ID = #{rrId,jdbcType=DECIMAL}
  </update>

    <select id="selectSequence" resultType="java.math.BigDecimal" useCache="false" flushCache="true">
       select SEQ_RECEIPT_RECORD.NEXTVAL from dual
    </select>
    <!---生成账单begain-->
    <select id="getReceiptRecorddByRrOccurtime" parameterType="java.util.HashMap"
            resultType="java.lang.Integer">
        select count(rd.rr_id)
            from receipt_record rd, price_define pd
            where rd.pd_syscode = pd.pd_syscode
            and rd.dm_id is null
            and rd.so_code=#{soCode}
            and rd.company_id=#{sacId}
            and rr_type = '1'
            and rd.rr_status='ON'
            and rd.eawb_ietype=#{ieType}
            <if test="rrAwbType!=null and rrAwbType!='' and rrAwbType!='null'">
              and  rd.rr_awb_type=#{rrAwbType}
            </if>
            <if test="mawbCodes!=null and mawbCodes!=''">
                and  rd.mawb_code in
                <foreach collection="mawbCodes" item="mawbCode" open="(" separator="," close=")">
                    #{mawbCode}
                </foreach>
            </if>
            <if test="pdSysCodes!=null and pdSysCodes!=''">
                and  rd.PD_SYSCODE in
                <foreach collection="pdSysCodes" item="pdSysCode" open="(" separator="," close=")">
                    #{pdSysCode}
                </foreach>
            </if>

            <if test="epKeys!=null and epKeys!=''">
                and rd.ep_key in
                <foreach collection="epKeys" item="epKey" open="(" separator="," close=")">
                    #{epKey}
                </foreach>
            </if>
            and (
                  rd.rr_occurtime >= sysdate-60 and
                   rd.rr_occurtime &lt;TRUNC(SYSDATE)
                )
            and rownum &lt;2
    </select>
    <!---生成账单begain-->
    <update id="updateDmIdByRrId" parameterType="java.util.Map">
        update receipt_record rd
            set rd.dm_id         = #{dmId},
            rd.rr_user_id    = #{rrUserId},
            rd.rr_handletime = #{rrHandletime}
            <if test="discountValue!=null and discountValue!=1.0 and discountValue!=0.0">
                ,rd.rr_actual_amount=rd.rr_actual_amount*#{discountValue}
            </if>
        where rd.rr_id in
            (
              select rd.rr_id
                 from receipt_record rd
                  where rd.dm_id is null
                  and rd.so_code=#{soCode}
                  and rd.company_id=#{sacId}
                  and rr_type = '1'
                  and rd.so_mode='RECHARGE'
                  and rd.rr_status='ON'
                  and nvl(rd.eawb_ietype,'E')=#{ieType}
                  <if test="ctCode!=null and ctCode!='' and ctCode!='null'">
                    and  rd.ct_code=#{ctCode}
                  </if>
                  <if test="rrAwbType!=null and rrAwbType!='' and rrAwbType!='null'">
                    and  rd.rr_awb_type=#{rrAwbType}
                  </if>
                  <if test="mawbCodes!=null and mawbCodes!=''">
                        and  rd.mawb_code in
                        <foreach collection="mawbCodes" item="mawbCode" open="(" separator="," close=")">
                            #{mawbCode}
                        </foreach>
                  </if>
                  <if test="pdSysCodes!=null and pdSysCodes!=''">
                        and  rd.PD_SYSCODE in
                        <foreach collection="pdSysCodes" item="pdSysCode" open="(" separator="," close=")">
                            #{pdSysCode}
                        </foreach>
                  </if>

                    <if test="epKeys!=null and epKeys!=''">
                        and rd.ep_key in
                        <foreach collection="epKeys" item="epKey" open="(" separator="," close=")">
                            #{epKey}
                        </foreach>
                    </if>
                   and
                   (
                        rd.rr_occurtime >=
                        to_date(#{startRrOccurtime}, 'yyyy-mm-dd') and
                        rd.rr_occurtime &lt;
                        to_date(#{endRrOccurtime}, 'yyyy-mm-dd')
                    )
            )
    </update>

    <select id="getBmsManifestAmountByRceipt" resultType="com.sinoair.billing.domain.model.billing.BmsManifestDetail">
        select e.sinotrans_id,e.eawb_so_code as so_code,e.eawb_servicetype_original as ep_key,sum(rd.rr_actual_amount) as receipt_amount,min(e.sac_id) as sac_id,
        ( select ct_sign from currencytype where ct_code = rd.ct_code) as ct_sign,to_char(min(rd.RR_OCCURTIME),'yyyymmdd') as dm_month
        from receipt_record rd,expressairwaybill e
        where rd.rr_id in
        (
            select rd.rr_id
            from receipt_record rd, price_define pd
            where rd.pd_syscode = pd.pd_syscode
            and rd.dm_id is null
            and rd.so_code=#{soCode}
            and rd.company_id=#{sacId}
            and rr_type = '1'
            and rd.rr_status='ON'
            and rd.eawb_ietype=#{ieType}
            <if test="ctCode!=null and ctCode!='' and ctCode!='null'">
                and  rd.ct_code=#{ctCode}
            </if>
            <if test="rrAwbType!=null and rrAwbType!='' and rrAwbType!='null'">
                and  rd.rr_awb_type=#{rrAwbType}
            </if>
            <if test="mawbCodes!=null and mawbCodes!=''">
                and  rd.mawb_code in
                <foreach collection="mawbCodes" item="mawbCode" open="(" separator="," close=")">
                    #{mawbCode}
                </foreach>
            </if>
            <if test="pdSysCodes!=null and pdSysCodes!=''">
                and  rd.PD_SYSCODE in
                <foreach collection="pdSysCodes" item="pdSysCode" open="(" separator="," close=")">
                    #{pdSysCode}
                </foreach>
            </if>

            <if test="epKeys!=null and epKeys!=''">
                and rd.ep_key in
                <foreach collection="epKeys" item="epKey" open="(" separator="," close=")">
                    #{epKey}
                </foreach>
            </if>
            and
            (
            rd.rr_occurtime >=
            to_date(#{startRrOccurtime}, 'yyyy-mm-dd') and
            rd.rr_occurtime &lt;
            to_date(#{endRrOccurtime}, 'yyyy-mm-dd')
            )
        )
        and rd.eawb_printcode = e.eawb_printcode
        group by e.sinotrans_id,e.eawb_so_code,e.eawb_servicetype_original,rd.ct_code
    </select>

    <select id="getReceiptRecordByDmId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
     select * from receipt_record rd where rd.dm_id=#{dmId}
  </select>


    <update id="updateUploadReceiptRecordByRrId" parameterType="com.sinoair.billing.domain.model.billing.ReceiptRecord">
        update receipt_record rd
        <set>
            <if test="rrActualAmount!=null and rrActualAmount!=''">
                rd.rr_actual_amount = #{rrActualAmount},
            </if>
            <if test="rrUserId!=null and rrUserId!=''">
                rd.rr_user_id =#{rrUserId},
            </if>
            <if test="companyId!=null and companyId!=''">
                rd.company_id = #{companyId},
            </if>
            <if test="rrHandletime!=null and rrHandletime!=''">
                rd.rr_handletime = #{rrHandletime}
            </if>
        </set>
        where rd.rr_id = #{rrId}
    </update>


    <update id="updateDmIdByTRrId" parameterType="java.math.BigDecimal">
        update receipt_record rd
         set rd.dm_id = null
        where rd.rr_id = #{rrId}
    </update>

    <select id="getExcelHongByEawbReference2" resultType="com.sinoair.billing.domain.vo.receipt.ReceiptExcelHongVo">
        select rd.eawb_reference2    rdEawbReference2,
            rd.rr_name            rrName,
            eawb.eawb_reference2  eawbReference2,
            eawb.eawb_reference1  eawbReference1,
            eawb.eawb_printcode   eawbPrintcode,
            rd.dm_id              rdDmId,
            rd.rr_plan_amount     eawbEpValue,
            eawb.eawb_destcountry eawbDestcountry,
            rd.company_id         sacId,
            rd.rr_id              rrId,
            rd.pr_id              prId,
            eawb.eawb_chargeableweight chargeableweight,
            eawb.eawb_servicetype      eawbServiceType
            from expressairwaybill eawb
            full outer join receipt_record rd on eawb.eawb_printcode =
            rd.eawb_printcode
        where eawb.eawb_reference2 in
        <foreach collection="eawbReference2s" item="eawbReference2" open="(" separator="," close=")">
            #{eawbReference2}
        </foreach>
        union
        select rd.eawb_reference2    rdEawbReference2,
            rd.rr_name            rrName,
            eawb.eawb_reference2  eawbReference2,
            eawb.eawb_reference1  eawbReference1,
            eawb.eawb_printcode   eawbPrintcode,
            rd.dm_id              rdDmId,
            rd.rr_plan_amount     eawbEpValue,
            eawb.eawb_destcountry eawbDestcountry,
            rd.company_id         sacId,
            rd.rr_id              rrId,
            rd.pr_id              prId,
            eawb.eawb_chargeableweight chargeableweight,
            eawb.eawb_servicetype      eawbServiceType
            from expressairwaybill eawb
            full outer join receipt_record rd on eawb.eawb_printcode =
            rd.eawb_printcode
        where rd.eawb_reference2 in
        <foreach collection="eawbReference2s" item="eawbReference2" open="(" separator="," close=")">
            #{eawbReference2}
        </foreach>
    </select>

    <!--生成账单-->
    <update id="updateBatchDmIdByRrId" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
            update receipt_record
            <set>
                <if test="item.dmId!=null">
                    dm_id = #{item.dmId},
                </if>
                <if test="item.rrUserId!=null">
                    rr_user_id =#{item.rrUserId},
                </if>
                <if test="item.rrHandletime!=null">
                    rr_handletime = #{item.rrHandletime},
                </if>
                <if test="item.rrActualAmount!=null and item.rrActualAmount!=1.0 and item.rrActualAmount!=0.0">
                    rd.rr_actual_amount=rd.rr_actual_amount*#{rrActualAmount}
                </if>
            </set>
            where rr_id=#{item.rrId}
        </foreach>
    </update>

    <!--追加账单-->
    <update id="updateBatchAppend" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
           update receipt_record
            <set>
                <if test="item.dmId!=null and item.dmId!=''">
                    dm_id = #{item.dmId},
                </if>
                <if test="item.rrUserId!=null and item.rrUserId!=''">
                    rr_user_id =#{item.rrUserId},
                </if>
                <if test="item.rrHandletime!=null and item.rrHandletime!=''">
                    rr_handletime = #{item.rrHandletime},
                </if>
                <if test="item.rrRemark!=null and item.rrRemark!=''">
                    rr_remark=#{item.rrRemark},
                </if>
                <if test="item.rrType!=null and item.rrType!=''">
                    rr_type=#{item.rrType},
                </if>
                <if test="item.epKey!=null and item.epKey!=''">
                    ep_key=#{item.epKey}
                </if>
            </set>
            where rr_id=#{item.rrId}
        </foreach>
    </update>

    <!--剔除账单-->
    <update id="updateBatchT" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
            update receipt_record
            <set>
                dm_id = null,
                <if test="item.rrUserId!=null and item.rrUserId!=''">
                    rr_user_id =#{item.rrUserId},
                </if>
                <if test="item.rrHandletime!=null and item.rrHandletime!=''">
                    rr_handletime = #{item.rrHandletime},
                </if>
                <if test="item.rrRemark!=null and item.rrRemark!=''">
                    rr_remark=#{item.rrRemark},
                </if>
                <if test="item.rrType!=null and item.rrType!=''">
                    rr_type=#{item.rrType},
                </if>
                <if test="item.epKey!=null and item.epKey!=''">
                    ep_key=#{item.epKey}
                </if>
            </set>
            where rr_id=#{item.rrId}
        </foreach>
    </update>

    <!--更新金额-->
    <update id="updateBatchRrActualAmount" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
            update receipt_record
            <set>
                <if test="item.rrActualAmount!=null and item.rrActualAmount!=''">
                    rr_actual_amount = #{item.rrActualAmount},
                </if>
                <if test="item.rrUserId!=null and item.rrUserId!=''">
                    rr_user_id =#{item.rrUserId},
                </if>
                <if test="item.rrHandletime!=null and item.rrHandletime!=''">
                    rr_handletime = #{item.rrHandletime},
                </if>
                <if test="item.rrRemark!=null and item.rrRemark!=''">
                    rr_remark=#{item.rrRemark},
                </if>
                <if test="item.rrType!=null and item.rrType!=''">
                    rr_type=#{item.rrType},
                </if>
                <if test="item.epKey!=null and item.epKey!=''">
                    ep_key=#{item.epKey}
                </if>
            </set>
            where rr_id=#{item.rrId}
        </foreach>
    </update>
    <select id="selectInciReceiptList" resultType="java.util.Map" parameterType="com.sinoair.billing.domain.vo.FilterParam">
        select rr.*,so.SO_NAME from receipt_record rr,price_define pd,SETTLEMENTOBJECT so where
        rr.pd_syscode=pd.pd_syscode
        and pd.pd_type='INCIDENT'
        and rr.so_code=so.so_code
        and rr.COMPANY_ID=#{companyId}
        <if test=" pdSyscode!=0 ">
        and rr.pd_syscode=#{pdSyscode}
        </if>
        <if test="starttime!=null and starttime != ''">
            and to_char(rr.rr_occurtime,'yyyy-mm-dd') &gt;= #{starttime}
        </if>
        <if test="endtime!=null and endtime != ''">
            and to_char(rr.rr_occurtime,'yyyy-mm-dd') &lt;= #{endtime}
        </if>
        <if test="code!=null and code!=''">
            and rr.so_code=#{code}
        </if>
        <!-- 初始化时 不查询数据 AND rr.rr_handletime &gt;=sysdate-2 AND rr.rr_handletime &lt;sysdate+1-->
        <if test="(code==null or code=='')and pdSyscode==0 and (starttime==null or starttime == '')
        and (endtime==null or endtime == '')">
            AND 1=2
        </if>
    </select>
    <select id="selectAssReceiptMap" resultType="java.util.Map" parameterType="com.sinoair.billing.domain.vo.FilterParam">
        select e.EA_CODE as MAWBCODE,rr.eawb_chargeableweight as WEIGHT,
        so.so_name as SONAME,rr.eawb_hawb_qty as EAWBS,pd.pd_name as PDNAME,
        rr.rr_plan_amount as PLANAMOUNT,ct.ct_name as CTNAME,e.EAWB_IETYPE as IETYPE
        from SETTLEMENTOBJECT so, expressassignment e,
        receipt_record rr,PRICE_DEFINE pd,currencytype ct
        where rr.so_code=so.so_code
        and e.ea_code=rr.mawb_code
        and ct.CT_CODE=rr.CT_CODE
        and pd.PD_SYSCODE=rr.PD_SYSCODE
        and rr.COMPANY_ID=#{companyId}
        <if test="code!=null and code != ''">
            and so.so_code=#{code}
        </if>
        <if test="pdSyscode!=0">
            and pd.PD_SYSCODE=#{pdSyscode}
        </if>
        <if test="starttime!=null and starttime != ''">
            and to_char(e.ea_handletime,'yyyy-mm-dd') &gt;= #{starttime}
        </if>
        <if test="endtime!=null and endtime != ''">
            and to_char(e.ea_handletime,'yyyy-mm-dd') &lt;= #{endtime}
        </if>
        <!-- 初始化时 不查询 AND e.ea_handletime &gt;=sysdate-2  AND e.ea_handletime &lt;sysdate+1 -->
        <if test="(mawbs==null or mawbs=='')  and (starttime==null or starttime == '') and (endtime==null or endtime == '') and (code==null or code == '') and (pdSyscode==0)">
            AND 1 = 2
        </if>
        <if test="mawbs!=null and mawbs!=''">
            and e.ea_code in
            <foreach collection="mawbs" item="mawb" open="(" close=")" separator=",">
                #{mawb}
            </foreach>
        </if>
        UNION
        SELECT e.EA_CODE as MAWBCODE,e.MAWB_P_WEIGHT as WEIGHT,
        so.so_name as SONAME,e.EA_TOTAL_EAWBS as EAWBS,pd.pd_name as PDNAME,
        null,null,e.EAWB_IETYPE as IETYPE
        FROM expressassignment e,SETTLEMENTOBJECT so,PRICE_DEFINE pd
        where 1=1
        <if test="code!=null and code != ''">
            and so.so_code=#{code}
        </if>
        <if test="pdSyscode!=0">
            and pd.PD_SYSCODE=#{pdSyscode}
        </if>
        <if test="starttime!=null and starttime != ''">
            and to_char(e.ea_handletime,'yyyy-mm-dd') &gt;= #{starttime}
        </if>
        <if test="endtime!=null and endtime != ''">
            and to_char(e.ea_handletime,'yyyy-mm-dd') &lt;= #{endtime}
        </if>
        <!-- 初始化时 不查询 AND e.ea_handletime &gt;=sysdate-2  AND e.ea_handletime &lt;sysdate+1 -->
        <if test="(mawbs==null or mawbs=='')  and (starttime==null or starttime == '') and (endtime==null or endtime == '') and (code==null or code == '') and (pdSyscode==0)">
            AND 1 = 2
        </if>
        <if test="mawbs!=null and mawbs!=''">
            and e.ea_code in
            <foreach collection="mawbs" item="mawb" open="(" close=")" separator=",">
                #{mawb}
            </foreach>
        </if>
    </select>
    <select id="selectReceiptByMawbCodeAndPdSyscode" resultType="com.sinoair.billing.domain.model.billing.ReceiptRecord"
            parameterType="com.sinoair.billing.domain.model.billing.ReceiptRecord">
        select * from receipt_record rr where rr.MAWB_CODE=#{mawbCode}
        and rr.PD_SYSCODE=#{pdSyscode}
    </select>
    <select id="selectReceiptByMawbCodeAndPdSyscodeAndSoCode"
            resultType="com.sinoair.billing.domain.model.billing.ReceiptRecord"
            parameterType="com.sinoair.billing.domain.model.billing.ReceiptRecord">
        select rr.rr_id from receipt_record rr where rr.MAWB_CODE=#{mawbCode}
        and rr.PD_SYSCODE=#{pdSyscode} and rr.SO_CODE=#{soCode}
    </select>

    <select id="selectTotalWeight" resultType="java.math.BigDecimal">
        SELECT sum(EAWB_CHARGEABLEWEIGHT)/2 as weight
        FROM receipt_record
        WHERE DM_ID = #{dmId}
          AND  OUTBOUND_COMPANY_ID = #{sacId}
    </select>

    <select id="selectTotalPieces" resultType="java.lang.Integer">
        SELECT count (DISTINCT eawb_printcode) as pieces
        FROM receipt_record
        WHERE DM_ID = #{dmId}
        AND  OUTBOUND_COMPANY_ID = #{sacId}
    </select>

    <select id="selectTotalWeightPieces" resultType="java.util.Map">
         SELECT sum(EAWB_CHARGEABLEWEIGHT)/2 as weight,
              count (DISTINCT eawb_printcode) as pieces
         FROM receipt_record
         WHERE DM_ID = #{dmId}
          AND  OUTBOUND_COMPANY_ID = #{sacId}
    </select>

    <select id="countReceiptRecord" resultType="int" parameterType="com.sinoair.billing.domain.model.billing.ReceiptRecord">
        select count(0)
        from receipt_record rr
        where rr.eawb_printcode=#{eawbPrintcode,jdbcType=VARCHAR}
        and rr.rr_name=#{rrName,jdbcType=VARCHAR}
    </select>

    <select id="countReceiptRecordTmp" resultType="int" parameterType="com.sinoair.billing.domain.model.billing.ReceiptRecordTmp">
        select count(0)
        from receipt_record_tmp_nhb rr
        where rr.eawb_printcode=#{eawbPrintcode,jdbcType=VARCHAR}
        and rr.OUTBOUND_COMPANY_ID=#{companyId,jdbcType=VARCHAR}
        and rr.so_code=#{soCode,jdbcType=VARCHAR}
        and rr.rr_name=#{rrName,jdbcType=VARCHAR}
    </select>

    <insert id="insertBatch" parameterType="java.util.ArrayList">
        insert into RECEIPT_RECORD (RR_ID, EAWB_PRINTCODE, MAWB_CODE,
        PR_ID, DM_ID, SO_CODE,
        CT_CODE, COMPANY_ID, RR_NAME,
        RR_TYPE, RR_PLAN_AMOUNT, RR_ACTUAL_AMOUNT,
        RR_STATUS, RR_USER_ID, RR_HANDLETIME,
        RR_AWB_TYPE, RR_REMARK, EAWB_REFERENCE1,
        EAWB_REFERENCE2, EAWB_CHARGEABLEWEIGHT, EAWB_HAWB_QTY,
        RR_OCCURTIME, EP_KEY, CHARGEABLEWEIGHT,
        PD_SYSCODE, OUTBOUND_COMPANY_ID, ESTIMATE_STATUS,
        EAWB_IETYPE, EAWB_DESTCOUNTRY, EAWB_DESTINATION,
        EAWB_DEPARTCOUNTRY, EAWB_DEPARTURE, BMS_NUM,
        BMS_DIRTY, RR_OCCURTIME2)
        select SEQ_RECEIPT_RECORD.NEXTVAL,pbd.* from (
        <foreach collection="list" item="item" index="index" separator="UNION ALL">
            select #{item.eawbPrintcode,jdbcType=VARCHAR} eawbPrintcode, #{item.mawbCode,jdbcType=VARCHAR} mawbCode,
            #{item.prId,jdbcType=DECIMAL} prId, #{item.dmId,jdbcType=DECIMAL} dmId, #{item.soCode,jdbcType=VARCHAR} soCode,
            #{item.ctCode,jdbcType=VARCHAR} ctCode, #{item.companyId,jdbcType=VARCHAR} companyId, #{item.rrName,jdbcType=VARCHAR} rrName,
            #{item.rrType,jdbcType=VARCHAR}, #{item.rrPlanAmount,jdbcType=DECIMAL} rrPlanAmount, #{item.rrPlanAmount,jdbcType=DECIMAL} rrActualAmount,
            'ON' rrStatus, 1 rrUserId, sysdate rrHandleTime,
            'H' rrAwbType, #{item.rrRemark,jdbcType=VARCHAR} rrRemark, #{item.eawbReference1,jdbcType=VARCHAR} eawbReference1,
            #{item.eawbReference2,jdbcType=VARCHAR} eawbReference2, #{item.eawbChargeableweight,jdbcType=DECIMAL} eawbChargeableweight, #{item.eawbHawbQty,jdbcType=DECIMAL} eawbHawbQty,
            #{item.rrOccurtime,jdbcType=TIMESTAMP} rrOccurtime, #{item.epKey,jdbcType=VARCHAR} epKey, #{item.chargeableweight,jdbcType=DECIMAL} chargeableweight,
            #{item.pdSyscode,jdbcType=DECIMAL} pdSyscode, #{item.outboundCompanyId,jdbcType=VARCHAR} outboundCompanyId, #{item.estimateStatus,jdbcType=VARCHAR} estimateStatus,
            #{item.eawbIetype,jdbcType=VARCHAR} eawbIetype, #{item.eawbDestcountry,jdbcType=VARCHAR} eawbDestcountry, #{item.eawbDestination,jdbcType=VARCHAR} eawbDestination,
            #{item.eawbDepartcountry,jdbcType=VARCHAR} eawbDepartcountry, #{item.eawbDeparture,jdbcType=VARCHAR} eawbDeparture, #{item.bmsNum,jdbcType=VARCHAR} bmsNum,
            #{item.bmsDirty,jdbcType=VARCHAR} bmsDirty, #{item.rrOccurtime,jdbcType=TIMESTAMP} rrOccurtime2 from dual
        </foreach>
        ) pbd
    </insert>

    <insert id="insertOtherBatch" parameterType="java.util.ArrayList">
        insert into RECEIPT_RECORD_OTHER (RR_ID, EAWB_PRINTCODE, MAWB_CODE,
        PR_ID, DM_ID, SO_CODE,
        CT_CODE, COMPANY_ID, RR_NAME,
        RR_TYPE, RR_PLAN_AMOUNT, RR_ACTUAL_AMOUNT,
        RR_STATUS, RR_USER_ID, RR_HANDLETIME,
        RR_AWB_TYPE, RR_REMARK, EAWB_REFERENCE1,
        EAWB_REFERENCE2, EAWB_CHARGEABLEWEIGHT, EAWB_HAWB_QTY,
        RR_OCCURTIME, EP_KEY, CHARGEABLEWEIGHT,
        PD_SYSCODE, OUTBOUND_COMPANY_ID, ESTIMATE_STATUS,
        EAWB_IETYPE, EAWB_DESTCOUNTRY, EAWB_DESTINATION,
        EAWB_DEPARTCOUNTRY, EAWB_DEPARTURE, BMS_NUM,
        BMS_DIRTY, RR_OCCURTIME2)
        select SEQ_RECEIPT_RECORD.NEXTVAL,pbd.* from (
        <foreach collection="list" item="item" index="index" separator="UNION ALL">
            select #{item.eawbPrintcode,jdbcType=VARCHAR} eawbPrintcode, #{item.mawbCode,jdbcType=VARCHAR} mawbCode,
            #{item.prId,jdbcType=DECIMAL} prId, #{item.dmId,jdbcType=DECIMAL} dmId, #{item.soCode,jdbcType=VARCHAR} soCode,
            #{item.ctCode,jdbcType=VARCHAR} ctCode, #{item.companyId,jdbcType=VARCHAR} companyId, #{item.rrName,jdbcType=VARCHAR} rrName,
            1 rrType, #{item.rrPlanAmount,jdbcType=DECIMAL} rrPlanAmount, #{item.rrPlanAmount,jdbcType=DECIMAL} rrActualAmount,
            'ON' rrStatus, 1 rrUserId, sysdate rrHandleTime,
            'H' rrAwbType, #{item.rrRemark,jdbcType=VARCHAR} rrRemark, #{item.eawbReference1,jdbcType=VARCHAR} eawbReference1,
            #{item.eawbReference2,jdbcType=VARCHAR} eawbReference2, #{item.eawbChargeableweight,jdbcType=DECIMAL} eawbChargeableweight, #{item.eawbHawbQty,jdbcType=DECIMAL} eawbHawbQty,
            #{item.rrOccurtime,jdbcType=TIMESTAMP} rrOccurtime, #{item.epKey,jdbcType=VARCHAR} epKey, #{item.chargeableweight,jdbcType=DECIMAL} chargeableweight,
            #{item.pdSyscode,jdbcType=DECIMAL} pdSyscode, #{item.outboundCompanyId,jdbcType=VARCHAR} outboundCompanyId, #{item.estimateStatus,jdbcType=VARCHAR} estimateStatus,
            #{item.eawbIetype,jdbcType=VARCHAR} eawbIetype, #{item.eawbDestcountry,jdbcType=VARCHAR} eawbDestcountry, #{item.eawbDestination,jdbcType=VARCHAR} eawbDestination,
            #{item.eawbDepartcountry,jdbcType=VARCHAR} eawbDepartcountry, #{item.eawbDeparture,jdbcType=VARCHAR} eawbDeparture, #{item.bmsNum,jdbcType=VARCHAR} bmsNum,
            #{item.bmsDirty,jdbcType=VARCHAR} bmsDirty, #{item.rrOccurtime,jdbcType=TIMESTAMP} rrOccurtime2 from dual
        </foreach>
        ) pbd
    </insert>

    <select id="countByPdSyscode" resultType="int" parameterType="com.sinoair.billing.domain.model.billing.ReceiptRecordTmp">
        select count(0)
        from receipt_record rr
        where rr.eawb_printcode=#{eawbPrintcode,jdbcType=VARCHAR}
        and rr.company_id=#{companyId,jdbcType=VARCHAR}
        and rr.so_code=#{soCode,jdbcType=VARCHAR}
        and rr.pd_syscode=#{pdSyscode,jdbcType=VARCHAR}

    </select>

    <select id="countOtherByPdSyscode" resultType="int" parameterType="com.sinoair.billing.domain.model.billing.ReceiptRecordTmp">
        select count(0)
        from receipt_record_other rr
        where rr.eawb_printcode=#{eawbPrintcode,jdbcType=VARCHAR}
        and rr.company_id=#{companyId,jdbcType=VARCHAR}
        and rr.so_code=#{soCode,jdbcType=VARCHAR}
        and rr.pd_syscode=#{pdSyscode,jdbcType=VARCHAR}

    </select>
    <!--
            and rr.RR_OCCURTIME >=to_date(${startOccurtime},'yyyymmdd')
        <![CDATA[
        and rr.RR_OCCURTIME <to_date(${endOccurtime},'yyyymmdd')
        ]]>
    -->

    <!--更新主单号-->
    <update id="updateBatchMawb" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
            update receipt_record
            <set>
                MAWB_CODE=#{item.mawbCode}
            </set>
            where rr_id=#{item.rrId}
        </foreach>
    </update>

    <!--更新主单号-->
    <update id="updateBatchMawbOther" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
            update receipt_record_other
            <set>
                MAWB_CODE=#{item.mawbCode}
            </set>
            where rr_id=#{item.rrId}
        </foreach>
    </update>

    <select id="selectByEawbPrintcode" parameterType="java.lang.String" resultMap="BaseResultMap">
        select RR_ID, EAWB_PRINTCODE, MAWB_CODE,RR_OCCURTIME from receipt_record rr where rr.eawb_printcode=#{eawbPrintcode,jdbcType=VARCHAR}
    </select>
    <select id="selectOtherByEawbPrintcode" parameterType="java.lang.String" resultMap="BaseResultMap">
        select RR_ID, EAWB_PRINTCODE, MAWB_CODE,RR_OCCURTIME from receipt_record_other rr where rr.eawb_printcode=#{eawbPrintcode,jdbcType=VARCHAR}
    </select>

    <select id="countReceiptRecordHis" resultType="int" parameterType="com.sinoair.billing.domain.model.billing.ReceiptRecord">
        select count(0)
        from receipt_record_his rr
        where rr.eawb_printcode=#{eawbPrintcode,jdbcType=VARCHAR}
        and rr.OUTBOUND_COMPANY_ID=#{companyId,jdbcType=VARCHAR}
        and rr.so_code=#{soCode,jdbcType=VARCHAR}
        and rr.rr_name=#{rrName,jdbcType=VARCHAR}
    </select>

    <select id="selectMaxRrId" resultType="java.lang.Long">
        SELECT
        max(RR_ID)
        FROM receipt_record
    </select>

    <select id="selectListByRrId" resultMap="BaseResultMap"
            parameterType="com.sinoair.billing.domain.vo.query.CeosQuery">

        SELECT
        <include refid="Base_Column_List" />
        FROM receipt_record
        WHERE  1=1
        and rr_id >= #{beginNo}
        <![CDATA[
        and rr_id < #{endNo}
        ]]>

    </select>

    <select id="selectListByDate" resultMap="BaseResultMap"
            parameterType="com.sinoair.billing.domain.vo.query.CommonQuery">

        SELECT
        <include refid="Base_Column_List" />
        FROM receipt_record
        WHERE  1=1
        and dm_id is null
        and so_code=#{soCode,jdbcType=VARCHAR}
        and
        (
        rr_occurtime >=
        to_date(${strStartDate},'yyyymmdd') and
        rr_occurtime &lt;
        to_date(${strEndDate},'yyyymmdd')
        )

    </select>

    <!--追加账单-->
    <update id="updateBatchDmTempId" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
            update receipt_record
            <set>
                dm_id = #{item.dmId},
                rr_handletime = #{item.rrHandletime}
            </set>
            where rr_id=#{item.rrId}
        </foreach>
    </update>

    <select id="selectTotalWeightByDmId" resultType="java.math.BigDecimal">
        SELECT sum(EAWB_CHARGEABLEWEIGHT) as weight
        FROM receipt_record
        WHERE DM_ID = #{dmId}
    </select>

    <select id="selectSumActualAmount" resultType="java.math.BigDecimal">
        SELECT sum(RR_ACTUAL_AMOUNT) as weight
        FROM receipt_record
        WHERE DM_ID = #{dmId}
    </select>

    <select id="selectCollect" resultType="com.sinoair.billing.domain.model.billing.DebitManifestTemporary">
        SELECT sum(RR_ACTUAL_AMOUNT) as dmAmount,sum(eawb_chargeableweight) as dmTotalWeight,count(rr_id) as dmTotalPieces
        FROM receipt_record
        WHERE DM_ID = #{dmId}
    </select>

    <select id="selectBillListByRrId" resultType="com.sinoair.billing.domain.model.billing.ReceiptEawb"
            parameterType="com.sinoair.billing.domain.vo.check.EawbCheckVO">

        SELECT
            r.eawb_printcode as eawbPrintcode,p.company_id as soCode,mp.p_servicetype_original as pCode
        FROM receipt_record r,product p,product mp
        where r.ep_key = p.p_servicetype_original
            and p.m_p_id = mp.p_id
            and r.rr_type = '1'
            and r.rr_actual_amount is not null
        <if test="beginNo != null" >
            and rr_id > #{beginNo}
            <![CDATA[
            and rr_id <= #{endNo}
            ]]>
        </if>
        <if test="startDate != null" >
            and
            (
            rr_occurtime >=
            to_date(${startDate},'yyyymmdd') and
            rr_occurtime &lt;
            to_date(${endDate},'yyyymmdd')
            )
        </if>

    </select>

    <select id="selectBillListInnerByRrId" resultType="com.sinoair.billing.domain.model.billing.ReceiptEawb"
            parameterType="com.sinoair.billing.domain.vo.check.EawbCheckVO">

        SELECT
        r.eawb_printcode as eawbPrintcode,p.company_id as companyId,p.p_servicetype_original as pCode, so.SO_CODE as soCode
        FROM receipt_record r,product p,settlementobject s, settlementobject so
        where r.ep_key = p.p_servicetype_original
        and r.rr_type = '1'
        and r.rr_actual_amount is not null
        and r.SO_CODE = s.SO_CODE
        and s.SETTLE_COMPANY=so.C_CODE
        and so.C_CODE!='ZSU'
        and p.P_TYPE='ONLINE'
        <if test="beginNo != null" >
            and rr_id > #{beginNo}
            <![CDATA[
            and rr_id <= #{endNo}
            ]]>
        </if>
        <if test="startDate != null" >
            and
            (
            rr_occurtime >=
            to_date(${startDate},'yyyymmdd') and
            rr_occurtime &lt;
            to_date(${endDate},'yyyymmdd')
            )
        </if>

    </select>

    <select id="selectOuterRRByEawbPrintcode" parameterType="java.lang.String" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from receipt_record rr
        where rr_type = '1'
              and rr.eawb_printcode=#{eawbPrintcode,jdbcType=VARCHAR}
    </select>

    <select id="listPriceReceipt_nc"  resultType="com.sinoair.billing.domain.model.billing.ReceiptRecord"
           >
        select distinct  <include refid="Common_NC_Column_List"/>,
        round(getReceiptPrice_NEWNC2(e.eawb_printcode,pr.pr_id),2) rrPlanAmount
        from expressairwaybill e,
        product                 p,
        product_customer        pc,
        price_receipt           pr
        where  e.eawb_servicetype_original = p.p_servicetype_original
        and e.eawb_so_code=pc.so_code
        and p.p_id = pc.p_id
        and pr.pc_id = pc.pc_id

        and pr.pr_auto='Y'
        and pr.pr_status='ON'
        and p.p_status='ON'
        and eawb_so_code not in('00060491')
        and pr.pr_special_key=getSpecialKey_RR3_Other(e.eawb_printcode,pr.pr_id)
        and pr.pr_expireddate >= trunc(e.eawb_handletime, 'dd')
        <![CDATA[
        and pr.pr_effectivedate <= trunc(e.eawb_handletime, 'dd')
        ]]>
        and  e.eawb_printcode=#{eawbPrintcode,jdbcType=VARCHAR}
        <if test="rrName != null" >
            and pr.pr_name=#{rrName,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="getBmsManifestFba" resultType="com.sinoair.billing.domain.model.billing.BmsRecDetailFba">
        select e.sinotrans_id,e.eawb_printcode,e.eawb_so_code as so_code,e.eawb_servicetype_original as ep_key,sum(rr.rr_actual_amount) as receipt_amount,min(e.sac_id) as sac_id,
               ( select ct_sign from currencytype where ct_code = rr.ct_code) as ct_sign,to_char(min(RR_OCCURTIME),'yyyymmdd') as dm_month
        from receipt_record rr,expressairwaybill e,settlementobject s
        where rr.eawb_printcode = e.eawb_printcode
          and rr.so_code = s.so_code
          and e.sinotrans_id is not null
          and rr_type = 'FBA'
          and rr.rr_actual_amount is not null
          and rr.dm_id is null
          and rr.so_mode='RECHARGE'
          and rr_occurtime >= to_date(${strStartDate},'yyyymmdd')
          and rr_occurtime &lt; to_date(${strEndDate},'yyyymmdd')
        group by e.sinotrans_id,e.eawb_printcode,e.eawb_so_code ,e.eawb_servicetype_original,rr.ct_code

    </select>

    <update id="updateByPrintcode" parameterType="com.sinoair.billing.domain.model.billing.ReceiptRecord">
        update receipt_record rd
        <set>
            dm_id = #{dmId},
            rr_handletime = sysdate
        </set>
        where eawb_printcode=#{eawbPrintcode}
    </update>

    <select id="getMonthlyDebitManifest" resultType="com.sinoair.billing.domain.model.billing.DebitManifest">
        select rr.SO_CODE soCode,rr.COMPANY_ID companyId,rr.EP_KEY eawbServicetypeOriginal,
        count(distinct rr.eawb_printcode) as dmTotalPieces,
        sum(rr.RR_ACTUAL_AMOUNT) dmPlanAmount,
        to_date(to_char(sysdate,'yyyy-MM')||'01','yyyy-MM-dd')-1 dmEndTime,
        to_date(to_char(ADD_MONTHS(sysdate,-1),'yyyy-MM')||'01','yyyy-MM-dd') dmStartTime
        from RECEIPT_RECORD rr, SETTLEMENTOBJECT so
        where rr.SO_CODE=so.SO_CODE
        and so.SO_VENDOR_TYPE='ONLINE'
        and (rr.SO_MODE='MONTHLY' or rr.SO_MODE is null)
        and rr.RR_TYPE !='INNER'
        and rr.SO_MODE !='RECHARGE'
        and rr.RR_ACTUAL_AMOUNT!=0
        and rr.RR_ACTUAL_AMOUNT is not null
        and rr.RR_STATUS='ON'
        and rr.DM_ID is null
        and rr.RR_OCCURTIME &lt; to_date(to_char(sysdate,'yyyy-MM')||'01','yyyy-MM-dd')
        and rr.RR_OCCURTIME &gt;= to_date(to_char(ADD_MONTHS(sysdate,-1),'yyyy-MM')||'01','yyyy-MM-dd')
        group by rr.SO_CODE,rr.COMPANY_ID,rr.EP_KEY
    </select>

    <select id="getWeekDebitManifest" resultType="com.sinoair.billing.domain.model.billing.DebitManifest">
        select rr.SO_CODE soCode,rr.COMPANY_ID companyId,rr.EP_KEY eawbServicetypeOriginal,
        count(distinct rr.eawb_printcode) as dmTotalPieces,
        sum(rr.RR_ACTUAL_AMOUNT) dmPlanAmount,
        to_date(to_char(sysdate+(2-to_char(sysdate,'d'))-1,'yyyy-MM-dd'),'yyyy-MM-dd') dmEndTime,
        to_date(to_char(sysdate+(2-to_char(sysdate,'d'))-7,'yyyy-MM-dd'),'yyyy-MM-dd') dmStartTime
        from RECEIPT_RECORD rr, SETTLEMENTOBJECT so
        where rr.SO_CODE=so.SO_CODE
        and so.SO_VENDOR_TYPE='ONLINE'
        and so.SO_MODE='WEEK'
        and rr.RR_TYPE !='INNER'
        and (rr.SO_MODE='MONTHLY' or rr.SO_MODE is null)
        and rr.RR_ACTUAL_AMOUNT!=0
        and rr.RR_ACTUAL_AMOUNT is not null
        and rr.RR_STATUS='ON'
        and rr.DM_ID is null
        and rr.RR_OCCURTIME &lt; to_date(to_char(sysdate+(2-to_char(sysdate,'d')),'yyyy-MM-dd'),'yyyy-MM-dd')
        and rr.RR_OCCURTIME &gt;= to_date(to_char(sysdate+(2-to_char(sysdate,'d'))-7,'yyyy-MM-dd'),'yyyy-MM-dd')
        group by rr.SO_CODE,rr.COMPANY_ID,rr.EP_KEY
    </select>

    <select id="getMonthlyTotalWeight" parameterType="java.util.Map" resultType="java.math.BigDecimal">
        select sum(EAWB_CHARGEABLEWEIGHT)
        from EXPRESSAIRWAYBILL
        where EAWB_PRINTCODE in (
        select distinct EAWB_PRINTCODE
        from RECEIPT_RECORD
        where SO_CODE=#{soCode,jdbcType=VARCHAR}
        and COMPANY_ID=#{companyId,jdbcType=VARCHAR}
        and EAWB_SERVICETYPE_ORIGINAL=#{eawbServicetypeOriginal,jdbcType=VARCHAR}
        and RR_TYPE !='INNER'
        and (SO_MODE!='RECHARGE' or SO_MODE is null)
        and RR_STATUS='ON'
        and DM_ID is null
        and RR_ACTUAL_AMOUNT!=0
        and RR_ACTUAL_AMOUNT is not null
        and RR_OCCURTIME &lt; to_date(to_char(sysdate,'yyyy-MM')||'01','yyyy-MM-dd')
        and RR_OCCURTIME &gt;= to_date(to_char(ADD_MONTHS(sysdate,-1),'yyyy-MM')||'01','yyyy-MM-dd')
        )
    </select>

    <select id="getWeekTotalWeight" parameterType="java.util.Map" resultType="java.math.BigDecimal">
        select sum(EAWB_CHARGEABLEWEIGHT)
        from EXPRESSAIRWAYBILL
        where EAWB_PRINTCODE in (
        select distinct EAWB_PRINTCODE
        from RECEIPT_RECORD
        where SO_CODE=#{soCode,jdbcType=VARCHAR}
        and COMPANY_ID=#{companyId,jdbcType=VARCHAR}
        and EAWB_SERVICETYPE_ORIGINAL=#{eawbServicetypeOriginal,jdbcType=VARCHAR}
        and RR_TYPE !='INNER'
        and (SO_MODE!='RECHARGE' or SO_MODE is null)
        and RR_STATUS='ON'
        and DM_ID is null
        and RR_ACTUAL_AMOUNT!=0
        and RR_ACTUAL_AMOUNT is not null
        and RR_OCCURTIME &lt; to_date(to_char(sysdate+(2-to_char(sysdate,'d')),'yyyy-MM-dd'),'yyyy-MM-dd')
        and RR_OCCURTIME &gt;= to_date(to_char(sysdate+(2-to_char(sysdate,'d'))-7,'yyyy-MM-dd'),'yyyy-MM-dd')
        )
    </select>

    <update id="updateDmIdMonthly" parameterType="com.sinoair.billing.domain.model.billing.DebitManifest">
        update RECEIPT_RECORD set DM_ID=#{dmId,jdbcType=DECIMAL}
        where SO_CODE=#{soCode,jdbcType=VARCHAR}
        and COMPANY_ID=#{companyId,jdbcType=VARCHAR}
        and EP_KEY=#{eawbServicetypeOriginal,jdbcType=VARCHAR}
        and RR_TYPE !='INNER'
        and (SO_MODE!='RECHARGE' or SO_MODE is null)
        and RR_STATUS='ON'
        and DM_ID is null
        and RR_ACTUAL_AMOUNT!=0
        and RR_ACTUAL_AMOUNT is not null
        and RR_OCCURTIME &lt; to_date(to_char(sysdate,'yyyy-MM')||'01','yyyy-MM-dd')
        and RR_OCCURTIME &gt;= to_date(to_char(ADD_MONTHS(sysdate,-1),'yyyy-MM')||'01','yyyy-MM-dd')
    </update>

    <update id="updateDmIdWeek" parameterType="com.sinoair.billing.domain.model.billing.DebitManifest">
        update RECEIPT_RECORD set DM_ID=#{dmId,jdbcType=DECIMAL}
        where SO_CODE=#{soCode,jdbcType=VARCHAR}
        and COMPANY_ID=#{companyId,jdbcType=VARCHAR}
        and EP_KEY=#{eawbServicetypeOriginal,jdbcType=VARCHAR}
        and RR_TYPE !='INNER'
        and (SO_MODE!='RECHARGE' or SO_MODE is null)
        and RR_STATUS='ON'
        and DM_ID is null
        and RR_ACTUAL_AMOUNT!=0
        and RR_ACTUAL_AMOUNT is not null
        and rr.RR_OCCURTIME &lt; to_date(to_char(sysdate+(2-to_char(sysdate,'d')),'yyyy-MM-dd'),'yyyy-MM-dd')
        and rr.RR_OCCURTIME &gt;= to_date(to_char(sysdate+(2-to_char(sysdate,'d'))-7,'yyyy-MM-dd'),'yyyy-MM-dd')
    </update>

    <update id="updateBmdSyscode" parameterType="java.util.Map">
        update receipt_record rd
        set rd.bmd_syscode         = #{bmdSyscode},
        rd.rr_user_id    = #{rrUserId}
        <if test="discountValue!=null and discountValue!=1.0 and discountValue!=0.0">
            ,rd.rr_actual_amount=rd.rr_actual_amount*#{discountValue}
        </if>
        where
        rd.rr_id in
        (
        select rr.rr_id
        from receipt_record rr,expressairwaybill e
        where  rr.eawb_printcode = e.eawb_printcode
        and rr.bmd_syscode is null
        and rr.company_id=#{sacId,jdbcType=VARCHAR}
        and e.eawb_type='B2C'
        and rr.rr_status='ON'
        and rr.ct_code=#{ctCode}
        and rr.so_code=#{soCode,jdbcType=VARCHAR}
        <if test="sinotransId!=null and sinotransId!=''">
            and e.sinotrans_id=#{sinotransId,jdbcType=VARCHAR}
        </if>
        <if test="dmId != null and dmId != ''">
            and rr.dm_id=#{dmId}
        </if>
        <if test="dmId == null or dmId == ''">
            and rr.dm_id is null
        </if>
        <if test="sinotransId==null or sinotransId==''">
            and e.sinotrans_id is null
        </if>
        <if test="eawbPrintcode !=null and eawbPrintcode != ''">
            and e.eawb_printcode=#{eawbPrintcode,jdbcType=VARCHAR}
        </if>
        and e.eawb_servicetype_original=#{epKey,jdbcType=VARCHAR}
        and to_char(rr.rr_occurtime,'yyyymmdd') =#{day,jdbcType=VARCHAR}
        )

    </update>


    <select id="getReceiptRecordForDm"
            resultType="com.sinoair.billing.domain.vo.manifest.RechargeDMListVO">
        select rd.so_code,to_char(rd.RR_OCCURTIME,'yyyymmdd') as startTime,
        to_char(rd.RR_OCCURTIME+1,'yyyymmdd') as endTime,rd.ct_code
        from receipt_record rd
        where  rd.dm_id is null
        and rd.company_id='ZSU'
        and rr_type = '1'
        and rd.rr_status='ON'
        and nvl(rd.eawb_ietype,'E')='E'
        and rd.so_mode='RECHARGE'
        and (
        rd.rr_handletime >= sysdate-30 and
        rd.rr_handletime &lt; TRUNC(SYSDATE)
        )
        group by rd.so_code,to_char(rd.RR_OCCURTIME,'yyyymmdd'),to_char(rd.RR_OCCURTIME+1,'yyyymmdd'),rd.ct_code
    </select>

    <insert id="timerInsertBatch">
        <selectKey keyProperty="rrId"  resultType="java.lang.Long" order="BEFORE">
            SELECT SEQ_RECEIPT_RECORD.NEXTVAL AS id FROM dual
        </selectKey>
        insert into RECEIPT_RECORD (RR_ID, EAWB_PRINTCODE, MAWB_CODE,
        PR_ID, DM_ID, SO_CODE,
        CT_CODE, COMPANY_ID, RR_NAME,
        RR_TYPE, RR_PLAN_AMOUNT, RR_ACTUAL_AMOUNT,
        RR_STATUS, RR_USER_ID, RR_HANDLETIME,
        RR_AWB_TYPE, RR_REMARK, EAWB_REFERENCE1,
        EAWB_REFERENCE2, EAWB_CHARGEABLEWEIGHT, EAWB_HAWB_QTY,
        RR_OCCURTIME, EP_KEY, CHARGEABLEWEIGHT,
        PD_SYSCODE, OUTBOUND_COMPANY_ID, ESTIMATE_STATUS,
        EAWB_IETYPE, EAWB_DESTCOUNTRY, EAWB_DESTINATION,
        EAWB_DEPARTCOUNTRY, EAWB_DEPARTURE, BMS_NUM,
        BMS_DIRTY,RR_OCCURTIME2,SO_MODE,FEE_STATUS,CT_RATE,RR_TOTAL_RMB,RR_TOTAL_FC)
        select SEQ_RECEIPT_RECORD.NEXTVAL,pbd.* from (
        <foreach collection="list" item="item" index="index" separator="UNION ALL">
            select #{item.eawbPrintcode,jdbcType=VARCHAR}, #{item.mawbCode,jdbcType=VARCHAR},
            #{item.prId,jdbcType=DECIMAL}, #{item.dmId,jdbcType=DECIMAL}, #{item.soCode,jdbcType=VARCHAR},
            #{item.ctCode,jdbcType=VARCHAR}, #{item.companyId,jdbcType=VARCHAR}, #{item.rrName,jdbcType=VARCHAR},
            #{item.rrType,jdbcType=VARCHAR}, #{item.rrPlanAmount,jdbcType=DECIMAL}, #{item.rrActualAmount,jdbcType=DECIMAL},
            #{item.rrStatus,jdbcType=VARCHAR}, #{item.rrUserId,jdbcType=DECIMAL}, #{item.rrHandletime,jdbcType=TIMESTAMP},
            #{item.rrAwbType,jdbcType=VARCHAR}, #{item.rrRemark,jdbcType=VARCHAR}, #{item.eawbReference1,jdbcType=VARCHAR},
            #{item.eawbReference2,jdbcType=VARCHAR}, #{item.eawbChargeableweight,jdbcType=DECIMAL}, #{item.eawbHawbQty,jdbcType=DECIMAL},
            #{item.rrOccurtime,jdbcType=TIMESTAMP}, #{item.epKey,jdbcType=VARCHAR}, #{item.chargeableweight,jdbcType=DECIMAL},
            #{item.pdSyscode,jdbcType=DECIMAL}, #{item.outboundCompanyId,jdbcType=VARCHAR}, #{item.estimateStatus,jdbcType=VARCHAR},
            #{item.eawbIeType,jdbcType=VARCHAR}, #{item.eawbDestcountry,jdbcType=VARCHAR}, #{item.eawbDestination,jdbcType=VARCHAR},
            #{item.eawbDepartcountry,jdbcType=VARCHAR}, #{item.eawbDeparture,jdbcType=VARCHAR}, #{item.bmsNum,jdbcType=VARCHAR},
            #{item.bmsDirty,jdbcType=VARCHAR}, #{item.rrOccurtime,jdbcType=TIMESTAMP},#{item.soMode,jdbcType=VARCHAR},
            #{item.feeStatus,jdbcType=VARCHAR},#{item.ctRate,jdbcType=DECIMAL},#{item.rrTotalRmb,jdbcType=DECIMAL},
            #{item.rrTotalfc,jdbcType=DECIMAL} from dual
        </foreach>
        ) pbd
    </insert>

    <insert id="receiptRecordBatch" parameterType="java.util.List">
        begin
        <foreach collection="list" item="item" index="index">
            insert into RECEIPT_RECORD (RR_ID, EAWB_PRINTCODE, MAWB_CODE,
            PR_ID, DM_ID, SO_CODE,
            CT_CODE, COMPANY_ID, RR_NAME,
            RR_TYPE, RR_PLAN_AMOUNT, RR_ACTUAL_AMOUNT,
            RR_STATUS, RR_USER_ID, RR_HANDLETIME,
            RR_AWB_TYPE, RR_REMARK, EAWB_REFERENCE1,
            EAWB_REFERENCE2, EAWB_CHARGEABLEWEIGHT, EAWB_HAWB_QTY,
            RR_OCCURTIME, EP_KEY, CHARGEABLEWEIGHT,
            PD_SYSCODE, OUTBOUND_COMPANY_ID, ESTIMATE_STATUS,
            EAWB_IETYPE, EAWB_DESTCOUNTRY, EAWB_DESTINATION,
            EAWB_DEPARTCOUNTRY, EAWB_DEPARTURE, BMS_NUM,
            BMS_DIRTY,RR_OCCURTIME2,SO_MODE,FEE_STATUS,CT_RATE,RR_TOTAL_RMB,RR_TOTAL_FC) VALUES(
            #{item.rrId,jdbcType=DECIMAL},#{item.eawbPrintcode,jdbcType=VARCHAR}, #{item.mawbCode,jdbcType=VARCHAR},
            #{item.prId,jdbcType=DECIMAL}, #{item.dmId,jdbcType=DECIMAL}, #{item.soCode,jdbcType=VARCHAR},
            #{item.ctCode,jdbcType=VARCHAR}, #{item.companyId,jdbcType=VARCHAR}, #{item.rrName,jdbcType=VARCHAR},
            #{item.rrType,jdbcType=VARCHAR}, #{item.rrPlanAmount,jdbcType=DECIMAL}, #{item.rrPlanAmount,jdbcType=DECIMAL},
            #{item.rrStatus,jdbcType=VARCHAR}, #{item.rrUserId,jdbcType=DECIMAL}, #{item.rrHandletime,jdbcType=TIMESTAMP},
            #{item.rrAwbType,jdbcType=VARCHAR}, #{item.rrRemark,jdbcType=VARCHAR}, #{item.eawbReference1,jdbcType=VARCHAR},
            #{item.eawbReference2,jdbcType=VARCHAR}, #{item.eawbChargeableweight,jdbcType=DECIMAL}, #{item.eawbHawbQty,jdbcType=DECIMAL},
            #{item.rrOccurtime,jdbcType=TIMESTAMP}, #{item.epKey,jdbcType=VARCHAR}, #{item.chargeableweight,jdbcType=DECIMAL},
            #{item.pdSyscode,jdbcType=DECIMAL}, #{item.outboundCompanyId,jdbcType=VARCHAR}, #{item.estimateStatus,jdbcType=VARCHAR},
            #{item.eawbIeType,jdbcType=VARCHAR}, #{item.eawbDestcountry,jdbcType=VARCHAR}, #{item.eawbDestination,jdbcType=VARCHAR},
            #{item.eawbDepartcountry,jdbcType=VARCHAR}, #{item.eawbDeparture,jdbcType=VARCHAR}, #{item.bmsNum,jdbcType=VARCHAR},
            #{item.bmsDirty,jdbcType=VARCHAR}, #{item.rrOccurtime,jdbcType=TIMESTAMP},#{item.soMode,jdbcType=VARCHAR},
            #{item.feeStatus,jdbcType=VARCHAR},#{item.ctRate,jdbcType=DECIMAL},#{item.rrTotalRmb,jdbcType=DECIMAL},
            #{item.rrTotalfc,jdbcType=DECIMAL});
        </foreach>
        end;
    </insert>
    <select id="queryByCondition" resultMap="BaseResultMap">
         select * from receipt_record where EAWB_PRINTCODE=#{eawbPrintcode} and pr_id=#{prId}
         <if test="pdSyscode !=null">
              and PD_SYSCODE=#{pdSyscode}
         </if>
    </select>
    <update id="updateTimeAndStatusByRrId">
        update receipt_record set FEE_STATUS=1,RR_OCCURTIME=#{rrOccurtime,jdbcType=TIMESTAMP} where RR_ID = ${rrId}
    </update>

    <select id="existReceiptRecordList" resultType="java.lang.Integer">
        select count(1)
        from receipt_record
        where EAWB_PRINTCODE = #{eawbPrintcode}
          and PR_ID = #{prId}
          and PD_SYSCODE = #{pdSyscode}
          and SO_CODE = #{soCode}
    </select>
</mapper>
