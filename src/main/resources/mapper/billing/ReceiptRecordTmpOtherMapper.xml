<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.ReceiptRecordTmpOtherMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.ReceiptRecordTmp" >
    <result column="RR_ID" property="rrId" jdbcType="DECIMAL" />
    <result column="EAWB_PRINTCODE" property="eawbPrintcode" jdbcType="VARCHAR" />
    <result column="MAWB_CODE" property="mawbCode" jdbcType="VARCHAR" />
    <result column="PR_ID" property="prId" jdbcType="DECIMAL" />
    <result column="DM_ID" property="dmId" jdbcType="DECIMAL" />
    <result column="SO_CODE" property="soCode" jdbcType="VARCHAR" />
    <result column="CT_CODE" property="ctCode" jdbcType="VARCHAR" />
    <result column="COMPANY_ID" property="companyId" jdbcType="VARCHAR" />
    <result column="RR_NAME" property="rrName" jdbcType="VARCHAR" />
    <result column="RR_TYPE" property="rrType" jdbcType="VARCHAR" />
    <result column="RR_PLAN_AMOUNT" property="rrPlanAmount" jdbcType="DECIMAL" />
    <result column="RR_ACTUAL_AMOUNT" property="rrActualAmount" jdbcType="DECIMAL" />
    <result column="RR_STATUS" property="rrStatus" jdbcType="VARCHAR" />
    <result column="RR_USER_ID" property="rrUserId" jdbcType="DECIMAL" />
    <result column="RR_HANDLETIME" property="rrHandletime" jdbcType="TIMESTAMP" />
    <result column="RR_AWB_TYPE" property="rrAwbType" jdbcType="VARCHAR" />
    <result column="RR_REMARK" property="rrRemark" jdbcType="VARCHAR" />
    <result column="EAWB_REFERENCE1" property="eawbReference1" jdbcType="VARCHAR" />
    <result column="EAWB_REFERENCE2" property="eawbReference2" jdbcType="VARCHAR" />
    <result column="EAWB_CHARGEABLEWEIGHT" property="eawbChargeableweight" jdbcType="DECIMAL" />
    <result column="EAWB_HAWB_QTY" property="eawbHawbQty" jdbcType="DECIMAL" />
    <result column="RR_OCCURTIME" property="rrOccurtime" jdbcType="TIMESTAMP" />
    <result column="EP_KEY" property="epKey" jdbcType="VARCHAR" />
    <result column="CHARGEABLEWEIGHT" property="chargeableweight" jdbcType="DECIMAL" />
    <result column="PD_SYSCODE" property="pdSyscode" jdbcType="DECIMAL" />
    <result column="OUTBOUND_COMPANY_ID" property="outboundCompanyId" jdbcType="VARCHAR" />
    <result column="ESTIMATE_STATUS" property="estimateStatus" jdbcType="VARCHAR" />
    <result column="EAWB_IETYPE" property="eawbIetype" jdbcType="VARCHAR" />
    <result column="EAWB_DESTCOUNTRY" property="eawbDestcountry" jdbcType="VARCHAR" />
    <result column="EAWB_DESTINATION" property="eawbDestination" jdbcType="VARCHAR" />
    <result column="EAWB_DEPARTCOUNTRY" property="eawbDepartcountry" jdbcType="VARCHAR" />
    <result column="EAWB_DEPARTURE" property="eawbDeparture" jdbcType="VARCHAR" />
    <result column="BMS_NUM" property="bmsNum" jdbcType="VARCHAR" />
    <result column="BMS_DIRTY" property="bmsDirty" jdbcType="VARCHAR" />
    <result column="RR_OCCURTIME2" property="rrOccurtime2" jdbcType="TIMESTAMP" />
  </resultMap>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.ReceiptRecordTmp" >
    insert into RECEIPT_RECORD_TMP_OTHER (RR_ID, EAWB_PRINTCODE, MAWB_CODE,
      PR_ID, DM_ID, SO_CODE, 
      CT_CODE, COMPANY_ID, RR_NAME, 
      RR_TYPE, RR_PLAN_AMOUNT, RR_ACTUAL_AMOUNT, 
      RR_STATUS, RR_USER_ID, RR_HANDLETIME, 
      RR_AWB_TYPE, RR_REMARK, EAWB_REFERENCE1, 
      EAWB_REFERENCE2, EAWB_CHARGEABLEWEIGHT, EAWB_HAWB_QTY, 
      RR_OCCURTIME, EP_KEY, CHARGEABLEWEIGHT, 
      PD_SYSCODE, OUTBOUND_COMPANY_ID, ESTIMATE_STATUS, 
      EAWB_IETYPE, EAWB_DESTCOUNTRY, EAWB_DESTINATION, 
      EAWB_DEPARTCOUNTRY, EAWB_DEPARTURE, BMS_NUM, 
      BMS_DIRTY, RR_OCCURTIME2)
    values (#{rrId,jdbcType=DECIMAL}, #{eawbPrintcode,jdbcType=VARCHAR}, #{mawbCode,jdbcType=VARCHAR}, 
      #{prId,jdbcType=DECIMAL}, #{dmId,jdbcType=DECIMAL}, #{soCode,jdbcType=VARCHAR}, 
      #{ctCode,jdbcType=VARCHAR}, #{companyId,jdbcType=VARCHAR}, #{rrName,jdbcType=VARCHAR}, 
      #{rrType,jdbcType=VARCHAR}, #{rrPlanAmount,jdbcType=DECIMAL}, #{rrActualAmount,jdbcType=DECIMAL}, 
      #{rrStatus,jdbcType=VARCHAR}, #{rrUserId,jdbcType=DECIMAL}, #{rrHandletime,jdbcType=TIMESTAMP}, 
      #{rrAwbType,jdbcType=VARCHAR}, #{rrRemark,jdbcType=VARCHAR}, #{eawbReference1,jdbcType=VARCHAR}, 
      #{eawbReference2,jdbcType=VARCHAR}, #{eawbChargeableweight,jdbcType=DECIMAL}, #{eawbHawbQty,jdbcType=DECIMAL}, 
      #{rrOccurtime,jdbcType=TIMESTAMP}, #{epKey,jdbcType=OTHER}, #{chargeableweight,jdbcType=DECIMAL}, 
      #{pdSyscode,jdbcType=DECIMAL}, #{outboundCompanyId,jdbcType=VARCHAR}, #{estimateStatus,jdbcType=VARCHAR}, 
      #{eawbIetype,jdbcType=VARCHAR}, #{eawbDestcountry,jdbcType=VARCHAR}, #{eawbDestination,jdbcType=VARCHAR}, 
      #{eawbDepartcountry,jdbcType=VARCHAR}, #{eawbDeparture,jdbcType=VARCHAR}, #{bmsNum,jdbcType=VARCHAR}, 
      #{bmsDirty,jdbcType=VARCHAR}, #{rrOccurtime2,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.ReceiptRecordTmp" >
    insert into RECEIPT_RECORD_TMP_OTHER
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="rrId != null" >
        RR_ID,
      </if>
      <if test="eawbPrintcode != null" >
        EAWB_PRINTCODE,
      </if>
      <if test="mawbCode != null" >
        MAWB_CODE,
      </if>
      <if test="prId != null" >
        PR_ID,
      </if>
      <if test="dmId != null" >
        DM_ID,
      </if>
      <if test="soCode != null" >
        SO_CODE,
      </if>
      <if test="ctCode != null" >
        CT_CODE,
      </if>
      <if test="companyId != null" >
        COMPANY_ID,
      </if>
      <if test="rrName != null" >
        RR_NAME,
      </if>
      <if test="rrType != null" >
        RR_TYPE,
      </if>
      <if test="rrPlanAmount != null" >
        RR_PLAN_AMOUNT,
      </if>
      <if test="rrActualAmount != null" >
        RR_ACTUAL_AMOUNT,
      </if>
      <if test="rrStatus != null" >
        RR_STATUS,
      </if>
      <if test="rrUserId != null" >
        RR_USER_ID,
      </if>
      <if test="rrHandletime != null" >
        RR_HANDLETIME,
      </if>
      <if test="rrAwbType != null" >
        RR_AWB_TYPE,
      </if>
      <if test="rrRemark != null" >
        RR_REMARK,
      </if>
      <if test="eawbReference1 != null" >
        EAWB_REFERENCE1,
      </if>
      <if test="eawbReference2 != null" >
        EAWB_REFERENCE2,
      </if>
      <if test="eawbChargeableweight != null" >
        EAWB_CHARGEABLEWEIGHT,
      </if>
      <if test="eawbHawbQty != null" >
        EAWB_HAWB_QTY,
      </if>
      <if test="rrOccurtime != null" >
        RR_OCCURTIME,
      </if>
      <if test="epKey != null" >
        EP_KEY,
      </if>
      <if test="chargeableweight != null" >
        CHARGEABLEWEIGHT,
      </if>
      <if test="pdSyscode != null" >
        PD_SYSCODE,
      </if>
      <if test="outboundCompanyId != null" >
        OUTBOUND_COMPANY_ID,
      </if>
      <if test="estimateStatus != null" >
        ESTIMATE_STATUS,
      </if>
      <if test="eawbIetype != null" >
        EAWB_IETYPE,
      </if>
      <if test="eawbDestcountry != null" >
        EAWB_DESTCOUNTRY,
      </if>
      <if test="eawbDestination != null" >
        EAWB_DESTINATION,
      </if>
      <if test="eawbDepartcountry != null" >
        EAWB_DEPARTCOUNTRY,
      </if>
      <if test="eawbDeparture != null" >
        EAWB_DEPARTURE,
      </if>
      <if test="bmsNum != null" >
        BMS_NUM,
      </if>
      <if test="bmsDirty != null" >
        BMS_DIRTY,
      </if>
      <if test="rrOccurtime2 != null" >
        RR_OCCURTIME2,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="rrId != null" >
        #{rrId,jdbcType=DECIMAL},
      </if>
      <if test="eawbPrintcode != null" >
        #{eawbPrintcode,jdbcType=VARCHAR},
      </if>
      <if test="mawbCode != null" >
        #{mawbCode,jdbcType=VARCHAR},
      </if>
      <if test="prId != null" >
        #{prId,jdbcType=DECIMAL},
      </if>
      <if test="dmId != null" >
        #{dmId,jdbcType=DECIMAL},
      </if>
      <if test="soCode != null" >
        #{soCode,jdbcType=VARCHAR},
      </if>
      <if test="ctCode != null" >
        #{ctCode,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null" >
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="rrName != null" >
        #{rrName,jdbcType=VARCHAR},
      </if>
      <if test="rrType != null" >
        #{rrType,jdbcType=VARCHAR},
      </if>
      <if test="rrPlanAmount != null" >
        #{rrPlanAmount,jdbcType=DECIMAL},
      </if>
      <if test="rrActualAmount != null" >
        #{rrActualAmount,jdbcType=DECIMAL},
      </if>
      <if test="rrStatus != null" >
        #{rrStatus,jdbcType=VARCHAR},
      </if>
      <if test="rrUserId != null" >
        #{rrUserId,jdbcType=DECIMAL},
      </if>
      <if test="rrHandletime != null" >
        #{rrHandletime,jdbcType=TIMESTAMP},
      </if>
      <if test="rrAwbType != null" >
        #{rrAwbType,jdbcType=VARCHAR},
      </if>
      <if test="rrRemark != null" >
        #{rrRemark,jdbcType=VARCHAR},
      </if>
      <if test="eawbReference1 != null" >
        #{eawbReference1,jdbcType=VARCHAR},
      </if>
      <if test="eawbReference2 != null" >
        #{eawbReference2,jdbcType=VARCHAR},
      </if>
      <if test="eawbChargeableweight != null" >
        #{eawbChargeableweight,jdbcType=DECIMAL},
      </if>
      <if test="eawbHawbQty != null" >
        #{eawbHawbQty,jdbcType=DECIMAL},
      </if>
      <if test="rrOccurtime != null" >
        #{rrOccurtime,jdbcType=TIMESTAMP},
      </if>
      <if test="epKey != null" >
        #{epKey,jdbcType=OTHER},
      </if>
      <if test="chargeableweight != null" >
        #{chargeableweight,jdbcType=DECIMAL},
      </if>
      <if test="pdSyscode != null" >
        #{pdSyscode,jdbcType=DECIMAL},
      </if>
      <if test="outboundCompanyId != null" >
        #{outboundCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="estimateStatus != null" >
        #{estimateStatus,jdbcType=VARCHAR},
      </if>
      <if test="eawbIetype != null" >
        #{eawbIetype,jdbcType=VARCHAR},
      </if>
      <if test="eawbDestcountry != null" >
        #{eawbDestcountry,jdbcType=VARCHAR},
      </if>
      <if test="eawbDestination != null" >
        #{eawbDestination,jdbcType=VARCHAR},
      </if>
      <if test="eawbDepartcountry != null" >
        #{eawbDepartcountry,jdbcType=VARCHAR},
      </if>
      <if test="eawbDeparture != null" >
        #{eawbDeparture,jdbcType=VARCHAR},
      </if>
      <if test="bmsNum != null" >
        #{bmsNum,jdbcType=VARCHAR},
      </if>
      <if test="bmsDirty != null" >
        #{bmsDirty,jdbcType=VARCHAR},
      </if>
      <if test="rrOccurtime2 != null" >
        #{rrOccurtime2,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <insert id="insertBatch" parameterType="java.util.ArrayList">
    insert into RECEIPT_RECORD_TMP_OTHER (RR_ID, EAWB_PRINTCODE, MAWB_CODE,
    PR_ID, DM_ID, SO_CODE,
    CT_CODE, COMPANY_ID, RR_NAME,
    RR_TYPE, RR_PLAN_AMOUNT, RR_ACTUAL_AMOUNT,
    RR_STATUS, RR_USER_ID, RR_HANDLETIME,
    RR_AWB_TYPE, RR_REMARK, EAWB_REFERENCE1,
    EAWB_REFERENCE2, EAWB_CHARGEABLEWEIGHT, EAWB_HAWB_QTY,
    RR_OCCURTIME, EP_KEY, CHARGEABLEWEIGHT,
    PD_SYSCODE, OUTBOUND_COMPANY_ID, ESTIMATE_STATUS,
    EAWB_IETYPE, EAWB_DESTCOUNTRY, EAWB_DESTINATION,
    EAWB_DEPARTCOUNTRY, EAWB_DEPARTURE, BMS_NUM,
    BMS_DIRTY, RR_OCCURTIME2)
    select SEQ_RECEIPT_RECORD_TMP_O.NEXTVAL,pbd.* from (
    <foreach collection="list" item="item" index="index" separator="UNION ALL">
      select #{item.eawbPrintcode,jdbcType=VARCHAR} eawbPrintcode, #{item.mawbCode,jdbcType=VARCHAR} mawbCode,
      #{item.prId,jdbcType=DECIMAL} prId, #{item.dmId,jdbcType=DECIMAL} dmId, #{item.soCode,jdbcType=VARCHAR} soCode,
      #{item.ctCode,jdbcType=VARCHAR} ctCode, #{item.companyId,jdbcType=VARCHAR} companyId, #{item.rrName,jdbcType=VARCHAR} rrName,
      1 rrType, #{item.rrPlanAmount,jdbcType=DECIMAL} rrPlanAmount, #{item.rrPlanAmount,jdbcType=DECIMAL} rrActualAmount,
      'ON' rrStatus, 1 rrUserId, sysdate rrHandleTime,
      'H' rrAwbType, #{item.rrRemark,jdbcType=VARCHAR} rrRemark, #{item.eawbReference1,jdbcType=VARCHAR} eawbReference1,
      #{item.eawbReference2,jdbcType=VARCHAR} eawbReference2, #{item.eawbChargeableweight,jdbcType=DECIMAL} eawbChargeableweight, #{item.eawbHawbQty,jdbcType=DECIMAL} eawbHawbQty,
      #{item.rrOccurtime,jdbcType=TIMESTAMP} rrOccurtime, #{item.epKey,jdbcType=VARCHAR} epKey, #{item.chargeableweight,jdbcType=DECIMAL} chargeableweight,
      #{item.pdSyscode,jdbcType=DECIMAL} pdSyscode, #{item.outboundCompanyId,jdbcType=VARCHAR} outboundCompanyId, #{item.estimateStatus,jdbcType=VARCHAR} estimateStatus,
      #{item.eawbIetype,jdbcType=VARCHAR} eawbIetype, #{item.eawbDestcountry,jdbcType=VARCHAR} eawbDestcountry, #{item.eawbDestination,jdbcType=VARCHAR} eawbDestination,
      #{item.eawbDepartcountry,jdbcType=VARCHAR} eawbDepartcountry, #{item.eawbDeparture,jdbcType=VARCHAR} eawbDeparture, #{item.bmsNum,jdbcType=VARCHAR} bmsNum,
      #{item.bmsDirty,jdbcType=VARCHAR} bmsDirty, #{item.rrOccurtime,jdbcType=TIMESTAMP} rrOccurtime2 from dual
    </foreach>
    ) pbd
  </insert>


  <select id="countReceiptRecordOther" resultType="int" parameterType="com.sinoair.billing.domain.model.billing.ReceiptRecordTmp">
    select count(0)
    from RECEIPT_RECORD_TMP_OTHER rr
    where rr.eawb_printcode=#{eawbPrintcode,jdbcType=VARCHAR}
      and rr.company_id=#{companyId,jdbcType=VARCHAR}
      and rr.so_code=#{soCode,jdbcType=VARCHAR}
      and rr.rr_name=#{rrName,jdbcType=VARCHAR}
  </select>

  <select id="list"  resultType="com.sinoair.billing.domain.model.billing.ReceiptRecordTmp">
    select
    *
    from RECEIPT_RECORD_TMP_OTHER
  </select>

  <delete id="deleteBatch" parameterType="java.util.List" >
    delete from RECEIPT_RECORD_TMP_OTHER
    where RR_ID in
    <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
      #{item.rrId,jdbcType=DECIMAL}
    </foreach>
  </delete>
</mapper>