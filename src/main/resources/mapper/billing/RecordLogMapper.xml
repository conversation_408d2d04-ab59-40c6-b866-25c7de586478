<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.RecordLogMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.RecordLog" >
    <id column="LOG_ID" property="logId" jdbcType="DECIMAL" />
    <result column="RECORD_BATCH" property="recordBatch" jdbcType="VARCHAR" />
    <result column="RECORD_MODULE" property="recordModule" jdbcType="VARCHAR" />
    <result column="RECORD_TYPE" property="recordType" jdbcType="VARCHAR" />
    <result column="RECORD_NAME" property="recordName" jdbcType="VARCHAR" />
    <result column="RECORD_COUNT" property="recordCount" jdbcType="DECIMAL" />
    <result column="RECORD_PARAM" property="recordParam" jdbcType="VARCHAR" />
    <result column="RECORD_REMARK" property="recordRemark" jdbcType="VARCHAR" />
    <result column="ALL_SIZE" property="allSize" jdbcType="DECIMAL" />
    <result column="QUERY_SIZE" property="querySize" jdbcType="DECIMAL" />
    <result column="FAIL_SIZE" property="failSize" jdbcType="DECIMAL" />
    <result column="CHECK_SIZE" property="checkSize" jdbcType="DECIMAL" />
    <result column="HANDLE_SIZE" property="handleSize" jdbcType="DECIMAL" />
    <result column="QUERY_COST" property="queryCost" jdbcType="DECIMAL" />
    <result column="CHECK_COST" property="checkCost" jdbcType="DECIMAL" />
    <result column="HANDLE_COST" property="handleCost" jdbcType="DECIMAL" />
    <result column="ALL_COST" property="allCost" jdbcType="DECIMAL" />
    <result column="TASK_STARTTIME" property="taskStarttime" jdbcType="TIMESTAMP" />
    <result column="LOG_CREATETIME" property="logCreatetime" jdbcType="TIMESTAMP" />
    <result column="LOG_HANDLETIME" property="logHandletime" jdbcType="TIMESTAMP" />
    <result column="LOG_SYSTIME" property="logSystime" jdbcType="TIMESTAMP" />
    <result column="ACTUAL_AMOUNT" property="actualAmount" jdbcType="DECIMAL" />
    <result column="HANDLE_DATE" property="handleDate" jdbcType="TIMESTAMP" />
    <result column="HANDLE_DATE_STR" property="handleDateStr" jdbcType="VARCHAR" />
    <result column="DEADLINE_DATE" property="deadlineDate" jdbcType="TIMESTAMP" />
    <result column="DEADLINE_DATE_STR" property="deadlineDateStr" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    LOG_ID, RECORD_BATCH, RECORD_MODULE, RECORD_TYPE, RECORD_NAME, RECORD_COUNT, RECORD_PARAM,
    RECORD_REMARK, ALL_SIZE, QUERY_SIZE, FAIL_SIZE, CHECK_SIZE, HANDLE_SIZE, QUERY_COST,
    CHECK_COST, HANDLE_COST, ALL_COST, TASK_STARTTIME, LOG_CREATETIME, LOG_HANDLETIME,
    LOG_SYSTIME, ACTUAL_AMOUNT, HANDLE_DATE, HANDLE_DATE_STR, DEADLINE_DATE, DEADLINE_DATE_STR
  </sql>
  <sql id='TABLE_SEQUENCE'>SEQ_LOG.NEXTVAL</sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.math.BigDecimal" >
    select
    <include refid="Base_Column_List" />
    from INS_RECORD_LOG
    where LOG_ID = #{logId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.math.BigDecimal" >
    delete from INS_RECORD_LOG
    where LOG_ID = #{logId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.RecordLog" >
    insert into INS_RECORD_LOG (LOG_ID, RECORD_BATCH, RECORD_MODULE,
    RECORD_TYPE, RECORD_NAME, RECORD_COUNT,
    RECORD_PARAM, RECORD_REMARK, ALL_SIZE,
    QUERY_SIZE, FAIL_SIZE, CHECK_SIZE,
    HANDLE_SIZE, QUERY_COST, CHECK_COST,
    HANDLE_COST, ALL_COST, TASK_STARTTIME,
    LOG_CREATETIME, LOG_HANDLETIME, LOG_SYSTIME,
    ACTUAL_AMOUNT, HANDLE_DATE, HANDLE_DATE_STR,
    DEADLINE_DATE, DEADLINE_DATE_STR)
    values (#{logId,jdbcType=DECIMAL}, #{recordBatch,jdbcType=VARCHAR}, #{recordModule,jdbcType=VARCHAR},
    #{recordType,jdbcType=VARCHAR}, #{recordName,jdbcType=VARCHAR}, #{recordCount,jdbcType=DECIMAL},
    #{recordParam,jdbcType=VARCHAR}, #{recordRemark,jdbcType=VARCHAR}, #{allSize,jdbcType=DECIMAL},
    #{querySize,jdbcType=DECIMAL}, #{failSize,jdbcType=DECIMAL}, #{checkSize,jdbcType=DECIMAL},
    #{handleSize,jdbcType=DECIMAL}, #{queryCost,jdbcType=DECIMAL}, #{checkCost,jdbcType=DECIMAL},
    #{handleCost,jdbcType=DECIMAL}, #{allCost,jdbcType=DECIMAL}, #{taskStarttime,jdbcType=TIMESTAMP},
    #{logCreatetime,jdbcType=TIMESTAMP}, #{logHandletime,jdbcType=TIMESTAMP}, sysdate,
    #{actualAmount,jdbcType=DECIMAL}, #{handleDate,jdbcType=TIMESTAMP}, #{handleDateStr,jdbcType=VARCHAR},
    #{deadlineDate,jdbcType=TIMESTAMP}, #{deadlineDateStr,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.RecordLog" >
    insert into INS_RECORD_LOG
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="logId != null" >
        LOG_ID,
      </if>
      <if test="recordBatch != null" >
        RECORD_BATCH,
      </if>
      <if test="recordModule != null" >
        RECORD_MODULE,
      </if>
      <if test="recordType != null" >
        RECORD_TYPE,
      </if>
      <if test="recordName != null" >
        RECORD_NAME,
      </if>
      <if test="recordCount != null" >
        RECORD_COUNT,
      </if>
      <if test="recordParam != null" >
        RECORD_PARAM,
      </if>
      <if test="recordRemark != null" >
        RECORD_REMARK,
      </if>
      <if test="allSize != null" >
        ALL_SIZE,
      </if>
      <if test="querySize != null" >
        QUERY_SIZE,
      </if>
      <if test="failSize != null" >
        FAIL_SIZE,
      </if>
      <if test="checkSize != null" >
        CHECK_SIZE,
      </if>
      <if test="handleSize != null" >
        HANDLE_SIZE,
      </if>
      <if test="queryCost != null" >
        QUERY_COST,
      </if>
      <if test="checkCost != null" >
        CHECK_COST,
      </if>
      <if test="handleCost != null" >
        HANDLE_COST,
      </if>
      <if test="allCost != null" >
        ALL_COST,
      </if>
      <if test="taskStarttime != null" >
        TASK_STARTTIME,
      </if>
      <if test="logCreatetime != null" >
        LOG_CREATETIME,
      </if>
      <if test="logHandletime != null" >
        LOG_HANDLETIME,
      </if>

        LOG_SYSTIME,

      <if test="actualAmount != null" >
        ACTUAL_AMOUNT,
      </if>
      <if test="handleDate != null" >
        HANDLE_DATE,
      </if>
      <if test="handleDateStr != null" >
        HANDLE_DATE_STR,
      </if>
      <if test="deadlineDate != null" >
        DEADLINE_DATE,
      </if>
      <if test="deadlineDateStr != null" >
        DEADLINE_DATE_STR,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="logId != null" >
        #{logId,jdbcType=DECIMAL},
      </if>
      <if test="recordBatch != null" >
        #{recordBatch,jdbcType=VARCHAR},
      </if>
      <if test="recordModule != null" >
        #{recordModule,jdbcType=VARCHAR},
      </if>
      <if test="recordType != null" >
        #{recordType,jdbcType=VARCHAR},
      </if>
      <if test="recordName != null" >
        #{recordName,jdbcType=VARCHAR},
      </if>
      <if test="recordCount != null" >
        #{recordCount,jdbcType=DECIMAL},
      </if>
      <if test="recordParam != null" >
        #{recordParam,jdbcType=VARCHAR},
      </if>
      <if test="recordRemark != null" >
        #{recordRemark,jdbcType=VARCHAR},
      </if>
      <if test="allSize != null" >
        #{allSize,jdbcType=DECIMAL},
      </if>
      <if test="querySize != null" >
        #{querySize,jdbcType=DECIMAL},
      </if>
      <if test="failSize != null" >
        #{failSize,jdbcType=DECIMAL},
      </if>
      <if test="checkSize != null" >
        #{checkSize,jdbcType=DECIMAL},
      </if>
      <if test="handleSize != null" >
        #{handleSize,jdbcType=DECIMAL},
      </if>
      <if test="queryCost != null" >
        #{queryCost,jdbcType=DECIMAL},
      </if>
      <if test="checkCost != null" >
        #{checkCost,jdbcType=DECIMAL},
      </if>
      <if test="handleCost != null" >
        #{handleCost,jdbcType=DECIMAL},
      </if>
      <if test="allCost != null" >
        #{allCost,jdbcType=DECIMAL},
      </if>
      <if test="taskStarttime != null" >
        #{taskStarttime,jdbcType=TIMESTAMP},
      </if>
      <if test="logCreatetime != null" >
        #{logCreatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="logHandletime != null" >
        #{logHandletime,jdbcType=TIMESTAMP},
      </if>

        sysdate,

      <if test="actualAmount != null" >
        #{actualAmount,jdbcType=DECIMAL},
      </if>
      <if test="handleDate != null" >
        #{handleDate,jdbcType=TIMESTAMP},
      </if>
      <if test="handleDateStr != null" >
        #{handleDateStr,jdbcType=VARCHAR},
      </if>
      <if test="deadlineDate != null" >
        #{deadlineDate,jdbcType=TIMESTAMP},
      </if>
      <if test="deadlineDateStr != null" >
        #{deadlineDateStr,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.RecordLog" >
    update INS_RECORD_LOG
    <set >
      <if test="recordBatch != null" >
        RECORD_BATCH = #{recordBatch,jdbcType=VARCHAR},
      </if>
      <if test="recordModule != null" >
        RECORD_MODULE = #{recordModule,jdbcType=VARCHAR},
      </if>
      <if test="recordType != null" >
        RECORD_TYPE = #{recordType,jdbcType=VARCHAR},
      </if>
      <if test="recordName != null" >
        RECORD_NAME = #{recordName,jdbcType=VARCHAR},
      </if>
      <if test="recordCount != null" >
        RECORD_COUNT = #{recordCount,jdbcType=DECIMAL},
      </if>
      <if test="recordParam != null" >
        RECORD_PARAM = #{recordParam,jdbcType=VARCHAR},
      </if>
      <if test="recordRemark != null" >
        RECORD_REMARK = #{recordRemark,jdbcType=VARCHAR},
      </if>
      <if test="allSize != null" >
        ALL_SIZE = #{allSize,jdbcType=DECIMAL},
      </if>
      <if test="querySize != null" >
        QUERY_SIZE = #{querySize,jdbcType=DECIMAL},
      </if>
      <if test="failSize != null" >
        FAIL_SIZE = #{failSize,jdbcType=DECIMAL},
      </if>
      <if test="checkSize != null" >
        CHECK_SIZE = #{checkSize,jdbcType=DECIMAL},
      </if>
      <if test="handleSize != null" >
        HANDLE_SIZE = #{handleSize,jdbcType=DECIMAL},
      </if>
      <if test="queryCost != null" >
        QUERY_COST = #{queryCost,jdbcType=DECIMAL},
      </if>
      <if test="checkCost != null" >
        CHECK_COST = #{checkCost,jdbcType=DECIMAL},
      </if>
      <if test="handleCost != null" >
        HANDLE_COST = #{handleCost,jdbcType=DECIMAL},
      </if>
      <if test="allCost != null" >
        ALL_COST = #{allCost,jdbcType=DECIMAL},
      </if>
      <if test="taskStarttime != null" >
        TASK_STARTTIME = #{taskStarttime,jdbcType=TIMESTAMP},
      </if>
      <if test="logCreatetime != null" >
        LOG_CREATETIME = #{logCreatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="logHandletime != null" >
        LOG_HANDLETIME = #{logHandletime,jdbcType=TIMESTAMP},
      </if>
      <if test="logSystime != null" >
        LOG_SYSTIME = #{logSystime,jdbcType=TIMESTAMP},
      </if>
      <if test="actualAmount != null" >
        ACTUAL_AMOUNT = #{actualAmount,jdbcType=DECIMAL},
      </if>
      <if test="handleDate != null" >
        HANDLE_DATE = #{handleDate,jdbcType=TIMESTAMP},
      </if>
      <if test="handleDateStr != null" >
        HANDLE_DATE_STR = #{handleDateStr,jdbcType=VARCHAR},
      </if>
      <if test="deadlineDate != null" >
        DEADLINE_DATE = #{deadlineDate,jdbcType=TIMESTAMP},
      </if>
      <if test="deadlineDateStr != null" >
        DEADLINE_DATE_STR = #{deadlineDateStr,jdbcType=VARCHAR},
      </if>
    </set>
    where LOG_ID = #{logId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.RecordLog" >
    update INS_RECORD_LOG
    set RECORD_BATCH = #{recordBatch,jdbcType=VARCHAR},
      RECORD_MODULE = #{recordModule,jdbcType=VARCHAR},
      RECORD_TYPE = #{recordType,jdbcType=VARCHAR},
      RECORD_NAME = #{recordName,jdbcType=VARCHAR},
      RECORD_COUNT = #{recordCount,jdbcType=DECIMAL},
      RECORD_PARAM = #{recordParam,jdbcType=VARCHAR},
      RECORD_REMARK = #{recordRemark,jdbcType=VARCHAR},
      ALL_SIZE = #{allSize,jdbcType=DECIMAL},
      QUERY_SIZE = #{querySize,jdbcType=DECIMAL},
      FAIL_SIZE = #{failSize,jdbcType=DECIMAL},
      CHECK_SIZE = #{checkSize,jdbcType=DECIMAL},
      HANDLE_SIZE = #{handleSize,jdbcType=DECIMAL},
      QUERY_COST = #{queryCost,jdbcType=DECIMAL},
      CHECK_COST = #{checkCost,jdbcType=DECIMAL},
      HANDLE_COST = #{handleCost,jdbcType=DECIMAL},
      ALL_COST = #{allCost,jdbcType=DECIMAL},
      TASK_STARTTIME = #{taskStarttime,jdbcType=TIMESTAMP},
      LOG_CREATETIME = #{logCreatetime,jdbcType=TIMESTAMP},
      LOG_HANDLETIME = #{logHandletime,jdbcType=TIMESTAMP},
      LOG_SYSTIME = #{logSystime,jdbcType=TIMESTAMP},
      ACTUAL_AMOUNT = #{actualAmount,jdbcType=DECIMAL},
      HANDLE_DATE = #{handleDate,jdbcType=TIMESTAMP},
      HANDLE_DATE_STR = #{handleDateStr,jdbcType=VARCHAR},
      DEADLINE_DATE = #{deadlineDate,jdbcType=TIMESTAMP},
      DEADLINE_DATE_STR = #{deadlineDateStr,jdbcType=VARCHAR}
    where LOG_ID = #{logId,jdbcType=DECIMAL}
  </update>

  <select id="selectSeq" resultType="java.lang.Integer">
    select
    <include refid="TABLE_SEQUENCE"/>
    from dual
  </select>
</mapper>