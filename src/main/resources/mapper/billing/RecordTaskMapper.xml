<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.RecordTaskMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.RecordTask" >
    <id column="TASK_ID" property="taskId" jdbcType="DECIMAL" />
    <result column="TASK_TYPE" property="taskType" jdbcType="VARCHAR" />
    <result column="TASK_SO_CODE" property="taskSoCode" jdbcType="VARCHAR" />
    <result column="TASK_START_DATE" property="taskStartDate" jdbcType="VARCHAR" />
    <result column="TASK_END_DATE" property="taskEndDate" jdbcType="VARCHAR" />
    <result column="TASK_KEYENTRYTIME" property="taskKeyentrytime" jdbcType="VARCHAR" />
    <result column="TASK_REMARK" property="taskRemark" jdbcType="VARCHAR" />
    <result column="TASK_STATUS" property="taskStatus" jdbcType="VARCHAR" />
    <result column="TASK_CREATETIME" property="taskCreatetime" jdbcType="TIMESTAMP" />
    <result column="TASK_HANDLETIME" property="taskHandletime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    TASK_ID, TASK_TYPE, TASK_SO_CODE, TASK_START_DATE, TASK_END_DATE, TASK_KEYENTRYTIME, 
    TASK_REMARK, TASK_STATUS, TASK_CREATETIME, TASK_HANDLETIME
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.math.BigDecimal" >
    select 
    <include refid="Base_Column_List" />
    from INS_RECORD_TASK
    where TASK_ID = #{taskId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.math.BigDecimal" >
    delete from INS_RECORD_TASK
    where TASK_ID = #{taskId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.RecordTask" >
    insert into INS_RECORD_TASK (TASK_ID, TASK_TYPE, TASK_SO_CODE, 
      TASK_START_DATE, TASK_END_DATE, TASK_KEYENTRYTIME, 
      TASK_REMARK, TASK_STATUS, TASK_CREATETIME, 
      TASK_HANDLETIME)
    values (#{taskId,jdbcType=DECIMAL}, #{taskType,jdbcType=VARCHAR}, #{taskSoCode,jdbcType=VARCHAR}, 
      #{taskStartDate,jdbcType=VARCHAR}, #{taskEndDate,jdbcType=VARCHAR}, #{taskKeyentrytime,jdbcType=VARCHAR}, 
      #{taskRemark,jdbcType=VARCHAR}, #{taskStatus,jdbcType=VARCHAR}, #{taskCreatetime,jdbcType=TIMESTAMP}, 
      #{taskHandletime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.RecordTask" >
    insert into INS_RECORD_TASK
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="taskId != null" >
        TASK_ID,
      </if>
      <if test="taskType != null" >
        TASK_TYPE,
      </if>
      <if test="taskSoCode != null" >
        TASK_SO_CODE,
      </if>
      <if test="taskStartDate != null" >
        TASK_START_DATE,
      </if>
      <if test="taskEndDate != null" >
        TASK_END_DATE,
      </if>
      <if test="taskKeyentrytime != null" >
        TASK_KEYENTRYTIME,
      </if>
      <if test="taskRemark != null" >
        TASK_REMARK,
      </if>
      <if test="taskStatus != null" >
        TASK_STATUS,
      </if>
      <if test="taskCreatetime != null" >
        TASK_CREATETIME,
      </if>
      <if test="taskHandletime != null" >
        TASK_HANDLETIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="taskId != null" >
        #{taskId,jdbcType=DECIMAL},
      </if>
      <if test="taskType != null" >
        #{taskType,jdbcType=VARCHAR},
      </if>
      <if test="taskSoCode != null" >
        #{taskSoCode,jdbcType=VARCHAR},
      </if>
      <if test="taskStartDate != null" >
        #{taskStartDate,jdbcType=VARCHAR},
      </if>
      <if test="taskEndDate != null" >
        #{taskEndDate,jdbcType=VARCHAR},
      </if>
      <if test="taskKeyentrytime != null" >
        #{taskKeyentrytime,jdbcType=VARCHAR},
      </if>
      <if test="taskRemark != null" >
        #{taskRemark,jdbcType=VARCHAR},
      </if>
      <if test="taskStatus != null" >
        #{taskStatus,jdbcType=VARCHAR},
      </if>
      <if test="taskCreatetime != null" >
        #{taskCreatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="taskHandletime != null" >
        #{taskHandletime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.RecordTask" >
    update INS_RECORD_TASK
    <set >
      <if test="taskType != null" >
        TASK_TYPE = #{taskType,jdbcType=VARCHAR},
      </if>
      <if test="taskSoCode != null" >
        TASK_SO_CODE = #{taskSoCode,jdbcType=VARCHAR},
      </if>
      <if test="taskStartDate != null" >
        TASK_START_DATE = #{taskStartDate,jdbcType=VARCHAR},
      </if>
      <if test="taskEndDate != null" >
        TASK_END_DATE = #{taskEndDate,jdbcType=VARCHAR},
      </if>
      <if test="taskKeyentrytime != null" >
        TASK_KEYENTRYTIME = #{taskKeyentrytime,jdbcType=VARCHAR},
      </if>
      <if test="taskRemark != null" >
        TASK_REMARK = #{taskRemark,jdbcType=VARCHAR},
      </if>
      <if test="taskStatus != null" >
        TASK_STATUS = #{taskStatus,jdbcType=VARCHAR},
      </if>
      <if test="taskCreatetime != null" >
        TASK_CREATETIME = #{taskCreatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="taskHandletime != null" >
        TASK_HANDLETIME = #{taskHandletime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where TASK_ID = #{taskId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.RecordTask" >
    update INS_RECORD_TASK
    set TASK_TYPE = #{taskType,jdbcType=VARCHAR},
      TASK_SO_CODE = #{taskSoCode,jdbcType=VARCHAR},
      TASK_START_DATE = #{taskStartDate,jdbcType=VARCHAR},
      TASK_END_DATE = #{taskEndDate,jdbcType=VARCHAR},
      TASK_KEYENTRYTIME = #{taskKeyentrytime,jdbcType=VARCHAR},
      TASK_REMARK = #{taskRemark,jdbcType=VARCHAR},
      TASK_STATUS = #{taskStatus,jdbcType=VARCHAR},
      TASK_CREATETIME = #{taskCreatetime,jdbcType=TIMESTAMP},
      TASK_HANDLETIME = #{taskHandletime,jdbcType=TIMESTAMP}
    where TASK_ID = #{taskId,jdbcType=DECIMAL}
  </update>

  <select id="selectByCondition" resultMap="BaseResultMap" parameterType="com.sinoair.billing.domain.vo.query.InsRecordQuery" >
    select
    <include refid="Base_Column_List" />
    from INS_RECORD_TASK
    where TASK_STATUS = 'PENDING'
      and TASK_TYPE = #{handleType,jdbcType=VARCHAR}
  </select>
</mapper>