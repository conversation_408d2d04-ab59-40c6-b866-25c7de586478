<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.RoleMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.Role" >
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="CODE" property="code" jdbcType="VARCHAR" />
    <result column="NAME" property="name" jdbcType="VARCHAR" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="EDIT_TIME" property="editTime" jdbcType="TIMESTAMP" />
    <result column="EDIT_USER_ID" property="editUserId" jdbcType="DECIMAL" />
    <result column="DESCRIPTION" property="description" jdbcType="VARCHAR" />
    <result column="COMPANY_ID" property="companyId" jdbcType="VARCHAR" />
    <result column="ROLE_CATE" property="roleCate" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, CODE, NAME, CREATE_TIME, EDIT_TIME, EDIT_USER_ID, DESCRIPTION, COMPANY_ID,ROLE_CATE
  </sql>

  <!-- 序列 -->
  <sql id='TABLE_SEQUENCE'>SEQ_ROLE.NEXTVAL</sql>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from ROLE
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from ROLE
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.Role" >
    <selectKey keyProperty="id" resultType="int" order="BEFORE">
      select <include refid="TABLE_SEQUENCE" /> from dual
    </selectKey>
    insert into ROLE (ID, CODE, NAME,
      CREATE_TIME, EDIT_TIME, EDIT_USER_ID, 
      DESCRIPTION, COMPANY_ID,ROLE_CATE)
    values (#{id,jdbcType=DECIMAL}, #{code,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{editTime,jdbcType=TIMESTAMP}, #{editUserId,jdbcType=DECIMAL}, 
      #{description,jdbcType=VARCHAR}, #{companyId,jdbcType=VARCHAR}, #{roleCate,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.Role" >
    <selectKey keyProperty="id" resultType="int" order="BEFORE">
      select <include refid="TABLE_SEQUENCE" /> from dual
    </selectKey>
    insert into ROLE
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="code != null" >
        CODE,
      </if>
      <if test="name != null" >
        NAME,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
      <if test="editTime != null" >
        EDIT_TIME,
      </if>
      <if test="editUserId != null" >
        EDIT_USER_ID,
      </if>
      <if test="description != null" >
        DESCRIPTION,
      </if>
      <if test="companyId != null" >
        COMPANY_ID,
      </if>
      <if test="roleCate != null" >
        ROLE_CATE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="code != null" >
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="name != null" >
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="editTime != null" >
        #{editTime,jdbcType=TIMESTAMP},
      </if>
      <if test="editUserId != null" >
        #{editUserId,jdbcType=DECIMAL},
      </if>
      <if test="description != null" >
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null" >
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="roleCate != null" >
        #{roleCate,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.Role" >
    update ROLE
    <set >
      <if test="code != null" >
        CODE = #{code,jdbcType=VARCHAR},
      </if>
      <if test="name != null" >
        NAME = #{name,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="editTime != null" >
        EDIT_TIME = #{editTime,jdbcType=TIMESTAMP},
      </if>
      <if test="editUserId != null" >
        EDIT_USER_ID = #{editUserId,jdbcType=DECIMAL},
      </if>
      <if test="description != null" >
        DESCRIPTION = #{description,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null" >
        COMPANY_ID = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="roleCate != null" >
        ROLE_CATE = #{roleCate,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.Role" >
    update ROLE
    set CODE = #{code,jdbcType=VARCHAR},
      NAME = #{name,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      EDIT_TIME = #{editTime,jdbcType=TIMESTAMP},
      EDIT_USER_ID = #{editUserId,jdbcType=DECIMAL},
      DESCRIPTION = #{description,jdbcType=VARCHAR},
      COMPANY_ID = #{companyId,jdbcType=DECIMAL},
      ROLE_CATE = #{roleCate,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>

  <select id="getRoleListByUserID" resultMap="BaseResultMap" parameterType="java.lang.Integer">
    SELECT * FROM ROLE
    where ID in (
    SELECT ROLE_ID from USERS_ROLE WHERE USER_ID = #{user_id}
    )
    ORDER BY ID
  </select>
  <select id="selectRoleList" resultType="java.util.HashMap" parameterType="com.sinoair.billing.domain.vo.FilterParam">
    select r.id as id, r.code as code,r.name as name,r.create_time as create_time,
    r.edit_time as edit_time,u.realname as realname,r.description as description ,r.company_id as company_id,r.role_cate as role_cate
    from ROLE r left join USERS u on u.id = r.edit_user_id
    where 1=1
    <if test="code != null and code != ''">
      and  UPPER(r.code) like '%'||UPPER(#{code})||'%'
    </if>
    <if test="name != null and name != ''">
      and  UPPER(r.name) like '%'||UPPER(#{name})||'%'
    </if>
    <if test=" id != 0">
      and r.id = #{id}
    </if>
  </select>
  <select id="selectRolesByCompanyId" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from ROLE
    where  COMPANY_ID in
    <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>
  <select id="selectListAll" resultMap="BaseResultMap">
    select * from ROLE order by id
  </select>

  <select id="selectRoleListByCompanyID" parameterType="com.sinoair.billing.domain.vo.FilterParam" resultType="com.sinoair.billing.domain.model.billing.Role" >
     select * from ROLE WHERE COMPANY_ID=#{companyId} AND ID not IN (SELECT ROLE_ID FROM USERS_ROLE WHERE USER_ID=#{id})
  </select>
</mapper>