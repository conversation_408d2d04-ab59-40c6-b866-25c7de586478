<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.RoleMenuMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.RoleMenuKey" >
    <id column="ROLE_ID" property="roleId" jdbcType="DECIMAL" />
    <id column="MENU_ID" property="menuId" jdbcType="DECIMAL" />
  </resultMap>
  <delete id="deleteByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.RoleMenuKey" >
    delete from ROLE_MENU
    where ROLE_ID = #{roleId,jdbcType=DECIMAL}
      and MENU_ID = #{menuId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.RoleMenuKey" >
    insert into ROLE_MENU (ROLE_ID, MENU_ID)
    values (#{roleId,jdbcType=DECIMAL}, #{menuId,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.RoleMenuKey" >
    insert into ROLE_MENU
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="roleId != null" >
        ROLE_ID,
      </if>
      <if test="menuId != null" >
        MENU_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="roleId != null" >
        #{roleId,jdbcType=DECIMAL},
      </if>
      <if test="menuId != null" >
        #{menuId,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>

  <select id="selectRoleMenuIdSet" resultType="java.lang.Integer" parameterType="java.lang.Integer">
    select rm.MENU_ID from ROLE_MENU rm where rm.ROLE_ID = #{roltId}
  </select>

  <delete id="deleteByRoleID" parameterType="java.lang.Integer" >
    delete from ROLE_MENU
    where ROLE_ID = #{roleId,jdbcType=DECIMAL}
  </delete>

  <insert id="insertByList" parameterType="java.util.ArrayList">

    insert into ROLE_MENU (MENU_ID, ROLE_ID)
    <foreach collection="list" item="item" index="index" separator="UNION" >
      (select #{item.menuId},#{item.roleId} from dual)
    </foreach>

  </insert>
</mapper>