<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.billing.dao.billing.SerialNOMapper">
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.SerialNO">
    <id column="SN_ID" jdbcType="DECIMAL" property="snId" />
    <result column="SN_NAME" jdbcType="VARCHAR" property="snName" />
    <result column="PROJECT_ID" jdbcType="DECIMAL" property="projectId" />
    <result column="COMPANY_ID" jdbcType="DECIMAL" property="companyId" />
    <result column="SN_PURPOSE" jdbcType="VARCHAR" property="snPurpose" />
    <result column="SN_BEGIN_NO" jdbcType="DECIMAL" property="snBeginNo" />
    <result column="SN_CURRENT_NO" jdbcType="DECIMAL" property="snCurrentNo" />
    <result column="SN_END_NO" jdbcType="DECIMAL" property="snEndNo" />
    <result column="SN_ALERT_NO" jdbcType="DECIMAL" property="snAlertNo" />
    <result column="SN_TYPE" jdbcType="VARCHAR" property="snType" />
    <result column="SN_VALID" jdbcType="VARCHAR" property="snValid" />
    <result column="SN_DATE" jdbcType="TIMESTAMP" property="snDate" />
    <result column="SN_COMMENTS" jdbcType="VARCHAR" property="snComments" />
  </resultMap>
  <sql id="Base_Column_List">
    SN_ID, SN_NAME, PROJECT_ID, COMPANY_ID, SN_PURPOSE, SN_BEGIN_NO, SN_CURRENT_NO, SN_END_NO, 
    SN_ALERT_NO, SN_TYPE, SN_VALID, SN_DATE, SN_COMMENTS
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from SERIALNO
    where SN_ID = #{snId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from SERIALNO
    where SN_ID = #{snId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.SerialNO">
    insert into SERIALNO (SN_ID, SN_NAME, PROJECT_ID, 
      COMPANY_ID, SN_PURPOSE, SN_BEGIN_NO, 
      SN_CURRENT_NO, SN_END_NO, SN_ALERT_NO, 
      SN_TYPE, SN_VALID, SN_DATE, 
      SN_COMMENTS)
    values (#{snId,jdbcType=DECIMAL}, #{snName,jdbcType=VARCHAR}, #{projectId,jdbcType=DECIMAL}, 
      #{companyId,jdbcType=DECIMAL}, #{snPurpose,jdbcType=VARCHAR}, #{snBeginNo,jdbcType=DECIMAL}, 
      #{snCurrentNo,jdbcType=DECIMAL}, #{snEndNo,jdbcType=DECIMAL}, #{snAlertNo,jdbcType=DECIMAL}, 
      #{snType,jdbcType=VARCHAR}, #{snValid,jdbcType=VARCHAR}, #{snDate,jdbcType=TIMESTAMP}, 
      #{snComments,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.SerialNO">
    insert into SERIALNO
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="snId != null">
        SN_ID,
      </if>
      <if test="snName != null">
        SN_NAME,
      </if>
      <if test="projectId != null">
        PROJECT_ID,
      </if>
      <if test="companyId != null">
        COMPANY_ID,
      </if>
      <if test="snPurpose != null">
        SN_PURPOSE,
      </if>
      <if test="snBeginNo != null">
        SN_BEGIN_NO,
      </if>
      <if test="snCurrentNo != null">
        SN_CURRENT_NO,
      </if>
      <if test="snEndNo != null">
        SN_END_NO,
      </if>
      <if test="snAlertNo != null">
        SN_ALERT_NO,
      </if>
      <if test="snType != null">
        SN_TYPE,
      </if>
      <if test="snValid != null">
        SN_VALID,
      </if>
      <if test="snDate != null">
        SN_DATE,
      </if>
      <if test="snComments != null">
        SN_COMMENTS,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="snId != null">
        #{snId,jdbcType=DECIMAL},
      </if>
      <if test="snName != null">
        #{snName,jdbcType=VARCHAR},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=DECIMAL},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=DECIMAL},
      </if>
      <if test="snPurpose != null">
        #{snPurpose,jdbcType=VARCHAR},
      </if>
      <if test="snBeginNo != null">
        #{snBeginNo,jdbcType=DECIMAL},
      </if>
      <if test="snCurrentNo != null">
        #{snCurrentNo,jdbcType=DECIMAL},
      </if>
      <if test="snEndNo != null">
        #{snEndNo,jdbcType=DECIMAL},
      </if>
      <if test="snAlertNo != null">
        #{snAlertNo,jdbcType=DECIMAL},
      </if>
      <if test="snType != null">
        #{snType,jdbcType=VARCHAR},
      </if>
      <if test="snValid != null">
        #{snValid,jdbcType=VARCHAR},
      </if>
      <if test="snDate != null">
        #{snDate,jdbcType=TIMESTAMP},
      </if>
      <if test="snComments != null">
        #{snComments,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.SerialNO">
    update SERIALNO
    <set>
      <if test="snName != null">
        SN_NAME = #{snName,jdbcType=VARCHAR},
      </if>
      <if test="projectId != null">
        PROJECT_ID = #{projectId,jdbcType=DECIMAL},
      </if>
      <if test="companyId != null">
        COMPANY_ID = #{companyId,jdbcType=DECIMAL},
      </if>
      <if test="snPurpose != null">
        SN_PURPOSE = #{snPurpose,jdbcType=VARCHAR},
      </if>
      <if test="snBeginNo != null">
        SN_BEGIN_NO = #{snBeginNo,jdbcType=DECIMAL},
      </if>
      <if test="snCurrentNo != null">
        SN_CURRENT_NO = #{snCurrentNo,jdbcType=DECIMAL},
      </if>
      <if test="snEndNo != null">
        SN_END_NO = #{snEndNo,jdbcType=DECIMAL},
      </if>
      <if test="snAlertNo != null">
        SN_ALERT_NO = #{snAlertNo,jdbcType=DECIMAL},
      </if>
      <if test="snType != null">
        SN_TYPE = #{snType,jdbcType=VARCHAR},
      </if>
      <if test="snValid != null">
        SN_VALID = #{snValid,jdbcType=VARCHAR},
      </if>
      <if test="snDate != null">
        SN_DATE = #{snDate,jdbcType=TIMESTAMP},
      </if>
      <if test="snComments != null">
        SN_COMMENTS = #{snComments,jdbcType=VARCHAR},
      </if>
    </set>
    where SN_ID = #{snId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.SerialNO">
    update SERIALNO
    set SN_NAME = #{snName,jdbcType=VARCHAR},
      PROJECT_ID = #{projectId,jdbcType=DECIMAL},
      COMPANY_ID = #{companyId,jdbcType=DECIMAL},
      SN_PURPOSE = #{snPurpose,jdbcType=VARCHAR},
      SN_BEGIN_NO = #{snBeginNo,jdbcType=DECIMAL},
      SN_CURRENT_NO = #{snCurrentNo,jdbcType=DECIMAL},
      SN_END_NO = #{snEndNo,jdbcType=DECIMAL},
      SN_ALERT_NO = #{snAlertNo,jdbcType=DECIMAL},
      SN_TYPE = #{snType,jdbcType=VARCHAR},
      SN_VALID = #{snValid,jdbcType=VARCHAR},
      SN_DATE = #{snDate,jdbcType=TIMESTAMP},
      SN_COMMENTS = #{snComments,jdbcType=VARCHAR}
    where SN_ID = #{snId,jdbcType=DECIMAL}
  </update>
  <select id="select" resultMap="BaseResultMap" parameterType="com.sinoair.billing.domain.model.billing.SerialNO">
    select * from SERIALNO
    where 1=1
    <if test="snName != null">
      AND SN_NAME=#{snName,jdbcType=VARCHAR}
    </if>
    <if test="projectId != null">AND
      PROJECT_ID= #{projectId,jdbcType=VARCHAR}
    </if>
    <if test="companyId != null">AND
      COMPANY_ID= #{companyId,jdbcType=VARCHAR}
    </if>
    <if test="snPurpose != null">AND
      SN_PURPOSE= #{snPurpose,jdbcType=VARCHAR}
    </if>
    <if test="snBeginNo != null">AND
      SN_BEGIN_NO= #{snBeginNo,jdbcType=DECIMAL}
    </if>
    <if test="snCurrentNo != null">AND
      SN_CURRENT_NO= #{snCurrentNo,jdbcType=DECIMAL}
    </if>
    <if test="snEndNo != null">AND
      SN_END_NO= #{snEndNo,jdbcType=DECIMAL}
    </if>
    <if test="snAlertNo != null">AND
      SN_ALERT_NO= #{snAlertNo,jdbcType=DECIMAL}
    </if>
    <if test="snType != null">AND
      SN_TYPE=#{snType,jdbcType=VARCHAR}
    </if>
    <if test="snValid != null">AND
      SN_VALID= #{snValid,jdbcType=VARCHAR}
    </if>
    <if test="snDate != null">AND
      SN_DATE= #{snDate,jdbcType=VARCHAR}
    </if>
    <if test="snComments != null">AND
      SN_COMMENTS= #{snComments,jdbcType=VARCHAR}
    </if>
  </select>
</mapper>