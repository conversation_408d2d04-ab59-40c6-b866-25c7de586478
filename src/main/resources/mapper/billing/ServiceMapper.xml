<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.ServiceMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.Service" >
    <id column="S_ID" property="sId" jdbcType="DECIMAL" />
    <result column="S_NAME" property="sName" jdbcType="VARCHAR" />
    <result column="TRANSMODE_ID" property="transmodeId" jdbcType="VARCHAR" />
    <result column="SO_CODE" property="soCode" jdbcType="VARCHAR" />
    <result column="EAD_CODE" property="eadCode" jdbcType="VARCHAR" />
    <result column="EAST_CODE" property="eastCode" jdbcType="VARCHAR" />
    <result column="S_DESC" property="sDesc" jdbcType="VARCHAR" />
    <result column="S_AWB_TYPE" property="sAwbType" jdbcType="VARCHAR" />
    <result column="S_STATUS" property="sStatus" jdbcType="VARCHAR" />
    <result column="S_HANDLETIME" property="sHandletime" jdbcType="TIMESTAMP" />
    <result column="S_USER_ID" property="sUserId" jdbcType="DECIMAL" />
  </resultMap>
  <sql id="Base_Column_List" >
    S_ID, S_NAME, TRANSMODE_ID, SO_CODE, EAD_CODE, EAST_CODE, S_DESC, S_AWB_TYPE, S_STATUS,
    S_HANDLETIME, S_USER_ID
  </sql>
  <!-- 序列 -->
  <sql id='TABLE_SEQUENCE'>SEQ_SERVICE.NEXTVAL</sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select
    <include refid="Base_Column_List" />
    from SERVICE
    where S_ID = #{sId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from SERVICE
    where S_ID = #{sId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.Service" >
    <selectKey keyProperty="sId" resultType="int" order="BEFORE">
      select <include refid="TABLE_SEQUENCE" /> from dual
    </selectKey>
    insert into SERVICE (S_ID, S_NAME, TRANSMODE_ID,
    SO_CODE, EAD_CODE, EAST_CODE,
    S_DESC, S_AWB_TYPE, S_STATUS,
    S_HANDLETIME, S_USER_ID)
    values (#{sId,jdbcType=DECIMAL}, #{sName,jdbcType=VARCHAR}, #{transmodeId,jdbcType=VARCHAR},
    #{soCode,jdbcType=VARCHAR}, #{eadCode,jdbcType=VARCHAR}, #{eastCode,jdbcType=VARCHAR},
    #{sDesc,jdbcType=VARCHAR}, #{sAwbType,jdbcType=VARCHAR}, #{sStatus,jdbcType=VARCHAR},
    #{sHandletime,jdbcType=TIMESTAMP}, #{sUserId,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.Service" >
    <selectKey keyProperty="sId" resultType="int" order="BEFORE">
      select <include refid="TABLE_SEQUENCE" /> from dual
    </selectKey>
    insert into SERVICE
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="sId != null" >
        S_ID,
      </if>
      <if test="sName != null" >
        S_NAME,
      </if>
      <if test="transmodeId != null" >
        TRANSMODE_ID,
      </if>
      <if test="soCode != null" >
        SO_CODE,
      </if>
      <if test="eadCode != null" >
        EAD_CODE,
      </if>
      <if test="eastCode != null" >
        EAST_CODE,
      </if>
      <if test="sDesc != null" >
        S_DESC,
      </if>
      <if test="sAwbType != null" >
        S_AWB_TYPE,
      </if>
      <if test="sStatus != null" >
        S_STATUS,
      </if>
      <if test="sHandletime != null" >
        S_HANDLETIME,
      </if>
      <if test="sUserId != null" >
        S_USER_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="sId != null" >
        #{sId,jdbcType=DECIMAL},
      </if>
      <if test="sName != null" >
        #{sName,jdbcType=VARCHAR},
      </if>
      <if test="transmodeId != null" >
        #{transmodeId,jdbcType=VARCHAR},
      </if>
      <if test="soCode != null" >
        #{soCode,jdbcType=VARCHAR},
      </if>
      <if test="eadCode != null" >
        #{eadCode,jdbcType=VARCHAR},
      </if>
      <if test="eastCode != null" >
        #{eastCode,jdbcType=VARCHAR},
      </if>
      <if test="sDesc != null" >
        #{sDesc,jdbcType=VARCHAR},
      </if>
      <if test="sAwbType != null" >
        #{sAwbType,jdbcType=VARCHAR},
      </if>
      <if test="sStatus != null" >
        #{sStatus,jdbcType=VARCHAR},
      </if>
      <if test="sHandletime != null" >
        #{sHandletime,jdbcType=TIMESTAMP},
      </if>
      <if test="sUserId != null" >
        #{sUserId,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.Service" >
    update SERVICE
    <set >
      <if test="sName != null" >
        S_NAME = #{sName,jdbcType=VARCHAR},
      </if>
      <if test="transmodeId != null" >
        TRANSMODE_ID = #{transmodeId,jdbcType=VARCHAR},
      </if>
      <if test="soCode != null" >
        SO_CODE = #{soCode,jdbcType=VARCHAR},
      </if>
      <if test="eadCode != null" >
        EAD_CODE = #{eadCode,jdbcType=VARCHAR},
      </if>
      <if test="eastCode != null" >
        EAST_CODE = #{eastCode,jdbcType=VARCHAR},
      </if>
      <if test="sDesc != null" >
        S_DESC = #{sDesc,jdbcType=VARCHAR},
      </if>
      <if test="sAwbType != null" >
        S_AWB_TYPE = #{sAwbType,jdbcType=VARCHAR},
      </if>
      <if test="sStatus != null" >
        S_STATUS = #{sStatus,jdbcType=VARCHAR},
      </if>
      <if test="sHandletime != null" >
        S_HANDLETIME = #{sHandletime,jdbcType=TIMESTAMP},
      </if>
      <if test="sUserId != null" >
        S_USER_ID = #{sUserId,jdbcType=DECIMAL},
      </if>
    </set>
    where S_ID = #{sId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.Service" >
    update SERVICE
    set S_NAME = #{sName,jdbcType=VARCHAR},
    TRANSMODE_ID = #{transmodeId,jdbcType=VARCHAR},
    SO_CODE = #{soCode,jdbcType=VARCHAR},
    EAD_CODE = #{eadCode,jdbcType=VARCHAR},
    EAST_CODE = #{eastCode,jdbcType=VARCHAR},
    S_DESC = #{sDesc,jdbcType=VARCHAR},
    S_AWB_TYPE = #{sAwbType,jdbcType=VARCHAR},
    S_STATUS = #{sStatus,jdbcType=VARCHAR},
    S_HANDLETIME = #{sHandletime,jdbcType=TIMESTAMP},
    S_USER_ID = #{sUserId,jdbcType=DECIMAL}
    where S_ID = #{sId,jdbcType=DECIMAL}
  </update>
  <select id="selectServiceList" resultType="com.sinoair.billing.domain.model.billing.Service">
    select * from SERVICE WHERE s_status='ON'and so_code=#{soCode} AND S_AWB_TYPE='M'
  </select>

  <select id="selectService" resultType="java.util.Map" parameterType="java.lang.String">
      select DISTINCT
        s.s_id as s_id,
        s.s_name as s_name,
        etd.TRANSMODENAME as transmode_name,
        st.so_name as so_name,
        ead.ead_name as ead_name,
         east.east_name as east_name,
        sp.sp_name as sp_name,
        s.S_DESC as s_desc,
        CASE s.S_AWB_TYPE
            when 'H' THEN '分单'
            when 'M' THEN '主单'
        end  as S_AWB_TYPE,
        s.S_STATUS as S_STATUS
    from SERVICE s
      LEFT  join EXPRESSTRANSMODEDEFINE etd on etd.TRANSMODEID = s.TRANSMODE_ID
      LEFT  JOIN SETTLEMENTOBJECT st on st.so_code = s.so_code
      left join EXPRESSACTIVITYDEFINE ead on ead.ead_code = s. ead_code
      left JOIN  EXPRESSACTIVITYSTATUSTYPE east on east.east_code = s.east_code
      left join supplier sp on sp.sp_code = s.so_code
      where 1=1
      and s.S_STATUS = 'ON'
    <if test="company_id != null and company_id != 'SNR'">
      and sp.COMPANY_ID  = #{company_id}
    </if>
    <if test="s_name != null">
      and
      UPPER(s.S_NAME) like '%'||UPPER(#{s_name})||'%'
    </if>
      <if test="so_code != null and so_code != '-1'">
          and s.so_code = #{so_code}
      </if>
      order by s.S_ID desc
  </select>

  <select id="selectServiceCount" resultType="java.lang.Integer">
    select count(0) from SERVICE
    where s_name = #{s_name}
    and so_code = #{so_code}
    <if test="s_id != null">
      and s_id != #{s_id}
    </if>
  </select>

  <select id="selectAllON" resultMap="BaseResultMap">
    select * from SERVICE s where s.S_STATUS = 'ON' order by S_ID desc
  </select>

    <select id="selectBySoCode" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT * from SERVICE s where s.S_STATUS = 'ON' and s.so_code = #{so_code}  order by S_ID desc
    </select>


  <select id="selectServiceBySelected" resultType="java.util.Map">
    select s.S_ID , s.S_NAME,sp.sp_name from PRODUCT_SERVICE ps LEFT JOIN SERVICE s on ps.S_ID = s.S_ID left join SUPPLIER sp on s.so_code = sp.sp_code
    where ps.p_id = #{p_id}
  </select>
</mapper>