<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.SettlementObjectBalanceMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.SettlementObjectBalance" >
    <id column="SB_ID" property="sbId" jdbcType="DECIMAL" />
    <result column="SO_CODE" property="soCode" jdbcType="VARCHAR" />
    <result column="SB_TYPE" property="sbType" jdbcType="VARCHAR" />
    <result column="SB_AMOUNT" property="sbAmount" jdbcType="DECIMAL" />
    <result column="AMOUNT_TYPE" property="amountType" jdbcType="VARCHAR" />
    <result column="EAWB_PRINTCODE" property="eawbPrintcode" jdbcType="VARCHAR" />
    <result column="EAWB_CHARGEABLEWEIGHT" property="eawbChargeableweight" jdbcType="DECIMAL" />
    <result column="EAWB_REFERENCE1" property="eawbReference1" jdbcType="VARCHAR" />
    <result column="EAWB_REFERENCE2" property="eawbReference2" jdbcType="VARCHAR" />
    <result column="EAWB_REFERENCE3" property="eawbReference3" jdbcType="VARCHAR" />
    <result column="APPROVER" property="approver" jdbcType="VARCHAR" />
    <result column="SB_FLOWNO" property="sbFlowno" jdbcType="VARCHAR" />
    <result column="SB_EXPLAIN" property="sbExplain" jdbcType="VARCHAR" />
    <result column="SB_REMARK" property="sbRemark" jdbcType="VARCHAR" />
    <result column="RR_OCCURTIME" property="rrOccurtime" jdbcType="TIMESTAMP" />
    <result column="SB_HANDLETIME" property="sbHandletime" jdbcType="TIMESTAMP" />
    <result column="SB_HANDLER" property="sbHandler" jdbcType="DECIMAL" />
    <result column="SO_BALANCE" property="soBalance" jdbcType="DECIMAL" />
    <result column="RR_ID" property="rrId" jdbcType="DECIMAL" />
    <result column="PR_NAME" property="prName" jdbcType="VARCHAR" />
  </resultMap>

  <sql id="Base_Column_List" >
    SB_ID, SO_CODE, SB_TYPE, SB_AMOUNT, AMOUNT_TYPE, EAWB_PRINTCODE, EAWB_CHARGEABLEWEIGHT, 
    EAWB_REFERENCE1, EAWB_REFERENCE2, EAWB_REFERENCE3, APPROVER, SB_FLOWNO, SB_EXPLAIN, 
    SB_REMARK, RR_OCCURTIME, SB_HANDLETIME,SB_HANDLER,SO_BALANCE,RR_ID,PR_NAME
  </sql>

  <!-- 序列 -->
  <sql id='TABLE_SEQUENCE'>SEQ_SETTLEMENTOBJECT.NEXTVAL</sql>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from SETTLEMENTOBJECT_BALANCE
    where SB_ID = #{sbId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from SETTLEMENTOBJECT_BALANCE
    where SB_ID = #{sbId,jdbcType=DECIMAL}
  </delete>

  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.SettlementObjectBalance" >
    <selectKey keyProperty="sbId" resultType="java.lang.Long" order="BEFORE">
      select
      <include refid="TABLE_SEQUENCE"/>
      from dual
    </selectKey>
    insert into SETTLEMENTOBJECT_BALANCE (SB_ID, SO_CODE, SB_TYPE, 
      SB_AMOUNT, AMOUNT_TYPE, EAWB_PRINTCODE, 
      EAWB_CHARGEABLEWEIGHT, EAWB_REFERENCE1, EAWB_REFERENCE2, 
      EAWB_REFERENCE3, APPROVER, SB_FLOWNO, 
      SB_EXPLAIN, SB_REMARK, RR_OCCURTIME,
      SB_HANDLETIME,SB_HANDLER,SO_BALANCE,RR_ID,PR_NAME)
    values (#{sbId,jdbcType=DECIMAL}, #{soCode,jdbcType=VARCHAR}, #{sbType,jdbcType=VARCHAR}, 
      #{sbAmount,jdbcType=DECIMAL}, #{amountType,jdbcType=VARCHAR}, #{eawbPrintcode,jdbcType=VARCHAR}, 
      #{eawbChargeableweight,jdbcType=DECIMAL}, #{eawbReference1,jdbcType=VARCHAR}, #{eawbReference2,jdbcType=VARCHAR}, 
      #{eawbReference3,jdbcType=VARCHAR}, #{approver,jdbcType=VARCHAR}, #{sbFlowno,jdbcType=VARCHAR}, 
      #{sbExplain,jdbcType=VARCHAR}, #{sbRemark,jdbcType=VARCHAR}, #{rrOccurtime,jdbcType=TIMESTAMP}, 
      #{sbHandletime,jdbcType=TIMESTAMP},#{sbHandler,jdbcType=DECIMAL},#{soBalance,jdbcType=DECIMAL},
      #{rrID,jdbcType=DECIMAL},#{prName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.SettlementObjectBalance" >
    <selectKey keyProperty="sbId" resultType="java.lang.Long" order="BEFORE">
      select
      <include refid="TABLE_SEQUENCE"/>
      from dual
    </selectKey>
    insert into SETTLEMENTOBJECT_BALANCE
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="sbId != null" >
        SB_ID,
      </if>
      <if test="soCode != null" >
        SO_CODE,
      </if>
      <if test="sbType != null" >
        SB_TYPE,
      </if>
      <if test="sbAmount != null" >
        SB_AMOUNT,
      </if>
      <if test="amountType != null" >
        AMOUNT_TYPE,
      </if>
      <if test="eawbPrintcode != null" >
        EAWB_PRINTCODE,
      </if>
      <if test="eawbChargeableweight != null" >
        EAWB_CHARGEABLEWEIGHT,
      </if>
      <if test="eawbReference1 != null" >
        EAWB_REFERENCE1,
      </if>
      <if test="eawbReference2 != null" >
        EAWB_REFERENCE2,
      </if>
      <if test="eawbReference3 != null" >
        EAWB_REFERENCE3,
      </if>
      <if test="approver != null" >
        APPROVER,
      </if>
      <if test="sbFlowno != null" >
        SB_FLOWNO,
      </if>
      <if test="sbExplain != null" >
        SB_EXPLAIN,
      </if>
      <if test="sbRemark != null" >
        SB_REMARK,
      </if>
      <if test="rrOccurtime != null" >
        RR_OCCURTIME,
      </if>
      <if test="sbHandletime != null" >
        SB_HANDLETIME,
      </if>
      <if test="sbHandler != null" >
        SB_HANDLER,
      </if>
      <if test="soBalance != null" >
        SO_BALANCE,
      </if>
      <if test="prName != null" >
        PR_NAME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="sbId != null" >
        #{sbId,jdbcType=DECIMAL},
      </if>
      <if test="soCode != null" >
        #{soCode,jdbcType=VARCHAR},
      </if>
      <if test="sbType != null" >
        #{sbType,jdbcType=VARCHAR},
      </if>
      <if test="sbAmount != null" >
        #{sbAmount,jdbcType=DECIMAL},
      </if>
      <if test="amountType != null" >
        #{amountType,jdbcType=VARCHAR},
      </if>
      <if test="eawbPrintcode != null" >
        #{eawbPrintcode,jdbcType=VARCHAR},
      </if>
      <if test="eawbChargeableweight != null" >
        #{eawbChargeableweight,jdbcType=DECIMAL},
      </if>
      <if test="eawbReference1 != null" >
        #{eawbReference1,jdbcType=VARCHAR},
      </if>
      <if test="eawbReference2 != null" >
        #{eawbReference2,jdbcType=VARCHAR},
      </if>
      <if test="eawbReference3 != null" >
        #{eawbReference3,jdbcType=VARCHAR},
      </if>
      <if test="approver != null" >
        #{approver,jdbcType=VARCHAR},
      </if>
      <if test="sbFlowno != null" >
        #{sbFlowno,jdbcType=VARCHAR},
      </if>
      <if test="sbExplain != null" >
        #{sbExplain,jdbcType=VARCHAR},
      </if>
      <if test="sbRemark != null" >
        #{sbRemark,jdbcType=VARCHAR},
      </if>
      <if test="rrOccurtime != null" >
        #{rrOccurtime,jdbcType=TIMESTAMP},
      </if>
      <if test="sbHandletime != null" >
        #{sbHandletime,jdbcType=TIMESTAMP},
      </if>
      <if test="sbHandler != null" >
        #{sbHandler,jdbcType=DECIMAL},
      </if>
      <if test="soBalance != null" >
        #{soBalance,jdbcType=DECIMAL},
      </if>
      <if test="prName != null" >
        #{prName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.SettlementObjectBalance" >
    update SETTLEMENTOBJECT_BALANCE
    <set >
      <if test="soCode != null" >
        SO_CODE = #{soCode,jdbcType=VARCHAR},
      </if>
      <if test="sbType != null" >
        SB_TYPE = #{sbType,jdbcType=VARCHAR},
      </if>
      <if test="sbAmount != null" >
        SB_AMOUNT = #{sbAmount,jdbcType=DECIMAL},
      </if>
      <if test="amountType != null" >
        AMOUNT_TYPE = #{amountType,jdbcType=VARCHAR},
      </if>
      <if test="eawbPrintcode != null" >
        EAWB_PRINTCODE = #{eawbPrintcode,jdbcType=VARCHAR},
      </if>
      <if test="eawbChargeableweight != null" >
        EAWB_CHARGEABLEWEIGHT = #{eawbChargeableweight,jdbcType=DECIMAL},
      </if>
      <if test="eawbReference1 != null" >
        EAWB_REFERENCE1 = #{eawbReference1,jdbcType=VARCHAR},
      </if>
      <if test="eawbReference2 != null" >
        EAWB_REFERENCE2 = #{eawbReference2,jdbcType=VARCHAR},
      </if>
      <if test="eawbReference3 != null" >
        EAWB_REFERENCE3 = #{eawbReference3,jdbcType=VARCHAR},
      </if>
      <if test="approver != null" >
        APPROVER = #{approver,jdbcType=VARCHAR},
      </if>
      <if test="sbFlowno != null" >
        SB_FLOWNO = #{sbFlowno,jdbcType=VARCHAR},
      </if>
      <if test="sbExplain != null" >
        SB_EXPLAIN = #{sbExplain,jdbcType=VARCHAR},
      </if>
      <if test="sbRemark != null" >
        SB_REMARK = #{sbRemark,jdbcType=VARCHAR},
      </if>
      <if test="rrOccurtime != null" >
        RR_OCCURTIME = #{rrOccurtime,jdbcType=TIMESTAMP},
      </if>
      <if test="sbHandletime != null" >
        SB_HANDLETIME = #{sbHandletime,jdbcType=TIMESTAMP},
      </if>
      <if test="sbHandler != null" >
        SB_HANDLER = #{sbHandler,jdbcType=DECIMAL},
      </if>
      <if test="soBalance != null" >
        SO_BALANCE = #{soBalance,jdbcType=DECIMAL},
      </if>
    </set>
    where SB_ID = #{sbId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.SettlementObjectBalance" >
    update SETTLEMENTOBJECT_BALANCE
    set SO_CODE = #{soCode,jdbcType=VARCHAR},
      SB_TYPE = #{sbType,jdbcType=VARCHAR},
      SB_AMOUNT = #{sbAmount,jdbcType=DECIMAL},
      AMOUNT_TYPE = #{amountType,jdbcType=VARCHAR},
      EAWB_PRINTCODE = #{eawbPrintcode,jdbcType=VARCHAR},
      EAWB_CHARGEABLEWEIGHT = #{eawbChargeableweight,jdbcType=DECIMAL},
      EAWB_REFERENCE1 = #{eawbReference1,jdbcType=VARCHAR},
      EAWB_REFERENCE2 = #{eawbReference2,jdbcType=VARCHAR},
      EAWB_REFERENCE3 = #{eawbReference3,jdbcType=VARCHAR},
      APPROVER = #{approver,jdbcType=VARCHAR},
      SB_FLOWNO = #{sbFlowno,jdbcType=VARCHAR},
      SB_EXPLAIN = #{sbExplain,jdbcType=VARCHAR},
      SB_REMARK = #{sbRemark,jdbcType=VARCHAR},
      RR_OCCURTIME = #{rrOccurtime,jdbcType=TIMESTAMP},
      SB_HANDLETIME = #{sbHandletime,jdbcType=TIMESTAMP},
      SB_HANDLER = #{sbHandler,jdbcType=DECIMAL},
      SO_BALANCE = #{soBalance,jdbcType=DECIMAL}
    where SB_ID = #{sbId,jdbcType=DECIMAL}
  </update>

  <select id="selectSeq" resultType="java.lang.Integer">
    select
    <include refid="TABLE_SEQUENCE"/>
    from dual
  </select>
</mapper>