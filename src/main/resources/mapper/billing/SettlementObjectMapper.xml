<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.SettlementObjectMapper">
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.SettlementObject">
    <id column="SO_SYSCODE" property="soSyscode" jdbcType="DECIMAL"/>
    <result column="SO_CODE" property="soCode" jdbcType="VARCHAR"/>
    <result column="SAC_ID" property="sacId" jdbcType="VARCHAR"/>
    <result column="C_CODE" property="cCode" jdbcType="VARCHAR"/>
    <result column="SO_FINANCIALCODE" property="soFinancialcode" jdbcType="VARCHAR"/>
    <result column="SO_BANK" property="soBank" jdbcType="VARCHAR"/>
    <result column="SO_BANKID" property="soBankid" jdbcType="VARCHAR"/>
    <result column="SO_BANKFC" property="soBankfc" jdbcType="VARCHAR"/>
    <result column="SO_BANKIDFC" property="soBankidfc" jdbcType="VARCHAR"/>
    <result column="SO_NAME" property="soName" jdbcType="VARCHAR"/>
    <result column="SO_ENAME" property="soEname" jdbcType="VARCHAR"/>
    <result column="SO_CONTACTMAN" property="soContactman" jdbcType="VARCHAR"/>
    <result column="SO_ADDRESS" property="soAddress" jdbcType="VARCHAR"/>
    <result column="SO_POSTCODE" property="soPostcode" jdbcType="VARCHAR"/>
    <result column="SO_TELEPHONE" property="soTelephone" jdbcType="VARCHAR"/>
    <result column="SO_FAX" property="soFax" jdbcType="VARCHAR"/>
    <result column="SO_EMAIL" property="soEmail" jdbcType="VARCHAR"/>
    <result column="SO_STATUS" property="soStatus" jdbcType="VARCHAR"/>
    <result column="SO_LM_CODE_DEFAULT" property="soLmCodeDefault" jdbcType="VARCHAR"/>
    <result column="SO_SAC_CODE" property="soSacCode" jdbcType="VARCHAR"/>
    <result column="SO_E_ID_HANDLER" property="soEIdHandler" jdbcType="DECIMAL"/>
    <result column="SO_HANDLETIME" property="soHandletime" jdbcType="TIMESTAMP"/>
    <result column="SO_ARCHIVETIME" property="soArchivetime" jdbcType="TIMESTAMP"/>
    <result column="CUST_CODE" property="custCode" jdbcType="VARCHAR"/>
    <result column="CUST_SITE_CODE" property="custSiteCode" jdbcType="VARCHAR"/>
    <result column="VENDOR_CODE" property="vendorCode" jdbcType="VARCHAR"/>
    <result column="VENDOR_SITE_CODE" property="vendorSiteCode" jdbcType="VARCHAR"/>
    <result column="SO_INTERNALMARK" property="soInternalmark" jdbcType="VARCHAR"/>
    <result column="SO_CC" property="soCc" jdbcType="VARCHAR"/>
    <result column="SO_VENDOR_TYPE" property="soVendorType" jdbcType="VARCHAR"/>
    <result column="SO_TYPE" property="soType" jdbcType="VARCHAR"/>
    <result column="CDH_NOTE" property="cdhNote" jdbcType="VARCHAR"/>
    <result column="SO_SHORTNAME" property="soShortname" jdbcType="VARCHAR"/>
    <result column="SO_NAME_ORG" property="soNameOrg" jdbcType="VARCHAR"/>
    <result column="TAX_RATE" property="taxRate" jdbcType="DECIMAL"/>
    <result column="SO_KEY" property="soKey" jdbcType="VARCHAR"/>
    <result column="SO_ERP" property="soErp" jdbcType="VARCHAR"/>
    <result column="SO_BALANCE" property="soBalance" jdbcType="DECIMAL"/>
    <result column="THRESHOLD" property="threshold" jdbcType="DECIMAL"/>
    <result column="APPROVER" property="approver" jdbcType="VARCHAR"/>
    <result column="SO_CATE" property="soCate" jdbcType="VARCHAR"/>
    <result column="SO_MODE" property="soMode" jdbcType="VARCHAR"/>
    <result column="SO_BALANCE_STATUS" property="soBalanceStatus" jdbcType="VARCHAR"/>
    <result column="SO_SMS_SENDTIME" property="soSmsSendtime" jdbcType="VARCHAR"/>
  </resultMap>
  <sql id="Base_Column_List">
    SO_SYSCODE, SO_CODE, SAC_ID, C_CODE, SO_FINANCIALCODE, SO_BANK, SO_BANKID, SO_BANKFC,
    SO_BANKIDFC, SO_NAME, SO_ENAME, SO_CONTACTMAN, SO_ADDRESS, SO_POSTCODE, SO_TELEPHONE,
    SO_FAX, SO_EMAIL, SO_STATUS, SO_LM_CODE_DEFAULT, SO_SAC_CODE, SO_E_ID_HANDLER, SO_HANDLETIME,
    SO_ARCHIVETIME, CUST_CODE, CUST_SITE_CODE, VENDOR_CODE, VENDOR_SITE_CODE, SO_INTERNALMARK,
    SO_CC, SO_VENDOR_TYPE, SO_TYPE, CDH_NOTE, SO_SHORTNAME, SO_NAME_ORG, TAX_RATE,SO_KEY,SO_ERP,SO_BALANCE,THRESHOLD,APPROVER,SO_CATE,
    SO_MODE,SO_BALANCE_STATUS,SO_SMS_SENDTIME
  </sql>
  <!-- 序列 -->
  <sql id='TABLE_SEQUENCE'>SEQ_SETTLEMENTOBJECT.NEXTVAL</sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
    select
    <include refid="Base_Column_List" />
    from SETTLEMENTOBJECT
    where SO_SYSCODE = #{soSyscode,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from SETTLEMENTOBJECT
    where SO_SYSCODE = #{soSyscode,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.SettlementObject">
    insert into SETTLEMENTOBJECT (SO_SYSCODE, SO_CODE, SAC_ID,
    C_CODE, SO_FINANCIALCODE, SO_BANK,
    SO_BANKID, SO_BANKFC, SO_BANKIDFC,
    SO_NAME, SO_ENAME, SO_CONTACTMAN,
    SO_ADDRESS, SO_POSTCODE, SO_TELEPHONE,
    SO_FAX, SO_EMAIL, SO_STATUS,
    SO_LM_CODE_DEFAULT, SO_SAC_CODE, SO_E_ID_HANDLER,
    SO_HANDLETIME, SO_ARCHIVETIME, CUST_CODE,
    CUST_SITE_CODE, VENDOR_CODE, VENDOR_SITE_CODE,
    SO_INTERNALMARK, SO_CC, SO_VENDOR_TYPE,
    SO_TYPE, CDH_NOTE, SO_SHORTNAME,
    SO_NAME_ORG, TAX_RATE,SO_KEY,SO_ERP,SO_BALANCE,THRESHOLD,APPROVER,SO_CATE)
    values (#{soSyscode,jdbcType=DECIMAL}, #{soCode,jdbcType=VARCHAR}, #{sacId,jdbcType=VARCHAR},
    #{cCode,jdbcType=VARCHAR}, #{soFinancialcode,jdbcType=VARCHAR}, #{soBank,jdbcType=VARCHAR},
    #{soBankid,jdbcType=VARCHAR}, #{soBankfc,jdbcType=VARCHAR}, #{soBankidfc,jdbcType=VARCHAR},
    #{soName,jdbcType=VARCHAR}, #{soEname,jdbcType=VARCHAR}, #{soContactman,jdbcType=VARCHAR},
    #{soAddress,jdbcType=VARCHAR}, #{soPostcode,jdbcType=VARCHAR}, #{soTelephone,jdbcType=VARCHAR},
    #{soFax,jdbcType=VARCHAR}, #{soEmail,jdbcType=VARCHAR}, #{soStatus,jdbcType=VARCHAR},
    #{soLmCodeDefault,jdbcType=VARCHAR}, #{soSacCode,jdbcType=VARCHAR}, #{soEIdHandler,jdbcType=DECIMAL},
    #{soHandletime,jdbcType=TIMESTAMP}, #{soArchivetime,jdbcType=TIMESTAMP}, #{custCode,jdbcType=VARCHAR},
    #{custSiteCode,jdbcType=VARCHAR}, #{vendorCode,jdbcType=VARCHAR}, #{vendorSiteCode,jdbcType=VARCHAR},
    #{soInternalmark,jdbcType=VARCHAR}, #{soCc,jdbcType=VARCHAR}, #{soVendorType,jdbcType=VARCHAR},
    #{soType,jdbcType=VARCHAR}, #{cdhNote,jdbcType=VARCHAR}, #{soShortname,jdbcType=VARCHAR},
    #{soNameOrg,jdbcType=VARCHAR}, #{taxRate,jdbcType=DECIMAL},
    #{soKey,jdbcType=VARCHAR}, #{soErp,jdbcType=VARCHAR},#{soBalance,jdbcType=DECIMAL},
    #{threshold,jdbcType=DECIMAL}, #{approver,jdbcType=VARCHAR}, #{soCate,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.SettlementObject">
    insert into SETTLEMENTOBJECT
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="soSyscode != null">
        SO_SYSCODE,
      </if>
      <if test="soCode != null">
        SO_CODE,
      </if>
      <if test="sacId != null">
        SAC_ID,
      </if>
      <if test="cCode != null">
        C_CODE,
      </if>
      <if test="soFinancialcode != null">
        SO_FINANCIALCODE,
      </if>
      <if test="soBank != null">
        SO_BANK,
      </if>
      <if test="soBankid != null">
        SO_BANKID,
      </if>
      <if test="soBankfc != null">
        SO_BANKFC,
      </if>
      <if test="soBankidfc != null">
        SO_BANKIDFC,
      </if>
      <if test="soName != null">
        SO_NAME,
      </if>
      <if test="soEname != null">
        SO_ENAME,
      </if>
      <if test="soContactman != null">
        SO_CONTACTMAN,
      </if>
      <if test="soAddress != null">
        SO_ADDRESS,
      </if>
      <if test="soPostcode != null">
        SO_POSTCODE,
      </if>
      <if test="soTelephone != null">
        SO_TELEPHONE,
      </if>
      <if test="soFax != null">
        SO_FAX,
      </if>
      <if test="soEmail != null">
        SO_EMAIL,
      </if>
      <if test="soStatus != null">
        SO_STATUS,
      </if>
      <if test="soLmCodeDefault != null">
        SO_LM_CODE_DEFAULT,
      </if>
      <if test="soSacCode != null">
        SO_SAC_CODE,
      </if>
      <if test="soEIdHandler != null">
        SO_E_ID_HANDLER,
      </if>
      <if test="soHandletime != null">
        SO_HANDLETIME,
      </if>
      <if test="soArchivetime != null">
        SO_ARCHIVETIME,
      </if>
      <if test="custCode != null">
        CUST_CODE,
      </if>
      <if test="custSiteCode != null">
        CUST_SITE_CODE,
      </if>
      <if test="vendorCode != null">
        VENDOR_CODE,
      </if>
      <if test="vendorSiteCode != null">
        VENDOR_SITE_CODE,
      </if>
      <if test="soInternalmark != null">
        SO_INTERNALMARK,
      </if>
      <if test="soCc != null">
        SO_CC,
      </if>
      <if test="soVendorType != null">
        SO_VENDOR_TYPE,
      </if>
      <if test="soType != null">
        SO_TYPE,
      </if>
      <if test="cdhNote != null">
        CDH_NOTE,
      </if>
      <if test="soShortname != null">
        SO_SHORTNAME,
      </if>
      <if test="soNameOrg != null">
        SO_NAME_ORG,
      </if>
      <if test="taxRate != null">
        TAX_RATE,
      </if>
      <if test="soKey != null">
        SO_KEY,
      </if>
      <if test="soErp != null">
        SO_ERP,
      </if>
      <if test="soBalance != null">
        SO_BALANCE,
      </if>
      <if test="threshold != null">
        THRESHOLD,
      </if>
      <if test="approver != null">
        APPROVER,
      </if>
      <if test="soCate != null">
        SO_CATE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="soSyscode != null">
        #{soSyscode,jdbcType=DECIMAL},
      </if>
      <if test="soCode != null">
        #{soCode,jdbcType=VARCHAR},
      </if>
      <if test="sacId != null">
        #{sacId,jdbcType=VARCHAR},
      </if>
      <if test="cCode != null">
        #{cCode,jdbcType=VARCHAR},
      </if>
      <if test="soFinancialcode != null">
        #{soFinancialcode,jdbcType=VARCHAR},
      </if>
      <if test="soBank != null">
        #{soBank,jdbcType=VARCHAR},
      </if>
      <if test="soBankid != null">
        #{soBankid,jdbcType=VARCHAR},
      </if>
      <if test="soBankfc != null">
        #{soBankfc,jdbcType=VARCHAR},
      </if>
      <if test="soBankidfc != null">
        #{soBankidfc,jdbcType=VARCHAR},
      </if>
      <if test="soName != null">
        #{soName,jdbcType=VARCHAR},
      </if>
      <if test="soEname != null">
        #{soEname,jdbcType=VARCHAR},
      </if>
      <if test="soContactman != null">
        #{soContactman,jdbcType=VARCHAR},
      </if>
      <if test="soAddress != null">
        #{soAddress,jdbcType=VARCHAR},
      </if>
      <if test="soPostcode != null">
        #{soPostcode,jdbcType=VARCHAR},
      </if>
      <if test="soTelephone != null">
        #{soTelephone,jdbcType=VARCHAR},
      </if>
      <if test="soFax != null">
        #{soFax,jdbcType=VARCHAR},
      </if>
      <if test="soEmail != null">
        #{soEmail,jdbcType=VARCHAR},
      </if>
      <if test="soStatus != null">
        #{soStatus,jdbcType=VARCHAR},
      </if>
      <if test="soLmCodeDefault != null">
        #{soLmCodeDefault,jdbcType=VARCHAR},
      </if>
      <if test="soSacCode != null">
        #{soSacCode,jdbcType=VARCHAR},
      </if>
      <if test="soEIdHandler != null">
        #{soEIdHandler,jdbcType=DECIMAL},
      </if>
      <if test="soHandletime != null">
        #{soHandletime,jdbcType=TIMESTAMP},
      </if>
      <if test="soArchivetime != null">
        #{soArchivetime,jdbcType=TIMESTAMP},
      </if>
      <if test="custCode != null">
        #{custCode,jdbcType=VARCHAR},
      </if>
      <if test="custSiteCode != null">
        #{custSiteCode,jdbcType=VARCHAR},
      </if>
      <if test="vendorCode != null">
        #{vendorCode,jdbcType=VARCHAR},
      </if>
      <if test="vendorSiteCode != null">
        #{vendorSiteCode,jdbcType=VARCHAR},
      </if>
      <if test="soInternalmark != null">
        #{soInternalmark,jdbcType=VARCHAR},
      </if>
      <if test="soCc != null">
        #{soCc,jdbcType=VARCHAR},
      </if>
      <if test="soVendorType != null">
        #{soVendorType,jdbcType=VARCHAR},
      </if>
      <if test="soType != null">
        #{soType,jdbcType=VARCHAR},
      </if>
      <if test="cdhNote != null">
        #{cdhNote,jdbcType=VARCHAR},
      </if>
      <if test="soShortname != null">
        #{soShortname,jdbcType=VARCHAR},
      </if>
      <if test="soNameOrg != null">
        #{soNameOrg,jdbcType=VARCHAR},
      </if>
      <if test="taxRate != null">
        #{taxRate,jdbcType=DECIMAL},
      </if>
      <if test="soKey != null">
        #{soKey,jdbcType=VARCHAR},
      </if>
      <if test="soErp != null">
        #{soErp,jdbcType=VARCHAR},
      </if>
      <if test="soBalance != null">
        #{soBalance,jdbcType=DECIMAL},
      </if>
      <if test="threshold != null">
        #{threshold,jdbcType=DECIMAL},
      </if>
      <if test="approver != null">
        #{approver,jdbcType=VARCHAR},
      </if>
      <if test="soCate != null">
        #{soCate,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.SettlementObject">
    update SETTLEMENTOBJECT
    <set>
      <if test="soCode != null">
        SO_CODE = #{soCode,jdbcType=VARCHAR},
      </if>
      <if test="sacId != null">
        SAC_ID = #{sacId,jdbcType=VARCHAR},
      </if>
      <if test="cCode != null">
        C_CODE = #{cCode,jdbcType=VARCHAR},
      </if>
      <if test="soFinancialcode != null">
        SO_FINANCIALCODE = #{soFinancialcode,jdbcType=VARCHAR},
      </if>
      <if test="soBank != null">
        SO_BANK = #{soBank,jdbcType=VARCHAR},
      </if>
      <if test="soBankid != null">
        SO_BANKID = #{soBankid,jdbcType=VARCHAR},
      </if>
      <if test="soBankfc != null">
        SO_BANKFC = #{soBankfc,jdbcType=VARCHAR},
      </if>
      <if test="soBankidfc != null">
        SO_BANKIDFC = #{soBankidfc,jdbcType=VARCHAR},
      </if>
      <if test="soName != null">
        SO_NAME = #{soName,jdbcType=VARCHAR},
      </if>
      <if test="soEname != null">
        SO_ENAME = #{soEname,jdbcType=VARCHAR},
      </if>
      <if test="soContactman != null">
        SO_CONTACTMAN = #{soContactman,jdbcType=VARCHAR},
      </if>
      <if test="soAddress != null">
        SO_ADDRESS = #{soAddress,jdbcType=VARCHAR},
      </if>
      <if test="soPostcode != null">
        SO_POSTCODE = #{soPostcode,jdbcType=VARCHAR},
      </if>
      <if test="soTelephone != null">
        SO_TELEPHONE = #{soTelephone,jdbcType=VARCHAR},
      </if>
      <if test="soFax != null">
        SO_FAX = #{soFax,jdbcType=VARCHAR},
      </if>
      <if test="soEmail != null">
        SO_EMAIL = #{soEmail,jdbcType=VARCHAR},
      </if>
      <if test="soStatus != null">
        SO_STATUS = #{soStatus,jdbcType=VARCHAR},
      </if>
      <if test="soLmCodeDefault != null">
        SO_LM_CODE_DEFAULT = #{soLmCodeDefault,jdbcType=VARCHAR},
      </if>
      <if test="soSacCode != null">
        SO_SAC_CODE = #{soSacCode,jdbcType=VARCHAR},
      </if>
      <if test="soEIdHandler != null">
        SO_E_ID_HANDLER = #{soEIdHandler,jdbcType=DECIMAL},
      </if>
      <if test="soHandletime != null">
        SO_HANDLETIME = #{soHandletime,jdbcType=TIMESTAMP},
      </if>
      <if test="soArchivetime != null">
        SO_ARCHIVETIME = #{soArchivetime,jdbcType=TIMESTAMP},
      </if>
      <if test="custCode != null">
        CUST_CODE = #{custCode,jdbcType=VARCHAR},
      </if>
      <if test="custSiteCode != null">
        CUST_SITE_CODE = #{custSiteCode,jdbcType=VARCHAR},
      </if>
      <if test="vendorCode != null">
        VENDOR_CODE = #{vendorCode,jdbcType=VARCHAR},
      </if>
      <if test="vendorSiteCode != null">
        VENDOR_SITE_CODE = #{vendorSiteCode,jdbcType=VARCHAR},
      </if>
      <if test="soInternalmark != null">
        SO_INTERNALMARK = #{soInternalmark,jdbcType=VARCHAR},
      </if>
      <if test="soCc != null">
        SO_CC = #{soCc,jdbcType=VARCHAR},
      </if>
      <if test="soVendorType != null">
        SO_VENDOR_TYPE = #{soVendorType,jdbcType=VARCHAR},
      </if>
      <if test="soType != null">
        SO_TYPE = #{soType,jdbcType=VARCHAR},
      </if>
      <if test="cdhNote != null">
        CDH_NOTE = #{cdhNote,jdbcType=VARCHAR},
      </if>
      <if test="soShortname != null">
        SO_SHORTNAME = #{soShortname,jdbcType=VARCHAR},
      </if>
      <if test="soNameOrg != null">
        SO_NAME_ORG = #{soNameOrg,jdbcType=VARCHAR},
      </if>
      <if test="taxRate != null">
        TAX_RATE = #{taxRate,jdbcType=DECIMAL},
      </if>
      <if test="soKey != null">
        SO_KEY = #{soKey,jdbcType=VARCHAR},
      </if>
      <if test="soErp != null">
        SO_ERP = #{soErp,jdbcType=VARCHAR},
      </if>
      <if test="soBalance != null">
        SO_BALANCE = #{soBalance,jdbcType=DECIMAL},
      </if>
      <if test="threshold != null">
        THRESHOLD = #{threshold,jdbcType=DECIMAL},
      </if>
      <if test="approver != null">
        APPROVER = #{approver,jdbcType=VARCHAR},
      </if>
      <if test="soCate != null">
        SO_CATE = #{soCate,jdbcType=VARCHAR},
      </if>
    </set>
    where SO_SYSCODE = #{soSyscode,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.SettlementObject">
    update SETTLEMENTOBJECT
    set SO_CODE = #{soCode,jdbcType=VARCHAR},
    SAC_ID = #{sacId,jdbcType=VARCHAR},
    C_CODE = #{cCode,jdbcType=VARCHAR},
    SO_FINANCIALCODE = #{soFinancialcode,jdbcType=VARCHAR},
    SO_BANK = #{soBank,jdbcType=VARCHAR},
    SO_BANKID = #{soBankid,jdbcType=VARCHAR},
    SO_BANKFC = #{soBankfc,jdbcType=VARCHAR},
    SO_BANKIDFC = #{soBankidfc,jdbcType=VARCHAR},
    SO_NAME = #{soName,jdbcType=VARCHAR},
    SO_ENAME = #{soEname,jdbcType=VARCHAR},
    SO_CONTACTMAN = #{soContactman,jdbcType=VARCHAR},
    SO_ADDRESS = #{soAddress,jdbcType=VARCHAR},
    SO_POSTCODE = #{soPostcode,jdbcType=VARCHAR},
    SO_TELEPHONE = #{soTelephone,jdbcType=VARCHAR},
    SO_FAX = #{soFax,jdbcType=VARCHAR},
    SO_EMAIL = #{soEmail,jdbcType=VARCHAR},
    SO_STATUS = #{soStatus,jdbcType=VARCHAR},
    SO_LM_CODE_DEFAULT = #{soLmCodeDefault,jdbcType=VARCHAR},
    SO_SAC_CODE = #{soSacCode,jdbcType=VARCHAR},
    SO_E_ID_HANDLER = #{soEIdHandler,jdbcType=DECIMAL},
    SO_HANDLETIME = #{soHandletime,jdbcType=TIMESTAMP},
    SO_ARCHIVETIME = #{soArchivetime,jdbcType=TIMESTAMP},
    CUST_CODE = #{custCode,jdbcType=VARCHAR},
    CUST_SITE_CODE = #{custSiteCode,jdbcType=VARCHAR},
    VENDOR_CODE = #{vendorCode,jdbcType=VARCHAR},
    VENDOR_SITE_CODE = #{vendorSiteCode,jdbcType=VARCHAR},
    SO_INTERNALMARK = #{soInternalmark,jdbcType=VARCHAR},
    SO_CC = #{soCc,jdbcType=VARCHAR},
    SO_VENDOR_TYPE = #{soVendorType,jdbcType=VARCHAR},
    SO_TYPE = #{soType,jdbcType=VARCHAR},
    CDH_NOTE = #{cdhNote,jdbcType=VARCHAR},
    SO_SHORTNAME = #{soShortname,jdbcType=VARCHAR},
    SO_NAME_ORG = #{soNameOrg,jdbcType=VARCHAR},
    TAX_RATE = #{taxRate,jdbcType=DECIMAL},
    SO_KEY = #{soKey,jdbcType=DECIMAL},
    SO_ERP = #{soErp,jdbcType=DECIMAL},
    SO_BALANCE = #{soBalance,jdbcType=DECIMAL},
    THRESHOLD = #{threshold,jdbcType=DECIMAL},
    APPROVER = #{approver,jdbcType=VARCHAR},
    SO_CATE = #{soCate,jdbcType=VARCHAR}
    where SO_SYSCODE = #{soSyscode,jdbcType=DECIMAL}
  </update>
  <select id="selectList" resultType="java.util.HashMap">
    select so_code as SOCODE,so_name as SONAME,so_type as SOTYPE,SAC_ID as SACID from SETTLEMENTOBJECT
  </select>
  <select id="selectListByCompanyId" resultType="java.util.HashMap">
    select so_code as SOCODE,so_name as SONAME,so_type as SOTYPE,C_CODE as CCODE from SETTLEMENTOBJECT WHERE
    (sac_id=#{companyId} or sac_id='SNR')
    <if test='soCate == "P" '>
      AND (SO_CATE = #{soCate} or SO_CATE IS NULL)
    </if>
  </select>
  <select id="selectInsideByCompanyId" resultType="java.util.HashMap">
    select so_code as SOCODE,so_name as SONAME from SETTLEMENTOBJECT WHERE c_code=#{companyId}
    and (SO_TYPE='内部部门' or SO_TYPE='外运发展' )
  </select>
  <select id="selectCompanyIdBySoCode" resultType="java.util.HashMap">
    select c_code as SACID,so_name as SONAME from SETTLEMENTOBJECT WHERE so_code=#{soCode}
    and (SO_TYPE='内部部门' or SO_TYPE='外运发展' )
  </select>
  <select id="getsoNameBysoCode" parameterType="java.lang.String" resultType="java.lang.String">
     select so_name from SETTLEMENTOBJECT where so_code=#{soCode}
  </select>
  <select id="selectSoTypeBySoCode" parameterType="java.lang.String" resultType="java.lang.String">
     select so_type from SETTLEMENTOBJECT where so_code=#{soCode}
  </select>

  <select id="selectListMapByCompanyId" resultType="java.util.HashMap">
    select so_code as SOCODE,so_name as SONAME,so_type as SOTYPE,C_CODE as CCODE from SETTLEMENTOBJECT WHERE
    sac_id=#{companyId} or sac_id='SNR'
  </select>

  <select id="selectSacIdBySoCode" resultType="java.lang.String">
    select SAC_ID from SETTLEMENTOBJECT where so_code = #{soCode,jdbcType=VARCHAR}
  </select>

  <select id="selectCCodeBySoCode" resultType="java.lang.String">
    select C_CODE from SETTLEMENTOBJECT where so_code = #{soCode,jdbcType=VARCHAR}
  </select>

  <select id="getInfoBySoCode" resultType="java.util.Map">
    select stb.so_syscode as soSyscode,stb.vendor_code as vendorCode,stb.tax_rate as taxRate,stb.cust_code custCode from settlementobject stb where stb.so_code =#{soCode} and stb.so_status = 'ON'
  </select>

  <update id="updateCustCodeBySoCode">
     update settlementobject stb set stb.cust_code=#{custCode} where stb.so_code=#{soCode} and stb.so_status='ON'
  </update>

  <select id="selectQuery" resultType="java.util.HashMap" parameterType="com.sinoair.billing.domain.vo.query.CustomerQuery">
    SELECT
      so.*, c.COMPANY_NAME,e.ERP_NAME,e.ERP_MODE,
      U .realname AS "HANDLER USER"
    FROM
      SETTLEMENTOBJECT so
    LEFT JOIN company c ON c.company_id = so.SO_SAC_CODE
    LEFT JOIN USERS U ON U . ID = so.so_e_id_handler
    LEFT JOIN ERP_DEFINE e ON so.SO_ERP = e.ERP_CODE
    where 1=1
    and so.SO_STATUS = 'ON'
    <if test="query.sacId != 'SNR'">
      AND so.SAC_ID = #{query.sacId}
    </if>
    <if test='query.soCate == "P" '>
      AND (so.SO_CATE = #{query.soCate} or so.SO_CATE IS NULL)
    </if>
    <if test="query.search != null and query.search != ''">
      AND ( UPPER(so.so_code) like '%'||UPPER(#{query.search})||'%'
            or
            UPPER(so.so_name) like '%'||UPPER(#{query.search})||'%'
            or
            UPPER(so.so_ename) like '%'||UPPER(#{query.search})||'%'
          )
    </if>

  </select>

  <select id="selectBySoCode" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List" /> FROM SETTLEMENTOBJECT WHERE SO_CODE = #{soCode} or C_CODE = #{soCode}
  </select>

  <select id="selectSeq" resultType="java.lang.Integer">
      select
        <include refid="TABLE_SEQUENCE"/>
      from dual
  </select>

  <select id="selectBySoKey" resultType="java.lang.Integer">
    SELECT count(0) FROM SETTLEMENTOBJECT WHERE SO_KEY = #{soKey}
  </select>
  <select id="selectByCCODE" resultType="java.lang.Integer">
    SELECT count(0) FROM SETTLEMENTOBJECT WHERE C_CODE = #{cCodeKEY}
  </select>
  <select id="selectBySoName" resultType="java.lang.Integer">
    SELECT count(0) FROM SETTLEMENTOBJECT WHERE SO_NAME = #{soName}
    <if test="soCode != null and soCode != ''">
      and SO_CODE != #{soCode}
    </if>
  </select>

  <select id="getSoProByPrId" resultType="java.util.Map" parameterType="java.lang.String">
      select s.so_code                as soCode,
             s.c_code                 as customer_code,
             s.so_key                 as secret,
             s.so_erp                 as soErp,
             p.p_servicetype_original as product_id,
             ed.erp_mode              as erpMode
        from price_receipt    pr,
             product_customer pc,
             settlementobject s,
             product          p,
             erp_define       ed
       where pr.pc_id = pc.pc_id
         and pc.p_id = p.p_id
         and pc.so_code = s.so_code
         and s.so_erp = ed.erp_code
         and s.so_status = 'ON'
         and s.so_erp is not null
         and pr.pr_id = #{prId}
   </select>

  <select id="getSoProListByPrId" resultType="java.util.Map" parameterType="java.lang.String">
   select distinct s.c_code as cCode,
            p.p_servicetype_original as product_id,
            s.so_code as so_code
      from product_customer pc1, product p,settlementobject s
     where pc1.p_id = p.p_id
       and s.so_code = pc1.so_code
       and pc1.so_code in (select pc.so_code
                             from price_receipt pr, product_customer pc
                            where pr.pc_id = pc.pc_id
                              and pr.pr_id = #{prId})

  </select>
  <!-- and so_erp is not null -->
  <select id="getSoBalanceList" resultType="java.util.Map"  parameterType="java.lang.String">
      select distinct c.so_code                as soCode,
             c.c_code                 as cCode,
             c.so_balance             as soBalance,
             c.threshold              as threshold,
             p.p_servicetype_original as product_id
        from settlementobject c, product_customer pc, product p, price_receipt pr
       where c.so_code = pc.so_code
         and pc.p_id = p.p_id
         and pc.pc_id = pr.pc_id
         and p.p_servicetype_original is not null
         and c.c_code is not null
      <if test="toSys != null and toSys != ''">
           and pr.pr_expireddate > sysdate
      </if>
       order by c.so_code
  </select>

  <select id="selectSettlementList" resultType="com.sinoair.billing.domain.model.billing.SettlementObject"  >
    select so_code,SO_NAME,CUST_CODE,CDH_NOTE,sac_id
    from settlementobject

  </select>

  <select id="selectAutoSoList" resultType="com.sinoair.billing.domain.model.billing.SettlementObject">
    select distinct so.so_code,so.so_mode,so.sac_id,pr.ct_code from price_receipt pr,product_customer pc,settlementobject so
    where pr.pc_id = pc.pc_id
      and pc.so_code = so.so_code
      and pr_auto = 'Y'
      and pr_status = 'ON'
      and bms_status = 'Y'
      and so.so_vendor_type = 'ONLINE' and so.SO_CODE !='ZSU1000001058'
      and pr_effectivedate &lt;  sysdate
      and pr_expireddate > sysdate

  </select>

  <select id="selectOnlineSoList" resultType="com.sinoair.billing.domain.model.billing.SettlementObject">
    select * from settlementobject so
    where
    so.so_vendor_type = 'ONLINE' and so.SO_CODE !='ZSU1000001058'
  </select>

  <select id="selectFlatSoList" resultType="java.lang.String"  >
    select so_code from settlementobject where 1=1
                                           and (
          so_vendor_type='ONLINE'
          and so_code !=  'ZSU168192'
        )
  </select>
<!--  select so_code from settlementobject where sac_id in ('ZSU','ZSH','YNT','WEH')-->
<!--  and so_code !=  'ZSU168192'-->

  <update id="addSoBanlance">
    update SETTLEMENTOBJECT set SO_BALANCE=SO_BALANCE+#{balance} where SO_CODE=#{soCode}
  </update>

  <update id="cutSoBanlance">
    update SETTLEMENTOBJECT set SO_BALANCE=SO_BALANCE-#{balance} where SO_BALANCE>=#{balance} and SO_CODE=#{soCode}
                                                                   and SO_BALANCE_STATUS != 'OFF'
  </update>
</mapper>