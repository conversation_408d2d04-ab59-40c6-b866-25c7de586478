<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.SettlementObjectPriceMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.SettlementObjectPrice" >
    <id column="SOP_ID" property="sopId" jdbcType="DECIMAL" />
    <result column="SO_CODE" property="soCode" jdbcType="VARCHAR" />
    <result column="PD_SYSCODE" property="pdSyscode" jdbcType="DECIMAL" />
    <result column="SOP_HANDLETIME" property="sopHandletime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    SOP_ID, SO_CODE, PD_SYSCODE, SOP_HANDLETIME
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from SETTLEMENTOBJECT_PRICE
    where SOP_ID = #{sopId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from SETTLEMENTOBJECT_PRICE
    where SOP_ID = #{sopId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.SettlementObjectPrice" >
    insert into SETTLEMENTOBJECT_PRICE (SOP_ID, SO_CODE, PD_SYSCODE, 
      SOP_HANDLETIME)
    values (#{sopId,jdbcType=DECIMAL}, #{soCode,jdbcType=VARCHAR}, #{pdSyscode,jdbcType=DECIMAL}, 
      #{sopHandletime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.SettlementObjectPrice" >
    insert into SETTLEMENTOBJECT_PRICE
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="sopId != null" >
        SOP_ID,
      </if>
      <if test="soCode != null" >
        SO_CODE,
      </if>
      <if test="pdSyscode != null" >
        PD_SYSCODE,
      </if>
      <if test="sopHandletime != null" >
        SOP_HANDLETIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="sopId != null" >
        #{sopId,jdbcType=DECIMAL},
      </if>
      <if test="soCode != null" >
        #{soCode,jdbcType=VARCHAR},
      </if>
      <if test="pdSyscode != null" >
        #{pdSyscode,jdbcType=DECIMAL},
      </if>
      <if test="sopHandletime != null" >
        #{sopHandletime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.SettlementObjectPrice" >
    update SETTLEMENTOBJECT_PRICE
    <set >
      <if test="soCode != null" >
        SO_CODE = #{soCode,jdbcType=VARCHAR},
      </if>
      <if test="pdSyscode != null" >
        PD_SYSCODE = #{pdSyscode,jdbcType=DECIMAL},
      </if>
      <if test="sopHandletime != null" >
        SOP_HANDLETIME = #{sopHandletime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where SOP_ID = #{sopId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.SettlementObjectPrice" >
    update SETTLEMENTOBJECT_PRICE
    set SO_CODE = #{soCode,jdbcType=VARCHAR},
      PD_SYSCODE = #{pdSyscode,jdbcType=DECIMAL},
      SOP_HANDLETIME = #{sopHandletime,jdbcType=TIMESTAMP}
    where SOP_ID = #{sopId,jdbcType=DECIMAL}
  </update>

  <select id="selectPriceBySoCode" resultType="java.util.Map" parameterType="java.lang.String" >
    select
      sop.pd_syscode as pdSysCode,
      pd.pd_name as priceName
    from settlementobject_price sop,price_define pd
    where sop.pd_syscode = pd.pd_syscode
     and sop.so_code = #{soCode}
  </select>
</mapper>