<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.SettlementRecordMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.SettlementRecord" >
    <id column="REFERENCE1" property="reference1" jdbcType="VARCHAR" />
    <result column="CT_CODE" property="ctCode" jdbcType="VARCHAR" />
    <result column="SR_AMOUNT" property="srAmount" jdbcType="DECIMAL" />
    <result column="SR_HANDLETIME" property="srHandletime" jdbcType="TIMESTAMP" />
    <result column="PULL_STATE" property="pullState" jdbcType="VARCHAR" />
    <result column="PULL_TIME" property="pullTime" jdbcType="TIMESTAMP" />
    <result column="SR_REMARK" property="srRemark" jdbcType="VARCHAR" />
    <result column="REFERENCE2" property="reference2" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    REFERENCE1, CT_CODE, SR_AMOUNT, SR_HANDLETIME, PULL_STATE, PULL_TIME, SR_REMARK,
    REFERENCE2
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select
    <include refid="Base_Column_List" />
    from SETTLEMENTRECORD
    where REFERENCE1 = #{reference1,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from SETTLEMENTRECORD
    where REFERENCE1 = #{reference1,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.SettlementRecord" >
    insert into SETTLEMENTRECORD (REFERENCE1, CT_CODE, SR_AMOUNT,
    SR_HANDLETIME, PULL_STATE, PULL_TIME,
    SR_REMARK, REFERENCE2)
    values (#{reference1,jdbcType=VARCHAR}, #{ctCode,jdbcType=VARCHAR}, #{srAmount,jdbcType=DECIMAL},
    #{srHandletime,jdbcType=TIMESTAMP}, #{pullState,jdbcType=VARCHAR}, #{pullTime,jdbcType=TIMESTAMP},
    #{srRemark,jdbcType=VARCHAR}, #{reference2,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.SettlementRecord" >
    insert into SETTLEMENTRECORD
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="reference1 != null" >
        REFERENCE1,
      </if>
      <if test="ctCode != null" >
        CT_CODE,
      </if>
      <if test="srAmount != null" >
        SR_AMOUNT,
      </if>
      <if test="srHandletime != null" >
        SR_HANDLETIME,
      </if>
      <if test="pullState != null" >
        PULL_STATE,
      </if>
      <if test="pullTime != null" >
        PULL_TIME,
      </if>
      <if test="srRemark != null" >
        SR_REMARK,
      </if>
      <if test="reference2 != null" >
        REFERENCE2,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="reference1 != null" >
        #{reference1,jdbcType=VARCHAR},
      </if>
      <if test="ctCode != null" >
        #{ctCode,jdbcType=VARCHAR},
      </if>
      <if test="srAmount != null" >
        #{srAmount,jdbcType=DECIMAL},
      </if>
      <if test="srHandletime != null" >
        #{srHandletime,jdbcType=TIMESTAMP},
      </if>
      <if test="pullState != null" >
        #{pullState,jdbcType=VARCHAR},
      </if>
      <if test="pullTime != null" >
        #{pullTime,jdbcType=TIMESTAMP},
      </if>
      <if test="srRemark != null" >
        #{srRemark,jdbcType=VARCHAR},
      </if>
      <if test="reference2 != null" >
        #{reference2,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.SettlementRecord" >
    update SETTLEMENTRECORD
    <set >
      <if test="ctCode != null" >
        CT_CODE = #{ctCode,jdbcType=VARCHAR},
      </if>
      <if test="srAmount != null" >
        SR_AMOUNT = #{srAmount,jdbcType=DECIMAL},
      </if>
      <if test="srHandletime != null" >
        SR_HANDLETIME = #{srHandletime,jdbcType=TIMESTAMP},
      </if>
      <if test="pullState != null" >
        PULL_STATE = #{pullState,jdbcType=VARCHAR},
      </if>
      <if test="pullTime != null" >
        PULL_TIME = #{pullTime,jdbcType=TIMESTAMP},
      </if>
      <if test="srRemark != null" >
        SR_REMARK = #{srRemark,jdbcType=VARCHAR},
      </if>
      <if test="reference2 != null" >
        REFERENCE2 = #{reference2,jdbcType=VARCHAR},
      </if>
    </set>
    where REFERENCE1 = #{reference1,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.SettlementRecord" >
    update SETTLEMENTRECORD
    set CT_CODE = #{ctCode,jdbcType=VARCHAR},
      SR_AMOUNT = #{srAmount,jdbcType=DECIMAL},
      SR_HANDLETIME = #{srHandletime,jdbcType=TIMESTAMP},
      PULL_STATE = #{pullState,jdbcType=VARCHAR},
      PULL_TIME = #{pullTime,jdbcType=TIMESTAMP},
      SR_REMARK = #{srRemark,jdbcType=VARCHAR},
      REFERENCE2 = #{reference2,jdbcType=VARCHAR}
    where REFERENCE1 = #{reference1,jdbcType=VARCHAR}
  </update>

  <select id="countByPrimaryKey" resultType="int" parameterType="java.lang.String" >
    select
    count(*)
    from SETTLEMENTRECORD
    where REFERENCE1 = #{reference1,jdbcType=VARCHAR}
  </select>

  <insert id="insertBatch"  parameterType="java.util.List">
    insert into SETTLEMENTRECORD (REFERENCE1, CT_CODE, SR_AMOUNT,
    SR_HANDLETIME, PULL_STATE, PULL_TIME,
    SR_REMARK,REFERENCE2)
    <foreach collection="list" item="item" index="index" separator="UNION" >
      (select
      #{item.reference1,jdbcType=VARCHAR}, #{item.ctCode,jdbcType=VARCHAR}, #{item.srAmount,jdbcType=DECIMAL},
      #{item.srHandletime,jdbcType=TIMESTAMP}, #{item.pullState,jdbcType=VARCHAR}, #{item.pullTime,jdbcType=TIMESTAMP},
      #{item.srRemark,jdbcType=VARCHAR}, #{item.reference2,jdbcType=VARCHAR}
      from dual)
    </foreach>
  </insert>

  <update id="updateBatch" parameterType="java.util.List">
    <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
      update SETTLEMENTRECORD
      <set>
        <if test="item.ctCode != null and item.ctCode!=''">
          CT_CODE = #{item.ctCode,jdbcType=VARCHAR},
        </if>
        <if test="item.srAmount != null" >
          SR_AMOUNT = #{item.srAmount,jdbcType=DECIMAL},
        </if>
        <if test="item.pullState != null and item.pullState!=''" >
          PULL_STATE = #{item.pullState,jdbcType=VARCHAR},
        </if>
        <if test="item.pullTime != null" >
          PULL_TIME = #{item.pullTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.srRemark != null and item.srRemark!=''" >
          SR_REMARK = #{item.srRemark,jdbcType=VARCHAR},
        </if>
      </set>
      where
        REFERENCE1 = #{item.reference1,jdbcType=VARCHAR}
    </foreach>
  </update>

    <update id="updateBatchByRef2" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
            update SETTLEMENTRECORD
            <set>
                <if test="item.ctCode != null and item.ctCode!=''">
                    CT_CODE = #{item.ctCode,jdbcType=VARCHAR},
                </if>
                <if test="item.srAmount != null" >
                    SR_AMOUNT = #{item.srAmount,jdbcType=DECIMAL},
                </if>
                <if test="item.pullState != null and item.pullState!=''" >
                    PULL_STATE = #{item.pullState,jdbcType=VARCHAR},
                </if>
                <if test="item.pullTime != null" >
                    PULL_TIME = #{item.pullTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.srRemark != null and item.srRemark!=''" >
                    SR_REMARK = #{item.srRemark,jdbcType=VARCHAR},
                </if>
            </set>
            where
            REFERENCE2 = #{item.reference2,jdbcType=VARCHAR}
        </foreach>
    </update>

    <select id="selectRecordList" resultType="com.sinoair.billing.domain.model.billing.SettlementRecord" parameterType="com.sinoair.billing.domain.vo.FilterParam" >
        select
        t.REFERENCE1 as "reference1",
        t.CT_CODE as "ctCode",
        t.SR_AMOUNT as "srAmount",
        t.SR_HANDLETIME as "srHandletime",
        t.PULL_STATE as "pullState",
        t.PULL_TIME as "pullTime",
        t.SR_REMARK as "srRemark",
        t.REFERENCE2 as "reference2",
        ct.ct_name as "ctName"
        from SETTLEMENTRECORD t,currencytype ct
        where 1 = 1
          and t.ct_code = ct.ct_code
        <if test="eawbReference1!=null and eawbReference1!=''">
            and t.REFERENCE1 = #{eawbReference1}
        </if>
        <if test="code!=null and code!=''">
            and t.REFERENCE2 = #{code}
        </if>
        <if test="status!=null and status!=''">
            and t.PULL_STATE = #{status}
        </if>
        <if test="starttime!=null and starttime!='' and endtime!=null and endtime!=''">
            and
            (
            t.SR_HANDLETIME >=
            to_date(#{starttime}, 'yyyy-mm-dd hh24:mi:ss') and
            t.SR_HANDLETIME &lt;=
            to_date(#{endtime}, 'yyyy-mm-dd hh24:mi:ss')
            )
        </if>

        <if test="pullstarttime!=null and pullstarttime!='' and pullendtime!=null and pullendtime!=''">
            and
            (
            t.PULL_TIME >=
            to_date(#{pullstarttime}, 'yyyy-mm-dd hh24:mi:ss') and
            t.PULL_TIME &lt;=
            to_date(#{pullendtime}, 'yyyy-mm-dd hh24:mi:ss')
            )
        </if>
        order by t.SR_HANDLETIME desc
    </select>

  <update id="batchUpdateByRef2" parameterType="java.util.Map">
    UPDATE SETTLEMENTRECORD SET PULL_STATE=#{outStatus},PULL_TIME=#{outTime} WHERE REFERENCE2 in
    <foreach collection="recordList" item="item" index="index"
             open="(" separator="," close=")">
      #{item.reference2}
    </foreach>
  </update>
</mapper>