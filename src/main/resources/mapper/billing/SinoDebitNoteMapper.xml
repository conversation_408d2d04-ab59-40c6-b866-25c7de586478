<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.SinoDebitNoteMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.SinoDebitNote" >
    <id column="SD_ID" property="sdId" jdbcType="DECIMAL" />
    <result column="COMPANY_ID" property="companyId" jdbcType="VARCHAR" />
    <result column="SO_CODE" property="soCode" jdbcType="VARCHAR" />
    <result column="CT_CODE" property="ctCode" jdbcType="VARCHAR" />
    <result column="SD_CURRENCYRATE" property="sdCurrencyrate" jdbcType="DECIMAL" />
    <result column="PD_NAME" property="pdName" jdbcType="VARCHAR" />
    <result column="PD_SYSCODE" property="pdSyscode" jdbcType="DECIMAL" />
    <result column="SD_TOTALRMB" property="sdTotalrmb" jdbcType="DECIMAL" />
    <result column="SD_TOTALFC" property="sdTotalfc" jdbcType="DECIMAL" />
    <result column="SO_TAX" property="soTax" jdbcType="DECIMAL" />
    <result column="TAX_AMOUNT" property="taxAmount" jdbcType="DECIMAL" />
    <result column="NOTAX_AMOUNT" property="notaxAmount" jdbcType="DECIMAL" />
    <result column="TAX_AMOUNT_FC" property="taxAmountFc" jdbcType="DECIMAL" />
    <result column="NOTAX_AMOUNT_FC" property="notaxAmountFc" jdbcType="DECIMAL" />
    <result column="INVOICE_CODE" property="invoiceCode" jdbcType="VARCHAR" />
    <result column="SD_USER_ID" property="sdUserId" jdbcType="DECIMAL" />
    <result column="SD_STATUS" property="sdStatus" jdbcType="VARCHAR" />
    <result column="SD_CREATE_TIME" property="sdCreateTime" jdbcType="TIMESTAMP" />
    <result column="SD_HANDLE_TIME" property="sdHandleTime" jdbcType="TIMESTAMP" />
    <result column="SD_DIRTY" property="sdDirty" jdbcType="VARCHAR" />
    <result column="SD_DIRTY_TIME" property="sdDirtyTime" jdbcType="TIMESTAMP" />
    <result column="SD_CODE" property="sdCode" jdbcType="VARCHAR" />
    <result column="SD_PLAN_AMOUNT" property="sdPlanAmount" jdbcType="DECIMAL" />
    <result column="SD_START_TIME" property="sdStartTime" jdbcType="TIMESTAMP" />
    <result column="SD_END_TIME" property="sdEndTime" jdbcType="TIMESTAMP" />
    <result column="SD_TOTAL_PIECES" property="sdTotalPieces" jdbcType="DECIMAL" />
    <result column="SD_TOTAL_WEIGHT" property="sdTotalWeight" jdbcType="DECIMAL" />
    <result column="SD_REMARK" property="sdRemark" jdbcType="VARCHAR" />
    <result column="SD_TYPE" property="sdType" jdbcType="VARCHAR" />
    <result column="ESTIMATE_STATUS" property="estimateStatus" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    SD_ID, COMPANY_ID, SO_CODE, CT_CODE, SD_CURRENCYRATE, PD_NAME, PD_SYSCODE, SD_TOTALRMB, 
    SD_TOTALFC, SO_TAX, TAX_AMOUNT, NOTAX_AMOUNT, TAX_AMOUNT_FC, NOTAX_AMOUNT_FC, INVOICE_CODE, 
    SD_USER_ID, SD_STATUS, SD_CREATE_TIME, SD_HANDLE_TIME, SD_DIRTY, SD_DIRTY_TIME, SD_CODE, 
    SD_PLAN_AMOUNT, SD_START_TIME, SD_END_TIME, SD_TOTAL_PIECES, SD_TOTAL_WEIGHT, SD_REMARK, 
    SD_TYPE, ESTIMATE_STATUS
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Short" >
    select 
    <include refid="Base_Column_List" />
    from SINO_DEBITNOTE
    where SD_ID = #{sdId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Short" >
    delete from SINO_DEBITNOTE
    where SD_ID = #{sdId,jdbcType=DECIMAL}
  </delete>
  <!-- 序列 -->
  <sql id='TABLE_SEQUENCE'>SEQ_DEBIT_MANIFEST.NEXTVAL</sql>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.SinoDebitNote" >
    <selectKey keyProperty="sdId" resultType="int" order="BEFORE">
      select
      <include refid="TABLE_SEQUENCE"/>
      from dual
    </selectKey>
    insert into SINO_DEBITNOTE (SD_ID, COMPANY_ID, SO_CODE, 
      CT_CODE, SD_CURRENCYRATE, PD_NAME, 
      PD_SYSCODE, SD_TOTALRMB, SD_TOTALFC, 
      SO_TAX, TAX_AMOUNT, NOTAX_AMOUNT, 
      TAX_AMOUNT_FC, NOTAX_AMOUNT_FC, INVOICE_CODE, 
      SD_USER_ID, SD_STATUS, SD_CREATE_TIME, 
      SD_HANDLE_TIME, SD_DIRTY, SD_DIRTY_TIME, 
      SD_CODE, SD_PLAN_AMOUNT, SD_START_TIME, 
      SD_END_TIME, SD_TOTAL_PIECES, SD_TOTAL_WEIGHT, 
      SD_REMARK, SD_TYPE, ESTIMATE_STATUS
      )
    values (#{sdId,jdbcType=DECIMAL}, #{companyId,jdbcType=VARCHAR}, #{soCode,jdbcType=VARCHAR}, 
      #{ctCode,jdbcType=VARCHAR}, #{sdCurrencyrate,jdbcType=DECIMAL}, #{pdName,jdbcType=VARCHAR}, 
      #{pdSyscode,jdbcType=DECIMAL}, #{sdTotalrmb,jdbcType=DECIMAL}, #{sdTotalfc,jdbcType=DECIMAL}, 
      #{soTax,jdbcType=DECIMAL}, #{taxAmount,jdbcType=DECIMAL}, #{notaxAmount,jdbcType=DECIMAL}, 
      #{taxAmountFc,jdbcType=DECIMAL}, #{notaxAmountFc,jdbcType=DECIMAL}, #{invoiceCode,jdbcType=VARCHAR}, 
      #{sdUserId,jdbcType=DECIMAL}, #{sdStatus,jdbcType=VARCHAR}, #{sdCreateTime,jdbcType=TIMESTAMP}, 
      #{sdHandleTime,jdbcType=TIMESTAMP}, #{sdDirty,jdbcType=VARCHAR}, #{sdDirtyTime,jdbcType=TIMESTAMP}, 
      #{sdCode,jdbcType=VARCHAR}, #{sdPlanAmount,jdbcType=DECIMAL}, #{sdStartTime,jdbcType=TIMESTAMP}, 
      #{sdEndTime,jdbcType=TIMESTAMP}, #{sdTotalPieces,jdbcType=DECIMAL}, #{sdTotalWeight,jdbcType=DECIMAL}, 
      #{sdRemark,jdbcType=VARCHAR}, #{sdType,jdbcType=VARCHAR}, #{estimateStatus,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.SinoDebitNote" >
    <selectKey keyProperty="sdId" resultType="int" order="BEFORE">
      select
      <include refid="TABLE_SEQUENCE"/>
      from dual
    </selectKey>
    insert into SINO_DEBITNOTE
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="sdId != null" >
        SD_ID,
      </if>
      <if test="companyId != null" >
        COMPANY_ID,
      </if>
      <if test="soCode != null" >
        SO_CODE,
      </if>
      <if test="ctCode != null" >
        CT_CODE,
      </if>
      <if test="sdCurrencyrate != null" >
        SD_CURRENCYRATE,
      </if>
      <if test="pdName != null" >
        PD_NAME,
      </if>
      <if test="pdSyscode != null" >
        PD_SYSCODE,
      </if>
      <if test="sdTotalrmb != null" >
        SD_TOTALRMB,
      </if>
      <if test="sdTotalfc != null" >
        SD_TOTALFC,
      </if>
      <if test="soTax != null" >
        SO_TAX,
      </if>
      <if test="taxAmount != null" >
        TAX_AMOUNT,
      </if>
      <if test="notaxAmount != null" >
        NOTAX_AMOUNT,
      </if>
      <if test="taxAmountFc != null" >
        TAX_AMOUNT_FC,
      </if>
      <if test="notaxAmountFc != null" >
        NOTAX_AMOUNT_FC,
      </if>
      <if test="invoiceCode != null" >
        INVOICE_CODE,
      </if>
      <if test="sdUserId != null" >
        SD_USER_ID,
      </if>
      <if test="sdStatus != null" >
        SD_STATUS,
      </if>
      <if test="sdCreateTime != null" >
        SD_CREATE_TIME,
      </if>
      <if test="sdHandleTime != null" >
        SD_HANDLE_TIME,
      </if>
      <if test="sdDirty != null" >
        SD_DIRTY,
      </if>
      <if test="sdDirtyTime != null" >
        SD_DIRTY_TIME,
      </if>
      <if test="sdCode != null" >
        SD_CODE,
      </if>
      <if test="sdPlanAmount != null" >
        SD_PLAN_AMOUNT,
      </if>
      <if test="sdStartTime != null" >
        SD_START_TIME,
      </if>
      <if test="sdEndTime != null" >
        SD_END_TIME,
      </if>
      <if test="sdTotalPieces != null" >
        SD_TOTAL_PIECES,
      </if>
      <if test="sdTotalWeight != null" >
        SD_TOTAL_WEIGHT,
      </if>
      <if test="sdRemark != null" >
        SD_REMARK,
      </if>
      <if test="sdType != null" >
        SD_TYPE,
      </if>
      <if test="estimateStatus != null" >
        ESTIMATE_STATUS,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="sdId != null" >
        #{sdId,jdbcType=DECIMAL},
      </if>
      <if test="companyId != null" >
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="soCode != null" >
        #{soCode,jdbcType=VARCHAR},
      </if>
      <if test="ctCode != null" >
        #{ctCode,jdbcType=VARCHAR},
      </if>
      <if test="sdCurrencyrate != null" >
        #{sdCurrencyrate,jdbcType=DECIMAL},
      </if>
      <if test="pdName != null" >
        #{pdName,jdbcType=VARCHAR},
      </if>
      <if test="pdSyscode != null" >
        #{pdSyscode,jdbcType=DECIMAL},
      </if>
      <if test="sdTotalrmb != null" >
        #{sdTotalrmb,jdbcType=DECIMAL},
      </if>
      <if test="sdTotalfc != null" >
        #{sdTotalfc,jdbcType=DECIMAL},
      </if>
      <if test="soTax != null" >
        #{soTax,jdbcType=DECIMAL},
      </if>
      <if test="taxAmount != null" >
        #{taxAmount,jdbcType=DECIMAL},
      </if>
      <if test="notaxAmount != null" >
        #{notaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="taxAmountFc != null" >
        #{taxAmountFc,jdbcType=DECIMAL},
      </if>
      <if test="notaxAmountFc != null" >
        #{notaxAmountFc,jdbcType=DECIMAL},
      </if>
      <if test="invoiceCode != null" >
        #{invoiceCode,jdbcType=VARCHAR},
      </if>
      <if test="sdUserId != null" >
        #{sdUserId,jdbcType=DECIMAL},
      </if>
      <if test="sdStatus != null" >
        #{sdStatus,jdbcType=VARCHAR},
      </if>
      <if test="sdCreateTime != null" >
        #{sdCreateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sdHandleTime != null" >
        #{sdHandleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sdDirty != null" >
        #{sdDirty,jdbcType=VARCHAR},
      </if>
      <if test="sdDirtyTime != null" >
        #{sdDirtyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sdCode != null" >
        #{sdCode,jdbcType=VARCHAR},
      </if>
      <if test="sdPlanAmount != null" >
        #{sdPlanAmount,jdbcType=DECIMAL},
      </if>
      <if test="sdStartTime != null" >
        #{sdStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sdEndTime != null" >
        #{sdEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sdTotalPieces != null" >
        #{sdTotalPieces,jdbcType=DECIMAL},
      </if>
      <if test="sdTotalWeight != null" >
        #{sdTotalWeight,jdbcType=DECIMAL},
      </if>
      <if test="sdRemark != null" >
        #{sdRemark,jdbcType=VARCHAR},
      </if>
      <if test="sdType != null" >
        #{sdType,jdbcType=VARCHAR},
      </if>
      <if test="estimateStatus != null" >
        #{estimateStatus,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.SinoDebitNote" >
    update SINO_DEBITNOTE
    <set >
      <if test="companyId != null" >
        COMPANY_ID = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="soCode != null" >
        SO_CODE = #{soCode,jdbcType=VARCHAR},
      </if>
      <if test="ctCode != null" >
        CT_CODE = #{ctCode,jdbcType=VARCHAR},
      </if>
      <if test="sdCurrencyrate != null" >
        SD_CURRENCYRATE = #{sdCurrencyrate,jdbcType=DECIMAL},
      </if>
      <if test="pdName != null" >
        PD_NAME = #{pdName,jdbcType=VARCHAR},
      </if>
      <if test="pdSyscode != null" >
        PD_SYSCODE = #{pdSyscode,jdbcType=DECIMAL},
      </if>
      <if test="sdTotalrmb != null" >
        SD_TOTALRMB = #{sdTotalrmb,jdbcType=DECIMAL},
      </if>
      <if test="sdTotalfc != null" >
        SD_TOTALFC = #{sdTotalfc,jdbcType=DECIMAL},
      </if>
      <if test="soTax != null" >
        SO_TAX = #{soTax,jdbcType=DECIMAL},
      </if>
      <if test="taxAmount != null" >
        TAX_AMOUNT = #{taxAmount,jdbcType=DECIMAL},
      </if>
      <if test="notaxAmount != null" >
        NOTAX_AMOUNT = #{notaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="taxAmountFc != null" >
        TAX_AMOUNT_FC = #{taxAmountFc,jdbcType=DECIMAL},
      </if>
      <if test="notaxAmountFc != null" >
        NOTAX_AMOUNT_FC = #{notaxAmountFc,jdbcType=DECIMAL},
      </if>
      <if test="invoiceCode != null" >
        INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR},
      </if>
      <if test="sdUserId != null" >
        SD_USER_ID = #{sdUserId,jdbcType=DECIMAL},
      </if>
      <if test="sdStatus != null" >
        SD_STATUS = #{sdStatus,jdbcType=VARCHAR},
      </if>
      <if test="sdCreateTime != null" >
        SD_CREATE_TIME = #{sdCreateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sdHandleTime != null" >
        SD_HANDLE_TIME = #{sdHandleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sdDirty != null" >
        SD_DIRTY = #{sdDirty,jdbcType=VARCHAR},
      </if>
      <if test="sdDirtyTime != null" >
        SD_DIRTY_TIME = #{sdDirtyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sdCode != null" >
        SD_CODE = #{sdCode,jdbcType=VARCHAR},
      </if>
      <if test="sdPlanAmount != null" >
        SD_PLAN_AMOUNT = #{sdPlanAmount,jdbcType=DECIMAL},
      </if>
      <if test="sdStartTime != null" >
        SD_START_TIME = #{sdStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sdEndTime != null" >
        SD_END_TIME = #{sdEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sdTotalPieces != null" >
        SD_TOTAL_PIECES = #{sdTotalPieces,jdbcType=DECIMAL},
      </if>
      <if test="sdTotalWeight != null" >
        SD_TOTAL_WEIGHT = #{sdTotalWeight,jdbcType=DECIMAL},
      </if>
      <if test="sdRemark != null" >
        SD_REMARK = #{sdRemark,jdbcType=VARCHAR},
      </if>
      <if test="sdType != null" >
        SD_TYPE = #{sdType,jdbcType=VARCHAR},
      </if>
      <if test="estimateStatus != null" >
        ESTIMATE_STATUS = #{estimateStatus,jdbcType=VARCHAR},
      </if>
    </set>
    where SD_ID = #{sdId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.SinoDebitNote" >
    update SINO_DEBITNOTE
    set COMPANY_ID = #{companyId,jdbcType=VARCHAR},
      SO_CODE = #{soCode,jdbcType=VARCHAR},
      CT_CODE = #{ctCode,jdbcType=VARCHAR},
      SD_CURRENCYRATE = #{sdCurrencyrate,jdbcType=DECIMAL},
      PD_NAME = #{pdName,jdbcType=VARCHAR},
      PD_SYSCODE = #{pdSyscode,jdbcType=DECIMAL},
      SD_TOTALRMB = #{sdTotalrmb,jdbcType=DECIMAL},
      SD_TOTALFC = #{sdTotalfc,jdbcType=DECIMAL},
      SO_TAX = #{soTax,jdbcType=DECIMAL},
      TAX_AMOUNT = #{taxAmount,jdbcType=DECIMAL},
      NOTAX_AMOUNT = #{notaxAmount,jdbcType=DECIMAL},
      TAX_AMOUNT_FC = #{taxAmountFc,jdbcType=DECIMAL},
      NOTAX_AMOUNT_FC = #{notaxAmountFc,jdbcType=DECIMAL},
      INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR},
      SD_USER_ID = #{sdUserId,jdbcType=DECIMAL},
      SD_STATUS = #{sdStatus,jdbcType=VARCHAR},
      SD_CREATE_TIME = #{sdCreateTime,jdbcType=TIMESTAMP},
      SD_HANDLE_TIME = #{sdHandleTime,jdbcType=TIMESTAMP},
      SD_DIRTY = #{sdDirty,jdbcType=VARCHAR},
      SD_DIRTY_TIME = #{sdDirtyTime,jdbcType=TIMESTAMP},
      SD_CODE = #{sdCode,jdbcType=VARCHAR},
      SD_PLAN_AMOUNT = #{sdPlanAmount,jdbcType=DECIMAL},
      SD_START_TIME = #{sdStartTime,jdbcType=TIMESTAMP},
      SD_END_TIME = #{sdEndTime,jdbcType=TIMESTAMP},
      SD_TOTAL_PIECES = #{sdTotalPieces,jdbcType=DECIMAL},
      SD_TOTAL_WEIGHT = #{sdTotalWeight,jdbcType=DECIMAL},
      SD_REMARK = #{sdRemark,jdbcType=VARCHAR},
      SD_TYPE = #{sdType,jdbcType=VARCHAR},
      ESTIMATE_STATUS = #{estimateStatus,jdbcType=VARCHAR}
    where SD_ID = #{sdId,jdbcType=DECIMAL}
  </update>

  <select id="selectSoByPercent" resultType="java.util.HashMap"  >
    select sop.so_code as soCode,sop.so_name as soName from internal_cost_map cmp,settlementobject sop
    where cmp.sp_code_pay = sop.so_code
    and cmp.billing_type = 'PERCENT'
    union
    select sor.so_code as soCode,sor.so_name as soName from internal_cost_map cmr,settlementobject sor
    where cmr.so_code_rec = sor.so_code
    and cmr.billing_type = 'PERCENT'
  </select>

  <select id="getDebitNoteListMap" resultType="java.util.HashMap"
          parameterType="com.sinoair.billing.domain.vo.FilterParam">
    select sd.sd_id as "sdId",
    sd.sd_code as "sdCode",
    sd.so_code as "soCode",
    sd.pd_name as "pdName",
    stt.so_name as "soName",
    sd.sd_status as "sdStatus",
    sd.sd_total_pieces as "sdTotalPieces",
    sd.sd_total_weight as "sdTotalWeight",
    to_char(sd.sd_start_time,'yyyy/MM/dd') as "sdStartTime",
    to_char(sd.sd_end_time,'yyyy/MM/dd')   as "sdEndTime",
    ct.ct_name as "ctCode",
    sd.ct_code as "ctCodeEn",
    sd.sd_plan_amount as "sdPlanAmount",
    sd.sd_totalfc as "sdTotalfc",
    sd.sd_type as "sdType",
    sd.sd_remark as "sdRemark"
    from SINO_DEBITNOTE sd, currencytype ct, settlementobject stt
    where sd.so_code = stt.so_code
    and sd.ct_code = ct.ct_code
    and 1 = 1
    and sd.sd_status!='OFF'
    and sd.company_id = #{companyId}
    <if test="code!=null and code!=''">
      and sd.sd_code = #{code}
    </if>
    <if test="status!=null and status!='ALL'">
      and sd.sd_status = #{status}
    </if>
    <if test="soCode!=null and soCode!=''">
      and sd.so_code = #{soCode}
    </if>
    <if test="starttime!=null and endtime!=null and starttime!='' and endtime!=''">
      and (to_char(sd.sd_start_time, 'yyyy-mm-dd') >= #{starttime} and
      to_char(sd.sd_start_time, 'yyyy-mm-dd') &lt;= #{endtime})
    </if>
    <if test='soCate == "P" '>
      AND (stt.SO_CATE = #{soCate} or stt.SO_CATE IS NULL)
    </if>
    <if test="sdType!=null and sdType!=''">
      and sd.sd_type = #{sdType}
    </if>
    order by sd.sd_start_time desc,sd.sd_id
  </select>
</mapper>