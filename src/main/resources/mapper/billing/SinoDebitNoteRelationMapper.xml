<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.SinoDebitNoteRelationMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.SinoDebitNoteRelation" >
    <id column="SDR_ID" property="sdrId" jdbcType="DECIMAL" />
    <result column="SD_ID" property="sdId" jdbcType="DECIMAL" />
    <result column="DM_ID" property="dmId" jdbcType="DECIMAL" />
  </resultMap>
  <sql id="Base_Column_List" >
    SDR_ID, SD_ID, DM_ID
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Short" >
    select 
    <include refid="Base_Column_List" />
    from SINO_DEBITNOTE_RELATION
    where SDR_ID = #{sdrId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Short" >
    delete from SINO_DEBITNOTE_RELATION
    where SDR_ID = #{sdrId,jdbcType=DECIMAL}
  </delete>
  <!-- 序列 -->
  <sql id='TABLE_SEQUENCE'>SEQ_DEBIT_MANIFEST.NEXTVAL</sql>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.SinoDebitNoteRelation" >
    <selectKey keyProperty="sdId" resultType="int" order="BEFORE">
      select
      <include refid="TABLE_SEQUENCE"/>
      from dual
    </selectKey>
    insert into SINO_DEBITNOTE_RELATION (SDR_ID, SD_ID, DM_ID
      )
    values (#{sdrId,jdbcType=DECIMAL}, #{sdId,jdbcType=DECIMAL}, #{dmId,jdbcType=DECIMAL}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.SinoDebitNoteRelation" >
    <selectKey keyProperty="sdId" resultType="int" order="BEFORE">
      select
      <include refid="TABLE_SEQUENCE"/>
      from dual
    </selectKey>
    insert into SINO_DEBITNOTE_RELATION
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="sdrId != null" >
        SDR_ID,
      </if>
      <if test="sdId != null" >
        SD_ID,
      </if>
      <if test="dmId != null" >
        DM_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="sdrId != null" >
        #{sdrId,jdbcType=DECIMAL},
      </if>
      <if test="sdId != null" >
        #{sdId,jdbcType=DECIMAL},
      </if>
      <if test="dmId != null" >
        #{dmId,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.SinoDebitNoteRelation" >
    update SINO_DEBITNOTE_RELATION
    <set >
      <if test="sdId != null" >
        SD_ID = #{sdId,jdbcType=DECIMAL},
      </if>
      <if test="dmId != null" >
        DM_ID = #{dmId,jdbcType=DECIMAL},
      </if>
    </set>
    where SDR_ID = #{sdrId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.SinoDebitNoteRelation" >
    update SINO_DEBITNOTE_RELATION
    set SD_ID = #{sdId,jdbcType=DECIMAL},
      DM_ID = #{dmId,jdbcType=DECIMAL}
    where SDR_ID = #{sdrId,jdbcType=DECIMAL}
  </update>

  <insert id="insertBatchByDmCode" parameterType="java.util.HashMap">
    insert into SINO_DEBITNOTE_RELATION (SDR_ID, SD_ID, DM_ID
          )
          select SEQ_DEBIT_MANIFEST.NEXTVAL,#{sdId},t.dm_id
        from DEBIT_MANIFEST t
        where t.dm_code in
    <foreach collection="dmCodes" item="dmCode" open="(" separator="," close=")">
      #{dmCode}
    </foreach>
  </insert>
</mapper>