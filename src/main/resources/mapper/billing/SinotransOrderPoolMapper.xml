<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.SinotransOrderPoolMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.SinotransOrderPool" >
    <id column="SOP_SYSCODE" property="sopSyscode" jdbcType="DECIMAL" />
    <result column="EAWB_PRINTCODE" property="eawbPrintcode" jdbcType="VARCHAR" />
    <result column="SINOTRANS_ID" property="sinotransId" jdbcType="VARCHAR" />
    <result column="ORDER_ID" property="orderId" jdbcType="VARCHAR" />
    <result column="SOURCE_ID" property="sourceId" jdbcType="VARCHAR" />
    <result column="ORIGINAL_ORG" property="originalOrg" jdbcType="VARCHAR" />
    <result column="STATUS_TEMPLATE" property="statusTemplate" jdbcType="VARCHAR" />
    <result column="ORIGINAL_CUST_ID" property="originalCustId" jdbcType="VARCHAR" />
    <result column="STANDARD_CUST_NAME" property="standardCustName" jdbcType="VARCHAR" />
    <result column="SENDER_CODE" property="senderCode" jdbcType="VARCHAR" />
    <result column="BUSINESS_VOLUME" property="businessVolume" jdbcType="DECIMAL" />
    <result column="BIZ_TYPE" property="bizType" jdbcType="VARCHAR" />
    <result column="IMPORT_EXPORT_MARKS" property="importExportMarks" jdbcType="VARCHAR" />
    <result column="BL_NO" property="blNo" jdbcType="VARCHAR" />
    <result column="CARRIER_CODE" property="carrierCode" jdbcType="VARCHAR" />
    <result column="ATTRIBUTE5" property="attribute5" jdbcType="VARCHAR" />
    <result column="ATTRIBUTE7" property="attribute7" jdbcType="VARCHAR" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="EXTEND_1" property="extend1" jdbcType="VARCHAR" />
    <result column="EXTEND_2" property="extend2" jdbcType="VARCHAR" />
    <result column="EXTEND_3" property="extend3" jdbcType="VARCHAR" />
    <result column="EXTEND_4" property="extend4" jdbcType="VARCHAR" />
    <result column="EXTEND_5" property="extend5" jdbcType="VARCHAR" />
    <result column="EXTEND_6" property="extend6" jdbcType="VARCHAR" />
    <result column="EXTEND_7" property="extend7" jdbcType="VARCHAR" />
    <result column="EXTEND_8" property="extend8" jdbcType="VARCHAR" />
    <result column="BM_PIECE" property="bmPiece" jdbcType="DECIMAL" />
    <result column="BM_CHARGEABLEWEIGHT" property="bmChargeableweight" jdbcType="DECIMAL" />
    <result column="TA_NC_CODE_DEPARTURE" property="taNcCodeDeparture" jdbcType="VARCHAR" />
    <result column="TA_NC_CODE_DESTINATION" property="taNcCodeDestination" jdbcType="VARCHAR" />
    <result column="EP_KEY" property="epKey" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    SOP_SYSCODE, EAWB_PRINTCODE, SINOTRANS_ID, ORDER_ID, SOURCE_ID, ORIGINAL_ORG, STATUS_TEMPLATE,
    ORIGINAL_CUST_ID, STANDARD_CUST_NAME, SENDER_CODE, BUSINESS_VOLUME, BIZ_TYPE, IMPORT_EXPORT_MARKS,
    BL_NO, CARRIER_CODE, ATTRIBUTE5, ATTRIBUTE7, CREATE_TIME, EXTEND_1, EXTEND_2, EXTEND_3,
    EXTEND_4, EXTEND_5, EXTEND_6, EXTEND_7, EXTEND_8, BM_PIECE, BM_CHARGEABLEWEIGHT,
    TA_NC_CODE_DEPARTURE, TA_NC_CODE_DESTINATION,EP_KEY
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from SINOTRANS_ORDER_POOL
    where SOP_SYSCODE = #{sopSyscode,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from SINOTRANS_ORDER_POOL
    where SOP_SYSCODE = #{sopSyscode,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.SinotransOrderPool" >
    insert into SINOTRANS_ORDER_POOL (SOP_SYSCODE, EAWB_PRINTCODE, SINOTRANS_ID,
    ORDER_ID, SOURCE_ID, ORIGINAL_ORG,
    STATUS_TEMPLATE, ORIGINAL_CUST_ID, STANDARD_CUST_NAME,
    SENDER_CODE, BUSINESS_VOLUME, BIZ_TYPE,
    IMPORT_EXPORT_MARKS, BL_NO, CARRIER_CODE,
    ATTRIBUTE5, ATTRIBUTE7, CREATE_TIME,
    EXTEND_1, EXTEND_2, EXTEND_3,
    EXTEND_4, EXTEND_5, EXTEND_6,
    EXTEND_7, EXTEND_8, BM_PIECE,
    BM_CHARGEABLEWEIGHT, TA_NC_CODE_DEPARTURE,
    TA_NC_CODE_DESTINATION,EP_KEY)
    values (#{sopSyscode,jdbcType=DECIMAL}, #{eawbPrintcode,jdbcType=VARCHAR}, #{sinotransId,jdbcType=VARCHAR},
    #{orderId,jdbcType=VARCHAR}, #{sourceId,jdbcType=VARCHAR}, #{originalOrg,jdbcType=VARCHAR},
    #{statusTemplate,jdbcType=VARCHAR}, #{originalCustId,jdbcType=VARCHAR}, #{standardCustName,jdbcType=VARCHAR},
    #{senderCode,jdbcType=VARCHAR}, #{businessVolume,jdbcType=DECIMAL}, #{bizType,jdbcType=VARCHAR},
    #{importExportMarks,jdbcType=VARCHAR}, #{blNo,jdbcType=VARCHAR}, #{carrierCode,jdbcType=VARCHAR},
    #{attribute5,jdbcType=VARCHAR}, #{attribute7,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
    #{extend1,jdbcType=VARCHAR}, #{extend2,jdbcType=VARCHAR}, #{extend3,jdbcType=VARCHAR},
    #{extend4,jdbcType=VARCHAR}, #{extend5,jdbcType=VARCHAR}, #{extend6,jdbcType=VARCHAR},
    #{extend7,jdbcType=VARCHAR}, #{extend8,jdbcType=VARCHAR}, #{bmPiece,jdbcType=DECIMAL},
    #{bmChargeableweight,jdbcType=DECIMAL}, #{taNcCodeDeparture,jdbcType=VARCHAR},
    #{taNcCodeDestination,jdbcType=VARCHAR},#{epKey,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.SinotransOrderPool" >
    insert into SINOTRANS_ORDER_POOL
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="sopSyscode != null" >
        SOP_SYSCODE,
      </if>
      <if test="eawbPrintcode != null" >
        EAWB_PRINTCODE,
      </if>
      <if test="sinotransId != null" >
        SINOTRANS_ID,
      </if>
      <if test="orderId != null" >
        ORDER_ID,
      </if>
      <if test="sourceId != null" >
        SOURCE_ID,
      </if>
      <if test="originalOrg != null" >
        ORIGINAL_ORG,
      </if>
      <if test="statusTemplate != null" >
        STATUS_TEMPLATE,
      </if>
      <if test="originalCustId != null" >
        ORIGINAL_CUST_ID,
      </if>
      <if test="standardCustName != null" >
        STANDARD_CUST_NAME,
      </if>
      <if test="senderCode != null" >
        SENDER_CODE,
      </if>
      <if test="businessVolume != null" >
        BUSINESS_VOLUME,
      </if>
      <if test="bizType != null" >
        BIZ_TYPE,
      </if>
      <if test="importExportMarks != null" >
        IMPORT_EXPORT_MARKS,
      </if>
      <if test="blNo != null" >
        BL_NO,
      </if>
      <if test="carrierCode != null" >
        CARRIER_CODE,
      </if>
      <if test="attribute5 != null" >
        ATTRIBUTE5,
      </if>
      <if test="attribute7 != null" >
        ATTRIBUTE7,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
      <if test="extend1 != null" >
        EXTEND_1,
      </if>
      <if test="extend2 != null" >
        EXTEND_2,
      </if>
      <if test="extend3 != null" >
        EXTEND_3,
      </if>
      <if test="extend4 != null" >
        EXTEND_4,
      </if>
      <if test="extend5 != null" >
        EXTEND_5,
      </if>
      <if test="extend6 != null" >
        EXTEND_6,
      </if>
      <if test="extend7 != null" >
        EXTEND_7,
      </if>
      <if test="extend8 != null" >
        EXTEND_8,
      </if>
      <if test="bmPiece != null" >
        BM_PIECE,
      </if>
      <if test="bmChargeableweight != null" >
        BM_CHARGEABLEWEIGHT,
      </if>
      <if test="taNcCodeDeparture != null" >
        TA_NC_CODE_DEPARTURE,
      </if>
      <if test="taNcCodeDestination != null" >
        TA_NC_CODE_DESTINATION,
      </if>
      <if test="epKey != null" >
        EP_KEY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="sopSyscode != null" >
        #{sopSyscode,jdbcType=DECIMAL},
      </if>
      <if test="eawbPrintcode != null" >
        #{eawbPrintcode,jdbcType=VARCHAR},
      </if>
      <if test="sinotransId != null" >
        #{sinotransId,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null" >
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="sourceId != null" >
        #{sourceId,jdbcType=VARCHAR},
      </if>
      <if test="originalOrg != null" >
        #{originalOrg,jdbcType=VARCHAR},
      </if>
      <if test="statusTemplate != null" >
        #{statusTemplate,jdbcType=VARCHAR},
      </if>
      <if test="originalCustId != null" >
        #{originalCustId,jdbcType=VARCHAR},
      </if>
      <if test="standardCustName != null" >
        #{standardCustName,jdbcType=VARCHAR},
      </if>
      <if test="senderCode != null" >
        #{senderCode,jdbcType=VARCHAR},
      </if>
      <if test="businessVolume != null" >
        #{businessVolume,jdbcType=DECIMAL},
      </if>
      <if test="bizType != null" >
        #{bizType,jdbcType=VARCHAR},
      </if>
      <if test="importExportMarks != null" >
        #{importExportMarks,jdbcType=VARCHAR},
      </if>
      <if test="blNo != null" >
        #{blNo,jdbcType=VARCHAR},
      </if>
      <if test="carrierCode != null" >
        #{carrierCode,jdbcType=VARCHAR},
      </if>
      <if test="attribute5 != null" >
        #{attribute5,jdbcType=VARCHAR},
      </if>
      <if test="attribute7 != null" >
        #{attribute7,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="extend1 != null" >
        #{extend1,jdbcType=VARCHAR},
      </if>
      <if test="extend2 != null" >
        #{extend2,jdbcType=VARCHAR},
      </if>
      <if test="extend3 != null" >
        #{extend3,jdbcType=VARCHAR},
      </if>
      <if test="extend4 != null" >
        #{extend4,jdbcType=VARCHAR},
      </if>
      <if test="extend5 != null" >
        #{extend5,jdbcType=VARCHAR},
      </if>
      <if test="extend6 != null" >
        #{extend6,jdbcType=VARCHAR},
      </if>
      <if test="extend7 != null" >
        #{extend7,jdbcType=VARCHAR},
      </if>
      <if test="extend8 != null" >
        #{extend8,jdbcType=VARCHAR},
      </if>
      <if test="bmPiece != null" >
        #{bmPiece,jdbcType=DECIMAL},
      </if>
      <if test="bmChargeableweight != null" >
        #{bmChargeableweight,jdbcType=DECIMAL},
      </if>
      <if test="taNcCodeDeparture != null" >
        #{taNcCodeDeparture,jdbcType=VARCHAR},
      </if>
      <if test="taNcCodeDestination != null" >
        #{taNcCodeDestination,jdbcType=VARCHAR},
      </if>
      <if test="epKey != null" >
        #{epKey,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.SinotransOrderPool" >
    update SINOTRANS_ORDER_POOL
    <set >
      <if test="eawbPrintcode != null" >
        EAWB_PRINTCODE = #{eawbPrintcode,jdbcType=VARCHAR},
      </if>
      <if test="sinotransId != null" >
        SINOTRANS_ID = #{sinotransId,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null" >
        ORDER_ID = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="sourceId != null" >
        SOURCE_ID = #{sourceId,jdbcType=VARCHAR},
      </if>
      <if test="originalOrg != null" >
        ORIGINAL_ORG = #{originalOrg,jdbcType=VARCHAR},
      </if>
      <if test="statusTemplate != null" >
        STATUS_TEMPLATE = #{statusTemplate,jdbcType=VARCHAR},
      </if>
      <if test="originalCustId != null" >
        ORIGINAL_CUST_ID = #{originalCustId,jdbcType=VARCHAR},
      </if>
      <if test="standardCustName != null" >
        STANDARD_CUST_NAME = #{standardCustName,jdbcType=VARCHAR},
      </if>
      <if test="senderCode != null" >
        SENDER_CODE = #{senderCode,jdbcType=VARCHAR},
      </if>
      <if test="businessVolume != null" >
        BUSINESS_VOLUME = #{businessVolume,jdbcType=DECIMAL},
      </if>
      <if test="bizType != null" >
        BIZ_TYPE = #{bizType,jdbcType=VARCHAR},
      </if>
      <if test="importExportMarks != null" >
        IMPORT_EXPORT_MARKS = #{importExportMarks,jdbcType=VARCHAR},
      </if>
      <if test="blNo != null" >
        BL_NO = #{blNo,jdbcType=VARCHAR},
      </if>
      <if test="carrierCode != null" >
        CARRIER_CODE = #{carrierCode,jdbcType=VARCHAR},
      </if>
      <if test="attribute5 != null" >
        ATTRIBUTE5 = #{attribute5,jdbcType=VARCHAR},
      </if>
      <if test="attribute7 != null" >
        ATTRIBUTE7 = #{attribute7,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="extend1 != null" >
        EXTEND_1 = #{extend1,jdbcType=VARCHAR},
      </if>
      <if test="extend2 != null" >
        EXTEND_2 = #{extend2,jdbcType=VARCHAR},
      </if>
      <if test="extend3 != null" >
        EXTEND_3 = #{extend3,jdbcType=VARCHAR},
      </if>
      <if test="extend4 != null" >
        EXTEND_4 = #{extend4,jdbcType=VARCHAR},
      </if>
      <if test="extend5 != null" >
        EXTEND_5 = #{extend5,jdbcType=VARCHAR},
      </if>
      <if test="extend6 != null" >
        EXTEND_6 = #{extend6,jdbcType=VARCHAR},
      </if>
      <if test="extend7 != null" >
        EXTEND_7 = #{extend7,jdbcType=VARCHAR},
      </if>
      <if test="extend8 != null" >
        EXTEND_8 = #{extend8,jdbcType=VARCHAR},
      </if>
      <if test="bmPiece != null" >
        BM_PIECE = #{bmPiece,jdbcType=DECIMAL},
      </if>
      <if test="bmChargeableweight != null" >
        BM_CHARGEABLEWEIGHT = #{bmChargeableweight,jdbcType=DECIMAL},
      </if>
      <if test="taNcCodeDeparture != null" >
        TA_NC_CODE_DEPARTURE = #{taNcCodeDeparture,jdbcType=VARCHAR},
      </if>
      <if test="taNcCodeDestination != null" >
        TA_NC_CODE_DESTINATION = #{taNcCodeDestination,jdbcType=VARCHAR},
      </if>
      <if test="epKey != null" >
        EP_KEY = #{epKey,jdbcType=VARCHAR},
      </if>
    </set>
    where SOP_SYSCODE = #{sopSyscode,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.SinotransOrderPool" >
    update SINOTRANS_ORDER_POOL
    set EAWB_PRINTCODE = #{eawbPrintcode,jdbcType=VARCHAR},
      SINOTRANS_ID = #{sinotransId,jdbcType=VARCHAR},
      ORDER_ID = #{orderId,jdbcType=VARCHAR},
      SOURCE_ID = #{sourceId,jdbcType=VARCHAR},
      ORIGINAL_ORG = #{originalOrg,jdbcType=VARCHAR},
      STATUS_TEMPLATE = #{statusTemplate,jdbcType=VARCHAR},
      ORIGINAL_CUST_ID = #{originalCustId,jdbcType=VARCHAR},
      STANDARD_CUST_NAME = #{standardCustName,jdbcType=VARCHAR},
      SENDER_CODE = #{senderCode,jdbcType=VARCHAR},
      BUSINESS_VOLUME = #{businessVolume,jdbcType=DECIMAL},
      BIZ_TYPE = #{bizType,jdbcType=VARCHAR},
      IMPORT_EXPORT_MARKS = #{importExportMarks,jdbcType=VARCHAR},
      BL_NO = #{blNo,jdbcType=VARCHAR},
      CARRIER_CODE = #{carrierCode,jdbcType=VARCHAR},
      ATTRIBUTE5 = #{attribute5,jdbcType=VARCHAR},
      ATTRIBUTE7 = #{attribute7,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      EXTEND_1 = #{extend1,jdbcType=VARCHAR},
      EXTEND_2 = #{extend2,jdbcType=VARCHAR},
      EXTEND_3 = #{extend3,jdbcType=VARCHAR},
      EXTEND_4 = #{extend4,jdbcType=VARCHAR},
      EXTEND_5 = #{extend5,jdbcType=VARCHAR},
      EXTEND_6 = #{extend6,jdbcType=VARCHAR},
      EXTEND_7 = #{extend7,jdbcType=VARCHAR},
      EXTEND_8 = #{extend8,jdbcType=VARCHAR},
      BM_PIECE = #{bmPiece,jdbcType=DECIMAL},
      BM_CHARGEABLEWEIGHT = #{bmChargeableweight,jdbcType=DECIMAL},
      TA_NC_CODE_DEPARTURE = #{taNcCodeDeparture,jdbcType=VARCHAR},
      TA_NC_CODE_DESTINATION = #{taNcCodeDestination,jdbcType=VARCHAR},
      EP_KEY = #{epKey,jdbcType=VARCHAR}
    where SOP_SYSCODE = #{sopSyscode,jdbcType=DECIMAL}
  </update>

  <insert id="insertBatch"  parameterType="java.util.List">
  insert into SINOTRANS_ORDER_POOL (SOP_SYSCODE, EAWB_PRINTCODE, SINOTRANS_ID,
  ORDER_ID, SOURCE_ID, ORIGINAL_ORG,
  STATUS_TEMPLATE, ORIGINAL_CUST_ID, STANDARD_CUST_NAME,
  SENDER_CODE, BUSINESS_VOLUME, BIZ_TYPE,
  IMPORT_EXPORT_MARKS, BL_NO, CARRIER_CODE,
  ATTRIBUTE5, ATTRIBUTE7, CREATE_TIME,
  EXTEND_1, EXTEND_2, EXTEND_3,
  EXTEND_4, EXTEND_5, EXTEND_6,
  EXTEND_7, EXTEND_8,
    BM_PIECE, BM_CHARGEABLEWEIGHT,
    TA_NC_CODE_DEPARTURE, TA_NC_CODE_DESTINATION,EP_KEY)
  select SEQ_ORDER_POOL.NEXTVAL,pbd.* from (
  <foreach collection="list" item="item" index="index" separator="UNION ALL">
    select #{item.eawbPrintcode,jdbcType=VARCHAR}, #{item.sinotransId,jdbcType=VARCHAR},
    #{item.orderId,jdbcType=VARCHAR}, #{item.sourceId,jdbcType=VARCHAR}, #{item.originalOrg,jdbcType=VARCHAR},
    #{item.statusTemplate,jdbcType=VARCHAR}, #{item.originalCustId,jdbcType=VARCHAR}, #{item.standardCustName,jdbcType=VARCHAR},
    #{item.senderCode,jdbcType=VARCHAR}, #{item.businessVolume,jdbcType=DECIMAL}, #{item.bizType,jdbcType=VARCHAR},
    #{item.importExportMarks,jdbcType=VARCHAR}, #{item.blNo,jdbcType=VARCHAR}, #{item.carrierCode,jdbcType=VARCHAR},
    #{item.attribute5,jdbcType=VARCHAR}, #{item.attribute7,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP},
    #{item.extend1,jdbcType=VARCHAR}, #{item.extend2,jdbcType=VARCHAR}, #{item.extend3,jdbcType=VARCHAR},
    #{item.extend4,jdbcType=VARCHAR}, #{item.extend5,jdbcType=VARCHAR}, #{item.extend6,jdbcType=VARCHAR},
    #{item.extend7,jdbcType=VARCHAR}, #{item.extend8,jdbcType=VARCHAR}, #{item.bmPiece,jdbcType=DECIMAL},
    #{item.bmChargeableweight,jdbcType=DECIMAL}, #{item.taNcCodeDeparture,jdbcType=VARCHAR},
    #{item.taNcCodeDestination,jdbcType=VARCHAR},#{item.epKey,jdbcType=VARCHAR} from dual
  </foreach>
  ) pbd
</insert>

  <select id="countOrderPool" resultType="java.lang.Integer" parameterType="java.lang.String" >
    select
    count(0)
    from SINOTRANS_ORDER_POOL
    where EAWB_PRINTCODE = #{eawbPrintcode,jdbcType=VARCHAR}
  </select>

  <select id="selectOrderWeightCollect" resultType="com.sinoair.billing.domain.model.billing.SinotransOrderWeight"
          parameterType="com.sinoair.billing.domain.vo.query.CommonQuery" >
    select
    SINOTRANS_ID,EP_KEY,
    sum(nvl(BM_PIECE,1)) as BM_PIECE,
    sum(BM_CHARGEABLEWEIGHT) as BM_CHARGEABLEWEIGHT,
    min(TA_NC_CODE_DEPARTURE) as DEPARTURE_CODE,
    min(TA_NC_CODE_DESTINATION) as DEST_CODE,
    max(EXTEND_1) as SAC_ID,
    max(EXTEND_3) as SO_CODE
    from SINOTRANS_ORDER_POOL
    where CREATE_TIME >= to_date(${strStartDate},'yyyymmdd')
      and CREATE_TIME &lt; to_date(${strEndDate},'yyyymmdd')
      group by SINOTRANS_ID,EP_KEY
  </select>

</mapper>