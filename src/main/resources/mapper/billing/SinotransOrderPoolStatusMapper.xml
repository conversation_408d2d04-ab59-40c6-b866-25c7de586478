<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.SinotransOrderPoolStatusMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.SinotransOrderPoolStatus" >
    <id column="SOPS_SYSCODE" property="sopsSyscode" jdbcType="DECIMAL" />
    <result column="EAWB_PRINTCODE" property="eawbPrintcode" jdbcType="VARCHAR" />
    <result column="STATUS_CODE" property="statusCode" jdbcType="VARCHAR" />
    <result column="STATUS_DESC" property="statusDesc" jdbcType="VARCHAR" />
    <result column="STATUS_TIME" property="statusTime" jdbcType="TIMESTAMP" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    SOPS_SYSCODE, EAWB_PRINTCODE, STATUS_CODE, STATUS_DESC, STATUS_TIME,
    CREATE_TIME
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from SINOTRANS_ORDER_POOL_STATUS
    where SOPS_SYSCODE = #{sopsSyscode,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from SINOTRANS_ORDER_POOL_STATUS
    where SOPS_SYSCODE = #{sopsSyscode,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.SinotransOrderPoolStatus" >
    insert into SINOTRANS_ORDER_POOL_STATUS (SOPS_SYSCODE, EAWB_PRINTCODE,
      STATUS_CODE, STATUS_DESC, STATUS_TIME, 
      CREATE_TIME
      )
    values (#{sopsSyscode,jdbcType=DECIMAL}, #{eawbPrintcode,jdbcType=VARCHAR},
      #{statusCode,jdbcType=VARCHAR}, #{statusDesc,jdbcType=VARCHAR}, #{statusTime,jdbcType=TIMESTAMP}, 
      #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.SinotransOrderPoolStatus" >
    insert into SINOTRANS_ORDER_POOL_STATUS
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="sopsSyscode != null" >
        SOPS_SYSCODE,
      </if>
      <if test="eawbPrintcode != null" >
        EAWB_PRINTCODE,
      </if>
      <if test="statusCode != null" >
        STATUS_CODE,
      </if>
      <if test="statusDesc != null" >
        STATUS_DESC,
      </if>
      <if test="statusTime != null" >
        STATUS_TIME,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="sopsSyscode != null" >
        #{sopsSyscode,jdbcType=DECIMAL},
      </if>
      <if test="eawbPrintcode != null" >
        #{eawbPrintcode,jdbcType=VARCHAR},
      </if>
      <if test="statusCode != null" >
        #{statusCode,jdbcType=VARCHAR},
      </if>
      <if test="statusDesc != null" >
        #{statusDesc,jdbcType=VARCHAR},
      </if>
      <if test="statusTime != null" >
        #{statusTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.SinotransOrderPoolStatus" >
    update SINOTRANS_ORDER_POOL_STATUS
    <set >
      <if test="eawbPrintcode != null" >
        EAWB_PRINTCODE = #{eawbPrintcode,jdbcType=VARCHAR},
      </if>
      <if test="statusCode != null" >
        STATUS_CODE = #{statusCode,jdbcType=VARCHAR},
      </if>
      <if test="statusDesc != null" >
        STATUS_DESC = #{statusDesc,jdbcType=VARCHAR},
      </if>
      <if test="statusTime != null" >
        STATUS_TIME = #{statusTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null" >
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where SOPS_SYSCODE = #{sopsSyscode,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.SinotransOrderPoolStatus" >
    update SINOTRANS_ORDER_POOL_STATUS
    set EAWB_PRINTCODE = #{eawbPrintcode,jdbcType=VARCHAR},
      STATUS_CODE = #{statusCode,jdbcType=VARCHAR},
      STATUS_DESC = #{statusDesc,jdbcType=VARCHAR},
      STATUS_TIME = #{statusTime,jdbcType=TIMESTAMP},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
    where SOPS_SYSCODE = #{sopsSyscode,jdbcType=DECIMAL}
  </update>

  <insert id="insertBatch"  parameterType="java.util.List">
    insert into SINOTRANS_ORDER_POOL_STATUS (SOPS_SYSCODE, EAWB_PRINTCODE,
    STATUS_CODE, STATUS_DESC, STATUS_TIME,
    CREATE_TIME)
    select SEQ_ORDER_POOL_STATUS.NEXTVAL,pbd.* from (
    <foreach collection="list" item="item" index="index" separator="UNION ALL">
      select #{item.eawbPrintcode,jdbcType=VARCHAR},
      #{item.statusCode,jdbcType=VARCHAR}, #{item.statusDesc,jdbcType=VARCHAR}, #{item.statusTime,jdbcType=TIMESTAMP},
      #{item.createTime,jdbcType=TIMESTAMP} from dual
    </foreach>
    ) pbd
  </insert>

  <select id="countOrderPoolStatus" resultType="java.lang.Integer" parameterType="java.lang.String" >
    select
    count(0)
    from SINOTRANS_ORDER_POOL_STATUS
    where EAWB_PRINTCODE = #{eawbPrintcode,jdbcType=VARCHAR}
    <if test="statusCode != null" >
      and STATUS_CODE = #{statusCode,jdbcType=VARCHAR}
    </if>
  </select>
</mapper>