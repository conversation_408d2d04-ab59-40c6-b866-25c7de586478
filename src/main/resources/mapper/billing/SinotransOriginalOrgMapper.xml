<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.SinotransOriginalOrgMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.SinotransOriginalOrg" >
    <id column="SAC_ID" property="sacId" jdbcType="VARCHAR" />
    <result column="SAC_NAME" property="sacName" jdbcType="VARCHAR" />
    <result column="MAIN_ORG_CODE" property="mainOrgCode" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    SAC_ID, SAC_NAME, MAIN_ORG_CODE
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from SINOTRANS_ORIGINAL_ORG
    where SAC_ID = #{sacId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from SINOTRANS_ORIGINAL_ORG
    where SAC_ID = #{sacId,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.SinotransOriginalOrg" >
    insert into SINOTRANS_ORIGINAL_ORG (SAC_ID, SAC_NAME, MAIN_ORG_CODE
      )
    values (#{sacId,jdbcType=VARCHAR}, #{sacName,jdbcType=VARCHAR}, #{mainOrgCode,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.SinotransOriginalOrg" >
    insert into SINOTRANS_ORIGINAL_ORG
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="sacId != null" >
        SAC_ID,
      </if>
      <if test="sacName != null" >
        SAC_NAME,
      </if>
      <if test="mainOrgCode != null" >
        MAIN_ORG_CODE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="sacId != null" >
        #{sacId,jdbcType=VARCHAR},
      </if>
      <if test="sacName != null" >
        #{sacName,jdbcType=VARCHAR},
      </if>
      <if test="mainOrgCode != null" >
        #{mainOrgCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.SinotransOriginalOrg" >
    update SINOTRANS_ORIGINAL_ORG
    <set >
      <if test="sacName != null" >
        SAC_NAME = #{sacName,jdbcType=VARCHAR},
      </if>
      <if test="mainOrgCode != null" >
        MAIN_ORG_CODE = #{mainOrgCode,jdbcType=VARCHAR},
      </if>
    </set>
    where SAC_ID = #{sacId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.SinotransOriginalOrg" >
    update SINOTRANS_ORIGINAL_ORG
    set SAC_NAME = #{sacName,jdbcType=VARCHAR},
      MAIN_ORG_CODE = #{mainOrgCode,jdbcType=VARCHAR}
    where SAC_ID = #{sacId,jdbcType=VARCHAR}
  </update>

  <select id="listSinotransOrg" resultMap="BaseResultMap"  >
    select
    <include refid="Base_Column_List" />
    from SINOTRANS_ORIGINAL_ORG
  </select>
</mapper>