<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.SupplierMapper">
    <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.Supplier" >
        <id column="SP_SYSCODE" property="spSyscode" jdbcType="VARCHAR" />
        <result column="COMPANY_ID" property="companyId" jdbcType="VARCHAR" />
        <result column="SP_CODE" property="spCode" jdbcType="VARCHAR" />
        <result column="SP_NAME" property="spName" jdbcType="VARCHAR" />
        <result column="SP_ENAME" property="spEname" jdbcType="VARCHAR" />
        <result column="SP_ADDRESS" property="spAddress" jdbcType="VARCHAR" />
        <result column="SP_EADDRESS" property="spEaddress" jdbcType="VARCHAR" />
        <result column="SP_POSTCODE" property="spPostcode" jdbcType="VARCHAR" />
        <result column="SP_USER_ID" property="spUserId" jdbcType="DECIMAL" />
        <result column="SP_HANDLETIME" property="spHandletime" jdbcType="TIMESTAMP" />
        <result column="SP_STATUS" property="spStatus" jdbcType="VARCHAR" />
        <result column="SP_TELEPHONE" property="spTelephone" jdbcType="VARCHAR" />
        <result column="SP_FAX" property="spFax" jdbcType="VARCHAR" />
        <result column="SP_CONTRACTINFO" property="spContractinfo" jdbcType="VARCHAR" />
        <result column="SP_TYPE" property="spType" jdbcType="VARCHAR" />
        <result column="SP_COMPANY_ID" property="spCompanyId" jdbcType="VARCHAR" />
        <result column="VENDOR_CODE" property="vendorCode" jdbcType="VARCHAR" />
        <result column="TAX_RATE" property="taxRate" jdbcType="DECIMAL" />
        <result column="BMS_STATUS" property="bmsStatus" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List" >
        SP_SYSCODE, COMPANY_ID, SP_CODE, SP_NAME, SP_ENAME, SP_ADDRESS, SP_EADDRESS, SP_POSTCODE,
        SP_USER_ID, SP_HANDLETIME, SP_STATUS, SP_TELEPHONE, SP_FAX, SP_CONTRACTINFO, SP_TYPE,
        SP_COMPANY_ID, VENDOR_CODE, TAX_RATE,BMS_STATUS
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select
        <include refid="Base_Column_List" />
        from SUPPLIER
        where SP_SYSCODE = #{spSyscode,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
        delete from SUPPLIER
        where SP_SYSCODE = #{spSyscode,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.Supplier" >
        insert into SUPPLIER (SP_SYSCODE, COMPANY_ID, SP_CODE,
        SP_NAME, SP_ENAME, SP_ADDRESS,
        SP_EADDRESS, SP_POSTCODE, SP_USER_ID,
        SP_HANDLETIME, SP_STATUS, SP_TELEPHONE,
        SP_FAX, SP_CONTRACTINFO, SP_TYPE,
        SP_COMPANY_ID, VENDOR_CODE, TAX_RATE
        )
        values (#{spSyscode,jdbcType=VARCHAR}, #{companyId,jdbcType=VARCHAR}, #{spCode,jdbcType=VARCHAR},
        #{spName,jdbcType=VARCHAR}, #{spEname,jdbcType=VARCHAR}, #{spAddress,jdbcType=VARCHAR},
        #{spEaddress,jdbcType=VARCHAR}, #{spPostcode,jdbcType=VARCHAR}, #{spUserId,jdbcType=DECIMAL},
        #{spHandletime,jdbcType=TIMESTAMP}, #{spStatus,jdbcType=VARCHAR}, #{spTelephone,jdbcType=VARCHAR},
        #{spFax,jdbcType=VARCHAR}, #{spContractinfo,jdbcType=VARCHAR}, #{spType,jdbcType=VARCHAR},
        #{spCompanyId,jdbcType=VARCHAR}, #{vendorCode,jdbcType=VARCHAR}, #{taxRate,jdbcType=DECIMAL}
        )
    </insert>
    <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.Supplier" >
        insert into SUPPLIER
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="spSyscode != null" >
                SP_SYSCODE,
            </if>
            <if test="companyId != null" >
                COMPANY_ID,
            </if>
            <if test="spCode != null" >
                SP_CODE,
            </if>
            <if test="spName != null" >
                SP_NAME,
            </if>
            <if test="spEname != null" >
                SP_ENAME,
            </if>
            <if test="spAddress != null" >
                SP_ADDRESS,
            </if>
            <if test="spEaddress != null" >
                SP_EADDRESS,
            </if>
            <if test="spPostcode != null" >
                SP_POSTCODE,
            </if>
            <if test="spUserId != null" >
                SP_USER_ID,
            </if>
            <if test="spHandletime != null" >
                SP_HANDLETIME,
            </if>
            <if test="spStatus != null" >
                SP_STATUS,
            </if>
            <if test="spTelephone != null" >
                SP_TELEPHONE,
            </if>
            <if test="spFax != null" >
                SP_FAX,
            </if>
            <if test="spContractinfo != null" >
                SP_CONTRACTINFO,
            </if>
            <if test="spType != null" >
                SP_TYPE,
            </if>
            <if test="spCompanyId != null" >
                SP_COMPANY_ID,
            </if>
            <if test="vendorCode != null" >
                VENDOR_CODE,
            </if>
            <if test="taxRate != null" >
                TAX_RATE,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="spSyscode != null" >
                #{spSyscode,jdbcType=VARCHAR},
            </if>
            <if test="companyId != null" >
                #{companyId,jdbcType=VARCHAR},
            </if>
            <if test="spCode != null" >
                #{spCode,jdbcType=VARCHAR},
            </if>
            <if test="spName != null" >
                #{spName,jdbcType=VARCHAR},
            </if>
            <if test="spEname != null" >
                #{spEname,jdbcType=VARCHAR},
            </if>
            <if test="spAddress != null" >
                #{spAddress,jdbcType=VARCHAR},
            </if>
            <if test="spEaddress != null" >
                #{spEaddress,jdbcType=VARCHAR},
            </if>
            <if test="spPostcode != null" >
                #{spPostcode,jdbcType=VARCHAR},
            </if>
            <if test="spUserId != null" >
                #{spUserId,jdbcType=DECIMAL},
            </if>
            <if test="spHandletime != null" >
                #{spHandletime,jdbcType=TIMESTAMP},
            </if>
            <if test="spStatus != null" >
                #{spStatus,jdbcType=VARCHAR},
            </if>
            <if test="spTelephone != null" >
                #{spTelephone,jdbcType=VARCHAR},
            </if>
            <if test="spFax != null" >
                #{spFax,jdbcType=VARCHAR},
            </if>
            <if test="spContractinfo != null" >
                #{spContractinfo,jdbcType=VARCHAR},
            </if>
            <if test="spType != null" >
                #{spType,jdbcType=VARCHAR},
            </if>
            <if test="spCompanyId != null" >
                #{spCompanyId,jdbcType=VARCHAR},
            </if>
            <if test="vendorCode != null" >
                #{vendorCode,jdbcType=VARCHAR},
            </if>
            <if test="taxRate != null" >
                #{taxRate,jdbcType=DECIMAL},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.Supplier" >
        update SUPPLIER
        <set >
            <if test="companyId != null" >
                COMPANY_ID = #{companyId,jdbcType=VARCHAR},
            </if>
            <if test="spCode != null" >
                SP_CODE = #{spCode,jdbcType=VARCHAR},
            </if>
            <if test="spName != null" >
                SP_NAME = #{spName,jdbcType=VARCHAR},
            </if>
            <if test="spEname != null" >
                SP_ENAME = #{spEname,jdbcType=VARCHAR},
            </if>
            <if test="spAddress != null" >
                SP_ADDRESS = #{spAddress,jdbcType=VARCHAR},
            </if>
            <if test="spEaddress != null" >
                SP_EADDRESS = #{spEaddress,jdbcType=VARCHAR},
            </if>
            <if test="spPostcode != null" >
                SP_POSTCODE = #{spPostcode,jdbcType=VARCHAR},
            </if>
            <if test="spUserId != null" >
                SP_USER_ID = #{spUserId,jdbcType=DECIMAL},
            </if>
            <if test="spHandletime != null" >
                SP_HANDLETIME = #{spHandletime,jdbcType=TIMESTAMP},
            </if>
            <if test="spStatus != null" >
                SP_STATUS = #{spStatus,jdbcType=VARCHAR},
            </if>
            <if test="spTelephone != null" >
                SP_TELEPHONE = #{spTelephone,jdbcType=VARCHAR},
            </if>
            <if test="spFax != null" >
                SP_FAX = #{spFax,jdbcType=VARCHAR},
            </if>
            <if test="spContractinfo != null" >
                SP_CONTRACTINFO = #{spContractinfo,jdbcType=VARCHAR},
            </if>
            <if test="spType != null" >
                SP_TYPE = #{spType,jdbcType=VARCHAR},
            </if>
            <if test="spCompanyId != null" >
                SP_COMPANY_ID = #{spCompanyId,jdbcType=VARCHAR},
            </if>
            <if test="vendorCode != null" >
                VENDOR_CODE = #{vendorCode,jdbcType=VARCHAR},
            </if>
            <if test="taxRate != null" >
                TAX_RATE = #{taxRate,jdbcType=DECIMAL},
            </if>
            <if test="bmsStatus != null">
                BMS_STATUS = #{bmsStatus,jdbcType=VARCHAR},
            </if>
        </set>
        where SP_SYSCODE = #{spSyscode,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.Supplier" >
    update SUPPLIER
    set COMPANY_ID = #{companyId,jdbcType=VARCHAR},
      SP_CODE = #{spCode,jdbcType=VARCHAR},
      SP_NAME = #{spName,jdbcType=VARCHAR},
      SP_ENAME = #{spEname,jdbcType=VARCHAR},
      SP_ADDRESS = #{spAddress,jdbcType=VARCHAR},
      SP_EADDRESS = #{spEaddress,jdbcType=VARCHAR},
      SP_POSTCODE = #{spPostcode,jdbcType=VARCHAR},
      SP_USER_ID = #{spUserId,jdbcType=DECIMAL},
      SP_HANDLETIME = #{spHandletime,jdbcType=TIMESTAMP},
      SP_STATUS = #{spStatus,jdbcType=VARCHAR},
      SP_TELEPHONE = #{spTelephone,jdbcType=VARCHAR},
      SP_FAX = #{spFax,jdbcType=VARCHAR},
      SP_CONTRACTINFO = #{spContractinfo,jdbcType=VARCHAR},
      SP_TYPE = #{spType,jdbcType=VARCHAR},
      SP_COMPANY_ID = #{spCompanyId,jdbcType=VARCHAR},
      VENDOR_CODE = #{vendorCode,jdbcType=VARCHAR},
        TAX_RATE = #{taxRate,jdbcType=DECIMAL},
        BMS_STATUS = #{bmsStatus,jdbcType=VARCHAR}
    where SP_SYSCODE = #{spSyscode,jdbcType=VARCHAR}
  </update>
    <select id="selectSupplierList" resultType="com.sinoair.billing.domain.model.billing.Supplier"
            parameterType="java.lang.String">
    select * from SUPPLIER WHERE 1=1 and SP_STATUS='ON' and (COMPANY_ID=#{companyId} or (sp_fax='Y' and sp_type='SINOAIR'))
  </select>

    <select id="selectListByCompanyId" resultType="java.util.Map" parameterType="java.lang.String">
        select SP_CODE,SP_NAME
        from SUPPLIER
        where SP_STATUS = 'ON'
        <if test="spEaddress != null">
            and COMPANY_ID = #{companyId,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="querysupplier" resultType="java.util.Map" parameterType="com.sinoair.billing.domain.model.billing.Supplier">
        select sp.sp_syscode      as sp_syscode,
               sp.sp_name         as sp_name,
               sp.sp_ename        as sp_ename,
               sp.sp_type         as sp_type,
               sp.sp_code         as sp_code,
               sp.vendor_code     as vendor_code,
               sp.tax_rate        as tax_rate,
               sp.sp_postcode     as sp_postcode,
               sp.sp_contractinfo as sp_contractinfo,
               sp.sp_telephone    as sp_telephone,
               sp.sp_fax          as sp_fax,
               case
               when sp.sp_status = 'ON' then
               '是'
               when sp.sp_status = 'OFF' then
               '否'
               end as sp_status
        from supplier sp
        where 1=1
        and sp.company_id = #{companyId}
        <if test="spName!=null and spName!=''">
            and sp.sp_name like '%'|| #{spName} ||'%'
        </if>
        <if test="spCode!=null and spCode!=''">
            and sp.Sp_Code=#{spCode}
        </if>
        <if test="spContractinfo!=null and spContractinfo!=''">
            and sp.sp_contractinfo=#{spContractinfo}
        </if>
        order by sp.sp_handletime desc
    </select>


    <select id="getSeq" resultType="java.lang.String">
    select seq_supplier.nextval from dual
  </select>

    <select id="countSupplierBySpName" parameterType="java.lang.String" resultType="int">
    select count(*) from supplier sp where sp.sp_name=#{spName}
  </select>

    <select id="querySupplierListSelect2" parameterType="java.lang.String" resultType="java.util.Map">
        select sp_code as "id",sp_name as "text",sp_name as "full_name"
        from supplier where 1=1
        <if test="q != null">
            AND UPPER(sp_name) like '%'||UPPER(#{q})||'%'
        </if>
    </select>

    <select id="selectListOnByCompanyIdandSNR" resultType="java.util.Map" parameterType="java.lang.String">
        select SP_CODE,SP_NAME
        from SUPPLIER
        where SP_STATUS = 'ON'
        <if test=" companyId != 'SNR' and companyId != null ">
            and COMPANY_ID = #{companyId,jdbcType=VARCHAR} or COMPANY_ID = 'SNR' or SP_TYPE = '外运发展' or sp_public='Y'
        </if>
        order by SP_NAME
    </select>
    <select id="selectByspCode" resultType="java.util.Map" parameterType="java.lang.String">
        select *
        from SUPPLIER
        where 1=1
        <if test=" soCode != '' and soCode != null ">
            and SP_CODE=#{soCode}
        </if>
    </select>
    <select id="selectSpTypeBySoCode" parameterType="java.lang.String" resultType="java.util.HashMap">
    select sp_type,sp_fax  from SUPPLIER where sp_code=#{soCode}
  </select>

    <select id="selectAllCodeNameForSelector" resultType="java.util.Map" parameterType="java.lang.String">
        select SP_CODE,SP_NAME
        from SUPPLIER
        where SP_STATUS = 'ON'
        <if test="companyId!=null and companyId!=''">
            and (COMPANY_ID = #{companyId,jdbcType=VARCHAR} or COMPANY_ID='SNR'  or
            SP_TYPE='DEPT' or SP_TYPE='SINOAIR' )
        </if>
    </select>

    <select id="selectSNRCodeNameForSelector" resultType="java.util.Map" parameterType="java.lang.String">
        select SP_CODE,SP_NAME
        from SUPPLIER
        where SP_STATUS = 'ON'
        <if test="companyId!=null and companyId!=''">
            and (COMPANY_ID = #{companyId,jdbcType=VARCHAR} or COMPANY_ID='SNR' or
            SP_TYPE='DEPT' or SP_TYPE='SINOAIR')
        </if>
    </select>

    <select id="getSpNameBySpCode" parameterType="java.lang.String" resultType="java.lang.String">
        select SP_NAME from SUPPLIER where SP_CODE=#{spCode}
    </select>

    <select id="selectAutoSpList" resultType="com.sinoair.billing.domain.model.billing.Supplier">
        select distinct sp.sp_code ,pp.company_id ,pp.ct_code
        from price_payment pp,service s,SUPPLIER sp
        where pp.s_id = s.s_id
        and s.so_code =sp.sp_code
        and (sp.company_id = 'ZSU' or SP_TYPE = 'SINOAIR')
        and pp_auto = 'Y'
        and pp_status = 'ON'
        and pp_effectivedate &lt;=  sysdate
        and pp_expireddate >= sysdate

    </select>
</mapper>