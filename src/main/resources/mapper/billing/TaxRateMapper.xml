<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.TaxRateMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.TaxRate" >
    <id column="TR_ID" property="trId" jdbcType="DECIMAL" />
    <result column="TR_NAME" property="trName" jdbcType="VARCHAR" />
    <result column="TR_TYPE" property="trType" jdbcType="VARCHAR" />
    <result column="TR_RATE" property="trRate" jdbcType="DECIMAL" />
  </resultMap>
  <sql id="Base_Column_List" >
    TR_ID, TR_NAME, TR_TYPE, TR_RATE
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from TAX_RATE
    where TR_ID = #{trId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from TAX_RATE
    where TR_ID = #{trId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.TaxRate" >
    insert into TAX_RATE (TR_ID, TR_NAME, TR_TYPE, 
      TR_RATE)
    values (#{trId,jdbcType=DECIMAL}, #{trName,jdbcType=VARCHAR}, #{trType,jdbcType=VARCHAR}, 
      #{trRate,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.TaxRate" >
    insert into TAX_RATE
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="trId != null" >
        TR_ID,
      </if>
      <if test="trName != null" >
        TR_NAME,
      </if>
      <if test="trType != null" >
        TR_TYPE,
      </if>
      <if test="trRate != null" >
        TR_RATE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="trId != null" >
        #{trId,jdbcType=DECIMAL},
      </if>
      <if test="trName != null" >
        #{trName,jdbcType=VARCHAR},
      </if>
      <if test="trType != null" >
        #{trType,jdbcType=VARCHAR},
      </if>
      <if test="trRate != null" >
        #{trRate,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.TaxRate" >
    update TAX_RATE
    <set >
      <if test="trName != null" >
        TR_NAME = #{trName,jdbcType=VARCHAR},
      </if>
      <if test="trType != null" >
        TR_TYPE = #{trType,jdbcType=VARCHAR},
      </if>
      <if test="trRate != null" >
        TR_RATE = #{trRate,jdbcType=DECIMAL},
      </if>
    </set>
    where TR_ID = #{trId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.TaxRate" >
    update TAX_RATE
    set TR_NAME = #{trName,jdbcType=VARCHAR},
      TR_TYPE = #{trType,jdbcType=VARCHAR},
      TR_RATE = #{trRate,jdbcType=DECIMAL}
    where TR_ID = #{trId,jdbcType=DECIMAL}
  </update>

  <select id="selectCreditTax" resultType="java.util.HashMap">
    select DISTINCT TR_RATE  from TAX_RATE where TR_NAME='进项税'
  </select>


  <select id="getCreditTaxByTrName" resultType="java.lang.String" resultMap="BaseResultMap">
    select DISTINCT TR_RATE  from TAX_RATE where TR_NAME=#{trName} order by TR_RATE
  </select>
</mapper>