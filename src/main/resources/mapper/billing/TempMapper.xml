<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.TempMapper" >



  <insert id="insertBatchTemp"  parameterType="java.util.List">
    insert into temp_nhb (eawb_reference1)

    <foreach collection="list" item="item" index="index" separator="UNION ALL">
      select #{item,jdbcType=VARCHAR} from dual
    </foreach>

  </insert>
  <insert id="insertBatchAsendia"  parameterType="java.util.List">
    insert into temp_asendia (eawb_reference1)

    <foreach collection="list" item="item" index="index" separator="UNION ALL">
      select #{item,jdbcType=VARCHAR} from dual
    </foreach>

  </insert>

  <insert id="insertBatchAsendiaResult"  parameterType="java.util.List">
    insert into TEMP_ASENDIA_RESULT (eawb_reference1,eawb_chargeableweight,pr_actual_amount,ep_key)

    <foreach collection="list" item="item" index="index" separator="UNION ALL">
      select #{item.eawbReference1,jdbcType=VARCHAR},#{item.eawbChargeableweight,jdbcType=VARCHAR},
        #{item.prActualAmount,jdbcType=VARCHAR},#{item.epKey,jdbcType=VARCHAR} from dual
    </foreach>

  </insert>

  <delete id="deleteBatchTemp"  parameterType="java.util.List">
    delete from temp_nhb where eawb_reference1 in
    <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
      #{item,jdbcType=VARCHAR}
    </foreach>

  </delete>
  <delete id="deleteTempRr"  parameterType="java.lang.String">
    delete from temp_nhb where eawb_reference1 = #{eawbPrintcode,jdbcType=VARCHAR}

  </delete>

  <delete id="deleteBatchTempLocal"  parameterType="java.util.List">
    delete from temp_nhb_ls where eawb_reference1 in
    <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
      #{item,jdbcType=VARCHAR}
    </foreach>

  </delete>

  <delete id="deleteBatchTempAsendia"  parameterType="java.util.List">
    delete from temp_asendia where eawb_reference1 in
    <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
      #{item,jdbcType=VARCHAR}
    </foreach>

  </delete>

  <select id="selectTemp" resultType="java.lang.String" >
    select eawb_reference1
    FROM temp_nhb
  </select>

  <select id="countTemp" resultType="java.lang.Integer" >
    select count(0)
    FROM temp_nhb
  </select>

  <select id="selectTempLocal" resultType="java.lang.String" >
    select eawb_reference1
    FROM temp_nhb_ls
  </select>

  <select id="countTempRecord" resultType="int" parameterType="com.sinoair.billing.domain.model.billing.ReceiptRecord">
    select count(0)
    from receipt_record rr
    where rr.eawb_printcode=#{eawbPrintcode,jdbcType=VARCHAR}
    and rr.rr_name=#{rrName,jdbcType=VARCHAR}
  </select>

  <select id="selectTempEawb" resultType="com.sinoair.billing.domain.model.billing.ExpressAirWayBill" >
    select eawb.EAWB_PRINTCODE,eawb.EAWB_TRACKING_NO
    FROM temp_nhb n,expressairwaybill eawb
    where n.EAWB_REFERENCE1 = eawb.EAWB_TRACKING_NO
  </select>

  <select id="selectCountPaymentByCode" resultType="java.lang.Integer" >
    select count(0)
    FROM payment_record
    where EAWB_PRINTCODE = #{eawbPrintcode}
  </select>

  <update id="batchUpdateEawbTrackingNo" parameterType="java.util.List">
    <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
      update payment_record
      <set>
          eawb_tracking_no = #{item.eawbTrackingNo},
      </set>
      where
      eawb_printcode=#{item.eawbPrintcode}
    </foreach>
  </update>

  <delete id="deleteBatchTempN"  parameterType="java.util.List">
    delete from temp_rr where rr_id in
    <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
      #{item,jdbcType=VARCHAR}
    </foreach>

  </delete>

<!--  <delete id="deleteBatchRRN"  parameterType="java.util.List">-->
<!--    delete from receipt_record r-->
<!--    where r.rr_id in-->
<!--    <foreach item="item" index="index" collection="list" open="(" separator="," close=")">-->
<!--      #{item,jdbcType=VARCHAR}-->
<!--    </foreach>-->

<!--  </delete>-->

  <select id="selectTempRRN" resultType="java.lang.Long" >
    select rr_id
    FROM temp_rr
  </select>

  <select id="selectTempAsendia" resultType="java.lang.String" >
    select eawb_reference1
    FROM temp_asendia_ls
  </select>

  <select id="selectTempAsendiaResult" resultType="com.sinoair.billing.domain.model.billing.PaymentRecord" >
    select *
    FROM TEMP_ASENDIA_RESULT
  </select>

  <!--select eawb_printcode-->
  <!--FROM temp_m_n-->
  <select id="selectTempMawbEawb" resultType="java.lang.String" >
    select *
    FROM temp_m_n
  </select>
  <delete id="deleteBatchTempMawbEawb"  parameterType="java.util.List">
    delete from temp_m_n where eawb_printcode in
    <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
      #{item,jdbcType=VARCHAR}
    </foreach>

  </delete>

  <insert id="insertTempErr"  parameterType="java.lang.String">
    insert into temp_nhb_err (eawb_printcode) VALUES (#{eawbPrintcode,jdbcType=VARCHAR})
  </insert>

  <select id="selectTempEa" resultType="java.lang.String" >
    select *
    FROM CHECK_EAWB_EA
  </select>

  <delete id="deleteBatchTempEa"  parameterType="java.util.List">
    delete from CHECK_EAWB_EA where ea_code in
    <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
      #{item,jdbcType=VARCHAR}
    </foreach>

  </delete>
  <insert id="insertTempEa"  parameterType="java.lang.String">
    insert into CHECK_EAWB_EA (ea_code) VALUES (#{ea_code,jdbcType=VARCHAR})
  </insert>
  <insert id="insertBatchTempEa"  parameterType="java.util.List">
    insert into CHECK_EAWB_EA (ea_code)
    <foreach collection="list" item="item" index="index" separator="UNION ALL">
      select #{item,jdbcType=VARCHAR} from dual
    </foreach>
  </insert>

  <update id="batchUpdateEawbSacId" parameterType="java.util.List">
    <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
      update expressairwaybill
      <set>
        sac_id = #{item.sacId},
      </set>
      where
      eawb_printcode=#{item.eawbPrintcode}
    </foreach>
  </update>

  <update id="batchUpdateRRSacId" parameterType="java.util.List">
    <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
      update receipt_record
      <set>
        company_id = #{item.sacId},
      </set>
      where
      eawb_printcode=#{item.eawbPrintcode}
    </foreach>
  </update>

  <update id="batchUpdateRROtherSacId" parameterType="java.util.List">
    <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
      update receipt_record_other
      <set>
        company_id = #{item.sacId},
      </set>
      where
      eawb_printcode=#{item.eawbPrintcode}
    </foreach>
  </update>

  <update id="batchUpdatePrSacId" parameterType="java.util.List">
    <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
      update payment_record
      <set>
        company_id = #{item.sacId},
      </set>
      where
      eawb_printcode=#{item.eawbPrintcode}
    </foreach>
  </update>
  <!-- and sac_id is null -->
  <select id="listEawbSacIdEmpty" resultType="com.sinoair.billing.domain.model.billing.ExpressAirWayBill" >
    select eawb_printcode,eawb_so_code from expressairwaybill
    where 1=1
      and sac_id is null
    and eawb_handletime>=to_date('20210301','yyyymmdd')
    <![CDATA[
    and eawb_handletime<to_date('20210401','yyyymmdd')
    ]]>
    <![CDATA[
    and rownum <= 100000
    ]]>
  </select>

  <insert id="insertBatchEbaIn"  parameterType="java.util.List">
    insert into CHECK_EBA_IN (eawb_printcode)

    <foreach collection="list" item="item" index="index" separator="UNION ALL">
      select #{item,jdbcType=VARCHAR} from dual
    </foreach>

  </insert>

  <insert id="insertBatchEbaAdc"  parameterType="java.util.List">
    insert into CHECK_EBA_ADC (eawb_printcode)

    <foreach collection="list" item="item" index="index" separator="UNION ALL">
      select #{item,jdbcType=VARCHAR} from dual
    </foreach>

  </insert>

  <insert id="insertBatchTempLpInfoWeight"  parameterType="java.util.List">
    insert into temp_sino_time_lp_info_weight (LP_NO,TRACK_NO,EP_KEY,PRICE_VALUE)

    <foreach collection="list" item="item" index="index" separator="UNION ALL">
      select #{item.lpNo,jdbcType=VARCHAR},#{item.trackNo,jdbcType=VARCHAR}
           ,#{item.epKey,jdbcType=VARCHAR},#{item.priceValue,jdbcType=DECIMAL} from dual
    </foreach>

  </insert>

  <insert id="insertBatchTempAir"  parameterType="java.util.List">
    insert into temp_air (EA_CODE,PKG_PRINTCODE,EP_KEY,EAWB_REFERENCE2,EAWB_REFERENCE1,
    EAWB_KEYENTRYTIME,WEIGHT)

    <foreach collection="list" item="item" index="index" separator="UNION ALL">
      select #{item.eaCode,jdbcType=VARCHAR},#{item.pkgPrintcode,jdbcType=VARCHAR}
      ,#{item.epKey,jdbcType=VARCHAR},#{item.eawbReference2,jdbcType=VARCHAR}
      ,#{item.eawbReference1,jdbcType=VARCHAR},#{item.eawbKeyentrytime,jdbcType=VARCHAR}
      ,#{item.weight,jdbcType=VARCHAR} from dual
    </foreach>

  </insert>

  <insert id="insertBatchTempTrain"  parameterType="java.util.List">
    insert into temp_train (EA_CODE,PKG_PRINTCODE,EP_KEY,EAWB_REFERENCE2,EAWB_REFERENCE1,
    EAWB_KEYENTRYTIME,WEIGHT)

    <foreach collection="list" item="item" index="index" separator="UNION ALL">
      select #{item.eaCode,jdbcType=VARCHAR},#{item.pkgPrintcode,jdbcType=VARCHAR}
      ,#{item.epKey,jdbcType=VARCHAR},#{item.eawbReference2,jdbcType=VARCHAR}
      ,#{item.eawbReference1,jdbcType=VARCHAR},#{item.eawbKeyentrytime,jdbcType=VARCHAR}
      ,#{item.weight,jdbcType=VARCHAR} from dual
    </foreach>

  </insert>
</mapper>