<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.TransmodeServicePayMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.TransmodeServicePay" >
    <result column="TRANSMODEID" property="transmodeid" jdbcType="VARCHAR" />
    <result column="S_ID" property="sId" jdbcType="DECIMAL" />
  </resultMap>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.TransmodeServicePay" >
    insert into TRANSMODE_SERVICE_PAY (TRANSMODEID, S_ID)
    values (#{transmodeid,jdbcType=VARCHAR}, #{sId,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.TransmodeServicePay" >
    insert into TRANSMODE_SERVICE_PAY
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="transmodeid != null" >
        TRANSMODEID,
      </if>
      <if test="sId != null" >
        S_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="transmodeid != null" >
        #{transmodeid,jdbcType=VARCHAR},
      </if>
      <if test="sId != null" >
        #{sId,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
</mapper>