<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.TrapsRRConfigMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.TrapsRRConfig" >
    <result column="TRAPS_ID" property="trapsId" jdbcType="DECIMAL" />
    <result column="START_PAGE" property="startPage" jdbcType="DECIMAL" />
    <result column="END_PAGE" property="endPage" jdbcType="DECIMAL" />
    <result column="PAGE_SIZE" property="pageSize" jdbcType="DECIMAL" />
    <result column="EAD_CODE" property="eadCode" jdbcType="VARCHAR" />
    <result column="EAST_CODE" property="eastCode" jdbcType="VARCHAR" />
    <result column="RR_NAME" property="rrName" jdbcType="VARCHAR" />
    <result column="PRINTCODE_TYPE" property="printcodeType" jdbcType="VARCHAR" />
  </resultMap>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.TrapsRRConfig" >
    insert into TRAPS_RR_CONFIG (TRAPS_ID, START_PAGE, END_PAGE, 
      PAGE_SIZE, EAD_CODE, EAST_CODE, 
      RR_NAME, PRINTCODE_TYPE)
    values (#{trapsId,jdbcType=DECIMAL}, #{startPage,jdbcType=DECIMAL}, #{endPage,jdbcType=DECIMAL}, 
      #{pageSize,jdbcType=DECIMAL}, #{eadCode,jdbcType=VARCHAR}, #{eastCode,jdbcType=VARCHAR}, 
      #{rrName,jdbcType=VARCHAR}, #{printcodeType,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.TrapsRRConfig" >
    insert into TRAPS_RR_CONFIG
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="trapsId != null" >
        TRAPS_ID,
      </if>
      <if test="startPage != null" >
        START_PAGE,
      </if>
      <if test="endPage != null" >
        END_PAGE,
      </if>
      <if test="pageSize != null" >
        PAGE_SIZE,
      </if>
      <if test="eadCode != null" >
        EAD_CODE,
      </if>
      <if test="eastCode != null" >
        EAST_CODE,
      </if>
      <if test="rrName != null" >
        RR_NAME,
      </if>
      <if test="printcodeType != null" >
        PRINTCODE_TYPE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="trapsId != null" >
        #{trapsId,jdbcType=DECIMAL},
      </if>
      <if test="startPage != null" >
        #{startPage,jdbcType=DECIMAL},
      </if>
      <if test="endPage != null" >
        #{endPage,jdbcType=DECIMAL},
      </if>
      <if test="pageSize != null" >
        #{pageSize,jdbcType=DECIMAL},
      </if>
      <if test="eadCode != null" >
        #{eadCode,jdbcType=VARCHAR},
      </if>
      <if test="eastCode != null" >
        #{eastCode,jdbcType=VARCHAR},
      </if>
      <if test="rrName != null" >
        #{rrName,jdbcType=VARCHAR},
      </if>
      <if test="printcodeType != null" >
        #{printcodeType,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <update id="updateTrapsRRConfig" parameterType="com.sinoair.billing.domain.model.billing.TrapsRRConfig" >
    update TRAPS_RR_CONFIG
    <set >
      <if test="startPage != null" >
        START_PAGE = #{startPage,jdbcType=DECIMAL},
      </if>
      <if test="endPage != null" >
        END_PAGE = #{endPage,jdbcType=DECIMAL},
      </if>
      <if test="pageSize != null" >
        PAGE_SIZE = #{pageSize,jdbcType=DECIMAL},
      </if>

    </set>
    where TRAPS_ID = #{trapsId,jdbcType=DECIMAL}

  </update>

    <select id="selectById" resultType="com.sinoair.billing.domain.model.billing.TrapsRRConfig" parameterType="java.lang.Integer" >
        select
            TRAPS_ID, START_PAGE, END_PAGE,
          PAGE_SIZE, EAD_CODE, EAST_CODE,
          RR_NAME, PRINTCODE_TYPE,so_type
        from TRAPS_RR_CONFIG
        where TRAPS_ID = #{trapsId,jdbcType=DECIMAL}
    </select>
</mapper>