<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.UserMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.User" >
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="USERNAME" property="username" jdbcType="VARCHAR" />
    <result column="PASSWORD" property="password" jdbcType="VARCHAR" />
    <result column="REALNAME" property="realname" jdbcType="VARCHAR" />
    <result column="SEX" property="sex" jdbcType="DECIMAL" />
    <result column="BIRTHDAY" property="birthday" jdbcType="TIMESTAMP" />
    <result column="DESCRIPTION" property="description" jdbcType="VARCHAR" />
    <result column="STATUS" property="status" jdbcType="VARCHAR" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="LAST_LOGIN_TIME" property="lastLoginTime" jdbcType="TIMESTAMP" />
    <result column="COMPANY_ID" property="companyId" jdbcType="VARCHAR" />
    <result column="OPEN_ID" property="openId" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, USERNAME, PASSWORD, REALNAME, SEX, BIRTHDAY, DESCRIPTION, STATUS, CREATE_TIME,
    LAST_LOGIN_TIME, COMPANY_ID, OPEN_ID
  </sql>

  <!-- 序列 -->
  <sql id='TABLE_SEQUENCE'>SEQ_USERS.NEXTVAL</sql>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select
    <include refid="Base_Column_List" />
    from USERS
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from USERS
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.User" >
    <selectKey keyProperty="id" resultType="int" order="BEFORE">
      select <include refid="TABLE_SEQUENCE" /> from dual
    </selectKey>
    insert into USERS (ID, USERNAME, PASSWORD,
    REALNAME, SEX, BIRTHDAY,
    DESCRIPTION, STATUS, CREATE_TIME,
    LAST_LOGIN_TIME, COMPANY_ID, OPEN_ID
    )
    values (#{id,jdbcType=DECIMAL}, #{username,jdbcType=VARCHAR}, #{password,jdbcType=VARCHAR},
    #{realname,jdbcType=VARCHAR}, #{sex,jdbcType=DECIMAL}, #{birthday,jdbcType=TIMESTAMP},
    #{description,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
    #{lastLoginTime,jdbcType=TIMESTAMP}, #{companyId,jdbcType=VARCHAR}, #{openId,jdbcType=VARCHAR}
    )
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.User" >
    <selectKey keyProperty="id" resultType="int" order="BEFORE">
      select <include refid="TABLE_SEQUENCE" /> from dual
    </selectKey>
    insert into USERS
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="username != null" >
        USERNAME,
      </if>
      <if test="password != null" >
        PASSWORD,
      </if>
      <if test="realname != null" >
        REALNAME,
      </if>
      <if test="sex != null" >
        SEX,
      </if>
      <if test="birthday != null" >
        BIRTHDAY,
      </if>
      <if test="description != null" >
        DESCRIPTION,
      </if>
      <if test="status != null" >
        STATUS,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
      <if test="lastLoginTime != null" >
        LAST_LOGIN_TIME,
      </if>
      <if test="companyId != null" >
        COMPANY_ID,
      </if>
      <if test="openId != null" >
        OPEN_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="username != null" >
        #{username,jdbcType=VARCHAR},
      </if>
      <if test="password != null" >
        #{password,jdbcType=VARCHAR},
      </if>
      <if test="realname != null" >
        #{realname,jdbcType=VARCHAR},
      </if>
      <if test="sex != null" >
        #{sex,jdbcType=DECIMAL},
      </if>
      <if test="birthday != null" >
        #{birthday,jdbcType=TIMESTAMP},
      </if>
      <if test="description != null" >
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastLoginTime != null" >
        #{lastLoginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null" >
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="openId != null" >
        #{openId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.billing.User" >
    update USERS
    <set >
      <if test="username != null" >
        USERNAME = #{username,jdbcType=VARCHAR},
      </if>
      <if test="password != null" >
        PASSWORD = #{password,jdbcType=VARCHAR},
      </if>
      <if test="realname != null" >
        REALNAME = #{realname,jdbcType=VARCHAR},
      </if>
      <if test="sex != null" >
        SEX = #{sex,jdbcType=DECIMAL},
      </if>
      <if test="birthday != null" >
        BIRTHDAY = #{birthday,jdbcType=TIMESTAMP},
      </if>
      <if test="description != null" >
        DESCRIPTION = #{description,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        STATUS = #{status,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastLoginTime != null" >
        LAST_LOGIN_TIME = #{lastLoginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null" >
        COMPANY_ID = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="openId != null" >
        OPEN_ID = #{openId,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.User" >
    update USERS
    set USERNAME = #{username,jdbcType=VARCHAR},
    PASSWORD = #{password,jdbcType=VARCHAR},
    REALNAME = #{realname,jdbcType=VARCHAR},
    SEX = #{sex,jdbcType=DECIMAL},
    BIRTHDAY = #{birthday,jdbcType=TIMESTAMP},
    DESCRIPTION = #{description,jdbcType=VARCHAR},
    STATUS = #{status,jdbcType=VARCHAR},
    CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
    LAST_LOGIN_TIME = #{lastLoginTime,jdbcType=TIMESTAMP},
    COMPANY_ID = #{companyId,jdbcType=VARCHAR},
    OPEN_ID = #{openId,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=DECIMAL}
  </update>

  <!-- 根据用户名获取用户信息 -->
  <select id="getUserListByUserName" resultType="com.sinoair.billing.domain.model.billing.User" parameterType="java.lang.String">
    select * from USERS where username = #{username}
  </select>
  <select id="selectUserList" resultMap="BaseResultMap" parameterType="com.sinoair.billing.domain.vo.FilterParam">
    select
    <include refid="Base_Column_List" />
    from USERS
    where 1=1
    <if test="username != null and username != ''" >
      AND  UPPER(username) like '%'||UPPER(#{username})||'%'
    </if>
    <if test="status != 'NAN' and status != null" >
      and status = #{status}
    </if>
    <if test="id != null and id != 0" >
      and id = #{id}
    </if>
    <if test="code1 != null and code1 != 0" >
      and id != #{code1}
    </if>
    <if test=" pid  == 1">
      and (
      COMPANY_ID IN (select dc.COMPANY_ID from company dc where dc.COMPANY_P_ID = #{companyId} )
      or
      COMPANY_ID = #{companyId}
      )
    </if>
    <if test=" pid != 0 and pid != 1 ">
      and COMPANY_ID = #{companyId}
    </if>
    order by id DESC
  </select>
  <select id="selectUserListMap" resultType="java.util.HashMap" parameterType="com.sinoair.billing.domain.vo.FilterParam">
    select
    u.username as "username",u.realname as "realname",u.sex as "sex",r.name as "rolename",u.status as "status",u.create_time as "createTime",u.id as "id"
    from USERS u,ROLE r,USERS_ROLE ur
    where 1=1
    and u.id = ur.user_id(+)
    and ur.role_id = r.id(+)
    <if test="username != null and username != ''" >
      AND  UPPER(username) like '%'||UPPER(#{username})||'%'
    </if>
    <if test="status != 'NAN' and status != null" >
      and status = #{status}
    </if>
    <if test="id != null and id != 0" >
      and u.id = #{id}
    </if>
    <if test="code1 != null and code1 != 0" >
      and u.id != #{code1}
    </if>
    <if test=' pid  ==  "SNR" '>
      and (
      u.COMPANY_ID IN (select dc.COMPANY_ID from company dc where dc.COMPANY_P_ID = #{companyId} )
      or
      u.COMPANY_ID = #{companyId}
      )
    </if>
    <if test=' pid != "0" and pid != "SNR" '>
      and u.COMPANY_ID = #{companyId}
    </if>
    order by u.id DESC
  </select>
  <!--根据主单号查询操作人-->
  <select id="selectUserByAwbCode" resultType="com.sinoair.billing.domain.model.billing.User" parameterType="java.lang.String">
    SELECT DU.REALNAME FROM HAWB DA,USERS DU WHERE DA.AWB_HANDLER = DU.ID AND  DA.AWB_CODE=#{awbCode,jdbcType=VARCHAR}
  </select>
</mapper>