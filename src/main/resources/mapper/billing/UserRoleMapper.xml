<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.billing.UserRoleMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.billing.UserRoleKey" >
    <id column="ROLE_ID" property="roleId" jdbcType="DECIMAL" />
    <id column="USER_ID" property="userId" jdbcType="DECIMAL" />
  </resultMap>
  <delete id="deleteByPrimaryKey" parameterType="com.sinoair.billing.domain.model.billing.UserRoleKey" >
    delete from USERS_ROLE
    where ROLE_ID = #{roleId,jdbcType=DECIMAL}
      and USER_ID = #{userId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.billing.UserRoleKey" >
    insert into USERS_ROLE (ROLE_ID, USER_ID)
    values (#{roleId,jdbcType=DECIMAL}, #{userId,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.billing.UserRoleKey" >
    insert into USERS_ROLE
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="roleId != null" >
        ROLE_ID,
      </if>
      <if test="userId != null" >
        USER_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="roleId != null" >
        #{roleId,jdbcType=DECIMAL},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>

  <select id="selectUserRoleKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
    select * from USERS_ROLE where USER_ID = #{userId}
  </select>

  <update id="updateUserRoleKey" parameterType="com.sinoair.billing.domain.model.billing.UserRoleKey">
    update USERS_ROLE
    set ROLE_ID = #{roleId}
    where USER_ID = #{userId}
  </update>
</mapper>