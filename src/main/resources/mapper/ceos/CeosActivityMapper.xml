<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.billing.dao.ceos.CeosActivityMapper">

    <sql id="Base_Column_List" >
          e.EAWB_PRINTCODE as eawbPrintcode,
          e.EAWB_SYSCODE as eawbSyscode,
          EAD_CODE as eadCode,
          case when EAST_CODE='CP' then 'CPT' else EAST_CODE end as eastCode,
          EBA_E_ID_HANDLER as ebaEIdHandler,
          nvl(ebap_pushtime,EBA_OCCURTIME) as ebaHandletime,
          EBA_REMARK as ebaRemark,
          e.sac_id,
          EBA_OCCURTIME,
          EBA_SOURCE,
          EBA_OCCURPLACE,
          FLAG,
          QA,
          UK_TRACK,
          EAT_PARTNER_ACTIVITY_CODE,
          EAT_PARTNER_ID,
          EBA_SAC_CODE,
          ebap_pushtime as qaPushtime,
          QA2,
          QA2_PUSHTIME,
          QA3,
          QA3_PUSHTIME,
          e.eba_syscode as ceosEbaSyscode

    </sql>

    <select id="selectCeosActivityPush" resultType="com.sinoair.billing.domain.model.billing.ExpressBusinessActivity"
            parameterType="com.sinoair.billing.domain.vo.query.CeosQuery">

        SELECT
          <include refid="Base_Column_List" />
        FROM EXPRESSBUSINESSACTIVITY E,expressbusinessactivitypush ep
        WHERE  e.eba_syscode=ep.eba_syscode
        <if test="eadCode != null and eadCode != ''">
            and e.EAD_CODE=#{eadCode}
        </if>
        <if test="eadCodes != null and eadCodes != ''">
            and e.EAD_CODE in (${eadCodes})
        </if>
        <if test="eastCode != null and eastCode != ''">
            and e.EAST_CODE=#{eastCode}
        </if>
        <if test="eastCodes != null and eastCodes != ''">
            and e.EAST_CODE in (${eastCodes})
        </if>
        and ep.ebap_pushtime >=to_date(${startEbaHandletime},'yyyymmdd')
        <![CDATA[
        and ep.ebap_pushtime <to_date(${endEbaHandletime},'yyyymmdd')
        ]]>

    </select>

    <select id="countCeosActivityPush" resultType="java.lang.Integer"
            parameterType="com.sinoair.billing.domain.vo.query.CeosQuery">

        SELECT
        count(0)
        FROM EXPRESSBUSINESSACTIVITY E,expressbusinessactivitypush ep
        WHERE  e.eba_syscode=ep.eba_syscode
        <if test="eadCode != null and eadCode != ''">
            and e.EAD_CODE=#{eadCode}
        </if>
        <if test="eadCodes != null and eadCodes != ''">
            and e.EAD_CODE in (${eadCodes})
        </if>
        <if test="eastCode != null and eastCode != ''">
            and e.EAST_CODE=#{eastCode}
        </if>
        <if test="eastCodes != null and eastCodes != ''">
            and e.EAST_CODE in (${eastCodes})
        </if>
        and ep.ebap_pushtime >=to_date(${startEbaHandletime},'yyyymmdd')
        <![CDATA[
        and ep.ebap_pushtime <to_date(${endEbaHandletime},'yyyymmdd')
        ]]>

    </select>

    <!--
            <choose>
            <when test="handleType =='CN_EBA_INBOUND_CN' ">
                EBA_OCCURTIME
            </when >
            <when test="handleType == 'CN_EBA_NON_CN' ">
                EBA_OCCURTIME
            </when >
            <when test="handleType == 'CN_EBA' ">
                nvl(EBA_OCCURTIME,sysdate)
            </when >
            <otherwise>
                EBA_OCCURTIME
            </otherwise>
        </choose> as ebaHandletime,
    -->
    <select id="selectCeosActivity" resultType="com.sinoair.billing.domain.model.billing.ExpressBusinessActivity"
            parameterType="com.sinoair.billing.domain.vo.query.CeosQuery">

        SELECT
        e.EAWB_PRINTCODE as eawbPrintcode,
        e.EAWB_SYSCODE as eawbSyscode,
        EAD_CODE as eadCode,
        EAST_CODE as eastCode,
        EBA_E_ID_HANDLER as ebaEIdHandler,
        EBA_HANDLETIME as ebaHandletime,
        EBA_REMARK as ebaRemark,
        e.sac_id,
        EBA_OCCURTIME as ebaOccurtime,
        EBA_SOURCE,
        EBA_OCCURPLACE,
        FLAG,
        QA,
        UK_TRACK,
        EAT_PARTNER_ACTIVITY_CODE,
        EAT_PARTNER_ID,
        EBA_SAC_CODE,
        (case when EAD_CODE='GFC_OUTBOUND' then QA_PUSHTIME
            else EBA_OCCURTIME end) as qaPushtime,
        QA2,
        QA2_PUSHTIME,
        QA3,
        QA3_PUSHTIME,
        e.eba_syscode as ceosEbaSyscode,
        b.EAWB_SYSCODE as soCode,
        b.EAWB_PRINTCODE            as eawbPrintcode,
        b.eawb_servicetype_original as eawbServiceTypeOriginal,
        b.EAWB_SO_CODE              as eawbSoCode,
        b.EAWB_DESTCOUNTRY          as eawbDestcountry,
        b.EAWB_DELIVER_POSTCODE     as eawbDeliverPostcode
        FROM EXPRESSBUSINESSACTIVITY E, expressairwaybill b
        WHERE  b.EAWB_SYSCODE = e.EAWB_SYSCODE
        <if test="soCode != null and soCode != ''">
            and b.eawb_so_code != #{soCode}
        </if>
        <if test="eadCode != null and eadCode != ''">
            and e.EAD_CODE=#{eadCode}
        </if>
        <if test="eadCodes != null and eadCodes != ''">
            and e.EAD_CODE in (${eadCodes})
        </if>
        <if test="eastCode != null and eastCode != ''">
            and e.EAST_CODE=#{eastCode}
        </if>
        <if test="eastCodes != null and eastCodes != ''">
            and e.EAST_CODE in (${eastCodes})
        </if>
        <if test="startEbaHandletime != null and startEbaHandletime != ''">
            and e.EBA_HANDLETIME >=to_date(${startEbaHandletime},'yyyymmdd')
            <![CDATA[
            and e.EBA_HANDLETIME <to_date(${endEbaHandletime},'yyyymmdd')
            ]]>
        </if>
        <if test="beginNo != null">
            and e.eba_syscode >= #{beginNo}
            <![CDATA[
            and e.eba_syscode < #{endNo}
            ]]>
        </if>
        <if test="eawbPrintcode != null and eawbPrintcode != ''">
            and e.EAWB_PRINTCODE=#{eawbPrintcode}
        </if>
    </select>
    <select id="selectCeosActivityCount" resultType="java.lang.Integer"
            parameterType="com.sinoair.billing.domain.vo.query.CeosQuery">

        SELECT
        count(0)
        FROM EXPRESSBUSINESSACTIVITY E
        WHERE  1=1
        <if test="eadCode != null and eadCode != ''">
            and e.EAD_CODE=#{eadCode}
        </if>
        <if test="eadCodes != null and eadCodes != ''">
            and e.EAD_CODE in (${eadCodes})
        </if>
        <if test="eastCode != null and eastCode != ''">
            and e.EAST_CODE=#{eastCode}
        </if>
        <if test="eastCodes != null and eastCodes != ''">
            and e.EAST_CODE in (${eastCodes})
        </if>
        <if test="startEbaHandletime != null and startEbaHandletime != ''">
            and e.EBA_HANDLETIME >=to_date(${startEbaHandletime},'yyyymmdd')
            <![CDATA[
            and e.EBA_HANDLETIME <to_date(${endEbaHandletime},'yyyymmdd')
            ]]>
        </if>
        <if test="beginNo != null">
            and e.eba_syscode >=#{beginNo}
            <![CDATA[
            and e.eba_syscode <#{endNo}
            ]]>
        </if>
    </select>

    <select id="selectCeosActivityMax" resultType="decimal"
            parameterType="com.sinoair.billing.domain.vo.query.CeosQuery">
        SELECT
        max (e.eba_syscode)
        FROM EXPRESSBUSINESSACTIVITY E
        WHERE  1=1
        <if test="beginNo != null">
            and e.eba_syscode >=#{beginNo}
            <![CDATA[
            and e.eba_syscode <#{endNo}
            ]]>
        </if>
    </select>

    <select id="countCeosActivity" resultType="java.lang.Integer" parameterType="com.sinoair.billing.domain.vo.query.ActivityQuery">
        select count(0)
        from EXPRESSBUSINESSACTIVITY
        where eawb_printcode=#{eawbPrintcode,jdbcType=VARCHAR}
        <if test="eadCode != null" >
            and EAD_CODE=#{eadCode,jdbcType=VARCHAR}
        </if>
        <if test="eastCode != null" >
            and EAST_CODE=#{eastCode,jdbcType=VARCHAR}
        </if>
        <if test="isCheckDate != null" >
            <![CDATA[
            and eba_handletime < trunc(sysdate-1,'dd')
            ]]>
        </if>
    </select>

    <select id="selectCheckCeosActivity" resultType="com.sinoair.billing.domain.model.billing.ExpressBusinessActivity"
            parameterType="com.sinoair.billing.domain.vo.query.CeosQuery">
        SELECT
        eba.EAWB_PRINTCODE as eawbPrintcode,
        eba.EAD_CODE as eadCode,
        eba.EAST_CODE as eastCode,
        eba.EBA_HANDLETIME as ebaHandletime
        FROM
        expressbusinessactivity eba
        WHERE  1=1
        and eba.EBA_HANDLETIME >=to_date(${startEbaHandletime},'yyyymmdd')
        <![CDATA[
        and eba.EBA_HANDLETIME <to_date(${endEbaHandletime},'yyyymmdd')
        ]]>

    </select>
    <!--
    e.eawb_printcode = eba.eawb_printcode
        and e.eawb_so_code=#{soCode}
          and eba.ead_code in ('INTERNATIONAL','FC_INBOUND')
         and eba.east_code in ('ASS','CP','CPT','ROE','ASF','OK')
    -->

    <select id="countCheckCeosActivity" resultType="java.lang.Integer" parameterType="com.sinoair.billing.domain.vo.query.CeosQuery">
        select count(eba.eba_syscode)
        FROM
        expressbusinessactivity eba
        WHERE 1=1
        and eba.EBA_HANDLETIME >=to_date(${startEbaHandletime},'yyyymmdd')
        <![CDATA[
        and eba.EBA_HANDLETIME <to_date(${endEbaHandletime},'yyyymmdd')
        ]]>
    </select>

    <select id="selectCeosActivityTemp" resultType="com.sinoair.billing.domain.model.billing.ExpressBusinessActivity"
            parameterType="com.sinoair.billing.domain.vo.query.ActivityQuery">

        SELECT
        <include refid="Base_Column_List" />
        FROM EXPRESSBUSINESSACTIVITY E,expressbusinessactivitypush ep
        WHERE  e.eba_syscode=ep.eba_syscode
           and e.eawb_printcode=#{eawbPrintcode}
           and e.EAST_CODE=#{eastCode}


    </select>

    <select id="countCeosActivityTemp" resultType="java.lang.Integer"
            parameterType="com.sinoair.billing.domain.vo.query.ActivityQuery">

        SELECT
        count(0)
        FROM EXPRESSBUSINESSACTIVITY E,expressbusinessactivitypush ep
        WHERE  e.eba_syscode=ep.eba_syscode
        and e.eawb_printcode=#{eawbPrintcode}
        and e.EAST_CODE=#{eastCode}

    </select>


    <select id="selectCeosActivityPushBySysCode" resultType="com.sinoair.billing.domain.model.billing.ExpressBusinessActivity"
            parameterType="com.sinoair.billing.domain.vo.query.CeosQuery">

        SELECT
        <include refid="Base_Column_List" />
        FROM EXPRESSBUSINESSACTIVITY E,expressbusinessactivitypush ep
        WHERE  e.eba_syscode=ep.eba_syscode
        and ep.ebap_syscode >= #{beginNo}
        <![CDATA[
        and ep.ebap_syscode < #{endNo}
        ]]>

    </select>

    <select id="countCeosActivityPushBySysCode" resultType="java.lang.Integer"
            parameterType="com.sinoair.billing.domain.vo.query.CeosQuery">

        SELECT
        count(0)
        FROM EXPRESSBUSINESSACTIVITY E,expressbusinessactivitypush ep
        WHERE  e.eba_syscode=ep.eba_syscode
        and ep.ebap_syscode >= #{beginNo}
        <![CDATA[
        and ep.ebap_syscode < #{endNo}
        ]]>

    </select>

    <select id="selectMaxEbapSysCode" resultType="java.lang.Long">
        SELECT
          max(ep.ebap_syscode)
        FROM expressbusinessactivitypush ep
    </select>

    <select id="selectMaxEbaCode" resultType="java.lang.Long">
        SELECT
            max(eba.eba_syscode)
        FROM EXPRESSBUSINESSACTIVITY eba
    </select>


    <select id="selectCeosActivitySoCode" resultType="com.sinoair.billing.domain.model.billing.ExpressBusinessActivity"
            parameterType="com.sinoair.billing.domain.vo.query.CeosQuery">

        SELECT
            e.EAWB_PRINTCODE as eawbPrintcode,
            e.EAWB_SYSCODE as eawbSyscode,
            EAD_CODE as eadCode,
            EAST_CODE as eastCode,
            EBA_E_ID_HANDLER as ebaEIdHandler,
            EBA_HANDLETIME as ebaHandletime,
            EBA_REMARK as ebaRemark,
            e.sac_id,
            EBA_OCCURTIME as ebaOccurtime,
            EBA_SOURCE,
            EBA_OCCURPLACE,
            FLAG,
            QA,
            UK_TRACK,
            EAT_PARTNER_ACTIVITY_CODE,
            EAT_PARTNER_ID,
            EBA_SAC_CODE,
            (case when EAD_CODE='GFC_OUTBOUND' then QA_PUSHTIME
                  else EBA_OCCURTIME end) as qaPushtime,
            QA2,
            QA2_PUSHTIME,
            QA3,
            QA3_PUSHTIME,
            e.eba_syscode as ceosEbaSyscode,
            (select eawb_so_code from eawbpre where EAWB_SYSCODE = e.EAWB_SYSCODE ) as soCode
        FROM EXPRESSBUSINESSACTIVITY E
        WHERE  1=1
          and EAWB_PRINTCODE in (
                select eawb_printcode from eawbpre where eawb_so_code = #{soCode}
            )
        <if test="startEbaHandletime != null and startEbaHandletime != ''">
            and e.EBA_HANDLETIME >=to_date(${startEbaHandletime},'yyyymmdd')
        </if>

    </select>

    <select id="countCeosActivitySoCode" resultType="java.lang.Integer"
            parameterType="com.sinoair.billing.domain.vo.query.CeosQuery">

        SELECT
            count(0)
        FROM EXPRESSBUSINESSACTIVITY E
        WHERE  1=1
          and EAWB_PRINTCODE in (
            select eawb_printcode from eawbpre where eawb_so_code = #{soCode}
        )
        <if test="startEbaHandletime != null and startEbaHandletime != ''">
            and e.EBA_HANDLETIME >=to_date(${startEbaHandletime},'yyyymmdd')
        </if>

    </select>
</mapper>

<!--        select so_code from settlementobject where sac_id in ('ZSU','ZSH')-->
<!--        and so_code !=  'ZSU168192'-->