<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.billing.dao.ceos.CeosEawbCustomTaxMapper">

    <sql id="Base_Column_List" >

    </sql>


    <select id="selectMaxEawbTaxSysCode" resultType="java.lang.Long" parameterType="java.lang.String">
        select eawbcit_syscode from (
        select eawbcit_syscode from eawb_custome_item_tax
        where 1=1
        order by eawbcit_syscode desc )
        where
        <![CDATA[
        rownum < 2
        ]]>
    </select>


    <select id="countCeosEawbTaxBySysCode" resultType="java.lang.Integer"
            parameterType="com.sinoair.billing.domain.vo.query.CeosQuery">

        select
          count(0)
        from eawb_custome_item_tax eawb
        where 1 =1
        and eawbcit_syscode > #{beginNo}
        <![CDATA[
         and eawbcit_syscode <= #{endNo}
         ]]>

    </select>

    <select id="selectCeosEawbTaxBySysCode" resultType="com.sinoair.billing.domain.model.billing.EawbCustomTax"
            parameterType="com.sinoair.billing.domain.vo.query.CeosQuery">

        select
          e.tracking_number,
          e.logisticsordercode,
          e.ct_code,
          e.eawb_custcurrency,
          e.eawb_custdeclval,
          e.eawb_keyentrytime,
          e.eawbc_syscode,
          c.eawbci_syscode,
          t.eawbcit_syscode,
          t.hscode,
          t.mawb_code,
          t.eawbcit_taxitemtype,
          t.eawbcit_taxes,
          t.eawbcit_taxtate,
          t.eawbcit_createtime,
          t.eawbcit_push_status,
          t.eawbcit_push_time,
          t.eawbcit_paycurrency,
          t.eawbcit_exchange_rate
        from eawb_custome_item_tax t,eawb_custom_item c,eawb_custom e
        where t.eawbci_syscode = c.eawbci_syscode
         and c.eawbc_syscode = e.eawbc_syscode
         and t.eawbcit_syscode > #{beginNo}
        <![CDATA[
         and t.eawbcit_syscode <= #{endNo}
        ]]>



    </select>
</mapper>