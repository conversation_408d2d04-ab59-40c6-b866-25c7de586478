<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.billing.dao.ceos.CeosEawbMapper">

    <sql id="Base_Column_List" >
        EAWB_SYSCODE, EAWB_CODE, EAWB_PRINTCODE, EAWB_E_ID_HANDLER, EST_CODE, EAWB_HANDLETIME,
        EAWB_SHIPPER_ACCOUNT, EAWB_PICKUP_ADDRESS, EAWB_PICKUP_POSTCODE, EAWB_PICKUP_CONTACT,
        EAWB_PICKUP_PHONE, EAWB_CONSIGNEE_ACCOUNT, EAWB_DELIVER_ADDRESS, EAWB_DELIVER_POSTCODE,
        EAWB_DELIVER_CONTACT, EAWB_DELIVER_PHONE, EAWB_DEPARTURE, EAWB_DESTINATION, EAWB_PRODUCTNAME,
        EAWB_TOTALPIECES, EAWB_TOTALVOLUME, EAWB_TOTALGROSSWEIGHT, EAWB_TOTALCHARGEABLEWEIGHT,
        EAWB_DECLAREVOLUME, EAWB_DECLAREGROSSWEIGHT, EAWB_DECLARECHARGEABLE, EAWB_PAYMENTMODE,
        EAWB_THIRDPARTY_ACCOUNT, CT_CODE, EAWB_CURRENCYRATE, EAWB_TOTALRMB, EAWB_TOTALFC,
        SAC_ID, EAWB_SAC_CODE, EB_CODE, EAWB_SO_CODE, EAWB_SHIPPER_ACCOUNTNAME, EAWB_CONSIGNEE_ACCOUNTNAME,
        EAWB_PICKUP_BLOCK, EAWB_PICKUP_TIME, EAWB_FREIGHTCHARGE, EAWB_INCIDENTALCHARGE, EAWB_INSURANCECHARGE,
        EAWB_PPCC, EAWB_DECLAREVALUE, EAWB_REFERENCE1, EAWB_REFERENCE2, EAWB_STANDARDFREIGHTPRICE,
        EAWB_STATUS, EAWB_PICKUPBYCONSIGNEE, EAWB_DELIVERYATHOLIDAY, EAWB_INNER, EAWB_INBOUND_SAC_ID,
        EAWB_OUTBOUND_SAC_ID, EAWB_PRODUCTDECLARE, EAWB_SERVICEREQUIREMENT, EAWB_REFERENCE3,
        EAWB_C_CODE, EAWB_TIME_LIMIT, EAWB_COLLECTPAYMENTFORGOODS, EAWB_SELFINSURANCE, EAWB_KEYENTRYTIME,
        EAWB_PRESERVATION, EAWB_INSURANCESERVICE, EAWB_AGENT_CODE, EAWB_BUSINESSMODE, EAWB_PRE_FREIGHTPRICE,
        EAWB_SENDVOICEREQUEST, CBC_SHIP_CODE, EAWB_ROUTE, EAWB_DEPARTCITY, EAWB_DEPARTSTATE,
        EAWB_DEPARTCOUNTRY, EAWB_DESTCITY, EAWB_DESTSTATE, EAWB_DESTCOUNTRY, EAWB_TYPE, EAWB_CUSTPRODNAME,
        EAWB_QUANTITY, EAWB_CUSTDECLVAL, EAWB_CUSTCURRENCY, EAWB_DECLCURRENCY, EAWB_CONSIGNMENTNO,
        EAWB_KJTYPE, EAWB_IETYPE, B_CODE, EAWB_FUELCHARGE, EAWB_INSUTYPE, EAWB_INSUAMOUNTTYPE,
        EAWB_INSUAMOUNT, EAWB_PRE_FUELPRICE, HANDSET_CARGO, INFOXML, EAWB_DISCOUNTVALUE,
        EAWB_TRANSMODEID, EAWB_PRODUCTTYPE, EAWB_ORIGINCITY, EAWB_SHIPPER_CACCOUNTNAME, EAWB_DELIVER_FAX,
        EAWB_SPECIFICATION, EAWB_CCTYPE, EAWB_CUSTREGISTRATIONCODE, EAWB_CUSTREGISTRATIONAME,
        EAWB_ENTRUSTCODE, EAWB_HSCODE, EAWB_CUSTPRODENNAME, EAWB_OPERATESTATUS, EAWB_UNIT,
        EAWB_UNITCODE, EAWB_CHECKSTATUS, EAWB_SERVICETYPE, MAWB_CODE, FLIGHT_NO, CUSTOM_TYPE,
        EAWB_ECOMMERCE, EAWB_TAXCODE, EAWB_DELIVER_INDENTITYCARDNO, CUSTOMS_STATUS, CSEF_SYSCODE,
        ETCOPY, EAWB_TRACK, EAWB_DELIVER_EMAIL, EAWB_PARTITION, EAWB_DELIVERY_POSTCODE_CORRECT,
        EAWB_CHANGE_LABEL_STATUS, EAWB_REFUND_WANGWANG_ID, EAWB_REFUND_NAME, EAWB_REFUND_PHONE,
        EAWB_REFUND_MOBILE, EAWB_REFUND_EMAIL, EAWB_REFUND_COUNTRY, EAWB_REFUND_PRIVINCE,
        EAWB_REFUND_CITY, EAWB_REFUND_DISTRICT, EAWB_REFUND_STREET, EAWB_REFUND_ZIPCODE,
        EAWB_UNDELIVERY_OPTION, EAWB_CARRIERNAME, EAWB_CARRIERNO, ORDER_CODE_IN, ORDER_SYSCODE_IN,
        ORDER_CODE_OUT, ORDER_SYSCODE_OUT, CUSTOMER_ORDER_CODE, EAWB_SERVICETYPE_ORIGINAL,
        EAWB_LENGTH, EAWB_WIDTH, EAWB_HEIGHT, EAWB_DELIVER_MOBILE, EAWB_COD, EAWB_CODVALUE,
        EAWB_CODCURRENCY, EAWB_REFUND_REFERENCE, EAWB_TRANSMODEID_ORIGINAL, EAWB_REFUND_SUPPLIER,
        EAWB_REFUND_REFERENCE1, EAWB_REFUND_REFERENCE2, EAWB_REFUND_REFERENCE3, EAWB_REFUND_ADDRESS,
        REFUND_STATUS, EAWB_DELIVER_STREET, EAWB_NEXT_OPT_CENTER, EAWB_PRE_OPT_CENTER, EAWB_TRUNK_CODE,
        EAWB_CHANNEL_CODE, EAWB_DELIVER_DISTRICT, EAWB_PICKUP_DISTRICT, EAWB_SENDER_ADDRESS,
        EAWB_SORTCODE, EAWB_CONSIGNEE_LATITUDE, EAWB_CONSIGNEE_LONGITUDE, EAWB_FIRSTMILE,
        EAWB_PAYMENTID, EAWB_PAYMENTEMAIL, EAWB_PAYMENTCONTACTNAME, EAWB_PAYMENTPHONENUMBER,
        EAWB_SELLERTAXNUMBER, EAWB_INVOICENUMBER, EAWB_PRODUCTURL
    </sql>

    <sql id="CN_Column_List" >
        EAWB_PRINTCODE,
        EST_CODE, EAWB_HANDLETIME, EAWB_DEPARTURE,EAWB_DESTINATION,
        NVL(EAWB_TOTALPIECES,0) as eawbPieces, NVL(EAWB_TOTALVOLUME,0) as eawbVolume,
        NVL(EAWB_TOTALGROSSWEIGHT,0) as eawbGrossweight,
        NVL(EAWB_TOTALCHARGEABLEWEIGHT,0) as eawbChargeableweight,CT_CODE,
        (select s.sac_id from settlementobject s
        where s.so_Code= e.eawb_so_code and s.sac_id not in('SNR')) as sacId,
        EAWB_SO_CODE,EAWB_REFERENCE1,
        EAWB_REFERENCE2, EAWB_STATUS,
        case when e.sac_id='YIW' then e.eawb_departure
        when e.eawb_inbound_sac_id='DGM' then 'DSS'
        when e.sac_id='DS2' then 'DSS'
        else e.sac_id end as eawbOutboundSacId,
        EAWB_KEYENTRYTIME, E.EAWB_DEPARTCOUNTRY,EAWB_DESTCOUNTRY,
        NVL(E.EAWB_CUSTPRODNAME,E.EAWB_PRODUCTNAME) as eawbCustprodname,
        EAWB_TRANSMODEID,
        nvl(pay.ep_value, case when
               e.EAWB_SERVICETYPE='DISTRIBUTOR_MBXPXP_RMSG' then 'DISTRIBUTOR_MBXPXP'
               when e.EAWB_SERVICETYPE='DISTRIBUTOR_MBXPXB_RMSG' then 'DISTRIBUTOR_MBXPXB'
               when e.EAWB_SERVICETYPE='DISTRIBUTOR_MBXPXN_RMSG' then 'DISTRIBUTOR_MBXPXN'
                 else e.EAWB_SERVICETYPE end) EAWB_SERVICETYPE,
        MAWB_CODE,NVL(EAWB_LENGTH,0) as eawbLength, NVL(EAWB_WIDTH,0) as eawbWidth,
        NVL(EAWB_HEIGHT,0) as eawbHeight,
        E.EAWB_PICKUP_CONTACT as eawbShipperAccountname,E.EAWB_CONSIGNEE_ACCOUNTNAME,'E' as eawbBtCode,
        E.EAWB_PICKUP_POSTCODE,E.EAWB_DELIVER_POSTCODE,

        CASE WHEN E.EAWB_SERVICETYPE in('DISTRIBUTOR_99999011',
        'DISTRIBUTOR_99999013','DISTRIBUTOR_11182855',
        'DISTRIBUTOR_MBXPXN','DISTRIBUTOR_MBXPXP','DISTRIBUTOR_MBXPXB','CAINIAO_POSTNL_EU',
        'CAINIAO_PACKAGE_POSTNL_EU') THEN ''

        WHEN E.EAWB_SERVICETYPE='DISTRIBUTOR_8782834' THEN 'ALS'||SUBSTR(E.EAWB_REFERENCE1,16,8)
        WHEN E.EAWB_SERVICETYPE_ORIGINAL='L_AE_ECONOMY_SINOCL_SRM' THEN 'SRM'||substr(e.eawb_reference1,14,10)
        WHEN E.EAWB_SERVICETYPE_ORIGINAL='L_AE_STANDARD_SINOCPRIVE_RM' THEN SUBSTR(E.EAWB_REFERENCE1,1,12)
        WHEN E.EAWB_SERVICETYPE_ORIGINAL='EUR_EXPRESS_COLISPRIVE' THEN SUBSTR(E.EAWB_REFERENCE1,1,12)
        WHEN E.EAWB_SERVICETYPE_ORIGINAL='L_AE_STANDARD_COLISPRIVE_BE' THEN SUBSTR(E.EAWB_REFERENCE1,1,12)
        WHEN E.EAWB_SERVICETYPE='DISTRIBUTOR_13174094' THEN SUBSTR(E.EAWB_REFERENCE1,1,11)
        WHEN E.EAWB_SERVICETYPE='L_AE_F1_STANDARD_SINOGLS' THEN SUBSTR(E.EAWB_REFERENCE1,1,11)
        WHEN E.EAWB_SERVICETYPE='L_AE_PREMIUM_SINOFEDEX' THEN NVL(eawb_reference3,eawb_reference1)
        WHEN E.EAWB_SERVICETYPE='DISTRIBUTOR_1682692' THEN E.MAWB_CODE
        WHEN E.EAWB_SERVICETYPE='L_AE_STANDARD_SINOES_PAQ' THEN E.EAWB_REFERENCE2
        WHEN E.EAWB_SERVICETYPE='L_AE_STANDARD_SINOAE_RM' THEN E.EAWB_REFERENCE2
        WHEN E.EAWB_SERVICETYPE='L_AE_STANDARD_SINOSA_RM' THEN E.EAWB_REFERENCE2
        else E.EAWB_REFERENCE1  END as eawbTrackingNo,
        E.EAWB_QUANTITY,
        E.EAWB_DECLAREVALUE as eawbCustdeclval,
        E.EAWB_SPECIFICATION,
        E.EAWB_HSCODE,
        E.EAWB_CUSTPRODENNAME,
        E.CUSTOMER_ORDER_CODE,E.ORDER_CODE_IN,e.EAWB_PARTITION,
        (case when e.eawb_servicetype_original in('L_AE_ONLINE_SINOES_OM',
        'L_AE_ONLINE_SINOES_OM_BAT')
        then 'DISTRIBUTOR_903172'
        else nvl(receipt.ep_value, e.eawb_servicetype_original) end) as eawbServiceTypeOriginal,
        e.eawb_transmodeid_original,e.eawb_destcity,
        E.EAWB_CUSTDECLVAL as eawbDeclarevolume,
        EAWB_DECLAREGROSSWEIGHT,
        EAWB_DECLARECHARGEABLE,E.EAWB_DESTSTATE,E.EAWB_IETYPE,
        case when e.eawb_servicetype='Tran_Store_13322345' then 'ON'
        else e.REFUND_STATUS end as refundStatus,eawb_ecommerce,
        <![CDATA[
        case when e.eawb_servicetype in('TRUNK_13451487','ESNAD') and EAWB_TOTALCHARGEABLEWEIGHT<0.5 then 0.5
        when e.eawb_servicetype in('TRUNK_13451487','ESNAD') and EAWB_TOTALCHARGEABLEWEIGHT>=15 then ceil(EAWB_TOTALCHARGEABLEWEIGHT)
        else EAWB_TOTALCHARGEABLEWEIGHT end as weightValue,
        ]]>
        EAWB_COD,
        EAWB_CODVALUE,
        EAWB_CODCURRENCY,eawb_inner,EB_CODE,EAWB_SERVICEREQUIREMENT
    </sql>

    <sql id="Other_Column_List" >
        EAWB_PRINTCODE,
           EST_CODE, EAWB_HANDLETIME, EAWB_DEPARTURE,EAWB_DESTINATION,
           NVL(EAWB_TOTALPIECES,0) as eawbPieces, NVL(EAWB_TOTALVOLUME,0) as eawbVolume,
           NVL(EAWB_TOTALGROSSWEIGHT,0) as eawbGrossweight,
           NVL(EAWB_TOTALCHARGEABLEWEIGHT,0) as eawbChargeableweight,CT_CODE,
           (select s.sac_id from settlementobject s
           where s.so_Code= e.eawb_so_code and s.sac_id not in('SNR')) as sacId,
           EAWB_SO_CODE,EAWB_REFERENCE1,
           EAWB_REFERENCE2, EAWB_STATUS,
           e.sac_id as eawbOutboundSacId,
           EAWB_KEYENTRYTIME, E.EAWB_DEPARTCOUNTRY,EAWB_DESTCOUNTRY,
           NVL(E.EAWB_CUSTPRODNAME,E.EAWB_PRODUCTNAME) as eawbCustprodname,
           EAWB_TRANSMODEID,
           nvl(pay.ep_value, e.eawb_servicetype) EAWB_SERVICETYPE,

           (select ea.new_ea_code from expressassignment ea,packages pa, eawb_packages ep where ea.ea_syscode=pa.ea_syscode
            and pa.pkg_syscode=ep.pkg_syscode and ep.eawb_syscode=e.eawb_syscode) as MAWB_CODE,NVL(EAWB_LENGTH,0) as eawbLength, NVL(EAWB_WIDTH,0) as eawbWidth,
           NVL(EAWB_HEIGHT,0) as eawbHeight,
           E.EAWB_PICKUP_CONTACT as eawbShipperAccountname,E.EAWB_CONSIGNEE_ACCOUNTNAME,'E' as eawbBtCode,
           E.EAWB_PICKUP_POSTCODE,E.EAWB_DELIVER_POSTCODE,
           CASE
              WHEN EAWB_SERVICETYPE_ORIGINAL IN ('DISTRIBUTOR_CLSRM','DISTRIBUTOR_CLSRM_BAT',
                'WISH_DISTRIBUTOR_CLSRM','WISH_DISTRIBUTOR_CLSRM_BAT'
                )  then 'SRM'||substr(e.eawb_reference1,14,10)
             WHEN E.EAWB_SERVICETYPE_ORIGINAL='SINOAIR_EUGLS' THEN SUBSTR(E.EAWB_REFERENCE1,1,11)
             WHEN E.EAWB_SERVICETYPE_ORIGINAL='EUR_EXPRESS_COLISPRIVE' THEN SUBSTR(E.EAWB_REFERENCE1,1,12)
             WHEN E.EAWB_SERVICETYPE_ORIGINAL='L_AE_STANDARD_COLISPRIVE_BE' THEN SUBSTR(E.EAWB_REFERENCE1,1,12)
             ELSE E.EAWB_REFERENCE1 END  EAWB_TRACKING_NO,
           E.EAWB_QUANTITY,
           E.EAWB_CUSTDECLVAL,
           E.EAWB_SPECIFICATION,
           E.EAWB_HSCODE,
           E.EAWB_CUSTPRODENNAME,
           E.CUSTOMER_ORDER_CODE,E.ORDER_CODE_IN,e.EAWB_PARTITION,
           nvl(receipt.ep_value, e.eawb_servicetype_original) as eawbServiceTypeOriginal,
           e.eawb_transmodeid_original,e.eawb_destcity,
           EAWB_DECLAREVOLUME,
           EAWB_DECLAREGROSSWEIGHT,
           EAWB_DECLARECHARGEABLE,E.EAWB_DESTSTATE,E.EAWB_IETYPE,
           e.REFUND_STATUS,eawb_ecommerce,
           EAWB_TOTALCHARGEABLEWEIGHT as weightValue,
           EAWB_COD,
           EAWB_CODVALUE,
           EAWB_CODCURRENCY,eawb_firstmile,EAWB_REFERENCE3,eawb_inner,EB_CODE,EAWB_SERVICEREQUIREMENT
    </sql>

    <select id="selectEawbBySysCode" resultType="com.sinoair.billing.domain.vo.ceos.CeosEawb"
            parameterType="com.sinoair.billing.domain.vo.query.CeosQuery">

        SELECT
          eawb_syscode,eawb_printcode,EAWB_KEYENTRYTIME,eawb_so_code
        FROM expressairwaybill
        WHERE eawb_syscode >= #{beginNo}
        <![CDATA[
        and eawb_syscode < #{endNo}
        ]]>
        <if test="soCode != null and soCode != ''">
            and eawb_so_code=#{soCode}
        </if>


    </select>

    <!--
        select eawb_syscode from (
        select eawb_syscode from expressairwaybill
        where 1=1
        <if test="soCode != null and soCode != ''">
            and eawb_so_code=#{soCode}
        </if>
        order by eawb_syscode desc )
        where
        <![CDATA[
        rownum < 2
        ]]>
     -->
    <select id="selectMaxEawbSysCode" resultType="java.lang.Long" parameterType="java.lang.String">
        select max(eawb_syscode) from expressairwaybill
    </select>

    <select id="selectEawbByHandleTime" resultType="com.sinoair.billing.domain.vo.ceos.CeosEawb"
            parameterType="com.sinoair.billing.domain.vo.query.CeosQuery">

        SELECT
        eawb_syscode,eawb_printcode,EAWB_KEYENTRYTIME,eawb_so_code
        FROM expressairwaybill
        WHERE 1=1
        and EAWB_HANDLETIME>=to_date(${startEbaHandletime},'yyyymmdd')
        <![CDATA[
        and EAWB_HANDLETIME<to_date(${endEbaHandletime},'yyyymmdd')
        ]]>

    </select>

    <select id="countEawbByHandleTime" resultType="java.lang.Integer"
            parameterType="com.sinoair.billing.domain.vo.query.CeosQuery">

        SELECT
        count(0)
        FROM expressairwaybill
        WHERE 1=1
        and eawb_so_code != '00060491'
        and EAWB_HANDLETIME>=to_date(${startEbaHandletime},'yyyymmdd')
        <![CDATA[
        and EAWB_HANDLETIME<to_date(${endEbaHandletime},'yyyymmdd')
        ]]>
        <if test="soCode != null and soCode != ''">
            and eawb_so_code=#{soCode}
        </if>

    </select>

    <select id="countCeosEawbBySysCode" resultType="java.lang.Integer"
            parameterType="com.sinoair.billing.domain.vo.query.CeosQuery">

        select
          count(0)
        from expressairwaybill eawb
        where 1 =1
        and eawb.eawb_syscode > #{beginNo}
        <![CDATA[
         and eawb.eawb_syscode <= #{endNo}
         ]]>

    </select>

    <select id="selectCeosEawbByTime" resultType="com.sinoair.billing.domain.vo.ceos.CeosEawb"
            parameterType="com.sinoair.billing.domain.vo.query.CeosQuery">

        SELECT

        eawb_syscode,eawb_printcode,EAWB_KEYENTRYTIME,eawb_so_code
        FROM expressairwaybill
        WHERE 1=1
        and eawb_so_code != '00060491'
        and EAWB_HANDLETIME>=to_date(${startEbaHandletime},'yyyymmdd')
        <![CDATA[
        and EAWB_HANDLETIME<to_date(${endEbaHandletime},'yyyymmdd')
        ]]>
        <if test="soCode != null and soCode != ''">
            and eawb_so_code=#{soCode}
        </if>

    </select>

    <select id="selectCeosEawbBySysCode" resultType="com.sinoair.billing.domain.model.billing.ExpressAirWayBill"
            parameterType="com.sinoair.billing.domain.vo.query.CeosQuery">

        SELECT
        <include refid="CN_Column_List"/>
        FROM expressairwaybill e
        LEFT JOIN express_property pay on (e.eawb_servicetype_original = pay.ep_key
        and pay.ep_group = 'serviceTypeOriginal_serviceTypePay')
        LEFT JOIN express_property receipt  on (e.eawb_servicetype_original = receipt.ep_key
        AND receipt.ep_group = 'serviceTypeOriginal_serviceTypeReceipt')
        WHERE 1=1
        and eawb_syscode >= #{beginNo}
        <![CDATA[
        and eawb_syscode < #{endNo}
        ]]>
        <if test="soCode != null and soCode != ''">
            and eawb_so_code=#{soCode}
        </if>
    </select>

    <!--
            and eawb_syscode > #{beginNo}
        <![CDATA[
        and eawb_syscode <= #{endNo}
        ]]>
    -->

    <select id="selectCeosEawbByEawbPrintcode" resultType="com.sinoair.billing.domain.model.billing.ExpressAirWayBillCeos"
            parameterType="java.lang.String">

        SELECT
        <include refid="Base_Column_List" />
        FROM expressairwaybill
        WHERE 1=1
        and EAWB_PRINTCODE=#{eawbPrintcode}
    </select>

    <select id="countCeosEawbByEawbPrintcode" resultType="java.lang.Integer"
            parameterType="java.lang.String">

        SELECT
        count(0)
        FROM expressairwaybill
        WHERE EAWB_PRINTCODE=#{eawbPrintcode}
    </select>

    <select id="selectCeosEawbCNBycode" resultType="com.sinoair.billing.domain.model.billing.ExpressAirWayBill"
            parameterType="java.lang.String">

        SELECT
            <include refid="CN_Column_List"/>
        FROM EXPRESSAIRWAYBILL E
        LEFT JOIN express_property pay on (e.eawb_servicetype_original = pay.ep_key
        and pay.ep_group = 'serviceTypeOriginal_serviceTypePay')
        LEFT JOIN express_property receipt  on (e.eawb_servicetype_original = receipt.ep_key
        AND receipt.ep_group = 'serviceTypeOriginal_serviceTypeReceipt')
        WHERE 1=1
        and EAWB_PRINTCODE=#{eawbPrintcode}
    </select>

    <select id="selectCeosEawbOtherByCode" resultType="com.sinoair.billing.domain.model.billing.ExpressAirWayBill"
            parameterType="java.lang.String">

    SELECT
        <include refid="Other_Column_List"/>
      FROM EXPRESSAIRWAYBILL e
      LEFT JOIN express_property pay on (e.eawb_servicetype_original = pay.ep_key
           and pay.ep_group = 'serviceTypeOriginal_serviceTypePay')
      LEFT JOIN express_property receipt  on (e.eawb_servicetype_original = receipt.ep_key
           AND receipt.ep_group = 'serviceTypeOriginal_serviceTypeReceipt')
        WHERE 1=1
        and EAWB_PRINTCODE=#{eawbPrintcode}
    </select>

    <select id="selectCeosFlatPre" resultType="com.sinoair.billing.domain.model.billing.ExpressAirWayBill"
            parameterType="java.lang.String">

        SELECT EAWB_PRINTCODE as eawbPrintcode,
               eawb_servicetype_original as eawbServiceTypeOriginal,
               EAWB_SO_CODE as  eawbSoCode,
               EAWB_DESTCOUNTRY as eawbDestcountry,
               EAWB_DELIVER_POSTCODE as eawbDeliverPostcode
        FROM eawbpre
        WHERE 1=1
        and EAWB_PRINTCODE=#{eawbPrintcode}
        and rownum = 1
    </select>

    <select id="selectCeosPre" resultType="com.sinoair.billing.domain.model.billing.EawbPre"
            parameterType="java.lang.String">

        SELECT
            EAWB_SYSCODE, EAWB_PRINTCODE, EAWB_HANDLETIME, EAWB_DELIVER_ADDRESS, EAWB_DELIVER_CONTACT,
               EAWB_DELIVER_PHONE, EAWB_DELIVER_MOBILE, EAWB_DELIVER_EMAIL, EAWB_DESTINATION, EAWB_SO_CODE,
               EAWB_REFERENCE1, EAWB_REFERENCE2, EAWB_STATUS, EAWB_KEYENTRYTIME, EAWB_DESTCOUNTRY,
               EAWB_CUSTPRODNAME, EAWB_TRANSMODEID, EAWB_SERVICETYPE, EAWB_DELIVER_POSTCODE, EAWB_CUSTDECLVAL,
               EAWB_DESTSTATE, EAWB_DESTCITY, EAWB_DECLAREGROSSWEIGHT, PKG_PRINTCODE
        FROM eawbpre
        WHERE 1=1
          and EAWB_PRINTCODE=#{eawbPrintcode}
          and rownum = 1
    </select>

    <select id="countPreByHandleTime" resultType="java.lang.Integer"
            parameterType="com.sinoair.billing.domain.vo.query.CeosQuery">

        SELECT
        count(0)
        FROM eawbpre
        WHERE 1=1
        and eawb_so_code != '00060491'
        and EAWB_HANDLETIME>=to_date(${startEbaHandletime},'yyyymmdd')
        <![CDATA[
        and EAWB_HANDLETIME<to_date(${endEbaHandletime},'yyyymmdd')
        ]]>

    </select>

    <select id="selectPreByHandleTime" resultType="com.sinoair.billing.domain.model.billing.EawbPre"
            parameterType="com.sinoair.billing.domain.vo.query.CeosQuery">

        SELECT

            EAWB_SYSCODE, EAWB_PRINTCODE, EAWB_HANDLETIME, EAWB_DELIVER_ADDRESS, EAWB_DELIVER_CONTACT,
            EAWB_DELIVER_PHONE, EAWB_DELIVER_MOBILE, EAWB_DELIVER_EMAIL, EAWB_DESTINATION, EAWB_SO_CODE,
            EAWB_REFERENCE1, EAWB_REFERENCE2, EAWB_STATUS, EAWB_KEYENTRYTIME, EAWB_DESTCOUNTRY,
            EAWB_CUSTPRODNAME, EAWB_TRANSMODEID, EAWB_SERVICETYPE, EAWB_DELIVER_POSTCODE, EAWB_CUSTDECLVAL,
            EAWB_DESTSTATE, EAWB_DESTCITY, EAWB_DECLAREGROSSWEIGHT, PKG_PRINTCODE
        FROM eawbpre
        WHERE 1=1
        and eawb_so_code != '00060491'
        and EAWB_HANDLETIME>=to_date(${startEbaHandletime},'yyyymmdd')
        <![CDATA[
        and EAWB_HANDLETIME<to_date(${endEbaHandletime},'yyyymmdd')
        ]]>

    </select>
</mapper>