<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.billing.dao.ceos.CeosEawbEaMapper">

    <sql id="Base_Column_List" >

    </sql>

    <select id="countCeosMawbEawb" resultType="java.lang.Integer"
            parameterType="com.sinoair.billing.domain.vo.query.CeosQuery">

       select
            count(0)
        from expressairwaybill eawb,
        eawb_packages     ep,
        expressassignment ea,
        packages  p
        where eawb.eawb_syscode = ep.eawb_syscode
        and ep.pkg_syscode = p.pkg_syscode
        and p.ea_code = ea.ea_code
        and ea.update_time>=to_date(${startEbaHandletime},'yyyymmdd')
        <![CDATA[
        and ea.update_time<to_date(${endEbaHandletime},'yyyymmdd')
        ]]>

    </select>

    <select id="selectCeosMawbEawb" resultType="com.sinoair.billing.domain.vo.ceos.CeosEawbEaVO"
            parameterType="com.sinoair.billing.domain.vo.query.CeosQuery">

        select
            ea.new_ea_code as mawbCode,
            eawb.eawb_printcode as eawbPrintcode,
            ea.transport_type as transportType,
            ea.ea_flight_type as eaFlightType,
            ea.new_flight_number as eawbSfCode,
            ea.ori_sac_id as destSacId
        from expressairwaybill eawb,
        eawb_packages     ep,
        expressassignment ea,
        packages  p
        where eawb.eawb_syscode = ep.eawb_syscode
        and ep.pkg_syscode = p.pkg_syscode
        and p.ea_code = ea.ea_code
        and ea.update_time>=to_date(${startEbaHandletime},'yyyymmdd')
        <![CDATA[
        and ea.update_time<to_date(${endEbaHandletime},'yyyymmdd')
        ]]>
    </select>

    <select id="countCeosMawbEawbByEaCode" resultType="java.lang.Integer">

        select
        count(0)
        from expressairwaybill eawb,
        eawb_packages     ep,
        expressassignment ea,
        packages  p
        where eawb.eawb_syscode = ep.eawb_syscode
        and ep.pkg_syscode = p.pkg_syscode
        and p.ea_code = ea.ea_code
        and ea.new_ea_code = #{eaCode}

    </select>

    <select id="selectCeosMawbEawbByEaCode" resultType="com.sinoair.billing.domain.vo.ceos.CeosEawbEaVO">

        select
        ea.new_ea_code as mawbCode,
        eawb.eawb_printcode as eawbPrintcode,
        eawb.eawb_so_code as eawbSoCode,
        ea.transport_type as transportType,
        ea.ea_flight_type as eaFlightType,
        ea.new_flight_number as eawbSfCode,
        ea.ori_sac_id as destSacId
        from expressairwaybill eawb,
        eawb_packages     ep,
        expressassignment ea,
        packages  p
        where eawb.eawb_syscode = ep.eawb_syscode
        and ep.pkg_syscode = p.pkg_syscode
        and p.ea_code = ea.ea_code
        and ea.new_ea_code = #{eaCode}
    </select>

    <select id="selectCeosMawbEawbByEawbPrintcode" resultType="com.sinoair.billing.domain.vo.ceos.CeosEawbEaVO">

        select
        ea.new_ea_code as mawbCode,
        eawb.eawb_printcode as eawbPrintcode,
        ea.transport_type as transportType,
        ea.ea_flight_type as eaFlightType,
        ea.new_flight_number as eawbSfCode,
        ea.ori_sac_id as destSacId
        from expressairwaybill eawb,
        eawb_packages     ep,
        expressassignment ea,
        packages  p
        where eawb.eawb_syscode = ep.eawb_syscode
        and ep.pkg_syscode = p.pkg_syscode
        and p.ea_code = ea.ea_code
        and eawb.eawb_printcode = #{eawbPrintcode}
        and rownum = 1
    </select>

    <select id="selectCeosMawbActualByPrintcode" resultType="com.sinoair.billing.domain.vo.ceos.CeosEawbEaVO">
        select
            ea.eaa_code as mawbCode,
            eawb.eawb_printcode as eawbPrintcode,
            ea.transport_type as transportType,
            ea.eaa_flight_type as eaFlightType,
            ea.flight_number as eawbSfCode,
            ea.ori_sac_id as destSacId
        from expressairwaybill eawb,
            eawb_packages     ep,
            expressassignmentactual ea,
            packages  p
        where eawb.eawb_syscode = ep.eawb_syscode
            and ep.pkg_syscode = p.pkg_syscode
            and p.ea_code = ea.eaa_code
          and eawb.eawb_printcode = #{eawbPrintcode}
          and rownum = 1
    </select>

    <select id="selectCeosEaaList" resultType="com.sinoair.billing.domain.model.billing.ExpressAssignmentActual"
            parameterType="com.sinoair.billing.domain.vo.query.CeosQuery">
        select
            ea.*
        from expressassignmentactual ea
        where 1=1
        <if test="startEbaHandletime != null and endEbaHandletime != ''">
          and ea.eaa_handletime>=to_date(${startEbaHandletime},'yyyymmdd')
        <![CDATA[
          and ea.eaa_handletime<to_date(${endEbaHandletime},'yyyymmdd')
        ]]>
        </if>
        and ea.eaa_handletime>=to_date('20220701','yyyymmdd')
    </select>

    <select id="countCeosEaaCn" resultType="java.lang.Integer">
        select
            count(0)
        from expressairwaybill eawb,
             eawb_packages     ep,
             expressassignmentactual ea,
             packages  p
        where eawb.eawb_syscode = ep.eawb_syscode
          and ep.pkg_syscode = p.pkg_syscode
          and p.ea_code = ea.eaa_code
          and eawb.eawb_so_code = '00060491'
          and ea.eaa_code = #{eaCode}
          and rownum = 1
    </select>
</mapper>