<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinoair.billing.dao.ceosbi.EawbEadFlatMapper" >
  <resultMap id="BaseResultMap" type="com.sinoair.billing.domain.model.ceosbi.EawbEadFlat" >
    <id column="EAWB_SYSCODE" property="eawbSyscode" jdbcType="DECIMAL" />
    <result column="EAWB_PRINTCODE" property="eawbPrintcode" jdbcType="VARCHAR" />
    <result column="FC_INBOUND" property="fcInbound" jdbcType="TIMESTAMP" />
    <result column="FC_OUTBOUND" property="fcOutbound" jdbcType="TIMESTAMP" />
    <result column="ASS" property="ass" jdbcType="TIMESTAMP" />
    <result column="UNASS" property="unass" jdbcType="TIMESTAMP" />
    <result column="ADC" property="adc" jdbcType="TIMESTAMP" />
    <result column="CPT" property="cpt" jdbcType="TIMESTAMP" />
    <result column="ROE" property="roe" jdbcType="TIMESTAMP" />
    <result column="DELIVERY" property="delivery" jdbcType="TIMESTAMP" />
    <result column="EEF_UPDATETIME" property="eefUpdatetime" jdbcType="TIMESTAMP" />
    <result column="EAWB_SERVICETYPE" property="eawbServicetype" jdbcType="VARCHAR" />
    <result column="EAWB_SO_CODE" property="eawbSoCode" jdbcType="VARCHAR" />
    <result column="EAWB_DESTCOUNTRY" property="eawbDestcountry" jdbcType="VARCHAR" />
    <result column="EAWB_POSTCODE" property="eawbPostcode" jdbcType="VARCHAR" />
    <result column="POSTCODE_FIRST" property="postcodeFirst" jdbcType="VARCHAR" />
    <result column="STATUS" property="status" jdbcType="VARCHAR" />
    <result column="EEF_UPDATETIME" property="eefUpdatetime" jdbcType="TIMESTAMP" />
    <result column="UNDELIVERY" property="undelivery" jdbcType="TIMESTAMP" />
    <result column="DECLARE" property="declare" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    EAWB_SYSCODE, EAWB_PRINTCODE, FC_INBOUND, FC_OUTBOUND, ASS, UNASS, ADC, CPT, ROE, 
    DELIVERY, EEF_UPDATETIME, EAWB_SERVICETYPE, EAWB_SO_CODE, EAWB_DESTCOUNTRY, EAWB_POSTCODE,
    POSTCODE_FIRST,STATUS,UNDELIVERY,DECLARE
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.math.BigDecimal" >
    select 
    <include refid="Base_Column_List" />
    from EAWB_EAD_FLAT_N
    where EAWB_SYSCODE = #{eawbSyscode,jdbcType=DECIMAL}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.math.BigDecimal" >
    delete from EAWB_EAD_FLAT_N
    where EAWB_SYSCODE = #{eawbSyscode,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.sinoair.billing.domain.model.ceosbi.EawbEadFlat" >
    insert into EAWB_EAD_FLAT (EAWB_SYSCODE, EAWB_PRINTCODE, FC_INBOUND, 
      FC_OUTBOUND, ASS, UNASS, 
      ADC, CPT, ROE, 
      DELIVERY, EEF_UPDATETIME)
    values (#{eawbSyscode,jdbcType=DECIMAL}, #{eawbPrintcode,jdbcType=VARCHAR}, #{fcInbound,jdbcType=TIMESTAMP}, 
      #{fcOutbound,jdbcType=TIMESTAMP}, #{ass,jdbcType=TIMESTAMP}, #{unass,jdbcType=TIMESTAMP}, 
      #{adc,jdbcType=TIMESTAMP}, #{cpt,jdbcType=TIMESTAMP}, #{roe,jdbcType=TIMESTAMP}, 
      #{delivery,jdbcType=TIMESTAMP}, #{eefUpdatetime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sinoair.billing.domain.model.ceosbi.EawbEadFlat" >
    insert into EAWB_EAD_FLAT
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="eawbSyscode != null" >
        EAWB_SYSCODE,
      </if>
      <if test="eawbPrintcode != null" >
        EAWB_PRINTCODE,
      </if>
      <if test="fcInbound != null" >
        FC_INBOUND,
      </if>
      <if test="fcOutbound != null" >
        FC_OUTBOUND,
      </if>
      <if test="ass != null" >
        ASS,
      </if>
      <if test="unass != null" >
        UNASS,
      </if>
      <if test="adc != null" >
        ADC,
      </if>
      <if test="cpt != null" >
        CPT,
      </if>
      <if test="roe != null" >
        ROE,
      </if>
      <if test="delivery != null" >
        DELIVERY,
      </if>
      <if test="eefUpdatetime != null" >
        EEF_UPDATETIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="eawbSyscode != null" >
        #{eawbSyscode,jdbcType=DECIMAL},
      </if>
      <if test="eawbPrintcode != null" >
        #{eawbPrintcode,jdbcType=VARCHAR},
      </if>
      <if test="fcInbound != null" >
        #{fcInbound,jdbcType=TIMESTAMP},
      </if>
      <if test="fcOutbound != null" >
        #{fcOutbound,jdbcType=TIMESTAMP},
      </if>
      <if test="ass != null" >
        #{ass,jdbcType=TIMESTAMP},
      </if>
      <if test="unass != null" >
        #{unass,jdbcType=TIMESTAMP},
      </if>
      <if test="adc != null" >
        #{adc,jdbcType=TIMESTAMP},
      </if>
      <if test="cpt != null" >
        #{cpt,jdbcType=TIMESTAMP},
      </if>
      <if test="roe != null" >
        #{roe,jdbcType=TIMESTAMP},
      </if>
      <if test="delivery != null" >
        #{delivery,jdbcType=TIMESTAMP},
      </if>
      <if test="eefUpdatetime != null" >
        #{eefUpdatetime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sinoair.billing.domain.model.ceosbi.EawbEadFlat" >
    update EAWB_EAD_FLAT
    <set >
      <if test="eawbPrintcode != null" >
        EAWB_PRINTCODE = #{eawbPrintcode,jdbcType=VARCHAR},
      </if>
      <if test="fcInbound != null" >
        FC_INBOUND = #{fcInbound,jdbcType=TIMESTAMP},
      </if>
      <if test="fcOutbound != null" >
        FC_OUTBOUND = #{fcOutbound,jdbcType=TIMESTAMP},
      </if>
      <if test="ass != null" >
        ASS = #{ass,jdbcType=TIMESTAMP},
      </if>
      <if test="unass != null" >
        UNASS = #{unass,jdbcType=TIMESTAMP},
      </if>
      <if test="adc != null" >
        ADC = #{adc,jdbcType=TIMESTAMP},
      </if>
      <if test="cpt != null" >
        CPT = #{cpt,jdbcType=TIMESTAMP},
      </if>
      <if test="roe != null" >
        ROE = #{roe,jdbcType=TIMESTAMP},
      </if>
      <if test="delivery != null" >
        DELIVERY = #{delivery,jdbcType=TIMESTAMP},
      </if>
      <if test="eefUpdatetime != null" >
        EEF_UPDATETIME = #{eefUpdatetime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where EAWB_SYSCODE = #{eawbSyscode,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sinoair.billing.domain.model.ceosbi.EawbEadFlat" >
    update EAWB_EAD_FLAT
    set EAWB_PRINTCODE = #{eawbPrintcode,jdbcType=VARCHAR},
      FC_INBOUND = #{fcInbound,jdbcType=TIMESTAMP},
      FC_OUTBOUND = #{fcOutbound,jdbcType=TIMESTAMP},
      ASS = #{ass,jdbcType=TIMESTAMP},
      UNASS = #{unass,jdbcType=TIMESTAMP},
      ADC = #{adc,jdbcType=TIMESTAMP},
      CPT = #{cpt,jdbcType=TIMESTAMP},
      ROE = #{roe,jdbcType=TIMESTAMP},
      DELIVERY = #{delivery,jdbcType=TIMESTAMP},
      EEF_UPDATETIME = #{eefUpdatetime,jdbcType=TIMESTAMP}
    where EAWB_SYSCODE = #{eawbSyscode,jdbcType=DECIMAL}
  </update>

  <insert id="insertBatch" parameterType="java.util.ArrayList" >
    insert into EAWB_EAD_FLAT_N (EAWB_SYSCODE, EAWB_PRINTCODE, FC_INBOUND,
                               FC_OUTBOUND, ASS, UNASS,
                               ADC, CPT, ROE,
                               DELIVERY, EEF_UPDATETIME, EAWB_SERVICETYPE, EAWB_SO_CODE, EAWB_DESTCOUNTRY, EAWB_POSTCODE,
    POSTCODE_FIRST,STATUS,UNDELIVERY,DECLARE)
    <foreach collection="list" item="item" index="index" separator="UNION" >
      (SELECT
      #{item.eawbSyscode,jdbcType=DECIMAL}, #{item.eawbPrintcode,jdbcType=VARCHAR}, #{item.fcInbound,jdbcType=TIMESTAMP},
      #{item.fcOutbound,jdbcType=TIMESTAMP}, #{item.ass,jdbcType=TIMESTAMP}, #{item.unass,jdbcType=TIMESTAMP},
      #{item.adc,jdbcType=TIMESTAMP},#{item.cpt,jdbcType=TIMESTAMP},#{item.roe,jdbcType=TIMESTAMP},#{item.delivery,jdbcType=TIMESTAMP},
      #{item.eefUpdatetime,jdbcType=TIMESTAMP}, #{item.eawbServicetype,jdbcType=VARCHAR},
      #{item.eawbSoCode,jdbcType=VARCHAR}, #{item.eawbDestcountry,jdbcType=VARCHAR}, #{item.eawbPostcode,jdbcType=VARCHAR},
      #{item.postcodeFirst,jdbcType=VARCHAR},#{item.status,jdbcType=VARCHAR},
      #{item.undelivery,jdbcType=TIMESTAMP},#{item.declare,jdbcType=TIMESTAMP}
      FROM dual)
    </foreach>
  </insert>

  <update id="updateBatch" parameterType="java.util.List">
    <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
      update EAWB_EAD_FLAT_N
      <set>
        <if test="item.fcInbound !=null">
          FC_INBOUND = #{item.fcInbound,jdbcType=TIMESTAMP},
        </if>
        <if test="item.fcOutbound!=null">
          FC_OUTBOUND = #{item.fcOutbound,jdbcType=TIMESTAMP},
        </if>
        <if test="item.ass !=null">
          ASS = #{item.ass,jdbcType=TIMESTAMP},
        </if>
        <if test="item.unass!=null">
          UNASS = #{item.unass,jdbcType=TIMESTAMP},
        </if>
        <if test="item.adc !=null">
          ADC = #{item.adc,jdbcType=TIMESTAMP},
        </if>
        <if test="item.cpt!=null">
          CPT = #{item.cpt,jdbcType=TIMESTAMP},
        </if>
        <if test="item.roe !=null">
          ROE = #{item.roe,jdbcType=TIMESTAMP},
        </if>
        <if test="item.delivery!=null">
          DELIVERY = #{item.delivery,jdbcType=TIMESTAMP},
        </if>
        <if test="item.eefUpdatetime!=null">
          EEF_UPDATETIME =#{item.eefUpdatetime,jdbcType=TIMESTAMP}
        </if>
        <if test="item.eawbServicetype != null" >
          EAWB_SERVICETYPE = #{item.eawbServicetype,jdbcType=VARCHAR},
        </if>
        <if test="item.eawbSoCode != null" >
          EAWB_SO_CODE = #{item.eawbSoCode,jdbcType=VARCHAR},
        </if>
        <if test="item.eawbDestcountry != null" >
          EAWB_DESTCOUNTRY = #{item.eawbDestcountry,jdbcType=VARCHAR},
        </if>
        <if test="item.eawbPostcode != null" >
          EAWB_POSTCODE = #{item.eawbPostcode,jdbcType=VARCHAR},
        </if>
        <if test="item.postcodeFirst != null" >
          POSTCODE_FIRST = #{item.postcodeFirst,jdbcType=VARCHAR},
        </if>
        <if test="item.status != null" >
          STATUS = #{item.status,jdbcType=VARCHAR},
        </if>
        <if test="item.undelivery != null" >
          UNDELIVERY = #{item.undelivery,jdbcType=TIMESTAMP},
        </if>
        <if test="item.declare != null" >
          DECLARE = #{item.declare,jdbcType=TIMESTAMP},
        </if>
      </set>
      where
      EAWB_SYSCODE=#{item.eawbSyscode}

    </foreach>
  </update>
</mapper>