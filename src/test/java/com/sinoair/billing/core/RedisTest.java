package com.sinoair.billing.core;

import com.sinoair.billing.BaseTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;

/**
 * @Author: 大雄
 * @Date: 2023/11/14 14:38
 * @Description:
 */
public class RedisTest  extends BaseTest {

    @Autowired
    private RedisTemplate<String,String> redisTemplate;

    @Test
    public void testRedis(){
        String key = "billing:test:abc";
        String value = "abc123";
        redisTemplate.opsForValue().set(key,value);
        System.out.println("读取redis的值："+redisTemplate.opsForValue().get(key));
        redisTemplate.delete(key);
        System.out.println("删除后读取redis的值："+redisTemplate.opsForValue().get(key));

    }

}
