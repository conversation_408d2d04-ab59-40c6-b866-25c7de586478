package com.sinoair.billing.dao.billing;

import com.sinoair.billing.domain.model.billing.EawbEadFlatN;
import java.math.BigDecimal;

public interface EawbEadFlatNMapper {
    int deleteByPrimaryKey(BigDecimal eawbSyscode);

    int insert(EawbEadFlatN record);

    int insertSelective(EawbEadFlatN record);

    EawbEadFlatN selectByPrimaryKey(BigDecimal eawbSyscode);

    int updateByPrimaryKeySelective(EawbEadFlatN record);

    int updateByPrimaryKey(EawbEadFlatN record);
}