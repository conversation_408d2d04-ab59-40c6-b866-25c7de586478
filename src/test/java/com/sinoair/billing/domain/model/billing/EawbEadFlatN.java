package com.sinoair.billing.domain.model.billing;

import java.math.BigDecimal;
import java.util.Date;

public class EawbEadFlatN {
    private BigDecimal eawbSyscode;

    private String eawbPrintcode;

    private Date fcInbound;

    private Date fcOutbound;

    private Date ass;

    private Date unass;

    private Date adc;

    private Date cpt;

    private Date roe;

    private Date delivery;

    private Date eefUpdatetime;

    private String eawbServicetype;

    private String eawbSoCode;

    private String eawbDestcountry;

    private String eawbPostcode;

    private String postcodeFirst;

    public BigDecimal getEawbSyscode() {
        return eawbSyscode;
    }

    public void setEawbSyscode(BigDecimal eawbSyscode) {
        this.eawbSyscode = eawbSyscode;
    }

    public String getEawbPrintcode() {
        return eawbPrintcode;
    }

    public void setEawbPrintcode(String eawbPrintcode) {
        this.eawbPrintcode = eawbPrintcode == null ? null : eawbPrintcode.trim();
    }

    public Date getFcInbound() {
        return fcInbound;
    }

    public void setFcInbound(Date fcInbound) {
        this.fcInbound = fcInbound;
    }

    public Date getFcOutbound() {
        return fcOutbound;
    }

    public void setFcOutbound(Date fcOutbound) {
        this.fcOutbound = fcOutbound;
    }

    public Date getAss() {
        return ass;
    }

    public void setAss(Date ass) {
        this.ass = ass;
    }

    public Date getUnass() {
        return unass;
    }

    public void setUnass(Date unass) {
        this.unass = unass;
    }

    public Date getAdc() {
        return adc;
    }

    public void setAdc(Date adc) {
        this.adc = adc;
    }

    public Date getCpt() {
        return cpt;
    }

    public void setCpt(Date cpt) {
        this.cpt = cpt;
    }

    public Date getRoe() {
        return roe;
    }

    public void setRoe(Date roe) {
        this.roe = roe;
    }

    public Date getDelivery() {
        return delivery;
    }

    public void setDelivery(Date delivery) {
        this.delivery = delivery;
    }

    public Date getEefUpdatetime() {
        return eefUpdatetime;
    }

    public void setEefUpdatetime(Date eefUpdatetime) {
        this.eefUpdatetime = eefUpdatetime;
    }

    public String getEawbServicetype() {
        return eawbServicetype;
    }

    public void setEawbServicetype(String eawbServicetype) {
        this.eawbServicetype = eawbServicetype == null ? null : eawbServicetype.trim();
    }

    public String getEawbSoCode() {
        return eawbSoCode;
    }

    public void setEawbSoCode(String eawbSoCode) {
        this.eawbSoCode = eawbSoCode == null ? null : eawbSoCode.trim();
    }

    public String getEawbDestcountry() {
        return eawbDestcountry;
    }

    public void setEawbDestcountry(String eawbDestcountry) {
        this.eawbDestcountry = eawbDestcountry == null ? null : eawbDestcountry.trim();
    }

    public String getEawbPostcode() {
        return eawbPostcode;
    }

    public void setEawbPostcode(String eawbPostcode) {
        this.eawbPostcode = eawbPostcode == null ? null : eawbPostcode.trim();
    }

    public String getPostcodeFirst() {
        return postcodeFirst;
    }

    public void setPostcodeFirst(String postcodeFirst) {
        this.postcodeFirst = postcodeFirst == null ? null : postcodeFirst.trim();
    }
}