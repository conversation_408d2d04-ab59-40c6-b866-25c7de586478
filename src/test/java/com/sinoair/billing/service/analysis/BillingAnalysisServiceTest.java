package com.sinoair.billing.service.analysis;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.io.FileWriter;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Map;

/**
 * 计费分析服务测试类
 * 用于分析计费遗漏的原因
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:spring/spring-context.xml"})
public class BillingAnalysisServiceTest {

    @Autowired
    private BillingAnalysisService billingAnalysisService;

    /**
     * 测试计费遗漏分析
     */
    @Test
    public void testAnalyzeBillingGaps() {
        try {
            System.out.println("开始分析计费遗漏原因...");
            
            Map<String, Object> result = billingAnalysisService.analyzeBillingGaps();
            
            System.out.println("=== 分析结果 ===");
            for (Map.Entry<String, Object> entry : result.entrySet()) {
                System.out.println(entry.getKey() + ": " + entry.getValue());
            }
            
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("分析异常：" + e.getMessage());
        }
    }

    /**
     * 生成计费遗漏分析报告
     */
    @Test
    public void testGenerateBillingGapReport() {
        try {
            System.out.println("生成计费遗漏分析报告...");
            
            String report = billingAnalysisService.generateBillingGapReport();
            
            // 输出到控制台
            System.out.println(report);
            
            // 保存到文件
            String reportFilePath = "D:/work/workspace/app-billing-timer/billing_gap_analysis_report.txt";
            try (PrintWriter writer = new PrintWriter(new FileWriter(reportFilePath))) {
                writer.println(report);
                System.out.println("\n报告已保存到文件：" + reportFilePath);
            } catch (IOException e) {
                System.out.println("保存报告文件异常：" + e.getMessage());
            }
            
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("生成报告异常：" + e.getMessage());
        }
    }

    /**
     * 主方法，可以直接运行进行分析
     */
    public static void main(String[] args) {
        try {
            System.out.println("计费遗漏分析程序启动...");
            System.out.println("请确保数据库连接正常");
            
            // 这里可以添加实际的分析逻辑
            
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("程序启动异常：" + e.getMessage());
        }
    }
}
