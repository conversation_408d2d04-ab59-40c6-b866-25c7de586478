package com.sinoair.billing.service.payment;

import com.sinoair.billing.core.exception.SinoAirException;
import com.sinoair.billing.core.util.SpringContextUtil;
import com.sinoair.billing.run.BillingTimerStart;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.support.AbstractApplicationContext;
import org.springframework.context.support.ClassPathXmlApplicationContext;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.io.*;
import java.lang.reflect.InvocationTargetException;
import java.util.*;

/**
 * 手动计费服务测试类
 * 用于执行9000多条遗漏数据的重新计费
 */
public class ManualBillingServiceTest {

    private ManualBillingService manualBillingService;

    /**
     * 初始化Spring容器
     */
    private void initSpringContext() {
        if (manualBillingService == null) {
            try {
                // 设置Spring Profile为product-local
                System.setProperty("spring.profiles.active", "product-local");
                System.out.println("设置Spring Profile: product-local");

                AbstractApplicationContext context = new ClassPathXmlApplicationContext("conf/applicationContext.xml");

                // 验证激活的profile
                String[] profiles = context.getEnvironment().getActiveProfiles();
                System.out.println("Spring容器激活的Profiles: " + java.util.Arrays.toString(profiles));

                new SpringContextUtil().setApplicationContext(context);
                manualBillingService = SpringContextUtil.getBean("ManualBillingService");
                System.out.println("Spring容器初始化成功");
            } catch (Exception e) {
                System.out.println("Spring容器初始化失败：" + e.getMessage());
                e.printStackTrace();
            }
        }
    }

    /**
     * 测试手动计费功能
     * 这个方法用于处理您导出的9000多条运单号
     */
    @Test
    public void testManualBillingFromFile() {
        try {
            // 初始化Spring容器
            initSpringContext();

            // 1. 从文件读取运单号列表
            // 请将您的Excel文件转换为文本文件，每行一个运单号，放在以下路径
            String filePath = "D:/work/workspace/app-billing-timer/missing_eawb_codes.txt";
            List<String> eawbPrintcodes = readEawbPrintcodesFromFile(filePath);

            if (eawbPrintcodes.isEmpty()) {
                System.out.println("未读取到运单号，请检查文件路径和格式");
                System.out.println("请将运单号保存到文件：" + filePath);
                System.out.println("文件格式：每行一个运单号");
                return;
            }

            System.out.println("从文件读取到运单号数量：" + eawbPrintcodes.size());

            // 2. 验证运单号
            System.out.println("开始验证运单号...");
            Map<String, Object> validateResult = manualBillingService.validateEawbPrintcodes(eawbPrintcodes);
            System.out.println("验证结果：" + validateResult.get("message"));

            if (!(Boolean) validateResult.get("valid")) {
                System.out.println("验证失败，停止处理");
                return;
            }

            // 3. 执行手动计费
            System.out.println("开始执行手动计费...");
            String result = manualBillingService.manualBillingByEawbPrintcodes(eawbPrintcodes, 500);
            System.out.println("计费结果：" + result);

        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("测试异常：" + e.getMessage());
        }
    }

    /**
     * 测试少量数据的手动计费（用于验证功能）
     */
    @Test
    public void testManualBillingSmallBatch() {
        try {
            // 初始化Spring容器
            initSpringContext();

            // 使用少量测试数据
            List<String> testEawbCodes = Arrays.asList(
                    "TEST001", "TEST002", "TEST003", "TEST004", "TEST005"
            );

            System.out.println("测试少量数据手动计费...");

            // 验证运单号
            Map<String, Object> validateResult = manualBillingService.validateEawbPrintcodes(testEawbCodes);
            System.out.println("验证结果：" + validateResult.get("message"));

            // 执行计费
            String result = manualBillingService.manualBillingByEawbPrintcodes(testEawbCodes, 100);
            System.out.println("计费结果：" + result);

        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("测试异常：" + e.getMessage());
        }
    }

    /**
     * 从文本文件读取运单号列表
     * @param filePath 文件路径
     * @return 运单号列表
     */
    private List<String> readEawbPrintcodesFromFile(String filePath) {
        List<String> eawbPrintcodes = new ArrayList<>();
        
        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
            String line;
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                if (!line.isEmpty()) {
                    eawbPrintcodes.add(line);
                }
            }
        } catch (FileNotFoundException e) {
            System.out.println("文件未找到：" + filePath);
            System.out.println("请创建文件并将运单号放入其中，每行一个运单号");
        } catch (IOException e) {
            System.out.println("读取文件异常：" + e.getMessage());
        }
        
        return eawbPrintcodes;
    }

    /**
     * 创建示例运单号文件（用于测试）
     */
    @Test
    public void createSampleEawbFile() {
        String filePath = "D:/work/workspace/app-billing-timer/sample_eawb_codes.txt";
        
        try (PrintWriter writer = new PrintWriter(new FileWriter(filePath))) {
            // 创建一些示例运单号
            for (int i = 1; i <= 100; i++) {
                writer.println("SAMPLE" + String.format("%06d", i));
            }
            System.out.println("示例文件已创建：" + filePath);
        } catch (IOException e) {
            System.out.println("创建示例文件异常：" + e.getMessage());
        }
    }

    /**
     * 主方法，可以直接运行进行手动计费
     */
    public static void main(String[] args) {
        try {
            System.out.println("手动计费程序启动...");
            System.out.println("请确保：");
            System.out.println("1. 数据库连接正常");
            System.out.println("2. 运单号文件已准备好：D:/work/workspace/app-billing-timer/missing_eawb_codes.txt");
            System.out.println("3. 文件格式：每行一个运单号");

            // 启动Spring容器
            AbstractApplicationContext context = new ClassPathXmlApplicationContext("conf/applicationContext.xml");
            new SpringContextUtil().setApplicationContext(context);

            ManualBillingService manualBillingService = SpringContextUtil.getBean("ManualBillingService");

            // 读取运单号文件
            String filePath = "D:/work/workspace/app-billing-timer/missing_eawb_codes.txt";
            ManualBillingServiceTest test = new ManualBillingServiceTest();
            List<String> eawbPrintcodes = test.readEawbPrintcodesFromFile(filePath);

            if (eawbPrintcodes.isEmpty()) {
                System.out.println("未读取到运单号，请检查文件路径和格式");
                return;
            }

            System.out.println("从文件读取到运单号数量：" + eawbPrintcodes.size());

            // 验证运单号
            System.out.println("开始验证运单号...");
            Map<String, Object> validateResult = manualBillingService.validateEawbPrintcodes(eawbPrintcodes);
            System.out.println("验证结果：" + validateResult.get("message"));

            if (!(Boolean) validateResult.get("valid")) {
                System.out.println("验证失败，停止处理");
                return;
            }

            // 执行手动计费
            System.out.println("开始执行手动计费...");
            String result = manualBillingService.manualBillingByEawbPrintcodes(eawbPrintcodes, 500);
            System.out.println("计费结果：" + result);

        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("程序启动异常：" + e.getMessage());
        }
    }

    /**
     * 批量处理大文件的运单号
     * 如果运单号文件很大，可以使用这个方法分批处理
     */
    @Test
    public void testBatchProcessLargeFile() {
        try {
            // 初始化Spring容器
            initSpringContext();

            String filePath = "D:/work/workspace/app-billing-timer/missing_eawb_codes.txt";
            int batchSize = 1000; // 每批处理1000个运单号

            try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
                List<String> batch = new ArrayList<>();
                String line;
                int batchNum = 1;
                int totalProcessed = 0;

                while ((line = reader.readLine()) != null) {
                    line = line.trim();
                    if (!line.isEmpty()) {
                        batch.add(line);

                        if (batch.size() >= batchSize) {
                            // 处理当前批次
                            System.out.println("处理第" + batchNum + "批，运单数：" + batch.size());
                            String result = manualBillingService.manualBillingByEawbPrintcodes(batch, 500);
                            System.out.println("批次" + batchNum + "结果：" + result);

                            totalProcessed += batch.size();
                            batch.clear();
                            batchNum++;

                            // 可以在这里添加延迟，避免对数据库造成过大压力
                            Thread.sleep(5000); // 休息5秒
                        }
                    }
                }

                // 处理最后一批
                if (!batch.isEmpty()) {
                    System.out.println("处理最后一批，运单数：" + batch.size());
                    String result = manualBillingService.manualBillingByEawbPrintcodes(batch, 500);
                    System.out.println("最后一批结果：" + result);
                    totalProcessed += batch.size();
                }

                System.out.println("所有批次处理完成，总处理运单数：" + totalProcessed);

            } catch (FileNotFoundException e) {
                System.out.println("文件未找到：" + filePath);
            } catch (IOException e) {
                System.out.println("读取文件异常：" + e.getMessage());
            }
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("批量处理异常：" + e.getMessage());
        }
    }
}
