package com.sinoair.billing.service.system.impl;

import com.sinoair.billing.BaseTest;
import com.sinoair.billing.dao.billing.BillingEawbEadFlatMapper;
import com.sinoair.billing.domain.model.ceosbi.EawbEadFlat;
import com.sinoair.billing.service.receipt.ReceiptService;
import com.sinoair.billing.service.system.MainService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: 大雄
 * @date: 2019-07-04
 * @time: 13:53
 * @description: To change this template use File | Settings | File Templates.
 */
public class MainServiceImplTest extends BaseTest {

    @Autowired
    private MainService mainService;

    @Autowired
    private BillingEawbEadFlatMapper billingEawbEadFlatMapper;

    @Autowired
    private ReceiptService receiptService;

    @Test
    public void selectCeosUser() {
        List list = mainService.selectCeosUser();
        System.out.println(list.size());
        mainService.checkDb();
        System.out.println("-----------");
    }

    @Test
    public void testHandleFlat() {

        List<EawbEadFlat> flatList = new ArrayList<>();
        EawbEadFlat flat = billingEawbEadFlatMapper.selectByPrimaryKey(new BigDecimal("3905079184"));
        flat.setDeclare(new Date());
        flatList.add(flat);

        EawbEadFlat flat2 = billingEawbEadFlatMapper.selectByPrimaryKey(new BigDecimal("3905103175"));;
        flat2.setFcInbound(new Date());
        flatList.add(flat2);

        billingEawbEadFlatMapper.updateBatch(flatList);
    }

    @Test
    public void testSyncAppRecord(){
        receiptService.syncAppRecord();
    }
}