package com.sinoair.billing.service.system.impl;

import com.sinoair.billing.BaseTest;
import com.sinoair.billing.core.util.FileUtil;
import com.sinoair.billing.dao.billing.CheckEawbResultDetailMapper;
import com.sinoair.billing.dao.billing.TempMapper;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import sun.misc.BASE64Decoder;

import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.util.Date;
import java.util.List;

/**
 * @Auther: nanhb
 * @Date: 2019-10-14 13:44
 * @Description:
 */
public class TempTest extends BaseTest {

    public final static String POSTCODE_PATH = "C:\\tmp\\billing\\eawb.txt";

    public final static int length = 1000;

    @Autowired
    private TempMapper tempMapper;
    @Autowired
    private CheckEawbResultDetailMapper checkEawbResultDetailMapper;

    @Test
    public void readEawbCode(){
        //读取邮政编码txt
        File file = new File(POSTCODE_PATH);
        List<String> tmpList = FileUtil.realFileTxt(file);
        System.out.println("eawbtxt="+tmpList.size());
        Date date = new Date();
        int size = tmpList.size();
        int count = size % length != 0 ? size / length + 1 : size / length;
        for (int i = 0; i < count; i++) {
            List<String> subList = tmpList.subList(i * length, ((1000 + i * length) < size ? (1000 + i * length) : size));
            if (subList.size() > 0){
                tempMapper.insertBatchTemp(subList);
//                tempMapper.insertBatchAsendia(subList);
//                tempMapper.insertBatchTempEa(subList);
//                checkEawbResultDetailMapper.insertBatchLackEawb(subList);

            }
        }

    }
}
