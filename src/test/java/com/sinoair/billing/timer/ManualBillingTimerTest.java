package com.sinoair.billing.timer;

import com.sinoair.billing.core.exception.SinoAirException;
import com.sinoair.billing.run.BillingTimerStart;

import java.lang.reflect.InvocationTargetException;

/**
 * 手动计费定时任务测试类
 * 用于执行遗漏数据的重新计费
 */
public class ManualBillingTimerTest {

    /**
     * 使用BillingTimerStart启动手动计费任务
     */
    public static void main(String[] args) throws NoSuchMethodException, IllegalAccessException, InstantiationException, SinoAirException, InvocationTargetException, ClassNotFoundException {

        System.out.println("=== 手动计费任务启动 ===");

        // 启动手动计费定时任务
        // 参数说明：
        // 第一个参数：定时任务类名（不包含包名）
        // 第二个参数：超时时间（毫秒），建议设置较大值，如30分钟
        // 第三个参数：是否多线程执行，T表示多线程
        BillingTimerStart.main(new String[]{"ManualBillingTimer", "1800000", "T"});

        System.out.println("手动计费任务执行完成");
    }
}
