package com.sinoair.billing.timer;

import com.sinoair.billing.core.exception.SinoAirException;
import com.sinoair.billing.run.BillingTimerStart;

import java.lang.reflect.InvocationTargetException;

/**
 * 手动计费定时任务测试类
 * 用于执行9000多条遗漏数据的重新计费
 */
public class ManualBillingTimerTest {

    /**
     * 使用BillingTimerStart启动手动计费任务
     * 这是推荐的执行方式，与项目现有的定时任务启动方式一致
     */
    public static void main(String[] args) throws NoSuchMethodException, IllegalAccessException, InstantiationException, SinoAirException, InvocationTargetException, ClassNotFoundException {

        System.out.println("=== 手动计费任务启动 ===");

        // 检查当前的Maven Profile设置
        String activeProfile = System.getProperty("spring.profiles.active");
        System.out.println("当前系统属性中的Profile: " + activeProfile);

        System.out.println("请确保：");
        System.out.println("1. 数据库连接正常");
        System.out.println("2. 运单号文件已准备好：D:/work/workspace/app-billing-timer/missing_eawb_codes.txt");
        System.out.println("3. 文件格式：每行一个运单号");
        System.out.println("4. 建议在业务低峰期执行");
        System.out.println("5. 当前使用Profile: " + System.getProperty("spring.profiles.active"));
        System.out.println("========================");

        // 启动手动计费定时任务
        // 参数说明：
        // 第一个参数：定时任务类名（不包含包名）
        // 第二个参数：超时时间（毫秒），建议设置较大值，如30分钟
        // 第三个参数：是否多线程执行，T表示多线程
        BillingTimerStart.main(new String[]{"ManualBillingTimer", "1800000", "T"});

        System.out.println("手动计费任务执行完成");
    }
}
