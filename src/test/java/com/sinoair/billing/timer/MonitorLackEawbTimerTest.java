package com.sinoair.billing.timer;

import com.sinoair.billing.core.exception.SinoAirException;
import com.sinoair.billing.run.BillingTimerStart;

import java.lang.reflect.InvocationTargetException;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: 大雄
 * @date: 2019-04-08
 * @time: 15:39
 * @description: To change this template use File | Settings | File Templates.
 */
public class MonitorLackEawbTimerTest {

    public static void main(String[] args) throws NoSuchMethodException, IllegalAccessException, InstantiationException, SinoAirException, InvocationTargetException, ClassNotFoundException{
        BillingTimerStart.main(new String[]{"MonitorLackEawbTimer","10000000","T"});

        System.out.println("-----------------");
    }
}