package com.sinoair.billing.timer;

import com.sinoair.billing.core.exception.SinoAirException;
import com.sinoair.billing.run.BillingTimerStart;
import java.lang.reflect.InvocationTargetException;


public class ReceiptRecord2ManifestMonthlyTimerTest {

    public static void main(String[] args) throws NoSuchMethodException, IllegalAccessException, InstantiationException, SinoAirException, InvocationTargetException, ClassNotFoundException{
        BillingTimerStart.main(new String[]{"ReceiptRecord2ManifestMonthlyTimer","10000000","T"});

        System.out.println("-----------------");
    }
}
