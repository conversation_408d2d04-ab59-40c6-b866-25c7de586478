package com.sinoair.billing.timer;

import com.sinoair.billing.core.exception.SinoAirException;
import com.sinoair.billing.run.BillingTimerStart;
import java.lang.reflect.InvocationTargetException;


public class ReceiptRecord2ManifestWeekTimerTest {

    public static void main(String[] args) throws NoSuchMethodException, IllegalAccessException, InstantiationException, SinoAirException, InvocationTargetException, ClassNotFoundException{
        BillingTimerStart.main(new String[]{"ReceiptRecord2ManifestWeekTimer","10000000","T"});

        System.out.println("-----------------");
    }
}
