package com.sinoair.billing.timer;

import com.sinoair.billing.core.exception.SinoAirException;
import com.sinoair.billing.run.BillingTimerStart;

import java.lang.reflect.InvocationTargetException;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: 大雄
 * @date: 2019-06-19
 * @time: 10:50
 * @description: To change this template use File | Settings | File Templates.
 */
public class ThreadTimerTest  {

    public static void main(String[] args) throws NoSuchMethodException, IllegalAccessException, InstantiationException, SinoAirException, InvocationTargetException, ClassNotFoundException {
        BillingTimerStart.main(new String[]{"ThreadTimer","100000","T"});
       /* ThreadTimer threadTimer = new ThreadTimer("ThreadTimer",100000,false);
        try {
            threadTimer.cronThreadRun();
        } catch (Exception e) {
            e.printStackTrace();
        }*/
        System.out.println("-----------------");
    }
}