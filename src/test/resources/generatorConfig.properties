# \u6570\u636E\u5E93\u9A71\u52A8jar \u8DEF\u5F84
#daxiong
drive.class.path=D:\\tools\\repository\\oracle\\ojdbc\\6.0\\ojdbc-6.0.jar
#drive.class.path=C:\\soft\\maven\\apache-maven-3.0.5-bin\\repository\\oracle\\ojdbc\\6.0\\ojdbc-6.0.jar


# \u6570\u636E\u5E93\u8FDE\u63A5\u53C2\u6570
jdbc.driver=oracle.jdbc.driver.OracleDriver
jdbc.url=jdbc:oracle:thin:@ 172.17.0.244:1521:training
jdbc.username=billing
jdbc.password=billing
#jdbc.url=******************************************
#jdbc.username=billing
#jdbc.password=billing.2021.SinoAir
#
## \u5305\u8DEF\u5F84\u914D\u7F6E
#model.package=com.sinoair.billing.domain.model.billing
#dao.package=com.sinoair.billing.dao.billing
#xml.mapper.package=mapper.billing

#jdbc.driver=oracle.jdbc.driver.OracleDriver
#jdbc.url=jdbc:oracle:thin:@ 172.17.0.244:1521:training
#jdbc.username=ceosbi
#jdbc.password=Ceosbi_244

# \u5305\u8DEF\u5F84\u914D\u7F6E
model.package=com.sinoair.billing.domain.model.billing
dao.package=com.sinoair.billing.dao.billing
xml.mapper.package=mapper.billing

#\u653E\u5728test\u4E2D\u751F\u6210 \u518D\u8F6C\u79FB\u5230main \u4EE5\u9632\u8986\u76D6
target.project=src/test/java
