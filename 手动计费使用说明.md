# 手动计费功能使用说明

## 概述
针对GeneratePaymentFeeTimer定时任务遗漏的9000多条计费数据，我们开发了手动计费功能来进行重新计费处理。

## 功能组件

### 1. ManualBillingService（手动计费服务）
- **位置**: `src/main/java/com/sinoair/billing/service/payment/ManualBillingService.java`
- **功能**: 根据运单号列表进行批量重新计费
- **主要方法**:
  - `manualBillingByEawbPrintcodes()`: 执行手动计费
  - `validateEawbPrintcodes()`: 验证运单号是否存在轨迹数据

### 2. ManualBillingServiceTest（测试用例）
- **位置**: `src/test/java/com/sinoair/billing/service/payment/ManualBillingServiceTest.java`
- **功能**: 提供多种测试方法来执行手动计费

### 3. BillingAnalysisService（分析服务）
- **位置**: `src/main/java/com/sinoair/billing/service/analysis/BillingAnalysisService.java`
- **功能**: 分析计费遗漏的原因

## 使用步骤

### 步骤1: 准备运单号文件
1. 将您导出的Excel文件中的运单号转换为文本文件
2. 文件路径: `D:/work/workspace/app-billing-timer/missing_eawb_codes.txt`
3. 文件格式: 每行一个运单号，如：
   ```
   1234567890
   1234567891
   1234567892
   ...
   ```

### 步骤2: 执行手动计费
有以下几种执行方式：

#### 方式1: 运行测试用例（推荐）
```bash
# 在IDE中运行测试方法
ManualBillingServiceTest.testManualBillingFromFile()
```

#### 方式2: 分批处理大文件
```bash
# 如果运单号很多，建议使用分批处理
ManualBillingServiceTest.testBatchProcessLargeFile()
```

#### 方式3: 小批量测试
```bash
# 先用少量数据测试功能是否正常
ManualBillingServiceTest.testManualBillingSmallBatch()
```

### 步骤3: 监控执行结果
- 查看控制台输出的处理进度和结果
- 检查数据库中的 `GENERATE_PAYMENT_FEE_LOG` 表，确认处理记录
- 验证应付费用和应收费用是否正确生成

## 核心逻辑说明

### 计费处理流程
1. **读取运单号**: 从文件或列表中读取需要重新计费的运单号
2. **查询轨迹数据**: 根据运单号查询对应的轨迹数据（ExpressBusinessActivity）
3. **分批处理**: 将数据分批处理，避免对系统造成过大压力
4. **多线程计费**: 使用GeneratePaymentFeeThread进行多线程计费处理
5. **记录日志**: 在GENERATE_PAYMENT_FEE_LOG表中记录处理日志

### 数据查询逻辑
```sql
-- 新增的查询方法：根据运单号列表查询轨迹数据
SELECT DISTINCT EBA_SYSCODE, eba.EAWB_PRINTCODE, EAD_CODE, EAST_CODE, 
       p.P_EADCODE, p.p_EASTCODE, EBA_OCCURTIME, BILLING_SYSCODE
FROM expressbusinessactivity eba, expressairwaybill eawb, product p
WHERE eawb.eawb_printcode = eba.eawb_printcode 
  AND eawb.EAWB_SERVICETYPE_ORIGINAL = p.P_SERVICETYPE_ORIGINAL
  AND eba.eawb_printcode IN ('运单号1', '运单号2', ...)
ORDER BY BILLING_SYSCODE ASC
```

## 可能的遗漏原因分析

### 1. BILLING_SYSCODE跳号问题
- **现象**: selectsysCodeList方法基于BILLING_SYSCODE > beginNo查询，如果存在跳号会导致遗漏
- **原因**: 数据同步延迟、系统异常中断
- **解决**: 使用手动计费功能重新处理遗漏的数据

### 2. 定时任务执行异常
- **现象**: 定时任务执行过程中发生异常，导致部分数据未处理
- **原因**: 数据库连接异常、内存不足、业务逻辑异常
- **解决**: 检查日志，修复异常，重新处理遗漏数据

### 3. 数据关联问题
- **现象**: 轨迹数据与运单数据、产品数据关联不完整
- **原因**: 数据同步不及时、外键约束问题
- **解决**: 检查数据完整性，修复关联问题

## 注意事项

### 1. 性能考虑
- 建议分批处理，每批500-1000个运单号
- 批次之间可以添加适当延迟，避免对数据库造成过大压力
- 监控系统资源使用情况

### 2. 数据安全
- 执行前建议备份相关数据表
- 先用少量数据测试功能是否正常
- 监控处理结果，确保数据正确性

### 3. 执行环境
- 确保数据库连接正常
- 确保有足够的系统资源
- 建议在业务低峰期执行

## 监控和验证

### 1. 处理进度监控
- 查看控制台输出
- 检查GENERATE_PAYMENT_FEE_LOG表的记录

### 2. 结果验证
- 检查PAYMENT_RECORD表中是否生成了应付费用记录
- 检查RECEIPT_RECORD表中是否生成了应收费用记录
- 对比处理前后的数据量变化

### 3. 异常处理
- 如果处理过程中出现异常，检查日志文件
- 根据异常信息调整处理策略
- 必要时联系技术支持

## 联系方式
如果在使用过程中遇到问题，请联系开发团队进行支持。
